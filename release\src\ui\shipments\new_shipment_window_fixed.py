#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                               QTabWidget, QWidget, QGroupBox, QFormLayout, 
                               QLineEdit, QComboBox, QTextEdit, QDateEdit, 
                               QSpinBox, QTableWidget, QMessageBox, QShortcut,
                               QApplication, QLabel)
from PySide6.QtCore import Signal, QDate, Qt, QKeySequence
from PySide6.QtGui import QFont

from src.database.database_manager import DatabaseManager

class NewShipmentWindowFixed(QDialog):
    """نافذة شحنة جديدة مع أزرار التحكم المطلوبة"""
    
    shipment_saved = Signal(int)  # إشارة عند حفظ الشحنة
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.current_shipment_id = None
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("شحنة جديدة - محدثة")
        self.setModal(True)
        self.resize(900, 700)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # عنوان
        title_label = QLabel("شاشة الشحنة الجديدة مع أزرار التحكم")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # التبويبات
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # إنشاء تبويب أساسي
        self.create_basic_tab()
        
        # أزرار التحكم
        self.create_control_buttons(main_layout)
        
    def create_basic_tab(self):
        """إنشاء التبويب الأساسي"""
        basic_tab = QWidget()
        self.tab_widget.addTab(basic_tab, "البيانات الأساسية")
        
        layout = QVBoxLayout(basic_tab)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_layout = QFormLayout(basic_group)
        
        # رقم الشحنة
        self.shipment_number_edit = QLineEdit()
        self.shipment_number_edit.setReadOnly(True)
        self.shipment_number_edit.setStyleSheet("background-color: #f0f0f0;")
        self.shipment_number_edit.setText("SH-2024-001")
        basic_layout.addRow("رقم الشحنة:", self.shipment_number_edit)
        
        # المورد
        self.supplier_edit = QLineEdit()
        self.supplier_edit.setReadOnly(True)
        basic_layout.addRow("المورد:", self.supplier_edit)
        
        # حالة الشحنة
        self.shipment_status_combo = QComboBox()
        self.shipment_status_combo.addItems([
            "تحت الطلب", "مؤكدة", "تم الشحن", "في الطريق", 
            "وصلت الميناء", "في الجمارك", "تم التسليم"
        ])
        basic_layout.addRow("حالة الشحنة:", self.shipment_status_combo)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        basic_layout.addRow("ملاحظات:", self.notes_edit)
        
        layout.addWidget(basic_group)
        layout.addStretch()
        
    def create_control_buttons(self, main_layout):
        """إنشاء أزرار التحكم"""
        # مجموعة أزرار التحكم
        buttons_group = QGroupBox("أزرار التحكم")
        buttons_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
                font-size: 14px;
            }
        """)
        
        buttons_layout = QHBoxLayout(buttons_group)
        buttons_layout.setSpacing(15)
        buttons_layout.setContentsMargins(20, 20, 20, 20)

        # زر إضافة شحنة جديدة
        self.new_button = QPushButton("🆕 إضافة")
        self.new_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                min-width: 120px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)

        # زر حفظ
        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                min-width: 120px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #229954;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)

        # زر تعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                min-width: 120px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #e67e22;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.edit_button.setEnabled(False)  # معطل في البداية

        # زر خروج
        self.exit_button = QPushButton("🚪 خروج")
        self.exit_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                min-width: 120px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #c0392b;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)

        # ترتيب الأزرار
        buttons_layout.addWidget(self.new_button)
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.exit_button)
        
        # إضافة مجموعة الأزرار للتخطيط الرئيسي
        main_layout.addWidget(buttons_group)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.new_button.clicked.connect(self.new_shipment)
        self.save_button.clicked.connect(self.save_shipment)
        self.edit_button.clicked.connect(self.edit_shipment)
        self.exit_button.clicked.connect(self.reject)
        
    def new_shipment(self):
        """إنشاء شحنة جديدة"""
        reply = QMessageBox.question(
            self,
            "شحنة جديدة",
            "هل تريد إنشاء شحنة جديدة؟\nسيتم مسح البيانات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.clear_form()
            self.current_shipment_id = None
            self.edit_button.setEnabled(False)
            QMessageBox.information(self, "شحنة جديدة", "تم إنشاء نموذج شحنة جديد")
    
    def save_shipment(self):
        """حفظ الشحنة"""
        QMessageBox.information(self, "حفظ", "تم حفظ الشحنة بنجاح")
        self.edit_button.setEnabled(True)
    
    def edit_shipment(self):
        """تعديل الشحنة"""
        QMessageBox.information(self, "تعديل", "يمكنك الآن تعديل الشحنة")
    
    def clear_form(self):
        """مسح النموذج"""
        self.supplier_edit.clear()
        self.shipment_status_combo.setCurrentIndex(0)
        self.notes_edit.clear()

def main():
    """اختبار النافذة الجديدة"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = NewShipmentWindowFixed()
    window.show()
    
    print("✅ تم فتح النافذة الجديدة مع أزرار التحكم")
    print("🔍 تحقق من وجود الأزرار التالية:")
    print("   • 🆕 إضافة")
    print("   • 💾 حفظ") 
    print("   • ✏️ تعديل")
    print("   • 🚪 خروج")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
