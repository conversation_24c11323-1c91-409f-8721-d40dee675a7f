#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حوار إعدادات قاعدة البيانات - ProShipment V2.0.0
Database Settings Dialog
"""

import sys
import os
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QRadioButton, QLineEdit, QSpinBox, QCheckBox, QPushButton,
    QLabel, QTabWidget, QWidget, QTextEdit, QProgressBar,
    QMessageBox, QFileDialog, QComboBox, QButtonGroup
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal
from PySide6.QtGui import QFont, QIcon

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.database.oracle_config import (
    DatabaseConfig, DatabaseType, OracleConfig, SQLiteConfig,
    OracleConnectionType, DatabaseConfigManager
)
from src.database.universal_database_manager import UniversalDatabaseManager

class DatabaseTestThread(QThread):
    """خيط اختبار الاتصال"""
    
    test_completed = Signal(bool, str)
    
    def __init__(self, config: DatabaseConfig):
        super().__init__()
        self.config = config
    
    def run(self):
        """تشغيل اختبار الاتصال"""
        try:
            db_manager = UniversalDatabaseManager(self.config)
            success = db_manager.test_connection()
            
            if success:
                db_info = db_manager.get_database_info()
                message = f"نجح الاتصال!\nنوع قاعدة البيانات: {db_info['type']}"
                if 'host' in db_info:
                    message += f"\nالخادم: {db_info['host']}:{db_info['port']}"
            else:
                message = "فشل في الاتصال"
            
            self.test_completed.emit(success, message)
            
        except Exception as e:
            self.test_completed.emit(False, f"خطأ في الاختبار: {str(e)}")

class DatabaseSettingsDialog(QDialog):
    """حوار إعدادات قاعدة البيانات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config_manager = DatabaseConfigManager()
        self.current_config = self.config_manager.load_config()
        self.test_thread = None
        
        self.setup_ui()
        self.load_current_settings()
        self.connect_signals()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إعدادات قاعدة البيانات - ProShipment V2.0.0")
        self.setModal(True)
        self.resize(600, 500)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # عنوان
        title_label = QLabel("إعدادات قاعدة البيانات")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # التبويبات
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # تبويب نوع قاعدة البيانات
        self.setup_database_type_tab()
        
        # تبويب إعدادات SQLite
        self.setup_sqlite_tab()
        
        # تبويب إعدادات Oracle
        self.setup_oracle_tab()
        
        # تبويب الاختبار
        self.setup_test_tab()
        
        # أزرار التحكم
        self.setup_control_buttons(main_layout)
    
    def setup_database_type_tab(self):
        """إعداد تبويب نوع قاعدة البيانات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة نوع قاعدة البيانات
        db_type_group = QGroupBox("نوع قاعدة البيانات")
        db_type_layout = QVBoxLayout(db_type_group)
        
        # أزرار الاختيار
        self.db_type_group = QButtonGroup()
        
        self.sqlite_radio = QRadioButton("SQLite (ملف محلي)")
        self.oracle_radio = QRadioButton("Oracle Database")
        
        self.db_type_group.addButton(self.sqlite_radio, 0)
        self.db_type_group.addButton(self.oracle_radio, 1)
        
        db_type_layout.addWidget(self.sqlite_radio)
        db_type_layout.addWidget(self.oracle_radio)
        
        layout.addWidget(db_type_group)
        
        # معلومات نوع قاعدة البيانات
        info_group = QGroupBox("معلومات")
        info_layout = QVBoxLayout(info_group)
        
        self.db_info_text = QTextEdit()
        self.db_info_text.setReadOnly(True)
        self.db_info_text.setMaximumHeight(150)
        info_layout.addWidget(self.db_info_text)
        
        layout.addWidget(info_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "نوع قاعدة البيانات")
    
    def setup_sqlite_tab(self):
        """إعداد تبويب SQLite"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # إعدادات SQLite
        sqlite_group = QGroupBox("إعدادات SQLite")
        sqlite_layout = QFormLayout(sqlite_group)
        
        # مسار الملف
        self.sqlite_path_edit = QLineEdit()
        path_layout = QHBoxLayout()
        path_layout.addWidget(self.sqlite_path_edit)
        
        browse_button = QPushButton("تصفح...")
        browse_button.clicked.connect(self.browse_sqlite_file)
        path_layout.addWidget(browse_button)
        
        sqlite_layout.addRow("مسار الملف:", path_layout)
        
        # مهلة الاتصال
        self.sqlite_timeout_spin = QSpinBox()
        self.sqlite_timeout_spin.setRange(1, 300)
        self.sqlite_timeout_spin.setSuffix(" ثانية")
        sqlite_layout.addRow("مهلة الاتصال:", self.sqlite_timeout_spin)
        
        # فحص نفس الخيط
        self.sqlite_same_thread_check = QCheckBox("فحص نفس الخيط")
        sqlite_layout.addRow("", self.sqlite_same_thread_check)
        
        layout.addWidget(sqlite_group)
        
        # معلومات الملف
        file_info_group = QGroupBox("معلومات الملف")
        file_info_layout = QFormLayout(file_info_group)
        
        self.file_size_label = QLabel("غير محدد")
        self.file_exists_label = QLabel("غير محدد")
        self.file_writable_label = QLabel("غير محدد")
        
        file_info_layout.addRow("حجم الملف:", self.file_size_label)
        file_info_layout.addRow("الملف موجود:", self.file_exists_label)
        file_info_layout.addRow("قابل للكتابة:", self.file_writable_label)
        
        layout.addWidget(file_info_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "إعدادات SQLite")
    
    def setup_oracle_tab(self):
        """إعداد تبويب Oracle"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # إعدادات الاتصال
        connection_group = QGroupBox("إعدادات الاتصال")
        connection_layout = QFormLayout(connection_group)
        
        self.oracle_host_edit = QLineEdit()
        self.oracle_port_spin = QSpinBox()
        self.oracle_port_spin.setRange(1, 65535)
        
        connection_layout.addRow("عنوان الخادم:", self.oracle_host_edit)
        connection_layout.addRow("المنفذ:", self.oracle_port_spin)
        
        # نوع الاتصال
        self.oracle_connection_type = QComboBox()
        self.oracle_connection_type.addItems([
            "Service Name",
            "SID",
            "TNS"
        ])
        connection_layout.addRow("نوع الاتصال:", self.oracle_connection_type)
        
        self.oracle_service_edit = QLineEdit()
        self.oracle_sid_edit = QLineEdit()
        
        connection_layout.addRow("اسم الخدمة:", self.oracle_service_edit)
        connection_layout.addRow("SID:", self.oracle_sid_edit)
        
        layout.addWidget(connection_group)
        
        # بيانات المستخدم
        user_group = QGroupBox("بيانات المستخدم")
        user_layout = QFormLayout(user_group)
        
        self.oracle_username_edit = QLineEdit()
        self.oracle_password_edit = QLineEdit()
        self.oracle_password_edit.setEchoMode(QLineEdit.Password)
        
        user_layout.addRow("اسم المستخدم:", self.oracle_username_edit)
        user_layout.addRow("كلمة المرور:", self.oracle_password_edit)
        
        layout.addWidget(user_group)
        
        # إعدادات الأداء
        performance_group = QGroupBox("إعدادات الأداء")
        performance_layout = QFormLayout(performance_group)
        
        self.oracle_pool_size_spin = QSpinBox()
        self.oracle_pool_size_spin.setRange(1, 100)
        
        self.oracle_max_overflow_spin = QSpinBox()
        self.oracle_max_overflow_spin.setRange(0, 200)
        
        performance_layout.addRow("حجم Pool:", self.oracle_pool_size_spin)
        performance_layout.addRow("الحد الأقصى:", self.oracle_max_overflow_spin)
        
        layout.addWidget(performance_group)
        
        # إعدادات الأمان
        security_group = QGroupBox("إعدادات الأمان")
        security_layout = QFormLayout(security_group)
        
        self.oracle_ssl_check = QCheckBox("استخدام SSL")
        self.oracle_wallet_edit = QLineEdit()
        
        security_layout.addRow("", self.oracle_ssl_check)
        security_layout.addRow("مسار Wallet:", self.oracle_wallet_edit)
        
        layout.addWidget(security_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "إعدادات Oracle")
    
    def setup_test_tab(self):
        """إعداد تبويب الاختبار"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات الاختبار
        test_group = QGroupBox("اختبار الاتصال")
        test_layout = QVBoxLayout(test_group)
        
        # زر الاختبار
        self.test_button = QPushButton("اختبار الاتصال")
        self.test_button.clicked.connect(self.test_connection)
        test_layout.addWidget(self.test_button)
        
        # شريط التقدم
        self.test_progress = QProgressBar()
        self.test_progress.setVisible(False)
        test_layout.addWidget(self.test_progress)
        
        # نتائج الاختبار
        self.test_results = QTextEdit()
        self.test_results.setReadOnly(True)
        test_layout.addWidget(self.test_results)
        
        layout.addWidget(test_group)
        
        self.tab_widget.addTab(tab, "اختبار الاتصال")
    
    def setup_control_buttons(self, main_layout):
        """إعداد أزرار التحكم"""
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        save_button = QPushButton("حفظ")
        save_button.clicked.connect(self.save_settings)
        buttons_layout.addWidget(save_button)
        
        # زر الإلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        # زر تطبيق
        apply_button = QPushButton("تطبيق")
        apply_button.clicked.connect(self.apply_settings)
        buttons_layout.addWidget(apply_button)
        
        buttons_layout.addStretch()
        
        # زر المساعدة
        help_button = QPushButton("مساعدة")
        help_button.clicked.connect(self.show_help)
        buttons_layout.addWidget(help_button)
        
        main_layout.addLayout(buttons_layout)
    
    def connect_signals(self):
        """ربط الإشارات"""
        # تغيير نوع قاعدة البيانات
        self.db_type_group.buttonClicked.connect(self.on_database_type_changed)
        
        # تغيير نوع اتصال Oracle
        self.oracle_connection_type.currentTextChanged.connect(self.on_oracle_connection_type_changed)
        
        # تغيير مسار SQLite
        self.sqlite_path_edit.textChanged.connect(self.update_sqlite_file_info)
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        # نوع قاعدة البيانات
        if self.current_config.type == DatabaseType.SQLITE:
            self.sqlite_radio.setChecked(True)
        else:
            self.oracle_radio.setChecked(True)
        
        # إعدادات SQLite
        if self.current_config.sqlite_config:
            sqlite_config = self.current_config.sqlite_config
            self.sqlite_path_edit.setText(sqlite_config.path)
            self.sqlite_timeout_spin.setValue(sqlite_config.timeout)
            self.sqlite_same_thread_check.setChecked(not sqlite_config.check_same_thread)
        
        # إعدادات Oracle
        if self.current_config.oracle_config:
            oracle_config = self.current_config.oracle_config
            self.oracle_host_edit.setText(oracle_config.host)
            self.oracle_port_spin.setValue(oracle_config.port)
            self.oracle_service_edit.setText(oracle_config.service_name or "")
            self.oracle_sid_edit.setText(oracle_config.sid or "")
            self.oracle_username_edit.setText(oracle_config.username)
            self.oracle_password_edit.setText(oracle_config.password)
            self.oracle_pool_size_spin.setValue(oracle_config.pool_size)
            self.oracle_max_overflow_spin.setValue(oracle_config.max_overflow)
            self.oracle_ssl_check.setChecked(oracle_config.use_ssl)
            self.oracle_wallet_edit.setText(oracle_config.wallet_location or "")
            
            # نوع الاتصال
            if oracle_config.connection_type == OracleConnectionType.SERVICE_NAME:
                self.oracle_connection_type.setCurrentIndex(0)
            elif oracle_config.connection_type == OracleConnectionType.SID:
                self.oracle_connection_type.setCurrentIndex(1)
            else:
                self.oracle_connection_type.setCurrentIndex(2)
        
        # تحديث المعلومات
        self.on_database_type_changed()
        self.update_sqlite_file_info()
    
    def on_database_type_changed(self):
        """عند تغيير نوع قاعدة البيانات"""
        if self.sqlite_radio.isChecked():
            info_text = """SQLite:
• قاعدة بيانات محلية في ملف واحد
• سهلة الاستخدام والنقل
• مناسبة للتطبيقات الصغيرة والمتوسطة
• لا تحتاج خادم منفصل"""
        else:
            info_text = """Oracle Database:
• قاعدة بيانات مؤسسية متقدمة
• أداء عالي للبيانات الكبيرة
• دعم عدة مستخدمين متزامنين
• ميزات أمان وموثوقية متقدمة
• تحتاج خادم Oracle منفصل"""
        
        self.db_info_text.setText(info_text)
    
    def on_oracle_connection_type_changed(self):
        """عند تغيير نوع اتصال Oracle"""
        connection_type = self.oracle_connection_type.currentText()
        
        if connection_type == "Service Name":
            self.oracle_service_edit.setEnabled(True)
            self.oracle_sid_edit.setEnabled(False)
        elif connection_type == "SID":
            self.oracle_service_edit.setEnabled(False)
            self.oracle_sid_edit.setEnabled(True)
        else:  # TNS
            self.oracle_service_edit.setEnabled(True)
            self.oracle_sid_edit.setEnabled(False)
    
    def update_sqlite_file_info(self):
        """تحديث معلومات ملف SQLite"""
        file_path = Path(self.sqlite_path_edit.text())
        
        if file_path.exists():
            # حجم الملف
            size_bytes = file_path.stat().st_size
            size_mb = size_bytes / (1024 * 1024)
            self.file_size_label.setText(f"{size_mb:.2f} MB")
            
            # الملف موجود
            self.file_exists_label.setText("نعم")
            
            # قابل للكتابة
            writable = os.access(file_path, os.W_OK)
            self.file_writable_label.setText("نعم" if writable else "لا")
        else:
            self.file_size_label.setText("الملف غير موجود")
            self.file_exists_label.setText("لا")
            self.file_writable_label.setText("غير محدد")
    
    def browse_sqlite_file(self):
        """تصفح ملف SQLite"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "اختر ملف قاعدة البيانات",
            self.sqlite_path_edit.text(),
            "ملفات SQLite (*.db *.sqlite *.sqlite3);;جميع الملفات (*)"
        )
        
        if file_path:
            self.sqlite_path_edit.setText(file_path)
    
    def test_connection(self):
        """اختبار الاتصال"""
        # إنشاء إعدادات من النموذج
        config = self.create_config_from_form()
        
        # بدء الاختبار
        self.test_button.setEnabled(False)
        self.test_progress.setVisible(True)
        self.test_progress.setRange(0, 0)  # شريط تقدم غير محدد
        
        self.test_results.append(f"🔄 بدء اختبار الاتصال مع {config.type.value}...")
        
        # إنشاء خيط الاختبار
        self.test_thread = DatabaseTestThread(config)
        self.test_thread.test_completed.connect(self.on_test_completed)
        self.test_thread.start()
    
    def on_test_completed(self, success: bool, message: str):
        """عند انتهاء الاختبار"""
        self.test_button.setEnabled(True)
        self.test_progress.setVisible(False)
        
        if success:
            self.test_results.append(f"✅ {message}")
        else:
            self.test_results.append(f"❌ {message}")
        
        # تنظيف الخيط
        if self.test_thread:
            self.test_thread.deleteLater()
            self.test_thread = None
    
    def create_config_from_form(self) -> DatabaseConfig:
        """إنشاء إعدادات من النموذج"""
        # نوع قاعدة البيانات
        if self.sqlite_radio.isChecked():
            db_type = DatabaseType.SQLITE
        else:
            db_type = DatabaseType.ORACLE
        
        # إعدادات SQLite
        sqlite_config = SQLiteConfig(
            path=self.sqlite_path_edit.text(),
            timeout=self.sqlite_timeout_spin.value(),
            check_same_thread=not self.sqlite_same_thread_check.isChecked()
        )
        
        # إعدادات Oracle
        connection_type_map = {
            "Service Name": OracleConnectionType.SERVICE_NAME,
            "SID": OracleConnectionType.SID,
            "TNS": OracleConnectionType.TNS
        }
        
        oracle_config = OracleConfig(
            host=self.oracle_host_edit.text(),
            port=self.oracle_port_spin.value(),
            service_name=self.oracle_service_edit.text() or None,
            sid=self.oracle_sid_edit.text() or None,
            username=self.oracle_username_edit.text(),
            password=self.oracle_password_edit.text(),
            connection_type=connection_type_map[self.oracle_connection_type.currentText()],
            pool_size=self.oracle_pool_size_spin.value(),
            max_overflow=self.oracle_max_overflow_spin.value(),
            use_ssl=self.oracle_ssl_check.isChecked(),
            wallet_location=self.oracle_wallet_edit.text() or None
        )
        
        return DatabaseConfig(
            type=db_type,
            sqlite_config=sqlite_config,
            oracle_config=oracle_config
        )
    
    def save_settings(self):
        """حفظ الإعدادات"""
        if self.apply_settings():
            self.accept()
    
    def apply_settings(self) -> bool:
        """تطبيق الإعدادات"""
        try:
            # إنشاء إعدادات جديدة
            new_config = self.create_config_from_form()
            
            # حفظ الإعدادات
            self.config_manager._config = new_config
            if self.config_manager.save_config():
                QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")
                return True
            else:
                QMessageBox.warning(self, "خطأ", "فشل في حفظ الإعدادات")
                return False
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الإعدادات:\n{str(e)}")
            return False
    
    def show_help(self):
        """عرض المساعدة"""
        help_text = """مساعدة إعدادات قاعدة البيانات:

1. نوع قاعدة البيانات:
   • SQLite: للاستخدام المحلي والبسيط
   • Oracle: للاستخدام المؤسسي المتقدم

2. إعدادات SQLite:
   • مسار الملف: مكان حفظ قاعدة البيانات
   • مهلة الاتصال: الوقت المسموح للعمليات

3. إعدادات Oracle:
   • عنوان الخادم: IP أو اسم خادم Oracle
   • المنفذ: عادة 1521
   • نوع الاتصال: Service Name أو SID
   • بيانات المستخدم: اسم المستخدم وكلمة المرور

4. اختبار الاتصال:
   • استخدم هذا لتأكيد صحة الإعدادات
   • يجب أن ينجح الاختبار قبل الحفظ

للمساعدة الإضافية، راجع دليل المستخدم."""
        
        QMessageBox.information(self, "مساعدة", help_text)
