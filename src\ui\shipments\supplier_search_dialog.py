# -*- coding: utf-8 -*-
"""
نافذة البحث عن الموردين
Supplier Search Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QMessageBox, QHeaderView, QAbstractItemView)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QKeySequence, QShortcut

from ...database.database_manager import DatabaseManager
from ...database.models import Supplier

class SupplierSearchDialog(QDialog):
    """نافذة البحث عن الموردين"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.selected_supplier = None
        self.setup_ui()
        self.setup_connections()
        self.load_suppliers()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("البحث عن مورد")
        self.setModal(True)
        self.resize(800, 600)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # منطقة البحث
        search_layout = QHBoxLayout()
        
        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالكود أو الاسم...")
        search_layout.addWidget(self.search_edit)
        
        self.search_button = QPushButton("بحث")
        search_layout.addWidget(self.search_button)
        
        self.clear_button = QPushButton("مسح")
        search_layout.addWidget(self.clear_button)
        
        main_layout.addLayout(search_layout)
        
        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(6)
        self.suppliers_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "الشخص المسؤول", "الهاتف", "البريد الإلكتروني", "نشط"
        ])
        
        # إعداد الجدول
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # الكود
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # الاسم
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الشخص المسؤول
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الهاتف
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # البريد
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # نشط
        
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setAlternatingRowColors(True)
        
        main_layout.addWidget(self.suppliers_table)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.select_button = QPushButton("اختيار")
        self.select_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.select_button.setEnabled(False)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.select_button)
        buttons_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(buttons_layout)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.search_button.clicked.connect(self.search_suppliers)
        self.clear_button.clicked.connect(self.clear_search)
        self.select_button.clicked.connect(self.select_supplier)
        self.cancel_button.clicked.connect(self.reject)
        
        # البحث عند الضغط على Enter
        self.search_edit.returnPressed.connect(self.search_suppliers)
        
        # تفعيل زر الاختيار عند تحديد صف
        self.suppliers_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        # اختيار المورد عند النقر المزدوج
        self.suppliers_table.itemDoubleClicked.connect(self.select_supplier)
        
    def load_suppliers(self):
        """تحميل جميع الموردين"""
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()
            self.populate_table(suppliers)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الموردين: {str(e)}")
        finally:
            session.close()
            
    def search_suppliers(self):
        """البحث عن الموردين"""
        search_text = self.search_edit.text().strip()
        if not search_text:
            self.load_suppliers()
            return
            
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter(
                Supplier.is_active == True,
                (Supplier.code.contains(search_text) | 
                 Supplier.name.contains(search_text) |
                 Supplier.name_en.contains(search_text))
            ).all()
            
            self.populate_table(suppliers)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث: {str(e)}")
        finally:
            session.close()
            
    def clear_search(self):
        """مسح البحث"""
        self.search_edit.clear()
        self.load_suppliers()
        
    def populate_table(self, suppliers):
        """ملء الجدول بالموردين"""
        self.suppliers_table.setRowCount(len(suppliers))
        
        for row, supplier in enumerate(suppliers):
            # الكود
            code_item = QTableWidgetItem(supplier.code or "")
            code_item.setData(Qt.UserRole, supplier.id)
            self.suppliers_table.setItem(row, 0, code_item)
            
            # الاسم
            name_item = QTableWidgetItem(supplier.name or "")
            self.suppliers_table.setItem(row, 1, name_item)
            
            # الشخص المسؤول
            contact_item = QTableWidgetItem(supplier.contact_person or "")
            self.suppliers_table.setItem(row, 2, contact_item)
            
            # الهاتف
            phone_item = QTableWidgetItem(supplier.phone or "")
            self.suppliers_table.setItem(row, 3, phone_item)
            
            # البريد الإلكتروني
            email_item = QTableWidgetItem(supplier.email or "")
            self.suppliers_table.setItem(row, 4, email_item)
            
            # نشط
            active_item = QTableWidgetItem("نعم" if supplier.is_active else "لا")
            self.suppliers_table.setItem(row, 5, active_item)
            
    def on_selection_changed(self):
        """عند تغيير التحديد"""
        has_selection = len(self.suppliers_table.selectedItems()) > 0
        self.select_button.setEnabled(has_selection)
        
    def select_supplier(self):
        """اختيار المورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            supplier_id = self.suppliers_table.item(current_row, 0).data(Qt.UserRole)
            
            # الحصول على بيانات المورد
            session = self.db_manager.get_session()
            try:
                self.selected_supplier = session.query(Supplier).filter(
                    Supplier.id == supplier_id
                ).first()
                
                if self.selected_supplier:
                    self.accept()
                else:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على المورد")
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في اختيار المورد: {str(e)}")
            finally:
                session.close()
        else:
            QMessageBox.warning(self, "تحذير", "يجب اختيار مورد أولاً")
            
    def get_selected_supplier(self):
        """الحصول على المورد المختار"""
        return self.selected_supplier
