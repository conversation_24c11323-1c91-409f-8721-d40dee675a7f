# -*- coding: utf-8 -*-
"""
شاشة طلبات الشراء من الموردين
Purchase Orders Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QFormLayout, QLabel, QLineEdit, QPushButton,
                               QTableWidget, QTableWidgetItem, QGroupBox,
                               QMessageBox, QHeaderView, QTextEdit, QComboBox,
                               QDateEdit, QDoubleSpinBox, QSpinBox, QCheckBox,
                               QTabWidget, QSplitter, QFrame, QGridLayout,
                               QScrollArea, QDialog, QDialogButtonBox, QFileDialog,
                               QAbstractItemView, QStatusBar, QToolBar, QMenu)
from PySide6.QtCore import Qt, QDate, Signal, QUrl, QTimer
from PySide6.QtGui import QFont, QIcon, QColor, QDesktopServices, QAction
from datetime import datetime, date
import json
import os

from ...database.database_manager import DatabaseManager
from ...database.models import PurchaseOrder, PurchaseOrderItem, Supplier, Currency, Item, ItemGroup, UnitOfMeasure
from ..shipments.supplier_search_dialog import SupplierSearchDialog
from ..items.item_search_dialog import ItemSearchDialog
from ...utils.formatters import format_currency, format_date

class PurchaseOrdersWindow(QMainWindow):
    """نافذة طلبات الشراء من الموردين"""
    
    def __init__(self, maximize_on_start=True, mode="list"):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_order_id = None
        self.maximize_on_start = maximize_on_start
        self.mode = mode  # "list" للعرض، "entry" للإدخال
        self.setup_ui()
        if self.mode == "list":
            self.load_data()

        # إعداد timer للبحث الفوري
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_instant_search)

        # إعداد الاتصالات
        self.setup_connections()

        # تحميل البيانات فقط في وضع القائمة
        if self.mode == "list":
            self.load_orders()

        # فتح النافذة حسب المعامل
        if self.maximize_on_start:
            self.showMaximized()
        else:
            super().show()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("طلبات الشراء من الموردين - ProShipment")
        self.setMinimumSize(1600, 900)

        # إعداد النافذة للوضع الاحترافي
        self.setup_window_properties()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()

        # إعداد المحتوى الرئيسي
        self.setup_main_content()

    def show(self):
        """إظهار النافذة مع التحكم في الحجم"""
        if self.maximize_on_start:
            super().showMaximized()
        else:
            # إظهار النافذة في الحجم العادي
            super().show()
            # تأكد من أن النافذة ليست في ملء الشاشة
            if self.isMaximized():
                self.showNormal()
            # إعادة تعيين المعامل للمرة القادمة
            self.maximize_on_start = True

    def setup_window_properties(self):
        """إعداد خصائص النافذة"""
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 10)
        self.setFont(font)

        # تطبيق اتجاه RTL
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QMenuBar {
                background-color: #2c3e50;
                color: white;
                border-bottom: 1px solid #34495e;
                font-weight: bold;
                padding: 5px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 15px;
                margin: 2px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #3498db;
            }
            QToolBar {
                background-color: #34495e;
                border: none;
                spacing: 5px;
                padding: 8px;
            }
            QStatusBar {
                background-color: #2c3e50;
                color: white;
                border-top: 1px solid #34495e;
                font-size: 12px;
                padding: 5px;
            }
        """)

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة ملف
        file_menu = menubar.addMenu("ملف")

        from PySide6.QtGui import QAction

        new_order_action = QAction("طلب شراء جديد", self)
        new_order_action.setShortcut("Ctrl+N")
        new_order_action.triggered.connect(self.new_order)
        file_menu.addAction(new_order_action)

        file_menu.addSeparator()

        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_order)
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        print_action = QAction("طباعة", self)
        print_action.setShortcut("Ctrl+P")
        print_action.triggered.connect(self.print_order)
        file_menu.addAction(print_action)

        file_menu.addSeparator()

        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)

        # قائمة تحرير
        edit_menu = menubar.addMenu("تحرير")

        refresh_action = QAction("تحديث البيانات", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.load_data)
        edit_menu.addAction(refresh_action)

        edit_menu.addSeparator()

        delete_action = QAction("حذف الطلب", self)
        delete_action.setShortcut("Delete")
        delete_action.triggered.connect(self.delete_order)
        edit_menu.addAction(delete_action)

        # قائمة العمليات
        operations_menu = menubar.addMenu("العمليات")

        send_to_shipment_action = QAction("إرسال للشحنات", self)
        send_to_shipment_action.triggered.connect(self.send_to_shipment)
        operations_menu.addAction(send_to_shipment_action)

        operations_menu.addSeparator()

        export_action = QAction("تصدير البيانات", self)
        export_action.setShortcut("Ctrl+E")
        export_action.triggered.connect(self.export_data)
        operations_menu.addAction(export_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")

        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        from PySide6.QtGui import QAction

        if self.mode == "entry":
            self.setup_entry_toolbar()
        else:
            self.setup_list_toolbar()

    def setup_list_toolbar(self):
        """إعداد شريط أدوات وضع القائمة"""
        from PySide6.QtGui import QAction

        toolbar = self.addToolBar("أدوات طلبات الشراء")
        toolbar.setMovable(False)

        # طلب جديد
        new_action = QAction("طلب جديد", self)
        new_action.setStatusTip("إنشاء طلب شراء جديد")
        new_action.triggered.connect(self.new_order)
        toolbar.addAction(new_action)

        toolbar.addSeparator()

        # تحديث
        refresh_action = QAction("تحديث", self)
        refresh_action.setStatusTip("تحديث قائمة الطلبات")
        refresh_action.triggered.connect(self.refresh_orders)
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        # تتبع الطلبات
        tracking_action = QAction("تتبع الطلبات", self)
        tracking_action.setStatusTip("فتح نافذة تتبع الطلبات المتقدمة")
        tracking_action.triggered.connect(self.open_tracking_window)
        toolbar.addAction(tracking_action)

    def setup_entry_toolbar(self):
        """إعداد شريط أدوات وضع الإدخال"""
        from PySide6.QtGui import QAction

        toolbar = self.addToolBar("أدوات طلب الشراء")
        toolbar.setMovable(False)

        # حفظ
        save_action = QAction("حفظ", self)
        save_action.setStatusTip("حفظ طلب الشراء")
        save_action.triggered.connect(self.save_order)
        toolbar.addAction(save_action)

        toolbar.addSeparator()

        # إضافة صنف
        add_item_action = QAction("إضافة صنف", self)
        add_item_action.setStatusTip("إضافة صنف جديد للطلب")
        add_item_action.triggered.connect(self.add_item)
        toolbar.addAction(add_item_action)

        # تعديل صنف
        edit_item_action = QAction("تعديل صنف", self)
        edit_item_action.setStatusTip("تعديل الصنف المحدد")
        edit_item_action.triggered.connect(self.edit_item)
        toolbar.addAction(edit_item_action)

        # حذف صنف
        delete_item_action = QAction("حذف صنف", self)
        delete_item_action.setStatusTip("حذف الصنف المحدد")
        delete_item_action.triggered.connect(self.delete_item)
        toolbar.addAction(delete_item_action)

        toolbar.addSeparator()

        # خروج
        exit_action = QAction("خروج", self)
        exit_action.setStatusTip("إغلاق النافذة")
        exit_action.triggered.connect(self.close)
        toolbar.addAction(exit_action)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()

        # رسالة الترحيب
        status_bar.showMessage("مرحباً بك في نظام طلبات الشراء - وضع ملء الشاشة", 3000)

        # إضافة معلومات دائمة
        from PySide6.QtWidgets import QLabel
        permanent_label = QLabel("جاهز للاستخدام")
        permanent_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        status_bar.addPermanentWidget(permanent_label)

    def setup_main_content(self):
        """إعداد المحتوى الرئيسي"""
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)

        # إعداد المحتوى حسب الوضع
        if self.mode == "entry":
            self.setup_entry_mode(main_layout)
        else:
            self.setup_list_mode(main_layout)

    def setup_list_mode(self, main_layout):
        """إعداد وضع العرض (الجدول)"""

        # منطقة البحث والفلترة
        search_group = QGroupBox("البحث والفلترة")
        search_layout = QFormLayout(search_group)

        # البحث بالنص
        search_text_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("بحث فوري في جميع الأعمدة... (اكتب للبحث مباشرة)")
        self.search_button = QPushButton("بحث")
        self.clear_search_button = QPushButton("مسح")

        search_text_layout.addWidget(self.search_edit)
        search_text_layout.addWidget(self.search_button)
        search_text_layout.addWidget(self.clear_search_button)
        search_layout.addRow("البحث:", search_text_layout)

        # فلترة بحالة الطلب
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItem("جميع الحالات", "")
        self.status_filter_combo.addItems([
            "جديد", "مؤكد", "قيد التنفيذ", "مكتمل", "ملغي"
        ])
        search_layout.addRow("حالة الطلب:", self.status_filter_combo)

        # فلترة بالمورد
        self.supplier_filter_combo = QComboBox()
        self.supplier_filter_combo.addItem("جميع الموردين", "")
        search_layout.addRow("المورد:", self.supplier_filter_combo)

        main_layout.addWidget(search_group)

        # جدول طلبات الشراء
        self.orders_table = QTableWidget()
        self.setup_orders_table()
        main_layout.addWidget(self.orders_table)

        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

    def setup_entry_mode(self, main_layout):
        """إعداد وضع الإدخال (النموذج)"""
        # إنشاء تبويبات للإدخال
        from PySide6.QtWidgets import QTabWidget

        self.details_tabs = QTabWidget()

        # استخدام الدوال الموجودة لإنشاء التبويبات
        self.create_basic_info_tab()
        self.create_items_tab()
        self.create_documents_tab()
        self.create_notes_tab()

        main_layout.addWidget(self.details_tabs)

        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

    def setup_orders_table(self):
        """إعداد جدول طلبات الشراء"""
        self.orders_table.setColumnCount(12)
        self.orders_table.setHorizontalHeaderLabels([
            "رقم الطلب", "تاريخ الطلب", "المورد", "إجمالي الأصناف", "إجمالي الكمية",
            "إجمالي القيمة", "العملة", "حالة الطلب", "تاريخ التسليم المتوقع",
            "الكمية المسلمة", "الكمية المتبقية", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.orders_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الطلب
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # تاريخ الطلب
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # المورد - توسيع لإظهار الاسم كاملاً
        header.resizeSection(2, 200)  # توسيع عمود المورد إلى 200 بكسل
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # إجمالي الأصناف
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # إجمالي الكمية
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # إجمالي القيمة
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # العملة
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # حالة الطلب

        # إعداد قائمة الزر الأيمن (Context Menu)
        self.orders_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.orders_table.customContextMenuRequested.connect(self.show_context_menu)
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # تاريخ التسليم المتوقع
        header.setSectionResizeMode(9, QHeaderView.ResizeToContents)  # الكمية المرسلة
        header.setSectionResizeMode(10, QHeaderView.ResizeToContents)  # الكمية المتبقية
        header.setSectionResizeMode(11, QHeaderView.Stretch)  # ملاحظات (قابل للتمدد)

        self.orders_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orders_table.setAlternatingRowColors(True)
        # جعل الجدول للقراءة فقط (غير قابل للتعديل)
        self.orders_table.setEditTriggers(QAbstractItemView.NoEditTriggers)

    def setup_connections(self):
        """إعداد الاتصالات"""
        # اتصالات وضع القائمة
        if hasattr(self, 'search_button'):
            self.search_button.clicked.connect(self.search_orders)
        if hasattr(self, 'clear_search_button'):
            self.clear_search_button.clicked.connect(self.clear_search)
        if hasattr(self, 'search_edit'):
            self.search_edit.returnPressed.connect(self.search_orders)
            # البحث الفوري السريع - البحث أثناء الكتابة
            self.search_edit.textChanged.connect(self.instant_search)

        # فلترة عند تغيير الكومبو بوكس
        if hasattr(self, 'status_filter_combo'):
            self.status_filter_combo.currentTextChanged.connect(self.filter_orders)
        if hasattr(self, 'supplier_filter_combo'):
            self.supplier_filter_combo.currentTextChanged.connect(self.filter_orders)

        # النقر المزدوج لفتح الطلب
        if hasattr(self, 'orders_table'):
            self.orders_table.itemDoubleClicked.connect(self.edit_order_from_table)

    def load_orders(self):
        """تحميل طلبات الشراء"""
        # هذه الدالة تعمل فقط في وضع القائمة
        if self.mode != "list":
            return

        session = self.db_manager.get_session()
        try:
            from ...database.models import PurchaseOrder, Supplier, PurchaseOrderItem

            orders = session.query(PurchaseOrder).outerjoin(Supplier).filter(
                PurchaseOrder.is_active == True
            ).order_by(PurchaseOrder.created_at.desc()).all()

            # تحميل بيانات الأصناف لكل طلب
            for order in orders:
                order.items = session.query(PurchaseOrderItem).filter(
                    PurchaseOrderItem.purchase_order_id == order.id
                ).all()

            if hasattr(self, 'populate_orders_table'):
                self.populate_orders_table(orders)
            if hasattr(self, 'load_suppliers_filter'):
                self.load_suppliers_filter()
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage(f"تم تحميل {len(orders)} طلب شراء")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الطلبات: {str(e)}")
        finally:
            session.close()

    def load_suppliers_filter(self):
        """تحميل قائمة الموردين للفلترة"""
        session = self.db_manager.get_session()
        try:
            from ...database.models import Supplier
            suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()

            self.supplier_filter_combo.clear()
            self.supplier_filter_combo.addItem("جميع الموردين", "")

            for supplier in suppliers:
                self.supplier_filter_combo.addItem(supplier.name, supplier.id)

        except Exception as e:
            print(f"خطأ في تحميل الموردين: {str(e)}")
        finally:
            session.close()

    def populate_orders_table(self, orders):
        """ملء الجدول بطلبات الشراء"""
        from ...utils.formatters import format_date, format_currency

        self.orders_table.setRowCount(len(orders))

        for row, order in enumerate(orders):
            # رقم الطلب
            order_number_item = QTableWidgetItem(order.order_number or "")
            order_number_item.setData(Qt.UserRole, order.id)  # حفظ معرف الطلب
            self.orders_table.setItem(row, 0, order_number_item)

            # تاريخ الطلب
            date_text = format_date(order.order_date, 'short') if order.order_date else ""
            date_item = QTableWidgetItem(date_text)
            self.orders_table.setItem(row, 1, date_item)

            # المورد
            supplier_name = order.supplier.name if order.supplier else ""
            supplier_item = QTableWidgetItem(supplier_name)
            self.orders_table.setItem(row, 2, supplier_item)

            # إجمالي الأصناف
            items_count = len(order.items) if hasattr(order, 'items') and order.items else 0
            items_count_item = QTableWidgetItem(str(items_count))
            self.orders_table.setItem(row, 3, items_count_item)

            # إجمالي الكمية
            total_quantity = 0
            if hasattr(order, 'items') and order.items:
                total_quantity = sum(item.quantity for item in order.items)
            quantity_item = QTableWidgetItem(f"{total_quantity:.2f}")
            self.orders_table.setItem(row, 4, quantity_item)

            # إجمالي القيمة مع العملة
            total_value = order.total_amount or 0
            currency_symbol = ""
            currency_code = ""

            if order.currency:
                currency_symbol = order.currency.symbol or ""
                currency_code = order.currency.code or ""

            # تنسيق القيمة مع العملة
            if currency_symbol:
                value_text = f"{format_currency(total_value)} {currency_symbol}"
            elif currency_code:
                value_text = f"{format_currency(total_value)} {currency_code}"
            else:
                value_text = format_currency(total_value)

            value_item = QTableWidgetItem(value_text)
            self.orders_table.setItem(row, 5, value_item)

            # العملة
            currency_text = order.currency.name if order.currency else ""
            currency_item = QTableWidgetItem(currency_text)
            self.orders_table.setItem(row, 6, currency_item)

            # حالة الطلب
            status_item = QTableWidgetItem(order.order_status or "")
            self.orders_table.setItem(row, 7, status_item)

            # تاريخ التسليم المتوقع
            delivery_date_text = format_date(order.expected_delivery_date, 'short') if order.expected_delivery_date else ""
            delivery_date_item = QTableWidgetItem(delivery_date_text)
            self.orders_table.setItem(row, 8, delivery_date_item)

            # الكمية المسلمة
            delivered_quantity = 0
            if hasattr(order, 'items') and order.items:
                delivered_quantity = sum(item.delivered_quantity or 0 for item in order.items)
            delivered_item = QTableWidgetItem(f"{delivered_quantity:.2f}")
            self.orders_table.setItem(row, 9, delivered_item)

            # الكمية المتبقية
            remaining_quantity = total_quantity - delivered_quantity
            remaining_item = QTableWidgetItem(f"{remaining_quantity:.2f}")
            self.orders_table.setItem(row, 10, remaining_item)

            # ملاحظات
            notes_item = QTableWidgetItem(order.notes or "")
            self.orders_table.setItem(row, 11, notes_item)

    def instant_search(self):
        """البحث الفوري أثناء الكتابة"""
        self.search_timer.stop()
        self.search_timer.start(300)  # انتظار 300 مللي ثانية بعد التوقف عن الكتابة

    def perform_instant_search(self):
        """تنفيذ البحث الفوري"""
        self.search_orders()

    def search_orders(self):
        """البحث في الطلبات"""
        search_text = self.search_edit.text().strip().lower()

        for row in range(self.orders_table.rowCount()):
            show_row = True

            if search_text:
                # البحث في جميع الأعمدة
                row_text = ""
                for col in range(self.orders_table.columnCount()):
                    item = self.orders_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                show_row = search_text in row_text

            self.orders_table.setRowHidden(row, not show_row)

        self.apply_filters()

    def filter_orders(self):
        """فلترة الطلبات"""
        self.apply_filters()

    def apply_filters(self):
        """تطبيق الفلاتر"""
        status_filter = self.status_filter_combo.currentData()
        supplier_filter = self.supplier_filter_combo.currentData()

        for row in range(self.orders_table.rowCount()):
            if self.orders_table.isRowHidden(row):
                continue  # تخطي الصفوف المخفية بالبحث

            show_row = True

            # فلتر حالة الطلب
            if status_filter:
                status_item = self.orders_table.item(row, 7)  # عمود حالة الطلب
                if status_item and status_item.text() != status_filter:
                    show_row = False

            # فلتر المورد
            if supplier_filter:
                # نحتاج للحصول على معرف المورد من البيانات
                order_id_item = self.orders_table.item(row, 0)
                if order_id_item:
                    order_id = order_id_item.data(Qt.UserRole)
                    session = self.db_manager.get_session()
                    try:
                        from ...database.models import PurchaseOrder
                        order = session.query(PurchaseOrder).filter(PurchaseOrder.id == order_id).first()
                        if order and order.supplier_id != supplier_filter:
                            show_row = False
                    finally:
                        session.close()

            self.orders_table.setRowHidden(row, not show_row)

    def clear_search(self):
        """مسح البحث"""
        self.search_edit.clear()
        self.status_filter_combo.setCurrentIndex(0)
        self.supplier_filter_combo.setCurrentIndex(0)

        # إظهار جميع الصفوف
        for row in range(self.orders_table.rowCount()):
            self.orders_table.setRowHidden(row, False)

    def show_context_menu(self, position):
        """إظهار قائمة الزر الأيمن للجدول"""
        # التحقق من وجود صف محدد
        item = self.orders_table.itemAt(position)
        if item is None:
            return

        # الحصول على معرف الطلب من الصف المحدد
        current_row = item.row()
        order_id_item = self.orders_table.item(current_row, 0)
        if not order_id_item:
            return

        order_id = order_id_item.data(Qt.UserRole)
        if not order_id:
            return

        # إنشاء قائمة الزر الأيمن
        context_menu = QMenu(self)

        # إضافة أيقونات للعمليات
        from PySide6.QtGui import QIcon

        # عملية تعديل الطلب
        edit_action = context_menu.addAction("تعديل الطلب")
        edit_action.setIcon(QIcon(":/icons/edit.png"))  # يمكن إضافة أيقونة لاحقاً
        edit_action.triggered.connect(lambda: self.edit_order_from_context_menu(order_id))

        # فاصل
        context_menu.addSeparator()

        # عملية حذف الطلب
        delete_action = context_menu.addAction("حذف الطلب")
        delete_action.setIcon(QIcon(":/icons/delete.png"))  # يمكن إضافة أيقونة لاحقاً
        delete_action.triggered.connect(lambda: self.delete_order_from_context_menu(order_id))

        # إظهار القائمة في الموضع المحدد
        context_menu.exec(self.orders_table.mapToGlobal(position))

    def edit_order_from_context_menu(self, order_id):
        """تعديل الطلب من قائمة الزر الأيمن"""
        try:
            if order_id:
                self.open_edit_window(order_id)
            else:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على معرف الطلب")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة التعديل:\n{str(e)}")

    def delete_order_from_context_menu(self, order_id):
        """حذف الطلب من قائمة الزر الأيمن"""
        try:
            # التأكد من رغبة المستخدم في الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "هل أنت متأكد من رغبتك في حذف هذا الطلب؟\n\nتحذير: هذه العملية لا يمكن التراجع عنها!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تنفيذ عملية الحذف
                session = self.db_manager.get_session()
                try:
                    from ...database.models import PurchaseOrder

                    # البحث عن الطلب
                    order = session.query(PurchaseOrder).filter(PurchaseOrder.id == order_id).first()
                    if order:
                        # حذف منطقي - تعيين is_active إلى False
                        order.is_active = False
                        session.commit()

                        QMessageBox.information(self, "نجح", "تم حذف الطلب بنجاح")

                        # إعادة تحميل البيانات
                        self.load_orders()
                    else:
                        QMessageBox.warning(self, "تحذير", "لم يتم العثور على الطلب المحدد")

                except Exception as e:
                    session.rollback()
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الطلب:\n{str(e)}")
                finally:
                    session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الطلب:\n{str(e)}")

    def edit_order_from_table(self):
        """تحرير الطلب من الجدول"""
        current_row = self.orders_table.currentRow()
        if current_row >= 0:
            order_id_item = self.orders_table.item(current_row, 0)
            if order_id_item:
                order_id = order_id_item.data(Qt.UserRole)
                if order_id:
                    # في التصميم الجديد، نفتح نافذة تحرير منفصلة
                    self.open_edit_window(order_id)
                else:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على معرف الطلب")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد طلب للتحرير")

    def open_edit_window(self, order_id):
        """فتح نافذة تحرير منفصلة للطلب"""
        try:
            # إنشاء نافذة تحرير جديدة في وضع الإدخال
            edit_window = PurchaseOrdersWindow(maximize_on_start=False, mode="entry")
            edit_window.current_order_id = order_id
            edit_window.setWindowTitle(f"تحرير طلب الشراء - {order_id}")

            # تحميل بيانات الطلب في النافذة الجديدة
            if hasattr(edit_window, 'load_order_details'):
                edit_window.load_order_details(order_id)

            edit_window.show()
            self.status_bar.showMessage(f"تم فتح نافذة تحرير الطلب")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة التحرير: {str(e)}")

    def load_order_by_id(self, order_id):
        """تحميل طلب بالمعرف - للاستخدام الداخلي فقط"""
        # هذه الدالة للاستخدام الداخلي فقط
        pass

    def create_order_details_direct(self, parent_layout):
        """إنشاء تفاصيل الطلب مباشرة في التخطيط الرئيسي"""
        # إطار التفاصيل محسن للعرض الكامل
        details_frame = QFrame()
        details_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #27ae60;
                border-radius: 12px;
                padding: 10px;
            }
        """)
        details_layout = QVBoxLayout(details_frame)
        details_layout.setContentsMargins(15, 15, 15, 15)
        details_layout.setSpacing(10)

        # عنوان قسم إدخال الطلبات
        details_title = QLabel("📝 إدخال وتحرير طلبات الشراء")
        details_title.setFont(QFont("Segoe UI", 14, QFont.Bold))
        details_title.setStyleSheet("""
            QLabel {
                color: white;
                padding: 12px 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #27ae60, stop:1 #229954);
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        details_layout.addWidget(details_title)

        # تبويبات التفاصيل محسنة للعرض الكامل
        self.details_tabs = QTabWidget()
        self.details_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 18px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QTabBar::tab:selected {
                background-color: #27ae60;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #d5f4e6;
            }
        """)

        # تبويب البيانات الأساسية
        self.create_basic_info_tab()

        # تبويب الأصناف
        self.create_items_tab()

        # تبويب المستندات
        self.create_documents_tab()

        # تبويب الملاحظات
        self.create_notes_tab()

        details_layout.addWidget(self.details_tabs)
        parent_layout.addWidget(details_frame)

    def create_orders_list(self, parent_splitter):
        """إنشاء قائمة الطلبات - تم نقلها إلى نافذة منفصلة"""
        # هذه الدالة لم تعد مستخدمة بعد إعادة التنظيم
        # تم نقل وظيفة قائمة الطلبات إلى PurchaseOrdersListWindow
        pass

    def create_color_legend(self, parent_layout):
        """إنشاء مفتاح الألوان محسن"""
        legend_frame = QFrame()
        legend_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        legend_frame.setMaximumHeight(80)
        legend_layout = QHBoxLayout(legend_frame)
        legend_layout.setSpacing(15)

        # عنوان المفتاح محسن
        legend_title = QLabel("🎨 مفتاح الألوان:")
        legend_title.setFont(QFont("Segoe UI", 11, QFont.Bold))
        legend_title.setStyleSheet("color: #2c3e50; margin-right: 10px;")
        legend_layout.addWidget(legend_title)

        # عناصر المفتاح محسنة
        legend_items = [
            ("unused", "#ffffff", "لم يستخدم"),
            ("partially_used", "#fff3cd", "مستخدم جزئياً"),
            ("fully_used", "#d4edda", "مستخدم بالكامل"),
            ("empty", "#f8d7da", "طلب فارغ")
        ]

        for status, color, text in legend_items:
            # حاوية العنصر
            item_widget = QWidget()
            item_layout = QHBoxLayout(item_widget)
            item_layout.setContentsMargins(5, 5, 5, 5)
            item_layout.setSpacing(5)

            # مربع اللون محسن
            color_box = QLabel()
            color_box.setFixedSize(25, 25)
            color_box.setStyleSheet(f"""
                background-color: {color};
                border: 2px solid #bdc3c7;
                border-radius: 4px;
            """)

            # النص محسن
            text_label = QLabel(text)
            text_label.setStyleSheet("color: #2c3e50; font-size: 10px; font-weight: bold;")
            text_label.setFont(QFont("Segoe UI", 9))

            item_layout.addWidget(color_box)
            item_layout.addWidget(text_label)

            legend_layout.addWidget(item_widget)

        legend_layout.addStretch()
        parent_layout.addWidget(legend_frame)

    def create_order_details(self, parent_splitter):
        """إنشاء تفاصيل الطلب محسن للموضع العلوي"""
        # إطار التفاصيل محسن للموضع العلوي
        details_frame = QFrame()
        details_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #27ae60;
                border-radius: 12px;
                padding: 8px;
            }
        """)
        details_layout = QVBoxLayout(details_frame)
        details_layout.setContentsMargins(10, 10, 10, 10)
        details_layout.setSpacing(8)

        # عنوان قسم إدخال الطلبات
        details_title = QLabel("📝 إدخال وتحرير طلبات الشراء")
        details_title.setFont(QFont("Segoe UI", 13, QFont.Bold))
        details_title.setStyleSheet("""
            QLabel {
                color: white;
                padding: 8px 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #27ae60, stop:1 #229954);
                border-radius: 6px;
                margin-bottom: 5px;
            }
        """)
        details_layout.addWidget(details_title)

        # تبويبات التفاصيل محسنة
        self.details_tabs = QTabWidget()
        self.details_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 8px 15px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #27ae60;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #d5f4e6;
            }
        """)

        # تبويب البيانات الأساسية
        self.create_basic_info_tab()

        # تبويب الأصناف
        self.create_items_tab()

        # تبويب المستندات
        self.create_documents_tab()

        # تبويب الملاحظات
        self.create_notes_tab()

        details_layout.addWidget(self.details_tabs)
        parent_splitter.addWidget(details_frame)

    def create_basic_info_tab(self):
        """إنشاء تبويب البيانات الأساسية محسن"""
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)
        basic_layout.setSpacing(15)

        # مجموعة بيانات الطلب
        order_group = QGroupBox("بيانات الطلب")
        order_layout = QGridLayout(order_group)
        order_layout.setSpacing(10)

        # الصف الأول: رقم الطلب وتاريخ الطلب
        # رقم الطلب
        order_number_label = QLabel("رقم الطلب:")
        self.order_number_edit = QLineEdit()
        self.order_number_edit.setReadOnly(True)
        self.order_number_edit.setMinimumHeight(35)
        order_layout.addWidget(order_number_label, 0, 0)
        order_layout.addWidget(self.order_number_edit, 0, 1)

        # تاريخ الطلب
        order_date_label = QLabel("تاريخ الطلب:")
        from ..widgets.flexible_date_edit import FlexibleDateEdit
        self.order_date_edit = FlexibleDateEdit()
        self.order_date_edit.setDate(QDate.currentDate())
        self.order_date_edit.setMinimumHeight(35)
        order_layout.addWidget(order_date_label, 0, 2)
        order_layout.addWidget(self.order_date_edit, 0, 3)

        # الصف الثاني: المورد وحالة الطلب
        # المورد
        supplier_label = QLabel("المورد:")
        supplier_layout = QHBoxLayout()
        self.supplier_edit = QLineEdit()
        self.supplier_edit.setReadOnly(True)
        self.supplier_edit.setMinimumHeight(35)
        self.supplier_search_btn = QPushButton("بحث")
        self.supplier_search_btn.setMinimumHeight(35)
        self.supplier_search_btn.clicked.connect(self.search_supplier)
        supplier_layout.addWidget(self.supplier_edit)
        supplier_layout.addWidget(self.supplier_search_btn)
        order_layout.addWidget(supplier_label, 1, 0)
        order_layout.addLayout(supplier_layout, 1, 1)

        # حالة الطلب
        status_label = QLabel("حالة الطلب:")
        self.order_status_combo = QComboBox()
        self.order_status_combo.addItems([
            "مسودة", "مرسل", "مؤكد", "جزئي", "مكتمل", "ملغي"
        ])
        self.order_status_combo.setMinimumHeight(35)
        order_layout.addWidget(status_label, 1, 2)
        order_layout.addWidget(self.order_status_combo, 1, 3)

        basic_layout.addWidget(order_group)
        
        # مجموعة البيانات المالية
        financial_group = QGroupBox("البيانات المالية")
        financial_layout = QGridLayout(financial_group)
        financial_layout.setSpacing(10)

        # الصف الأول: العملة وسعر الصرف
        # العملة
        currency_label = QLabel("العملة:")
        self.currency_combo = QComboBox()
        self.currency_combo.setMinimumHeight(35)
        self.currency_combo.setStyleSheet(self.get_input_style())
        # تحميل العملات من قاعدة البيانات - سيتم استدعاؤها بعد إنشاء جميع العناصر
        financial_layout.addWidget(currency_label, 0, 0)
        financial_layout.addWidget(self.currency_combo, 0, 1)

        # سعر الصرف
        exchange_rate_label = QLabel("سعر الصرف:")
        self.exchange_rate_edit = QLineEdit()
        self.exchange_rate_edit.setText("1.0000")
        self.exchange_rate_edit.setMinimumHeight(35)
        financial_layout.addWidget(exchange_rate_label, 0, 2)
        financial_layout.addWidget(self.exchange_rate_edit, 0, 3)

        # الصف الثاني: مبلغ الخصم ومبلغ الضريبة
        # مبلغ الخصم
        discount_label = QLabel("مبلغ الخصم:")
        self.discount_amount_edit = QLineEdit()
        self.discount_amount_edit.setText("0.00")
        self.discount_amount_edit.setMinimumHeight(35)
        financial_layout.addWidget(discount_label, 1, 0)
        financial_layout.addWidget(self.discount_amount_edit, 1, 1)

        # مبلغ الضريبة
        tax_label = QLabel("مبلغ الضريبة:")
        self.tax_amount_edit = QLineEdit()
        self.tax_amount_edit.setText("0.00")
        self.tax_amount_edit.setMinimumHeight(35)
        financial_layout.addWidget(tax_label, 1, 2)
        financial_layout.addWidget(self.tax_amount_edit, 1, 3)

        # الصف الثالث: المبلغ الإجمالي
        # المبلغ الإجمالي
        total_label = QLabel("المبلغ الإجمالي:")
        self.total_amount_edit = QLineEdit()
        self.total_amount_edit.setText("0.00")
        self.total_amount_edit.setReadOnly(True)
        self.total_amount_edit.setMinimumHeight(35)
        self.total_amount_edit.setStyleSheet("background-color: #f0f0f0; font-weight: bold; font-size: 14px;")
        financial_layout.addWidget(total_label, 2, 0)
        financial_layout.addWidget(self.total_amount_edit, 2, 1, 1, 3)  # يمتد عبر 3 أعمدة

        # ربط الحقول المالية بحساب الإجمالي التلقائي
        self.discount_amount_edit.textChanged.connect(self.calculate_total)
        self.tax_amount_edit.textChanged.connect(self.calculate_total)

        basic_layout.addWidget(financial_group)
        
        # مجموعة التواريخ
        dates_group = QGroupBox("التواريخ المهمة")
        dates_layout = QGridLayout(dates_group)
        dates_layout.setSpacing(10)

        # الصف الأول: تاريخ التسليم المتوقع وتاريخ التسليم الفعلي
        # تاريخ التسليم المتوقع
        expected_date_label = QLabel("تاريخ التسليم المتوقع:")
        from ..widgets.flexible_date_edit import FlexibleDateEdit
        self.expected_delivery_date_edit = FlexibleDateEdit()
        self.expected_delivery_date_edit.setMinimumHeight(35)
        dates_layout.addWidget(expected_date_label, 0, 0)
        dates_layout.addWidget(self.expected_delivery_date_edit, 0, 1)

        # تاريخ التسليم الفعلي
        actual_date_label = QLabel("تاريخ التسليم الفعلي:")
        self.actual_delivery_date_edit = FlexibleDateEdit()
        self.actual_delivery_date_edit.setMinimumHeight(35)
        dates_layout.addWidget(actual_date_label, 0, 2)
        dates_layout.addWidget(self.actual_delivery_date_edit, 0, 3)

        basic_layout.addWidget(dates_group)
        basic_layout.addStretch()
        
        # إضافة تصميم محسن للتبويب
        basic_tab.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #2c3e50;
            }
            QLineEdit, QComboBox, QDateEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
            }
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {
                border: 2px solid #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)

        self.details_tabs.addTab(basic_tab, "البيانات الأساسية")

        # تحميل العملات بعد إنشاء جميع العناصر
        self.load_currencies()
    def create_items_tab(self):
        """إنشاء تبويب الأصناف"""
        items_tab = QWidget()
        items_layout = QVBoxLayout(items_tab)
        
        # شريط أدوات الأصناف
        items_toolbar = QHBoxLayout()
        
        self.add_item_btn = QPushButton("إضافة صنف")
        self.add_item_btn.clicked.connect(self.add_item)
        
        self.edit_item_btn = QPushButton("تعديل صنف")
        self.edit_item_btn.clicked.connect(self.edit_item)
        
        self.delete_item_btn = QPushButton("حذف صنف")
        self.delete_item_btn.clicked.connect(self.delete_item)
        
        items_toolbar.addWidget(self.add_item_btn)
        items_toolbar.addWidget(self.edit_item_btn)
        items_toolbar.addWidget(self.delete_item_btn)
        items_toolbar.addStretch()
        
        items_layout.addLayout(items_toolbar)
        
        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(10)
        self.items_table.setHorizontalHeaderLabels([
            "كود الصنف", "اسم الصنف", "الكمية", "سعر الوحدة",
            "تاريخ الإنتاج", "تاريخ الانتهاء", "الخصم", "الإجمالي", "المسلم", "المتبقي"
        ])
        
        # تنسيق جدول الأصناف
        items_header = self.items_table.horizontalHeader()
        items_header.setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.setAlternatingRowColors(True)
        
        items_layout.addWidget(self.items_table)
        
        self.details_tabs.addTab(items_tab, "الأصناف")

    def get_input_style(self, readonly=False):
        """الحصول على نمط الحقول"""
        bg_color = "#f8f9fa" if readonly else "white"
        return f"""
            QLineEdit, QComboBox, QTextEdit, QDateEdit {{
                background-color: {bg_color};
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                min-height: 20px;
            }}
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus, QDateEdit:focus {{
                border: 2px solid #3498db;
                background-color: #f8f9fa;
            }}
        """

    def create_documents_tab(self):
        """إنشاء تبويب المستندات"""
        documents_tab = QWidget()
        self.details_tabs.addTab(documents_tab, "📄 المستندات")

        layout = QVBoxLayout(documents_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # قسم روابط المستندات المحددة
        documents_links_group = QGroupBox("روابط المستندات")
        documents_links_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #9b59b6;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #9b59b6;
                background-color: white;
            }
        """)
        documents_links_layout = QGridLayout(documents_links_group)
        documents_links_layout.setSpacing(8)

        # العقد مع المورد
        self.contract_label = QLabel("رابط العقد مع المورد:")
        self.contract_url_display = QLabel("لا يوجد رابط")
        self.contract_url_display.setStyleSheet("""
            QLabel {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
                color: #3498db;
                text-decoration: underline;
            }
        """)
        self.contract_url_display.setOpenExternalLinks(True)
        self.contract_url_display.setTextInteractionFlags(Qt.TextInteractionFlag.TextBrowserInteraction)

        self.contract_add_link_btn = QPushButton("إضافة رابط")
        self.contract_add_link_btn.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")
        self.contract_add_attachment_btn = QPushButton("إضافة مرفق")
        self.contract_add_attachment_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        documents_links_layout.addWidget(self.contract_label, 0, 0)
        documents_links_layout.addWidget(self.contract_url_display, 0, 1)
        documents_links_layout.addWidget(self.contract_add_link_btn, 0, 2)
        documents_links_layout.addWidget(self.contract_add_attachment_btn, 0, 3)

        # التصاميم الأولية
        self.initial_designs_label = QLabel("رابط التصاميم الأولية:")
        self.initial_designs_url_display = QLabel("لا يوجد رابط")
        self.initial_designs_url_display.setStyleSheet("""
            QLabel {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
                color: #3498db;
                text-decoration: underline;
            }
        """)
        self.initial_designs_url_display.setOpenExternalLinks(True)
        self.initial_designs_url_display.setTextInteractionFlags(Qt.TextInteractionFlag.TextBrowserInteraction)

        self.initial_designs_add_link_btn = QPushButton("إضافة رابط")
        self.initial_designs_add_link_btn.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")
        self.initial_designs_add_attachment_btn = QPushButton("إضافة مرفق")
        self.initial_designs_add_attachment_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        documents_links_layout.addWidget(self.initial_designs_label, 1, 0)
        documents_links_layout.addWidget(self.initial_designs_url_display, 1, 1)
        documents_links_layout.addWidget(self.initial_designs_add_link_btn, 1, 2)
        documents_links_layout.addWidget(self.initial_designs_add_attachment_btn, 1, 3)

        # التصميم النهائي المعتمد
        self.final_design_label = QLabel("رابط التصميم النهائي المعتمد:")
        self.final_design_url_display = QLabel("لا يوجد رابط")
        self.final_design_url_display.setStyleSheet("""
            QLabel {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
                color: #3498db;
                text-decoration: underline;
            }
        """)
        self.final_design_url_display.setOpenExternalLinks(True)
        self.final_design_url_display.setTextInteractionFlags(Qt.TextInteractionFlag.TextBrowserInteraction)

        self.final_design_add_link_btn = QPushButton("إضافة رابط")
        self.final_design_add_link_btn.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")
        self.final_design_add_attachment_btn = QPushButton("إضافة مرفق")
        self.final_design_add_attachment_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        documents_links_layout.addWidget(self.final_design_label, 2, 0)
        documents_links_layout.addWidget(self.final_design_url_display, 2, 1)
        documents_links_layout.addWidget(self.final_design_add_link_btn, 2, 2)
        documents_links_layout.addWidget(self.final_design_add_attachment_btn, 2, 3)

        # مرفقات أخرى
        self.other_attachments_label = QLabel("رابط مرفقات أخرى:")
        self.other_attachments_url_display = QLabel("لا يوجد رابط")
        self.other_attachments_url_display.setStyleSheet("""
            QLabel {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
                color: #3498db;
                text-decoration: underline;
            }
        """)
        self.other_attachments_url_display.setOpenExternalLinks(True)
        self.other_attachments_url_display.setTextInteractionFlags(Qt.TextInteractionFlag.TextBrowserInteraction)

        self.other_attachments_add_link_btn = QPushButton("إضافة رابط")
        self.other_attachments_add_link_btn.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")
        self.other_attachments_add_attachment_btn = QPushButton("إضافة مرفق")
        self.other_attachments_add_attachment_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        documents_links_layout.addWidget(self.other_attachments_label, 3, 0)
        documents_links_layout.addWidget(self.other_attachments_url_display, 3, 1)
        documents_links_layout.addWidget(self.other_attachments_add_link_btn, 3, 2)
        documents_links_layout.addWidget(self.other_attachments_add_attachment_btn, 3, 3)

        layout.addWidget(documents_links_group)

        # ربط أزرار إضافة الروابط
        self.contract_add_link_btn.clicked.connect(lambda: self.add_link_dialog('contract'))
        self.initial_designs_add_link_btn.clicked.connect(lambda: self.add_link_dialog('initial_designs'))
        self.final_design_add_link_btn.clicked.connect(lambda: self.add_link_dialog('final_design'))
        self.other_attachments_add_link_btn.clicked.connect(lambda: self.add_link_dialog('other_attachments'))

        # ربط أزرار إضافة المرفقات
        self.contract_add_attachment_btn.clicked.connect(lambda: self.add_attachment_with_manager('contract'))
        self.initial_designs_add_attachment_btn.clicked.connect(lambda: self.add_attachment_with_manager('initial_designs'))
        self.final_design_add_attachment_btn.clicked.connect(lambda: self.add_attachment_with_manager('final_design'))
        self.other_attachments_add_attachment_btn.clicked.connect(lambda: self.add_attachment_with_manager('other_attachments'))

    def create_notes_tab(self):
        """إنشاء تبويب الملاحظات"""
        notes_tab = QWidget()
        notes_layout = QVBoxLayout(notes_tab)
        
        # الملاحظات
        notes_group = QGroupBox("الملاحظات")
        notes_form = QFormLayout(notes_group)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        notes_form.addRow("ملاحظات:", self.notes_edit)
        
        notes_layout.addWidget(notes_group)
        
        # الشروط والأحكام
        terms_group = QGroupBox("الشروط والأحكام")
        terms_form = QFormLayout(terms_group)
        
        self.terms_conditions_edit = QTextEdit()
        self.terms_conditions_edit.setMaximumHeight(150)
        terms_form.addRow("الشروط والأحكام:", self.terms_conditions_edit)
        
        notes_layout.addWidget(terms_group)
        notes_layout.addStretch()
        
        self.details_tabs.addTab(notes_tab, "الملاحظات")

    def add_link_dialog(self, document_type):
        """حوار إضافة رابط"""
        from ..shipments.add_link_dialog import AddLinkDialog

        title_map = {
            'contract': 'العقد مع المورد',
            'initial_designs': 'التصاميم الأولية',
            'final_design': 'التصميم النهائي المعتمد',
            'other_attachments': 'مرفقات أخرى'
        }

        # الحصول على الرابط الحالي من النص المعروض
        current_url = ""
        if document_type == 'contract':
            current_text = self.contract_url_display.text()
            if current_text != "لا يوجد رابط" and "href=" in current_text:
                # استخراج الرابط من HTML
                import re
                match = re.search(r'href="([^"]*)"', current_text)
                if match:
                    current_url = match.group(1)
        elif document_type == 'initial_designs':
            current_text = self.initial_designs_url_display.text()
            if current_text != "لا يوجد رابط" and "href=" in current_text:
                import re
                match = re.search(r'href="([^"]*)"', current_text)
                if match:
                    current_url = match.group(1)
        elif document_type == 'final_design':
            current_text = self.final_design_url_display.text()
            if current_text != "لا يوجد رابط" and "href=" in current_text:
                import re
                match = re.search(r'href="([^"]*)"', current_text)
                if match:
                    current_url = match.group(1)
        elif document_type == 'other_attachments':
            current_text = self.other_attachments_url_display.text()
            if current_text != "لا يوجد رابط" and "href=" in current_text:
                import re
                match = re.search(r'href="([^"]*)"', current_text)
                if match:
                    current_url = match.group(1)

        # فتح نافذة إضافة الرابط
        dialog = AddLinkDialog(self, title_map.get(document_type, 'مستند'), current_url, "")
        if dialog.exec() == QDialog.Accepted:
            link_data = dialog.get_link_data()
            url = link_data['url']
            description = link_data['description'] or url

            # تحديث الحقل المناسب بتنسيق HTML للرابط التشعبي
            if url:
                html_link = f'<a href="{url}" style="color: #3498db; text-decoration: underline;">{description}</a>'

                if document_type == 'contract':
                    self.contract_url_display.setText(html_link)
                elif document_type == 'initial_designs':
                    self.initial_designs_url_display.setText(html_link)
                elif document_type == 'final_design':
                    self.final_design_url_display.setText(html_link)
                elif document_type == 'other_attachments':
                    self.other_attachments_url_display.setText(html_link)



                # إضافة tooltip مع الوصف
                tooltip_text = f"{title_map.get(document_type, 'مستند')}\nانقر لفتح الرابط"
                if description:
                    tooltip_text = f"{title_map.get(document_type, 'مستند')}\n{description}\nانقر لفتح الرابط"

                # تحديد الحقل المناسب وإضافة tooltip
                if document_type == 'contract':
                    self.contract_url_display.setToolTip(tooltip_text)
                elif document_type == 'initial_designs':
                    self.initial_designs_url_display.setToolTip(tooltip_text)
                elif document_type == 'final_design':
                    self.final_design_url_display.setToolTip(tooltip_text)
                elif document_type == 'other_attachments':
                    self.other_attachments_url_display.setToolTip(tooltip_text)

            # تحديث حالة الطلب عند إضافة رابط
            self.update_order_status('confirmed')

    def add_file_attachment(self, document_type):
        """إضافة مرفق ملف"""
        from ..shipments.attachments_manager_dialog import AttachmentsManagerDialog

        title_map = {
            'contract': 'العقد مع المورد',
            'initial_designs': 'التصاميم الأولية',
            'final_design': 'التصميم النهائي المعتمد',
            'other_attachments': 'مرفقات أخرى'
        }

        # الحصول على الملفات الحالية
        existing_files = []
        current_path = ""
        if document_type == 'contract':
            current_path = self.contract_url_edit.text().strip()
        elif document_type == 'initial_designs':
            current_path = self.initial_designs_url_edit.text().strip()
        elif document_type == 'final_design':
            current_path = self.final_design_url_edit.text().strip()
        elif document_type == 'other_attachments':
            current_path = self.other_attachments_url_edit.text().strip()

        # تنظيف المسار من بروتوكول file://
        if current_path.startswith('file:///'):
            current_path = current_path[8:]  # إزالة file:///

        if current_path and os.path.exists(current_path):
            existing_files = [current_path]

        # فتح نافذة إدارة المرفقات
        dialog = AttachmentsManagerDialog(self, title_map.get(document_type, 'مستندات'), existing_files)
        if dialog.exec() == QDialog.Accepted:
            # الحصول على قائمة الملفات المحدثة
            updated_files = dialog.get_files_list()

            # تحديث الحقل بأول ملف أو مسح الحقل إذا لم توجد ملفات
            file_path = updated_files[0] if updated_files else ""

            if document_type == 'contract':
                self.contract_url_edit.setText(file_path)
            elif document_type == 'initial_designs':
                self.initial_designs_url_edit.setText(file_path)
            elif document_type == 'final_design':
                self.final_design_url_edit.setText(file_path)
            elif document_type == 'other_attachments':
                self.other_attachments_url_edit.setText(file_path)

            if updated_files:
                QMessageBox.information(self, "نجح", f"تم تحديث مرفقات {title_map.get(document_type, 'المستند')} بنجاح")

    def add_attachment_with_manager(self, document_type):
        """إضافة مرفق باستخدام نافذة إدارة المرفقات (منفصل عن الروابط)"""
        from ..shipments.attachments_manager_dialog import AttachmentsManagerDialog

        title_map = {
            'contract': 'العقد مع المورد',
            'initial_designs': 'التصاميم الأولية',
            'final_design': 'التصميم النهائي المعتمد',
            'other_attachments': 'مرفقات أخرى'
        }

        # فتح نافذة إدارة المرفقات (بدون ملفات موجودة لأن المرفقات منفصلة عن الروابط)
        dialog = AttachmentsManagerDialog(self, title_map.get(document_type, 'مستندات'), [])
        if dialog.exec() == QDialog.Accepted:
            # الحصول على قائمة الملفات المحدثة
            updated_files = dialog.get_files_list()

            # تغيير اسم الزر إلى "مرفق" إذا تم إضافة ملفات
            if updated_files:
                self.update_attachment_button_text(document_type, "مرفق")
                QMessageBox.information(self, "نجح", f"تم إضافة {len(updated_files)} مرفق لـ {title_map.get(document_type, 'المستند')} بنجاح!\nالمرفقات محفوظة في نظام إدارة المرفقات منفصلة عن الروابط.")
            else:
                self.update_attachment_button_text(document_type, "إضافة مرفق")

    def update_attachment_button_text(self, document_type, text):
        """تحديث نص زر إضافة المرفق"""
        if document_type == 'contract':
            self.contract_add_attachment_btn.setText(text)
        elif document_type == 'initial_designs':
            self.initial_designs_add_attachment_btn.setText(text)
        elif document_type == 'final_design':
            self.final_design_add_attachment_btn.setText(text)
        elif document_type == 'other_attachments':
            self.other_attachments_add_attachment_btn.setText(text)



    def update_attachment_buttons_state(self):
        """تحديث حالة جميع أزرار إضافة المرفقات بناءً على وجود المرفقات"""
        # ملاحظة: الآن المرفقات منفصلة عن الروابط
        # أزرار إضافة المرفق تبقى كما هي لأن المرفقات تُدار منفصلة
        pass

    def get_document_url(self, document_type):
        """الحصول على رابط المستند من النص المعروض"""
        import re

        if document_type == 'contract':
            text = self.contract_url_display.text()
        elif document_type == 'initial_designs':
            text = self.initial_designs_url_display.text()
        elif document_type == 'final_design':
            text = self.final_design_url_display.text()
        elif document_type == 'other_attachments':
            text = self.other_attachments_url_display.text()
        else:
            return ""

        # استخراج الرابط من HTML إذا كان موجوداً
        if text != "لا يوجد رابط" and "href=" in text:
            match = re.search(r'href="([^"]*)"', text)
            if match:
                return match.group(1)
        return ""

    def update_document_display(self, document_type, url):
        """تحديث عرض المستند"""
        if url:
            # إنشاء رابط HTML
            html_link = f'<a href="{url}" style="color: #3498db; text-decoration: underline;">{url}</a>'
        else:
            html_link = "لا يوجد رابط"

        # تحديث الحقل المناسب
        if document_type == 'contract':
            self.contract_url_display.setText(html_link)
        elif document_type == 'initial_designs':
            self.initial_designs_url_display.setText(html_link)
        elif document_type == 'final_design':
            self.final_design_url_display.setText(html_link)
        elif document_type == 'other_attachments':
            self.other_attachments_url_display.setText(html_link)

    def open_url(self, url):
        """فتح الرابط أو الملف"""
        try:
            from PySide6.QtGui import QDesktopServices
            from PySide6.QtCore import QUrl
            import os

            if url.startswith(('http://', 'https://')):
                # رابط ويب
                QDesktopServices.openUrl(QUrl(url))
            elif os.path.exists(url):
                # ملف محلي
                QDesktopServices.openUrl(QUrl.fromLocalFile(url))
            else:
                # محاولة فتح كرابط ويب
                QDesktopServices.openUrl(QUrl(f"http://{url}"))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الرابط: {str(e)}")

    def update_order_status(self, new_status):
        """تحديث حالة الطلب التفاعلية"""
        try:
            # تحديث حقل الحالة في الواجهة
            if hasattr(self, 'order_status_combo'):
                status_map = {
                    'draft': 'مسودة',
                    'confirmed': 'مؤكدة',
                    'sent': 'مرسل',
                    'partial': 'جزئي',
                    'complete': 'كلي',
                    'cancelled': 'ملغي'
                }

                status_text = status_map.get(new_status, new_status)

                # البحث عن النص في القائمة المنسدلة وتحديده
                for i in range(self.order_status_combo.count()):
                    if self.order_status_combo.itemText(i) == status_text:
                        self.order_status_combo.setCurrentIndex(i)
                        break

                # تحديث لون الحالة
                self.update_status_color(new_status)

                print(f"تم تحديث حالة الطلب إلى: {status_text}")

        except Exception as e:
            print(f"خطأ في تحديث حالة الطلب: {str(e)}")

    def update_status_color(self, status):
        """تحديث لون حالة الطلب"""
        try:
            if hasattr(self, 'order_status_combo'):
                color_map = {
                    'draft': '#6c757d',      # رمادي
                    'confirmed': '#007bff',   # أزرق
                    'sent': '#ffc107',       # أصفر
                    'partial': '#fd7e14',    # برتقالي
                    'complete': '#28a745',   # أخضر
                    'cancelled': '#dc3545'   # أحمر
                }

                color = color_map.get(status, '#6c757d')
                self.order_status_combo.setStyleSheet(f"""
                    QComboBox {{
                        background-color: {color};
                        color: white;
                        font-weight: bold;
                        padding: 5px;
                        border-radius: 3px;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تحديث لون الحالة: {str(e)}")

    def check_order_completion_status(self):
        """فحص حالة اكتمال الطلب بناءً على استخدام الأصناف"""
        try:
            if not hasattr(self, 'current_order_id') or not self.current_order_id:
                return

            session = self.db_manager.get_session()

            # الحصول على أصناف الطلب
            order_items = session.query(PurchaseOrderItem).filter(
                PurchaseOrderItem.purchase_order_id == self.current_order_id
            ).all()

            if not order_items:
                return

            total_ordered = sum(item.quantity for item in order_items)
            total_delivered = sum(item.delivered_quantity or 0 for item in order_items)

            if total_delivered == 0:
                # لم يتم تسليم أي شيء
                new_status = 'confirmed'
            elif total_delivered >= total_ordered:
                # تم تسليم كل شيء
                new_status = 'complete'
            else:
                # تم تسليم جزء فقط
                new_status = 'partial'

            self.update_order_status(new_status)

        except Exception as e:
            print(f"خطأ في فحص حالة اكتمال الطلب: {str(e)}")
        finally:
            if 'session' in locals():
                session.close()

    def auto_update_order_status(self, order):
        """تحديث الحالة التلقائي بناءً على المستندات والأصناف"""
        try:
            # فحص وجود المستندات
            has_documents = any([
                order.contract_url and order.contract_url.strip(),
                order.initial_designs_url and order.initial_designs_url.strip(),
                order.final_design_url and order.final_design_url.strip(),
                order.other_attachments_url and order.other_attachments_url.strip()
            ])

            # إذا كانت هناك مستندات، تحديث الحالة إلى مؤكدة
            if has_documents and order.order_status in ['مسودة', 'draft']:
                order.order_status = 'مؤكدة'
                print("تم تحديث حالة الطلب إلى 'مؤكدة' بسبب وجود مستندات")

        except Exception as e:
            print(f"خطأ في التحديث التلقائي للحالة: {str(e)}")

    def mark_order_as_sent(self):
        """تحديد الطلب كمرسل للشحنات"""
        try:
            if hasattr(self, 'current_order_id') and self.current_order_id:
                session = self.db_manager.get_session()
                order = session.query(PurchaseOrder).filter(
                    PurchaseOrder.id == self.current_order_id
                ).first()

                if order:
                    order.order_status = 'مرسل'
                    session.commit()
                    self.update_order_status('sent')
                    print("تم تحديث حالة الطلب إلى 'مرسل'")

                session.close()

        except Exception as e:
            print(f"خطأ في تحديث حالة الطلب إلى مرسل: {str(e)}")
            if 'session' in locals():
                session.close()

    def update_item_delivery_status(self, item_id, delivered_quantity):
        """تحديث حالة تسليم الصنف"""
        try:
            session = self.db_manager.get_session()

            # تحديث الكمية المسلمة للصنف
            item = session.query(PurchaseOrderItem).filter(
                PurchaseOrderItem.id == item_id
            ).first()

            if item:
                item.delivered_quantity = delivered_quantity
                session.commit()

                # فحص حالة اكتمال الطلب
                self.check_order_completion_status()

            session.close()

        except Exception as e:
            print(f"خطأ في تحديث حالة تسليم الصنف: {str(e)}")
            if 'session' in locals():
                session.close()

    def load_data(self):
        """تحميل البيانات"""
        # تحميل البيانات للتصميم الجديد
        pass





    def load_order_details(self, order_id):
        """تحميل تفاصيل الطلب"""
        try:
            session = self.db_manager.get_session()
            order = session.query(PurchaseOrder).filter(PurchaseOrder.id == order_id).first()

            if order:
                self.current_order_id = order_id

                # البيانات الأساسية
                if hasattr(self, 'order_number_edit'):
                    self.order_number_edit.setText(order.order_number)

                if order.order_date and hasattr(self, 'order_date_edit'):
                    self.order_date_edit.setDate(QDate.fromString(order.order_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

                # المورد
                if order.supplier and hasattr(self, 'supplier_edit'):
                    self.supplier_edit.setText(order.supplier.name)
                    self.supplier_edit.setProperty("supplier_id", order.supplier_id)

                # الحالة
                if hasattr(self, 'order_status_combo'):
                    status_index = self.order_status_combo.findText(order.order_status)
                    if status_index >= 0:
                        self.order_status_combo.setCurrentIndex(status_index)

                # العملة
                if order.currency_id and hasattr(self, 'currency_combo'):
                    currency_index = self.currency_combo.findData(order.currency_id)
                    if currency_index >= 0:
                        self.currency_combo.setCurrentIndex(currency_index)

                # البيانات المالية
                if hasattr(self, 'exchange_rate_edit'):
                    self.exchange_rate_edit.setText(str(order.exchange_rate) if order.exchange_rate else "1.0000")
                if hasattr(self, 'discount_amount_edit'):
                    self.discount_amount_edit.setText(str(order.discount_amount) if order.discount_amount else "0.00")
                if hasattr(self, 'tax_amount_edit'):
                    self.tax_amount_edit.setText(str(order.tax_amount) if order.tax_amount else "0.00")
                if hasattr(self, 'total_amount_edit'):
                    self.total_amount_edit.setText(str(order.total_amount) if order.total_amount else "0.00")

                # التواريخ
                if order.expected_delivery_date:
                    self.expected_delivery_date_edit.setDate(
                        QDate.fromString(order.expected_delivery_date.strftime("%Y-%m-%d"), "yyyy-MM-dd")
                    )

                if order.actual_delivery_date:
                    self.actual_delivery_date_edit.setDate(
                        QDate.fromString(order.actual_delivery_date.strftime("%Y-%m-%d"), "yyyy-MM-dd")
                    )

                # الملاحظات
                self.notes_edit.setPlainText(order.notes or "")
                self.terms_conditions_edit.setPlainText(order.terms_conditions or "")

                # المستندات - تحديث حقول العرض
                self.update_document_display('contract', order.contract_url or "")
                self.update_document_display('initial_designs', order.initial_designs_url or "")
                self.update_document_display('final_design', order.final_design_url or "")
                self.update_document_display('other_attachments', order.other_attachments_url or "")

                # تحديث حالة أزرار إضافة المرفقات
                self.update_attachment_buttons_state()

                # تحميل الأصناف
                self.load_order_items(order_id)

                # تحديث لون الحالة بناءً على الحالة المحملة
                status_map = {
                    'مسودة': 'draft',
                    'مؤكدة': 'confirmed',
                    'مرسل': 'sent',
                    'جزئي': 'partial',
                    'كلي': 'complete',
                    'ملغي': 'cancelled'
                }
                status_key = status_map.get(order.order_status, 'draft')
                self.update_status_color(status_key)

            session.close()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل تفاصيل الطلب: {str(e)}")

    def load_order_items(self, order_id):
        """تحميل أصناف الطلب"""
        try:
            session = self.db_manager.get_session()
            items = session.query(PurchaseOrderItem).filter(
                PurchaseOrderItem.purchase_order_id == order_id
            ).all()

            self.items_table.setRowCount(len(items))

            for row, item in enumerate(items):
                # كود الصنف
                item_code = item.item.code if item.item else ""
                self.items_table.setItem(row, 0, QTableWidgetItem(item_code))

                # اسم الصنف
                item_name = item.item.name if item.item else ""
                self.items_table.setItem(row, 1, QTableWidgetItem(item_name))

                # الكمية
                self.items_table.setItem(row, 2, QTableWidgetItem(f"{item.quantity:.2f}"))

                # سعر الوحدة
                self.items_table.setItem(row, 3, QTableWidgetItem(f"{item.unit_price:.2f}"))

                # تاريخ الإنتاج
                production_date = ""
                if hasattr(item, 'production_date') and item.production_date:
                    production_date = item.production_date.strftime("%Y-%m-%d")
                self.items_table.setItem(row, 4, QTableWidgetItem(production_date))

                # تاريخ الانتهاء
                expiry_date = ""
                if hasattr(item, 'expiry_date') and item.expiry_date:
                    expiry_date = item.expiry_date.strftime("%Y-%m-%d")
                self.items_table.setItem(row, 5, QTableWidgetItem(expiry_date))

                # الخصم - العمود الجديد 6
                self.items_table.setItem(row, 6, QTableWidgetItem(f"{item.discount_amount:.2f}"))

                # الإجمالي - العمود الجديد 7
                self.items_table.setItem(row, 7, QTableWidgetItem(f"{item.total_price:.2f}"))

                # المسلم - العمود الجديد 8
                self.items_table.setItem(row, 8, QTableWidgetItem(f"{item.delivered_quantity:.2f}"))

                # المتبقي - العمود الجديد 9
                remaining = item.quantity - item.delivered_quantity
                self.items_table.setItem(row, 9, QTableWidgetItem(f"{remaining:.2f}"))

                # حفظ معرف الصنف الأصلي (من جدول الأصناف)
                if item.item:
                    self.items_table.item(row, 0).setData(Qt.UserRole, item.item.id)

                # حفظ معرف عنصر طلب الشراء للتعديل لاحقاً
                self.items_table.item(row, 0).setData(Qt.UserRole + 1, item.id)

            session.close()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل أصناف الطلب: {str(e)}")

    def new_order(self):
        """طلب جديد - فتح نافذة إدخال منفصلة"""
        try:
            # إنشاء نافذة إدخال جديدة
            new_order_window = PurchaseOrdersWindow(maximize_on_start=False, mode="entry")
            new_order_window.current_order_id = None
            new_order_window.setWindowTitle("طلب شراء جديد")

            # تنظيف النافذة الجديدة وإعدادها لطلب جديد
            if hasattr(new_order_window, 'clear_form'):
                new_order_window.clear_form()

            # توليد رقم طلب جديد
            if hasattr(new_order_window, 'generate_order_number'):
                new_order_number = new_order_window.generate_order_number()
                if hasattr(new_order_window, 'order_number_edit'):
                    new_order_window.order_number_edit.setText(new_order_number)

            new_order_window.show()
            self.status_bar.showMessage("تم فتح نافذة طلب جديد")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة طلب جديد: {str(e)}")

    def open_tracking_window(self):
        """فتح نافذة تتبع الطلبات المتقدمة"""
        try:
            from .purchase_orders_tracking_window import PurchaseOrdersTrackingWindow

            # إنشاء نافذة التتبع
            self.tracking_window = PurchaseOrdersTrackingWindow(self)
            self.tracking_window.show()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة التتبع: {str(e)}")

    def load_currencies(self):
        """تحميل العملات من قاعدة البيانات"""
        session = self.db_manager.get_session()
        try:
            from ...database.models import Currency
            currencies = session.query(Currency).filter(Currency.is_active == True).all()

            self.currency_combo.clear()

            if currencies:
                for currency in currencies:
                    display_text = f"{currency.name} ({currency.code})"
                    if currency.symbol:
                        display_text = f"{currency.symbol} - {display_text}"
                    self.currency_combo.addItem(display_text, currency.id)

                # تحديد العملة الافتراضية من إعدادات النظام
                default_currency = self.db_manager.get_setting("default_currency", "SAR")
                for i in range(self.currency_combo.count()):
                    if default_currency in self.currency_combo.itemText(i):
                        self.currency_combo.setCurrentIndex(i)
                        break
            else:
                # في حالة عدم وجود عملات، إضافة عملات افتراضية
                default_currencies = [
                    ("الريال السعودي", "SAR", "ر.س"),
                    ("الدولار الأمريكي", "USD", "$"),
                    ("اليورو", "EUR", "€"),
                    ("الدرهم الإماراتي", "AED", "د.إ"),
                    ("الدينار الكويتي", "KWD", "د.ك"),
                    ("الريال القطري", "QAR", "ر.ق"),
                    ("الدينار البحريني", "BHD", "د.ب")
                ]

                for name, code, symbol in default_currencies:
                    display_text = f"{symbol} - {name} ({code})"
                    self.currency_combo.addItem(display_text, code)

                # تحديد الريال السعودي كافتراضي
                for i in range(self.currency_combo.count()):
                    if "SAR" in self.currency_combo.itemText(i):
                        self.currency_combo.setCurrentIndex(i)
                        break

        except Exception as e:
            print(f"خطأ في تحميل العملات: {str(e)}")
            # في حالة الخطأ، إضافة عملات افتراضية
            self.currency_combo.clear()
            self.currency_combo.addItems(["ر.س - الريال السعودي (SAR)", "$ - الدولار الأمريكي (USD)", "€ - اليورو (EUR)"])
        finally:
            session.close()

    def clear_form(self):
        """مسح النموذج"""
        if hasattr(self, 'order_number_edit'):
            self.order_number_edit.clear()
        if hasattr(self, 'order_date_edit'):
            self.order_date_edit.setDate(QDate.currentDate())
        if hasattr(self, 'supplier_edit'):
            self.supplier_edit.clear()
            self.supplier_edit.setProperty("supplier_id", None)
        if hasattr(self, 'order_status_combo'):
            self.order_status_combo.setCurrentIndex(0)
        if hasattr(self, 'currency_combo'):
            self.currency_combo.setCurrentIndex(0)
        if hasattr(self, 'exchange_rate_edit'):
            self.exchange_rate_edit.setText("1.0000")
        if hasattr(self, 'discount_amount_edit'):
            self.discount_amount_edit.setText("0.00")
        if hasattr(self, 'tax_amount_edit'):
            self.tax_amount_edit.setText("0.00")
        if hasattr(self, 'total_amount_edit'):
            self.total_amount_edit.setText("0.00")
        if hasattr(self, 'expected_delivery_date_edit'):
            self.expected_delivery_date_edit.clear()
        if hasattr(self, 'actual_delivery_date_edit'):
            self.actual_delivery_date_edit.clear()
        if hasattr(self, 'notes_edit'):
            self.notes_edit.clear()
        if hasattr(self, 'terms_conditions_edit'):
            self.terms_conditions_edit.clear()

        # تنظيف حقول المستندات
        if hasattr(self, 'contract_url_edit'):
            self.contract_url_edit.clear()
        if hasattr(self, 'initial_designs_url_edit'):
            self.initial_designs_url_edit.clear()
        if hasattr(self, 'final_design_url_edit'):
            self.final_design_url_edit.clear()
        if hasattr(self, 'other_attachments_url_edit'):
            self.other_attachments_url_edit.clear()

        # إعادة تعيين نص أزرار إضافة المرفقات
        if hasattr(self, 'contract_add_attachment_btn'):
            self.contract_add_attachment_btn.setText("إضافة مرفق")
        if hasattr(self, 'initial_designs_add_attachment_btn'):
            self.initial_designs_add_attachment_btn.setText("إضافة مرفق")
        if hasattr(self, 'final_design_add_attachment_btn'):
            self.final_design_add_attachment_btn.setText("إضافة مرفق")
        if hasattr(self, 'other_attachments_add_attachment_btn'):
            self.other_attachments_add_attachment_btn.setText("إضافة مرفق")

        if hasattr(self, 'items_table'):
            self.items_table.setRowCount(0)

    def generate_order_number(self):
        """توليد رقم طلب جديد"""
        try:
            session = self.db_manager.get_session()

            # البحث عن آخر رقم طلب
            last_order = session.query(PurchaseOrder).order_by(PurchaseOrder.id.desc()).first()

            if last_order and last_order.order_number:
                # استخراج الرقم من آخر طلب
                try:
                    last_number = int(last_order.order_number.replace("PO", ""))
                    new_number = last_number + 1
                except:
                    new_number = 1
            else:
                new_number = 1

            session.close()
            return f"PO{new_number:06d}"

        except Exception as e:
            return f"PO{datetime.now().strftime('%Y%m%d%H%M%S')}"

    def load_order_by_number(self, order_number):
        """تحميل طلب بواسطة رقم الطلب"""
        try:
            session = self.db_manager.get_session()

            # البحث عن الطلب
            order = session.query(PurchaseOrder).filter(
                PurchaseOrder.order_number == order_number
            ).first()

            if order:
                # تحميل بيانات الطلب
                self.current_order_id = order.id
                self.order_number_edit.setText(order.order_number or "")

                # تحميل التاريخ
                if order.order_date:
                    from PySide6.QtCore import QDate
                    self.order_date_edit.setDate(QDate.fromString(str(order.order_date), "yyyy-MM-dd"))

                # تحميل المورد
                if order.supplier:
                    supplier_text = f"{order.supplier.name} ({order.supplier.code})"
                    self.supplier_edit.setText(supplier_text)
                    self.supplier_edit.setProperty("supplier_id", order.supplier_id)

                # تحميل العملة
                if order.currency_id and hasattr(self, 'currency_combo'):
                    for i in range(self.currency_combo.count()):
                        if self.currency_combo.itemData(i) == order.currency_id:
                            self.currency_combo.setCurrentIndex(i)
                            break

                # تحميل الحالة
                if order.order_status and hasattr(self, 'order_status_combo'):
                    status_index = self.order_status_combo.findText(order.order_status)
                    if status_index >= 0:
                        self.order_status_combo.setCurrentIndex(status_index)

                # تحميل البيانات المالية
                if hasattr(self, 'exchange_rate_edit'):
                    self.exchange_rate_edit.setText(str(order.exchange_rate or "1.0000"))
                if hasattr(self, 'discount_amount_edit'):
                    self.discount_amount_edit.setText(str(order.discount_amount or "0.00"))
                if hasattr(self, 'tax_amount_edit'):
                    self.tax_amount_edit.setText(str(order.tax_amount or "0.00"))
                if hasattr(self, 'total_amount_edit'):
                    self.total_amount_edit.setText(str(order.total_amount or "0.00"))

                # تحميل التواريخ
                if order.expected_delivery_date:
                    self.expected_delivery_date_edit.setDate(
                        QDate.fromString(str(order.expected_delivery_date), "yyyy-MM-dd")
                    )
                if order.actual_delivery_date:
                    self.actual_delivery_date_edit.setDate(
                        QDate.fromString(str(order.actual_delivery_date), "yyyy-MM-dd")
                    )

                # تحميل الملاحظات والشروط
                self.notes_edit.setPlainText(order.notes or "")
                self.terms_conditions_edit.setPlainText(order.terms_conditions or "")

                # تحميل المستندات
                self.contract_url_edit.setText(order.contract_url or "")
                self.initial_designs_url_edit.setText(order.initial_designs_url or "")
                self.final_design_url_edit.setText(order.final_design_url or "")
                self.other_attachments_url_edit.setText(order.other_attachments_url or "")

                # تحميل الأصناف
                self.load_order_items(order.id)

                # تم تحميل الطلب بنجاح - لا حاجة لرسالة منبثقة
            else:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "تحذير", f"لم يتم العثور على طلب برقم {order_number}")

            session.close()

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الطلب:\n{str(e)}")

    def search_supplier(self):
        """البحث عن مورد"""
        dialog = SupplierSearchDialog(self)
        if dialog.exec() == QDialog.Accepted:
            selected_supplier = dialog.get_selected_supplier()
            if selected_supplier:
                # selected_supplier هو كائن Supplier وليس قاموس
                supplier_text = f"{selected_supplier.name} ({selected_supplier.code})"
                self.supplier_edit.setText(supplier_text)
                self.supplier_edit.setProperty("supplier_id", selected_supplier.id)

                # رسالة تأكيد
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(
                    self,
                    "تم الاختيار",
                    f"✅ تم اختيار المورد: {selected_supplier.name}"
                )

    def check_purchase_order_used_in_shipments(self, purchase_order_id):
        """التحقق من استخدام طلب الشراء في الشحنات"""
        try:
            from ...database.models import ShipmentItem, PurchaseOrderItem

            session = self.db_manager.get_session()

            # البحث عن أصناف طلب الشراء المستخدمة في الشحنات
            used_items = session.query(ShipmentItem).join(PurchaseOrderItem).filter(
                PurchaseOrderItem.purchase_order_id == purchase_order_id,
                ShipmentItem.purchase_order_item_id == PurchaseOrderItem.id
            ).all()

            session.close()
            return len(used_items) > 0, used_items

        except Exception as e:
            print(f"❌ خطأ في التحقق من استخدام طلب الشراء: {str(e)}")
            return False, []

    def get_purchase_order_usage_status(self, purchase_order_id):
        """الحصول على حالة استخدام طلب الشراء"""
        try:
            from ...database.models import ShipmentItem, PurchaseOrderItem

            session = self.db_manager.get_session()

            # الحصول على جميع أصناف طلب الشراء
            order_items = session.query(PurchaseOrderItem).filter(
                PurchaseOrderItem.purchase_order_id == purchase_order_id
            ).all()

            if not order_items:
                session.close()
                return "empty"  # طلب فارغ

            total_ordered = sum(item.quantity for item in order_items)
            total_delivered = sum(item.delivered_quantity or 0 for item in order_items)

            session.close()

            if total_delivered == 0:
                return "unused"  # لم يستخدم
            elif total_delivered >= total_ordered:
                return "fully_used"  # مستخدم بالكامل
            else:
                return "partially_used"  # مستخدم جزئياً

        except Exception as e:
            print(f"❌ خطأ في الحصول على حالة استخدام الطلب: {str(e)}")
            return "unknown"

    def get_status_color(self, status):
        """الحصول على لون الحالة"""
        colors = {
            "unused": "#ffffff",        # أبيض - لم يستخدم
            "partially_used": "#fff3cd", # أصفر فاتح - مستخدم جزئياً
            "fully_used": "#d4edda",    # أخضر فاتح - مستخدم بالكامل
            "empty": "#f8d7da",         # أحمر فاتح - طلب فارغ
            "unknown": "#e2e3e5"        # رمادي - غير معروف
        }
        return colors.get(status, "#ffffff")

    def apply_row_colors(self):
        """تطبيق ألوان الصفوف حسب حالة الاستخدام - تم نقلها إلى نافذة منفصلة"""
        # هذه الدالة لم تعد مستخدمة بعد إعادة التنظيم
        # تم نقل وظيفة تلوين الصفوف إلى PurchaseOrdersListWindow
        pass

    def add_item(self):
        """إضافة صنف من خلال نافذة البحث المتقدمة"""
        try:
            # التحقق من استخدام طلب الشراء في الشحنات
            if hasattr(self, 'current_order_id') and self.current_order_id:
                is_used, used_items = self.check_purchase_order_used_in_shipments(self.current_order_id)
                if is_used:
                    QMessageBox.warning(
                        self,
                        "تحذير",
                        "⚠️ لا يمكن إضافة أصناف جديدة لهذا الطلب\n\n"
                        "السبب: تم استخدام هذا الطلب في الشحنات\n"
                        f"عدد الأصناف المستخدمة: {len(used_items)}\n\n"
                        "لإضافة أصناف جديدة، يجب إنشاء طلب شراء جديد."
                    )
                    return

            from ..shipments.advanced_item_search_dialog import AdvancedItemSearchDialog
            from ..shipments.item_price_dialog import ItemPriceDialog
            from PySide6.QtWidgets import QDialog

            # فتح نافذة البحث المتقدمة
            dialog = AdvancedItemSearchDialog(self)
            if dialog.exec() == QDialog.Accepted:
                selected_item = dialog.get_selected_item()
                if selected_item:
                    # فتح نافذة إدخال السعر والكمية
                    price_dialog = ItemPriceDialog(self, selected_item)
                    if price_dialog.exec() == QDialog.Accepted:
                        price_data = price_dialog.get_item_data()

                        # إضافة الصنف إلى الجدول
                        self.add_item_to_table(selected_item, price_data)

        except ImportError:
            # في حالة عدم وجود نافذة البحث المتقدمة، استخدم الطريقة القديمة
            self.add_item_fallback()
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إضافة الصنف:\n{str(e)}"
            )

    def add_item_to_table(self, selected_item, price_data):
        """إضافة الصنف إلى الجدول"""
        try:
            # إضافة صف جديد
            row = self.items_table.rowCount()
            self.items_table.insertRow(row)

            # استخدام البيانات المدخلة من النافذة
            quantity = price_data['quantity']
            unit_price = price_data['unit_price']
            total_price = price_data['total_price']
            production_date = price_data.get('production_date', '')
            expiry_date = price_data.get('expiry_date', '')
            notes = price_data.get('notes', '')

            # ملء البيانات
            self.items_table.setItem(row, 0, QTableWidgetItem(selected_item['code']))  # كود الصنف
            self.items_table.setItem(row, 1, QTableWidgetItem(selected_item['name']))  # اسم الصنف
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{quantity:.2f}"))  # الكمية
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{unit_price:.2f}"))  # سعر الوحدة

            # تاريخ الإنتاج
            self.items_table.setItem(row, 4, QTableWidgetItem(production_date))

            # تاريخ الانتهاء
            self.items_table.setItem(row, 5, QTableWidgetItem(expiry_date))

            # الخصم - افتراضي 0
            self.items_table.setItem(row, 6, QTableWidgetItem("0.00"))

            # الإجمالي
            self.items_table.setItem(row, 7, QTableWidgetItem(f"{total_price:.2f}"))

            # المسلم - افتراضي 0
            self.items_table.setItem(row, 8, QTableWidgetItem("0.00"))

            # المتبقي - يجب أن يكون نفس الكمية المدخلة
            self.items_table.setItem(row, 9, QTableWidgetItem(f"{quantity:.2f}"))

            # حفظ معرف الصنف
            self.items_table.item(row, 0).setData(Qt.UserRole, selected_item['id'])

            # ربط تغيير الكمية والسعر بإعادة الحساب
            self.items_table.itemChanged.connect(self.on_item_changed)

            # تحديث الإجمالي
            self.calculate_total()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الصنف: {str(e)}")

    def add_item_fallback(self):
        """إضافة صنف بالطريقة الاحتياطية (البحث البسيط)"""
        dialog = ItemSearchDialog(self)
        if dialog.exec() == QDialog.Accepted:
            selected_item = dialog.get_selected_item()
            if selected_item:
                self.add_existing_item(selected_item)

    def add_existing_item(self, selected_item):
        """إضافة صنف موجود من البحث"""
        # إضافة الصنف إلى الجدول
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)

        # كود الصنف
        self.items_table.setItem(row, 0, QTableWidgetItem(selected_item['code']))

        # اسم الصنف
        self.items_table.setItem(row, 1, QTableWidgetItem(selected_item['name']))

        # الكمية - افتراضي 1
        quantity_item = QTableWidgetItem("1.00")
        self.items_table.setItem(row, 2, quantity_item)

        # سعر الوحدة - من بيانات الصنف
        unit_price = selected_item.get('cost_price', 0.0)
        price_item = QTableWidgetItem(f"{unit_price:.2f}")
        self.items_table.setItem(row, 3, price_item)

        # تاريخ الإنتاج - فارغ افتراضياً
        production_date_item = QTableWidgetItem("")
        self.items_table.setItem(row, 4, production_date_item)

        # تاريخ الانتهاء - فارغ افتراضياً
        expiry_date_item = QTableWidgetItem("")
        self.items_table.setItem(row, 5, expiry_date_item)

        # الخصم - افتراضي 0
        discount_item = QTableWidgetItem("0.00")
        self.items_table.setItem(row, 6, discount_item)

        # الإجمالي
        total = 1.00 * unit_price
        total_item = QTableWidgetItem(f"{total:.2f}")
        self.items_table.setItem(row, 7, total_item)

        # المسلم - افتراضي 0
        delivered_item = QTableWidgetItem("0.00")
        self.items_table.setItem(row, 8, delivered_item)

        # المتبقي - يجب أن يكون نفس الكمية المدخلة
        remaining_item = QTableWidgetItem("1.00")
        self.items_table.setItem(row, 9, remaining_item)

        # حفظ معرف الصنف
        self.items_table.item(row, 0).setData(Qt.UserRole, selected_item['id'])

        # ربط تغيير الكمية والسعر بإعادة الحساب
        self.items_table.itemChanged.connect(self.on_item_changed)

        # تحديث الإجمالي
        self.calculate_total()

    def on_item_changed(self, item):
        """معالجة تغيير خلية في جدول الأصناف"""
        if not item:
            return

        row = item.row()
        col = item.column()

        # إذا تم تغيير الكمية (عمود 2) أو السعر (عمود 3) أو الخصم (عمود 6)
        if col in [2, 3, 6]:
            try:
                # الحصول على القيم
                quantity = float(self.items_table.item(row, 2).text()) if self.items_table.item(row, 2) else 0.0
                unit_price = float(self.items_table.item(row, 3).text()) if self.items_table.item(row, 3) else 0.0
                discount = float(self.items_table.item(row, 6).text()) if self.items_table.item(row, 6) else 0.0

                # حساب الإجمالي
                total = (quantity * unit_price) - discount

                # تحديث عمود الإجمالي
                if self.items_table.item(row, 7):
                    self.items_table.item(row, 7).setText(f"{total:.2f}")
                else:
                    self.items_table.setItem(row, 7, QTableWidgetItem(f"{total:.2f}"))

                # تحديث الكمية المتبقية
                delivered = float(self.items_table.item(row, 8).text()) if self.items_table.item(row, 8) else 0.0
                remaining = quantity - delivered

                if self.items_table.item(row, 9):
                    self.items_table.item(row, 9).setText(f"{remaining:.2f}")
                else:
                    self.items_table.setItem(row, 9, QTableWidgetItem(f"{remaining:.2f}"))

                # تحديث الإجمالي العام
                self.calculate_total()

            except ValueError:
                # في حالة إدخال قيمة غير صحيحة
                pass

        # إذا تم تغيير الكمية المسلمة (عمود 8)
        elif col == 8:
            try:
                quantity = float(self.items_table.item(row, 2).text()) if self.items_table.item(row, 2) else 0.0
                delivered = float(self.items_table.item(row, 8).text()) if self.items_table.item(row, 8) else 0.0
                remaining = quantity - delivered

                if self.items_table.item(row, 9):
                    self.items_table.item(row, 9).setText(f"{remaining:.2f}")
                else:
                    self.items_table.setItem(row, 9, QTableWidgetItem(f"{remaining:.2f}"))

            except ValueError:
                pass

    def edit_item(self):
        """تعديل صنف"""
        current_row = self.items_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صنف للتعديل")
            return

        # التحقق من استخدام طلب الشراء في الشحنات
        if hasattr(self, 'current_order_id') and self.current_order_id:
            is_used, used_items = self.check_purchase_order_used_in_shipments(self.current_order_id)
            if is_used:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "⚠️ لا يمكن تعديل أصناف هذا الطلب\n\n"
                    "السبب: تم استخدام هذا الطلب في الشحنات\n"
                    f"عدد الأصناف المستخدمة: {len(used_items)}\n\n"
                    "لتعديل الأصناف، يجب إنشاء طلب شراء جديد."
                )
                return

        # فتح نافذة تعديل الصنف
        dialog = ItemEditDialog(self, current_row, self.items_table)
        if dialog.exec() == QDialog.Accepted:
            self.calculate_total()

    def delete_item(self):
        """حذف صنف"""
        current_row = self.items_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صنف للحذف")
            return

        # التحقق من استخدام طلب الشراء في الشحنات
        if hasattr(self, 'current_order_id') and self.current_order_id:
            is_used, used_items = self.check_purchase_order_used_in_shipments(self.current_order_id)
            if is_used:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "⚠️ لا يمكن حذف أصناف من هذا الطلب\n\n"
                    "السبب: تم استخدام هذا الطلب في الشحنات\n"
                    f"عدد الأصناف المستخدمة: {len(used_items)}\n\n"
                    "لحذف الأصناف، يجب إنشاء طلب شراء جديد."
                )
                return

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا الصنف؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.items_table.removeRow(current_row)
            self.calculate_total()

    def calculate_total(self):
        """حساب الإجمالي"""
        total = 0.0

        for row in range(self.items_table.rowCount()):
            try:
                # الإجمالي في العمود 7 بعد إضافة أعمدة التواريخ
                item_total = float(self.items_table.item(row, 7).text())
                total += item_total
            except:
                pass

        # إضافة الضريبة وطرح الخصم
        try:
            discount = float(self.discount_amount_edit.text()) if self.discount_amount_edit.text() else 0.0
        except ValueError:
            discount = 0.0

        try:
            tax = float(self.tax_amount_edit.text()) if self.tax_amount_edit.text() else 0.0
        except ValueError:
            tax = 0.0

        final_total = total - discount + tax
        self.total_amount_edit.setText(f"{final_total:.2f}")

    def save_order(self):
        """حفظ الطلب"""
        try:
            # التحقق من البيانات المطلوبة
            if not self.order_number_edit.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الطلب")
                return

            supplier_id = self.supplier_edit.property("supplier_id")
            if not supplier_id:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار المورد")
                return

            session = self.db_manager.get_session()

            if self.current_order_id:
                # تحديث طلب موجود
                order = session.query(PurchaseOrder).filter(
                    PurchaseOrder.id == self.current_order_id
                ).first()
            else:
                # طلب جديد
                order = PurchaseOrder()

            # تحديث البيانات
            if hasattr(self, 'order_number_edit'):
                order.order_number = self.order_number_edit.text().strip()
            if hasattr(self, 'order_date_edit'):
                order.order_date = self.order_date_edit.date().toPython()
            order.supplier_id = supplier_id
            if hasattr(self, 'order_status_combo'):
                order.order_status = self.order_status_combo.currentText()
            if hasattr(self, 'currency_combo'):
                order.currency_id = self.currency_combo.currentData()
            # تحويل الحقول النصية إلى أرقام
            try:
                order.exchange_rate = float(self.exchange_rate_edit.text()) if self.exchange_rate_edit.text() else 1.0
            except ValueError:
                order.exchange_rate = 1.0

            try:
                order.discount_amount = float(self.discount_amount_edit.text()) if self.discount_amount_edit.text() else 0.0
            except ValueError:
                order.discount_amount = 0.0

            try:
                order.tax_amount = float(self.tax_amount_edit.text()) if self.tax_amount_edit.text() else 0.0
            except ValueError:
                order.tax_amount = 0.0

            try:
                order.total_amount = float(self.total_amount_edit.text()) if self.total_amount_edit.text() else 0.0
            except ValueError:
                order.total_amount = 0.0

            # التواريخ
            if self.expected_delivery_date_edit.date().isValid():
                order.expected_delivery_date = self.expected_delivery_date_edit.date().toPython()

            if self.actual_delivery_date_edit.date().isValid():
                order.actual_delivery_date = self.actual_delivery_date_edit.date().toPython()

            # الملاحظات
            order.notes = self.notes_edit.toPlainText()
            order.terms_conditions = self.terms_conditions_edit.toPlainText()

            # المستندات - استخراج الروابط من النص المعروض
            order.contract_url = self.get_document_url('contract')
            order.initial_designs_url = self.get_document_url('initial_designs')
            order.final_design_url = self.get_document_url('final_design')
            order.other_attachments_url = self.get_document_url('other_attachments')

            if not self.current_order_id:
                session.add(order)
                session.flush()  # للحصول على معرف الطلب
                self.current_order_id = order.id

            # حفظ الأصناف
            self.save_order_items(session, order.id)

            # تحديث الحالة التلقائي بناءً على المستندات
            self.auto_update_order_status(order)

            session.commit()
            session.close()

            QMessageBox.information(self, "نجح", "تم حفظ الطلب بنجاح")

            # تحديث الجدول فقط في وضع القائمة
            if self.mode == "list" and hasattr(self, 'load_orders'):
                self.load_orders()

        except Exception as e:
            if 'session' in locals():
                session.rollback()
                session.close()
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الطلب: {str(e)}")

    def save_order_items(self, session, order_id):
        """حفظ أصناف الطلب"""
        # حذف الأصناف الموجودة
        session.query(PurchaseOrderItem).filter(
            PurchaseOrderItem.purchase_order_id == order_id
        ).delete()

        # إضافة الأصناف الجديدة
        for row in range(self.items_table.rowCount()):
            try:
                item_id = self.items_table.item(row, 0).data(Qt.UserRole)
                quantity = float(self.items_table.item(row, 2).text())
                unit_price = float(self.items_table.item(row, 3).text())

                # تاريخ الإنتاج
                production_date_text = self.items_table.item(row, 4).text() if self.items_table.item(row, 4) else ""
                production_date = None
                if production_date_text:
                    try:
                        from datetime import datetime
                        production_date = datetime.strptime(production_date_text, "%Y-%m-%d").date()
                    except:
                        production_date = None

                # تاريخ الانتهاء
                expiry_date_text = self.items_table.item(row, 5).text() if self.items_table.item(row, 5) else ""
                expiry_date = None
                if expiry_date_text:
                    try:
                        from datetime import datetime
                        expiry_date = datetime.strptime(expiry_date_text, "%Y-%m-%d").date()
                    except:
                        expiry_date = None

                discount_amount = float(self.items_table.item(row, 6).text())
                total_price = float(self.items_table.item(row, 7).text())
                delivered_quantity = float(self.items_table.item(row, 8).text())

                order_item = PurchaseOrderItem(
                    purchase_order_id=order_id,
                    item_id=item_id,
                    quantity=quantity,
                    unit_price=unit_price,
                    discount_amount=discount_amount,
                    total_price=total_price,
                    delivered_quantity=delivered_quantity,
                    remaining_quantity=quantity - delivered_quantity,
                    production_date=production_date,
                    expiry_date=expiry_date
                )

                session.add(order_item)

            except Exception as e:
                print(f"خطأ في حفظ الصنف في الصف {row}: {e}")
                continue

    def delete_order(self):
        """حذف الطلب"""
        if not self.current_order_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلب للحذف")
            return

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا الطلب؟\nسيتم حذف جميع الأصناف المرتبطة به.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                session = self.db_manager.get_session()

                # الحصول على الطلب أولاً
                order = session.query(PurchaseOrder).filter(
                    PurchaseOrder.id == self.current_order_id
                ).first()

                if order:
                    # حذف أصناف الطلب أولاً باستخدام synchronize_session=False
                    session.query(PurchaseOrderItem).filter(
                        PurchaseOrderItem.purchase_order_id == self.current_order_id
                    ).delete(synchronize_session=False)

                    # حذف الطلب
                    session.delete(order)

                    session.commit()
                    QMessageBox.information(self, "نجح", "تم حذف الطلب بنجاح")
                    self.clear_form()
                    self.current_order_id = None
                else:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على الطلب")

                session.close()

            except Exception as e:
                if 'session' in locals():
                    session.rollback()
                    session.close()
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف الطلب: {str(e)}")

    def send_to_shipment(self):
        """إرسال الطلب إلى الشحنات"""
        if not self.current_order_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلب لإرساله للشحنات")
            return

        try:
            session = self.db_manager.get_session()
            order = session.query(PurchaseOrder).filter(
                PurchaseOrder.id == self.current_order_id
            ).first()

            if not order:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على الطلب")
                return

            # التحقق من وجود أصناف
            items_count = session.query(PurchaseOrderItem).filter(
                PurchaseOrderItem.purchase_order_id == self.current_order_id
            ).count()

            if items_count == 0:
                QMessageBox.warning(self, "تحذير", "لا يمكن إرسال طلب فارغ للشحنات")
                return

            session.close()

            # فتح نافذة الشحنة الجديدة مع بيانات الطلب
            from ..shipments.new_shipment_window import NewShipmentWindow

            shipment_window = NewShipmentWindow()
            shipment_window.load_from_purchase_order(self.current_order_id)
            shipment_window.show()

            QMessageBox.information(self, "نجح", "تم فتح نافذة شحنة جديدة مع بيانات الطلب")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إرسال الطلب للشحنات: {str(e)}")

    def print_order(self):
        """طباعة الطلب"""
        if not self.current_order_id:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد طلب للطباعة")
            return

        QMessageBox.information(self, "طباعة", "سيتم تطوير وظيفة الطباعة قريباً")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "تصدير", "سيتم تطوير وظيفة التصدير قريباً")

    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(self, "حول البرنامج",
                         "نظام إدارة طلبات الشراء\n"
                         "الإصدار 2.0\n"
                         "ProShipment System\n\n"
                         "تم تطوير هذا النظام لإدارة طلبات الشراء من الموردين\n"
                         "مع دعم كامل لوضع ملء الشاشة والواجهة الاحترافية")

    def refresh_orders(self):
        """تحديث قائمة الطلبات"""
        try:
            self.load_orders()
            self.statusBar().showMessage("تم تحديث قائمة الطلبات بنجاح", 2000)
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في تحديث الطلبات:\n{str(e)}")


class ItemEditDialog(QDialog):
    """نافذة تعديل صنف في الطلب"""

    def __init__(self, parent, row, table):
        super().__init__(parent)
        self.row = row
        self.table = table
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تعديل صنف")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout(self)

        # نموذج البيانات
        form_layout = QFormLayout()

        # كود الصنف (للقراءة فقط)
        self.item_code_edit = QLineEdit()
        self.item_code_edit.setReadOnly(True)
        form_layout.addRow("كود الصنف:", self.item_code_edit)

        # اسم الصنف (للقراءة فقط)
        self.item_name_edit = QLineEdit()
        self.item_name_edit.setReadOnly(True)
        form_layout.addRow("اسم الصنف:", self.item_name_edit)

        # الكمية
        self.quantity_edit = QLineEdit()
        self.quantity_edit.setPlaceholderText("أدخل الكمية")
        self.quantity_edit.textChanged.connect(self.calculate_total)
        form_layout.addRow("الكمية:", self.quantity_edit)

        # سعر الوحدة
        self.unit_price_edit = QLineEdit()
        self.unit_price_edit.setPlaceholderText("أدخل سعر الوحدة")
        self.unit_price_edit.textChanged.connect(self.calculate_total)
        form_layout.addRow("سعر الوحدة:", self.unit_price_edit)

        # تاريخ الإنتاج
        self.production_date_edit = FlexibleDateEdit()
        self.production_date_edit.setDate(QDate.currentDate())
        form_layout.addRow("تاريخ الإنتاج:", self.production_date_edit)

        # تاريخ الانتهاء
        self.expiry_date_edit = FlexibleDateEdit()
        self.expiry_date_edit.setDate(QDate.currentDate().addDays(365))  # سنة من الآن افتراضياً
        form_layout.addRow("تاريخ الانتهاء:", self.expiry_date_edit)

        # مبلغ الخصم
        self.discount_edit = QLineEdit()
        self.discount_edit.setPlaceholderText("أدخل مبلغ الخصم")
        self.discount_edit.textChanged.connect(self.calculate_total)
        form_layout.addRow("مبلغ الخصم:", self.discount_edit)

        # الإجمالي (للقراءة فقط)
        self.total_edit = QLineEdit()
        self.total_edit.setReadOnly(True)
        self.total_edit.setStyleSheet("background-color: #f0f0f0;")
        form_layout.addRow("الإجمالي:", self.total_edit)

        # الكمية المسلمة
        self.delivered_edit = QLineEdit()
        self.delivered_edit.setPlaceholderText("أدخل الكمية المسلمة")
        self.delivered_edit.textChanged.connect(self.calculate_remaining)
        form_layout.addRow("الكمية المسلمة:", self.delivered_edit)

        # الكمية المتبقية (للقراءة فقط)
        self.remaining_edit = QLineEdit()
        self.remaining_edit.setReadOnly(True)
        self.remaining_edit.setStyleSheet("background-color: #f0f0f0;")
        form_layout.addRow("الكمية المتبقية:", self.remaining_edit)

        layout.addLayout(form_layout)

        # أزرار الحوار
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def load_data(self):
        """تحميل البيانات من الجدول"""
        self.item_code_edit.setText(self.table.item(self.row, 0).text())
        self.item_name_edit.setText(self.table.item(self.row, 1).text())
        self.quantity_edit.setText(self.table.item(self.row, 2).text())
        self.unit_price_edit.setText(self.table.item(self.row, 3).text())

        # تحميل تاريخ الإنتاج
        production_date_text = self.table.item(self.row, 4).text() if self.table.item(self.row, 4) else ""
        if production_date_text:
            try:
                production_date = QDate.fromString(production_date_text, "yyyy-MM-dd")
                if production_date.isValid():
                    self.production_date_edit.setDate(production_date)
            except:
                pass

        # تحميل تاريخ الانتهاء
        expiry_date_text = self.table.item(self.row, 5).text() if self.table.item(self.row, 5) else ""
        if expiry_date_text:
            try:
                expiry_date = QDate.fromString(expiry_date_text, "yyyy-MM-dd")
                if expiry_date.isValid():
                    self.expiry_date_edit.setDate(expiry_date)
            except:
                pass

        self.discount_edit.setText(self.table.item(self.row, 6).text())
        self.delivered_edit.setText(self.table.item(self.row, 8).text())

        # حساب الإجمالي والمتبقي بعد تحميل البيانات
        self.calculate_total()
        self.calculate_remaining()

    def calculate_total(self):
        """حساب الإجمالي"""
        try:
            # الحصول على القيم مع التحقق من صحتها
            quantity_text = self.quantity_edit.text().strip()
            unit_price_text = self.unit_price_edit.text().strip()
            discount_text = self.discount_edit.text().strip()

            # تحويل النصوص إلى أرقام
            quantity = float(quantity_text.replace(',', '.')) if quantity_text else 0.0
            unit_price = float(unit_price_text.replace(',', '.')) if unit_price_text else 0.0
            discount = float(discount_text.replace(',', '.')) if discount_text else 0.0

            # حساب الإجمالي
            total = (quantity * unit_price) - discount

            # التأكد من أن الإجمالي لا يقل عن صفر
            total = max(0, total)

            # تحديث حقل الإجمالي
            self.total_edit.setText(f"{total:.2f}")

        except (ValueError, AttributeError) as e:
            # في حالة إدخال قيم غير صحيحة
            self.total_edit.setText("0.00")
        except Exception as e:
            print(f"خطأ في حساب الإجمالي: {str(e)}")
            self.total_edit.setText("0.00")

    def calculate_remaining(self):
        """حساب الكمية المتبقية"""
        try:
            quantity = float(self.quantity_edit.text() or "0")
            delivered = float(self.delivered_edit.text() or "0")

            remaining = quantity - delivered
            self.remaining_edit.setText(f"{remaining:.2f}")
        except ValueError:
            # في حالة إدخال قيم غير صحيحة
            self.remaining_edit.setText("0.00")

    def accept(self):
        """قبول التعديلات"""
        # تحديث الجدول
        self.table.item(self.row, 2).setText(self.quantity_edit.text())
        self.table.item(self.row, 3).setText(self.unit_price_edit.text())

        # تحديث تاريخ الإنتاج
        production_date = self.production_date_edit.date().toString("yyyy-MM-dd")
        if self.table.item(self.row, 4):
            self.table.item(self.row, 4).setText(production_date)
        else:
            self.table.setItem(self.row, 4, QTableWidgetItem(production_date))

        # تحديث تاريخ الانتهاء
        expiry_date = self.expiry_date_edit.date().toString("yyyy-MM-dd")
        if self.table.item(self.row, 5):
            self.table.item(self.row, 5).setText(expiry_date)
        else:
            self.table.setItem(self.row, 5, QTableWidgetItem(expiry_date))

        self.table.item(self.row, 6).setText(self.discount_edit.text())
        self.table.item(self.row, 7).setText(self.total_edit.text())
        self.table.item(self.row, 8).setText(self.delivered_edit.text())
        self.table.item(self.row, 9).setText(self.remaining_edit.text())

        super().accept()

class ItemAddDialog(QDialog):
    """نافذة إضافة صنف جديد في الطلب"""

    def __init__(self, parent, table):
        super().__init__(parent)
        self.table = table
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة صنف جديد")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout(self)

        # نموذج البيانات
        form_layout = QFormLayout()

        # كود الصنف
        self.item_code_edit = QLineEdit()
        self.item_code_edit.setPlaceholderText("أدخل كود الصنف")
        form_layout.addRow("كود الصنف:", self.item_code_edit)

        # اسم الصنف
        self.item_name_edit = QLineEdit()
        self.item_name_edit.setPlaceholderText("أدخل اسم الصنف")
        form_layout.addRow("اسم الصنف:", self.item_name_edit)

        # الكمية
        self.quantity_edit = QLineEdit()
        self.quantity_edit.setPlaceholderText("أدخل الكمية")
        self.quantity_edit.textChanged.connect(self.calculate_total)
        form_layout.addRow("الكمية:", self.quantity_edit)

        # سعر الوحدة
        self.unit_price_edit = QLineEdit()
        self.unit_price_edit.setPlaceholderText("أدخل سعر الوحدة")
        self.unit_price_edit.textChanged.connect(self.calculate_total)
        form_layout.addRow("سعر الوحدة:", self.unit_price_edit)

        # تاريخ الإنتاج
        self.production_date_edit = FlexibleDateEdit()
        self.production_date_edit.setDate(QDate.currentDate())
        form_layout.addRow("تاريخ الإنتاج:", self.production_date_edit)

        # تاريخ الانتهاء
        self.expiry_date_edit = FlexibleDateEdit()
        self.expiry_date_edit.setDate(QDate.currentDate().addDays(365))  # سنة من الآن افتراضياً
        form_layout.addRow("تاريخ الانتهاء:", self.expiry_date_edit)

        # مبلغ الخصم
        self.discount_edit = QLineEdit()
        self.discount_edit.setPlaceholderText("أدخل مبلغ الخصم")
        self.discount_edit.textChanged.connect(self.calculate_total)
        form_layout.addRow("مبلغ الخصم:", self.discount_edit)

        # الإجمالي (للقراءة فقط)
        self.total_edit = QLineEdit()
        self.total_edit.setReadOnly(True)
        self.total_edit.setStyleSheet("background-color: #f0f0f0;")
        form_layout.addRow("الإجمالي:", self.total_edit)

        # الكمية المسلمة
        self.delivered_edit = QLineEdit()
        self.delivered_edit.setPlaceholderText("أدخل الكمية المسلمة")
        self.delivered_edit.textChanged.connect(self.calculate_remaining)
        form_layout.addRow("الكمية المسلمة:", self.delivered_edit)

        # الكمية المتبقية (للقراءة فقط)
        self.remaining_edit = QLineEdit()
        self.remaining_edit.setReadOnly(True)
        self.remaining_edit.setStyleSheet("background-color: #f0f0f0;")
        form_layout.addRow("الكمية المتبقية:", self.remaining_edit)

        layout.addLayout(form_layout)

        # أزرار الحوار
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def calculate_total(self):
        """حساب الإجمالي"""
        try:
            quantity = float(self.quantity_edit.text() or "0")
            unit_price = float(self.unit_price_edit.text() or "0")
            discount = float(self.discount_edit.text() or "0")

            total = (quantity * unit_price) - discount
            self.total_edit.setText(f"{total:.2f}")

            # تحديث الكمية المتبقية
            self.calculate_remaining()
        except ValueError:
            # في حالة إدخال قيم غير صحيحة
            self.total_edit.setText("0.00")

    def calculate_remaining(self):
        """حساب الكمية المتبقية"""
        try:
            quantity = float(self.quantity_edit.text() or "0")
            delivered = float(self.delivered_edit.text() or "0")

            remaining = quantity - delivered
            self.remaining_edit.setText(f"{remaining:.2f}")
        except ValueError:
            # في حالة إدخال قيم غير صحيحة
            self.remaining_edit.setText("0.00")

    def accept(self):
        """قبول الإضافة"""
        # التحقق من صحة البيانات
        if not self.item_code_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كود الصنف")
            return

        if not self.item_name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الصنف")
            return

        # إضافة صف جديد للجدول
        row_count = self.table.rowCount()
        self.table.insertRow(row_count)

        # ملء البيانات
        self.table.setItem(row_count, 0, QTableWidgetItem(self.item_code_edit.text()))
        self.table.setItem(row_count, 1, QTableWidgetItem(self.item_name_edit.text()))
        self.table.setItem(row_count, 2, QTableWidgetItem(self.quantity_edit.text() or "0"))
        self.table.setItem(row_count, 3, QTableWidgetItem(self.unit_price_edit.text() or "0"))

        # تاريخ الإنتاج
        production_date = self.production_date_edit.date().toString("yyyy-MM-dd")
        self.table.setItem(row_count, 4, QTableWidgetItem(production_date))

        # تاريخ الانتهاء
        expiry_date = self.expiry_date_edit.date().toString("yyyy-MM-dd")
        self.table.setItem(row_count, 5, QTableWidgetItem(expiry_date))

        # مسح الحقول
        self.clear_item_form()

    def refresh_orders(self):
        """تحديث قائمة الطلبات"""
        if self.mode == "list":
            self.load_orders()