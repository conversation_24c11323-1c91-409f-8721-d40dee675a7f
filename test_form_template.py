#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لقالب النموذج
Quick Test for Form Template
"""

import sys
import os
from datetime import datetime

def print_banner():
    """طباعة شعار الاختبار"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║               🧪 اختبار قالب النموذج 🧪                      ║
    ║                                                              ║
    ║              SHIPMENT ERP Form Template Test                 ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    # فحص Python
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python {python_version.major}.{python_version.minor}.{python_version.micro} - يتطلب 3.8+")
        return False
    
    # فحص PySide6
    try:
        import PySide6
        print(f"✅ PySide6 {PySide6.__version__}")
    except ImportError:
        print("❌ PySide6 غير مثبت")
        return False
    
    # فحص الملفات المطلوبة
    required_files = [
        "form_template.py",
        "run_form_template.py"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    return True

def test_imports():
    """اختبار استيراد المكونات"""
    print("\n🧪 اختبار استيراد المكونات...")
    
    try:
        from form_template import FormTemplate
        print("✅ form_template")
    except ImportError as e:
        print(f"❌ form_template - {e}")
        return False
    
    try:
        from run_form_template import FormTemplateApp
        print("✅ run_form_template")
    except ImportError as e:
        print(f"❌ run_form_template - {e}")
        return False
    
    return True

def test_form_creation():
    """اختبار إنشاء النموذج"""
    print("\n🏗️ اختبار إنشاء النموذج...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from form_template import FormTemplate
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء النموذج
        form = FormTemplate()
        print("✅ تم إنشاء النموذج بنجاح")
        
        # اختبار المتغيرات
        variables = form.get_form_variables()
        print(f"✅ تم الحصول على {len(variables)} متغير")
        
        # اختبار جمع البيانات
        data = form.collect_form_data()
        print("✅ تم جمع البيانات بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النموذج: {e}")
        return False

def show_template_info():
    """عرض معلومات القالب"""
    print("\n📋 معلومات القالب:")
    print("   🎯 مطابق للتصميم المرفق في الصورة")
    print("   📝 يحتوي على جميع حقول البيانات")
    print("   ⚙️ لوحة خيارات وإعدادات كاملة")
    print("   🔧 شريط أدوات متكامل")
    print("   📊 جدول معلومات سفلي")
    print("   📱 شريط حالة تفاعلي")
    print("   🎨 أنماط وألوان احترافية")
    print("   ✅ وظائف التحقق والتصديق")

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n🚀 تعليمات الاستخدام:")
    print("   📌 تشغيل القالب:")
    print("      python run_form_template.py")
    print("")
    print("   📌 استخدام في التطبيق:")
    print("      from form_template import FormTemplate")
    print("      form = FormTemplate()")
    print("      variables = form.get_form_variables()")
    print("")
    print("   📌 جمع البيانات:")
    print("      data = form.collect_form_data()")

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 65)
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات!")
        input("\nاضغط Enter للخروج...")
        return
    
    print("\n✅ جميع المتطلبات متوفرة")
    
    # اختبار الاستيراد
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيراد!")
        input("\nاضغط Enter للخروج...")
        return
    
    # اختبار إنشاء النموذج
    if not test_form_creation():
        print("\n❌ فشل في اختبار إنشاء النموذج!")
        input("\nاضغط Enter للخروج...")
        return
    
    print("\n🎉 جميع الاختبارات نجحت!")
    
    # عرض المعلومات
    show_template_info()
    show_usage_instructions()
    
    print("\n" + "=" * 65)
    print("✅ الاختبار مكتمل بنجاح!")
    print("🎯 القالب جاهز للاستخدام")
    print("=" * 65)

if __name__ == "__main__":
    main()
