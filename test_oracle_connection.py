#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصال بقاعدة بيانات Oracle
Oracle Database Connection Test
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.universal_database_manager import UniversalDatabaseManager
from src.database.oracle_config import DatabaseConfigManager

def test_oracle_connection():
    """اختبار الاتصال بقاعدة بيانات Oracle"""
    
    print("🔍 اختبار الاتصال بقاعدة بيانات Oracle...")
    print("=" * 50)
    
    try:
        # تحميل التكوين
        print("📋 تحميل إعدادات قاعدة البيانات...")
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        
        print(f"   نوع قاعدة البيانات: {config.type.value}")
        print(f"   الخادم: {config.oracle_config.host}:{config.oracle_config.port}")
        print(f"   اسم الخدمة: {config.oracle_config.service_name}")
        print(f"   اسم المستخدم: {config.oracle_config.username}")
        print()
        
        # إنشاء مدير قاعدة البيانات
        print("🔗 إنشاء اتصال قاعدة البيانات...")
        db_manager = UniversalDatabaseManager(config)
        
        # اختبار الاتصال
        print("🧪 اختبار الاتصال...")
        if db_manager.test_connection():
            print("   ✅ نجح الاتصال بقاعدة البيانات!")
            
            # اختبار تهيئة قاعدة البيانات
            print("🏗️ اختبار تهيئة قاعدة البيانات...")
            if db_manager.initialize_database():
                print("   ✅ تم تهيئة قاعدة البيانات بنجاح!")
                
                # اختبار إنشاء جلسة
                print("📝 اختبار إنشاء جلسة...")
                with db_manager.get_session() as session:
                    # تنفيذ استعلام بسيط
                    result = session.execute("SELECT 1 FROM DUAL")
                    value = result.scalar()
                    if value == 1:
                        print("   ✅ تم إنشاء الجلسة وتنفيذ الاستعلام بنجاح!")
                    else:
                        print("   ❌ خطأ في تنفيذ الاستعلام")
                        
            else:
                print("   ❌ فشل في تهيئة قاعدة البيانات")
                
        else:
            print("   ❌ فشل في الاتصال بقاعدة البيانات")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("=" * 50)
    print("✅ انتهى اختبار الاتصال بنجاح!")
    return True

def show_connection_info():
    """عرض معلومات الاتصال"""
    print("📊 معلومات الاتصال:")
    print("-" * 30)
    
    try:
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        
        if config.type.value == "oracle":
            oracle_config = config.oracle_config
            print(f"نوع قاعدة البيانات: Oracle")
            print(f"الخادم: {oracle_config.host}")
            print(f"المنفذ: {oracle_config.port}")
            print(f"اسم الخدمة: {oracle_config.service_name}")
            print(f"اسم المستخدم: {oracle_config.username}")
            print(f"نوع الاتصال: {oracle_config.connection_type}")
            print(f"حجم المجموعة: {oracle_config.pool_size}")
            print(f"الحد الأقصى للفائض: {oracle_config.max_overflow}")
            print(f"مهلة الاتصال: {oracle_config.pool_timeout} ثانية")
            print(f"SSL: {'مفعل' if oracle_config.use_ssl else 'معطل'}")
        else:
            print(f"نوع قاعدة البيانات: {config.type.value}")
            
    except Exception as e:
        print(f"خطأ في قراءة التكوين: {e}")

if __name__ == "__main__":
    print("🚀 اختبار اتصال Oracle Database")
    print("=" * 50)
    
    # عرض معلومات الاتصال
    show_connection_info()
    print()
    
    # تشغيل الاختبار
    success = test_oracle_connection()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت! التطبيق جاهز للعمل مع Oracle.")
    else:
        print("\n⚠️ هناك مشاكل في الاتصال. يرجى مراجعة الإعدادات.")
        sys.exit(1)
