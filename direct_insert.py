#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدراج مباشر للبيانات في Oracle
Direct Insert Data into Oracle
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def direct_insert():
    try:
        print("Starting direct insert...")
        
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        
        print("Loading config...")
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        
        print(f"Database type: {config.type.value}")
        
        db_manager = UniversalDatabaseManager(config)
        
        print("Testing connection...")
        if not db_manager.test_connection():
            print("Connection failed!")
            return False
        
        print("Connection successful!")
        
        # إدراج وحدات القياس مباشرة
        units_sql = """
        INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
        SELECT 'قطعة', 'Piece', 'قطعة', 'pcs', 'وحدة العد الأساسية', 1, SYSDATE, SYSDATE FROM dual
        WHERE NOT EXISTS (SELECT 1 FROM units_of_measure WHERE name = 'قطعة')
        """
        
        groups_sql = """
        INSERT INTO item_groups (name, name_en, description, is_active, created_at, updated_at)
        SELECT 'إلكترونيات', 'Electronics', 'الأجهزة والمعدات الإلكترونية', 1, SYSDATE, SYSDATE FROM dual
        WHERE NOT EXISTS (SELECT 1 FROM item_groups WHERE name = 'إلكترونيات')
        """
        
        with db_manager.get_session() as session:
            print("Inserting sample unit...")
            session.execute(units_sql)
            
            print("Inserting sample group...")
            session.execute(groups_sql)
            
            print("Checking results...")
            units_count = session.execute("SELECT COUNT(*) FROM units_of_measure").scalar()
            groups_count = session.execute("SELECT COUNT(*) FROM item_groups").scalar()
            
            print(f"Units count: {units_count}")
            print(f"Groups count: {groups_count}")
            
        print("Direct insert completed successfully!")
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = direct_insert()
    print(f"Result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
