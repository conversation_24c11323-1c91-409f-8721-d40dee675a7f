# -*- coding: utf-8 -*-
"""
نافذة تأكيد حذف الفرع
Delete Branch Confirmation Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QFrame, QMessageBox, QTextEdit)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QPixmap

import sqlite3
from pathlib import Path
from datetime import datetime

class DeleteBranchDialog(QDialog):
    """نافذة تأكيد حذف الفرع"""
    
    branch_deleted = Signal(int)  # إشارة عند حذف الفرع
    
    def __init__(self, branch_id, branch_name, parent=None):
        super().__init__(parent)
        self.branch_id = branch_id
        self.branch_name = branch_name
        self.setWindowTitle("تأكيد حذف الفرع - ProShipment")
        self.setMinimumSize(500, 400)
        self.resize(600, 450)
        self.setModal(True)

        # توسيط النافذة
        self.center_window()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.check_dependencies()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # أيقونة التحذير والعنوان
        header_frame = QFrame()
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة التحذير
        warning_label = QLabel("⚠️")
        warning_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: #e74c3c;
                margin-right: 20px;
            }
        """)
        header_layout.addWidget(warning_label)
        
        # نص التحذير
        warning_text = QLabel(f"هل أنت متأكد من حذف الفرع؟\n\n'{self.branch_name}'")
        warning_text.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                line-height: 1.5;
            }
        """)
        warning_text.setWordWrap(True)
        header_layout.addWidget(warning_text)
        header_layout.addStretch()
        
        layout.addWidget(header_frame)
        
        # معلومات التبعيات
        self.dependencies_frame = QFrame()
        dependencies_layout = QVBoxLayout(self.dependencies_frame)
        
        dependencies_title = QLabel("التحقق من البيانات المرتبطة:")
        dependencies_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #34495e;
                margin-bottom: 10px;
            }
        """)
        dependencies_layout.addWidget(dependencies_title)
        
        self.dependencies_text = QTextEdit()
        self.dependencies_text.setMaximumHeight(150)
        self.dependencies_text.setMinimumHeight(150)
        self.dependencies_text.setReadOnly(True)
        self.dependencies_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
                background-color: #f8f9fa;
                font-family: 'Segoe UI';
                font-size: 12px;
            }
        """)
        dependencies_layout.addWidget(self.dependencies_text)
        
        layout.addWidget(self.dependencies_frame)
        
        # رسالة تحذير إضافية
        warning_note = QLabel("⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!")
        warning_note.setAlignment(Qt.AlignCenter)
        warning_note.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 5px;
                padding: 10px;
                color: #856404;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        layout.addWidget(warning_note)
        
        # أزرار الإجراءات
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.addStretch()
        
        self.delete_btn = QPushButton("🗑️ نعم، احذف الفرع")
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        buttons_layout.addWidget(self.delete_btn)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.delete_btn.clicked.connect(self.delete_branch)
        self.cancel_btn.clicked.connect(self.reject)
    
    def check_dependencies(self):
        """التحقق من البيانات المرتبطة بالفرع"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            dependencies_info = []
            can_delete = True
            
            # التحقق من المعاملات المالية المرتبطة
            try:
                cursor.execute("SELECT COUNT(*) FROM transactions WHERE branch_id = ?", (self.branch_id,))
                transactions_count = cursor.fetchone()[0]
                
                if transactions_count > 0:
                    dependencies_info.append(f"💳 المعاملات المالية: {transactions_count} معاملة")
                    can_delete = False
                else:
                    dependencies_info.append("💳 المعاملات المالية: لا توجد معاملات مرتبطة")
            except sqlite3.OperationalError:
                # جدول المعاملات غير موجود
                dependencies_info.append("💳 المعاملات المالية: لا توجد معاملات مرتبطة")
            
            # التحقق من الحوالات المرتبطة
            try:
                cursor.execute("SELECT COUNT(*) FROM remittances WHERE branch_id = ?", (self.branch_id,))
                remittances_count = cursor.fetchone()[0]
                
                if remittances_count > 0:
                    dependencies_info.append(f"💸 الحوالات المرتبطة: {remittances_count} حوالة")
                    can_delete = False
                else:
                    dependencies_info.append("💸 الحوالات المرتبطة: لا توجد حوالات مرتبطة")
            except sqlite3.OperationalError:
                # جدول الحوالات غير موجود
                dependencies_info.append("💸 الحوالات المرتبطة: لا توجد حوالات مرتبطة")
            
            # التحقق من الحسابات البنكية المرتبطة
            try:
                cursor.execute("SELECT COUNT(*) FROM bank_accounts WHERE branch_id = ?", (self.branch_id,))
                accounts_count = cursor.fetchone()[0]
                
                if accounts_count > 0:
                    dependencies_info.append(f"🏦 الحسابات البنكية: {accounts_count} حساب")
                    can_delete = False
                else:
                    dependencies_info.append("🏦 الحسابات البنكية: لا توجد حسابات مرتبطة")
            except sqlite3.OperationalError:
                # جدول الحسابات البنكية غير موجود
                dependencies_info.append("🏦 الحسابات البنكية: لا توجد حسابات مرتبطة")
            
            # التحقق من الموظفين المرتبطين
            try:
                cursor.execute("SELECT COUNT(*) FROM employees WHERE branch_id = ?", (self.branch_id,))
                employees_count = cursor.fetchone()[0]
                
                if employees_count > 0:
                    dependencies_info.append(f"👥 الموظفين المرتبطين: {employees_count} موظف")
                    can_delete = False
                else:
                    dependencies_info.append("👥 الموظفين المرتبطين: لا يوجد موظفين مرتبطين")
            except sqlite3.OperationalError:
                # جدول الموظفين غير موجود
                dependencies_info.append("👥 الموظفين المرتبطين: لا يوجد موظفين مرتبطين")
            
            conn.close()
            
            # عرض المعلومات
            self.dependencies_text.setPlainText("\n".join(dependencies_info))
            
            # تفعيل/تعطيل زر الحذف
            if can_delete:
                self.delete_btn.setEnabled(True)
                self.dependencies_text.append("\n✅ يمكن حذف الفرع بأمان")
            else:
                self.delete_btn.setEnabled(False)
                self.dependencies_text.append("\n❌ لا يمكن حذف الفرع لوجود بيانات مرتبطة")
                self.dependencies_text.append("\nيجب حذف أو نقل البيانات المرتبطة أولاً")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في التحقق من التبعيات:\n{str(e)}")
            self.reject()
    
    def delete_branch(self):
        """حذف الفرع"""
        try:
            # تأكيد إضافي
            reply = QMessageBox.question(
                self, 
                "تأكيد نهائي", 
                f"هل أنت متأكد تماماً من حذف الفرع '{self.branch_name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # حذف الفرع من قاعدة البيانات
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # التحقق من وجود عمود updated_at
            cursor.execute("PRAGMA table_info(branches)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]

            # حذف الفرع (soft delete)
            if 'updated_at' in column_names:
                cursor.execute("UPDATE branches SET is_active = 0, updated_at = ? WHERE id = ?",
                             (datetime.now().isoformat(), self.branch_id))
            else:
                cursor.execute("UPDATE branches SET is_active = 0 WHERE id = ?", (self.branch_id,))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "تم الحذف", f"تم حذف الفرع '{self.branch_name}' بنجاح")
            
            # إرسال إشارة
            self.branch_deleted.emit(self.branch_id)
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف الفرع:\n{str(e)}")
