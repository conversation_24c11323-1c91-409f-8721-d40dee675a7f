# -*- coding: utf-8 -*-
"""
تبويب مندوبي المشتريات
Purchase Representatives Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QComboBox, QCheckBox,
                               QTextEdit, QDoubleSpinBox, QAbstractItemView, QSplitter,
                               QDateEdit, QSpinBox)
from PySide6.QtCore import Qt, QDate, QTimer
from PySide6.QtGui import QFont

from ...database.database_manager import DatabaseManager
from ...database.models import PurchaseRepresentative, Supplier, SupplierRepresentative, Currency


class PurchaseRepresentativesTab(QWidget):
    """تبويب مندوبي المشتريات"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_representative_id = None
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        self.setup_ui()
        self.load_currencies()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # إنشاء splitter للتحكم في الأحجام
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - نموذج الإدخال
        form_widget = self.create_form_section()
        form_widget.setMaximumWidth(500)
        form_widget.setMinimumWidth(450)
        
        # الجانب الأيمن - الجدول
        table_widget = self.create_table_section()
        
        splitter.addWidget(form_widget)
        splitter.addWidget(table_widget)
        splitter.setStretchFactor(0, 0)  # النموذج لا يتمدد
        splitter.setStretchFactor(1, 1)  # الجدول يتمدد
        
        main_layout.addWidget(splitter)
    
    def create_form_section(self):
        """إنشاء قسم النموذج"""
        form_group = QGroupBox("بيانات مندوب المشتريات")
        form_layout = QVBoxLayout(form_group)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(15, 15, 15, 15)

        # نموذج الإدخال
        input_form = QFormLayout()
        input_form.setSpacing(10)

        # البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(8)

        # كود المندوب
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("كود المندوب (مطلوب)")
        self.code_edit.setMinimumHeight(30)
        basic_layout.addRow("كود المندوب *:", self.code_edit)

        # اسم المندوب
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("اسم المندوب")
        self.name_edit.setMinimumHeight(30)
        basic_layout.addRow("اسم المندوب *:", self.name_edit)

        # الاسم بالإنجليزية
        self.name_en_edit = QLineEdit()
        self.name_en_edit.setPlaceholderText("Name in English")
        self.name_en_edit.setMinimumHeight(30)
        basic_layout.addRow("الاسم بالإنجليزية:", self.name_en_edit)

        # العملة
        self.currency_combo = QComboBox()
        self.currency_combo.setMinimumHeight(30)
        basic_layout.addRow("العملة:", self.currency_combo)

        form_layout.addWidget(basic_group)

        # بيانات الاتصال
        contact_group = QGroupBox("بيانات الاتصال")
        contact_layout = QFormLayout(contact_group)
        contact_layout.setSpacing(8)

        # الهاتف
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("رقم الهاتف")
        self.phone_edit.setMinimumHeight(30)
        contact_layout.addRow("الهاتف:", self.phone_edit)

        # الجوال
        self.mobile_edit = QLineEdit()
        self.mobile_edit.setPlaceholderText("رقم الجوال")
        self.mobile_edit.setMinimumHeight(30)
        contact_layout.addRow("الجوال:", self.mobile_edit)

        # البريد الإلكتروني
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("البريد الإلكتروني")
        self.email_edit.setMinimumHeight(30)
        contact_layout.addRow("البريد الإلكتروني:", self.email_edit)

        form_layout.addWidget(contact_group)

        # بيانات العمل
        work_group = QGroupBox("بيانات العمل")
        work_layout = QFormLayout(work_group)
        work_layout.setSpacing(8)

        # القسم
        self.department_edit = QLineEdit()
        self.department_edit.setPlaceholderText("القسم")
        self.department_edit.setMinimumHeight(30)
        work_layout.addRow("القسم:", self.department_edit)

        # المنصب
        self.position_edit = QLineEdit()
        self.position_edit.setPlaceholderText("المنصب")
        self.position_edit.setMinimumHeight(30)
        work_layout.addRow("المنصب:", self.position_edit)

        # تاريخ التوظيف
        self.hire_date_edit = QDateEdit()
        self.hire_date_edit.setDate(QDate.currentDate())
        self.hire_date_edit.setCalendarPopup(True)
        self.hire_date_edit.setMinimumHeight(30)
        work_layout.addRow("تاريخ التوظيف:", self.hire_date_edit)

        # الراتب
        self.salary_spin = QDoubleSpinBox()
        self.salary_spin.setRange(0, 999999)
        self.salary_spin.setDecimals(2)
        self.salary_spin.setSuffix(" ريال")
        self.salary_spin.setMinimumHeight(30)
        work_layout.addRow("الراتب:", self.salary_spin)

        # نسبة العمولة
        self.commission_spin = QDoubleSpinBox()
        self.commission_spin.setRange(0, 100)
        self.commission_spin.setDecimals(2)
        self.commission_spin.setSuffix(" %")
        self.commission_spin.setMinimumHeight(30)
        work_layout.addRow("نسبة العمولة:", self.commission_spin)

        # المدينة
        self.city_edit = QLineEdit()
        self.city_edit.setPlaceholderText("المدينة")
        self.city_edit.setMinimumHeight(30)
        work_layout.addRow("المدينة:", self.city_edit)

        form_layout.addWidget(work_group)

        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.add_button = QPushButton("إضافة مندوب")
        self.add_button.clicked.connect(self.add_representative)
        self.add_button.setMinimumHeight(40)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        self.update_button = QPushButton("تحديث")
        self.update_button.clicked.connect(self.update_representative)
        self.update_button.setEnabled(False)
        self.update_button.setMinimumHeight(40)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)

        self.clear_button = QPushButton("مسح")
        self.clear_button.clicked.connect(self.clear_form)
        self.clear_button.setMinimumHeight(40)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.clear_button)

        form_layout.addLayout(buttons_layout)
        
        return form_group
    
    def create_table_section(self):
        """إنشاء قسم الجدول"""
        table_group = QGroupBox("مندوبي المشتريات")
        table_layout = QVBoxLayout(table_group)
        table_layout.setSpacing(10)
        table_layout.setContentsMargins(15, 15, 15, 15)

        # شريط البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم أو الكود أو القسم...")
        self.search_edit.textChanged.connect(self.on_search_changed)
        self.search_edit.setMinimumHeight(30)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)
        table_layout.addLayout(search_layout)

        # الجدول
        self.representatives_table = QTableWidget()
        self.representatives_table.setColumnCount(9)
        self.representatives_table.setHorizontalHeaderLabels([
            "الكود", "اسم المندوب", "القسم", "المنصب", "الهاتف", "البريد الإلكتروني", "العملة", "المدينة", "الحالة"
        ])
        
        # إعدادات الجدول
        self.representatives_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.representatives_table.setAlternatingRowColors(True)
        self.representatives_table.horizontalHeader().setStretchLastSection(True)
        self.representatives_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # ضبط عرض الأعمدة
        header = self.representatives_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # الكود
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # اسم المندوب
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # القسم
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # المنصب
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الهاتف
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # البريد
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # العملة
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # المدينة
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # الحالة

        # ربط إشارة التحديد
        self.representatives_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        table_layout.addWidget(self.representatives_table)

        # أزرار الجدول
        table_buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_data)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        delete_button = QPushButton("حذف")
        delete_button.clicked.connect(self.delete_representative)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addStretch()
        
        table_layout.addLayout(table_buttons_layout)
        
        return table_group

    def load_currencies(self):
        """تحميل العملات من قاعدة البيانات"""
        session = self.db_manager.get_session()
        try:
            currencies = session.query(Currency).filter_by(is_active=True).order_by(Currency.name).all()

            self.currency_combo.clear()

            # إضافة العملات من قاعدة البيانات
            for currency in currencies:
                display_text = currency.name
                if currency.symbol:
                    display_text += f" ({currency.symbol})"
                self.currency_combo.addItem(display_text, currency.id)

            # إذا لم توجد عملات، إضافة عملات افتراضية
            if self.currency_combo.count() == 0:
                default_currencies = [
                    ("ريال سعودي", "SAR"),
                    ("دولار أمريكي", "USD"),
                    ("يورو", "EUR"),
                    ("جنيه إسترليني", "GBP"),
                    ("درهم إماراتي", "AED")
                ]
                for name, symbol in default_currencies:
                    self.currency_combo.addItem(f"{name} ({symbol})", None)

            # تحديد العملة الافتراضية
            default_index = 0
            for i in range(self.currency_combo.count()):
                if "ريال سعودي" in self.currency_combo.itemText(i):
                    default_index = i
                    break
            self.currency_combo.setCurrentIndex(default_index)

        except Exception as e:
            # في حالة الخطأ، استخدام عملات افتراضية
            self.currency_combo.clear()
            default_currencies = [
                "ريال سعودي (SAR)",
                "دولار أمريكي (USD)",
                "يورو (EUR)",
                "جنيه إسترليني (GBP)",
                "درهم إماراتي (AED)"
            ]
            self.currency_combo.addItems(default_currencies)
            self.currency_combo.setCurrentIndex(0)

        finally:
            session.close()

    def load_data(self):
        """تحميل بيانات مندوبي المشتريات"""
        session = self.db_manager.get_session()
        try:
            representatives = session.query(PurchaseRepresentative).filter_by(
                is_active=True
            ).order_by(PurchaseRepresentative.name).all()

            self.representatives_table.setRowCount(len(representatives))

            for row, rep in enumerate(representatives):
                # الكود
                code_item = QTableWidgetItem(rep.code or "")
                code_item.setData(Qt.UserRole, rep.id)
                self.representatives_table.setItem(row, 0, code_item)

                # اسم المندوب
                name_item = QTableWidgetItem(rep.name or "")
                self.representatives_table.setItem(row, 1, name_item)

                # القسم
                department_item = QTableWidgetItem(rep.department or "")
                self.representatives_table.setItem(row, 2, department_item)

                # المنصب
                position_item = QTableWidgetItem(rep.position or "")
                self.representatives_table.setItem(row, 3, position_item)

                # الهاتف
                phone = rep.mobile or rep.phone or ""
                phone_item = QTableWidgetItem(phone)
                self.representatives_table.setItem(row, 4, phone_item)

                # البريد الإلكتروني
                email_item = QTableWidgetItem(rep.email or "")
                self.representatives_table.setItem(row, 5, email_item)

                # العملة
                currency_item = QTableWidgetItem(rep.currency or "ريال سعودي")
                self.representatives_table.setItem(row, 6, currency_item)

                # المدينة
                city_item = QTableWidgetItem(rep.city or "")
                self.representatives_table.setItem(row, 7, city_item)

                # الحالة
                status_item = QTableWidgetItem("نشط" if rep.is_active else "غير نشط")
                self.representatives_table.setItem(row, 8, status_item)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
        finally:
            session.close()

    def on_search_changed(self):
        """عند تغيير نص البحث"""
        self.search_timer.stop()
        self.search_timer.start(300)  # انتظار 300ms قبل البحث

    def perform_search(self):
        """تنفيذ البحث"""
        search_text = self.search_edit.text().strip().lower()

        session = self.db_manager.get_session()
        try:
            query = session.query(PurchaseRepresentative).filter(
                PurchaseRepresentative.is_active == True
            )

            if search_text:
                query = query.filter(
                    (PurchaseRepresentative.name.ilike(f'%{search_text}%')) |
                    (PurchaseRepresentative.code.ilike(f'%{search_text}%')) |
                    (PurchaseRepresentative.department.ilike(f'%{search_text}%')) |
                    (PurchaseRepresentative.position.ilike(f'%{search_text}%'))
                )

            representatives = query.order_by(PurchaseRepresentative.name).all()

            # ملء الجدول
            self.representatives_table.setRowCount(len(representatives))

            for row, rep in enumerate(representatives):
                # الكود
                code_item = QTableWidgetItem(rep.code or "")
                code_item.setData(Qt.UserRole, rep.id)
                self.representatives_table.setItem(row, 0, code_item)

                # اسم المندوب
                name_item = QTableWidgetItem(rep.name or "")
                self.representatives_table.setItem(row, 1, name_item)

                # القسم
                department_item = QTableWidgetItem(rep.department or "")
                self.representatives_table.setItem(row, 2, department_item)

                # المنصب
                position_item = QTableWidgetItem(rep.position or "")
                self.representatives_table.setItem(row, 3, position_item)

                # الهاتف
                phone = rep.mobile or rep.phone or ""
                phone_item = QTableWidgetItem(phone)
                self.representatives_table.setItem(row, 4, phone_item)

                # البريد الإلكتروني
                email_item = QTableWidgetItem(rep.email or "")
                self.representatives_table.setItem(row, 5, email_item)

                # العملة
                currency_item = QTableWidgetItem(rep.currency or "ريال سعودي")
                self.representatives_table.setItem(row, 6, currency_item)

                # المدينة
                city_item = QTableWidgetItem(rep.city or "")
                self.representatives_table.setItem(row, 7, city_item)

                # الحالة
                status_item = QTableWidgetItem("نشط" if rep.is_active else "غير نشط")
                self.representatives_table.setItem(row, 8, status_item)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في البحث: {str(e)}")
        finally:
            session.close()

    def add_representative(self):
        """إضافة مندوب جديد"""
        if not self.validate_form():
            return

        session = self.db_manager.get_session()
        try:
            # التحقق من عدم تكرار الكود
            existing = session.query(PurchaseRepresentative).filter_by(
                code=self.code_edit.text().strip()
            ).first()

            if existing:
                QMessageBox.warning(self, "تحذير", "كود المندوب موجود بالفعل!")
                return

            # إنشاء مندوب جديد
            representative = PurchaseRepresentative(
                code=self.code_edit.text().strip(),
                name=self.name_edit.text().strip(),
                name_en=self.name_en_edit.text().strip() or None,
                phone=self.phone_edit.text().strip() or None,
                mobile=self.mobile_edit.text().strip() or None,
                email=self.email_edit.text().strip() or None,
                department=self.department_edit.text().strip() or None,
                position=self.position_edit.text().strip() or None,
                hire_date=self.hire_date_edit.date().toPython(),
                salary=self.salary_spin.value() if self.salary_spin.value() > 0 else None,
                commission_rate=self.commission_spin.value(),
                currency=self.currency_combo.currentText(),
                city=self.city_edit.text().strip() or None
            )

            session.add(representative)
            session.commit()

            QMessageBox.information(self, "نجح", "تم إضافة المندوب بنجاح!")
            self.clear_form()
            self.load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المندوب: {str(e)}")
        finally:
            session.close()

    def update_representative(self):
        """تحديث مندوب موجود"""
        if not self.current_representative_id:
            return

        if not self.validate_form():
            return

        session = self.db_manager.get_session()
        try:
            representative = session.query(PurchaseRepresentative).get(self.current_representative_id)
            if not representative:
                QMessageBox.warning(self, "خطأ", "المندوب غير موجود!")
                return

            # التحقق من عدم تكرار الكود (إذا تم تغييره)
            if representative.code != self.code_edit.text().strip():
                existing = session.query(PurchaseRepresentative).filter_by(
                    code=self.code_edit.text().strip()
                ).filter(
                    PurchaseRepresentative.id != self.current_representative_id
                ).first()

                if existing:
                    QMessageBox.warning(self, "تحذير", "كود المندوب موجود بالفعل!")
                    return

            # تحديث البيانات
            representative.code = self.code_edit.text().strip()
            representative.name = self.name_edit.text().strip()
            representative.name_en = self.name_en_edit.text().strip() or None
            representative.phone = self.phone_edit.text().strip() or None
            representative.mobile = self.mobile_edit.text().strip() or None
            representative.email = self.email_edit.text().strip() or None
            representative.department = self.department_edit.text().strip() or None
            representative.position = self.position_edit.text().strip() or None
            representative.hire_date = self.hire_date_edit.date().toPython()
            representative.salary = self.salary_spin.value() if self.salary_spin.value() > 0 else None
            representative.commission_rate = self.commission_spin.value()
            representative.currency = self.currency_combo.currentText()
            representative.city = self.city_edit.text().strip() or None

            session.commit()

            QMessageBox.information(self, "نجح", "تم تحديث المندوب بنجاح!")
            self.clear_form()
            self.load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث المندوب: {str(e)}")
        finally:
            session.close()

    def delete_representative(self):
        """حذف مندوب محدد"""
        current_row = self.representatives_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مندوب للحذف")
            return

        # الحصول على معرف المندوب
        representative_id = self.representatives_table.item(current_row, 0).data(Qt.UserRole)
        if not representative_id:
            return

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا المندوب؟\nسيتم إلغاء تفعيله بدلاً من الحذف النهائي.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        session = self.db_manager.get_session()
        try:
            representative = session.query(PurchaseRepresentative).get(representative_id)
            if representative:
                # حذف ناعم - تعطيل المندوب بدلاً من الحذف
                representative.is_active = False
                session.commit()

                QMessageBox.information(self, "نجح", "تم حذف المندوب بنجاح!")
                self.load_data()
            else:
                QMessageBox.warning(self, "خطأ", "المندوب غير موجود!")

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف المندوب: {str(e)}")
        finally:
            session.close()

    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.representatives_table.currentRow()
        if current_row >= 0:
            self.load_selected_representative()

    def load_selected_representative(self):
        """تحميل بيانات المندوب المحدد"""
        current_row = self.representatives_table.currentRow()
        if current_row < 0:
            return

        representative_id = self.representatives_table.item(current_row, 0).data(Qt.UserRole)
        if not representative_id:
            return

        session = self.db_manager.get_session()
        try:
            representative = session.query(PurchaseRepresentative).get(representative_id)
            if not representative:
                return

            self.current_representative_id = representative.id

            # تعبئة النموذج
            self.code_edit.setText(representative.code or "")
            self.name_edit.setText(representative.name or "")
            self.name_en_edit.setText(representative.name_en or "")
            self.phone_edit.setText(representative.phone or "")
            self.mobile_edit.setText(representative.mobile or "")
            self.email_edit.setText(representative.email or "")
            self.department_edit.setText(representative.department or "")
            self.position_edit.setText(representative.position or "")

            if representative.hire_date:
                self.hire_date_edit.setDate(QDate.fromString(str(representative.hire_date), "yyyy-MM-dd"))
            else:
                self.hire_date_edit.setDate(QDate.currentDate())

            self.salary_spin.setValue(representative.salary or 0)
            self.commission_spin.setValue(representative.commission_rate or 0)

            # تحديد العملة
            currency = representative.currency or "ريال سعودي"
            index = self.currency_combo.findText(currency)
            if index >= 0:
                self.currency_combo.setCurrentIndex(index)
            else:
                self.currency_combo.setCurrentText("ريال سعودي")

            self.city_edit.setText(representative.city or "")

            # تفعيل زر التحديث
            self.add_button.setEnabled(False)
            self.update_button.setEnabled(True)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
        finally:
            session.close()

    def clear_form(self):
        """مسح النموذج"""
        self.current_representative_id = None
        self.code_edit.clear()
        self.name_edit.clear()
        self.name_en_edit.clear()
        self.phone_edit.clear()
        self.mobile_edit.clear()
        self.email_edit.clear()
        self.department_edit.clear()
        self.position_edit.clear()
        self.hire_date_edit.setDate(QDate.currentDate())
        self.salary_spin.setValue(0)
        self.commission_spin.setValue(0)

        # تحديد العملة الافتراضية
        default_index = 0
        for i in range(self.currency_combo.count()):
            if "ريال سعودي" in self.currency_combo.itemText(i):
                default_index = i
                break
        self.currency_combo.setCurrentIndex(default_index)

        self.city_edit.clear()

        # إعادة تفعيل زر الإضافة
        self.add_button.setEnabled(True)
        self.update_button.setEnabled(False)

    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.code_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كود المندوب")
            self.code_edit.setFocus()
            return False

        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المندوب")
            self.name_edit.setFocus()
            return False

        return True
