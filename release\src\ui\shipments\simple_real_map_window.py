#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة خرائط حقيقية مبسطة للاختبار
"""

from PySide6.QtWidgets import (QMainWindow, QVBoxLayout, QHBoxLayout, 
                               QWidget, QPushButton, QLabel, QMessageBox,
                               QProgressBar, QTextEdit)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont
import tempfile
import webbrowser
import os

try:
    import folium
    FOLIUM_AVAILABLE = True
except ImportError:
    FOLIUM_AVAILABLE = False

class SimpleMapThread(QThread):
    """خيط بسيط لإنشاء الخريطة"""
    finished = Signal(str)
    error = Signal(str)
    
    def run(self):
        try:
            if not FOLIUM_AVAILABLE:
                self.error.emit("مكتبة Folium غير متوفرة")
                return
            
            # إنشاء خريطة بسيطة
            m = folium.Map(
                location=[24.7136, 46.6753],  # الرياض
                zoom_start=6,
                tiles='OpenStreetMap'
            )
            
            # إضافة علامة بسيطة
            folium.Marker(
                [24.7136, 46.6753],
                popup="الرياض - المملكة العربية السعودية",
                tooltip="الرياض"
            ).add_to(m)
            
            # حفظ الخريطة
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.html')
            temp_file.close()
            m.save(temp_file.name)
            
            self.finished.emit(temp_file.name)
            
        except Exception as e:
            self.error.emit(str(e))

class SimpleRealMapWindow(QMainWindow):
    """نافذة خرائط حقيقية مبسطة"""
    
    def __init__(self):
        super().__init__()
        self.current_map_file = None
        self.map_thread = None
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🗺️ خرائط حقيقية مبسطة - اختبار")
        self.setGeometry(200, 200, 800, 600)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        
        # العنوان
        title_label = QLabel("🌍 اختبار الخرائط الحقيقية")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # معلومات الحالة
        self.status_label = QLabel("جاهز للاختبار")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # منطقة المعلومات
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(200)
        self.info_text.setPlainText("معلومات الاختبار:\n")
        layout.addWidget(self.info_text)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        # زر إنشاء الخريطة
        self.create_map_btn = QPushButton("🗺️ إنشاء خريطة تجريبية")
        self.create_map_btn.clicked.connect(self.create_test_map)
        buttons_layout.addWidget(self.create_map_btn)
        
        # زر فتح الخريطة
        self.open_map_btn = QPushButton("🌐 فتح في المتصفح")
        self.open_map_btn.clicked.connect(self.open_in_browser)
        self.open_map_btn.setEnabled(False)
        buttons_layout.addWidget(self.open_map_btn)
        
        # زر اختبار المكتبات
        test_libs_btn = QPushButton("🔧 اختبار المكتبات")
        test_libs_btn.clicked.connect(self.test_libraries)
        buttons_layout.addWidget(test_libs_btn)
        
        layout.addLayout(buttons_layout)
        
        # اختبار المكتبات عند البدء
        self.test_libraries()
    
    def add_info(self, text):
        """إضافة معلومات للنص"""
        current = self.info_text.toPlainText()
        self.info_text.setPlainText(current + text + "\n")
        
    def test_libraries(self):
        """اختبار توفر المكتبات"""
        self.add_info("=== اختبار المكتبات ===")
        
        # اختبار Folium
        try:
            import folium
            self.add_info(f"✅ Folium متوفر - الإصدار: {folium.__version__}")
        except ImportError as e:
            self.add_info(f"❌ Folium غير متوفر: {e}")
            
        # اختبار Geopy
        try:
            import geopy
            self.add_info(f"✅ Geopy متوفر - الإصدار: {geopy.__version__}")
        except ImportError as e:
            self.add_info(f"❌ Geopy غير متوفر: {e}")
            
        # اختبار Branca
        try:
            import branca
            self.add_info(f"✅ Branca متوفر - الإصدار: {branca.__version__}")
        except ImportError as e:
            self.add_info(f"❌ Branca غير متوفر: {e}")
            
        self.add_info("=== انتهاء الاختبار ===")
    
    def create_test_map(self):
        """إنشاء خريطة تجريبية"""
        if not FOLIUM_AVAILABLE:
            QMessageBox.critical(self, "خطأ", "مكتبة Folium غير متوفرة!")
            return
            
        self.add_info("بدء إنشاء خريطة تجريبية...")
        self.status_label.setText("جاري إنشاء الخريطة...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        self.create_map_btn.setEnabled(False)
        
        # إنشاء وتشغيل الخيط
        self.map_thread = SimpleMapThread()
        self.map_thread.finished.connect(self.on_map_created)
        self.map_thread.error.connect(self.on_map_error)
        self.map_thread.start()
    
    def on_map_created(self, file_path):
        """عند إنشاء الخريطة بنجاح"""
        self.current_map_file = file_path
        self.add_info(f"✅ تم إنشاء الخريطة: {file_path}")
        self.status_label.setText("تم إنشاء الخريطة بنجاح!")
        self.progress_bar.setVisible(False)
        self.create_map_btn.setEnabled(True)
        self.open_map_btn.setEnabled(True)
        
    def on_map_error(self, error_msg):
        """عند حدوث خطأ في إنشاء الخريطة"""
        self.add_info(f"❌ خطأ في إنشاء الخريطة: {error_msg}")
        self.status_label.setText("فشل في إنشاء الخريطة")
        self.progress_bar.setVisible(False)
        self.create_map_btn.setEnabled(True)
        QMessageBox.critical(self, "خطأ", f"فشل في إنشاء الخريطة:\n{error_msg}")
        
    def open_in_browser(self):
        """فتح الخريطة في المتصفح"""
        if self.current_map_file and os.path.exists(self.current_map_file):
            try:
                webbrowser.open(f'file://{os.path.abspath(self.current_map_file)}')
                self.add_info("✅ تم فتح الخريطة في المتصفح")
            except Exception as e:
                self.add_info(f"❌ فشل في فتح المتصفح: {e}")
                QMessageBox.critical(self, "خطأ", f"فشل في فتح المتصفح:\n{e}")
        else:
            QMessageBox.warning(self, "تحذير", "لا توجد خريطة لفتحها!")
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        # تنظيف الملفات المؤقتة
        if self.current_map_file and os.path.exists(self.current_map_file):
            try:
                os.unlink(self.current_map_file)
            except:
                pass
        event.accept()
