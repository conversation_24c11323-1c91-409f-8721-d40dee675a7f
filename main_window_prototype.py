#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج تجريبي للواجهة الرئيسية مطابق للصورة
Main Window Prototype Based on Image Design
"""

import sys
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QToolBar, QStatusBar, QLabel, QPushButton, QFrame,
    QScrollArea, QSplitter, QTextEdit, QTreeWidget, QTreeWidgetItem,
    QSizePolicy, QSpacerItem
)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import (
    QPixmap, QPainter, QLinearGradient, QColor, QFont, QIcon,
    QPalette, QBrush, QPen
)

class GradientWidget(QWidget):
    """ويدجت مع خلفية متدرجة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(800, 600)
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # إنشاء التدرج اللوني
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(240, 248, 255))  # أزرق فاتح
        gradient.setColorAt(0.3, QColor(255, 255, 255))  # أبيض
        gradient.setColorAt(0.7, QColor(255, 255, 255))  # أبيض
        gradient.setColorAt(1, QColor(255, 240, 245))  # وردي فاتح
        
        painter.fillRect(self.rect(), gradient)
        
        # رسم الخطوط المنحنية الزخرفية
        pen = QPen(QColor(100, 150, 255, 50), 2)
        painter.setPen(pen)
        
        # خطوط منحنية في الأسفل (أزرق)
        for i in range(20):
            x = i * 40
            y = self.height() - 100 + (i % 3) * 20
            painter.drawLine(x, y, x + 200, y - 50)
        
        # خطوط منحنية في الأعلى (أحمر)
        pen.setColor(QColor(255, 100, 100, 50))
        painter.setPen(pen)
        for i in range(15):
            x = self.width() - i * 50
            y = 100 + (i % 2) * 30
            painter.drawLine(x, y, x - 150, y + 40)

class LogoWidget(QLabel):
    """ويدجت عرض الشعار"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlignment(Qt.AlignCenter)
        self.setMinimumSize(400, 200)
        
        # إنشاء نص الشعار
        self.setText("SHIPMENT ERP")
        font = QFont("Arial", 48, QFont.Bold)
        self.setFont(font)
        
        # تلوين النص
        self.setStyleSheet("""
            QLabel {
                color: #2E86AB;
                background: transparent;
            }
        """)

class SystemTreeWidget(QFrame):
    """شجرة أنظمة التطبيق"""

    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumWidth(250)
        self.setMinimumWidth(220)

        layout = QVBoxLayout(self)

        # عنوان القائمة
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #E8F4FD;
                padding: 8px;
                border: 1px solid #B0D4F1;
                border-radius: 4px;
                color: #2E86AB;
            }
        """)
        layout.addWidget(title_label)

        # إنشاء شجرة الأنظمة
        self.tree = QTreeWidget()
        self.tree.setHeaderHidden(True)
        self.tree.setRootIsDecorated(True)
        self.tree.setIndentation(20)

        # تطبيق الأنماط على الشجرة
        self.tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                font-size: 11px;
            }
            QTreeWidget::item {
                padding: 6px;
                border-bottom: 1px solid #F0F0F0;
            }
            QTreeWidget::item:hover {
                background-color: #F0F8FF;
                color: #2E86AB;
            }
            QTreeWidget::item:selected {
                background-color: #E8F4FD;
                color: #2E86AB;
                font-weight: bold;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: url(none);
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: url(none);
            }
        """)

        self.setup_tree_items()
        layout.addWidget(self.tree)

        # ربط الأحداث
        self.tree.itemClicked.connect(self.on_item_clicked)

    def setup_tree_items(self):
        """إعداد عناصر الشجرة"""

        # 1. نظام إدارة الشحنات
        shipment_system = QTreeWidgetItem(self.tree, ["📦 نظام إدارة الشحنات"])
        shipment_system.setExpanded(True)

        # فروع نظام الشحنات
        QTreeWidgetItem(shipment_system, ["📋 إنشاء شحنة جديدة"])
        QTreeWidgetItem(shipment_system, ["🔍 البحث عن الشحنات"])
        QTreeWidgetItem(shipment_system, ["📊 تتبع الشحنات"])
        QTreeWidgetItem(shipment_system, ["📝 تحديث حالة الشحنة"])
        QTreeWidgetItem(shipment_system, ["🏷️ طباعة ملصقات الشحن"])
        QTreeWidgetItem(shipment_system, ["📈 تقارير الشحنات"])

        # 2. نظام إدارة العملاء
        customer_system = QTreeWidgetItem(self.tree, ["👥 نظام إدارة العملاء"])
        customer_system.setExpanded(False)

        # فروع نظام العملاء
        QTreeWidgetItem(customer_system, ["➕ إضافة عميل جديد"])
        QTreeWidgetItem(customer_system, ["✏️ تعديل بيانات العميل"])
        QTreeWidgetItem(customer_system, ["🔍 البحث عن العملاء"])
        QTreeWidgetItem(customer_system, ["📋 قائمة العملاء"])
        QTreeWidgetItem(customer_system, ["💳 إدارة الحسابات"])
        QTreeWidgetItem(customer_system, ["📊 تقارير العملاء"])

        # 3. نظام إدارة المخازن
        warehouse_system = QTreeWidgetItem(self.tree, ["🏪 نظام إدارة المخازن"])
        warehouse_system.setExpanded(False)

        # فروع نظام المخازن
        QTreeWidgetItem(warehouse_system, ["📦 إدارة المخزون"])
        QTreeWidgetItem(warehouse_system, ["📍 مواقع التخزين"])
        QTreeWidgetItem(warehouse_system, ["🔄 حركة البضائع"])
        QTreeWidgetItem(warehouse_system, ["📊 تقارير المخزون"])
        QTreeWidgetItem(warehouse_system, ["⚠️ تنبيهات المخزون"])

        # 4. النظام المالي
        financial_system = QTreeWidgetItem(self.tree, ["💰 النظام المالي"])
        financial_system.setExpanded(False)

        # فروع النظام المالي
        QTreeWidgetItem(financial_system, ["💵 إدارة الفواتير"])
        QTreeWidgetItem(financial_system, ["💳 المدفوعات"])
        QTreeWidgetItem(financial_system, ["📊 التقارير المالية"])
        QTreeWidgetItem(financial_system, ["💹 الأرباح والخسائر"])
        QTreeWidgetItem(financial_system, ["🧾 الضرائب"])

        # 5. نظام التقارير والإحصائيات
        reports_system = QTreeWidgetItem(self.tree, ["📈 نظام التقارير"])
        reports_system.setExpanded(False)

        # فروع نظام التقارير
        QTreeWidgetItem(reports_system, ["📊 التقارير اليومية"])
        QTreeWidgetItem(reports_system, ["📅 التقارير الشهرية"])
        QTreeWidgetItem(reports_system, ["📆 التقارير السنوية"])
        QTreeWidgetItem(reports_system, ["📋 تقارير مخصصة"])
        QTreeWidgetItem(reports_system, ["📈 الرسوم البيانية"])

        # 6. نظام الإعدادات والإدارة
        admin_system = QTreeWidgetItem(self.tree, ["⚙️ نظام الإدارة"])
        admin_system.setExpanded(False)

        # فروع نظام الإدارة
        QTreeWidgetItem(admin_system, ["👤 إدارة المستخدمين"])
        QTreeWidgetItem(admin_system, ["🔐 الصلاحيات والأذونات"])
        QTreeWidgetItem(admin_system, ["🗄️ النسخ الاحتياطية"])
        QTreeWidgetItem(admin_system, ["🔧 إعدادات النظام"])
        QTreeWidgetItem(admin_system, ["📝 سجل العمليات"])
        QTreeWidgetItem(admin_system, ["🔄 تحديثات النظام"])

    def on_item_clicked(self, item, column):
        """معالج النقر على عنصر في الشجرة"""
        item_text = item.text(0)
        print(f"تم النقر على: {item_text}")

        # يمكن إضافة منطق التنقل هنا
        # مثال: فتح نافذة معينة حسب العنصر المختار

class SideMenuWidget(QFrame):
    """القائمة الجانبية البسيطة"""

    def __init__(self, title, items, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumWidth(200)
        self.setMinimumWidth(180)

        layout = QVBoxLayout(self)

        # عنوان القائمة
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #E8F4FD;
                padding: 8px;
                border: 1px solid #B0D4F1;
                border-radius: 4px;
                color: #2E86AB;
            }
        """)
        layout.addWidget(title_label)

        # عناصر القائمة
        for item in items:
            btn = QPushButton(item)
            btn.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 8px 12px;
                    border: none;
                    background-color: transparent;
                    color: #333;
                }
                QPushButton:hover {
                    background-color: #F0F8FF;
                    color: #2E86AB;
                }
                QPushButton:pressed {
                    background-color: #E8F4FD;
                }
            """)
            layout.addWidget(btn)

        layout.addStretch()

class MainWindowPrototype(QMainWindow):
    """النافذة الرئيسية للنموذج التجريبي"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام إدارة الشحنات - SHIPMENT ERP")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # إنشاء القائمة الرئيسية
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء المحتوى المركزي
        self.create_central_widget()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        file_menu.addAction("جديد")
        file_menu.addAction("فتح")
        file_menu.addAction("حفظ")
        file_menu.addSeparator()
        file_menu.addAction("خروج")
        
        # قائمة تحرير
        edit_menu = menubar.addMenu("تحرير")
        edit_menu.addAction("تراجع")
        edit_menu.addAction("إعادة")
        edit_menu.addSeparator()
        edit_menu.addAction("نسخ")
        edit_menu.addAction("لصق")
        
        # قائمة عرض
        view_menu = menubar.addMenu("عرض")
        view_menu.addAction("شريط الأدوات")
        view_menu.addAction("شريط الحالة")
        view_menu.addAction("ملء الشاشة")
        
        # قائمة أدوات
        tools_menu = menubar.addMenu("أدوات")
        tools_menu.addAction("إعدادات")
        tools_menu.addAction("خيارات")
        
        # قائمة مساعدة
        help_menu = menubar.addMenu("مساعدة")
        help_menu.addAction("حول البرنامج")
        help_menu.addAction("دليل المستخدم")
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        
        # أزرار شريط الأدوات
        actions = [
            ("جديد", "إنشاء مستند جديد"),
            ("فتح", "فتح مستند موجود"),
            ("حفظ", "حفظ المستند الحالي"),
            ("طباعة", "طباعة المستند"),
            ("بحث", "البحث في النظام"),
            ("تقارير", "عرض التقارير"),
            ("إعدادات", "إعدادات النظام"),
            ("مساعدة", "الحصول على المساعدة")
        ]
        
        for text, tooltip in actions:
            action = toolbar.addAction(text)
            action.setToolTip(tooltip)
            if text in ["حفظ", "تقارير"]:
                toolbar.addSeparator()
    
    def create_central_widget(self):
        """إنشاء المحتوى المركزي"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # إنشاء المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # القائمة الجانبية اليسرى - شجرة الأنظمة
        left_menu = SystemTreeWidget("🏢 أنظمة التطبيق")
        splitter.addWidget(left_menu)
        
        # المنطقة المركزية
        center_widget = GradientWidget()
        center_layout = QVBoxLayout(center_widget)
        
        # إضافة مساحة فارغة في الأعلى
        center_layout.addStretch(1)
        
        # إضافة الشعار
        logo = LogoWidget()
        center_layout.addWidget(logo)

        # إضافة النص التوضيحي
        subtitle = QLabel("نظام إدارة الشحنات المتقدم")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setFont(QFont("Arial", 14))
        subtitle.setStyleSheet("color: #666; background: transparent;")
        center_layout.addWidget(subtitle)
        
        # إضافة مساحة فارغة في الأسفل
        center_layout.addStretch(1)
        
        splitter.addWidget(center_widget)
        
        # القائمة الجانبية اليمنى
        right_menu_items = [
            "إدارة المستخدمين",
            "صلاحيات النظام",
            "سجل العمليات",
            "النسخ الاحتياطية",
            "إعدادات الشبكة",
            "تحديثات النظام",
            "الدعم الفني",
            "اتصل بنا",
            "حول النظام"
        ]
        right_menu = SideMenuWidget("إعدادات النظام", right_menu_items)
        splitter.addWidget(right_menu)
        
        # تحديد أحجام المقاسم
        splitter.setSizes([250, 750, 200])
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()
        
        # معلومات المستخدم
        user_label = QLabel("المستخدم: مدير النظام")
        status_bar.addWidget(user_label)
        
        # فاصل
        status_bar.addPermanentWidget(QLabel(" | "))
        
        # تاريخ ووقت
        from datetime import datetime
        time_label = QLabel(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        status_bar.addPermanentWidget(time_label)
        
        # فاصل
        status_bar.addPermanentWidget(QLabel(" | "))
        
        # حالة الاتصال
        connection_label = QLabel("متصل بقاعدة البيانات")
        connection_label.setStyleSheet("color: green;")
        status_bar.addPermanentWidget(connection_label)
    
    def setup_style(self):
        """إعداد الأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #F5F5F5;
            }
            QMenuBar {
                background-color: #E8F4FD;
                border-bottom: 1px solid #B0D4F1;
                padding: 4px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #D1E7DD;
            }
            QToolBar {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                spacing: 3px;
                padding: 4px;
            }
            QStatusBar {
                background-color: #E9ECEF;
                border-top: 1px solid #DEE2E6;
                padding: 4px;
            }
        """)

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة الرئيسية
    window = MainWindowPrototype()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
