#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية للتطبيق - SHIPMENT ERP Main Interface
تم تحديثها من النموذج التجريبي لتصبح الواجهة الرئيسية
"""

import sys
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QToolBar, QStatusBar, QLabel, QPushButton, QFrame,
    QScrollArea, QSplitter, QTextEdit, QTreeWidget, QTreeWidgetItem,
    QSizePolicy, QSpacerItem, QMessageBox
)
from PySide6.QtCore import Qt, QSize, QTimer, Signal
from PySide6.QtGui import (
    QPixmap, QPainter, QLinearGradient, QC<PERSON>r, <PERSON>F<PERSON>, <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rush, Q<PERSON><PERSON>, QAction
)

# استيراد مكتبة القوالب
try:
    from template_library import Templates, نموذج_ادخال_اساسي
    TEMPLATES_AVAILABLE = True
except ImportError:
    TEMPLATES_AVAILABLE = False
    print("⚠️ مكتبة القوالب غير متاحة")

class GradientWidget(QWidget):
    """ويدجت مع خلفية متدرجة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(800, 600)
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # إنشاء التدرج اللوني
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(240, 248, 255))  # أزرق فاتح
        gradient.setColorAt(0.3, QColor(255, 255, 255))  # أبيض
        gradient.setColorAt(0.7, QColor(255, 255, 255))  # أبيض
        gradient.setColorAt(1, QColor(255, 240, 245))  # وردي فاتح
        
        painter.fillRect(self.rect(), gradient)
        
        # رسم الخطوط المنحنية الزخرفية
        pen = QPen(QColor(100, 150, 255, 50), 2)
        painter.setPen(pen)
        
        # خطوط منحنية في الأسفل (أزرق)
        for i in range(20):
            x = i * 40
            y = self.height() - 100 + (i % 3) * 20
            painter.drawLine(x, y, x + 200, y - 50)
        
        # خطوط منحنية في الأعلى (أحمر)
        pen.setColor(QColor(255, 100, 100, 50))
        painter.setPen(pen)
        for i in range(15):
            x = self.width() - i * 50
            y = 100 + (i % 2) * 30
            painter.drawLine(x, y, x - 150, y + 40)

class LogoWidget(QLabel):
    """ويدجت عرض الشعار"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlignment(Qt.AlignCenter)
        self.setMinimumSize(400, 200)
        
        # إنشاء نص الشعار
        self.setText("SHIPMENT ERP")
        font = QFont("Arial", 48, QFont.Bold)
        self.setFont(font)
        
        # تلوين النص
        self.setStyleSheet("""
            QLabel {
                color: #2E86AB;
                background: transparent;
            }
        """)

class SystemTreeWidget(QFrame):
    """شجرة أنظمة التطبيق"""

    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumWidth(250)
        self.setMinimumWidth(220)

        layout = QVBoxLayout(self)

        # عنوان القائمة
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #E8F4FD;
                padding: 8px;
                border: 1px solid #B0D4F1;
                border-radius: 4px;
                color: #2E86AB;
            }
        """)
        layout.addWidget(title_label)

        # إنشاء شجرة الأنظمة
        self.tree = QTreeWidget()
        self.tree.setHeaderHidden(True)
        self.tree.setRootIsDecorated(True)
        self.tree.setIndentation(20)

        # تطبيق الأنماط على الشجرة
        self.tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                font-size: 11px;
            }
            QTreeWidget::item {
                padding: 6px;
                border-bottom: 1px solid #F0F0F0;
            }
            QTreeWidget::item:hover {
                background-color: #F0F8FF;
                color: #2E86AB;
            }
            QTreeWidget::item:selected {
                background-color: #E8F4FD;
                color: #2E86AB;
                font-weight: bold;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: url(none);
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: url(none);
            }
        """)

        self.setup_tree_items()
        layout.addWidget(self.tree)

        # ربط الأحداث
        self.tree.itemClicked.connect(self.on_item_clicked)

    def setup_tree_items(self):
        """إعداد عناصر الشجرة"""

        # 1. نظام إدارة الشحنات
        shipment_system = QTreeWidgetItem(self.tree, ["📦 نظام إدارة الشحنات"])
        shipment_system.setExpanded(True)

        # فروع نظام الشحنات
        new_shipment_item = QTreeWidgetItem(shipment_system, ["📋 إنشاء شحنة جديدة"])
        new_shipment_item.setData(0, Qt.UserRole, "new_shipment")

        search_item = QTreeWidgetItem(shipment_system, ["🔍 البحث عن الشحنات"])
        search_item.setData(0, Qt.UserRole, "search_shipments")

        track_item = QTreeWidgetItem(shipment_system, ["📊 تتبع الشحنات"])
        track_item.setData(0, Qt.UserRole, "track_shipments")

        update_item = QTreeWidgetItem(shipment_system, ["📝 تحديث حالة الشحنة"])
        update_item.setData(0, Qt.UserRole, "update_status")

        print_item = QTreeWidgetItem(shipment_system, ["🏷️ طباعة ملصقات الشحن"])
        print_item.setData(0, Qt.UserRole, "print_labels")

        reports_item = QTreeWidgetItem(shipment_system, ["📈 تقارير الشحنات"])
        reports_item.setData(0, Qt.UserRole, "shipment_reports")

        # 2. نظام إدارة العملاء
        customer_system = QTreeWidgetItem(self.tree, ["👥 نظام إدارة العملاء"])
        customer_system.setExpanded(False)

        # فروع نظام العملاء
        QTreeWidgetItem(customer_system, ["➕ إضافة عميل جديد"])
        QTreeWidgetItem(customer_system, ["✏️ تعديل بيانات العميل"])
        QTreeWidgetItem(customer_system, ["🔍 البحث عن العملاء"])
        QTreeWidgetItem(customer_system, ["📋 قائمة العملاء"])
        QTreeWidgetItem(customer_system, ["💳 إدارة الحسابات"])
        QTreeWidgetItem(customer_system, ["📊 تقارير العملاء"])

        # 3. نظام إدارة المخازن
        warehouse_system = QTreeWidgetItem(self.tree, ["🏪 نظام إدارة المخازن"])
        warehouse_system.setExpanded(False)

        # فروع نظام المخازن
        QTreeWidgetItem(warehouse_system, ["📦 إدارة المخزون"])
        QTreeWidgetItem(warehouse_system, ["📍 مواقع التخزين"])
        QTreeWidgetItem(warehouse_system, ["🔄 حركة البضائع"])
        QTreeWidgetItem(warehouse_system, ["📊 تقارير المخزون"])
        QTreeWidgetItem(warehouse_system, ["⚠️ تنبيهات المخزون"])

        # 4. النظام المالي
        financial_system = QTreeWidgetItem(self.tree, ["💰 النظام المالي"])
        financial_system.setExpanded(False)

        # فروع النظام المالي
        QTreeWidgetItem(financial_system, ["💵 إدارة الفواتير"])
        QTreeWidgetItem(financial_system, ["💳 المدفوعات"])
        QTreeWidgetItem(financial_system, ["📊 التقارير المالية"])
        QTreeWidgetItem(financial_system, ["💹 الأرباح والخسائر"])
        QTreeWidgetItem(financial_system, ["🧾 الضرائب"])

        # 5. نظام التقارير والإحصائيات
        reports_system = QTreeWidgetItem(self.tree, ["📈 نظام التقارير"])
        reports_system.setExpanded(False)

        # فروع نظام التقارير
        QTreeWidgetItem(reports_system, ["📊 التقارير اليومية"])
        QTreeWidgetItem(reports_system, ["📅 التقارير الشهرية"])
        QTreeWidgetItem(reports_system, ["📆 التقارير السنوية"])
        QTreeWidgetItem(reports_system, ["📋 تقارير مخصصة"])
        QTreeWidgetItem(reports_system, ["📈 الرسوم البيانية"])

        # 6. مكتبة القوالب (إذا كانت متاحة)
        if TEMPLATES_AVAILABLE:
            templates_system = QTreeWidgetItem(self.tree, ["🏛️ مكتبة القوالب"])
            templates_system.setExpanded(False)

            # فروع مكتبة القوالب
            basic_template_item = QTreeWidgetItem(templates_system, ["📋 نموذج إدخال أساسي"])
            basic_template_item.setData(0, Qt.UserRole, "new_shipment")

            library_item = QTreeWidgetItem(templates_system, ["🏛️ مكتبة القوالب"])
            library_item.setData(0, Qt.UserRole, "template_library")

            QTreeWidgetItem(templates_system, ["📝 قوالب مخصصة"])
            QTreeWidgetItem(templates_system, ["⚙️ إعدادات القوالب"])

        # 7. نظام الإعدادات والإدارة
        admin_system = QTreeWidgetItem(self.tree, ["⚙️ نظام الإدارة"])
        admin_system.setExpanded(False)

        # فروع نظام الإدارة
        QTreeWidgetItem(admin_system, ["👤 إدارة المستخدمين"])
        QTreeWidgetItem(admin_system, ["🔐 الصلاحيات والأذونات"])
        QTreeWidgetItem(admin_system, ["🗄️ النسخ الاحتياطية"])
        QTreeWidgetItem(admin_system, ["🔧 إعدادات النظام"])
        QTreeWidgetItem(admin_system, ["📝 سجل العمليات"])
        QTreeWidgetItem(admin_system, ["🔄 تحديثات النظام"])

    def on_item_clicked(self, item, column):
        """معالج النقر على عنصر في الشجرة"""
        item_text = item.text(0)
        item_data = item.data(0, Qt.UserRole)

        print(f"تم النقر على: {item_text}")

        # معالجة النقر حسب نوع العنصر
        if item_data == "new_shipment":
            self.open_new_shipment_form()
        elif item_data == "search_shipments":
            self.show_search_dialog()
        elif item_data == "track_shipments":
            self.show_tracking_dialog()
        elif item_data == "template_library":
            self.open_template_library()
        else:
            # عرض رسالة للعناصر الأخرى
            QMessageBox.information(
                self, "معلومات",
                f"تم النقر على: {item_text}\n\n"
                "هذه الميزة قيد التطوير وستكون متاحة قريباً."
            )

class SideMenuWidget(QFrame):
    """القائمة الجانبية البسيطة"""

    def __init__(self, title, items, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumWidth(200)
        self.setMinimumWidth(180)

        layout = QVBoxLayout(self)

        # عنوان القائمة
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #E8F4FD;
                padding: 8px;
                border: 1px solid #B0D4F1;
                border-radius: 4px;
                color: #2E86AB;
            }
        """)
        layout.addWidget(title_label)

        # عناصر القائمة
        for item in items:
            btn = QPushButton(item)
            btn.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 8px 12px;
                    border: none;
                    background-color: transparent;
                    color: #333;
                }
                QPushButton:hover {
                    background-color: #F0F8FF;
                    color: #2E86AB;
                }
                QPushButton:pressed {
                    background-color: #E8F4FD;
                }
            """)
            layout.addWidget(btn)

        layout.addStretch()

class MainWindowPrototype(QMainWindow):
    """الواجهة الرئيسية للتطبيق - محدثة من النموذج التجريبي"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام إدارة الشحنات المتقدم - SHIPMENT ERP v2.0")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # متغيرات التطبيق
        self.active_forms = []
        self.current_user = "مدير النظام"
        self.system_status = "متصل"

        # إعداد مكتبة القوالب
        if TEMPLATES_AVAILABLE:
            Templates.setup_app(QApplication.instance())
            self.setup_template_connections()

        self.setup_ui()
        self.setup_style()
        self.setup_timer()

        # رسالة ترحيب
        self.show_welcome_message()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # إنشاء القائمة الرئيسية
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء المحتوى المركزي
        self.create_central_widget()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")

        new_action = QAction("جديد", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.open_new_shipment_form)
        file_menu.addAction(new_action)

        file_menu.addAction("فتح")
        file_menu.addAction("حفظ")
        file_menu.addSeparator()

        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة تحرير
        edit_menu = menubar.addMenu("تحرير")
        edit_menu.addAction("تراجع")
        edit_menu.addAction("إعادة")
        edit_menu.addSeparator()
        edit_menu.addAction("نسخ")
        edit_menu.addAction("لصق")
        
        # قائمة عرض
        view_menu = menubar.addMenu("عرض")
        view_menu.addAction("شريط الأدوات")
        view_menu.addAction("شريط الحالة")
        view_menu.addAction("ملء الشاشة")
        
        # قائمة النماذج
        forms_menu = menubar.addMenu("النماذج")

        basic_form_action = QAction("نموذج إدخال أساسي", self)
        basic_form_action.triggered.connect(self.open_new_shipment_form)
        forms_menu.addAction(basic_form_action)

        if TEMPLATES_AVAILABLE:
            template_library_action = QAction("مكتبة القوالب", self)
            template_library_action.triggered.connect(self.open_template_library)
            forms_menu.addAction(template_library_action)

        # قائمة أدوات
        tools_menu = menubar.addMenu("أدوات")
        tools_menu.addAction("إعدادات")
        tools_menu.addAction("خيارات")

        # قائمة مساعدة
        help_menu = menubar.addMenu("مساعدة")

        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

        help_menu.addAction("دليل المستخدم")
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        
        # أزرار شريط الأدوات
        actions = [
            ("جديد", "إنشاء مستند جديد"),
            ("فتح", "فتح مستند موجود"),
            ("حفظ", "حفظ المستند الحالي"),
            ("طباعة", "طباعة المستند"),
            ("بحث", "البحث في النظام"),
            ("تقارير", "عرض التقارير"),
            ("إعدادات", "إعدادات النظام"),
            ("مساعدة", "الحصول على المساعدة")
        ]
        
        for text, tooltip in actions:
            action = toolbar.addAction(text)
            action.setToolTip(tooltip)
            if text in ["حفظ", "تقارير"]:
                toolbar.addSeparator()
    
    def create_central_widget(self):
        """إنشاء المحتوى المركزي"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # إنشاء المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # القائمة الجانبية اليسرى - شجرة الأنظمة
        left_menu = SystemTreeWidget("🏢 أنظمة التطبيق")
        splitter.addWidget(left_menu)
        
        # المنطقة المركزية
        center_widget = GradientWidget()
        center_layout = QVBoxLayout(center_widget)
        
        # إضافة مساحة فارغة في الأعلى
        center_layout.addStretch(1)
        
        # إضافة الشعار
        logo = LogoWidget()
        center_layout.addWidget(logo)

        # إضافة النص التوضيحي
        subtitle = QLabel("نظام إدارة الشحنات المتقدم")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setFont(QFont("Arial", 14))
        subtitle.setStyleSheet("color: #666; background: transparent;")
        center_layout.addWidget(subtitle)
        
        # إضافة مساحة فارغة في الأسفل
        center_layout.addStretch(1)
        
        splitter.addWidget(center_widget)
        
        # القائمة الجانبية اليمنى
        right_menu_items = [
            "إدارة المستخدمين",
            "صلاحيات النظام",
            "سجل العمليات",
            "النسخ الاحتياطية",
            "إعدادات الشبكة",
            "تحديثات النظام",
            "الدعم الفني",
            "اتصل بنا",
            "حول النظام"
        ]
        right_menu = SideMenuWidget("إعدادات النظام", right_menu_items)
        splitter.addWidget(right_menu)
        
        # تحديد أحجام المقاسم
        splitter.setSizes([250, 750, 200])
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()
        
        # معلومات المستخدم
        user_label = QLabel("المستخدم: مدير النظام")
        status_bar.addWidget(user_label)
        
        # فاصل
        status_bar.addPermanentWidget(QLabel(" | "))
        
        # تاريخ ووقت
        from datetime import datetime
        time_label = QLabel(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        status_bar.addPermanentWidget(time_label)
        
        # فاصل
        status_bar.addPermanentWidget(QLabel(" | "))
        
        # حالة الاتصال
        connection_label = QLabel("متصل بقاعدة البيانات")
        connection_label.setStyleSheet("color: green;")
        status_bar.addPermanentWidget(connection_label)
    
    def setup_style(self):
        """إعداد الأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #F5F5F5;
            }
            QMenuBar {
                background-color: #E8F4FD;
                border-bottom: 1px solid #B0D4F1;
                padding: 4px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #D1E7DD;
            }
            QToolBar {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                spacing: 3px;
                padding: 4px;
            }
            QStatusBar {
                background-color: #E9ECEF;
                border-top: 1px solid #DEE2E6;
                padding: 4px;
            }
        """)

    def setup_template_connections(self):
        """إعداد اتصالات مكتبة القوالب"""
        if TEMPLATES_AVAILABLE:
            manager = Templates.get_manager()
            manager.template_created.connect(self.on_form_created)
            manager.template_closed.connect(self.on_form_closed)

    def setup_timer(self):
        """إعداد مؤقت تحديث الوقت"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # تحديث كل ثانية
        self.update_time()

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%H:%M:%S")
        # البحث عن عنصر الوقت في شريط الحالة وتحديثه
        for widget in self.statusBar().children():
            if isinstance(widget, QLabel) and "التاريخ:" in widget.text():
                widget.setText(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                break

    def open_new_shipment_form(self):
        """فتح نموذج شحنة جديد"""
        if TEMPLATES_AVAILABLE:
            try:
                form = نموذج_ادخال_اساسي(fullscreen=False)
                if form:
                    form.show()
                    self.statusBar().showMessage("تم فتح نموذج إدخال أساسي جديد", 3000)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في فتح النموذج:\n{str(e)}")
        else:
            QMessageBox.information(
                self, "معلومات",
                "مكتبة القوالب غير متاحة.\n"
                "تأكد من وجود ملف template_library.py"
            )

    def open_template_library(self):
        """فتح مكتبة القوالب"""
        if TEMPLATES_AVAILABLE:
            try:
                from run_template_library import TemplateSelector
                selector = TemplateSelector(self)
                selector.exec()
            except ImportError:
                QMessageBox.information(
                    self, "مكتبة القوالب",
                    "🏛️ مكتبة القوالب المتاحة:\n\n"
                    "• نموذج إدخال أساسي\n"
                    "• قوالب مخصصة (قيد التطوير)\n\n"
                    "استخدم القائمة الجانبية للوصول للنماذج."
                )
        else:
            QMessageBox.warning(self, "تحذير", "مكتبة القوالب غير متاحة")

    def show_search_dialog(self):
        """عرض نافذة البحث"""
        QMessageBox.information(
            self, "البحث عن الشحنات",
            "🔍 نافذة البحث المتقدم\n\n"
            "الميزات المتاحة:\n"
            "• البحث برقم الشحنة\n"
            "• البحث باسم العميل\n"
            "• البحث بالتاريخ\n"
            "• البحث بالحالة\n\n"
            "🚧 قيد التطوير"
        )

    def show_tracking_dialog(self):
        """عرض نافذة التتبع"""
        QMessageBox.information(
            self, "تتبع الشحنات",
            "📊 نظام تتبع الشحنات\n\n"
            "الميزات المتاحة:\n"
            "• تتبع في الوقت الفعلي\n"
            "• خريطة المسار\n"
            "• تحديثات الحالة\n"
            "• إشعارات تلقائية\n\n"
            "🚧 قيد التطوير"
        )

    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(
            self, "حول البرنامج",
            "🚢 SHIPMENT ERP v2.0\n"
            "نظام إدارة الشحنات المتقدم\n\n"
            "الواجهة الرئيسية المحدثة\n"
            "مع مكتبة القوالب المتكاملة\n\n"
            "المطور: فريق SHIPMENT Solutions\n"
            "تاريخ الإصدار: 2024\n\n"
            "جميع الحقوق محفوظة ©"
        )

    def show_welcome_message(self):
        """عرض رسالة ترحيب"""
        QMessageBox.information(
            self, "مرحباً",
            "🎉 مرحباً بك في نظام إدارة الشحنات المتقدم!\n\n"
            "الواجهة الرئيسية الجديدة تحتوي على:\n"
            "📋 نماذج تفاعلية متطورة\n"
            "🏛️ مكتبة قوالب شاملة\n"
            "🌳 قوائم تنقل منظمة\n"
            "⚙️ إعدادات متقدمة\n\n"
            "ابدأ بالنقر على 'إنشاء شحنة جديدة' من القائمة الجانبية!"
        )

    def on_form_created(self, template_name, template_instance):
        """معالج إنشاء نموذج جديد"""
        self.active_forms.append(template_instance)
        self.statusBar().showMessage(f"تم إنشاء نموذج: {template_name}", 3000)

    def on_form_closed(self, template_name):
        """معالج إغلاق نموذج"""
        # تحديث قائمة النماذج النشطة
        self.active_forms = [f for f in self.active_forms if not f.isHidden()]
        self.statusBar().showMessage(f"تم إغلاق نموذج: {template_name}", 3000)

    def closeEvent(self, event):
        """معالج إغلاق التطبيق"""
        if self.active_forms:
            reply = QMessageBox.question(
                self, "تأكيد الإغلاق",
                f"يوجد {len(self.active_forms)} نموذج نشط.\n"
                "هل تريد إغلاق التطبيق؟\n"
                "سيتم إغلاق جميع النماذج النشطة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.No:
                event.ignore()
                return

        # إيقاف المؤقت
        if hasattr(self, 'timer'):
            self.timer.stop()

        # إغلاق جميع النماذج النشطة
        for form in self.active_forms:
            form.close()

        event.accept()

def main():
    """الدالة الرئيسية"""
    print("🚀 تشغيل نظام إدارة الشحنات المتقدم")
    print("=" * 50)
    print("📋 الواجهة الرئيسية الجديدة")
    print("   • واجهة تجريبية محدثة")
    print("   • مكتبة قوالب متكاملة")
    print("   • قوائم تنقل تفاعلية")
    print("   • نماذج متطورة")
    print("=" * 50)

    app = QApplication(sys.argv)

    # إعداد الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)

    # إنشاء النافذة الرئيسية
    window = MainWindowPrototype()
    window.show()

    print("✅ تم تشغيل الواجهة الرئيسية بنجاح!")
    print("🎯 الواجهة جاهزة للاستخدام")

    # تشغيل التطبيق
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
