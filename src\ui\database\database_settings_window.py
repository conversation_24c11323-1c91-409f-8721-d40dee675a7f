# -*- coding: utf-8 -*-
"""
نافذة إعدادات قاعدة البيانات المتقدمة
Advanced Database Settings Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTabWidget, QGroupBox, QGridLayout, QLabel,
                               QPushButton, QLineEdit, QSpinBox, QComboBox,
                               QCheckBox, QTextEdit, QProgressBar, QMessageBox,
                               QFileDialog, QTableWidget, QTableWidgetItem,
                               QHeaderView, QAbstractItemView, QFrame,
                               QScrollArea, QSlider, QDoubleSpinBox,
                               QDateTimeEdit, QToolBar, QStatusBar)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, QDateTime
from PySide6.QtGui import QFont, QIcon, QPixmap, QColor, QPalette

import sqlite3
import json
import os
import shutil
import zipfile
from pathlib import Path
from datetime import datetime, timedelta
import psutil
import threading
import time

from ...utils.arabic_support import reshape_arabic_text

class DatabaseMaintenanceThread(QThread):
    """خيط صيانة قاعدة البيانات"""
    progress_updated = Signal(int)
    status_updated = Signal(str)
    finished_signal = Signal(bool, str)
    
    def __init__(self, operation, db_path, options=None):
        super().__init__()
        self.operation = operation
        self.db_path = db_path
        self.options = options or {}
        
    def run(self):
        try:
            if self.operation == "vacuum":
                self.vacuum_database()
            elif self.operation == "analyze":
                self.analyze_database()
            elif self.operation == "integrity_check":
                self.integrity_check()
            elif self.operation == "backup":
                self.backup_database()
            elif self.operation == "optimize":
                self.optimize_database()
                
            self.finished_signal.emit(True, "تمت العملية بنجاح")
            
        except Exception as e:
            self.finished_signal.emit(False, str(e))
            
    def vacuum_database(self):
        """ضغط قاعدة البيانات"""
        self.status_updated.emit("جاري ضغط قاعدة البيانات...")
        conn = sqlite3.connect(self.db_path)
        
        # الحصول على حجم قاعدة البيانات قبل الضغط
        initial_size = os.path.getsize(self.db_path)
        self.progress_updated.emit(25)
        
        # تنفيذ VACUUM
        conn.execute("VACUUM")
        self.progress_updated.emit(75)
        
        conn.close()
        
        # الحصول على حجم قاعدة البيانات بعد الضغط
        final_size = os.path.getsize(self.db_path)
        saved_space = initial_size - final_size
        
        self.status_updated.emit(f"تم توفير {saved_space / 1024 / 1024:.2f} ميجابايت")
        self.progress_updated.emit(100)
        
    def analyze_database(self):
        """تحليل قاعدة البيانات"""
        self.status_updated.emit("جاري تحليل قاعدة البيانات...")
        conn = sqlite3.connect(self.db_path)
        
        # تحليل جميع الجداول
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        total_tables = len(tables)
        for i, (table_name,) in enumerate(tables):
            self.status_updated.emit(f"تحليل جدول: {table_name}")
            cursor.execute(f"ANALYZE {table_name}")
            progress = int((i + 1) / total_tables * 100)
            self.progress_updated.emit(progress)
            
        conn.close()
        
    def integrity_check(self):
        """فحص سلامة قاعدة البيانات"""
        self.status_updated.emit("فحص سلامة قاعدة البيانات...")
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # فحص سلامة قاعدة البيانات
        self.progress_updated.emit(25)
        cursor.execute("PRAGMA integrity_check")
        result = cursor.fetchone()
        
        self.progress_updated.emit(50)
        
        # فحص المفاتيح الخارجية
        cursor.execute("PRAGMA foreign_key_check")
        foreign_key_errors = cursor.fetchall()
        
        self.progress_updated.emit(75)
        
        if result[0] == "ok" and not foreign_key_errors:
            self.status_updated.emit("قاعدة البيانات سليمة")
        else:
            self.status_updated.emit("تم العثور على أخطاء في قاعدة البيانات")
            
        self.progress_updated.emit(100)
        conn.close()
        
    def backup_database(self):
        """نسخ احتياطي لقاعدة البيانات"""
        backup_path = self.options.get('backup_path')
        if not backup_path:
            raise Exception("مسار النسخ الاحتياطي غير محدد")
            
        self.status_updated.emit("إنشاء نسخة احتياطية...")
        
        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        backup_dir = Path(backup_path).parent
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        self.progress_updated.emit(25)
        
        # نسخ ملف قاعدة البيانات
        shutil.copy2(self.db_path, backup_path)
        
        self.progress_updated.emit(75)
        
        # ضغط النسخة الاحتياطية إذا كان مطلوباً
        if self.options.get('compress', False):
            zip_path = backup_path + '.zip'
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(backup_path, Path(backup_path).name)
            os.remove(backup_path)
            
        self.status_updated.emit("تم إنشاء النسخة الاحتياطية بنجاح")
        self.progress_updated.emit(100)
        
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        self.status_updated.emit("تحسين قاعدة البيانات...")
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # تحسين إعدادات قاعدة البيانات
        optimizations = [
            "PRAGMA optimize",
            "PRAGMA wal_checkpoint(TRUNCATE)",
            "REINDEX"
        ]
        
        total_steps = len(optimizations)
        for i, optimization in enumerate(optimizations):
            self.status_updated.emit(f"تنفيذ: {optimization}")
            cursor.execute(optimization)
            progress = int((i + 1) / total_steps * 100)
            self.progress_updated.emit(progress)
            
        conn.close()

class DatabaseSettingsWindow(QMainWindow):
    """نافذة إعدادات قاعدة البيانات المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إعدادات قاعدة البيانات المتقدمة - ProShipment")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # متغيرات النافذة
        self.db_path = Path("data/proshipment.db")
        self.maintenance_thread = None
        self.auto_backup_timer = QTimer()
        self.monitoring_timer = QTimer()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        
        # تحميل الإعدادات
        self.load_settings()
        self.start_monitoring()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # عنوان النافذة
        self.create_header_section(layout)
        
        # التبويبات الرئيسية
        self.tabs = QTabWidget()
        
        # تبويب المعلومات العامة
        general_tab = self.create_general_info_tab()
        self.tabs.addTab(general_tab, "المعلومات العامة")
        
        # تبويب الصيانة والتحسين
        maintenance_tab = self.create_maintenance_tab()
        self.tabs.addTab(maintenance_tab, "الصيانة والتحسين")
        
        # تبويب النسخ الاحتياطية
        backup_tab = self.create_backup_tab()
        self.tabs.addTab(backup_tab, "النسخ الاحتياطية")
        
        # تبويب الأداء والمراقبة
        performance_tab = self.create_performance_tab()
        self.tabs.addTab(performance_tab, "الأداء والمراقبة")
        
        # تبويب الأمان والحماية
        security_tab = self.create_security_tab()
        self.tabs.addTab(security_tab, "الأمان والحماية")
        
        # تبويب الإعدادات المتقدمة
        advanced_tab = self.create_advanced_tab()
        self.tabs.addTab(advanced_tab, "الإعدادات المتقدمة")
        
        layout.addWidget(self.tabs)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1e40af, stop:1 #3b82f6);
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة قاعدة البيانات
        icon_label = QLabel("🗄️")
        icon_label.setStyleSheet("font-size: 36px; color: white;")
        header_layout.addWidget(icon_label)
        
        # معلومات العنوان
        info_layout = QVBoxLayout()
        
        title_label = QLabel("إعدادات قاعدة البيانات المتقدمة")
        title_label.setFont(QFont("Arial", 20, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        
        subtitle_label = QLabel("إدارة شاملة لقاعدة البيانات والأداء والأمان")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setStyleSheet("color: #e0e7ff;")
        
        info_layout.addWidget(title_label)
        info_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        # معلومات سريعة عن قاعدة البيانات
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        stats_layout = QGridLayout(stats_frame)
        
        self.db_size_label = QLabel("حجم قاعدة البيانات\n0 MB")
        self.db_tables_label = QLabel("عدد الجداول\n0")
        self.db_status_label = QLabel("حالة الاتصال\nمتصل")
        self.last_backup_label = QLabel("آخر نسخة احتياطية\nغير محدد")
        
        for i, label in enumerate([self.db_size_label, self.db_tables_label, 
                                 self.db_status_label, self.last_backup_label]):
            label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    text-align: center;
                    font-size: 11px;
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(label, 0, i)
        
        header_layout.addWidget(stats_frame)
        
        layout.addWidget(header_frame)

    def create_general_info_tab(self):
        """إنشاء تبويب المعلومات العامة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # معلومات قاعدة البيانات
        db_info_group = QGroupBox("معلومات قاعدة البيانات")
        db_info_layout = QGridLayout(db_info_group)

        # مسار قاعدة البيانات
        db_info_layout.addWidget(QLabel("مسار قاعدة البيانات:"), 0, 0)
        self.db_path_label = QLabel(str(self.db_path))
        self.db_path_label.setStyleSheet("font-family: monospace; background-color: #f3f4f6; padding: 5px; border-radius: 3px;")
        db_info_layout.addWidget(self.db_path_label, 0, 1)

        # حجم قاعدة البيانات
        db_info_layout.addWidget(QLabel("حجم الملف:"), 1, 0)
        self.file_size_label = QLabel("0 MB")
        db_info_layout.addWidget(self.file_size_label, 1, 1)

        # تاريخ الإنشاء
        db_info_layout.addWidget(QLabel("تاريخ الإنشاء:"), 2, 0)
        self.creation_date_label = QLabel("-")
        db_info_layout.addWidget(self.creation_date_label, 2, 1)

        # تاريخ آخر تعديل
        db_info_layout.addWidget(QLabel("آخر تعديل:"), 3, 0)
        self.modification_date_label = QLabel("-")
        db_info_layout.addWidget(self.modification_date_label, 3, 1)

        # إصدار SQLite
        db_info_layout.addWidget(QLabel("إصدار SQLite:"), 4, 0)
        self.sqlite_version_label = QLabel("-")
        db_info_layout.addWidget(self.sqlite_version_label, 4, 1)

        layout.addWidget(db_info_group)

        # إحصائيات الجداول
        tables_group = QGroupBox("إحصائيات الجداول")
        tables_layout = QVBoxLayout(tables_group)

        self.tables_table = QTableWidget()
        self.setup_tables_table()
        tables_layout.addWidget(self.tables_table)

        layout.addWidget(tables_group)

        # أزرار الإجراءات السريعة
        actions_frame = QFrame()
        actions_layout = QHBoxLayout(actions_frame)

        refresh_btn = QPushButton("🔄 تحديث المعلومات")
        refresh_btn.clicked.connect(self.refresh_database_info)
        actions_layout.addWidget(refresh_btn)

        export_info_btn = QPushButton("📊 تصدير المعلومات")
        export_info_btn.clicked.connect(self.export_database_info)
        actions_layout.addWidget(export_info_btn)

        actions_layout.addStretch()
        layout.addWidget(actions_frame)

        return tab

    def create_maintenance_tab(self):
        """إنشاء تبويب الصيانة والتحسين"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # عمليات الصيانة
        maintenance_group = QGroupBox("عمليات الصيانة")
        maintenance_layout = QGridLayout(maintenance_group)

        # ضغط قاعدة البيانات
        vacuum_btn = QPushButton("🗜️ ضغط قاعدة البيانات (VACUUM)")
        vacuum_btn.setToolTip("إزالة المساحات الفارغة وتحسين الأداء")
        vacuum_btn.clicked.connect(lambda: self.start_maintenance_operation("vacuum"))
        maintenance_layout.addWidget(vacuum_btn, 0, 0)

        # تحليل قاعدة البيانات
        analyze_btn = QPushButton("📈 تحليل قاعدة البيانات (ANALYZE)")
        analyze_btn.setToolTip("تحديث إحصائيات الجداول لتحسين الاستعلامات")
        analyze_btn.clicked.connect(lambda: self.start_maintenance_operation("analyze"))
        maintenance_layout.addWidget(analyze_btn, 0, 1)

        # فحص سلامة قاعدة البيانات
        integrity_btn = QPushButton("🔍 فحص السلامة")
        integrity_btn.setToolTip("فحص سلامة البيانات والهيكل")
        integrity_btn.clicked.connect(lambda: self.start_maintenance_operation("integrity_check"))
        maintenance_layout.addWidget(integrity_btn, 1, 0)

        # تحسين شامل
        optimize_btn = QPushButton("⚡ تحسين شامل")
        optimize_btn.setToolTip("تنفيذ جميع عمليات التحسين")
        optimize_btn.clicked.connect(lambda: self.start_maintenance_operation("optimize"))
        maintenance_layout.addWidget(optimize_btn, 1, 1)

        layout.addWidget(maintenance_group)

        # شريط التقدم والحالة
        progress_group = QGroupBox("حالة العملية")
        progress_layout = QVBoxLayout(progress_group)

        self.maintenance_progress = QProgressBar()
        self.maintenance_progress.setVisible(False)
        progress_layout.addWidget(self.maintenance_progress)

        self.maintenance_status = QLabel("جاهز")
        self.maintenance_status.setStyleSheet("color: #059669; font-weight: bold;")
        progress_layout.addWidget(self.maintenance_status)

        layout.addWidget(progress_group)

        # جدولة الصيانة التلقائية
        schedule_group = QGroupBox("جدولة الصيانة التلقائية")
        schedule_layout = QGridLayout(schedule_group)

        self.auto_vacuum_checkbox = QCheckBox("ضغط تلقائي أسبوعي")
        schedule_layout.addWidget(self.auto_vacuum_checkbox, 0, 0)

        self.auto_analyze_checkbox = QCheckBox("تحليل تلقائي يومي")
        schedule_layout.addWidget(self.auto_analyze_checkbox, 0, 1)

        schedule_layout.addWidget(QLabel("وقت التنفيذ:"), 1, 0)
        self.maintenance_time = QDateTimeEdit()
        self.maintenance_time.setDisplayFormat("hh:mm")
        self.maintenance_time.setTime(QDateTime.currentDateTime().time())
        schedule_layout.addWidget(self.maintenance_time, 1, 1)

        layout.addWidget(schedule_group)
        layout.addStretch()

        return tab

    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات النسخ الاحتياطية
        backup_settings_group = QGroupBox("إعدادات النسخ الاحتياطية")
        backup_settings_layout = QGridLayout(backup_settings_group)

        # مجلد النسخ الاحتياطية
        backup_settings_layout.addWidget(QLabel("مجلد النسخ الاحتياطية:"), 0, 0)
        self.backup_folder_input = QLineEdit()
        self.backup_folder_input.setText("backups/")
        backup_settings_layout.addWidget(self.backup_folder_input, 0, 1)

        browse_btn = QPushButton("📁 تصفح")
        browse_btn.clicked.connect(self.browse_backup_folder)
        backup_settings_layout.addWidget(browse_btn, 0, 2)

        # تكرار النسخ الاحتياطية
        backup_settings_layout.addWidget(QLabel("تكرار النسخ:"), 1, 0)
        self.backup_frequency = QComboBox()
        self.backup_frequency.addItems(["يدوي", "يومي", "أسبوعي", "شهري"])
        backup_settings_layout.addWidget(self.backup_frequency, 1, 1)

        # عدد النسخ المحفوظة
        backup_settings_layout.addWidget(QLabel("عدد النسخ المحفوظة:"), 2, 0)
        self.backup_retention = QSpinBox()
        self.backup_retention.setRange(1, 100)
        self.backup_retention.setValue(10)
        backup_settings_layout.addWidget(self.backup_retention, 2, 1)

        # ضغط النسخ الاحتياطية
        self.compress_backup_checkbox = QCheckBox("ضغط النسخ الاحتياطية")
        self.compress_backup_checkbox.setChecked(True)
        backup_settings_layout.addWidget(self.compress_backup_checkbox, 3, 0, 1, 2)

        layout.addWidget(backup_settings_group)

        # عمليات النسخ الاحتياطي
        backup_actions_group = QGroupBox("عمليات النسخ الاحتياطي")
        backup_actions_layout = QHBoxLayout(backup_actions_group)

        create_backup_btn = QPushButton("💾 إنشاء نسخة احتياطية")
        create_backup_btn.clicked.connect(self.create_manual_backup)
        backup_actions_layout.addWidget(create_backup_btn)

        restore_backup_btn = QPushButton("📥 استعادة نسخة احتياطية")
        restore_backup_btn.clicked.connect(self.restore_backup)
        backup_actions_layout.addWidget(restore_backup_btn)

        verify_backup_btn = QPushButton("✅ التحقق من النسخ")
        verify_backup_btn.clicked.connect(self.verify_backups)
        backup_actions_layout.addWidget(verify_backup_btn)

        layout.addWidget(backup_actions_group)

        # قائمة النسخ الاحتياطية
        backups_list_group = QGroupBox("النسخ الاحتياطية المتاحة")
        backups_list_layout = QVBoxLayout(backups_list_group)

        self.backups_table = QTableWidget()
        self.setup_backups_table()
        backups_list_layout.addWidget(self.backups_table)

        layout.addWidget(backups_list_group)

        return tab

    def create_performance_tab(self):
        """إنشاء تبويب الأداء والمراقبة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # مراقبة الأداء في الوقت الفعلي
        monitoring_group = QGroupBox("مراقبة الأداء")
        monitoring_layout = QGridLayout(monitoring_group)

        # استخدام المعالج
        monitoring_layout.addWidget(QLabel("استخدام المعالج:"), 0, 0)
        self.cpu_usage_label = QLabel("0%")
        self.cpu_usage_bar = QProgressBar()
        monitoring_layout.addWidget(self.cpu_usage_label, 0, 1)
        monitoring_layout.addWidget(self.cpu_usage_bar, 0, 2)

        # استخدام الذاكرة
        monitoring_layout.addWidget(QLabel("استخدام الذاكرة:"), 1, 0)
        self.memory_usage_label = QLabel("0 MB")
        self.memory_usage_bar = QProgressBar()
        monitoring_layout.addWidget(self.memory_usage_label, 1, 1)
        monitoring_layout.addWidget(self.memory_usage_bar, 1, 2)

        # عدد الاتصالات النشطة
        monitoring_layout.addWidget(QLabel("الاتصالات النشطة:"), 2, 0)
        self.active_connections_label = QLabel("0")
        monitoring_layout.addWidget(self.active_connections_label, 2, 1)

        # متوسط وقت الاستعلام
        monitoring_layout.addWidget(QLabel("متوسط وقت الاستعلام:"), 3, 0)
        self.avg_query_time_label = QLabel("0 ms")
        monitoring_layout.addWidget(self.avg_query_time_label, 3, 1)

        layout.addWidget(monitoring_group)

        # إعدادات الأداء
        performance_settings_group = QGroupBox("إعدادات الأداء")
        performance_settings_layout = QGridLayout(performance_settings_group)

        # حجم ذاكرة التخزين المؤقت
        performance_settings_layout.addWidget(QLabel("حجم Cache (KB):"), 0, 0)
        self.cache_size_spinbox = QSpinBox()
        self.cache_size_spinbox.setRange(1024, 1048576)  # 1MB to 1GB
        self.cache_size_spinbox.setValue(8192)  # 8MB default
        performance_settings_layout.addWidget(self.cache_size_spinbox, 0, 1)

        # مهلة الاستعلام
        performance_settings_layout.addWidget(QLabel("مهلة الاستعلام (ثانية):"), 1, 0)
        self.query_timeout_spinbox = QSpinBox()
        self.query_timeout_spinbox.setRange(1, 300)
        self.query_timeout_spinbox.setValue(30)
        performance_settings_layout.addWidget(self.query_timeout_spinbox, 1, 1)

        # عدد الاتصالات المتزامنة
        performance_settings_layout.addWidget(QLabel("الاتصالات المتزامنة:"), 2, 0)
        self.max_connections_spinbox = QSpinBox()
        self.max_connections_spinbox.setRange(1, 1000)
        self.max_connections_spinbox.setValue(100)
        performance_settings_layout.addWidget(self.max_connections_spinbox, 2, 1)

        # تفعيل WAL mode
        self.wal_mode_checkbox = QCheckBox("تفعيل WAL Mode (Write-Ahead Logging)")
        self.wal_mode_checkbox.setToolTip("يحسن الأداء في البيئات متعددة المستخدمين")
        performance_settings_layout.addWidget(self.wal_mode_checkbox, 3, 0, 1, 2)

        layout.addWidget(performance_settings_group)

        # إحصائيات الاستعلامات
        query_stats_group = QGroupBox("إحصائيات الاستعلامات")
        query_stats_layout = QVBoxLayout(query_stats_group)

        self.query_stats_table = QTableWidget()
        self.setup_query_stats_table()
        query_stats_layout.addWidget(self.query_stats_table)

        layout.addWidget(query_stats_group)

        return tab

    def create_security_tab(self):
        """إنشاء تبويب الأمان والحماية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات الأمان
        security_settings_group = QGroupBox("إعدادات الأمان")
        security_settings_layout = QGridLayout(security_settings_group)

        # تشفير قاعدة البيانات
        self.encryption_checkbox = QCheckBox("تفعيل تشفير قاعدة البيانات")
        security_settings_layout.addWidget(self.encryption_checkbox, 0, 0, 1, 2)

        # كلمة مرور قاعدة البيانات
        security_settings_layout.addWidget(QLabel("كلمة مرور قاعدة البيانات:"), 1, 0)
        self.db_password_input = QLineEdit()
        self.db_password_input.setEchoMode(QLineEdit.Password)
        self.db_password_input.setEnabled(False)
        security_settings_layout.addWidget(self.db_password_input, 1, 1)

        # تفعيل المراجعة
        self.audit_log_checkbox = QCheckBox("تفعيل سجل المراجعة")
        self.audit_log_checkbox.setChecked(True)
        security_settings_layout.addWidget(self.audit_log_checkbox, 2, 0, 1, 2)

        # تسجيل الاستعلامات الحساسة
        self.log_sensitive_queries_checkbox = QCheckBox("تسجيل الاستعلامات الحساسة")
        security_settings_layout.addWidget(self.log_sensitive_queries_checkbox, 3, 0, 1, 2)

        layout.addWidget(security_settings_group)

        # صلاحيات المستخدمين
        permissions_group = QGroupBox("صلاحيات المستخدمين")
        permissions_layout = QVBoxLayout(permissions_group)

        self.permissions_table = QTableWidget()
        self.setup_permissions_table()
        permissions_layout.addWidget(self.permissions_table)

        layout.addWidget(permissions_group)

        # سجل الأمان
        security_log_group = QGroupBox("سجل الأمان")
        security_log_layout = QVBoxLayout(security_log_group)

        self.security_log_text = QTextEdit()
        self.security_log_text.setReadOnly(True)
        self.security_log_text.setMaximumHeight(150)
        security_log_layout.addWidget(self.security_log_text)

        layout.addWidget(security_log_group)

        return tab

    def create_advanced_tab(self):
        """إنشاء تبويب الإعدادات المتقدمة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات SQLite المتقدمة
        sqlite_settings_group = QGroupBox("إعدادات SQLite المتقدمة")
        sqlite_settings_layout = QGridLayout(sqlite_settings_group)

        # Page Size
        sqlite_settings_layout.addWidget(QLabel("حجم الصفحة (bytes):"), 0, 0)
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["512", "1024", "2048", "4096", "8192", "16384", "32768", "65536"])
        self.page_size_combo.setCurrentText("4096")
        sqlite_settings_layout.addWidget(self.page_size_combo, 0, 1)

        # Journal Mode
        sqlite_settings_layout.addWidget(QLabel("وضع Journal:"), 1, 0)
        self.journal_mode_combo = QComboBox()
        self.journal_mode_combo.addItems(["DELETE", "TRUNCATE", "PERSIST", "MEMORY", "WAL", "OFF"])
        self.journal_mode_combo.setCurrentText("WAL")
        sqlite_settings_layout.addWidget(self.journal_mode_combo, 1, 1)

        # Synchronous Mode
        sqlite_settings_layout.addWidget(QLabel("وضع المزامنة:"), 2, 0)
        self.sync_mode_combo = QComboBox()
        self.sync_mode_combo.addItems(["OFF", "NORMAL", "FULL", "EXTRA"])
        self.sync_mode_combo.setCurrentText("NORMAL")
        sqlite_settings_layout.addWidget(self.sync_mode_combo, 2, 1)

        # Temp Store
        sqlite_settings_layout.addWidget(QLabel("مخزن مؤقت:"), 3, 0)
        self.temp_store_combo = QComboBox()
        self.temp_store_combo.addItems(["DEFAULT", "FILE", "MEMORY"])
        self.temp_store_combo.setCurrentText("MEMORY")
        sqlite_settings_layout.addWidget(self.temp_store_combo, 3, 1)

        layout.addWidget(sqlite_settings_group)

        # إعدادات الاتصال
        connection_settings_group = QGroupBox("إعدادات الاتصال")
        connection_settings_layout = QGridLayout(connection_settings_group)

        # Connection Pool Size
        connection_settings_layout.addWidget(QLabel("حجم مجموعة الاتصالات:"), 0, 0)
        self.connection_pool_size = QSpinBox()
        self.connection_pool_size.setRange(1, 100)
        self.connection_pool_size.setValue(10)
        connection_settings_layout.addWidget(self.connection_pool_size, 0, 1)

        # Connection Timeout
        connection_settings_layout.addWidget(QLabel("مهلة الاتصال (ثانية):"), 1, 0)
        self.connection_timeout = QSpinBox()
        self.connection_timeout.setRange(1, 300)
        self.connection_timeout.setValue(30)
        connection_settings_layout.addWidget(self.connection_timeout, 1, 1)

        # Busy Timeout
        connection_settings_layout.addWidget(QLabel("مهلة الانتظار (ms):"), 2, 0)
        self.busy_timeout = QSpinBox()
        self.busy_timeout.setRange(100, 30000)
        self.busy_timeout.setValue(5000)
        connection_settings_layout.addWidget(self.busy_timeout, 2, 1)

        layout.addWidget(connection_settings_group)

        # أزرار التطبيق
        apply_buttons_frame = QFrame()
        apply_buttons_layout = QHBoxLayout(apply_buttons_frame)

        apply_settings_btn = QPushButton("✅ تطبيق الإعدادات")
        apply_settings_btn.clicked.connect(self.apply_advanced_settings)
        apply_buttons_layout.addWidget(apply_settings_btn)

        reset_settings_btn = QPushButton("🔄 إعادة تعيين")
        reset_settings_btn.clicked.connect(self.reset_advanced_settings)
        apply_buttons_layout.addWidget(reset_settings_btn)

        export_config_btn = QPushButton("📤 تصدير الإعدادات")
        export_config_btn.clicked.connect(self.export_configuration)
        apply_buttons_layout.addWidget(export_config_btn)

        import_config_btn = QPushButton("📥 استيراد الإعدادات")
        import_config_btn.clicked.connect(self.import_configuration)
        apply_buttons_layout.addWidget(import_config_btn)

        apply_buttons_layout.addStretch()
        layout.addWidget(apply_buttons_frame)

        layout.addStretch()
        return tab

    def setup_tables_table(self):
        """إعداد جدول الجداول"""
        headers = ["اسم الجدول", "عدد السجلات", "حجم البيانات", "آخر تحديث"]
        self.tables_table.setColumnCount(len(headers))
        self.tables_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.tables_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.tables_table.setAlternatingRowColors(True)
        self.tables_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.tables_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_backups_table(self):
        """إعداد جدول النسخ الاحتياطية"""
        headers = ["اسم الملف", "التاريخ", "الحجم", "النوع", "إجراءات"]
        self.backups_table.setColumnCount(len(headers))
        self.backups_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.backups_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.backups_table.setAlternatingRowColors(True)
        self.backups_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.backups_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_query_stats_table(self):
        """إعداد جدول إحصائيات الاستعلامات"""
        headers = ["نوع الاستعلام", "العدد", "متوسط الوقت", "أطول وقت", "آخر تنفيذ"]
        self.query_stats_table.setColumnCount(len(headers))
        self.query_stats_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.query_stats_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.query_stats_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.query_stats_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_permissions_table(self):
        """إعداد جدول الصلاحيات"""
        headers = ["المستخدم", "القراءة", "الكتابة", "الحذف", "الإدارة"]
        self.permissions_table.setColumnCount(len(headers))
        self.permissions_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.permissions_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.permissions_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.permissions_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        for i in range(1, len(headers)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # تحديث المعلومات
        refresh_action = toolbar.addAction("🔄 تحديث")
        refresh_action.setToolTip("تحديث جميع المعلومات")
        refresh_action.triggered.connect(self.refresh_all_data)

        toolbar.addSeparator()

        # نسخة احتياطية سريعة
        quick_backup_action = toolbar.addAction("💾 نسخة احتياطية")
        quick_backup_action.setToolTip("إنشاء نسخة احتياطية سريعة")
        quick_backup_action.triggered.connect(self.create_quick_backup)

        # تحسين سريع
        quick_optimize_action = toolbar.addAction("⚡ تحسين")
        quick_optimize_action.setToolTip("تحسين سريع لقاعدة البيانات")
        quick_optimize_action.triggered.connect(self.quick_optimize)

        toolbar.addSeparator()

        # إعدادات
        settings_action = toolbar.addAction("⚙️ حفظ الإعدادات")
        settings_action.setToolTip("حفظ جميع الإعدادات")
        settings_action.triggered.connect(self.save_all_settings)

    def setup_statusbar(self):
        """إعداد شريط الحالة"""
        self.statusbar = self.statusBar()

        # حالة قاعدة البيانات
        self.db_connection_status = QLabel("متصل")
        self.db_connection_status.setStyleSheet("color: green; font-weight: bold;")
        self.statusbar.addWidget(self.db_connection_status)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # آخر عملية صيانة
        self.last_maintenance_label = QLabel("آخر صيانة: غير محدد")
        self.statusbar.addPermanentWidget(self.last_maintenance_label)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # شريط التقدم العام
        self.general_progress = QProgressBar()
        self.general_progress.setVisible(False)
        self.general_progress.setMaximumWidth(200)
        self.statusbar.addPermanentWidget(self.general_progress)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # ربط تغيير التشفير بتفعيل كلمة المرور
        self.encryption_checkbox.toggled.connect(
            lambda checked: self.db_password_input.setEnabled(checked)
        )

        # ربط تغيير تكرار النسخ الاحتياطية
        self.backup_frequency.currentTextChanged.connect(self.update_backup_schedule)

        # تحديث دوري للمراقبة
        self.monitoring_timer.timeout.connect(self.update_performance_monitoring)

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            settings_file = Path("config/database_settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # تطبيق الإعدادات على الواجهة
                self.apply_settings_to_ui(settings)

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def start_monitoring(self):
        """بدء مراقبة الأداء"""
        self.monitoring_timer.start(2000)  # كل ثانيتين
        self.refresh_database_info()

    def refresh_database_info(self):
        """تحديث معلومات قاعدة البيانات"""
        try:
            if self.db_path.exists():
                # حجم الملف
                file_size = self.db_path.stat().st_size
                size_mb = file_size / (1024 * 1024)
                self.file_size_label.setText(f"{size_mb:.2f} MB")
                self.db_size_label.setText(f"حجم قاعدة البيانات\n{size_mb:.1f} MB")

                # تواريخ الملف
                creation_time = datetime.fromtimestamp(self.db_path.stat().st_ctime)
                modification_time = datetime.fromtimestamp(self.db_path.stat().st_mtime)

                self.creation_date_label.setText(creation_time.strftime("%Y-%m-%d %H:%M"))
                self.modification_date_label.setText(modification_time.strftime("%Y-%m-%d %H:%M"))

                # معلومات SQLite
                conn = sqlite3.connect(str(self.db_path))
                cursor = conn.cursor()

                # إصدار SQLite
                cursor.execute("SELECT sqlite_version()")
                sqlite_version = cursor.fetchone()[0]
                self.sqlite_version_label.setText(sqlite_version)

                # عدد الجداول
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                tables_count = cursor.fetchone()[0]
                self.db_tables_label.setText(f"عدد الجداول\n{tables_count}")

                # تحديث جدول الجداول
                self.update_tables_info(cursor)

                conn.close()

        except Exception as e:
            print(f"خطأ في تحديث معلومات قاعدة البيانات: {e}")
            self.db_connection_status.setText("خطأ في الاتصال")
            self.db_connection_status.setStyleSheet("color: red; font-weight: bold;")

    def update_tables_info(self, cursor):
        """تحديث معلومات الجداول"""
        try:
            # الحصول على قائمة الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = cursor.fetchall()

            self.tables_table.setRowCount(len(tables))

            for row, (table_name,) in enumerate(tables):
                # اسم الجدول
                self.tables_table.setItem(row, 0, QTableWidgetItem(table_name))

                # عدد السجلات
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                record_count = cursor.fetchone()[0]
                self.tables_table.setItem(row, 1, QTableWidgetItem(str(record_count)))

                # حجم البيانات (تقديري)
                cursor.execute(f"SELECT SUM(LENGTH(sql)) FROM sqlite_master WHERE tbl_name='{table_name}'")
                size_estimate = cursor.fetchone()[0] or 0
                size_kb = size_estimate / 1024
                self.tables_table.setItem(row, 2, QTableWidgetItem(f"{size_kb:.2f} KB"))

                # آخر تحديث (من معلومات الملف)
                modification_time = datetime.fromtimestamp(self.db_path.stat().st_mtime)
                self.tables_table.setItem(row, 3, QTableWidgetItem(modification_time.strftime("%Y-%m-%d")))

        except Exception as e:
            print(f"خطأ في تحديث معلومات الجداول: {e}")

    def update_performance_monitoring(self):
        """تحديث مراقبة الأداء"""
        try:
            # استخدام المعالج
            cpu_percent = psutil.cpu_percent()
            self.cpu_usage_label.setText(f"{cpu_percent:.1f}%")
            self.cpu_usage_bar.setValue(int(cpu_percent))

            # استخدام الذاكرة
            memory = psutil.virtual_memory()
            memory_mb = memory.used / (1024 * 1024)
            memory_percent = memory.percent
            self.memory_usage_label.setText(f"{memory_mb:.0f} MB")
            self.memory_usage_bar.setValue(int(memory_percent))

            # محاكاة بيانات الاتصالات والاستعلامات
            self.active_connections_label.setText("1")
            self.avg_query_time_label.setText("15 ms")

        except Exception as e:
            print(f"خطأ في مراقبة الأداء: {e}")

    def start_maintenance_operation(self, operation):
        """بدء عملية صيانة"""
        if self.maintenance_thread and self.maintenance_thread.isRunning():
            QMessageBox.warning(self, "تحذير", "عملية صيانة أخرى قيد التنفيذ")
            return

        self.maintenance_progress.setVisible(True)
        self.maintenance_progress.setValue(0)

        # إنشاء خيط الصيانة
        self.maintenance_thread = DatabaseMaintenanceThread(operation, str(self.db_path))
        self.maintenance_thread.progress_updated.connect(self.maintenance_progress.setValue)
        self.maintenance_thread.status_updated.connect(self.maintenance_status.setText)
        self.maintenance_thread.finished_signal.connect(self.on_maintenance_finished)

        self.maintenance_thread.start()

    def on_maintenance_finished(self, success, message):
        """معالج انتهاء عملية الصيانة"""
        self.maintenance_progress.setVisible(False)

        if success:
            self.maintenance_status.setText("تمت العملية بنجاح")
            self.maintenance_status.setStyleSheet("color: #059669; font-weight: bold;")
            QMessageBox.information(self, "نجح", message)

            # تحديث معلومات قاعدة البيانات
            self.refresh_database_info()

        else:
            self.maintenance_status.setText("فشلت العملية")
            self.maintenance_status.setStyleSheet("color: #ef4444; font-weight: bold;")
            QMessageBox.critical(self, "خطأ", f"فشلت العملية: {message}")

    def create_manual_backup(self):
        """إنشاء نسخة احتياطية يدوية"""
        backup_folder = Path(self.backup_folder_input.text())
        backup_folder.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"proshipment_backup_{timestamp}.db"
        backup_path = backup_folder / backup_filename

        options = {
            'backup_path': str(backup_path),
            'compress': self.compress_backup_checkbox.isChecked()
        }

        if self.maintenance_thread and self.maintenance_thread.isRunning():
            QMessageBox.warning(self, "تحذير", "عملية أخرى قيد التنفيذ")
            return

        self.maintenance_thread = DatabaseMaintenanceThread("backup", str(self.db_path), options)
        self.maintenance_thread.progress_updated.connect(self.general_progress.setValue)
        self.maintenance_thread.status_updated.connect(lambda msg: self.statusbar.showMessage(msg))
        self.maintenance_thread.finished_signal.connect(self.on_backup_finished)

        self.general_progress.setVisible(True)
        self.maintenance_thread.start()

    def on_backup_finished(self, success, message):
        """معالج انتهاء النسخ الاحتياطي"""
        self.general_progress.setVisible(False)

        if success:
            QMessageBox.information(self, "نجح", "تم إنشاء النسخة الاحتياطية بنجاح")
            self.refresh_backups_list()
        else:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية: {message}")

    def refresh_backups_list(self):
        """تحديث قائمة النسخ الاحتياطية"""
        try:
            backup_folder = Path(self.backup_folder_input.text())
            if not backup_folder.exists():
                return

            backup_files = list(backup_folder.glob("proshipment_backup_*.db*"))
            self.backups_table.setRowCount(len(backup_files))

            for row, backup_file in enumerate(backup_files):
                # اسم الملف
                self.backups_table.setItem(row, 0, QTableWidgetItem(backup_file.name))

                # التاريخ
                modification_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                self.backups_table.setItem(row, 1, QTableWidgetItem(modification_time.strftime("%Y-%m-%d %H:%M")))

                # الحجم
                file_size = backup_file.stat().st_size / (1024 * 1024)
                self.backups_table.setItem(row, 2, QTableWidgetItem(f"{file_size:.2f} MB"))

                # النوع
                file_type = "مضغوط" if backup_file.suffix == ".zip" else "عادي"
                self.backups_table.setItem(row, 3, QTableWidgetItem(file_type))

        except Exception as e:
            print(f"خطأ في تحديث قائمة النسخ الاحتياطية: {e}")

    # دوال مبسطة للوظائف الأخرى
    def browse_backup_folder(self):
        """تصفح مجلد النسخ الاحتياطية"""
        folder = QFileDialog.getExistingDirectory(self, "اختر مجلد النسخ الاحتياطية")
        if folder:
            self.backup_folder_input.setText(folder)

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        QMessageBox.information(self, "قريباً", "وظيفة استعادة النسخ الاحتياطية قيد التطوير")

    def verify_backups(self):
        """التحقق من النسخ الاحتياطية"""
        QMessageBox.information(self, "قريباً", "وظيفة التحقق من النسخ قيد التطوير")

    def apply_advanced_settings(self):
        """تطبيق الإعدادات المتقدمة"""
        QMessageBox.information(self, "قريباً", "تطبيق الإعدادات المتقدمة قيد التطوير")

    def reset_advanced_settings(self):
        """إعادة تعيين الإعدادات المتقدمة"""
        QMessageBox.information(self, "قريباً", "إعادة تعيين الإعدادات قيد التطوير")

    def export_configuration(self):
        """تصدير الإعدادات"""
        QMessageBox.information(self, "قريباً", "تصدير الإعدادات قيد التطوير")

    def import_configuration(self):
        """استيراد الإعدادات"""
        QMessageBox.information(self, "قريباً", "استيراد الإعدادات قيد التطوير")

    def export_database_info(self):
        """تصدير معلومات قاعدة البيانات"""
        QMessageBox.information(self, "قريباً", "تصدير معلومات قاعدة البيانات قيد التطوير")

    def update_backup_schedule(self):
        """تحديث جدولة النسخ الاحتياطية"""
        frequency = self.backup_frequency.currentText()
        if frequency != "يدوي":
            QMessageBox.information(self, "تنبيه", f"تم تعيين النسخ الاحتياطية {frequency}")

    def apply_settings_to_ui(self, settings):
        """تطبيق الإعدادات على الواجهة"""
        try:
            # تطبيق إعدادات النسخ الاحتياطية
            if 'backup_folder' in settings:
                self.backup_folder_input.setText(settings['backup_folder'])
            if 'backup_frequency' in settings:
                self.backup_frequency.setCurrentText(settings['backup_frequency'])

        except Exception as e:
            print(f"خطأ في تطبيق الإعدادات: {e}")

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        self.refresh_database_info()
        self.refresh_backups_list()

    def create_quick_backup(self):
        """نسخة احتياطية سريعة"""
        self.create_manual_backup()

    def quick_optimize(self):
        """تحسين سريع"""
        self.start_maintenance_operation("optimize")

    def save_all_settings(self):
        """حفظ جميع الإعدادات"""
        try:
            settings = {
                'backup_folder': self.backup_folder_input.text(),
                'backup_frequency': self.backup_frequency.currentText(),
                'backup_retention': self.backup_retention.value(),
                'compress_backup': self.compress_backup_checkbox.isChecked(),
                'cache_size': self.cache_size_spinbox.value(),
                'query_timeout': self.query_timeout_spinbox.value(),
                'max_connections': self.max_connections_spinbox.value(),
                'wal_mode': self.wal_mode_checkbox.isChecked(),
                'encryption': self.encryption_checkbox.isChecked(),
                'audit_log': self.audit_log_checkbox.isChecked(),
                'page_size': self.page_size_combo.currentText(),
                'journal_mode': self.journal_mode_combo.currentText(),
                'sync_mode': self.sync_mode_combo.currentText(),
                'temp_store': self.temp_store_combo.currentText()
            }

            # إنشاء مجلد الإعدادات
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)

            # حفظ الإعدادات
            with open(config_dir / "database_settings.json", 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الإعدادات: {e}")
