# -*- coding: utf-8 -*-
"""
تبويب ربط الموردين بمندوبي المشتريات
Supplier Representatives Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QComboBox, QCheckBox,
                               QTextEdit, QAbstractItemView, QSplitter, QDateEdit, QDoubleSpinBox)
from PySide6.QtCore import Qt, QDate, QTimer
from PySide6.QtGui import QFont, QKeySequence, QShortcut

from ...database.database_manager import DatabaseManager
from ...database.models import Supplier, PurchaseRepresentative, SupplierRepresentative, ShipmentCommission
from .advanced_supplier_search_dialog import AdvancedSupplierSearchDialog


class SupplierRepresentativesTab(QWidget):
    """تبويب ربط الموردين بمندوبي المشتريات"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_assignment_id = None
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        self.setup_ui()
        self.setup_shortcuts()
        self.load_data()
    
    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # اختصار F9 لفتح البحث المتقدم
        self.f9_shortcut = QShortcut(QKeySequence("F9"), self)
        self.f9_shortcut.activated.connect(self.open_advanced_search)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # إنشاء splitter للتحكم في الأحجام
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - نموذج الإدخال
        form_widget = self.create_form_section()
        form_widget.setMaximumWidth(450)
        form_widget.setMinimumWidth(400)
        
        # الجانب الأيمن - الجدول
        table_widget = self.create_table_section()
        
        splitter.addWidget(form_widget)
        splitter.addWidget(table_widget)
        splitter.setStretchFactor(0, 0)  # النموذج لا يتمدد
        splitter.setStretchFactor(1, 1)  # الجدول يتمدد
        
        main_layout.addWidget(splitter)
    
    def create_form_section(self):
        """إنشاء قسم النموذج"""
        form_group = QGroupBox("ربط مورد بمندوب مشتريات")
        form_layout = QVBoxLayout(form_group)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(15, 15, 15, 15)

        # نموذج الإدخال
        input_form = QFormLayout()
        input_form.setSpacing(10)

        # البحث عن المورد مع زر البحث المتقدم
        supplier_search_layout = QHBoxLayout()
        
        self.supplier_search = QLineEdit()
        self.supplier_search.setPlaceholderText("ابحث عن المورد... (F9 للبحث المتقدم)")
        self.supplier_search.textChanged.connect(self.on_supplier_search_changed)
        self.supplier_search.setMinimumHeight(35)
        
        # زر البحث المتقدم
        self.advanced_search_button = QPushButton("🔍")
        self.advanced_search_button.setToolTip("البحث المتقدم (F9)")
        self.advanced_search_button.clicked.connect(self.open_advanced_search)
        self.advanced_search_button.setMaximumWidth(40)
        self.advanced_search_button.setMinimumHeight(35)
        
        supplier_search_layout.addWidget(self.supplier_search)
        supplier_search_layout.addWidget(self.advanced_search_button)
        
        input_form.addRow("البحث عن المورد:", supplier_search_layout)

        # قائمة الموردين
        self.supplier_combo = QComboBox()
        self.supplier_combo.setMinimumHeight(35)
        input_form.addRow("المورد:", self.supplier_combo)

        # مندوب المشتريات
        self.representative_combo = QComboBox()
        self.representative_combo.setMinimumHeight(35)
        input_form.addRow("مندوب المشتريات:", self.representative_combo)

        # المندوب الرئيسي
        self.is_primary_check = QCheckBox("المندوب الرئيسي للمورد")
        input_form.addRow("", self.is_primary_check)

        # تاريخ التعيين
        self.assigned_date_edit = QDateEdit()
        self.assigned_date_edit.setDate(QDate.currentDate())
        self.assigned_date_edit.setCalendarPopup(True)
        self.assigned_date_edit.setMinimumHeight(35)
        input_form.addRow("تاريخ التعيين:", self.assigned_date_edit)

        # الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات حول تعيين المندوب للمورد...")
        input_form.addRow("الملاحظات:", self.notes_edit)

        form_layout.addLayout(input_form)

        # قسم طريقة احتساب العمولة للشحنات
        commission_group = QGroupBox("طريقة احتساب العمولة للشحنات")
        commission_layout = QVBoxLayout(commission_group)
        commission_layout.setSpacing(10)

        # نوع الاحتساب
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("نوع الاحتساب:"))
        self.calculation_type_combo = QComboBox()
        self.calculation_type_combo.addItems(["حسب الكمية", "حسب قيمة البضاعة"])
        self.calculation_type_combo.setMinimumHeight(30)
        self.calculation_type_combo.currentTextChanged.connect(self.on_calculation_type_changed)
        type_layout.addWidget(self.calculation_type_combo)
        type_layout.addStretch()
        commission_layout.addLayout(type_layout)

        # إعدادات حسب الكمية
        self.quantity_widget = QWidget()
        quantity_layout = QFormLayout(self.quantity_widget)

        self.min_quantity_spin = QDoubleSpinBox()
        self.min_quantity_spin.setRange(0, 999999)
        self.min_quantity_spin.setDecimals(2)
        self.min_quantity_spin.setValue(0)
        self.min_quantity_spin.setMinimumHeight(30)
        quantity_layout.addRow("الحد الأدنى للكمية:", self.min_quantity_spin)

        self.max_quantity_spin = QDoubleSpinBox()
        self.max_quantity_spin.setRange(0, 999999)
        self.max_quantity_spin.setDecimals(2)
        self.max_quantity_spin.setValue(0)
        self.max_quantity_spin.setMinimumHeight(30)
        self.max_quantity_spin.setSpecialValueText("بدون حد أقصى")
        quantity_layout.addRow("الحد الأقصى للكمية:", self.max_quantity_spin)

        self.quantity_rate_spin = QDoubleSpinBox()
        self.quantity_rate_spin.setRange(0, 999999)
        self.quantity_rate_spin.setDecimals(2)
        self.quantity_rate_spin.setValue(0)
        self.quantity_rate_spin.setMinimumHeight(30)
        self.quantity_rate_spin.setSuffix(" ريال/وحدة")
        quantity_layout.addRow("معدل العمولة:", self.quantity_rate_spin)

        commission_layout.addWidget(self.quantity_widget)

        # إعدادات حسب قيمة البضاعة
        self.value_widget = QWidget()
        value_layout = QFormLayout(self.value_widget)

        self.min_value_spin = QDoubleSpinBox()
        self.min_value_spin.setRange(0, 999999999)
        self.min_value_spin.setDecimals(2)
        self.min_value_spin.setValue(0)
        self.min_value_spin.setMinimumHeight(30)
        self.min_value_spin.setSuffix(" ريال")
        value_layout.addRow("الحد الأدنى للقيمة:", self.min_value_spin)

        self.max_value_spin = QDoubleSpinBox()
        self.max_value_spin.setRange(0, 999999999)
        self.max_value_spin.setDecimals(2)
        self.max_value_spin.setValue(0)
        self.max_value_spin.setMinimumHeight(30)
        self.max_value_spin.setSuffix(" ريال")
        self.max_value_spin.setSpecialValueText("بدون حد أقصى")
        value_layout.addRow("الحد الأقصى للقيمة:", self.max_value_spin)

        self.value_percentage_spin = QDoubleSpinBox()
        self.value_percentage_spin.setRange(0, 100)
        self.value_percentage_spin.setDecimals(2)
        self.value_percentage_spin.setValue(0)
        self.value_percentage_spin.setMinimumHeight(30)
        self.value_percentage_spin.setSuffix(" %")
        value_layout.addRow("نسبة العمولة:", self.value_percentage_spin)

        commission_layout.addWidget(self.value_widget)

        # وصف شروط العمولة
        self.commission_description_edit = QTextEdit()
        self.commission_description_edit.setMaximumHeight(60)
        self.commission_description_edit.setPlaceholderText("وصف شروط العمولة (اختياري)...")
        commission_layout.addWidget(QLabel("الوصف:"))
        commission_layout.addWidget(self.commission_description_edit)

        form_layout.addWidget(commission_group)

        # تحديد النوع الافتراضي
        self.on_calculation_type_changed()

        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.add_button = QPushButton("إضافة تعيين")
        self.add_button.clicked.connect(self.add_assignment)
        self.add_button.setMinimumHeight(40)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        self.update_button = QPushButton("تحديث")
        self.update_button.clicked.connect(self.update_assignment)
        self.update_button.setEnabled(False)
        self.update_button.setMinimumHeight(40)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)

        self.clear_button = QPushButton("مسح")
        self.clear_button.clicked.connect(self.clear_form)
        self.clear_button.setMinimumHeight(40)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.clear_button)

        form_layout.addLayout(buttons_layout)
        
        return form_group
    
    def create_table_section(self):
        """إنشاء قسم الجدول"""
        table_group = QGroupBox("تعيينات مندوبي المشتريات للموردين")
        table_layout = QVBoxLayout(table_group)
        table_layout.setSpacing(10)
        table_layout.setContentsMargins(15, 15, 15, 15)

        # الجدول
        self.assignments_table = QTableWidget()
        self.assignments_table.setColumnCount(6)
        self.assignments_table.setHorizontalHeaderLabels([
            "المورد", "مندوب المشتريات", "رئيسي", "تاريخ التعيين", "الملاحظات", "الحالة"
        ])
        
        # إعدادات الجدول
        self.assignments_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.assignments_table.setAlternatingRowColors(True)
        self.assignments_table.horizontalHeader().setStretchLastSection(True)
        self.assignments_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # ضبط عرض الأعمدة
        header = self.assignments_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)           # المورد
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # مندوب المشتريات
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # رئيسي
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # تاريخ التعيين
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # الملاحظات
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحالة

        # ربط إشارة التحديد
        self.assignments_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        table_layout.addWidget(self.assignments_table)

        # أزرار الجدول
        table_buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_data)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        delete_button = QPushButton("حذف")
        delete_button.clicked.connect(self.delete_assignment)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addStretch()
        
        table_layout.addLayout(table_buttons_layout)
        
        return table_group

    def open_advanced_search(self):
        """فتح نافذة البحث المتقدم للموردين"""
        try:
            dialog = AdvancedSupplierSearchDialog(self)
            dialog.supplier_selected.connect(self.on_supplier_selected_from_search)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة البحث: {str(e)}")

    def on_supplier_selected_from_search(self, supplier):
        """عند اختيار مورد من نافذة البحث المتقدم"""
        try:
            # تحديث حقل البحث
            self.supplier_search.setText(supplier.name)

            # تحديث القائمة المنسدلة
            self.load_suppliers()

            # اختيار المورد في القائمة
            for i in range(self.supplier_combo.count()):
                if self.supplier_combo.itemData(i) == supplier.id:
                    self.supplier_combo.setCurrentIndex(i)
                    break

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في اختيار المورد: {str(e)}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter_by(is_active=True).order_by(Supplier.name).all()

            self.supplier_combo.clear()
            self.supplier_combo.addItem("-- اختر مورد --", None)

            for supplier in suppliers:
                display_text = supplier.name
                if supplier.code:
                    display_text += f" ({supplier.code})"
                self.supplier_combo.addItem(display_text, supplier.id)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل الموردين: {str(e)}")
        finally:
            session.close()

    def load_representatives(self):
        """تحميل قائمة مندوبي المشتريات"""
        session = self.db_manager.get_session()
        try:
            representatives = session.query(PurchaseRepresentative).filter_by(
                is_active=True
            ).order_by(PurchaseRepresentative.name).all()

            self.representative_combo.clear()
            self.representative_combo.addItem("-- اختر مندوب --", None)

            for rep in representatives:
                display_text = rep.name
                if rep.code:
                    display_text += f" ({rep.code})"
                if rep.department:
                    display_text += f" - {rep.department}"
                self.representative_combo.addItem(display_text, rep.id)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل المندوبين: {str(e)}")
        finally:
            session.close()

    def on_calculation_type_changed(self):
        """عند تغيير نوع احتساب العمولة"""
        calculation_type = self.calculation_type_combo.currentText()

        if calculation_type == "حسب الكمية":
            self.quantity_widget.setVisible(True)
            self.value_widget.setVisible(False)
        else:  # حسب قيمة البضاعة
            self.quantity_widget.setVisible(False)
            self.value_widget.setVisible(True)

    def save_commission_rule(self, session, supplier_representative_id):
        """حفظ قاعدة العمولة"""
        try:
            # حذف القواعد القديمة
            session.query(ShipmentCommission).filter_by(
                supplier_representative_id=supplier_representative_id
            ).delete()

            # إنشاء قاعدة جديدة
            calculation_type = "quantity" if self.calculation_type_combo.currentText() == "حسب الكمية" else "value"

            commission_rule = ShipmentCommission(
                supplier_representative_id=supplier_representative_id,
                calculation_type=calculation_type,
                description=self.commission_description_edit.toPlainText().strip() or None
            )

            if calculation_type == "quantity":
                commission_rule.min_quantity = self.min_quantity_spin.value()
                commission_rule.max_quantity = self.max_quantity_spin.value() if self.max_quantity_spin.value() > 0 else None
                commission_rule.quantity_commission_rate = self.quantity_rate_spin.value()
            else:  # value
                commission_rule.min_value = self.min_value_spin.value()
                commission_rule.max_value = self.max_value_spin.value() if self.max_value_spin.value() > 0 else None
                commission_rule.value_commission_percentage = self.value_percentage_spin.value()

            session.add(commission_rule)

        except Exception as e:
            raise Exception(f"خطأ في حفظ قاعدة العمولة: {str(e)}")

    def load_commission_rule(self, session, supplier_representative_id):
        """تحميل قاعدة العمولة"""
        try:
            # البحث عن قاعدة العمولة
            commission_rule = session.query(ShipmentCommission).filter_by(
                supplier_representative_id=supplier_representative_id,
                is_active=True
            ).first()

            if commission_rule:
                # تحديد نوع الاحتساب
                if commission_rule.calculation_type == "quantity":
                    self.calculation_type_combo.setCurrentText("حسب الكمية")
                    self.min_quantity_spin.setValue(commission_rule.min_quantity or 0)
                    self.max_quantity_spin.setValue(commission_rule.max_quantity or 0)
                    self.quantity_rate_spin.setValue(commission_rule.quantity_commission_rate or 0)
                else:  # value
                    self.calculation_type_combo.setCurrentText("حسب قيمة البضاعة")
                    self.min_value_spin.setValue(commission_rule.min_value or 0)
                    self.max_value_spin.setValue(commission_rule.max_value or 0)
                    self.value_percentage_spin.setValue(commission_rule.value_commission_percentage or 0)

                # الوصف
                self.commission_description_edit.setPlainText(commission_rule.description or "")
            else:
                # قيم افتراضية
                self.calculation_type_combo.setCurrentIndex(0)
                self.min_quantity_spin.setValue(0)
                self.max_quantity_spin.setValue(0)
                self.quantity_rate_spin.setValue(0)
                self.min_value_spin.setValue(0)
                self.max_value_spin.setValue(0)
                self.value_percentage_spin.setValue(0)
                self.commission_description_edit.clear()

            # تحديث العرض
            self.on_calculation_type_changed()

        except Exception as e:
            print(f"خطأ في تحميل قاعدة العمولة: {str(e)}")

    def load_data(self):
        """تحميل بيانات التعيينات"""
        # تحميل القوائم المنسدلة
        self.load_suppliers()
        self.load_representatives()

        # تحميل بيانات الجدول
        session = self.db_manager.get_session()
        try:
            assignments = session.query(SupplierRepresentative).join(
                Supplier
            ).join(
                PurchaseRepresentative
            ).filter(
                SupplierRepresentative.is_active == True
            ).order_by(
                Supplier.name, PurchaseRepresentative.name
            ).all()

            self.assignments_table.setRowCount(len(assignments))

            for row, assignment in enumerate(assignments):
                # المورد
                supplier_item = QTableWidgetItem(assignment.supplier.name)
                supplier_item.setData(Qt.UserRole, assignment.id)
                self.assignments_table.setItem(row, 0, supplier_item)

                # مندوب المشتريات
                rep_item = QTableWidgetItem(assignment.representative.name)
                self.assignments_table.setItem(row, 1, rep_item)

                # رئيسي
                primary_item = QTableWidgetItem("✓" if assignment.is_primary else "")
                primary_item.setTextAlignment(Qt.AlignCenter)
                self.assignments_table.setItem(row, 2, primary_item)

                # تاريخ التعيين
                date_text = ""
                if assignment.assigned_date:
                    date_text = str(assignment.assigned_date)
                date_item = QTableWidgetItem(date_text)
                self.assignments_table.setItem(row, 3, date_item)

                # الملاحظات
                notes_item = QTableWidgetItem(assignment.notes or "")
                self.assignments_table.setItem(row, 4, notes_item)

                # الحالة
                status_item = QTableWidgetItem("نشط" if assignment.is_active else "غير نشط")
                self.assignments_table.setItem(row, 5, status_item)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
        finally:
            session.close()

    def on_supplier_search_changed(self):
        """عند تغيير نص البحث"""
        self.search_timer.stop()
        self.search_timer.start(300)  # انتظار 300ms قبل البحث

    def perform_search(self):
        """تنفيذ البحث الفوري"""
        search_text = self.supplier_search.text().strip().lower()

        if not search_text:
            # إذا كان البحث فارغ، أظهر جميع الموردين
            self.load_suppliers()
            return

        session = self.db_manager.get_session()
        try:
            # البحث في اسم المورد أو الكود
            suppliers = session.query(Supplier).filter(
                Supplier.is_active == True,
                (Supplier.name.ilike(f'%{search_text}%') |
                 Supplier.code.ilike(f'%{search_text}%'))
            ).order_by(Supplier.name).all()

            self.supplier_combo.clear()
            self.supplier_combo.addItem("-- اختر مورد --", None)

            for supplier in suppliers:
                display_text = supplier.name
                if supplier.code:
                    display_text += f" ({supplier.code})"
                self.supplier_combo.addItem(display_text, supplier.id)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في البحث: {str(e)}")
        finally:
            session.close()

    def add_assignment(self):
        """إضافة تعيين جديد"""
        if not self.validate_form():
            return

        supplier_id = self.supplier_combo.currentData()
        representative_id = self.representative_combo.currentData()

        session = self.db_manager.get_session()
        try:
            # التحقق من عدم وجود تعيين مسبق
            existing = session.query(SupplierRepresentative).filter_by(
                supplier_id=supplier_id,
                representative_id=representative_id,
                is_active=True
            ).first()

            if existing:
                QMessageBox.warning(self, "تحذير", "هذا المندوب مُعيَّن بالفعل لهذا المورد!")
                return

            # إذا كان المندوب رئيسي، إلغاء الرئيسية من المندوبين الآخرين لنفس المورد
            if self.is_primary_check.isChecked():
                session.query(SupplierRepresentative).filter_by(
                    supplier_id=supplier_id,
                    is_primary=True,
                    is_active=True
                ).update({'is_primary': False})

            # إنشاء تعيين جديد
            assignment = SupplierRepresentative(
                supplier_id=supplier_id,
                representative_id=representative_id,
                is_primary=self.is_primary_check.isChecked(),
                assigned_date=self.assigned_date_edit.date().toPython(),
                notes=self.notes_edit.toPlainText().strip() or None
            )

            session.add(assignment)
            session.flush()  # للحصول على معرف التعيين

            # إضافة قاعدة العمولة
            self.save_commission_rule(session, assignment.id)

            session.commit()

            QMessageBox.information(self, "نجح", "تم إضافة التعيين وقاعدة العمولة بنجاح!")
            self.clear_form()
            self.load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة التعيين: {str(e)}")
        finally:
            session.close()

    def update_assignment(self):
        """تحديث تعيين موجود"""
        if not self.current_assignment_id:
            return

        if not self.validate_form():
            return

        supplier_id = self.supplier_combo.currentData()
        representative_id = self.representative_combo.currentData()

        session = self.db_manager.get_session()
        try:
            assignment = session.query(SupplierRepresentative).get(self.current_assignment_id)
            if not assignment:
                QMessageBox.warning(self, "خطأ", "التعيين غير موجود!")
                return

            # التحقق من عدم تكرار التعيين (إذا تم تغييره)
            if (assignment.supplier_id != supplier_id or
                assignment.representative_id != representative_id):
                existing = session.query(SupplierRepresentative).filter_by(
                    supplier_id=supplier_id,
                    representative_id=representative_id,
                    is_active=True
                ).filter(
                    SupplierRepresentative.id != self.current_assignment_id
                ).first()

                if existing:
                    QMessageBox.warning(self, "تحذير", "هذا المندوب مُعيَّن بالفعل لهذا المورد!")
                    return

            # إذا كان المندوب رئيسي، إلغاء الرئيسية من المندوبين الآخرين لنفس المورد
            if self.is_primary_check.isChecked():
                session.query(SupplierRepresentative).filter_by(
                    supplier_id=supplier_id,
                    is_primary=True,
                    is_active=True
                ).filter(
                    SupplierRepresentative.id != self.current_assignment_id
                ).update({'is_primary': False})

            # تحديث البيانات
            assignment.supplier_id = supplier_id
            assignment.representative_id = representative_id
            assignment.is_primary = self.is_primary_check.isChecked()
            assignment.assigned_date = self.assigned_date_edit.date().toPython()
            assignment.notes = self.notes_edit.toPlainText().strip() or None

            # تحديث قاعدة العمولة
            self.save_commission_rule(session, assignment.id)

            session.commit()

            QMessageBox.information(self, "نجح", "تم تحديث التعيين وقاعدة العمولة بنجاح!")
            self.clear_form()
            self.load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث التعيين: {str(e)}")
        finally:
            session.close()

    def delete_assignment(self):
        """حذف تعيين محدد"""
        current_row = self.assignments_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار تعيين للحذف")
            return

        # الحصول على معرف التعيين
        assignment_id = self.assignments_table.item(current_row, 0).data(Qt.UserRole)
        if not assignment_id:
            return

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا التعيين؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        session = self.db_manager.get_session()
        try:
            assignment = session.query(SupplierRepresentative).get(assignment_id)
            if assignment:
                # حذف ناعم - تعطيل التعيين بدلاً من الحذف
                assignment.is_active = False
                session.commit()

                QMessageBox.information(self, "نجح", "تم حذف التعيين بنجاح!")
                self.load_data()
            else:
                QMessageBox.warning(self, "خطأ", "التعيين غير موجود!")

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف التعيين: {str(e)}")
        finally:
            session.close()

    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.assignments_table.currentRow()
        if current_row >= 0:
            self.load_selected_assignment()

    def load_selected_assignment(self):
        """تحميل بيانات التعيين المحدد"""
        current_row = self.assignments_table.currentRow()
        if current_row < 0:
            return

        assignment_id = self.assignments_table.item(current_row, 0).data(Qt.UserRole)
        if not assignment_id:
            return

        session = self.db_manager.get_session()
        try:
            assignment = session.query(SupplierRepresentative).get(assignment_id)
            if not assignment:
                return

            self.current_assignment_id = assignment.id

            # تعبئة النموذج
            # اختيار المورد
            for i in range(self.supplier_combo.count()):
                if self.supplier_combo.itemData(i) == assignment.supplier_id:
                    self.supplier_combo.setCurrentIndex(i)
                    break

            # اختيار المندوب
            for i in range(self.representative_combo.count()):
                if self.representative_combo.itemData(i) == assignment.representative_id:
                    self.representative_combo.setCurrentIndex(i)
                    break

            # المندوب الرئيسي
            self.is_primary_check.setChecked(assignment.is_primary)

            # تاريخ التعيين
            if assignment.assigned_date:
                self.assigned_date_edit.setDate(QDate.fromString(str(assignment.assigned_date), "yyyy-MM-dd"))
            else:
                self.assigned_date_edit.setDate(QDate.currentDate())

            # الملاحظات
            self.notes_edit.setPlainText(assignment.notes or "")

            # تحميل قاعدة العمولة
            self.load_commission_rule(session, assignment.id)

            # تفعيل زر التحديث
            self.add_button.setEnabled(False)
            self.update_button.setEnabled(True)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
        finally:
            session.close()

    def clear_form(self):
        """مسح النموذج"""
        self.current_assignment_id = None
        self.supplier_search.clear()
        self.supplier_combo.setCurrentIndex(0)
        self.representative_combo.setCurrentIndex(0)
        self.is_primary_check.setChecked(False)
        self.assigned_date_edit.setDate(QDate.currentDate())
        self.notes_edit.clear()

        # مسح حقول العمولة
        self.calculation_type_combo.setCurrentIndex(0)
        self.min_quantity_spin.setValue(0)
        self.max_quantity_spin.setValue(0)
        self.quantity_rate_spin.setValue(0)
        self.min_value_spin.setValue(0)
        self.max_value_spin.setValue(0)
        self.value_percentage_spin.setValue(0)
        self.commission_description_edit.clear()
        self.on_calculation_type_changed()

        # إعادة تفعيل زر الإضافة
        self.add_button.setEnabled(True)
        self.update_button.setEnabled(False)

    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.supplier_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد")
            self.supplier_combo.setFocus()
            return False

        if not self.representative_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مندوب مشتريات")
            self.representative_combo.setFocus()
            return False

        return True
