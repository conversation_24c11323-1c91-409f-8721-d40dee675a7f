#!/usr/bin/env python3
"""
نافذة تتبع طلبات الشراء المتقدمة
Advanced Purchase Orders Tracking Window
"""

import sys
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QTableWidget, QTableWidgetItem, QHeaderView, QFrame, QLabel,
    QLineEdit, QComboBox, QPushButton, QGroupBox, QSplitter,
    QStatusBar, QToolBar, QMessageBox, QProgressBar, QDateEdit,
    QCheckBox, QSpinBox, QTabWidget, QTextEdit, QScrollArea
)
from PySide6.QtCore import Qt, QTimer, QDate, QThread, QObject, Signal
from PySide6.QtGui import QFont, QAction, QPixmap, QPainter, QColor, QBrush

from ...database.database_manager import DatabaseManager
from ...database.models import PurchaseOrder, PurchaseOrderItem, Supplier, Currency, Item
from ...utils.formatters import format_currency, format_date


class PurchaseOrdersTrackingWindow(QMainWindow):
    """نافذة تتبع طلبات الشراء المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.setup_window()
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.setWindowTitle("تتبع طلبات الشراء المتقدم - ProShipment")
        self.setMinimumSize(1600, 900)
        
        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()
        
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 10)
        self.setFont(font)
        
        # تطبيق تخطيط RTL
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin: 5px;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #495057;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #007bff;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
            QLineEdit, QComboBox, QDateEdit, QSpinBox {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
                font-size: 11px;
            }
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QSpinBox:focus {
                border-color: #007bff;
                outline: none;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QStatusBar {
                background-color: #343a40;
                color: white;
                border-top: 1px solid #dee2e6;
            }
            QToolBar {
                background-color: #495057;
                border: none;
                spacing: 5px;
                padding: 5px;
            }
            QToolBar QToolButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QToolBar QToolButton:hover {
                background-color: #5a6268;
            }
        """)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # إعداد شريط الأدوات
        self.setup_toolbar()
        
        # إعداد قسم الإحصائيات
        self.setup_statistics_section(main_layout)
        
        # إعداد قسم البحث والفلترة
        self.setup_search_filter_section(main_layout)
        
        # إعداد الجدول الرئيسي
        self.setup_main_table(main_layout)
        
        # إعداد شريط الحالة
        self.setup_status_bar()
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("أدوات التتبع")
        toolbar.setMovable(False)
        
        # تحديث البيانات
        refresh_action = QAction("تحديث البيانات", self)
        refresh_action.setStatusTip("تحديث جميع البيانات")
        refresh_action.triggered.connect(self.refresh_all_data)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # تصدير التقرير
        export_action = QAction("تصدير التقرير", self)
        export_action.setStatusTip("تصدير تقرير التتبع")
        export_action.triggered.connect(self.export_tracking_report)
        toolbar.addAction(export_action)
        
        # طباعة التقرير
        print_action = QAction("طباعة التقرير", self)
        print_action.setStatusTip("طباعة تقرير التتبع")
        print_action.triggered.connect(self.print_tracking_report)
        toolbar.addAction(print_action)
        
        toolbar.addSeparator()
        
        # إعدادات التتبع
        settings_action = QAction("إعدادات التتبع", self)
        settings_action.setStatusTip("إعدادات نظام التتبع")
        settings_action.triggered.connect(self.show_tracking_settings)
        toolbar.addAction(settings_action)
        
        # إغلاق
        close_action = QAction("إغلاق", self)
        close_action.setStatusTip("إغلاق نافذة التتبع")
        close_action.triggered.connect(self.close)
        toolbar.addAction(close_action)
    
    def setup_statistics_section(self, parent_layout):
        """إعداد قسم الإحصائيات"""
        stats_frame = QFrame()
        stats_frame.setFixedHeight(300)
        stats_layout = QVBoxLayout(stats_frame)
        
        # عنوان القسم
        title_label = QLabel("إحصائيات طلبات الشراء")
        title_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #343a40; margin: 10px;")
        stats_layout.addWidget(title_label)
        
        # شبكة البطاقات
        cards_widget = QWidget()
        cards_layout = QGridLayout(cards_widget)
        cards_layout.setSpacing(15)
        
        # بطاقات الإحصائيات
        self.create_statistics_cards(cards_layout)
        
        stats_layout.addWidget(cards_widget)
        parent_layout.addWidget(stats_frame)
    
    def create_statistics_cards(self, layout):
        """إنشاء بطاقات الإحصائيات"""
        # بيانات البطاقات
        cards_data = [
            {"title": "إجمالي الطلبات", "value": "0", "color": "#007bff", "key": "total_orders"},
            {"title": "طلبات نشطة", "value": "0", "color": "#28a745", "key": "active_orders"},
            {"title": "طلبات مكتملة", "value": "0", "color": "#17a2b8", "key": "completed_orders"},
            {"title": "طلبات متأخرة", "value": "0", "color": "#dc3545", "key": "overdue_orders"},
            {"title": "إجمالي القيمة", "value": "0.00", "color": "#6f42c1", "key": "total_value"},
            {"title": "متوسط وقت التسليم", "value": "0 يوم", "color": "#fd7e14", "key": "avg_delivery_time"}
        ]
        
        self.stats_cards = {}
        
        for i, card_data in enumerate(cards_data):
            row = i // 3
            col = i % 3
            
            card = self.create_stat_card(
                card_data["title"], 
                card_data["value"], 
                card_data["color"]
            )
            
            self.stats_cards[card_data["key"]] = card
            layout.addWidget(card, row, col)
    
    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFixedHeight(100)
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:1 {self.darken_color(color)});
                border: none;
                border-radius: 12px;
                color: white;
            }}
            QLabel {{
                color: white;
                background: transparent;
                border: none;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 10, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # حفظ مرجع للقيمة للتحديث لاحقاً
        card.value_label = value_label
        
        return card
    
    def darken_color(self, color):
        """تغميق اللون"""
        color_map = {
            "#007bff": "#0056b3",
            "#28a745": "#1e7e34", 
            "#17a2b8": "#117a8b",
            "#dc3545": "#bd2130",
            "#6f42c1": "#59359a",
            "#fd7e14": "#e8650e"
        }
        return color_map.get(color, color)

    def setup_search_filter_section(self, parent_layout):
        """إعداد قسم البحث والفلترة"""
        search_frame = QGroupBox("البحث والفلترة المتقدمة")
        search_layout = QGridLayout(search_frame)
        search_layout.setSpacing(10)

        # البحث النصي
        search_layout.addWidget(QLabel("البحث:"), 0, 0)
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث في رقم الطلب، المورد، أو الملاحظات...")
        search_layout.addWidget(self.search_edit, 0, 1, 1, 2)

        # فلتر حالة الطلب
        search_layout.addWidget(QLabel("حالة الطلب:"), 1, 0)
        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "جميع الحالات", "مسودة", "مرسل", "مؤكد", "جزئي", "مكتمل", "ملغي"
        ])
        search_layout.addWidget(self.status_filter, 1, 1)

        # فلتر المورد
        search_layout.addWidget(QLabel("المورد:"), 1, 2)
        self.supplier_filter = QComboBox()
        self.supplier_filter.addItem("جميع الموردين", None)
        search_layout.addWidget(self.supplier_filter, 1, 3)

        # فلتر التاريخ من
        search_layout.addWidget(QLabel("من تاريخ:"), 2, 0)
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        search_layout.addWidget(self.date_from, 2, 1)

        # فلتر التاريخ إلى
        search_layout.addWidget(QLabel("إلى تاريخ:"), 2, 2)
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        search_layout.addWidget(self.date_to, 2, 3)

        # فلتر القيمة من
        search_layout.addWidget(QLabel("القيمة من:"), 3, 0)
        self.value_from = QSpinBox()
        self.value_from.setMaximum(999999999)
        self.value_from.setSuffix(" ريال")
        search_layout.addWidget(self.value_from, 3, 1)

        # فلتر القيمة إلى
        search_layout.addWidget(QLabel("القيمة إلى:"), 3, 2)
        self.value_to = QSpinBox()
        self.value_to.setMaximum(999999999)
        self.value_to.setValue(999999999)
        self.value_to.setSuffix(" ريال")
        search_layout.addWidget(self.value_to, 3, 3)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        search_btn = QPushButton("بحث")
        search_btn.clicked.connect(self.perform_search)
        buttons_layout.addWidget(search_btn)

        clear_btn = QPushButton("مسح الفلاتر")
        clear_btn.clicked.connect(self.clear_filters)
        buttons_layout.addWidget(clear_btn)

        export_filtered_btn = QPushButton("تصدير النتائج")
        export_filtered_btn.clicked.connect(self.export_filtered_results)
        buttons_layout.addWidget(export_filtered_btn)

        buttons_layout.addStretch()
        search_layout.addLayout(buttons_layout, 4, 0, 1, 4)

        parent_layout.addWidget(search_frame)

    def setup_main_table(self, parent_layout):
        """إعداد الجدول الرئيسي"""
        table_frame = QGroupBox("تفاصيل طلبات الشراء")
        table_layout = QVBoxLayout(table_frame)

        # إنشاء الجدول
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(15)
        self.orders_table.setHorizontalHeaderLabels([
            "رقم الطلب", "تاريخ الطلب", "المورد", "حالة الطلب",
            "عدد الأصناف", "إجمالي الكمية", "إجمالي القيمة", "العملة",
            "تاريخ التسليم المتوقع", "الكمية المسلمة", "الكمية المتبقية",
            "نسبة الإنجاز", "الأيام المتبقية", "حالة التأخير", "ملاحظات"
        ])

        # إعدادات الجدول
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.orders_table.setSelectionMode(QTableWidget.SingleSelection)
        self.orders_table.setSortingEnabled(True)
        self.orders_table.verticalHeader().setVisible(False)

        # تعديل عرض الأعمدة
        header = self.orders_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # رقم الطلب
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # تاريخ الطلب
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # المورد
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # حالة الطلب
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # عدد الأصناف
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # إجمالي الكمية
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # إجمالي القيمة
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # العملة
        header.setSectionResizeMode(8, QHeaderView.Fixed)  # تاريخ التسليم
        header.setSectionResizeMode(9, QHeaderView.Fixed)  # الكمية المسلمة
        header.setSectionResizeMode(10, QHeaderView.Fixed)  # الكمية المتبقية
        header.setSectionResizeMode(11, QHeaderView.Fixed)  # نسبة الإنجاز
        header.setSectionResizeMode(12, QHeaderView.Fixed)  # الأيام المتبقية
        header.setSectionResizeMode(13, QHeaderView.Fixed)  # حالة التأخير
        header.setSectionResizeMode(14, QHeaderView.Stretch)  # ملاحظات

        # تحديد عرض الأعمدة
        self.orders_table.setColumnWidth(0, 120)  # رقم الطلب
        self.orders_table.setColumnWidth(1, 100)  # تاريخ الطلب
        self.orders_table.setColumnWidth(3, 100)  # حالة الطلب
        self.orders_table.setColumnWidth(4, 80)   # عدد الأصناف
        self.orders_table.setColumnWidth(5, 100)  # إجمالي الكمية
        self.orders_table.setColumnWidth(6, 120)  # إجمالي القيمة
        self.orders_table.setColumnWidth(7, 80)   # العملة
        self.orders_table.setColumnWidth(8, 100)  # تاريخ التسليم
        self.orders_table.setColumnWidth(9, 100)  # الكمية المسلمة
        self.orders_table.setColumnWidth(10, 100) # الكمية المتبقية
        self.orders_table.setColumnWidth(11, 100) # نسبة الإنجاز
        self.orders_table.setColumnWidth(12, 100) # الأيام المتبقية
        self.orders_table.setColumnWidth(13, 100) # حالة التأخير

        table_layout.addWidget(self.orders_table)
        parent_layout.addWidget(table_frame)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # عداد الطلبات
        self.orders_count_label = QLabel("إجمالي الطلبات: 0")
        self.status_bar.addWidget(self.orders_count_label)

        self.status_bar.addPermanentWidget(QLabel(""))

        # مؤشر التحديث
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)

        # وقت آخر تحديث
        self.last_update_label = QLabel("آخر تحديث: لم يتم التحديث بعد")
        self.status_bar.addPermanentWidget(self.last_update_label)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # البحث الفوري
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        self.search_edit.textChanged.connect(lambda: self.search_timer.start(300))

        # فلاتر الكومبو بوكس
        self.status_filter.currentTextChanged.connect(self.perform_search)
        self.supplier_filter.currentTextChanged.connect(self.perform_search)

        # فلاتر التاريخ
        self.date_from.dateChanged.connect(self.perform_search)
        self.date_to.dateChanged.connect(self.perform_search)

        # النقر المزدوج على الجدول
        self.orders_table.itemDoubleClicked.connect(self.view_order_details)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            self.load_suppliers_filter()
            # تحميل البيانات بشكل آمن
            self.safe_refresh_data()
        except Exception as e:
            print(f"تحذير: خطأ في تحميل البيانات الأولية: {e}")
            # تعيين قيم افتراضية للإحصائيات
            self.set_default_statistics()

    def load_suppliers_filter(self):
        """تحميل قائمة الموردين للفلترة"""
        try:
            session = self.db_manager.get_session()
            suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()

            self.supplier_filter.clear()
            self.supplier_filter.addItem("جميع الموردين", None)

            for supplier in suppliers:
                self.supplier_filter.addItem(supplier.name, supplier.id)

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الموردين: {str(e)}")

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        try:
            # تحديث الإحصائيات
            self.progress_bar.setValue(25)
            self.update_statistics()

            # تحديث الجدول
            self.progress_bar.setValue(50)
            self.load_orders_data()

            # تحديث وقت آخر تحديث
            self.progress_bar.setValue(100)
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.last_update_label.setText(f"آخر تحديث: {current_time}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث البيانات: {str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            session = self.db_manager.get_session()

            # إجمالي الطلبات
            total_orders = session.query(PurchaseOrder).filter(
                PurchaseOrder.is_active == True
            ).count()

            # الطلبات النشطة (غير مكتملة وغير ملغية)
            active_orders = session.query(PurchaseOrder).filter(
                PurchaseOrder.is_active == True,
                PurchaseOrder.order_status.notin_(['مكتمل', 'ملغي'])
            ).count()

            # الطلبات المكتملة
            completed_orders = session.query(PurchaseOrder).filter(
                PurchaseOrder.is_active == True,
                PurchaseOrder.order_status == 'مكتمل'
            ).count()

            # الطلبات المتأخرة
            today = datetime.now().date()
            overdue_orders = session.query(PurchaseOrder).filter(
                PurchaseOrder.is_active == True,
                PurchaseOrder.expected_delivery_date < today,
                PurchaseOrder.order_status.notin_(['مكتمل', 'ملغي'])
            ).count()

            # إجمالي القيمة
            orders_with_values = session.query(PurchaseOrder.total_amount).filter(
                PurchaseOrder.is_active == True,
                PurchaseOrder.total_amount.isnot(None)
            ).all()

            total_value = sum(order[0] for order in orders_with_values) if orders_with_values else 0

            # متوسط وقت التسليم (للطلبات المكتملة)
            completed_orders_with_dates = session.query(PurchaseOrder).filter(
                PurchaseOrder.is_active == True,
                PurchaseOrder.order_status == 'مكتمل',
                PurchaseOrder.expected_delivery_date.isnot(None)
            ).all()

            avg_delivery_days = 0
            if completed_orders_with_dates:
                total_days = 0
                for order in completed_orders_with_dates:
                    if order.order_date and order.expected_delivery_date:
                        days_diff = (order.expected_delivery_date.date() - order.order_date.date()).days
                        total_days += days_diff
                avg_delivery_days = total_days // len(completed_orders_with_dates)

            # تحديث البطاقات
            self.stats_cards["total_orders"].value_label.setText(str(total_orders))
            self.stats_cards["active_orders"].value_label.setText(str(active_orders))
            self.stats_cards["completed_orders"].value_label.setText(str(completed_orders))
            self.stats_cards["overdue_orders"].value_label.setText(str(overdue_orders))
            self.stats_cards["total_value"].value_label.setText(f"{total_value:,.2f}")
            self.stats_cards["avg_delivery_time"].value_label.setText(f"{avg_delivery_days} يوم")

            session.close()

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")
            self.set_default_statistics()

    def safe_refresh_data(self):
        """تحديث البيانات بشكل آمن"""
        try:
            self.update_statistics()
            self.load_orders_data()
        except Exception as e:
            print(f"تحذير: خطأ في تحديث البيانات: {e}")
            self.set_default_statistics()

    def set_default_statistics(self):
        """تعيين قيم افتراضية للإحصائيات"""
        try:
            # تعيين قيم افتراضية للبطاقات
            default_stats = {
                'total_orders': ('إجمالي الطلبات', '0', '#007bff'),
                'active_orders': ('الطلبات النشطة', '0', '#28a745'),
                'completed_orders': ('الطلبات المكتملة', '0', '#17a2b8'),
                'overdue_orders': ('الطلبات المتأخرة', '0', '#dc3545'),
                'total_value': ('إجمالي القيمة', '0.00', '#ffc107'),
                'avg_delivery_time': ('متوسط وقت التسليم', '0 يوم', '#6f42c1')
            }

            for card_key, (title, value, color) in default_stats.items():
                if card_key in self.stats_cards:
                    card = self.stats_cards[card_key]
                    # البحث عن QLabel للقيمة في البطاقة
                    for child in card.findChildren(QLabel):
                        if child.objectName() == f"{card_key}_value":
                            child.setText(value)
                            break

            # تحديث شريط الحالة
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage("جاهز - لا توجد بيانات")

        except Exception as e:
            print(f"خطأ في تعيين القيم الافتراضية: {e}")

    def load_orders_data(self):
        """تحميل بيانات الطلبات"""
        try:
            session = self.db_manager.get_session()

            # بناء الاستعلام الأساسي
            query = session.query(PurchaseOrder).outerjoin(Supplier).filter(
                PurchaseOrder.is_active == True
            )

            # تطبيق الفلاتر
            query = self.apply_filters(query)

            # ترتيب النتائج
            orders = query.order_by(PurchaseOrder.created_at.desc()).all()

            # تحميل بيانات الأصناف لكل طلب
            for order in orders:
                order.items = session.query(PurchaseOrderItem).filter(
                    PurchaseOrderItem.purchase_order_id == order.id
                ).all()

            # تعبئة الجدول
            self.populate_orders_table(orders)

            # تحديث عداد الطلبات
            self.orders_count_label.setText(f"إجمالي الطلبات: {len(orders)}")

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الطلبات: {str(e)}")

    def apply_filters(self, query):
        """تطبيق الفلاتر على الاستعلام"""
        # فلتر النص
        search_text = self.search_edit.text().strip()
        if search_text:
            query = query.filter(
                (PurchaseOrder.order_number.contains(search_text)) |
                (Supplier.name.contains(search_text)) |
                (PurchaseOrder.notes.contains(search_text))
            )

        # فلتر الحالة
        status = self.status_filter.currentText()
        if status != "جميع الحالات":
            query = query.filter(PurchaseOrder.order_status == status)

        # فلتر المورد
        supplier_id = self.supplier_filter.currentData()
        if supplier_id:
            query = query.filter(PurchaseOrder.supplier_id == supplier_id)

        # فلتر التاريخ
        date_from = self.date_from.date().toPython()
        date_to = self.date_to.date().toPython()
        query = query.filter(
            PurchaseOrder.order_date >= date_from,
            PurchaseOrder.order_date <= date_to
        )

        # فلتر القيمة
        value_from = self.value_from.value()
        value_to = self.value_to.value()
        if value_from > 0:
            query = query.filter(PurchaseOrder.total_amount >= value_from)
        if value_to < 999999999:
            query = query.filter(PurchaseOrder.total_amount <= value_to)

        return query

    def populate_orders_table(self, orders):
        """تعبئة جدول الطلبات"""
        self.orders_table.setRowCount(len(orders))

        for row, order in enumerate(orders):
            try:
                # حساب الإحصائيات
                total_items = len(order.items) if hasattr(order, 'items') else 0
                total_quantity = sum(item.quantity for item in order.items) if hasattr(order, 'items') else 0
                delivered_quantity = sum(item.delivered_quantity for item in order.items) if hasattr(order, 'items') else 0
                remaining_quantity = total_quantity - delivered_quantity
                completion_percentage = (delivered_quantity / total_quantity * 100) if total_quantity > 0 else 0

                # حساب الأيام المتبقية
                days_remaining = ""
                delay_status = "في الوقت المحدد"
                if order.expected_delivery_date:
                    today = datetime.now().date()
                    expected_date = order.expected_delivery_date.date() if hasattr(order.expected_delivery_date, 'date') else order.expected_delivery_date
                    days_diff = (expected_date - today).days

                    if days_diff > 0:
                        days_remaining = f"{days_diff} يوم"
                        delay_status = "في الوقت المحدد"
                    elif days_diff == 0:
                        days_remaining = "اليوم"
                        delay_status = "مستحق اليوم"
                    else:
                        days_remaining = f"{abs(days_diff)} يوم متأخر"
                        delay_status = "متأخر"

                # تعبئة البيانات
                self.orders_table.setItem(row, 0, QTableWidgetItem(order.order_number or ""))
                self.orders_table.setItem(row, 1, QTableWidgetItem(format_date(order.order_date)))
                self.orders_table.setItem(row, 2, QTableWidgetItem(order.supplier.name if order.supplier else ""))

                # حالة الطلب مع لون
                status_item = QTableWidgetItem(order.order_status or "")
                status_item.setBackground(self.get_status_color(order.order_status))
                self.orders_table.setItem(row, 3, status_item)

                self.orders_table.setItem(row, 4, QTableWidgetItem(str(total_items)))
                self.orders_table.setItem(row, 5, QTableWidgetItem(f"{total_quantity:,.0f}"))
                self.orders_table.setItem(row, 6, QTableWidgetItem(f"{order.total_amount:,.2f}"))
                self.orders_table.setItem(row, 7, QTableWidgetItem(order.currency.code if order.currency else ""))
                self.orders_table.setItem(row, 8, QTableWidgetItem(format_date(order.expected_delivery_date)))
                self.orders_table.setItem(row, 9, QTableWidgetItem(f"{delivered_quantity:,.0f}"))
                self.orders_table.setItem(row, 10, QTableWidgetItem(f"{remaining_quantity:,.0f}"))

                # نسبة الإنجاز مع لون
                completion_item = QTableWidgetItem(f"{completion_percentage:.1f}%")
                completion_item.setBackground(self.get_completion_color(completion_percentage))
                self.orders_table.setItem(row, 11, completion_item)

                self.orders_table.setItem(row, 12, QTableWidgetItem(days_remaining))

                # حالة التأخير مع لون
                delay_item = QTableWidgetItem(delay_status)
                delay_item.setBackground(self.get_delay_color(delay_status))
                self.orders_table.setItem(row, 13, delay_item)

                self.orders_table.setItem(row, 14, QTableWidgetItem(order.notes or ""))

                # حفظ معرف الطلب
                self.orders_table.item(row, 0).setData(Qt.UserRole, order.id)

            except Exception as e:
                print(f"خطأ في تعبئة الصف {row}: {str(e)}")
                continue

    def get_status_color(self, status):
        """الحصول على لون حالة الطلب"""
        colors = {
            "مسودة": QColor("#6c757d"),      # رمادي
            "مرسل": QColor("#007bff"),       # أزرق
            "مؤكد": QColor("#28a745"),       # أخضر
            "جزئي": QColor("#ffc107"),       # أصفر
            "مكتمل": QColor("#17a2b8"),      # أزرق فاتح
            "ملغي": QColor("#dc3545")        # أحمر
        }
        color = colors.get(status, QColor("#6c757d"))
        color.setAlpha(100)  # شفافية
        return QBrush(color)

    def get_completion_color(self, percentage):
        """الحصول على لون نسبة الإنجاز"""
        if percentage >= 100:
            color = QColor("#28a745")  # أخضر
        elif percentage >= 75:
            color = QColor("#17a2b8")  # أزرق فاتح
        elif percentage >= 50:
            color = QColor("#ffc107")  # أصفر
        elif percentage >= 25:
            color = QColor("#fd7e14")  # برتقالي
        else:
            color = QColor("#dc3545")  # أحمر

        color.setAlpha(100)
        return QBrush(color)

    def get_delay_color(self, delay_status):
        """الحصول على لون حالة التأخير"""
        colors = {
            "في الوقت المحدد": QColor("#28a745"),    # أخضر
            "مستحق اليوم": QColor("#ffc107"),       # أصفر
            "متأخر": QColor("#dc3545")              # أحمر
        }
        color = colors.get(delay_status, QColor("#6c757d"))
        color.setAlpha(100)
        return QBrush(color)

    def perform_search(self):
        """تنفيذ البحث"""
        self.load_orders_data()

    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.search_edit.clear()
        self.status_filter.setCurrentIndex(0)
        self.supplier_filter.setCurrentIndex(0)
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_to.setDate(QDate.currentDate())
        self.value_from.setValue(0)
        self.value_to.setValue(999999999)
        self.perform_search()

    def view_order_details(self, item):
        """عرض تفاصيل الطلب"""
        if item.column() == 0:  # النقر على رقم الطلب
            order_id = item.data(Qt.UserRole)
            if order_id:
                # فتح نافذة تفاصيل الطلب
                from .purchase_orders_window import PurchaseOrdersWindow

                details_window = PurchaseOrdersWindow(mode="entry", maximize_on_start=False)
                details_window.load_order_details(order_id)
                details_window.setWindowTitle(f"تفاصيل الطلب - {item.text()}")
                details_window.show()

    def export_tracking_report(self):
        """تصدير تقرير التتبع"""
        try:
            from PySide6.QtWidgets import QFileDialog
            import csv

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير تقرير التتبع",
                f"تقرير_تتبع_طلبات_الشراء_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    headers = []
                    for col in range(self.orders_table.columnCount()):
                        headers.append(self.orders_table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(self.orders_table.rowCount()):
                        row_data = []
                        for col in range(self.orders_table.columnCount()):
                            item = self.orders_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تصدير التقرير: {str(e)}")

    def export_filtered_results(self):
        """تصدير النتائج المفلترة"""
        self.export_tracking_report()

    def print_tracking_report(self):
        """طباعة تقرير التتبع"""
        QMessageBox.information(self, "طباعة", "سيتم تطوير وظيفة الطباعة قريباً")

    def show_tracking_settings(self):
        """عرض إعدادات التتبع"""
        QMessageBox.information(self, "إعدادات", "سيتم تطوير إعدادات التتبع قريباً")


# دالة مساعدة لفتح نافذة التتبع
def open_purchase_orders_tracking():
    """فتح نافذة تتبع طلبات الشراء"""
    tracking_window = PurchaseOrdersTrackingWindow()
    tracking_window.show()
    return tracking_window


if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # تطبيق الخط العربي
    font = QFont("Segoe UI", 10)
    app.setFont(font)

    # إنشاء وعرض النافذة
    window = PurchaseOrdersTrackingWindow()
    window.show()

    sys.exit(app.exec())
