# -*- coding: utf-8 -*-
"""
إضافة جداول مندوبي المشتريات
Add Purchase Representatives Tables Migration
"""

import sqlite3
import os
from datetime import datetime

def add_purchase_representatives_tables():
    """إضافة جداول مندوبي المشتريات"""
    
    # مسار قاعدة البيانات
    db_path = 'data/proshipment.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 فحص وجود جداول مندوبي المشتريات...")
        
        # فحص وجود جدول مندوبي المشتريات
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='purchase_representatives'
        """)
        
        if not cursor.fetchone():
            print("🔨 إنشاء جدول purchase_representatives...")
            
            # إنشاء جدول مندوبي المشتريات
            cursor.execute("""
                CREATE TABLE purchase_representatives (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code VARCHAR(50) UNIQUE,
                    name VARCHAR(200) NOT NULL,
                    name_en VARCHAR(200),
                    phone VARCHAR(50),
                    mobile VARCHAR(50),
                    email VARCHAR(100),
                    department VARCHAR(100),
                    position VARCHAR(100),
                    hire_date DATE,
                    salary REAL,
                    commission_rate REAL DEFAULT 0.0,
                    address TEXT,
                    city VARCHAR(100),
                    notes TEXT,
                    emergency_contact VARCHAR(200),
                    emergency_phone VARCHAR(50),
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            print("✅ تم إنشاء جدول purchase_representatives بنجاح!")
        else:
            print("ℹ️ جدول purchase_representatives موجود بالفعل")
        
        # فحص وجود جدول ربط الموردين بالمندوبين
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='supplier_representatives'
        """)
        
        if not cursor.fetchone():
            print("🔨 إنشاء جدول supplier_representatives...")
            
            # إنشاء جدول ربط الموردين بالمندوبين
            cursor.execute("""
                CREATE TABLE supplier_representatives (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    supplier_id INTEGER NOT NULL,
                    representative_id INTEGER NOT NULL,
                    is_primary BOOLEAN DEFAULT 0,
                    assigned_date DATE DEFAULT CURRENT_DATE,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                    FOREIGN KEY (representative_id) REFERENCES purchase_representatives (id)
                )
            """)
            
            print("✅ تم إنشاء جدول supplier_representatives بنجاح!")
        else:
            print("ℹ️ جدول supplier_representatives موجود بالفعل")
        
        # إنشاء الفهارس
        print("🔨 إنشاء الفهارس...")
        
        # فهارس جدول مندوبي المشتريات
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_purchase_representatives_code 
            ON purchase_representatives (code)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_purchase_representatives_name 
            ON purchase_representatives (name)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_purchase_representatives_active 
            ON purchase_representatives (is_active)
        """)
        
        # فهارس جدول ربط الموردين بالمندوبين
        cursor.execute("""
            CREATE UNIQUE INDEX IF NOT EXISTS idx_supplier_representative_unique 
            ON supplier_representatives (supplier_id, representative_id)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_supplier_representatives_supplier_id 
            ON supplier_representatives (supplier_id)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_supplier_representatives_representative_id 
            ON supplier_representatives (representative_id)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_supplier_representatives_primary 
            ON supplier_representatives (is_primary)
        """)
        
        # حفظ التغييرات
        conn.commit()
        
        print("✅ تم إنشاء جميع الفهارس بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {str(e)}")
        return False
        
    finally:
        if conn:
            conn.close()

def add_sample_representatives():
    """إضافة مندوبين تجريبيين"""
    
    db_path = 'data/proshipment.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود مندوبين
        cursor.execute("SELECT COUNT(*) FROM purchase_representatives")
        count = cursor.fetchone()[0]
        
        if count == 0:
            print("📝 إضافة مندوبين تجريبيين...")
            
            # إضافة مندوبين تجريبيين
            representatives = [
                ('REP001', 'أحمد محمد السعد', 'Ahmed Mohammed Al-Saad', '0112345678', '0501234567', '<EMAIL>', 'المشتريات', 'مندوب مشتريات أول', '2020-01-15', 8000.0, 2.5, 'الرياض - حي النخيل', 'الرياض'),
                ('REP002', 'فاطمة علي الأحمد', 'Fatima Ali Al-Ahmad', '0123456789', '0512345678', '<EMAIL>', 'المشتريات', 'مندوبة مشتريات', '2021-03-10', 7000.0, 2.0, 'جدة - حي الصفا', 'جدة'),
                ('REP003', 'خالد عبدالله النصر', 'Khalid Abdullah Al-Nasr', '0134567890', '0523456789', '<EMAIL>', 'المشتريات', 'مندوب مشتريات متخصص', '2019-06-20', 9000.0, 3.0, 'الدمام - حي الفيصلية', 'الدمام'),
                ('REP004', 'نورا سعد الغامدي', 'Nora Saad Al-Ghamdi', '0145678901', '0534567890', '<EMAIL>', 'المشتريات', 'مندوبة مشتريات كبيرة', '2018-09-05', 10000.0, 3.5, 'مكة - حي العزيزية', 'مكة'),
                ('REP005', 'محمد عبدالرحمن القحطاني', 'Mohammed Abdulrahman Al-Qahtani', '0156789012', '0545678901', '<EMAIL>', 'المشتريات', 'مدير مندوبي المشتريات', '2017-02-12', 12000.0, 4.0, 'المدينة المنورة - حي قباء', 'المدينة المنورة')
            ]
            
            for rep in representatives:
                cursor.execute("""
                    INSERT INTO purchase_representatives 
                    (code, name, name_en, phone, mobile, email, department, position, hire_date, salary, commission_rate, address, city)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, rep)
            
            conn.commit()
            print(f"✅ تم إضافة {len(representatives)} مندوب تجريبي!")
        else:
            print(f"ℹ️ يوجد {count} مندوب بالفعل")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المندوبين التجريبيين: {str(e)}")
        return False

def test_representatives_tables():
    """اختبار جداول مندوبي المشتريات"""
    try:
        from ...database.models import PurchaseRepresentative, SupplierRepresentative
        from ...database.database_manager import DatabaseManager
        
        print("\n🧪 اختبار جداول مندوبي المشتريات...")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # عدد المندوبين
        representatives_count = session.query(PurchaseRepresentative).count()
        print(f"📊 عدد المندوبين: {representatives_count}")
        
        # عرض المندوبين
        representatives = session.query(PurchaseRepresentative).limit(3).all()
        print("👥 المندوبين:")
        for rep in representatives:
            print(f"   - {rep.name} ({rep.code}) - {rep.department}")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إضافة جداول مندوبي المشتريات")
    print("=" * 50)
    
    if add_purchase_representatives_tables():
        print("\n" + "=" * 50)
        add_sample_representatives()
        print("\n" + "=" * 50)
        test_representatives_tables()
        print("\n🎉 تم إكمال العملية بنجاح!")
    else:
        print("\n❌ فشل في إضافة الجداول")
