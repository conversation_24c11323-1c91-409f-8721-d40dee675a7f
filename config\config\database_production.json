{"type": "oracle", "oracle_config": {"host": "prod-oracle-cluster.company.com", "port": 1521, "service_name": "PROSHIP_PROD", "sid": null, "username": "proshipment_prod", "password": "", "connection_type": "service_name", "pool_size": 50, "max_overflow": 100, "pool_timeout": 60, "pool_recycle": 1800, "pool_pre_ping": true, "use_ssl": true, "ssl_cert_path": "/etc/ssl/oracle/cert.pem", "wallet_location": "/etc/oracle/wallet", "encoding": "UTF-8", "nencoding": "UTF-8", "threaded": true, "auto_commit": false}, "sqlite_config": {"path": "data/proshipment_backup.db", "timeout": 30, "check_same_thread": false}}