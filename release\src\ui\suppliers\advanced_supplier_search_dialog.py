# -*- coding: utf-8 -*-
"""
نافذة البحث المتقدم للموردين
Advanced Supplier Search Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QComboBox,
                               QAbstractItemView, QDialogButtonBox, QSplitter)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QKeySequence, QShortcut

from ...database.database_manager import DatabaseManager
from ...database.models import Supplier


class AdvancedSupplierSearchDialog(QDialog):
    """نافذة البحث المتقدم للموردين"""
    
    # إشارة لإرسال المورد المحدد
    supplier_selected = Signal(object)  # Supplier object
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.selected_supplier = None
        self.setup_ui()
        self.load_data()
        
        # ربط اختصار F9 لفتح النافذة (للمرجع)
        self.setWindowTitle("البحث المتقدم للموردين - اضغط F9")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("البحث المتقدم للموردين")
        self.setMinimumSize(1100, 700)
        self.resize(1200, 800)
        
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 10)
        self.setFont(font)
        
        # تطبيق تخطيط RTL
        self.setLayoutDirection(Qt.RightToLeft)
        
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # إنشاء splitter للتحكم في الأحجام
        splitter = QSplitter(Qt.Vertical)
        
        # قسم البحث
        search_widget = self.create_search_section()
        search_widget.setMaximumHeight(300)
        search_widget.setMinimumHeight(280)
        
        # قسم النتائج
        results_widget = self.create_results_section()
        
        splitter.addWidget(search_widget)
        splitter.addWidget(results_widget)
        splitter.setStretchFactor(0, 0)  # قسم البحث لا يتمدد
        splitter.setStretchFactor(1, 1)  # قسم النتائج يتمدد
        
        main_layout.addWidget(splitter)
        
        # أزرار النافذة
        self.create_dialog_buttons(main_layout)
        
        # تطبيق الأنماط
        self.apply_styles()
    
    def create_search_section(self):
        """إنشاء قسم البحث"""
        search_group = QGroupBox("معايير البحث")
        search_layout = QVBoxLayout(search_group)
        search_layout.setSpacing(20)
        search_layout.setContentsMargins(20, 25, 20, 20)

        # استخدام تخطيط عمودي بسيط مع صفوف منفصلة
        # الصف الأول - كود المورد واسم المورد
        row1_layout = QHBoxLayout()
        row1_layout.setSpacing(20)

        # كود المورد
        code_container = QVBoxLayout()
        code_container.setSpacing(5)
        code_label = QLabel("كود المورد:")
        code_label.setFixedHeight(20)
        code_label.setAlignment(Qt.AlignRight)
        self.code_search = QLineEdit()
        self.code_search.setPlaceholderText("ابحث بكود المورد...")
        self.code_search.textChanged.connect(self.on_search_changed)
        self.code_search.setFixedHeight(35)
        self.code_search.setMinimumWidth(200)
        code_container.addWidget(code_label)
        code_container.addWidget(self.code_search)

        # اسم المورد
        name_container = QVBoxLayout()
        name_container.setSpacing(5)
        name_label = QLabel("اسم المورد:")
        name_label.setFixedHeight(20)
        name_label.setAlignment(Qt.AlignRight)
        self.name_search = QLineEdit()
        self.name_search.setPlaceholderText("ابحث باسم المورد...")
        self.name_search.textChanged.connect(self.on_search_changed)
        self.name_search.setFixedHeight(35)
        self.name_search.setMinimumWidth(200)
        name_container.addWidget(name_label)
        name_container.addWidget(self.name_search)

        row1_layout.addLayout(code_container)
        row1_layout.addLayout(name_container)

        # الصف الثاني - نوع المورد والمدينة
        row2_layout = QHBoxLayout()
        row2_layout.setSpacing(15)

        # نوع المورد
        type_container = QVBoxLayout()
        type_label = QLabel("نوع المورد:")
        type_label.setFixedHeight(20)
        self.type_combo = QComboBox()
        self.type_combo.addItems(["الكل", "شركة", "فرد", "مؤسسة", "شركة شحن"])
        self.type_combo.currentTextChanged.connect(self.on_search_changed)
        self.type_combo.setFixedHeight(35)
        type_container.addWidget(type_label)
        type_container.addWidget(self.type_combo)

        # المدينة
        city_container = QVBoxLayout()
        city_label = QLabel("المدينة:")
        city_label.setFixedHeight(20)
        self.city_search = QLineEdit()
        self.city_search.setPlaceholderText("ابحث بالمدينة...")
        self.city_search.textChanged.connect(self.on_search_changed)
        self.city_search.setFixedHeight(35)
        city_container.addWidget(city_label)
        city_container.addWidget(self.city_search)

        row2_layout.addLayout(type_container)
        row2_layout.addLayout(city_container)

        # الصف الثالث - الهاتف والبريد الإلكتروني
        row3_layout = QHBoxLayout()
        row3_layout.setSpacing(15)

        # الهاتف
        phone_container = QVBoxLayout()
        phone_label = QLabel("الهاتف:")
        phone_label.setFixedHeight(20)
        self.phone_search = QLineEdit()
        self.phone_search.setPlaceholderText("ابحث بالهاتف...")
        self.phone_search.textChanged.connect(self.on_search_changed)
        self.phone_search.setFixedHeight(35)
        phone_container.addWidget(phone_label)
        phone_container.addWidget(self.phone_search)

        # البريد الإلكتروني
        email_container = QVBoxLayout()
        email_label = QLabel("البريد الإلكتروني:")
        email_label.setFixedHeight(20)
        self.email_search = QLineEdit()
        self.email_search.setPlaceholderText("ابحث بالبريد الإلكتروني...")
        self.email_search.textChanged.connect(self.on_search_changed)
        self.email_search.setFixedHeight(35)
        email_container.addWidget(email_label)
        email_container.addWidget(self.email_search)

        row3_layout.addLayout(phone_container)
        row3_layout.addLayout(email_container)

        # إضافة الصفوف إلى التخطيط الرئيسي
        search_layout.addLayout(row1_layout)
        search_layout.addLayout(row2_layout)
        search_layout.addLayout(row3_layout)
        
        # أزرار البحث
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        buttons_layout.setContentsMargins(0, 15, 0, 0)

        search_button = QPushButton("🔍 بحث")
        search_button.clicked.connect(self.perform_search)
        search_button.setDefault(True)
        search_button.setMinimumWidth(120)
        search_button.setMaximumWidth(150)

        clear_button = QPushButton("🗑️ مسح")
        clear_button.clicked.connect(self.clear_search)
        clear_button.setMinimumWidth(120)
        clear_button.setMaximumWidth(150)

        buttons_layout.addStretch()
        buttons_layout.addWidget(search_button)
        buttons_layout.addWidget(clear_button)
        buttons_layout.addStretch()

        search_layout.addLayout(buttons_layout)
        
        return search_group
    
    def create_results_section(self):
        """إنشاء قسم النتائج"""
        results_group = QGroupBox("نتائج البحث")
        results_layout = QVBoxLayout(results_group)
        results_layout.setSpacing(10)
        results_layout.setContentsMargins(15, 15, 15, 15)
        
        # جدول النتائج
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(8)
        self.results_table.setHorizontalHeaderLabels([
            "الكود", "اسم المورد", "النوع", "الهاتف", "البريد الإلكتروني", 
            "المدينة", "شخص الاتصال", "الحالة"
        ])
        
        # إعدادات الجدول
        self.results_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSortingEnabled(True)
        
        # ضبط عرض الأعمدة
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # الكود
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # اسم المورد
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # النوع
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الهاتف
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # البريد
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # المدينة
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # شخص الاتصال
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # الحالة
        
        # ربط النقر المزدوج
        self.results_table.itemDoubleClicked.connect(self.on_item_double_clicked)
        
        results_layout.addWidget(self.results_table)
        
        # معلومات النتائج
        self.results_info = QLabel("جاهز للبحث...")
        self.results_info.setStyleSheet("color: #666; font-style: italic;")
        results_layout.addWidget(self.results_info)
        
        return results_group
    
    def create_dialog_buttons(self, layout):
        """إنشاء أزرار النافذة"""
        button_box = QDialogButtonBox()
        
        # زر اختيار
        select_button = QPushButton("✅ اختيار")
        select_button.clicked.connect(self.accept_selection)
        select_button.setEnabled(False)
        button_box.addButton(select_button, QDialogButtonBox.AcceptRole)
        self.select_button = select_button
        
        # زر إلغاء
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(self.reject)
        button_box.addButton(cancel_button, QDialogButtonBox.RejectRole)
        
        # ربط تغيير التحديد
        self.results_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(button_box)
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #f8f9fa;
            }
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                padding: 2px 5px;
                min-width: 120px;
                max-height: 20px;
                background-color: transparent;
            }
            QLineEdit, QComboBox {
                padding: 8px 12px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
                min-height: 35px;
                max-height: 35px;
                background-color: white;
                margin: 2px 0px;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #3498db;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 1px solid #bdc3c7;
                width: 8px;
                height: 8px;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
                min-height: 35px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:default {
                background-color: #27ae60;
            }
            QPushButton:default:hover {
                background-color: #229954;
            }
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                border: 1px solid #ddd;
                border-radius: 6px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                border-right: 1px solid #2c3e50;
            }
            QHeaderView::section:last {
                border-right: none;
            }
        """)
    
    def load_data(self):
        """تحميل جميع الموردين في البداية"""
        self.perform_search()
    
    def on_search_changed(self):
        """عند تغيير معايير البحث"""
        # يمكن إضافة بحث فوري هنا إذا رغبت
        pass
    
    def perform_search(self):
        """تنفيذ البحث"""
        session = self.db_manager.get_session()
        try:
            # بناء الاستعلام
            query = session.query(Supplier).filter(Supplier.is_active == True)
            
            # تطبيق معايير البحث
            if self.code_search.text().strip():
                query = query.filter(Supplier.code.ilike(f'%{self.code_search.text().strip()}%'))
            
            if self.name_search.text().strip():
                query = query.filter(Supplier.name.ilike(f'%{self.name_search.text().strip()}%'))
            
            if self.type_combo.currentText() != "الكل":
                query = query.filter(Supplier.supplier_type == self.type_combo.currentText())
            
            if self.city_search.text().strip():
                query = query.filter(Supplier.city.ilike(f'%{self.city_search.text().strip()}%'))
            
            if self.phone_search.text().strip():
                phone_text = self.phone_search.text().strip()
                query = query.filter(
                    (Supplier.phone.ilike(f'%{phone_text}%')) |
                    (Supplier.mobile.ilike(f'%{phone_text}%'))
                )
            
            if self.email_search.text().strip():
                query = query.filter(Supplier.email.ilike(f'%{self.email_search.text().strip()}%'))
            
            # تنفيذ الاستعلام
            suppliers = query.order_by(Supplier.name).all()
            
            # ملء الجدول
            self.populate_results_table(suppliers)
            
            # تحديث معلومات النتائج
            self.results_info.setText(f"تم العثور على {len(suppliers)} مورد")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث: {str(e)}")
            self.results_info.setText("خطأ في البحث")
        finally:
            session.close()
    
    def populate_results_table(self, suppliers):
        """ملء جدول النتائج"""
        self.results_table.setRowCount(len(suppliers))
        
        for row, supplier in enumerate(suppliers):
            # الكود
            code_item = QTableWidgetItem(supplier.code or "")
            code_item.setData(Qt.UserRole, supplier.id)
            self.results_table.setItem(row, 0, code_item)
            
            # اسم المورد
            name_item = QTableWidgetItem(supplier.name or "")
            self.results_table.setItem(row, 1, name_item)
            
            # النوع
            type_item = QTableWidgetItem(supplier.supplier_type or "")
            self.results_table.setItem(row, 2, type_item)
            
            # الهاتف
            phone = supplier.phone or supplier.mobile or ""
            phone_item = QTableWidgetItem(phone)
            self.results_table.setItem(row, 3, phone_item)
            
            # البريد الإلكتروني
            email_item = QTableWidgetItem(supplier.email or "")
            self.results_table.setItem(row, 4, email_item)
            
            # المدينة
            city_item = QTableWidgetItem(supplier.city or "")
            self.results_table.setItem(row, 5, city_item)
            
            # شخص الاتصال
            contact_item = QTableWidgetItem(supplier.contact_person or "")
            self.results_table.setItem(row, 6, contact_item)
            
            # الحالة
            status_item = QTableWidgetItem("نشط" if supplier.is_active else "غير نشط")
            self.results_table.setItem(row, 7, status_item)
    
    def clear_search(self):
        """مسح معايير البحث"""
        self.code_search.clear()
        self.name_search.clear()
        self.type_combo.setCurrentIndex(0)
        self.city_search.clear()
        self.phone_search.clear()
        self.email_search.clear()
        self.perform_search()  # إعادة تحميل جميع الموردين
    
    def on_selection_changed(self):
        """عند تغيير التحديد"""
        has_selection = len(self.results_table.selectedItems()) > 0
        self.select_button.setEnabled(has_selection)
    
    def on_item_double_clicked(self, item):
        """عند النقر المزدوج على عنصر"""
        self.accept_selection()
    
    def accept_selection(self):
        """قبول الاختيار"""
        current_row = self.results_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد")
            return
        
        supplier_id = self.results_table.item(current_row, 0).data(Qt.UserRole)
        if not supplier_id:
            return
        
        # الحصول على بيانات المورد
        session = self.db_manager.get_session()
        try:
            supplier = session.query(Supplier).get(supplier_id)
            if supplier:
                self.selected_supplier = supplier
                self.supplier_selected.emit(supplier)
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", "المورد غير موجود")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في اختيار المورد: {str(e)}")
        finally:
            session.close()
    
    def get_selected_supplier(self):
        """الحصول على المورد المحدد"""
        return self.selected_supplier
