# -*- coding: utf-8 -*-
"""
تبويب تقارير الموردين
Suppliers Reports Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QTextEdit, QCheckBox,
                               QComboBox, QDateEdit, QSpinBox, QTabWidget, QMainWindow,
                               QMenuBar, QMenu, QToolBar, QStatusBar)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont, QAction, QIcon

from ...database.database_manager import DatabaseManager
from ...database.models import Supplier

class SuppliersReportsWidget(QWidget):
    """ويدجت تقارير الموردين"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.load_initial_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # عنوان القسم
        title_label = QLabel("تقارير الموردين والشركاء التجاريين")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # تبويبات التقارير
        self.reports_tab_widget = QTabWidget()
        self.reports_tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 8px 15px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        # إضافة تبويبات التقارير
        self.setup_suppliers_summary_tab()
        self.setup_transactions_report_tab()
        self.setup_performance_report_tab()
        self.setup_financial_report_tab()
        
        main_layout.addWidget(self.reports_tab_widget)
    
    def setup_suppliers_summary_tab(self):
        """إعداد تبويب ملخص الموردين"""
        
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # فلاتر التقرير
        filters_group = QGroupBox("فلاتر التقرير")
        filters_layout = QHBoxLayout(filters_group)
        
        # فلتر حسب النوع
        type_layout = QFormLayout()
        self.supplier_type_filter = QComboBox()
        self.supplier_type_filter.addItems(["الكل", "شركة", "فرد", "مؤسسة"])
        type_layout.addRow("نوع المورد:", self.supplier_type_filter)
        filters_layout.addLayout(type_layout)
        
        # فلتر حسب الحالة
        status_layout = QFormLayout()
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "نشط", "غير نشط"])
        status_layout.addRow("الحالة:", self.status_filter)
        filters_layout.addLayout(status_layout)
        
        # فلتر حسب المدينة
        city_layout = QFormLayout()
        self.city_filter = QComboBox()
        self.load_cities()
        city_layout.addRow("المدينة:", self.city_filter)
        filters_layout.addLayout(city_layout)
        
        # زر التطبيق
        apply_button = QPushButton("تطبيق الفلاتر")
        apply_button.clicked.connect(self.apply_suppliers_filters)
        apply_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        filters_layout.addWidget(apply_button)
        
        layout.addWidget(filters_group)
        
        # جدول ملخص الموردين
        self.suppliers_summary_table = QTableWidget()
        self.suppliers_summary_table.setColumnCount(8)
        self.suppliers_summary_table.setHorizontalHeaderLabels([
            "اسم المورد", "النوع", "المدينة", "الهاتف", "البريد الإلكتروني",
            "حد الائتمان", "مدة السداد", "الحالة"
        ])
        
        header = self.suppliers_summary_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.suppliers_summary_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.suppliers_summary_table)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.refresh_suppliers_summary)
        
        export_button = QPushButton("تصدير إلى Excel")
        export_button.clicked.connect(self.export_suppliers_summary)
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        print_button = QPushButton("طباعة")
        print_button.clicked.connect(self.print_suppliers_summary)
        
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addWidget(export_button)
        buttons_layout.addWidget(print_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        self.reports_tab_widget.addTab(tab_widget, "ملخص الموردين")
    
    def setup_transactions_report_tab(self):
        """إعداد تبويب تقرير المعاملات"""
        
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # فلاتر التقرير
        filters_group = QGroupBox("فلاتر تقرير المعاملات")
        filters_layout = QHBoxLayout(filters_group)
        
        # فترة التقرير
        period_layout = QFormLayout()
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        self.from_date.setCalendarPopup(True)
        period_layout.addRow("من تاريخ:", self.from_date)
        
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.setCalendarPopup(True)
        period_layout.addRow("إلى تاريخ:", self.to_date)
        
        filters_layout.addLayout(period_layout)
        
        # فلتر المورد
        supplier_layout = QFormLayout()
        self.supplier_filter = QComboBox()
        self.load_suppliers_for_filter()
        supplier_layout.addRow("المورد:", self.supplier_filter)
        filters_layout.addLayout(supplier_layout)
        
        # فلتر نوع المعاملة
        transaction_layout = QFormLayout()
        self.transaction_type_filter = QComboBox()
        self.transaction_type_filter.addItems([
            "الكل", "فاتورة شراء", "دفعة", "خصم", "إرجاع", "تسوية"
        ])
        transaction_layout.addRow("نوع المعاملة:", self.transaction_type_filter)
        filters_layout.addLayout(transaction_layout)
        
        # زر التطبيق
        apply_trans_button = QPushButton("تطبيق الفلاتر")
        apply_trans_button.clicked.connect(self.apply_transactions_filters)
        apply_trans_button.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        filters_layout.addWidget(apply_trans_button)
        
        layout.addWidget(filters_group)
        
        # جدول المعاملات
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(7)
        self.transactions_table.setHorizontalHeaderLabels([
            "التاريخ", "المورد", "نوع المعاملة", "رقم المرجع", "المبلغ", "العملة", "الوصف"
        ])
        
        header = self.transactions_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.transactions_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.transactions_table)
        
        # ملخص المعاملات
        summary_group = QGroupBox("ملخص المعاملات")
        summary_layout = QHBoxLayout(summary_group)
        
        self.total_transactions_label = QLabel("إجمالي المعاملات: 0")
        self.total_amount_label = QLabel("إجمالي المبلغ: 0.00 ريال")
        
        summary_layout.addWidget(self.total_transactions_label)
        summary_layout.addWidget(self.total_amount_label)
        summary_layout.addStretch()
        
        layout.addWidget(summary_group)
        
        self.reports_tab_widget.addTab(tab_widget, "تقرير المعاملات")
    
    def setup_performance_report_tab(self):
        """إعداد تبويب تقرير الأداء"""
        
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # معلومات الأداء
        performance_group = QGroupBox("مؤشرات أداء الموردين")
        performance_layout = QVBoxLayout(performance_group)
        
        # جدول الأداء
        self.performance_table = QTableWidget()
        self.performance_table.setColumnCount(6)
        self.performance_table.setHorizontalHeaderLabels([
            "المورد", "عدد المعاملات", "إجمالي المشتريات", "متوسط قيمة المعاملة",
            "آخر معاملة", "تقييم الأداء"
        ])
        
        header = self.performance_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.performance_table.setAlternatingRowColors(True)
        
        performance_layout.addWidget(self.performance_table)
        layout.addWidget(performance_group)
        
        # أزرار التحكم
        perf_buttons_layout = QHBoxLayout()
        
        refresh_perf_button = QPushButton("تحديث البيانات")
        refresh_perf_button.clicked.connect(self.refresh_performance_data)
        
        export_perf_button = QPushButton("تصدير التقرير")
        export_perf_button.clicked.connect(self.export_performance_report)
        
        perf_buttons_layout.addWidget(refresh_perf_button)
        perf_buttons_layout.addWidget(export_perf_button)
        perf_buttons_layout.addStretch()
        
        layout.addLayout(perf_buttons_layout)
        
        self.reports_tab_widget.addTab(tab_widget, "تقرير الأداء")
    
    def setup_financial_report_tab(self):
        """إعداد تبويب التقرير المالي"""
        
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # ملخص مالي
        financial_group = QGroupBox("الملخص المالي للموردين")
        financial_layout = QVBoxLayout(financial_group)
        
        # إحصائيات مالية
        stats_layout = QHBoxLayout()
        
        self.total_suppliers_label = QLabel("إجمالي الموردين: 0")
        self.active_suppliers_label = QLabel("الموردين النشطين: 0")
        self.total_credit_label = QLabel("إجمالي حدود الائتمان: 0.00 ريال")
        
        stats_layout.addWidget(self.total_suppliers_label)
        stats_layout.addWidget(self.active_suppliers_label)
        stats_layout.addWidget(self.total_credit_label)
        stats_layout.addStretch()
        
        financial_layout.addLayout(stats_layout)
        
        # جدول التقرير المالي
        self.financial_table = QTableWidget()
        self.financial_table.setColumnCount(5)
        self.financial_table.setHorizontalHeaderLabels([
            "المورد", "حد الائتمان", "الرصيد الحالي", "المبلغ المستحق", "حالة الحساب"
        ])
        
        header = self.financial_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.financial_table.setAlternatingRowColors(True)
        
        financial_layout.addWidget(self.financial_table)
        layout.addWidget(financial_group)
        
        self.reports_tab_widget.addTab(tab_widget, "التقرير المالي")
    
    def load_cities(self):
        """تحميل قائمة المدن"""
        session = self.db_manager.get_session()
        try:
            cities = session.query(Supplier.city).distinct().filter(
                Supplier.city.isnot(None), Supplier.city != ""
            ).all()
            
            self.city_filter.clear()
            self.city_filter.addItem("الكل", None)
            
            for city in cities:
                if city[0]:
                    self.city_filter.addItem(city[0], city[0])
            
        except Exception as e:
            print(f"خطأ في تحميل المدن: {e}")
        finally:
            session.close()
    
    def load_suppliers_for_filter(self):
        """تحميل قائمة الموردين للفلتر"""
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter_by(is_active=True).all()
            
            self.supplier_filter.clear()
            self.supplier_filter.addItem("الكل", None)
            
            for supplier in suppliers:
                self.supplier_filter.addItem(supplier.name, supplier.id)
            
        except Exception as e:
            print(f"خطأ في تحميل الموردين: {e}")
        finally:
            session.close()
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        self.refresh_suppliers_summary()
        self.refresh_financial_summary()
    
    def refresh_suppliers_summary(self):
        """تحديث ملخص الموردين"""
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter_by(is_active=True).all()
            
            self.suppliers_summary_table.setRowCount(len(suppliers))
            
            for row, supplier in enumerate(suppliers):
                # اسم المورد
                self.suppliers_summary_table.setItem(row, 0, QTableWidgetItem(supplier.name or ""))
                
                # النوع
                self.suppliers_summary_table.setItem(row, 1, QTableWidgetItem(supplier.supplier_type or ""))
                
                # المدينة
                self.suppliers_summary_table.setItem(row, 2, QTableWidgetItem(supplier.city or ""))
                
                # الهاتف
                self.suppliers_summary_table.setItem(row, 3, QTableWidgetItem(supplier.phone or ""))
                
                # البريد الإلكتروني
                self.suppliers_summary_table.setItem(row, 4, QTableWidgetItem(supplier.email or ""))
                
                # حد الائتمان
                credit_limit = f"{supplier.credit_limit or 0:.2f} ريال"
                self.suppliers_summary_table.setItem(row, 5, QTableWidgetItem(credit_limit))
                
                # مدة السداد
                payment_terms = f"{supplier.payment_terms or 0} يوم"
                self.suppliers_summary_table.setItem(row, 6, QTableWidgetItem(payment_terms))
                
                # الحالة
                status_text = "نشط" if supplier.is_active else "غير نشط"
                status_item = QTableWidgetItem(status_text)
                if supplier.is_active:
                    status_item.setBackground(Qt.green)
                else:
                    status_item.setBackground(Qt.red)
                self.suppliers_summary_table.setItem(row, 7, status_item)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل ملخص الموردين:\n{str(e)}"
            )
        finally:
            session.close()
    
    def refresh_financial_summary(self):
        """تحديث الملخص المالي"""
        session = self.db_manager.get_session()
        try:
            # إحصائيات عامة
            total_suppliers = session.query(Supplier).count()
            active_suppliers = session.query(Supplier).filter_by(is_active=True).count()
            
            # إجمالي حدود الائتمان
            total_credit = session.query(Supplier.credit_limit).filter_by(is_active=True).all()
            total_credit_amount = sum(credit[0] or 0 for credit in total_credit)
            
            # تحديث التسميات
            self.total_suppliers_label.setText(f"إجمالي الموردين: {total_suppliers}")
            self.active_suppliers_label.setText(f"الموردين النشطين: {active_suppliers}")
            self.total_credit_label.setText(f"إجمالي حدود الائتمان: {total_credit_amount:.2f} ريال")
            
        except Exception as e:
            print(f"خطأ في تحديث الملخص المالي: {e}")
        finally:
            session.close()
    
    def apply_suppliers_filters(self):
        """تطبيق فلاتر ملخص الموردين"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة الفلاتر قيد التطوير"
        )
    
    def apply_transactions_filters(self):
        """تطبيق فلاتر المعاملات"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة فلاتر المعاملات قيد التطوير"
        )
    
    def refresh_performance_data(self):
        """تحديث بيانات الأداء"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "تقرير الأداء قيد التطوير"
        )
    
    def export_suppliers_summary(self):
        """تصدير ملخص الموردين"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة التصدير قيد التطوير"
        )
    
    def export_performance_report(self):
        """تصدير تقرير الأداء"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة تصدير تقرير الأداء قيد التطوير"
        )
    
    def print_suppliers_summary(self):
        """طباعة ملخص الموردين"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة الطباعة قيد التطوير"
        )
    
    def generate_suppliers_report(self):
        """إنتاج تقرير الموردين"""
        self.reports_tab_widget.setCurrentIndex(0)
        self.refresh_suppliers_summary()
    
    def generate_transactions_report(self):
        """إنتاج تقرير المعاملات"""
        self.reports_tab_widget.setCurrentIndex(1)
        self.apply_transactions_filters()
    
    def generate_performance_report(self):
        """إنتاج تقرير الأداء"""
        self.reports_tab_widget.setCurrentIndex(2)
        self.refresh_performance_data()
    
    def refresh_reports(self):
        """تحديث جميع التقارير"""
        self.refresh_suppliers_summary()
        self.refresh_financial_summary()
        self.refresh_performance_data()


class SuppliersReportsWindow(QMainWindow):
    """نافذة تقارير الموردين المنفصلة"""

    def __init__(self):
        super().__init__()
        self.setup_window()
        self.setup_ui()

    def setup_window(self):
        """إعداد النافذة"""
        self.setWindowTitle("تقارير الموردين - ProShipment")
        self.setMinimumSize(1400, 800)

        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()

        # تطبيق الخط العربي
        font = QFont("Segoe UI", 10)
        self.setFont(font)

        # تطبيق تخطيط RTL
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QMenuBar {
                background-color: #2c3e50;
                color: white;
                border: none;
                padding: 5px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 15px;
                margin: 2px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #34495e;
            }
            QToolBar {
                background-color: #34495e;
                border: none;
                spacing: 5px;
                padding: 5px;
            }
            QStatusBar {
                background-color: #2c3e50;
                color: white;
                border: none;
            }
        """)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء الويدجت الرئيسي
        self.reports_widget = SuppliersReportsWidget()
        self.setCentralWidget(self.reports_widget)

        # إعداد شريط القوائم
        self.setup_menu_bar()

        # إعداد شريط الأدوات
        self.setup_toolbar()

        # إعداد شريط الحالة
        self.setup_status_bar()

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu("ملف")

        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)

        # قائمة التقارير
        reports_menu = menubar.addMenu("التقارير")

        suppliers_report_action = QAction("تقرير الموردين", self)
        suppliers_report_action.triggered.connect(self.reports_widget.generate_suppliers_report)
        reports_menu.addAction(suppliers_report_action)

        transactions_report_action = QAction("تقرير المعاملات", self)
        transactions_report_action.triggered.connect(self.reports_widget.generate_transactions_report)
        reports_menu.addAction(transactions_report_action)

        performance_report_action = QAction("تقرير الأداء", self)
        performance_report_action.triggered.connect(self.reports_widget.generate_performance_report)
        reports_menu.addAction(performance_report_action)

        reports_menu.addSeparator()

        refresh_all_action = QAction("تحديث جميع التقارير", self)
        refresh_all_action.setShortcut("F5")
        refresh_all_action.triggered.connect(self.reports_widget.refresh_reports)
        reports_menu.addAction(refresh_all_action)

        # قائمة التصدير
        export_menu = menubar.addMenu("تصدير")

        export_suppliers_action = QAction("تصدير ملخص الموردين", self)
        export_suppliers_action.triggered.connect(self.reports_widget.export_suppliers_summary)
        export_menu.addAction(export_suppliers_action)

        export_performance_action = QAction("تصدير تقرير الأداء", self)
        export_performance_action.triggered.connect(self.reports_widget.export_performance_report)
        export_menu.addAction(export_performance_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")

        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setMovable(False)

        # زر تحديث جميع التقارير
        refresh_action = QAction("تحديث الكل", self)
        refresh_action.setToolTip("تحديث جميع التقارير (F5)")
        refresh_action.triggered.connect(self.reports_widget.refresh_reports)
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        # زر تقرير الموردين
        suppliers_action = QAction("تقرير الموردين", self)
        suppliers_action.setToolTip("عرض تقرير ملخص الموردين")
        suppliers_action.triggered.connect(self.reports_widget.generate_suppliers_report)
        toolbar.addAction(suppliers_action)

        # زر تقرير المعاملات
        transactions_action = QAction("تقرير المعاملات", self)
        transactions_action.setToolTip("عرض تقرير المعاملات")
        transactions_action.triggered.connect(self.reports_widget.generate_transactions_report)
        toolbar.addAction(transactions_action)

        # زر تقرير الأداء
        performance_action = QAction("تقرير الأداء", self)
        performance_action.setToolTip("عرض تقرير الأداء")
        performance_action.triggered.connect(self.reports_widget.generate_performance_report)
        toolbar.addAction(performance_action)

        toolbar.addSeparator()

        # زر تصدير
        export_action = QAction("تصدير", self)
        export_action.setToolTip("تصدير التقرير الحالي")
        export_action.triggered.connect(self.reports_widget.export_suppliers_summary)
        toolbar.addAction(export_action)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.showMessage("جاهز - تقارير الموردين")

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(
            self,
            "حول البرنامج",
            "نظام تقارير الموردين\n"
            "جزء من نظام ProShipment المتكامل\n"
            "الإصدار 1.0"
        )
