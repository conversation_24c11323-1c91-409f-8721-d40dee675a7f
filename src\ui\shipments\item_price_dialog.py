#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة إدخال السعر والكمية للأصناف
"""

import sys
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel,
    QPushButton, QLineEdit, QDoubleSpinBox, QSpinBox, QGroupBox,
    QDialogButtonBox, QMessageBox, QTextEdit, QDateEdit
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont, QPixmap, QIcon
from ..widgets.flexible_date_edit import FlexibleDateEdit

class ItemPriceDialog(QDialog):
    """نافذة إدخال السعر والكمية للصنف"""
    
    def __init__(self, parent=None, item_data=None):
        super().__init__(parent)
        self.item_data = item_data or {}
        self.quantity = 0
        self.unit_price = 0.0
        self.total_price = 0.0
        self.production_date = None
        self.expiry_date = None
        self.notes = ""
        self.setup_ui()
        self.setup_connections()
        
        # إذا كان هناك بيانات موجودة، املأ الحقول
        if item_data:
            self.load_item_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        item_name = self.item_data.get('name', 'صنف غير محدد')
        self.setWindowTitle(f"إدخال السعر والكمية - {item_name}")
        self.setModal(True)
        self.resize(500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel(f"إدخال بيانات الصنف")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # مجموعة بيانات الصنف
        item_info_group = QGroupBox("معلومات الصنف")
        item_info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #3498db;
                background-color: white;
            }
        """)
        item_info_layout = QFormLayout(item_info_group)
        
        # اسم الصنف
        self.item_name_label = QLabel(item_name)
        self.item_name_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; }")
        item_info_layout.addRow("اسم الصنف:", self.item_name_label)
        
        # كود الصنف
        item_code = self.item_data.get('code', 'غير محدد')
        self.item_code_label = QLabel(item_code)
        self.item_code_label.setStyleSheet("QLabel { color: #7f8c8d; }")
        item_info_layout.addRow("كود الصنف:", self.item_code_label)
        
        # وحدة القياس
        unit = self.item_data.get('unit', 'قطعة')
        self.unit_label = QLabel(unit)
        self.unit_label.setStyleSheet("QLabel { color: #7f8c8d; }")
        item_info_layout.addRow("وحدة القياس:", self.unit_label)
        
        main_layout.addWidget(item_info_group)
        
        # مجموعة إدخال البيانات
        input_group = QGroupBox("إدخال الكمية والسعر")
        input_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #27ae60;
                background-color: white;
            }
        """)
        input_layout = QFormLayout(input_group)
        
        # الكمية
        self.quantity_edit = QLineEdit()
        self.quantity_edit.setPlaceholderText("أدخل الكمية...")
        self.quantity_edit.setText("1")
        self.quantity_edit.setStyleSheet(self.get_input_style())
        input_layout.addRow("الكمية:", self.quantity_edit)

        # السعر الوحدة
        self.unit_price_edit = QLineEdit()
        self.unit_price_edit.setPlaceholderText("أدخل سعر الوحدة...")
        self.unit_price_edit.setText("1.00")
        self.unit_price_edit.setStyleSheet(self.get_input_style())
        input_layout.addRow("سعر الوحدة:", self.unit_price_edit)

        # تاريخ الإنتاج
        self.production_date_edit = FlexibleDateEdit()
        self.production_date_edit.setDate(QDate.currentDate())
        self.production_date_edit.setStyleSheet(self.get_input_style())
        input_layout.addRow("تاريخ الإنتاج:", self.production_date_edit)

        # تاريخ الانتهاء
        self.expiry_date_edit = FlexibleDateEdit()
        self.expiry_date_edit.setDate(QDate.currentDate().addDays(365))  # سنة من الآن افتراضياً
        self.expiry_date_edit.setStyleSheet(self.get_input_style())
        input_layout.addRow("تاريخ الانتهاء:", self.expiry_date_edit)

        # المجموع (للقراءة فقط)
        self.total_price_label = QLabel("1.00")
        self.total_price_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
                color: #495057;
            }
        """)
        input_layout.addRow("المجموع:", self.total_price_label)
        
        main_layout.addWidget(input_group)
        
        # مجموعة الملاحظات
        notes_group = QGroupBox("ملاحظات (اختياري)")
        notes_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #f39c12;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #f39c12;
                background-color: white;
            }
        """)
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات خاصة بهذا الصنف...")
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 11px;
            }
            QTextEdit:focus {
                border-color: #f39c12;
            }
        """)
        notes_layout.addWidget(self.notes_edit)
        
        main_layout.addWidget(notes_group)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        button_box.setLayoutDirection(Qt.RightToLeft)
        
        # زر موافق
        ok_button = button_box.addButton("موافق", QDialogButtonBox.AcceptRole)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        
        # زر إلغاء
        cancel_button = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        
        button_box.accepted.connect(self.accept_data)
        button_box.rejected.connect(self.reject)
        
        main_layout.addWidget(button_box)
    
    def get_input_style(self):
        """الحصول على تنسيق الحقول"""
        return """
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        # تحديث المجموع عند تغيير الكمية أو السعر
        self.quantity_edit.textChanged.connect(self.update_total)
        self.unit_price_edit.textChanged.connect(self.update_total)
    
    def update_total(self):
        """تحديث المجموع"""
        try:
            # تحويل النص إلى أرقام مع التحقق من الصحة
            quantity_text = self.quantity_edit.text().strip()
            unit_price_text = self.unit_price_edit.text().strip()

            # التحقق من وجود قيم
            if not quantity_text or not unit_price_text:
                self.total_price_label.setText("0.00")
                return

            # تحويل النصوص إلى أرقام
            quantity = float(quantity_text.replace(',', '.'))
            unit_price = float(unit_price_text.replace(',', '.'))

            # التحقق من أن القيم موجبة
            if quantity <= 0 or unit_price <= 0:
                self.total_price_label.setText("0.00")
                return

            total = quantity * unit_price
            self.total_price_label.setText(f"{total:.2f}")
        except (ValueError, AttributeError) as e:
            # في حالة خطأ في التحويل، عرض صفر
            self.total_price_label.setText("0.00")
        except Exception as e:
            print(f"خطأ في حساب المجموع: {str(e)}")
            self.total_price_label.setText("0.00")
    
    def load_item_data(self):
        """تحميل بيانات الصنف الموجودة"""
        try:
            if 'quantity' in self.item_data:
                self.quantity_edit.setText(str(self.item_data['quantity']))
            if 'unit_price' in self.item_data:
                self.unit_price_edit.setText(str(self.item_data['unit_price']))
            if 'notes' in self.item_data:
                self.notes_edit.setPlainText(str(self.item_data['notes']))

            # تحديث المجموع
            self.update_total()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الصنف: {str(e)}")
    
    def accept_data(self):
        """قبول البيانات والتحقق من صحتها"""
        try:
            # التحقق من صحة البيانات
            quantity_text = self.quantity_edit.text().strip()
            unit_price_text = self.unit_price_edit.text().strip()

            # التحقق من وجود قيم
            if not quantity_text:
                QMessageBox.warning(self, "خطأ", "يجب إدخال الكمية")
                self.quantity_edit.setFocus()
                return

            if not unit_price_text:
                QMessageBox.warning(self, "خطأ", "يجب إدخال سعر الوحدة")
                self.unit_price_edit.setFocus()
                return

            # تحويل النصوص إلى أرقام
            try:
                quantity = float(quantity_text.replace(',', '.'))
                unit_price = float(unit_price_text.replace(',', '.'))
            except ValueError:
                QMessageBox.warning(self, "خطأ", "يجب إدخال أرقام صحيحة للكمية والسعر")
                return

            # التحقق من أن القيم موجبة
            if quantity <= 0:
                QMessageBox.warning(self, "خطأ", "يجب أن تكون الكمية أكبر من صفر")
                self.quantity_edit.setFocus()
                return

            if unit_price <= 0:
                QMessageBox.warning(self, "خطأ", "يجب أن يكون السعر أكبر من صفر")
                self.unit_price_edit.setFocus()
                return

            # حفظ البيانات
            self.quantity = quantity
            self.unit_price = unit_price
            self.total_price = self.quantity * self.unit_price
            self.production_date = self.production_date_edit.date().toString("yyyy-MM-dd")
            self.expiry_date = self.expiry_date_edit.date().toString("yyyy-MM-dd")
            self.notes = self.notes_edit.toPlainText().strip()

            # قبول الحوار
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات: {str(e)}")
    
    def get_item_data(self):
        """الحصول على بيانات الصنف المدخلة"""
        return {
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'total_price': self.total_price,
            'production_date': self.production_date,
            'expiry_date': self.expiry_date,
            'notes': self.notes
        }
