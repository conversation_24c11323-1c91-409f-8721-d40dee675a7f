#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أمثلة استخدام مكتبة القوالب
Template Library Usage Examples
"""

import sys
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

# استيراد مكتبة القوالب
from template_library import TemplateLibrary, Templates, create_template, نموذج_ادخال_اساسي

def example_1_basic_usage():
    """مثال 1: الاستخدام الأساسي"""
    print("\n" + "="*50)
    print("📋 مثال 1: الاستخدام الأساسي")
    print("="*50)
    
    # الطريقة الأولى: استخدام الكلاس الرئيسي
    template1 = TemplateLibrary.create("نموذج_ادخال_اساسي", fullscreen=False)
    print("✅ تم إنشاء القالب باستخدام TemplateLibrary.create()")

    # الطريقة الثانية: استخدام الاختصار
    template2 = Templates.create("نموذج_ادخال_اساسي", fullscreen=False)
    print("✅ تم إنشاء القالب باستخدام Templates.create()")
    
    return [template1, template2]

def example_2_direct_method():
    """مثال 2: الطريقة المباشرة"""
    print("\n" + "="*50)
    print("📋 مثال 2: الطريقة المباشرة")
    print("="*50)
    
    # استخدام الطريقة المباشرة للقالب
    template = Templates.نموذج_ادخال_اساسي(fullscreen=False)
    print("✅ تم إنشاء القالب باستخدام الطريقة المباشرة")
    
    return template

def example_3_function_shortcuts():
    """مثال 3: اختصارات الدوال"""
    print("\n" + "="*50)
    print("📋 مثال 3: اختصارات الدوال")
    print("="*50)
    
    # استخدام الدالة المختصرة العامة
    template1 = create_template("نموذج_ادخال_اساسي", fullscreen=True)
    print("✅ تم إنشاء القالب باستخدام create_template()")
    
    # استخدام الدالة المختصرة المخصصة
    template2 = نموذج_ادخال_اساسي(fullscreen=False)
    print("✅ تم إنشاء القالب باستخدام نموذج_ادخال_اساسي()")
    
    return [template1, template2]

def example_4_template_management():
    """مثال 4: إدارة القوالب"""
    print("\n" + "="*50)
    print("📋 مثال 4: إدارة القوالب")
    print("="*50)
    
    # الحصول على مدير القوالب
    manager = Templates.get_manager()
    
    # إنشاء عدة قوالب
    template1 = Templates.create("نموذج_ادخال_اساسي", fullscreen=True)
    template2 = Templates.create("نموذج_ادخال_اساسي", fullscreen=False)
    
    # عرض القوالب النشطة
    active_templates = manager.get_active_templates()
    print(f"📊 عدد القوالب النشطة: {len(active_templates)}")
    
    for instance_id, template_data in active_templates.items():
        info = template_data['info']
        print(f"   • {info['display_name']} - {template_data['created_date']}")
    
    return [template1, template2]

def example_5_template_info():
    """مثال 5: معلومات القوالب"""
    print("\n" + "="*50)
    print("📋 مثال 5: معلومات القوالب")
    print("="*50)
    
    # عرض جميع القوالب المتاحة
    all_templates = Templates.list_all()
    print(f"📋 القوالب المتاحة ({len(all_templates)}):")
    for template_name in all_templates:
        print(f"   • {template_name}")
    
    # عرض معلومات قالب محدد
    print("\n📋 معلومات نموذج الإدخال الأساسي:")
    Templates.info("نموذج_ادخال_اساسي")
    
    # البحث في القوالب
    search_results = Templates.search("أساسي")
    print(f"\n🔍 نتائج البحث عن 'أساسي': {len(search_results)} نتيجة")

def example_6_custom_parameters():
    """مثال 6: معاملات مخصصة"""
    print("\n" + "="*50)
    print("📋 مثال 6: معاملات مخصصة")
    print("="*50)
    
    # إنشاء قالب مع معاملات مخصصة
    template = Templates.create(
        "نموذج_ادخال_اساسي",
        fullscreen=True,
        # يمكن إضافة معاملات إضافية هنا
    )
    
    if template:
        # تخصيص القالب بعد الإنشاء
        template.setWindowTitle("نموذج مخصص - SHIPMENT ERP")
        
        # ملء بيانات تجريبية
        variables = template.get_form_variables()
        if 'document_number' in variables:
            variables['document_number'].setText("CUSTOM-001")
        if 'customer_name' in variables:
            variables['customer_name'].setText("عميل مخصص")
        
        print("✅ تم تخصيص القالب بنجاح")
    
    return template

def example_7_event_handling():
    """مثال 7: معالجة الأحداث"""
    print("\n" + "="*50)
    print("📋 مثال 7: معالجة الأحداث")
    print("="*50)
    
    # الحصول على مدير القوالب
    manager = Templates.get_manager()
    
    # ربط معالجات الأحداث
    def on_template_created(template_name, template_instance):
        print(f"🎉 تم إنشاء قالب جديد: {template_name}")
        print(f"   العنوان: {template_instance.windowTitle()}")
    
    def on_template_closed(template_name):
        print(f"🔒 تم إغلاق القالب: {template_name}")
    
    # ربط الإشارات
    manager.template_created.connect(on_template_created)
    manager.template_closed.connect(on_template_closed)
    
    # إنشاء قالب لاختبار الأحداث
    template = Templates.create("نموذج_ادخال_اساسي")
    
    print("✅ تم ربط معالجات الأحداث")
    return template

def run_all_examples():
    """تشغيل جميع الأمثلة"""
    print("🚀 بدء تشغيل أمثلة مكتبة القوالب")
    print("="*70)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    Templates.setup_app(app)
    
    # تشغيل الأمثلة
    templates = []
    
    try:
        # مثال 1: الاستخدام الأساسي
        templates.extend(example_1_basic_usage())
        
        # مثال 2: الطريقة المباشرة
        templates.append(example_2_direct_method())
        
        # مثال 3: اختصارات الدوال
        templates.extend(example_3_function_shortcuts())
        
        # مثال 4: إدارة القوالب
        templates.extend(example_4_template_management())
        
        # مثال 5: معلومات القوالب
        example_5_template_info()
        
        # مثال 6: معاملات مخصصة
        templates.append(example_6_custom_parameters())
        
        # مثال 7: معالجة الأحداث
        templates.append(example_7_event_handling())
        
        print("\n" + "="*70)
        print(f"✅ تم إنشاء {len([t for t in templates if t])} قالب بنجاح!")
        print("🎯 يمكنك الآن التفاعل مع القوالب")
        print("❌ اضغط Ctrl+C لإنهاء البرنامج")
        print("="*70)
        
        # رسالة ترحيب
        if templates and any(templates):
            QMessageBox.information(
                None, "مكتبة القوالب",
                f"🎉 مرحباً بك في مكتبة القوالب!\n\n"
                f"تم إنشاء {len([t for t in templates if t])} قالب بنجاح.\n\n"
                f"الميزات المتاحة:\n"
                f"• ملء الشاشة (F11)\n"
                f"• العرض العادي (Escape)\n"
                f"• جميع وظائف النموذج\n\n"
                f"استمتع بالاستخدام!"
            )
        
        # تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الأمثلة: {e}")
        return 1

def interactive_demo():
    """عرض تفاعلي"""
    print("🎮 العرض التفاعلي لمكتبة القوالب")
    print("="*50)
    
    app = QApplication(sys.argv)
    Templates.setup_app(app)
    
    while True:
        print("\n📋 اختر نوع القالب:")
        print("1. نموذج إدخال أساسي (ملء الشاشة)")
        print("2. نموذج إدخال أساسي (عرض عادي)")
        print("3. عرض معلومات القوالب")
        print("4. البحث في القوالب")
        print("5. خروج")
        
        try:
            choice = input("\nاختيارك (1-5): ").strip()
            
            if choice == "1":
                template = Templates.نموذج_ادخال_اساسي(fullscreen=True)
                if template:
                    print("✅ تم إنشاء نموذج إدخال أساسي (ملء الشاشة)")
                    return app.exec()
            
            elif choice == "2":
                template = Templates.نموذج_ادخال_اساسي(fullscreen=False)
                if template:
                    print("✅ تم إنشاء نموذج إدخال أساسي (عرض عادي)")
                    return app.exec()
            
            elif choice == "3":
                Templates.info("نموذج_ادخال_اساسي")
            
            elif choice == "4":
                query = input("أدخل كلمة البحث: ").strip()
                results = Templates.search(query)
                print(f"🔍 نتائج البحث: {len(results)} نتيجة")
                for name, info in results.items():
                    print(f"   • {info['display_name']}")
            
            elif choice == "5":
                print("👋 شكراً لاستخدام مكتبة القوالب!")
                break
            
            else:
                print("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى")
        
        except KeyboardInterrupt:
            print("\n👋 تم إنهاء البرنامج")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    return 0

def main():
    """الدالة الرئيسية"""
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        return interactive_demo()
    else:
        return run_all_examples()

if __name__ == "__main__":
    sys.exit(main())
