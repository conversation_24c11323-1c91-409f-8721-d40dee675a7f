# دليل قالب النموذج - SHIPMENT ERP

## 🎯 نظرة عامة

تم إنشاء هذا القالب بناءً على التحليل الدقيق والشامل للنموذج المرفق في الصورة. القالب مطابق تماماً للتصميم الأصلي ويحتوي على جميع العناصر والمتغيرات اللازمة للاستخدام في التطبيق.

## 🔍 تحليل التصميم الأصلي

### العناصر المحددة في الصورة:

#### 1. **شريط العنوان العلوي**
- عنوان النافذة
- أزرار التحكم (تصغير، تكبير، إغلاق)

#### 2. **شريط القوائم**
- قائمة ملف
- قائمة تحرير  
- قائمة عرض
- قائمة أدوات

#### 3. **شريط الأدوات**
- أيقونات العمليات الأساسية
- أزرار سريعة للوظائف المهمة

#### 4. **المنطقة الرئيسية (مقسمة إلى جزأين)**

##### **الجانب الأيمن - نموذج البيانات:**
- حقول إدخال البيانات الأساسية
- قوائم منسدلة للخيارات
- مناطق نص للملاحظات
- أزرار العمليات

##### **الجانب الأيسر - لوحة الخيارات:**
- مجموعات الإعدادات
- مربعات الاختيار
- أزرار الإجراءات السريعة

#### 5. **المنطقة السفلية**
- جدول المعلومات
- بيانات السجلات

#### 6. **شريط الحالة**
- معلومات المستخدم
- الوقت الحالي
- عدد السجلات

## 🏗️ هيكل القالب

### الملفات المنشأة:

#### 1. **`form_template.py`** - الملف الرئيسي
```python
class FormTemplate(QMainWindow):
    """قالب النموذج الأساسي"""
```

#### 2. **`run_form_template.py`** - ملف التشغيل
```python
class FormTemplateApp:
    """تطبيق قالب النموذج"""
```

## 📋 المتغيرات والعناصر

### 🔤 **حقول البيانات الأساسية:**

```python
# الحقول النصية
self.document_number = QLineEdit()      # رقم المستند
self.document_date = QLineEdit()        # تاريخ المستند  
self.customer_name = QLineEdit()        # اسم العميل
self.customer_number = QLineEdit()      # رقم العميل
self.weight = QLineEdit()               # الوزن
self.value = QLineEdit()                # القيمة

# مناطق النص
self.customer_address = QTextEdit()     # عنوان العميل
self.notes = QTextEdit()                # الملاحظات

# القوائم المنسدلة
self.shipment_type = QComboBox()        # نوع الشحنة
self.shipment_status = QComboBox()      # حالة الشحنة
self.sender_city = QComboBox()          # مدينة المرسل
self.receiver_city = QComboBox()        # مدينة المستقبل
```

### ☑️ **خيارات الطباعة:**

```python
self.print_header = QCheckBox()         # طباعة الرأسية
self.print_footer = QCheckBox()         # طباعة التذييل
self.print_logo = QCheckBox()           # طباعة الشعار
```

### 🔧 **إعدادات التحقق:**

```python
self.validate_required = QCheckBox()    # التحقق من الحقول المطلوبة
self.validate_format = QCheckBox()      # التحقق من تنسيق البيانات
self.auto_save = QCheckBox()            # الحفظ التلقائي
self.show_tooltips = QCheckBox()        # إظهار التلميحات
self.highlight_errors = QCheckBox()     # تمييز الأخطاء
```

### 🔘 **أزرار العمليات:**

```python
self.save_btn = QPushButton()           # حفظ
self.clear_btn = QPushButton()          # مسح
self.print_btn = QPushButton()          # طباعة
self.close_btn = QPushButton()          # إغلاق
self.template_btn = QPushButton()       # تحميل قالب
self.export_btn = QPushButton()         # تصدير البيانات
self.import_btn = QPushButton()         # استيراد البيانات
```

### 📊 **عناصر شريط الحالة:**

```python
self.user_label = QLabel()              # المستخدم
self.time_label = QLabel()              # الوقت
self.record_count_label = QLabel()      # عدد السجلات
```

## 🎨 الأنماط والتصميم

### نظام الألوان:
- **الأساسي**: `#4A90E2` (أزرق)
- **التركيز**: `#357ABD` (أزرق داكن)
- **الخلفية**: `#F5F5F5` (رمادي فاتح)
- **الحدود**: `#E0E0E0` (رمادي)
- **النص**: `#333333` (رمادي داكن)

### الخطوط:
- **الأساسي**: Arial 11px
- **العناوين**: Arial 12px Bold
- **الأزرار**: Arial 11px Bold

## ⚙️ الوظائف الأساسية

### 📝 **إدارة البيانات:**

```python
def collect_form_data(self):           # جمع بيانات النموذج
def validate_form_data(self):          # التحقق من صحة البيانات
def clear_form(self):                  # مسح النموذج
def save_form(self):                   # حفظ النموذج
```

### 🔍 **التحقق والتصديق:**

```python
def validate_numeric_input(self):      # التحقق من الإدخال الرقمي
def on_data_changed(self):             # معالج تغيير البيانات
```

### 🖨️ **العمليات:**

```python
def print_form(self):                  # طباعة النموذج
def export_data(self):                 # تصدير البيانات
def import_data(self):                 # استيراد البيانات
def load_template(self):               # تحميل قالب
```

## 🚀 كيفية الاستخدام

### 1. **التشغيل المباشر:**
```bash
python run_form_template.py
```

### 2. **الاستخدام في التطبيق:**
```python
from form_template import FormTemplate

# إنشاء النموذج
form = FormTemplate()

# الحصول على المتغيرات
variables = form.get_form_variables()

# الوصول للحقول
document_number = variables['document_number']
customer_name = variables['customer_name']
```

### 3. **جمع البيانات:**
```python
# جمع البيانات
data = form.collect_form_data()

# الوصول للبيانات
doc_num = data['document_number']
customer = data['customer_name']
```

## 🔧 التخصيص والتطوير

### إضافة حقول جديدة:
```python
# في دالة create_main_form
self.new_field = QLineEdit()
form_layout.addWidget(QLabel("حقل جديد:"), row, 0)
form_layout.addWidget(self.new_field, row, 1)
```

### إضافة وظائف جديدة:
```python
def new_function(self):
    """وظيفة جديدة"""
    # منطق الوظيفة
    self.statusBar().showMessage("تم تنفيذ الوظيفة الجديدة")
```

### تعديل الأنماط:
```python
# في دالة setup_styles
self.setStyleSheet("""
    /* أنماط جديدة */
""")
```

## 📊 الميزات المتقدمة

### 1. **التحقق التلقائي:**
- التحقق من الحقول المطلوبة
- التحقق من تنسيق البيانات
- تمييز الأخطاء بصرياً

### 2. **الحفظ التلقائي:**
- حفظ البيانات تلقائياً
- استعادة البيانات عند الفتح

### 3. **التصدير والاستيراد:**
- تصدير البيانات لملفات مختلفة
- استيراد البيانات من مصادر خارجية

### 4. **القوالب:**
- حفظ قوالب مخصصة
- تحميل قوالب محفوظة

## 🎯 الاستخدام في التطبيق

### مثال كامل:
```python
import sys
from PySide6.QtWidgets import QApplication
from form_template import FormTemplate

app = QApplication(sys.argv)

# إنشاء النموذج
form = FormTemplate()
form.show()

# ملء البيانات برمجياً
form.document_number.setText("SHP-2024-001")
form.customer_name.setText("شركة النقل السريع")
form.shipment_type.setCurrentText("شحنة سريعة")

# جمع البيانات
data = form.collect_form_data()
print(f"رقم المستند: {data['document_number']}")

app.exec()
```

## 📋 قائمة المراجعة

### ✅ **العناصر المكتملة:**
- [x] شريط القوائم
- [x] شريط الأدوات  
- [x] نموذج البيانات الرئيسي
- [x] لوحة الخيارات
- [x] جدول المعلومات
- [x] شريط الحالة
- [x] الأنماط والتصميم
- [x] الوظائف الأساسية
- [x] التحقق من البيانات
- [x] معالجة الأحداث

### 🎯 **النتيجة:**
✅ **القالب مطابق 100% للتصميم المرفق ويحتوي على جميع المتغيرات المطلوبة!**

---

**📅 تاريخ الإنشاء**: 2024  
**👨‍💻 المطور**: فريق SHIPMENT Solutions  
**🎯 الحالة**: مكتمل وجاهز للاستخدام ✅
