# مكتبة القوالب - SHIPMENT ERP Template Library

## 🎯 نظرة عامة

**مكتبة القوالب** هي نظام شامل ومتطور لإدارة واستدعاء جميع قوالب النماذج في مشروع SHIPMENT ERP. تم تطويرها لتوفير طريقة سهلة ومرنة لإنشاء واستخدام القوالب مع دعم ملء الشاشة والتخصيص المتقدم.

## ✨ الميزات الرئيسية

### 🏛️ **إدارة شاملة للقوالب**
- سجل مركزي لجميع القوالب
- تصنيف وتنظيم حسب الفئات
- نظام بحث متقدم
- معلومات تفصيلية لكل قالب

### 🖥️ **دعم ملء الشاشة**
- وضع ملء الشاشة افتراضي
- التبديل السريع (F11)
- العرض العادي عند الحاجة
- حفظ تفضيلات العرض

### ⚡ **اختصارات سريعة**
- أسماء مخصصة لكل قالب
- 4 طرق مختلفة للاستدعاء
- واجهة برمجية بسيطة
- استخدام سهل ومرن

## 📋 القوالب المتاحة

### 📝 **نموذج إدخال أساسي**
- **الاسم المرجعي**: `نموذج_ادخال_اساسي`
- **الاسم المعروض**: نموذج إدخال أساسي
- **الوصف**: نموذج أساسي لإدخال بيانات الشحنات والعملاء
- **المتغيرات**: 30 متغير جاهز للاستخدام
- **الوظائف**: 20+ وظيفة متكاملة

## 🚀 طرق الاستخدام

### 1. **الطريقة المباشرة (الأسرع)**
```python
from template_library import نموذج_ادخال_اساسي

# ملء الشاشة
template = نموذج_ادخال_اساسي(fullscreen=True)

# العرض العادي
template = نموذج_ادخال_اساسي(fullscreen=False)
```

### 2. **الطريقة العامة**
```python
from template_library import Templates

template = Templates.create("نموذج_ادخال_اساسي", fullscreen=True)
```

### 3. **الطريقة المختصرة**
```python
from template_library import Templates

template = Templates.نموذج_ادخال_اساسي(fullscreen=True)
```

### 4. **الدالة المختصرة**
```python
from template_library import create_template

template = create_template("نموذج_ادخال_اساسي", fullscreen=True)
```

## 🔧 الاستخدام المتقدم

### **إعداد التطبيق**
```python
from template_library import Templates
from PySide6.QtWidgets import QApplication

app = QApplication(sys.argv)
Templates.setup_app(app)
```

### **التعامل مع المتغيرات**
```python
# إنشاء القالب
template = Templates.نموذج_ادخال_اساسي()

# الحصول على المتغيرات
variables = template.get_form_variables()

# تخصيص البيانات
variables['document_number'].setText("SHP-001")
variables['customer_name'].setText("اسم العميل")
variables['save_btn'].clicked.connect(my_save_function)

# جمع البيانات
data = template.collect_form_data()
```

### **إدارة القوالب**
```python
# عرض القوالب المتاحة
templates = Templates.list_all()

# البحث في القوالب
results = Templates.search("أساسي")

# معلومات تفصيلية
Templates.info("نموذج_ادخال_اساسي")

# إدارة القوالب النشطة
manager = Templates.get_manager()
active_templates = manager.get_active_templates()
```

## 📁 الملفات المتضمنة

### **الملفات الأساسية**
- **`template_library.py`** - المكتبة الرئيسية
- **`form_template.py`** - القالب الأساسي المحدث
- **`run_template_library.py`** - نافذة اختيار القوالب
- **`template_examples.py`** - أمثلة شاملة
- **`test_template_library.py`** - اختبارات شاملة
- **`quick_template_demo.py`** - عرض سريع

### **ملفات التوثيق**
- **`TEMPLATE_LIBRARY_MAIN_README.md`** - هذا الملف
- **`TEMPLATE_LIBRARY_README.md`** - دليل المكتبة المفصل
- **`ملخص_مكتبة_القوالب.md`** - ملخص المشروع
- **`دليل_قالب_النموذج.md`** - دليل القالب الأساسي

## 🧪 التشغيل والاختبار

### **اختبار المكتبة**
```bash
python test_template_library.py
```

### **نافذة اختيار القوالب**
```bash
python run_template_library.py
```

### **الأمثلة الشاملة**
```bash
# جميع الأمثلة
python template_examples.py

# العرض التفاعلي
python template_examples.py --interactive
```

### **العرض السريع**
```bash
# العرض الأساسي
python quick_template_demo.py

# عرض جميع الطرق
python quick_template_demo.py --all

# العرض التفاعلي
python quick_template_demo.py --interactive
```

## 🖥️ وظائف ملء الشاشة

### **التحكم في وضع العرض**
```python
# إنشاء قالب بملء الشاشة
template = Templates.نموذج_ادخال_اساسي(fullscreen=True)

# التبديل لملء الشاشة
template.maximize_view()

# التبديل للعرض العادي
template.normal_view()

# التبديل التلقائي
template.toggle_fullscreen()
```

### **اختصارات لوحة المفاتيح**
- **F11**: تبديل ملء الشاشة
- **Escape**: العرض العادي (من ملء الشاشة)

## 🔧 إضافة قوالب جديدة

### **تسجيل قالب جديد**
```python
from template_library import Templates
from my_custom_template import MyCustomTemplate

Templates.register_new(
    name="قالب_مخصص",
    display_name="قالب مخصص",
    description="وصف القالب المخصص",
    template_class=MyCustomTemplate,
    category="قوالب_مخصصة",
    version="1.0.0",
    author="اسم المطور",
    tags=["مخصص", "جديد"]
)

# استخدام القالب الجديد
template = Templates.create("قالب_مخصص")
```

## 📊 الإحصائيات

### **نتائج الاختبار**
```
✅ Python 3.13.5
✅ PySide6 6.9.1
✅ جميع الملفات متوفرة (8 ملفات)
✅ جميع المكونات تعمل (7 مكونات)
✅ 1 قالب مسجل ومتاح
✅ 30 متغير جاهز للاستخدام
✅ 4 طرق استدعاء مختلفة
✅ وظائف ملء الشاشة تعمل
🎉 جميع الاختبارات نجحت!
```

### **الأرقام**
- **📁 الملفات**: 8 ملفات
- **📝 أسطر الكود**: 2000+ سطر
- **🏛️ المكونات**: 3 كلاسات أساسية
- **🔤 المتغيرات**: 30 متغير
- **⚡ طرق الاستدعاء**: 4 طرق
- **🧪 الاختبارات**: 7 اختبارات
- **📖 الأمثلة**: 7 أمثلة

## 🎯 أمثلة سريعة

### **مثال 1: الاستخدام الأساسي**
```python
from template_library import نموذج_ادخال_اساسي

# إنشاء النموذج
template = نموذج_ادخال_اساسي(fullscreen=True)

# ملء البيانات
variables = template.get_form_variables()
variables['document_number'].setText("SHP-001")
variables['customer_name'].setText("شركة النقل")

# عرض النموذج
template.show()
```

### **مثال 2: معالجة البيانات**
```python
def process_shipment_data():
    template = نموذج_ادخال_اساسي()
    
    # جمع البيانات
    data = template.collect_form_data()
    
    # التحقق من البيانات
    if template.validate_form_data():
        print(f"حفظ الشحنة: {data['document_number']}")
        # منطق الحفظ هنا
    else:
        print("خطأ في البيانات")

# ربط الدالة بزر الحفظ
template.save_btn.clicked.connect(process_shipment_data)
```

### **مثال 3: التكامل مع النظام**
```python
class ShipmentSystem:
    def __init__(self):
        self.templates = Templates
        self.templates.setup_app(self.app)
    
    def open_data_entry(self):
        """فتح نموذج إدخال البيانات"""
        return self.templates.نموذج_ادخال_اساسي(fullscreen=True)
    
    def create_custom_form(self, form_type):
        """إنشاء نموذج مخصص"""
        return self.templates.create(form_type, fullscreen=True)
```

## 📞 الدعم والمساعدة

### **للحصول على المساعدة**
1. راجع التوثيق الشامل في `TEMPLATE_LIBRARY_README.md`
2. شغل ملفات الاختبار للتحقق من التثبيت
3. جرب الأمثلة المتوفرة في `template_examples.py`
4. استخدم العرض السريع في `quick_template_demo.py`

### **الإبلاغ عن المشاكل**
- تأكد من تثبيت جميع المتطلبات (Python 3.8+, PySide6)
- شغل `test_template_library.py` للتحقق من الحالة
- راجع رسائل الخطأ في وحدة التحكم

## 🎯 الميزات المستقبلية

- **قوالب إضافية** للنماذج المختلفة
- **نظام ثيمات** للقوالب
- **حفظ التفضيلات** للمستخدمين
- **قوالب ديناميكية** قابلة للتخصيص
- **تصدير واستيراد** القوالب

---

## ✅ النتيجة النهائية

🎉 **مكتبة قوالب شاملة ومتطورة جاهزة للاستخدام!**

### **المنجز**
- ✅ **تطوير القالب** ليفتح في وضع ملء الشاشة
- ✅ **إنشاء مكتبة شاملة** لإدارة القوالب
- ✅ **نظام تسمية مخصص** لكل قالب
- ✅ **نموذج إدخال أساسي** جاهز للاستخدام
- ✅ **4 طرق استدعاء** مختلفة ومرنة
- ✅ **توثيق شامل** وأمثلة عملية
- ✅ **اختبارات شاملة** ومكتملة
- ✅ **واجهات تفاعلية** سهلة الاستخدام

### **الاستخدام الفوري**
```python
# بدء الاستخدام في سطر واحد!
from template_library import نموذج_ادخال_اساسي
template = نموذج_ادخال_اساسي(fullscreen=True)
```

**📅 تاريخ الإنجاز**: 2024  
**👨‍💻 المطور**: فريق SHIPMENT Solutions  
**🎯 الحالة**: مكتمل وجاهز للاستخدام ✅
