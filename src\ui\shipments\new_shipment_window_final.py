#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة شحنة جديدة نهائية مع أزرار التحكم العاملة
هذه النافذة تحل محل النافذة الأصلية المعقدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                               QTabWidget, QWidget, QGroupBox, QFormLayout, 
                               QLineEdit, QComboBox, QTextEdit, QDateEdit, 
                               QSpinBox, QTableWidget, QMessageBox, QLabel,
                               QFrame, QToolBar, QDialogButtonBox, QApplication)
from PySide6.QtCore import Signal, QDate, Qt, QSize
from PySide6.QtGui import QFont, QAction

class NewShipmentWindowFinal(QDialog):
    """نافذة شحنة جديدة نهائية مع أزرار التحكم العاملة"""
    
    shipment_saved = Signal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_shipment_id = None
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🚢 شحنة جديدة - مع أزرار التحكم")
        self.setModal(True)
        self.resize(1000, 750)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        self.create_title_header(main_layout)
        
        # شريط أزرار التحكم
        self.create_control_toolbar(main_layout)
        
        # التبويبات
        self.create_tabs(main_layout)
        
        # أزرار الحفظ والإلغاء
        self.create_dialog_buttons(main_layout)
        
    def create_title_header(self, main_layout):
        """إنشاء عنوان النافذة"""
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 12px;
                padding: 15px;
            }
        """)
        
        title_layout = QHBoxLayout(title_frame)
        
        title_label = QLabel("🚢 نظام إدارة الشحنات - شحنة جديدة")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 20px;
                font-weight: bold;
                padding: 10px;
            }
        """)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        main_layout.addWidget(title_frame)
        
    def create_control_toolbar(self, main_layout):
        """إنشاء شريط أزرار التحكم"""
        # إطار الأزرار
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel | QFrame.Raised)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 3px solid #3498db;
                border-radius: 15px;
                padding: 15px;
                margin: 5px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setSpacing(20)
        toolbar_layout.setContentsMargins(25, 20, 25, 20)

        # زر إضافة
        self.new_button = QPushButton("🆕 إضافة")
        self.new_button.setFixedSize(160, 60)
        self.new_button.setStyleSheet(self.get_button_style("#3498db", "#2980b9"))

        # زر حفظ
        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setFixedSize(160, 60)
        self.save_button.setStyleSheet(self.get_button_style("#27ae60", "#229954"))

        # زر تعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setFixedSize(160, 60)
        self.edit_button.setStyleSheet(self.get_button_style("#f39c12", "#e67e22"))
        self.edit_button.setEnabled(False)

        # زر خروج
        self.exit_button = QPushButton("🚪 خروج")
        self.exit_button.setFixedSize(160, 60)
        self.exit_button.setStyleSheet(self.get_button_style("#e74c3c", "#c0392b"))

        # ترتيب الأزرار
        toolbar_layout.addWidget(self.new_button)
        toolbar_layout.addWidget(self.save_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.exit_button)
        
        main_layout.addWidget(toolbar_frame)
        
    def get_button_style(self, color1, color2):
        """الحصول على نمط الزر"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 {color1}, stop:1 {color2});
                color: white;
                font-weight: bold;
                font-size: 16px;
                border: 3px solid {color2};
                border-radius: 15px;
            }}
            QPushButton:hover {{
                background: {color2};
                border: 3px solid {color1};
                transform: scale(1.05);
            }}
            QPushButton:pressed {{
                background: {color1};
            }}
            QPushButton:disabled {{
                background: #bdc3c7;
                color: #7f8c8d;
                border: 3px solid #95a5a6;
            }}
        """
        
    def create_tabs(self, main_layout):
        """إنشاء التبويبات"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #bdc3c7;
                border-radius: 10px;
                background-color: white;
                padding: 10px;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 12px 25px;
                margin: 3px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
        """)
        
        # إنشاء التبويبات
        self.create_basic_tab()
        self.create_items_tab()
        self.create_financial_tab()
        
        main_layout.addWidget(self.tab_widget)
        
    def create_basic_tab(self):
        """إنشاء التبويب الأساسي"""
        basic_tab = QWidget()
        self.tab_widget.addTab(basic_tab, "📋 البيانات الأساسية")
        
        layout = QVBoxLayout(basic_tab)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #2c3e50;
                background-color: white;
            }
        """)
        
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(15)
        basic_layout.setRowWrapPolicy(QFormLayout.WrapLongRows)
        
        # رقم الشحنة
        self.shipment_number_edit = QLineEdit()
        self.shipment_number_edit.setReadOnly(True)
        self.shipment_number_edit.setText("SH-2024-001")
        self.shipment_number_edit.setStyleSheet(self.get_input_style(readonly=True))
        basic_layout.addRow("رقم الشحنة:", self.shipment_number_edit)
        
        # المورد
        self.supplier_edit = QLineEdit()
        self.supplier_edit.setPlaceholderText("اختر المورد...")
        self.supplier_edit.setStyleSheet(self.get_input_style())
        basic_layout.addRow("المورد:", self.supplier_edit)
        
        # حالة الشحنة
        self.shipment_status_combo = QComboBox()
        self.shipment_status_combo.addItems([
            "تحت الطلب", "مؤكدة", "تم الشحن", "في الطريق", 
            "وصلت الميناء", "في الجمارك", "تم التسليم"
        ])
        self.shipment_status_combo.setStyleSheet(self.get_input_style())
        basic_layout.addRow("حالة الشحنة:", self.shipment_status_combo)
        
        # تاريخ الشحنة
        self.shipment_date_edit = QDateEdit()
        self.shipment_date_edit.setDate(QDate.currentDate())
        self.shipment_date_edit.setCalendarPopup(True)
        self.shipment_date_edit.setStyleSheet(self.get_input_style())
        basic_layout.addRow("تاريخ الشحنة:", self.shipment_date_edit)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(120)
        self.notes_edit.setPlaceholderText("أدخل ملاحظات الشحنة...")
        self.notes_edit.setStyleSheet(self.get_input_style())
        basic_layout.addRow("ملاحظات:", self.notes_edit)
        
        layout.addWidget(basic_group)
        layout.addStretch()
        
    def get_input_style(self, readonly=False):
        """الحصول على نمط الحقول"""
        bg_color = "#f8f9fa" if readonly else "white"
        return f"""
            QLineEdit, QComboBox, QTextEdit, QDateEdit {{
                background-color: {bg_color};
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                min-height: 20px;
            }}
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus, QDateEdit:focus {{
                border: 2px solid #3498db;
                background-color: #f8f9fa;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 30px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border: 2px solid #3498db;
                width: 10px;
                height: 10px;
            }}
        """
        
    def create_items_tab(self):
        """إنشاء تبويب الأصناف"""
        items_tab = QWidget()
        self.tab_widget.addTab(items_tab, "📦 الأصناف")
        
        layout = QVBoxLayout(items_tab)
        layout.setContentsMargins(25, 25, 25, 25)
        
        info_label = QLabel("📦 تبويب الأصناف - قيد التطوير")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #7f8c8d;
                padding: 50px;
                text-align: center;
                border: 2px dashed #bdc3c7;
                border-radius: 10px;
                background-color: #f8f9fa;
            }
        """)
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
    def create_financial_tab(self):
        """إنشاء التبويب المالي"""
        financial_tab = QWidget()
        self.tab_widget.addTab(financial_tab, "💰 البيانات المالية")
        
        layout = QVBoxLayout(financial_tab)
        layout.setContentsMargins(25, 25, 25, 25)
        
        info_label = QLabel("💰 التبويب المالي - قيد التطوير")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #7f8c8d;
                padding: 50px;
                text-align: center;
                border: 2px dashed #bdc3c7;
                border-radius: 10px;
                background-color: #f8f9fa;
            }
        """)
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
    def create_dialog_buttons(self, main_layout):
        """إنشاء أزرار الحوار"""
        button_box = QDialogButtonBox()
        button_box.setStyleSheet("""
            QDialogButtonBox {
                background-color: #f8f9fa;
                border-top: 3px solid #dee2e6;
                padding: 20px;
                border-radius: 10px;
            }
            QPushButton {
                background-color: #6c757d;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        
        # زر حفظ وإغلاق
        save_close_btn = button_box.addButton("حفظ وإغلاق", QDialogButtonBox.AcceptRole)
        save_close_btn.clicked.connect(self.save_and_close)
        
        # زر إلغاء
        cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        cancel_btn.clicked.connect(self.reject)
        
        main_layout.addWidget(button_box)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.new_button.clicked.connect(self.new_shipment)
        self.save_button.clicked.connect(self.save_shipment)
        self.edit_button.clicked.connect(self.edit_shipment)
        self.exit_button.clicked.connect(self.reject)
        
    def new_shipment(self):
        """إنشاء شحنة جديدة"""
        reply = QMessageBox.question(
            self,
            "شحنة جديدة",
            "هل تريد إنشاء شحنة جديدة؟\nسيتم مسح البيانات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.clear_form()
            self.current_shipment_id = None
            self.edit_button.setEnabled(False)
            QMessageBox.information(self, "شحنة جديدة", "✅ تم إنشاء نموذج شحنة جديد")
    
    def save_shipment(self):
        """حفظ الشحنة"""
        try:
            # محاكاة عملية الحفظ
            QMessageBox.information(self, "حفظ", "✅ تم حفظ الشحنة بنجاح")
            self.edit_button.setEnabled(True)
            self.current_shipment_id = 1  # محاكاة ID
            return True
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الشحنة:\n{str(e)}")
            return False
    
    def edit_shipment(self):
        """تعديل الشحنة"""
        QMessageBox.information(self, "تعديل", "✏️ يمكنك الآن تعديل الشحنة")
    
    def save_and_close(self):
        """حفظ وإغلاق النافذة"""
        if self.save_shipment():
            self.accept()
    
    def clear_form(self):
        """مسح النموذج"""
        try:
            self.supplier_edit.clear()
            self.shipment_status_combo.setCurrentIndex(0)
            self.shipment_date_edit.setDate(QDate.currentDate())
            self.notes_edit.clear()
            print("✅ تم مسح النموذج")
        except Exception as e:
            print(f"خطأ في مسح النموذج: {str(e)}")

def main():
    """اختبار النافذة النهائية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = NewShipmentWindowFinal()
    window.show()
    
    print("✅ تم فتح النافذة النهائية مع أزرار التحكم")
    print("🔍 تحقق من وجود:")
    print("   • عنوان أزرق في الأعلى")
    print("   • شريط أزرار ملون: إضافة (أزرق)، حفظ (أخضر)، تعديل (برتقالي)، خروج (أحمر)")
    print("   • تبويبات: البيانات الأساسية، الأصناف، البيانات المالية")
    print("   • أزرار حفظ وإغلاق/إلغاء في الأسفل")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
