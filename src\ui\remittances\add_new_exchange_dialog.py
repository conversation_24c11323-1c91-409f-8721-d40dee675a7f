# -*- coding: utf-8 -*-
"""
نافذة إضافة صراف جديد
Add New Exchange Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QTextEdit, QPushButton,
                               QGroupBox, QCheckBox, QSpinBox, QDoubleSpinBox,
                               QMessageBox, QFrame, QFileDialog, QProgressBar,
                               QTimeEdit, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, Signal, QTimer, QTime
from PySide6.QtGui import QFont, QPixmap, QIcon

import sqlite3
import os
from pathlib import Path
from datetime import datetime

class AddNewExchangeDialog(QDialog):
    """نافذة إضافة صراف جديد"""
    
    exchange_added = Signal(int)  # إشارة عند إضافة صراف جديد
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إضافة صراف جديد - ProShipment")
        self.setMinimumSize(750, 920)
        self.resize(850, 920)
        self.setModal(True)

        # توسيط النافذة
        self.center_window()
        
        # متغيرات النافذة
        self.exchange_logo_path = None
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.setup_validators()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #059669, stop:1 #047857);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        title_layout = QHBoxLayout(title_frame)
        
        title_label = QLabel("💱 إضافة صراف جديد")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                background: transparent;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addWidget(title_frame)
        
        # المعلومات الأساسية
        basic_group = QGroupBox("المعلومات الأساسية")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        basic_layout = QGridLayout(basic_group)
        basic_layout.setSpacing(10)
        
        # اسم الصراف
        basic_layout.addWidget(QLabel("اسم الصراف: *"), 0, 0)
        self.exchange_name_input = QLineEdit()
        self.exchange_name_input.setPlaceholderText("أدخل اسم الصراف...")
        self.exchange_name_input.setMinimumHeight(35)
        basic_layout.addWidget(self.exchange_name_input, 0, 1, 1, 3)

        # اسم الصراف بالإنجليزية
        basic_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 1, 0)
        self.exchange_name_en_input = QLineEdit()
        self.exchange_name_en_input.setPlaceholderText("Exchange Name in English...")
        self.exchange_name_en_input.setMinimumHeight(35)
        basic_layout.addWidget(self.exchange_name_en_input, 1, 1, 1, 3)

        # رمز الصراف
        basic_layout.addWidget(QLabel("رمز الصراف: *"), 2, 0)
        self.exchange_code_input = QLineEdit()
        self.exchange_code_input.setPlaceholderText("مثال: EXC001")
        self.exchange_code_input.setMaxLength(10)
        self.exchange_code_input.setMinimumHeight(35)
        basic_layout.addWidget(self.exchange_code_input, 2, 1)

        # رقم الترخيص
        basic_layout.addWidget(QLabel("رقم الترخيص:"), 2, 2)
        self.license_number_input = QLineEdit()
        self.license_number_input.setPlaceholderText("رقم ترخيص الصراف...")
        self.license_number_input.setMinimumHeight(35)
        basic_layout.addWidget(self.license_number_input, 2, 3)
        
        layout.addWidget(basic_group)
        
        # معلومات الاتصال
        contact_group = QGroupBox("معلومات الاتصال")
        contact_group.setStyleSheet(basic_group.styleSheet())
        contact_layout = QGridLayout(contact_group)
        contact_layout.setSpacing(10)
        
        # العنوان
        contact_layout.addWidget(QLabel("العنوان:"), 0, 0)
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(100)
        self.address_input.setMinimumHeight(100)
        self.address_input.setPlaceholderText("أدخل عنوان الصراف...")
        contact_layout.addWidget(self.address_input, 0, 1, 1, 3)

        # الهاتف
        contact_layout.addWidget(QLabel("الهاتف:"), 1, 0)
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("+966 11 123 4567")
        self.phone_input.setMinimumHeight(35)
        contact_layout.addWidget(self.phone_input, 1, 1)

        # الجوال
        contact_layout.addWidget(QLabel("الجوال:"), 1, 2)
        self.mobile_input = QLineEdit()
        self.mobile_input.setPlaceholderText("+966 50 123 4567")
        self.mobile_input.setMinimumHeight(35)
        contact_layout.addWidget(self.mobile_input, 1, 3)

        # البريد الإلكتروني
        contact_layout.addWidget(QLabel("البريد الإلكتروني:"), 2, 0)
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        self.email_input.setMinimumHeight(35)
        contact_layout.addWidget(self.email_input, 2, 1)

        # الموقع الإلكتروني
        contact_layout.addWidget(QLabel("الموقع الإلكتروني:"), 2, 2)
        self.website_input = QLineEdit()
        self.website_input.setPlaceholderText("https://www.exchange.com")
        self.website_input.setMinimumHeight(35)
        contact_layout.addWidget(self.website_input, 2, 3)
        
        layout.addWidget(contact_group)
        
        # المعلومات المالية والتشغيلية
        financial_group = QGroupBox("المعلومات المالية والتشغيلية")
        financial_group.setStyleSheet(basic_group.styleSheet())
        financial_layout = QGridLayout(financial_group)
        financial_layout.setSpacing(10)
        
        # العملات المدعومة
        financial_layout.addWidget(QLabel("العملات المدعومة:"), 0, 0)
        self.supported_currencies_list = QListWidget()
        self.supported_currencies_list.setMaximumHeight(100)
        self.load_currencies()
        financial_layout.addWidget(self.supported_currencies_list, 0, 1, 2, 3)
        
        # رسوم التحويل
        financial_layout.addWidget(QLabel("رسوم التحويل:"), 2, 0)
        self.transfer_fee_input = QDoubleSpinBox()
        self.transfer_fee_input.setRange(0, 999999)
        self.transfer_fee_input.setDecimals(2)
        self.transfer_fee_input.setSuffix(" ريال")
        self.transfer_fee_input.setMinimumHeight(35)
        financial_layout.addWidget(self.transfer_fee_input, 2, 1)

        # نسبة العمولة
        financial_layout.addWidget(QLabel("نسبة العمولة:"), 2, 2)
        self.commission_rate_input = QDoubleSpinBox()
        self.commission_rate_input.setRange(0, 100)
        self.commission_rate_input.setDecimals(2)
        self.commission_rate_input.setSuffix(" %")
        self.commission_rate_input.setMinimumHeight(35)
        financial_layout.addWidget(self.commission_rate_input, 2, 3)

        # الحد الأدنى للتحويل
        financial_layout.addWidget(QLabel("الحد الأدنى للتحويل:"), 3, 0)
        self.min_transfer_input = QDoubleSpinBox()
        self.min_transfer_input.setRange(0, 999999999)
        self.min_transfer_input.setDecimals(2)
        self.min_transfer_input.setSuffix(" ريال")
        self.min_transfer_input.setMinimumHeight(35)
        financial_layout.addWidget(self.min_transfer_input, 3, 1)

        # الحد الأقصى للتحويل
        financial_layout.addWidget(QLabel("الحد الأقصى للتحويل:"), 3, 2)
        self.max_transfer_input = QDoubleSpinBox()
        self.max_transfer_input.setRange(0, 999999999)
        self.max_transfer_input.setDecimals(2)
        self.max_transfer_input.setValue(500000)
        self.max_transfer_input.setSuffix(" ريال")
        self.max_transfer_input.setMinimumHeight(35)
        financial_layout.addWidget(self.max_transfer_input, 3, 3)
        
        layout.addWidget(financial_group)

        # الشعار والملاحظات
        extra_group = QGroupBox("معلومات إضافية")
        extra_group.setStyleSheet(basic_group.styleSheet())
        extra_layout = QGridLayout(extra_group)
        extra_layout.setSpacing(10)
        
        # شعار الصراف
        extra_layout.addWidget(QLabel("شعار الصراف:"), 0, 0)
        logo_layout = QHBoxLayout()
        self.logo_path_input = QLineEdit()
        self.logo_path_input.setPlaceholderText("اختر ملف الشعار...")
        self.logo_path_input.setReadOnly(True)
        logo_layout.addWidget(self.logo_path_input)
        
        self.browse_logo_btn = QPushButton("تصفح...")
        self.browse_logo_btn.setMaximumWidth(80)
        logo_layout.addWidget(self.browse_logo_btn)
        
        extra_layout.addLayout(logo_layout, 0, 1, 1, 3)
        
        # الملاحظات
        extra_layout.addWidget(QLabel("ملاحظات:"), 1, 0)
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        extra_layout.addWidget(self.notes_input, 1, 1, 1, 3)
        
        # خيارات إضافية
        options_layout = QHBoxLayout()
        self.is_active_checkbox = QCheckBox("الصراف نشط")
        self.is_active_checkbox.setChecked(True)
        options_layout.addWidget(self.is_active_checkbox)
        
        self.online_service_checkbox = QCheckBox("خدمة إلكترونية")
        options_layout.addWidget(self.online_service_checkbox)
        
        self.home_delivery_checkbox = QCheckBox("توصيل منزلي")
        options_layout.addWidget(self.home_delivery_checkbox)
        
        options_layout.addStretch()
        extra_layout.addLayout(options_layout, 2, 0, 1, 4)
        
        layout.addWidget(extra_group)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # أزرار الإجراءات
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.addStretch()
        
        self.save_btn = QPushButton("💾 حفظ الصراف")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:pressed {
                background-color: #047857;
            }
        """)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #dc2626;
            }
            QPushButton:pressed {
                background-color: #b91c1c;
            }
        """)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_exchange)
        self.cancel_btn.clicked.connect(self.reject)
        self.browse_logo_btn.clicked.connect(self.browse_logo)
        
        # التحقق من صحة البيانات عند التغيير
        self.exchange_name_input.textChanged.connect(self.validate_form)
        self.exchange_code_input.textChanged.connect(self.validate_form)
        
    def setup_validators(self):
        """إعداد مدققات الإدخال"""
        # تحويل رمز الصراف للأحرف الكبيرة
        self.exchange_code_input.textChanged.connect(
            lambda: self.exchange_code_input.setText(self.exchange_code_input.text().upper())
        )
        
    def load_currencies(self):
        """تحميل العملات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # التأكد من وجود جدول العملات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS currencies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    symbol TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT,
                    updated_at TEXT
                )
            """)

            # إضافة عملات افتراضية إذا لم تكن موجودة
            default_currencies = [
                ('SAR', 'الريال السعودي', 'Saudi Riyal', 'ر.س'),
                ('USD', 'الدولار الأمريكي', 'US Dollar', '$'),
                ('EUR', 'اليورو', 'Euro', '€'),
                ('AED', 'الدرهم الإماراتي', 'UAE Dirham', 'د.إ'),
                ('KWD', 'الدينار الكويتي', 'Kuwaiti Dinar', 'د.ك'),
                ('QAR', 'الريال القطري', 'Qatari Riyal', 'ر.ق'),
                ('BHD', 'الدينار البحريني', 'Bahraini Dinar', 'د.ب'),
                ('OMR', 'الريال العماني', 'Omani Rial', 'ر.ع')
            ]

            for code, name, name_en, symbol in default_currencies:
                cursor.execute("""
                    INSERT OR IGNORE INTO currencies (code, name, name_en, symbol, is_active, created_at)
                    VALUES (?, ?, ?, ?, 1, ?)
                """, (code, name, name_en, symbol, datetime.now().isoformat()))

            conn.commit()

            cursor.execute("SELECT id, code, name FROM currencies WHERE is_active = 1 ORDER BY name")
            currencies = cursor.fetchall()

            for currency in currencies:
                item = QListWidgetItem(f"{currency[1]} - {currency[2]}")
                item.setData(Qt.UserRole, currency[0])
                item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
                item.setCheckState(Qt.Unchecked)

                # تحديد العملات الافتراضية
                if currency[1] in ['SAR', 'USD', 'EUR']:
                    item.setCheckState(Qt.Checked)

                self.supported_currencies_list.addItem(item)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل العملات: {e}")
            # إضافة عملات افتراضية
            default_currencies = [
                ("SAR", "الريال السعودي", 1),
                ("USD", "الدولار الأمريكي", 2),
                ("EUR", "اليورو", 3),
                ("AED", "الدرهم الإماراتي", 4)
            ]

            for code, name, currency_id in default_currencies:
                item = QListWidgetItem(f"{code} - {name}")
                item.setData(Qt.UserRole, currency_id)
                item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
                item.setCheckState(Qt.Checked if code in ['SAR', 'USD'] else Qt.Unchecked)
                self.supported_currencies_list.addItem(item)
    
    def browse_logo(self):
        """تصفح ملف الشعار"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر شعار الصراف",
            "",
            "Image Files (*.png *.jpg *.jpeg *.gif *.bmp)"
        )
        
        if file_path:
            self.exchange_logo_path = file_path
            self.logo_path_input.setText(os.path.basename(file_path))
    
    def get_selected_currencies(self):
        """الحصول على العملات المحددة"""
        selected_currencies = []
        for i in range(self.supported_currencies_list.count()):
            item = self.supported_currencies_list.item(i)
            if item.checkState() == Qt.Checked:
                currency_id = item.data(Qt.UserRole)
                if currency_id:
                    selected_currencies.append(currency_id)
        return selected_currencies
    
    def validate_form(self):
        """التحقق من صحة النموذج"""
        is_valid = (
            self.exchange_name_input.text().strip() and
            self.exchange_code_input.text().strip()
        )

        self.save_btn.setEnabled(is_valid)
        return is_valid

    def validate_required_fields(self):
        """التحقق من الحقول المطلوبة مع رسائل تفصيلية"""
        missing_fields = []

        if not self.exchange_name_input.text().strip():
            missing_fields.append("اسم الصراف")

        if not self.exchange_code_input.text().strip():
            missing_fields.append("رمز الصراف")

        return missing_fields

    def ensure_database_structure(self):
        """التأكد من وجود هيكل قاعدة البيانات الصحيح"""
        try:
            db_path = Path("data/proshipment.db")
            db_path.parent.mkdir(exist_ok=True)

            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء جدول العملات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS currencies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    symbol TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT
                )
            """)

            # إدراج العملات الافتراضية
            currencies = [
                ('SAR', 'الريال السعودي', 'Saudi Riyal', 'ر.س'),
                ('USD', 'الدولار الأمريكي', 'US Dollar', '$'),
                ('EUR', 'اليورو', 'Euro', '€'),
                ('AED', 'الدرهم الإماراتي', 'UAE Dirham', 'د.إ')
            ]

            for code, name, name_en, symbol in currencies:
                cursor.execute("""
                    INSERT OR IGNORE INTO currencies (code, name, name_en, symbol, is_active, created_at)
                    VALUES (?, ?, ?, ?, 1, ?)
                """, (code, name, name_en, symbol, datetime.now().isoformat()))

            # إنشاء جدول الصرافين مع جميع الأعمدة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS exchanges (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    code TEXT UNIQUE NOT NULL,
                    license_number TEXT,
                    address TEXT,
                    phone TEXT,
                    mobile TEXT,
                    email TEXT,
                    website TEXT,
                    transfer_fee REAL DEFAULT 0,
                    commission_rate REAL DEFAULT 0,
                    min_transfer_amount REAL DEFAULT 0,
                    max_transfer_amount REAL DEFAULT 500000,
                    logo_path TEXT,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    online_service BOOLEAN DEFAULT 0,
                    home_delivery BOOLEAN DEFAULT 0,
                    created_at TEXT
                )
            """)

            # إنشاء جدول العملات المدعومة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS exchange_currencies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exchange_id INTEGER NOT NULL,
                    currency_id INTEGER NOT NULL,
                    created_at TEXT,
                    UNIQUE(exchange_id, currency_id)
                )
            """)

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {e}")
            return False

    def save_exchange(self):
        """حفظ الصراف الجديد"""
        # التحقق من الحقول المطلوبة
        missing_fields = self.validate_required_fields()
        if missing_fields:
            fields_text = "، ".join(missing_fields)
            QMessageBox.warning(
                self,
                "حقول مطلوبة",
                f"يرجى إدخال القيم التالية:\n• {fields_text}"
            )
            return

        # التأكد من هيكل قاعدة البيانات
        if not self.ensure_database_structure():
            QMessageBox.critical(self, "خطأ", "فشل في إنشاء قاعدة البيانات")
            return
        
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.save_btn.setEnabled(False)
            
            # جمع البيانات مع معالجة الحقول الاختيارية
            selected_currencies = self.get_selected_currencies()

            exchange_data = {
                'name': self.exchange_name_input.text().strip(),
                'name_en': self.exchange_name_en_input.text().strip() or None,
                'code': self.exchange_code_input.text().strip(),
                'license_number': self.license_number_input.text().strip() or None,
                'address': self.address_input.toPlainText().strip() or None,
                'phone': self.phone_input.text().strip() or None,
                'mobile': self.mobile_input.text().strip() or None,
                'email': self.email_input.text().strip() or None,
                'website': self.website_input.text().strip() or None,
                'transfer_fee': self.transfer_fee_input.value(),
                'commission_rate': self.commission_rate_input.value(),
                'min_transfer_amount': self.min_transfer_input.value(),
                'max_transfer_amount': self.max_transfer_input.value(),
                'logo_path': getattr(self, 'exchange_logo_path', None),
                'notes': self.notes_input.toPlainText().strip() or None,
                'is_active': self.is_active_checkbox.isChecked(),
                'online_service': self.online_service_checkbox.isChecked(),
                'home_delivery': self.home_delivery_checkbox.isChecked(),
                'supported_currencies': selected_currencies,
                'created_at': datetime.now().isoformat()
            }
            
            # حفظ في قاعدة البيانات
            exchange_id = self.save_to_database(exchange_data)
            
            if exchange_id:
                QMessageBox.information(self, "نجح الحفظ", 
                                      f"تم حفظ الصراف '{exchange_data['name']}' بنجاح")
                
                # إرسال إشارة
                self.exchange_added.emit(exchange_id)
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ الصراف")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الصراف:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.save_btn.setEnabled(True)
    
    def save_to_database(self, data):
        """حفظ البيانات في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # التأكد من وجود جدول الصرافين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS exchanges (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    code TEXT UNIQUE NOT NULL,
                    license_number TEXT,
                    type TEXT NOT NULL,
                    country TEXT NOT NULL,
                    address TEXT,
                    phone TEXT,
                    mobile TEXT,
                    email TEXT,
                    website TEXT,
                    transfer_fee REAL DEFAULT 0,
                    commission_rate REAL DEFAULT 0,
                    min_transfer_amount REAL DEFAULT 0,
                    max_transfer_amount REAL DEFAULT 500000,
                    logo_path TEXT,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    online_service BOOLEAN DEFAULT 0,
                    home_delivery BOOLEAN DEFAULT 0,
                    created_at TEXT,
                    updated_at TEXT
                )
            """)

            # إضافة الأعمدة المفقودة إذا لم تكن موجودة
            try:
                cursor.execute("ALTER TABLE exchanges ADD COLUMN license_number TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE exchanges ADD COLUMN mobile TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE exchanges ADD COLUMN commission_rate REAL DEFAULT 0")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE exchanges ADD COLUMN min_transfer_amount REAL DEFAULT 0")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE exchanges ADD COLUMN max_transfer_amount REAL DEFAULT 500000")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE exchanges ADD COLUMN logo_path TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE exchanges ADD COLUMN online_service BOOLEAN DEFAULT 0")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE exchanges ADD COLUMN home_delivery BOOLEAN DEFAULT 0")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل
            
            # إدراج الصراف الجديد
            cursor.execute("""
                INSERT INTO exchanges (
                    name, name_en, code, license_number, address,
                    phone, mobile, email, website, transfer_fee, commission_rate,
                    min_transfer_amount, max_transfer_amount, logo_path, notes,
                    is_active, online_service, home_delivery, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['name'], data['name_en'], data['code'], data['license_number'],
                data['address'], data['phone'], data['mobile'], data['email'],
                data['website'], data['transfer_fee'], data['commission_rate'],
                data['min_transfer_amount'], data['max_transfer_amount'],
                data['logo_path'], data['notes'], data['is_active'], data['online_service'],
                data['home_delivery'], data['created_at']
            ))
            
            exchange_id = cursor.lastrowid
            
            # حفظ العملات المدعومة
            if data['supported_currencies']:
                # التأكد من وجود جدول العملات المدعومة
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS exchange_currencies (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        exchange_id INTEGER NOT NULL,
                        currency_id INTEGER NOT NULL,
                        created_at TEXT,
                        FOREIGN KEY (exchange_id) REFERENCES exchanges (id),
                        FOREIGN KEY (currency_id) REFERENCES currencies (id),
                        UNIQUE(exchange_id, currency_id)
                    )
                """)
                
                for currency_id in data['supported_currencies']:
                    cursor.execute("""
                        INSERT OR IGNORE INTO exchange_currencies (exchange_id, currency_id, created_at)
                        VALUES (?, ?, ?)
                    """, (exchange_id, currency_id, data['created_at']))
            
            conn.commit()
            conn.close()
            
            return exchange_id
            
        except sqlite3.IntegrityError as e:
            if "UNIQUE constraint failed: exchanges.code" in str(e):
                QMessageBox.warning(self, "خطأ", "رمز الصراف موجود بالفعل. يرجى اختيار رمز آخر.")
            else:
                QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {e}")
            return None
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات: {e}")
            return None
