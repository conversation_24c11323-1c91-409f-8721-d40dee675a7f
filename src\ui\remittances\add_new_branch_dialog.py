# -*- coding: utf-8 -*-
"""
نافذة إضافة فرع جديد
Add New Branch Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QTextEdit, QPushButton,
                               QGroupBox, QCheckBox, QSpinBox, QDoubleSpinBox,
                               QMessageBox, QFrame, QProgressBar, QTimeEdit)
from PySide6.QtCore import Qt, Signal, QTimer, QTime
from PySide6.QtGui import QFont, QPixmap, QIcon

import sqlite3
from pathlib import Path
from datetime import datetime

class AddNewBranchDialog(QDialog):
    """نافذة إضافة فرع جديد"""
    
    branch_added = Signal(int)  # إشارة عند إضافة فرع جديد
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إضافة فرع جديد - ProShipment")
        self.setMinimumSize(700, 920)
        self.resize(800, 920)
        self.setModal(True)

        # توسيط النافذة
        self.center_window()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.setup_validators()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #7c3aed, stop:1 #6d28d9);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        title_layout = QHBoxLayout(title_frame)
        
        title_label = QLabel("🏢 إضافة فرع جديد")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                background: transparent;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addWidget(title_frame)
        
        # اختيار البنك/الصراف
        parent_group = QGroupBox("اختيار البنك أو الصراف")
        parent_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        parent_layout = QGridLayout(parent_group)
        parent_layout.setSpacing(10)
        
        # نوع الجهة الأم
        parent_layout.addWidget(QLabel("نوع الجهة: *"), 0, 0)
        self.parent_type_combo = QComboBox()
        self.parent_type_combo.addItems(["بنك", "صراف"])
        self.parent_type_combo.setMinimumHeight(35)
        parent_layout.addWidget(self.parent_type_combo, 0, 1, 1, 3)

        # اختيار الجهة الأم
        parent_layout.addWidget(QLabel("اختيار الجهة: *"), 1, 0)
        self.parent_entity_combo = QComboBox()
        self.parent_entity_combo.setMinimumHeight(35)
        parent_layout.addWidget(self.parent_entity_combo, 1, 1, 1, 3)
        
        layout.addWidget(parent_group)
        
        # المعلومات الأساسية
        basic_group = QGroupBox("المعلومات الأساسية")
        basic_group.setStyleSheet(parent_group.styleSheet())
        basic_layout = QGridLayout(basic_group)
        basic_layout.setSpacing(10)
        
        # اسم الفرع
        basic_layout.addWidget(QLabel("اسم الفرع: *"), 0, 0)
        self.branch_name_input = QLineEdit()
        self.branch_name_input.setPlaceholderText("أدخل اسم الفرع...")
        self.branch_name_input.setMinimumHeight(35)
        basic_layout.addWidget(self.branch_name_input, 0, 1, 1, 3)

        # اسم الفرع بالإنجليزية
        basic_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 1, 0)
        self.branch_name_en_input = QLineEdit()
        self.branch_name_en_input.setPlaceholderText("Branch Name in English...")
        self.branch_name_en_input.setMinimumHeight(35)
        basic_layout.addWidget(self.branch_name_en_input, 1, 1, 1, 3)

        # رمز الفرع
        basic_layout.addWidget(QLabel("رمز الفرع: *"), 2, 0)
        self.branch_code_input = QLineEdit()
        self.branch_code_input.setPlaceholderText("مثال: BR001")
        self.branch_code_input.setMaxLength(10)
        self.branch_code_input.setMinimumHeight(35)
        basic_layout.addWidget(self.branch_code_input, 2, 1)

        # نوع الفرع
        basic_layout.addWidget(QLabel("نوع الفرع:"), 2, 2)
        self.branch_type_combo = QComboBox()
        self.branch_type_combo.addItems([
            "فرع رئيسي",
            "فرع فرعي",
            "مكتب تمثيلي",
            "نقطة خدمة",
            "صراف آلي",
            "مركز تحويل"
        ])
        self.branch_type_combo.setMinimumHeight(35)
        basic_layout.addWidget(self.branch_type_combo, 2, 3)


        
        layout.addWidget(basic_group)
        
        # معلومات الاتصال والموقع
        contact_group = QGroupBox("معلومات الاتصال والموقع")
        contact_group.setStyleSheet(parent_group.styleSheet())
        contact_layout = QGridLayout(contact_group)
        contact_layout.setSpacing(10)
        
        # العنوان التفصيلي
        contact_layout.addWidget(QLabel("العنوان التفصيلي:"), 0, 0)
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(100)
        self.address_input.setMinimumHeight(100)
        self.address_input.setPlaceholderText("أدخل العنوان التفصيلي للفرع...")
        contact_layout.addWidget(self.address_input, 0, 1, 1, 3)

        # الهاتف
        contact_layout.addWidget(QLabel("الهاتف:"), 1, 0)
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("+966 11 123 4567")
        self.phone_input.setMinimumHeight(35)
        contact_layout.addWidget(self.phone_input, 1, 1)

        # الفاكس
        contact_layout.addWidget(QLabel("الفاكس:"), 1, 2)
        self.fax_input = QLineEdit()
        self.fax_input.setPlaceholderText("+966 11 123 4568")
        self.fax_input.setMinimumHeight(35)
        contact_layout.addWidget(self.fax_input, 1, 3)

        # البريد الإلكتروني
        contact_layout.addWidget(QLabel("البريد الإلكتروني:"), 2, 0)
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        self.email_input.setMinimumHeight(35)
        contact_layout.addWidget(self.email_input, 2, 1)

        # الرمز البريدي
        contact_layout.addWidget(QLabel("الرمز البريدي:"), 2, 2)
        self.postal_code_input = QLineEdit()
        self.postal_code_input.setPlaceholderText("12345")
        self.postal_code_input.setMaxLength(5)
        self.postal_code_input.setMinimumHeight(35)
        contact_layout.addWidget(self.postal_code_input, 2, 3)
        
        layout.addWidget(contact_group)
        

        
        # معلومات إضافية
        extra_group = QGroupBox("معلومات إضافية")
        extra_group.setStyleSheet(parent_group.styleSheet())
        extra_layout = QGridLayout(extra_group)
        extra_layout.setSpacing(10)
        
        # اسم مدير الفرع
        extra_layout.addWidget(QLabel("مدير الفرع:"), 0, 0)
        self.manager_name_input = QLineEdit()
        self.manager_name_input.setPlaceholderText("اسم مدير الفرع...")
        self.manager_name_input.setMinimumHeight(35)
        extra_layout.addWidget(self.manager_name_input, 0, 1)

        # هاتف المدير
        extra_layout.addWidget(QLabel("هاتف المدير:"), 0, 2)
        self.manager_phone_input = QLineEdit()
        self.manager_phone_input.setPlaceholderText("+966 50 123 4567")
        self.manager_phone_input.setMinimumHeight(35)
        extra_layout.addWidget(self.manager_phone_input, 0, 3)

        # الملاحظات
        extra_layout.addWidget(QLabel("ملاحظات:"), 1, 0)
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(100)
        self.notes_input.setMinimumHeight(100)
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        extra_layout.addWidget(self.notes_input, 1, 1, 1, 3)

        # حالة الفرع
        self.is_active_checkbox = QCheckBox("الفرع نشط")
        self.is_active_checkbox.setChecked(True)
        self.is_active_checkbox.setMinimumHeight(30)
        extra_layout.addWidget(self.is_active_checkbox, 2, 0, 1, 2)
        
        layout.addWidget(extra_group)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # أزرار الإجراءات
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.addStretch()
        
        self.save_btn = QPushButton("💾 حفظ الفرع")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:pressed {
                background-color: #047857;
            }
        """)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #dc2626;
            }
            QPushButton:pressed {
                background-color: #b91c1c;
            }
        """)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_branch)
        self.cancel_btn.clicked.connect(self.reject)
        
        # تحديث قائمة الجهات عند تغيير النوع
        self.parent_type_combo.currentTextChanged.connect(self.update_parent_entities)
        
        # التحقق من صحة البيانات عند التغيير
        self.branch_name_input.textChanged.connect(self.validate_form)
        self.branch_code_input.textChanged.connect(self.validate_form)
        self.parent_entity_combo.currentTextChanged.connect(self.validate_form)
        
        # تحميل البيانات الأولية
        self.update_parent_entities()
        
    def setup_validators(self):
        """إعداد مدققات الإدخال"""
        # تحويل رمز الفرع للأحرف الكبيرة
        self.branch_code_input.textChanged.connect(
            lambda: self.branch_code_input.setText(self.branch_code_input.text().upper())
        )
        
    def update_parent_entities(self):
        """تحديث قائمة الجهات الأم"""
        self.parent_entity_combo.clear()
        parent_type = self.parent_type_combo.currentText()
        
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            if parent_type == "بنك":
                cursor.execute("SELECT id, name FROM banks WHERE is_active = 1 ORDER BY name")
            else:  # صراف
                cursor.execute("SELECT id, name FROM exchanges WHERE is_active = 1 ORDER BY name")
            
            entities = cursor.fetchall()
            
            self.parent_entity_combo.addItem("اختر الجهة...", None)
            for entity in entities:
                self.parent_entity_combo.addItem(entity[1], entity[0])
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تحميل الجهات: {e}")
            self.parent_entity_combo.addItem("لا توجد جهات متاحة", None)
    
    def validate_form(self):
        """التحقق من صحة النموذج"""
        is_valid = (
            self.branch_name_input.text().strip() and
            self.branch_code_input.text().strip() and
            self.parent_entity_combo.currentData() is not None
        )
        
        self.save_btn.setEnabled(is_valid)
        return is_valid
    
    def ensure_database_structure(self):
        """التأكد من وجود هيكل قاعدة البيانات الصحيح"""
        try:
            db_path = Path("data/proshipment.db")
            db_path.parent.mkdir(exist_ok=True)

            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء جدول الفروع مع جميع الأعمدة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS branches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    code TEXT UNIQUE NOT NULL,
                    type TEXT NOT NULL,
                    parent_type TEXT NOT NULL,
                    parent_id INTEGER NOT NULL,
                    city TEXT NOT NULL,
                    region TEXT,
                    address TEXT,
                    phone TEXT,
                    fax TEXT,
                    email TEXT,
                    postal_code TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    working_days TEXT,
                    manager_name TEXT,
                    manager_phone TEXT,
                    notes TEXT,
                    cash_service BOOLEAN DEFAULT 1,
                    transfer_service BOOLEAN DEFAULT 1,
                    exchange_service BOOLEAN DEFAULT 0,
                    atm_service BOOLEAN DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT
                )
            """)

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {e}")
            return False

    def save_branch(self):
        """حفظ الفرع الجديد"""
        if not self.validate_form():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إكمال جميع الحقول المطلوبة")
            return

        # التأكد من هيكل قاعدة البيانات
        if not self.ensure_database_structure():
            QMessageBox.critical(self, "خطأ", "فشل في إنشاء قاعدة البيانات")
            return
        
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.save_btn.setEnabled(False)
            
            # جمع البيانات
            branch_data = {
                'name': self.branch_name_input.text().strip(),
                'name_en': self.branch_name_en_input.text().strip() or None,
                'code': self.branch_code_input.text().strip(),
                'type': self.branch_type_combo.currentText(),
                'parent_type': self.parent_type_combo.currentText(),
                'parent_id': self.parent_entity_combo.currentData(),
                'address': self.address_input.toPlainText().strip() or None,
                'phone': self.phone_input.text().strip() or None,
                'fax': self.fax_input.text().strip() or None,
                'email': self.email_input.text().strip() or None,
                'manager_name': self.manager_name_input.text().strip() or None,
                'manager_phone': self.manager_phone_input.text().strip() or None,
                'notes': self.notes_input.toPlainText().strip() or None,
                'is_active': self.is_active_checkbox.isChecked(),
                'created_at': datetime.now().isoformat()
            }
            
            # حفظ في قاعدة البيانات
            branch_id = self.save_to_database(branch_data)
            
            if branch_id:
                QMessageBox.information(self, "نجح الحفظ", 
                                      f"تم حفظ الفرع '{branch_data['name']}' بنجاح")
                
                # إرسال إشارة
                self.branch_added.emit(branch_id)
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ الفرع")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الفرع:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.save_btn.setEnabled(True)
    
    def save_to_database(self, data):
        """حفظ البيانات في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # التأكد من وجود جدول الفروع
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS branches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    code TEXT UNIQUE NOT NULL,
                    type TEXT NOT NULL,
                    parent_type TEXT NOT NULL,
                    parent_id INTEGER NOT NULL,
                    city TEXT NOT NULL,
                    region TEXT,
                    address TEXT,
                    phone TEXT,
                    fax TEXT,
                    email TEXT,
                    postal_code TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    working_days TEXT,
                    manager_name TEXT,
                    manager_phone TEXT,
                    notes TEXT,
                    cash_service BOOLEAN DEFAULT 1,
                    transfer_service BOOLEAN DEFAULT 1,
                    exchange_service BOOLEAN DEFAULT 0,
                    atm_service BOOLEAN DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT,
                    updated_at TEXT
                )
            """)

            # إضافة الأعمدة المفقودة إذا لم تكن موجودة
            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN parent_type TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN parent_id INTEGER")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN region TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN fax TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN postal_code TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN start_time TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN end_time TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN working_days TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN manager_name TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN manager_phone TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN cash_service BOOLEAN DEFAULT 1")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN transfer_service BOOLEAN DEFAULT 1")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN exchange_service BOOLEAN DEFAULT 0")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE branches ADD COLUMN atm_service BOOLEAN DEFAULT 0")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل
            
            # تحديد bank_id أو exchange_id بناءً على نوع الجهة الأم
            bank_id = None
            exchange_id = None

            if data['parent_type'] == "بنك":
                bank_id = data['parent_id']
            else:  # صراف
                exchange_id = data['parent_id']

            # إدراج الفرع الجديد
            cursor.execute("""
                INSERT INTO branches (
                    name, name_en, code, type, parent_type, parent_id,
                    bank_id, exchange_id, address, phone, fax, email,
                    manager_name, manager_phone, notes, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['name'], data['name_en'], data['code'], data['type'],
                data['parent_type'], data['parent_id'], bank_id, exchange_id,
                data['address'], data['phone'], data['fax'], data['email'],
                data['manager_name'], data['manager_phone'], data['notes'], data['is_active']
            ))
            
            branch_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return branch_id
            
        except sqlite3.IntegrityError as e:
            if "UNIQUE constraint failed: branches.code" in str(e):
                QMessageBox.warning(self, "خطأ", "رمز الفرع موجود بالفعل. يرجى اختيار رمز آخر.")
            else:
                QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {e}")
            return None
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات: {e}")
            return None
