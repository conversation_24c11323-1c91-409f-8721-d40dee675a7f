#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح Oracle وتشغيل التطبيق
Fix Oracle and Run Application
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def fix_oracle_sequences():
    """إصلاح مشكلة الـ sequences في Oracle"""
    
    print("🔧 إصلاح مشكلة Oracle Sequences...")
    
    try:
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        
        # تحميل التكوين
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        print(f"نوع قاعدة البيانات: {config.type.value}")
        
        # اختبار الاتصال
        if not db_manager.test_connection():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # تنفيذ أوامر إصلاح الـ sequences
        sequences_sql = [
            # إنشاء sequences
            """CREATE SEQUENCE units_of_measure_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE""",
            """CREATE SEQUENCE item_groups_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE""",
            """CREATE SEQUENCE items_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE""",
            """CREATE SEQUENCE currencies_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE""",
            """CREATE SEQUENCE system_settings_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE""",
            """CREATE SEQUENCE companies_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE""",
            """CREATE SEQUENCE fiscal_years_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE""",
        ]
        
        triggers_sql = [
            # إنشاء triggers
            """CREATE OR REPLACE TRIGGER units_of_measure_trigger
               BEFORE INSERT ON units_of_measure
               FOR EACH ROW
               BEGIN
                   IF :NEW.id IS NULL THEN
                       :NEW.id := units_of_measure_seq.NEXTVAL;
                   END IF;
               END;""",
            
            """CREATE OR REPLACE TRIGGER item_groups_trigger
               BEFORE INSERT ON item_groups
               FOR EACH ROW
               BEGIN
                   IF :NEW.id IS NULL THEN
                       :NEW.id := item_groups_seq.NEXTVAL;
                   END IF;
               END;""",
            
            """CREATE OR REPLACE TRIGGER items_trigger
               BEFORE INSERT ON items
               FOR EACH ROW
               BEGIN
                   IF :NEW.id IS NULL THEN
                       :NEW.id := items_seq.NEXTVAL;
                   END IF;
               END;""",
            
            """CREATE OR REPLACE TRIGGER currencies_trigger
               BEFORE INSERT ON currencies
               FOR EACH ROW
               BEGIN
                   IF :NEW.id IS NULL THEN
                       :NEW.id := currencies_seq.NEXTVAL;
                   END IF;
               END;""",
            
            """CREATE OR REPLACE TRIGGER system_settings_trigger
               BEFORE INSERT ON system_settings
               FOR EACH ROW
               BEGIN
                   IF :NEW.id IS NULL THEN
                       :NEW.id := system_settings_seq.NEXTVAL;
                   END IF;
               END;""",
            
            """CREATE OR REPLACE TRIGGER companies_trigger
               BEFORE INSERT ON companies
               FOR EACH ROW
               BEGIN
                   IF :NEW.id IS NULL THEN
                       :NEW.id := companies_seq.NEXTVAL;
                   END IF;
               END;""",
            
            """CREATE OR REPLACE TRIGGER fiscal_years_trigger
               BEFORE INSERT ON fiscal_years
               FOR EACH ROW
               BEGIN
                   IF :NEW.id IS NULL THEN
                       :NEW.id := fiscal_years_seq.NEXTVAL;
                   END IF;
               END;"""
        ]
        
        with db_manager.get_session() as session:
            # إنشاء sequences
            print("📝 إنشاء Sequences...")
            for sql in sequences_sql:
                try:
                    session.execute(sql)
                    print(f"   ✅ تم إنشاء sequence")
                except Exception as e:
                    if "name is already used" in str(e) or "already exists" in str(e):
                        print(f"   ⚠️ Sequence موجود مسبقاً")
                    else:
                        print(f"   ❌ خطأ في إنشاء sequence: {e}")
            
            # إنشاء triggers
            print("🔧 إنشاء Triggers...")
            for sql in triggers_sql:
                try:
                    session.execute(sql)
                    print(f"   ✅ تم إنشاء trigger")
                except Exception as e:
                    print(f"   ❌ خطأ في إنشاء trigger: {e}")
            
            print("✅ تم إصلاح مشكلة Sequences بنجاح!")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح Oracle: {e}")
        import traceback
        traceback.print_exc()
        return False

def insert_sample_data():
    """إدراج البيانات الأساسية"""
    
    print("\n📦 إدراج البيانات الأساسية...")
    
    try:
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        from src.database.models import UnitOfMeasure, ItemGroup, Currency, SystemSettings, Company, FiscalYear
        from datetime import datetime
        
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        with db_manager.get_session() as session:
            # فحص البيانات الموجودة
            units_count = session.query(UnitOfMeasure).count()
            groups_count = session.query(ItemGroup).count()
            
            print(f"وحدات القياس الموجودة: {units_count}")
            print(f"مجموعات الأصناف الموجودة: {groups_count}")
            
            # إدراج وحدات القياس إذا لم تكن موجودة
            if units_count == 0:
                print("إدراج وحدات القياس...")
                units = [
                    UnitOfMeasure(name="قطعة", name_en="Piece", symbol="قطعة", symbol_en="pcs", description="وحدة العد الأساسية"),
                    UnitOfMeasure(name="كيلوجرام", name_en="Kilogram", symbol="كجم", symbol_en="kg", description="وحدة قياس الوزن الأساسية"),
                    UnitOfMeasure(name="متر", name_en="Meter", symbol="م", symbol_en="m", description="وحدة قياس الطول الأساسية"),
                    UnitOfMeasure(name="لتر", name_en="Liter", symbol="لتر", symbol_en="L", description="وحدة قياس السوائل"),
                ]
                
                for unit in units:
                    session.add(unit)
                    print(f"   ✅ تم إضافة: {unit.name}")
            
            # إدراج مجموعات الأصناف إذا لم تكن موجودة
            if groups_count == 0:
                print("إدراج مجموعات الأصناف...")
                groups = [
                    ItemGroup(name="إلكترونيات", name_en="Electronics", description="الأجهزة والمعدات الإلكترونية"),
                    ItemGroup(name="ملابس", name_en="Clothing", description="الملابس والأزياء"),
                    ItemGroup(name="أغذية", name_en="Food", description="المواد الغذائية والمشروبات"),
                    ItemGroup(name="أدوات منزلية", name_en="Home Appliances", description="الأدوات والأجهزة المنزلية"),
                ]
                
                for group in groups:
                    session.add(group)
                    print(f"   ✅ تم إضافة: {group.name}")
            
            # فحص النتائج النهائية
            final_units = session.query(UnitOfMeasure).count()
            final_groups = session.query(ItemGroup).count()
            
            print(f"\n📊 النتائج النهائية:")
            print(f"   وحدات القياس: {final_units}")
            print(f"   مجموعات الأصناف: {final_groups}")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إدراج البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_application():
    """تشغيل التطبيق"""
    
    print("\n🚀 تشغيل التطبيق...")
    
    try:
        import subprocess
        import sys
        
        # تشغيل التطبيق في عملية منفصلة
        process = subprocess.Popen([sys.executable, "main.py"], 
                                 cwd=str(Path(__file__).parent),
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("💡 يمكنك الآن استخدام التطبيق")
        print("💡 للوصول لإدارة وحدات القياس: إدارة الأصناف → وحدات القياس")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 إصلاح Oracle وتشغيل التطبيق")
    print("=" * 50)
    
    # إصلاح مشكلة sequences
    if not fix_oracle_sequences():
        print("❌ فشل في إصلاح Oracle")
        return False
    
    # إدراج البيانات الأساسية
    if not insert_sample_data():
        print("❌ فشل في إدراج البيانات")
        return False
    
    # تشغيل التطبيق
    if not run_application():
        print("❌ فشل في تشغيل التطبيق")
        return False
    
    print("\n🎉 تم إصلاح جميع المشاكل وتشغيل التطبيق بنجاح!")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 يمكنك تشغيل التطبيق يدوياً باستخدام: python main.py")
    sys.exit(0 if success else 1)
