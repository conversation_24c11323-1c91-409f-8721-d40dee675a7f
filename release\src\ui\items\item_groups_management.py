# -*- coding: utf-8 -*-
"""
ويدجت إدارة مجموعات الأصناف
Item Groups Management Widget
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QTextEdit, QCheckBox,
                               QComboBox, QTreeWidget, QTreeWidgetItem, QSplitter)
from PySide6.QtCore import Qt

from ...database.database_manager import DatabaseManager
from ...database.models import ItemGroup

class ItemGroupsManagementWidget(QWidget):
    """ويدجت إدارة مجموعات الأصناف"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_group_id = None
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(15)
        
        # إنشاء Splitter للتقسيم
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - شجرة المجموعات
        tree_group = QGroupBox("شجرة مجموعات الأصناف")
        tree_layout = QVBoxLayout(tree_group)
        
        self.groups_tree = QTreeWidget()
        self.groups_tree.setHeaderLabels(["اسم المجموعة", "الكود"])
        self.groups_tree.itemSelectionChanged.connect(self.on_tree_selection_changed)
        
        tree_layout.addWidget(self.groups_tree)
        
        # أزرار إدارة الشجرة
        tree_buttons_layout = QHBoxLayout()
        
        expand_button = QPushButton("توسيع الكل")
        expand_button.clicked.connect(self.groups_tree.expandAll)
        
        collapse_button = QPushButton("طي الكل")
        collapse_button.clicked.connect(self.groups_tree.collapseAll)
        
        tree_buttons_layout.addWidget(expand_button)
        tree_buttons_layout.addWidget(collapse_button)
        tree_buttons_layout.addStretch()
        
        tree_layout.addLayout(tree_buttons_layout)
        
        splitter.addWidget(tree_group)
        
        # الجانب الأيمن - نموذج التفاصيل
        details_group = QGroupBox("تفاصيل المجموعة")
        details_layout = QVBoxLayout(details_group)
        
        # نموذج الإدخال
        form_layout = QFormLayout()
        
        self.group_name_edit = QLineEdit()
        self.group_name_edit.setPlaceholderText("اسم مجموعة الأصناف")
        form_layout.addRow("اسم المجموعة:", self.group_name_edit)
        
        self.group_code_edit = QLineEdit()
        self.group_code_edit.setPlaceholderText("كود المجموعة (اختياري)")
        form_layout.addRow("كود المجموعة:", self.group_code_edit)
        
        self.parent_group_combo = QComboBox()
        self.load_parent_groups()
        form_layout.addRow("المجموعة الأب:", self.parent_group_combo)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("وصف المجموعة")
        form_layout.addRow("الوصف:", self.description_edit)
        
        self.is_active_check = QCheckBox("نشط")
        self.is_active_check.setChecked(True)
        form_layout.addRow("", self.is_active_check)
        
        details_layout.addLayout(form_layout)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton("إضافة مجموعة")
        self.add_button.clicked.connect(self.add_group)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.update_button = QPushButton("تحديث")
        self.update_button.clicked.connect(self.update_group)
        self.update_button.setEnabled(False)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        
        self.delete_button = QPushButton("حذف")
        self.delete_button.clicked.connect(self.delete_group)
        self.delete_button.setEnabled(False)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        
        self.clear_button = QPushButton("مسح")
        self.clear_button.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.delete_button)
        buttons_layout.addWidget(self.clear_button)
        
        details_layout.addLayout(buttons_layout)
        
        # معلومات إضافية
        info_group = QGroupBox("معلومات إضافية")
        info_layout = QFormLayout(info_group)
        
        self.items_count_label = QLabel("0")
        info_layout.addRow("عدد الأصناف:", self.items_count_label)
        
        self.subgroups_count_label = QLabel("0")
        info_layout.addRow("عدد المجموعات الفرعية:", self.subgroups_count_label)
        
        self.created_date_label = QLabel("-")
        info_layout.addRow("تاريخ الإنشاء:", self.created_date_label)
        
        details_layout.addWidget(info_group)
        
        splitter.addWidget(details_group)
        
        # تعيين نسب التقسيم
        splitter.setSizes([400, 600])
        
        main_layout.addWidget(splitter)
    
    def load_parent_groups(self):
        """تحميل قائمة المجموعات الأب"""
        session = self.db_manager.get_session()
        try:
            groups = session.query(ItemGroup).filter_by(is_active=True).all()
            
            self.parent_group_combo.clear()
            self.parent_group_combo.addItem("-- بدون مجموعة أب --", None)
            
            for group in groups:
                self.parent_group_combo.addItem(group.name, group.id)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل قائمة المجموعات:\n{str(e)}"
            )
        finally:
            session.close()
    
    def load_data(self):
        """تحميل بيانات مجموعات الأصناف"""
        session = self.db_manager.get_session()
        try:
            # تحميل المجموعات الرئيسية (بدون أب)
            root_groups = session.query(ItemGroup).filter_by(
                parent_id=None, is_active=True
            ).all()
            
            self.groups_tree.clear()
            
            for group in root_groups:
                self.add_group_to_tree(group, None, session)
            
            self.groups_tree.expandAll()
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل بيانات المجموعات:\n{str(e)}"
            )
        finally:
            session.close()
    
    def add_group_to_tree(self, group, parent_item, session):
        """إضافة مجموعة إلى الشجرة"""
        if parent_item is None:
            item = QTreeWidgetItem(self.groups_tree)
        else:
            item = QTreeWidgetItem(parent_item)
        
        item.setText(0, group.name)
        item.setText(1, group.code or "")
        item.setData(0, Qt.UserRole, group.id)
        
        # تحميل المجموعات الفرعية
        subgroups = session.query(ItemGroup).filter_by(
            parent_id=group.id, is_active=True
        ).all()
        
        for subgroup in subgroups:
            self.add_group_to_tree(subgroup, item, session)
    
    def on_tree_selection_changed(self):
        """عند تغيير التحديد في الشجرة"""
        current_item = self.groups_tree.currentItem()
        if current_item:
            group_id = current_item.data(0, Qt.UserRole)
            self.load_group_details(group_id)
    
    def load_group_details(self, group_id):
        """تحميل تفاصيل المجموعة"""
        session = self.db_manager.get_session()
        try:
            group = session.query(ItemGroup).get(group_id)
            if group:
                self.current_group_id = group.id
                self.group_name_edit.setText(group.name)
                self.group_code_edit.setText(group.code or "")
                self.description_edit.setPlainText(group.description or "")
                self.is_active_check.setChecked(group.is_active)
                
                # تعيين المجموعة الأب
                if group.parent_id:
                    parent_index = self.parent_group_combo.findData(group.parent_id)
                    if parent_index >= 0:
                        self.parent_group_combo.setCurrentIndex(parent_index)
                else:
                    self.parent_group_combo.setCurrentIndex(0)
                
                # تحديث المعلومات الإضافية
                # عدد الأصناف (سيتم تنفيذه لاحقاً)
                self.items_count_label.setText("0")
                
                # عدد المجموعات الفرعية
                subgroups_count = session.query(ItemGroup).filter_by(
                    parent_id=group.id, is_active=True
                ).count()
                self.subgroups_count_label.setText(str(subgroups_count))
                
                # تاريخ الإنشاء
                if group.created_at:
                    self.created_date_label.setText(group.created_at.strftime("%Y-%m-%d"))
                else:
                    self.created_date_label.setText("-")
                
                # تفعيل أزرار التحديث والحذف
                self.add_button.setEnabled(False)
                self.update_button.setEnabled(True)
                self.delete_button.setEnabled(True)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل تفاصيل المجموعة:\n{str(e)}"
            )
        finally:
            session.close()
    
    def add_group(self):
        """إضافة مجموعة جديدة"""
        if not self.validate_form():
            return
        
        session = self.db_manager.get_session()
        try:
            # التحقق من عدم وجود المجموعة مسبقاً
            existing = session.query(ItemGroup).filter_by(
                name=self.group_name_edit.text()
            ).first()
            if existing:
                QMessageBox.warning(self, "مجموعة موجودة", "هذه المجموعة موجودة بالفعل")
                return
            
            # التحقق من الكود إذا تم إدخاله
            if self.group_code_edit.text():
                existing_code = session.query(ItemGroup).filter_by(
                    code=self.group_code_edit.text()
                ).first()
                if existing_code:
                    QMessageBox.warning(self, "كود موجود", "هذا الكود مستخدم بالفعل")
                    return
            
            # إنشاء المجموعة الجديدة
            new_group = ItemGroup(
                name=self.group_name_edit.text(),
                code=self.group_code_edit.text() if self.group_code_edit.text() else None,
                description=self.description_edit.toPlainText(),
                parent_id=self.parent_group_combo.currentData(),
                is_active=self.is_active_check.isChecked()
            )
            
            session.add(new_group)
            session.commit()
            
            QMessageBox.information(self, "تم الإضافة", "تم إضافة المجموعة بنجاح")
            
            self.clear_form()
            self.load_data()
            self.load_parent_groups()
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(
                self,
                "خطأ في الإضافة",
                f"حدث خطأ أثناء إضافة المجموعة:\n{str(e)}"
            )
        finally:
            session.close()
    
    def update_group(self):
        """تحديث بيانات المجموعة"""
        if not self.current_group_id or not self.validate_form():
            return
        
        session = self.db_manager.get_session()
        try:
            group = session.query(ItemGroup).get(self.current_group_id)
            if group:
                # التحقق من عدم تعيين المجموعة كأب لنفسها
                parent_id = self.parent_group_combo.currentData()
                if parent_id == self.current_group_id:
                    QMessageBox.warning(
                        self,
                        "خطأ في البيانات",
                        "لا يمكن تعيين المجموعة كأب لنفسها"
                    )
                    return
                
                group.name = self.group_name_edit.text()
                group.code = self.group_code_edit.text() if self.group_code_edit.text() else None
                group.description = self.description_edit.toPlainText()
                group.parent_id = parent_id
                group.is_active = self.is_active_check.isChecked()
                
                session.commit()
                
                QMessageBox.information(self, "تم التحديث", "تم تحديث بيانات المجموعة بنجاح")
                
                self.clear_form()
                self.load_data()
                self.load_parent_groups()
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(
                self,
                "خطأ في التحديث",
                f"حدث خطأ أثناء تحديث بيانات المجموعة:\n{str(e)}"
            )
        finally:
            session.close()
    
    def delete_group(self):
        """حذف المجموعة"""
        if not self.current_group_id:
            return
        
        session = self.db_manager.get_session()
        try:
            group = session.query(ItemGroup).get(self.current_group_id)
            if group:
                # التحقق من وجود مجموعات فرعية
                subgroups_count = session.query(ItemGroup).filter_by(
                    parent_id=self.current_group_id, is_active=True
                ).count()
                
                if subgroups_count > 0:
                    QMessageBox.warning(
                        self,
                        "لا يمكن الحذف",
                        "لا يمكن حذف المجموعة لأنها تحتوي على مجموعات فرعية"
                    )
                    return
                
                # التحقق من وجود أصناف (سيتم تنفيذه لاحقاً)
                
                reply = QMessageBox.question(
                    self,
                    "تأكيد الحذف",
                    f"هل تريد حذف المجموعة '{group.name}'؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    group.is_active = False  # حذف منطقي
                    session.commit()
                    
                    QMessageBox.information(
                        self,
                        "تم الحذف",
                        f"تم حذف المجموعة '{group.name}' بنجاح"
                    )
                    
                    self.clear_form()
                    self.load_data()
                    self.load_parent_groups()
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(
                self,
                "خطأ في الحذف",
                f"حدث خطأ أثناء حذف المجموعة:\n{str(e)}"
            )
        finally:
            session.close()
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.group_name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المجموعة")
            return False
        
        return True
    
    def clear_form(self):
        """مسح النموذج"""
        self.current_group_id = None
        self.group_name_edit.clear()
        self.group_code_edit.clear()
        self.description_edit.clear()
        self.parent_group_combo.setCurrentIndex(0)
        self.is_active_check.setChecked(True)
        
        # مسح المعلومات الإضافية
        self.items_count_label.setText("0")
        self.subgroups_count_label.setText("0")
        self.created_date_label.setText("-")
        
        # إعادة تعيين حالة الأزرار
        self.add_button.setEnabled(True)
        self.update_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        
        # إلغاء التحديد في الشجرة
        self.groups_tree.clearSelection()
