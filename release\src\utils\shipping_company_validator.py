"""
نظام التحقق من صحة وتصحيح أسماء شركات الشحن العالمية
يقوم بمقارنة البيانات المدخلة مع قاعدة بيانات شركات الشحن العالمية
ويقترح التصحيحات المناسبة
"""

import re
from difflib import SequenceMatcher
from typing import List, Dict, Tuple, Optional
import json
import os

class ShippingCompanyValidator:
    """فئة للتحقق من صحة وتصحيح أسماء شركات الشحن"""
    
    def __init__(self):
        """تهيئة قاعدة بيانات شركات الشحن العالمية"""
        self.global_shipping_companies = {
            # شركات الشحن البحري العالمية
            "MSC": {
                "full_name": "Mediterranean Shipping Company",
                "arabic_name": "شركة البحر الأبيض المتوسط للشحن",
                "aliases": ["MSC", "Mediterranean Shipping", "MSC Shipping", "ام اس سي"],
                "type": "بحري",
                "country": "سويسرا"
            },
            "MAERSK": {
                "full_name": "A.P. Moller-Maersk",
                "arabic_name": "مايرسك",
                "aliases": ["Maersk", "Maersk Line", "AP Moller", "مايرسك", "مايرسك لاين"],
                "type": "بحري",
                "country": "الدنمارك"
            },
            "CMA_CGM": {
                "full_name": "CMA CGM Group",
                "arabic_name": "سي ام ايه سي جي ام",
                "aliases": ["CMA CGM", "CMA-CGM", "CMACGM", "سي ام ايه", "سي جي ام"],
                "type": "بحري",
                "country": "فرنسا"
            },
            "COSCO": {
                "full_name": "China COSCO Shipping Corporation",
                "arabic_name": "كوسكو الصينية للشحن",
                "aliases": ["COSCO", "COSCO Shipping", "China COSCO", "كوسكو", "كوسكو شيبينغ"],
                "type": "بحري",
                "country": "الصين"
            },
            "HAPAG_LLOYD": {
                "full_name": "Hapag-Lloyd AG",
                "arabic_name": "هاباغ لويد",
                "aliases": ["Hapag Lloyd", "Hapag-Lloyd", "هاباغ لويد", "هاباج لويد"],
                "type": "بحري",
                "country": "ألمانيا"
            },
            "ONE": {
                "full_name": "Ocean Network Express",
                "arabic_name": "شبكة المحيط السريع",
                "aliases": ["ONE", "Ocean Network Express", "وان", "اوشن نتورك"],
                "type": "بحري",
                "country": "اليابان"
            },
            "EVERGREEN": {
                "full_name": "Evergreen Marine Corporation",
                "arabic_name": "إيفرجرين مارين",
                "aliases": ["Evergreen", "Evergreen Marine", "ايفرجرين", "ايفر جرين"],
                "type": "بحري",
                "country": "تايوان"
            },
            "YANG_MING": {
                "full_name": "Yang Ming Marine Transport Corporation",
                "arabic_name": "يانغ مينغ",
                "aliases": ["Yang Ming", "YangMing", "يانغ مينغ", "يانج مينج"],
                "type": "بحري",
                "country": "تايوان"
            },
            "HMM": {
                "full_name": "HMM Co., Ltd.",
                "arabic_name": "اتش ام ام",
                "aliases": ["HMM", "Hyundai Merchant Marine", "اتش ام ام", "هيونداي"],
                "type": "بحري",
                "country": "كوريا الجنوبية"
            },
            "PIL": {
                "full_name": "Pacific International Lines",
                "arabic_name": "خطوط المحيط الهادئ الدولية",
                "aliases": ["PIL", "Pacific International", "بي اي ال"],
                "type": "بحري",
                "country": "سنغافورة"
            },
            
            # شركات الشحن الجوي العالمية
            "DHL": {
                "full_name": "DHL Express",
                "arabic_name": "دي اتش ال",
                "aliases": ["DHL", "DHL Express", "دي اتش ال", "دي اتش إل"],
                "type": "جوي",
                "country": "ألمانيا"
            },
            "FEDEX": {
                "full_name": "FedEx Corporation",
                "arabic_name": "فيديكس",
                "aliases": ["FedEx", "Federal Express", "فيديكس", "فيدكس"],
                "type": "جوي",
                "country": "الولايات المتحدة"
            },
            "UPS": {
                "full_name": "United Parcel Service",
                "arabic_name": "يو بي اس",
                "aliases": ["UPS", "United Parcel", "يو بي اس", "يو بي إس"],
                "type": "جوي",
                "country": "الولايات المتحدة"
            },
            "TNT": {
                "full_name": "TNT Express",
                "arabic_name": "تي ان تي",
                "aliases": ["TNT", "TNT Express", "تي ان تي", "تي إن تي"],
                "type": "جوي",
                "country": "هولندا"
            },
            "ARAMEX": {
                "full_name": "Aramex International",
                "arabic_name": "أرامكس",
                "aliases": ["Aramex", "أرامكس", "ارامكس"],
                "type": "جوي",
                "country": "الإمارات العربية المتحدة"
            },
            
            # شركات الشحن الإقليمية والمحلية
            "SAUDI_SHIPPING": {
                "full_name": "Saudi Shipping Company",
                "arabic_name": "الشركة السعودية للنقل البحري",
                "aliases": ["Saudi Shipping", "الشركة السعودية للنقل", "الشحن السعودي"],
                "type": "بحري",
                "country": "السعودية"
            },
            "GULF_SHIPPING": {
                "full_name": "Gulf Shipping Lines",
                "arabic_name": "خطوط الخليج للشحن",
                "aliases": ["Gulf Shipping", "خطوط الخليج", "الخليج للشحن", "شركة الخليج"],
                "type": "بحري",
                "country": "الإمارات العربية المتحدة"
            },
            "EMIRATES_SHIPPING": {
                "full_name": "Emirates Shipping Line",
                "arabic_name": "خط الإمارات للشحن",
                "aliases": ["Emirates Shipping", "خط الإمارات", "الإمارات للشحن"],
                "type": "بحري",
                "country": "الإمارات العربية المتحدة"
            },
            "NSCSA": {
                "full_name": "National Shipping Company of Saudi Arabia",
                "arabic_name": "الشركة الوطنية للنقل البحري",
                "aliases": ["NSCSA", "National Shipping", "الشركة الوطنية للنقل"],
                "type": "بحري",
                "country": "السعودية"
            }
        }
        
        # إنشاء فهرس للبحث السريع
        self.search_index = self._build_search_index()
    
    def _build_search_index(self) -> Dict[str, str]:
        """بناء فهرس للبحث السريع في أسماء الشركات"""
        index = {}
        
        for company_key, company_data in self.global_shipping_companies.items():
            # إضافة الاسم الكامل
            index[company_data["full_name"].lower()] = company_key
            index[company_data["arabic_name"].lower()] = company_key
            
            # إضافة الأسماء البديلة
            for alias in company_data["aliases"]:
                index[alias.lower()] = company_key
        
        return index
    
    def normalize_text(self, text: str) -> str:
        """تطبيع النص للمقارنة"""
        if not text:
            return ""
        
        # إزالة المسافات الزائدة والأحرف الخاصة
        text = re.sub(r'\s+', ' ', text.strip())
        text = re.sub(r'[^\w\s\u0600-\u06FF]', '', text)  # الحفاظ على الأحرف العربية
        
        return text.lower()
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """حساب نسبة التشابه بين نصين"""
        return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
    
    def find_best_match(self, input_name: str, threshold: float = 0.6) -> Optional[Dict]:
        """البحث عن أفضل تطابق لاسم الشركة المدخل"""
        if not input_name or not input_name.strip():
            return None
        
        normalized_input = self.normalize_text(input_name)
        
        # البحث المباشر في الفهرس
        if normalized_input in self.search_index:
            company_key = self.search_index[normalized_input]
            return {
                "company_key": company_key,
                "company_data": self.global_shipping_companies[company_key],
                "similarity": 1.0,
                "match_type": "exact"
            }
        
        # البحث بالتشابه
        best_match = None
        best_similarity = 0.0
        
        for search_term, company_key in self.search_index.items():
            similarity = self.calculate_similarity(normalized_input, search_term)
            
            if similarity > best_similarity and similarity >= threshold:
                best_similarity = similarity
                best_match = {
                    "company_key": company_key,
                    "company_data": self.global_shipping_companies[company_key],
                    "similarity": similarity,
                    "match_type": "fuzzy",
                    "matched_term": search_term
                }
        
        return best_match
    
    def suggest_corrections(self, input_name: str, max_suggestions: int = 3) -> List[Dict]:
        """اقتراح تصحيحات لاسم الشركة"""
        suggestions = []
        
        if not input_name or not input_name.strip():
            return suggestions
        
        normalized_input = self.normalize_text(input_name)
        similarity_scores = []
        
        # حساب التشابه مع جميع الشركات
        for company_key, company_data in self.global_shipping_companies.items():
            max_similarity = 0.0
            best_match_name = ""
            
            # فحص الاسم الكامل
            similarity = self.calculate_similarity(normalized_input, company_data["full_name"])
            if similarity > max_similarity:
                max_similarity = similarity
                best_match_name = company_data["full_name"]
            
            # فحص الاسم العربي
            similarity = self.calculate_similarity(normalized_input, company_data["arabic_name"])
            if similarity > max_similarity:
                max_similarity = similarity
                best_match_name = company_data["arabic_name"]
            
            # فحص الأسماء البديلة
            for alias in company_data["aliases"]:
                similarity = self.calculate_similarity(normalized_input, alias)
                if similarity > max_similarity:
                    max_similarity = similarity
                    best_match_name = alias
            
            if max_similarity > 0.3:  # حد أدنى للتشابه
                similarity_scores.append({
                    "company_key": company_key,
                    "company_data": company_data,
                    "similarity": max_similarity,
                    "suggested_name": best_match_name
                })
        
        # ترتيب النتائج حسب التشابه
        similarity_scores.sort(key=lambda x: x["similarity"], reverse=True)
        
        # إرجاع أفضل الاقتراحات
        return similarity_scores[:max_suggestions]
    
    def validate_and_correct(self, input_name: str) -> Dict:
        """التحقق من صحة اسم الشركة واقتراح التصحيح"""
        result = {
            "original_name": input_name,
            "is_valid": False,
            "corrected_name": None,
            "company_info": None,
            "suggestions": [],
            "confidence": 0.0
        }
        
        if not input_name or not input_name.strip():
            return result
        
        # البحث عن تطابق مباشر أو قريب
        best_match = self.find_best_match(input_name, threshold=0.7)
        
        if best_match:
            result["is_valid"] = True
            result["corrected_name"] = best_match["company_data"]["arabic_name"]
            result["company_info"] = best_match["company_data"]
            result["confidence"] = best_match["similarity"]
            
            if best_match["similarity"] < 1.0:
                result["suggestions"] = [best_match]
        else:
            # اقتراح تصحيحات محتملة
            suggestions = self.suggest_corrections(input_name)
            result["suggestions"] = suggestions
            
            if suggestions:
                # اختيار أفضل اقتراح كتصحيح
                best_suggestion = suggestions[0]
                if best_suggestion["similarity"] > 0.6:
                    result["corrected_name"] = best_suggestion["suggested_name"]
                    result["company_info"] = best_suggestion["company_data"]
                    result["confidence"] = best_suggestion["similarity"]
        
        return result
    
    def get_company_by_key(self, company_key: str) -> Optional[Dict]:
        """الحصول على بيانات الشركة بالمفتاح"""
        return self.global_shipping_companies.get(company_key)
    
    def get_all_companies(self) -> Dict:
        """الحصول على جميع الشركات"""
        return self.global_shipping_companies
    
    def get_companies_by_type(self, shipping_type: str) -> List[Dict]:
        """الحصول على الشركات حسب نوع الشحن"""
        companies = []
        for company_key, company_data in self.global_shipping_companies.items():
            if company_data["type"] == shipping_type:
                companies.append({
                    "key": company_key,
                    "data": company_data
                })
        return companies
