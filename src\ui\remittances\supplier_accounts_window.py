# -*- coding: utf-8 -*-
"""
نافذة إدارة حسابات الموردين
Supplier Accounts Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTableWidget, QTableWidgetItem, QPushButton, QLabel,
                               QComboBox, QFrame, QSplitter, QGroupBox, QGridLayout,
                               QMessageBox, QHeaderView, QAbstractItemView, QMenu,
                               QToolBar, QStatusBar, QProgressBar, QTextEdit,
                               QTabWidget, QScrollArea, QDoubleSpinBox, QLineEdit,
                               QDateEdit, QCheckBox)
from PySide6.QtCore import Qt, QDate, Signal, QTimer
from PySide6.QtGui import QIcon, QFont, QPixmap, QAction, QPalette, QColor

import sqlite3
from pathlib import Path
from datetime import datetime, timedelta
import json

from ...utils.arabic_support import reshape_arabic_text

class SupplierAccountsWindow(QMainWindow):
    """نافذة إدارة حسابات الموردين"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة حسابات الموردين - ProShipment")
        self.setMinimumSize(1400, 800)
        self.resize(1600, 900)
        
        # متغيرات النافذة
        self.current_accounts = []
        self.current_transactions = []
        self.selected_account_id = None
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        
        # تحميل البيانات الأولية
        self.load_accounts()
        self.load_filters_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط الفلاتر
        self.create_filters_section(main_layout)
        
        # المحتوى الرئيسي - ثلاثة أقسام
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - قائمة الحسابات
        left_panel = self.create_accounts_list_panel()
        splitter.addWidget(left_panel)
        
        # الوسط - تفاصيل الحساب
        middle_panel = self.create_account_details_panel()
        splitter.addWidget(middle_panel)
        
        # الجانب الأيمن - المعاملات
        right_panel = self.create_transactions_panel()
        splitter.addWidget(right_panel)
        
        # تعيين النسب
        splitter.setSizes([400, 500, 500])
        main_layout.addWidget(splitter)
        
    def create_filters_section(self, layout):
        """إنشاء قسم الفلاتر"""
        filters_frame = QFrame()
        filters_frame.setFrameStyle(QFrame.StyledPanel)
        filters_frame.setStyleSheet("""
            QFrame {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        filters_layout = QHBoxLayout(filters_frame)
        filters_layout.setSpacing(15)
        
        # فلتر المورد
        filters_layout.addWidget(QLabel("المورد:"))
        self.supplier_filter = QComboBox()
        self.supplier_filter.addItem("جميع الموردين", "")
        self.supplier_filter.setMinimumWidth(200)
        filters_layout.addWidget(self.supplier_filter)
        
        # فلتر العملة
        filters_layout.addWidget(QLabel("العملة:"))
        self.currency_filter = QComboBox()
        self.currency_filter.addItem("جميع العملات", "")
        self.currency_filter.setMinimumWidth(150)
        filters_layout.addWidget(self.currency_filter)
        
        # فلتر الحالة
        filters_layout.addWidget(QLabel("الحالة:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحسابات", "نشط", "محجوز", "غير نشط"])
        self.status_filter.setMinimumWidth(120)
        filters_layout.addWidget(self.status_filter)
        
        # أزرار الفلاتر
        self.apply_filters_btn = QPushButton("🔍 تطبيق")
        self.apply_filters_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        filters_layout.addWidget(self.apply_filters_btn)
        
        self.clear_filters_btn = QPushButton("🗑️ مسح")
        self.clear_filters_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        filters_layout.addWidget(self.clear_filters_btn)
        
        filters_layout.addStretch()
        layout.addWidget(filters_frame)
        
    def create_accounts_list_panel(self):
        """إنشاء لوحة قائمة الحسابات"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # عنوان القسم
        title_label = QLabel("حسابات الموردين")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #1e293b; padding: 10px;")
        layout.addWidget(title_label)
        
        # جدول الحسابات
        self.accounts_table = QTableWidget()
        self.setup_accounts_table()
        layout.addWidget(self.accounts_table)
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.StyledPanel)
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f1f5f9;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        
        stats_layout = QVBoxLayout(stats_frame)
        
        self.total_accounts_label = QLabel("إجمالي الحسابات: 0")
        self.active_accounts_label = QLabel("الحسابات النشطة: 0")
        self.total_balance_label = QLabel("إجمالي الأرصدة: 0.00")
        
        for label in [self.total_accounts_label, self.active_accounts_label, self.total_balance_label]:
            label.setStyleSheet("font-weight: bold; color: #374151; font-size: 12px;")
            stats_layout.addWidget(label)
        
        layout.addWidget(stats_frame)
        
        return panel
        
    def create_account_details_panel(self):
        """إنشاء لوحة تفاصيل الحساب"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # عنوان القسم
        title_label = QLabel("تفاصيل الحساب")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #1e293b; padding: 10px;")
        layout.addWidget(title_label)
        
        # تبويبات التفاصيل
        self.details_tabs = QTabWidget()
        
        # تبويب المعلومات الأساسية
        basic_info_tab = self.create_basic_info_tab()
        self.details_tabs.addTab(basic_info_tab, "المعلومات الأساسية")
        
        # تبويب الأرصدة والحدود
        balances_tab = self.create_balances_tab()
        self.details_tabs.addTab(balances_tab, "الأرصدة والحدود")
        
        # تبويب الإعدادات
        settings_tab = self.create_settings_tab()
        self.details_tabs.addTab(settings_tab, "الإعدادات")
        
        layout.addWidget(self.details_tabs)
        
        # أزرار الإجراءات
        actions_frame = QFrame()
        actions_layout = QHBoxLayout(actions_frame)
        
        self.edit_account_btn = QPushButton("✏️ تعديل الحساب")
        self.edit_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #f59e0b;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d97706;
            }
        """)
        
        self.block_account_btn = QPushButton("🔒 حجز الحساب")
        self.block_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #dc2626;
            }
        """)
        
        self.new_transaction_btn = QPushButton("💰 معاملة جديدة")
        self.new_transaction_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        
        actions_layout.addWidget(self.edit_account_btn)
        actions_layout.addWidget(self.block_account_btn)
        actions_layout.addWidget(self.new_transaction_btn)
        actions_layout.addStretch()
        
        layout.addWidget(actions_frame)
        
        return panel
        
    def create_transactions_panel(self):
        """إنشاء لوحة المعاملات"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # عنوان القسم
        title_label = QLabel("معاملات الحساب")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #1e293b; padding: 10px;")
        layout.addWidget(title_label)
        
        # فلاتر المعاملات
        trans_filters_frame = QFrame()
        trans_filters_layout = QHBoxLayout(trans_filters_frame)
        
        trans_filters_layout.addWidget(QLabel("نوع المعاملة:"))
        self.transaction_type_filter = QComboBox()
        self.transaction_type_filter.addItems(["جميع المعاملات", "دائن", "مدين", "تحويل"])
        trans_filters_layout.addWidget(self.transaction_type_filter)
        
        trans_filters_layout.addWidget(QLabel("من تاريخ:"))
        self.trans_date_from = QDateEdit()
        self.trans_date_from.setDate(QDate.currentDate().addDays(-30))
        self.trans_date_from.setCalendarPopup(True)
        trans_filters_layout.addWidget(self.trans_date_from)
        
        trans_filters_layout.addWidget(QLabel("إلى تاريخ:"))
        self.trans_date_to = QDateEdit()
        self.trans_date_to.setDate(QDate.currentDate())
        self.trans_date_to.setCalendarPopup(True)
        trans_filters_layout.addWidget(self.trans_date_to)
        
        trans_filters_layout.addStretch()
        layout.addWidget(trans_filters_frame)
        
        # جدول المعاملات
        self.transactions_table = QTableWidget()
        self.setup_transactions_table()
        layout.addWidget(self.transactions_table)
        
        # إحصائيات المعاملات
        trans_stats_frame = QFrame()
        trans_stats_frame.setFrameStyle(QFrame.StyledPanel)
        trans_stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f1f5f9;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        
        trans_stats_layout = QHBoxLayout(trans_stats_frame)
        
        self.total_transactions_label = QLabel("إجمالي المعاملات: 0")
        self.total_debits_label = QLabel("إجمالي المدين: 0.00")
        self.total_credits_label = QLabel("إجمالي الدائن: 0.00")
        
        for label in [self.total_transactions_label, self.total_debits_label, self.total_credits_label]:
            label.setStyleSheet("font-weight: bold; color: #374151; font-size: 11px;")
            trans_stats_layout.addWidget(label)
        
        trans_stats_layout.addStretch()
        layout.addWidget(trans_stats_frame)
        
        return panel

    def setup_accounts_table(self):
        """إعداد جدول الحسابات"""
        headers = [
            "المورد", "العملة", "رقم الحساب", "الرصيد الحالي",
            "الرصيد المتاح", "حد الائتمان", "الحالة", "آخر معاملة"
        ]

        self.accounts_table.setColumnCount(len(headers))
        self.accounts_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.accounts_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.accounts_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.accounts_table.setAlternatingRowColors(True)
        self.accounts_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.accounts_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)           # المورد
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # العملة
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # رقم الحساب
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الرصيد الحالي
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الرصيد المتاح
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # حد الائتمان
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الحالة

        # تنسيق الجدول
        self.accounts_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e2e8f0;
                background-color: white;
                alternate-background-color: #f8fafc;
                selection-background-color: #3b82f6;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e2e8f0;
            }
            QHeaderView::section {
                background-color: #f1f5f9;
                color: #374151;
                padding: 10px;
                border: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)

    def setup_transactions_table(self):
        """إعداد جدول المعاملات"""
        headers = [
            "رقم المعاملة", "التاريخ", "النوع", "المبلغ المدين",
            "المبلغ الدائن", "الرصيد بعد", "الوصف", "المستخدم"
        ]

        self.transactions_table.setColumnCount(len(headers))
        self.transactions_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.transactions_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.transactions_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.transactions_table.setAlternatingRowColors(True)
        self.transactions_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.transactions_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم المعاملة
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # النوع
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # المبلغ المدين
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # المبلغ الدائن
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الرصيد بعد
        header.setSectionResizeMode(6, QHeaderView.Stretch)           # الوصف

        # تنسيق الجدول
        self.transactions_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e2e8f0;
                background-color: white;
                alternate-background-color: #f8fafc;
                selection-background-color: #3b82f6;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #e2e8f0;
            }
            QHeaderView::section {
                background-color: #f1f5f9;
                color: #374151;
                padding: 8px;
                border: 1px solid #d1d5db;
                font-weight: bold;
                font-size: 11px;
            }
        """)

    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # معلومات الحساب
        account_group = QGroupBox("معلومات الحساب")
        account_layout = QGridLayout(account_group)

        self.supplier_name_label = QLabel("-")
        self.currency_name_label = QLabel("-")
        self.account_number_label = QLabel("-")
        self.created_date_label = QLabel("-")

        account_layout.addWidget(QLabel("المورد:"), 0, 0)
        account_layout.addWidget(self.supplier_name_label, 0, 1)
        account_layout.addWidget(QLabel("العملة:"), 0, 2)
        account_layout.addWidget(self.currency_name_label, 0, 3)

        account_layout.addWidget(QLabel("رقم الحساب:"), 1, 0)
        account_layout.addWidget(self.account_number_label, 1, 1)
        account_layout.addWidget(QLabel("تاريخ الإنشاء:"), 1, 2)
        account_layout.addWidget(self.created_date_label, 1, 3)

        layout.addWidget(account_group)

        # معلومات المورد
        supplier_group = QGroupBox("معلومات المورد")
        supplier_layout = QGridLayout(supplier_group)

        self.supplier_code_label = QLabel("-")
        self.supplier_type_label = QLabel("-")
        self.supplier_phone_label = QLabel("-")
        self.supplier_email_label = QLabel("-")

        supplier_layout.addWidget(QLabel("كود المورد:"), 0, 0)
        supplier_layout.addWidget(self.supplier_code_label, 0, 1)
        supplier_layout.addWidget(QLabel("نوع المورد:"), 0, 2)
        supplier_layout.addWidget(self.supplier_type_label, 0, 3)

        supplier_layout.addWidget(QLabel("الهاتف:"), 1, 0)
        supplier_layout.addWidget(self.supplier_phone_label, 1, 1)
        supplier_layout.addWidget(QLabel("البريد الإلكتروني:"), 1, 2)
        supplier_layout.addWidget(self.supplier_email_label, 1, 3)

        layout.addWidget(supplier_group)
        layout.addStretch()

        return tab

    def create_balances_tab(self):
        """إنشاء تبويب الأرصدة والحدود"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # الأرصدة
        balances_group = QGroupBox("الأرصدة")
        balances_layout = QGridLayout(balances_group)

        self.opening_balance_label = QLabel("-")
        self.current_balance_label = QLabel("-")
        self.available_balance_label = QLabel("-")
        self.blocked_balance_label = QLabel("-")

        balances_layout.addWidget(QLabel("الرصيد الافتتاحي:"), 0, 0)
        balances_layout.addWidget(self.opening_balance_label, 0, 1)
        balances_layout.addWidget(QLabel("الرصيد الحالي:"), 0, 2)
        balances_layout.addWidget(self.current_balance_label, 0, 3)

        balances_layout.addWidget(QLabel("الرصيد المتاح:"), 1, 0)
        balances_layout.addWidget(self.available_balance_label, 1, 1)
        balances_layout.addWidget(QLabel("الرصيد المحجوز:"), 1, 2)
        balances_layout.addWidget(self.blocked_balance_label, 1, 3)

        # تنسيق الرصيد الحالي
        self.current_balance_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #059669;
                background-color: #ecfdf5;
                padding: 8px;
                border-radius: 6px;
                border: 1px solid #a7f3d0;
            }
        """)

        layout.addWidget(balances_group)

        # حدود الائتمان
        credit_group = QGroupBox("حدود الائتمان")
        credit_layout = QGridLayout(credit_group)

        self.credit_limit_label = QLabel("-")
        self.used_credit_label = QLabel("-")
        self.available_credit_label = QLabel("-")

        credit_layout.addWidget(QLabel("حد الائتمان:"), 0, 0)
        credit_layout.addWidget(self.credit_limit_label, 0, 1)
        credit_layout.addWidget(QLabel("الائتمان المستخدم:"), 0, 2)
        credit_layout.addWidget(self.used_credit_label, 0, 3)

        credit_layout.addWidget(QLabel("الائتمان المتاح:"), 1, 0)
        credit_layout.addWidget(self.available_credit_label, 1, 1, 1, 3)

        # تنسيق الائتمان المتاح
        self.available_credit_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #3b82f6;
                background-color: #eff6ff;
                padding: 8px;
                border-radius: 6px;
                border: 1px solid #93c5fd;
            }
        """)

        layout.addWidget(credit_group)
        layout.addStretch()

        return tab

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات الحساب
        settings_group = QGroupBox("إعدادات الحساب")
        settings_layout = QGridLayout(settings_group)

        self.is_active_label = QLabel("-")
        self.is_blocked_label = QLabel("-")
        self.block_reason_label = QLabel("-")
        self.last_update_label = QLabel("-")

        settings_layout.addWidget(QLabel("حالة الحساب:"), 0, 0)
        settings_layout.addWidget(self.is_active_label, 0, 1)
        settings_layout.addWidget(QLabel("محجوز:"), 0, 2)
        settings_layout.addWidget(self.is_blocked_label, 0, 3)

        settings_layout.addWidget(QLabel("سبب الحجز:"), 1, 0)
        settings_layout.addWidget(self.block_reason_label, 1, 1, 1, 3)

        settings_layout.addWidget(QLabel("آخر تحديث:"), 2, 0)
        settings_layout.addWidget(self.last_update_label, 2, 1, 1, 3)

        layout.addWidget(settings_group)

        # ملاحظات
        notes_group = QGroupBox("ملاحظات الحساب")
        notes_layout = QVBoxLayout(notes_group)

        self.account_notes_text = QTextEdit()
        self.account_notes_text.setReadOnly(True)
        self.account_notes_text.setMaximumHeight(100)
        notes_layout.addWidget(self.account_notes_text)

        layout.addWidget(notes_group)
        layout.addStretch()

        return tab

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # إنشاء حساب جديد
        new_account_action = QAction("➕ حساب جديد", self)
        new_account_action.setStatusTip("إنشاء حساب جديد لمورد")
        new_account_action.triggered.connect(self.create_new_account)
        toolbar.addAction(new_account_action)

        toolbar.addSeparator()

        # تحديث البيانات
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setStatusTip("تحديث قائمة الحسابات")
        refresh_action.triggered.connect(self.load_accounts)
        toolbar.addAction(refresh_action)

        # تصدير البيانات
        export_action = QAction("📊 تصدير", self)
        export_action.setStatusTip("تصدير البيانات إلى Excel")
        export_action.triggered.connect(self.export_data)
        toolbar.addAction(export_action)

        toolbar.addSeparator()

        # التقارير
        reports_action = QAction("📈 تقارير الحسابات", self)
        reports_action.setStatusTip("عرض تقارير حسابات الموردين")
        reports_action.triggered.connect(self.show_reports)
        toolbar.addAction(reports_action)

    def setup_statusbar(self):
        """إعداد شريط الحالة"""
        self.statusbar = self.statusBar()

        # عداد الحسابات
        self.accounts_count_label = QLabel("الحسابات: 0")
        self.statusbar.addWidget(self.accounts_count_label)

        # فاصل
        self.statusbar.addPermanentWidget(QLabel("|"))

        # حالة الاتصال بقاعدة البيانات
        self.db_status_label = QLabel("قاعدة البيانات: متصل")
        self.db_status_label.setStyleSheet("color: green;")
        self.statusbar.addPermanentWidget(self.db_status_label)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusbar.addPermanentWidget(self.progress_bar)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # الفلاتر
        self.apply_filters_btn.clicked.connect(self.apply_filters)
        self.clear_filters_btn.clicked.connect(self.clear_filters)

        # فلاتر الكومبو بوكس
        self.supplier_filter.currentTextChanged.connect(self.apply_filters)
        self.currency_filter.currentTextChanged.connect(self.apply_filters)
        self.status_filter.currentTextChanged.connect(self.apply_filters)

        # فلاتر المعاملات
        self.transaction_type_filter.currentTextChanged.connect(self.load_transactions)
        self.trans_date_from.dateChanged.connect(self.load_transactions)
        self.trans_date_to.dateChanged.connect(self.load_transactions)

        # جدول الحسابات
        self.accounts_table.itemSelectionChanged.connect(self.on_account_selected)
        self.accounts_table.itemDoubleClicked.connect(self.edit_account)

        # أزرار الإجراءات
        self.edit_account_btn.clicked.connect(self.edit_account)
        self.block_account_btn.clicked.connect(self.toggle_account_block)
        self.new_transaction_btn.clicked.connect(self.create_new_transaction)

    def load_accounts(self):
        """تحميل قائمة حسابات الموردين"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)

            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # استعلام الحسابات مع معلومات المورد والعملة
            query = """
                SELECT
                    sa.id, s.name as supplier_name, c.code as currency_code,
                    sa.account_number, sa.current_balance, sa.available_balance,
                    sa.credit_limit, sa.is_active, sa.is_blocked,
                    sa.last_transaction_date, c.name as currency_name
                FROM supplier_accounts sa
                LEFT JOIN suppliers s ON sa.supplier_id = s.id
                LEFT JOIN currencies c ON sa.currency_id = c.id
                ORDER BY s.name, c.code
            """

            cursor.execute(query)
            accounts = cursor.fetchall()

            # تحديث الجدول
            self.update_accounts_table(accounts)

            # تحديث الإحصائيات
            self.update_accounts_statistics()

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الحسابات:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def update_accounts_table(self, accounts):
        """تحديث جدول الحسابات"""
        self.current_accounts = accounts
        self.accounts_table.setRowCount(len(accounts))

        for row, account in enumerate(accounts):
            # المورد
            self.accounts_table.setItem(row, 0, QTableWidgetItem(str(account[1] or "")))

            # العملة
            self.accounts_table.setItem(row, 1, QTableWidgetItem(str(account[2] or "")))

            # رقم الحساب
            self.accounts_table.setItem(row, 2, QTableWidgetItem(str(account[3] or "")))

            # الرصيد الحالي
            current_balance = f"{account[4]:,.2f}" if account[4] else "0.00"
            balance_item = QTableWidgetItem(current_balance)
            if account[4] and account[4] < 0:
                balance_item.setBackground(QColor("#fee2e2"))
            elif account[4] and account[4] > 0:
                balance_item.setBackground(QColor("#dcfce7"))
            self.accounts_table.setItem(row, 3, balance_item)

            # الرصيد المتاح
            available_balance = f"{account[5]:,.2f}" if account[5] else "0.00"
            self.accounts_table.setItem(row, 4, QTableWidgetItem(available_balance))

            # حد الائتمان
            credit_limit = f"{account[6]:,.2f}" if account[6] else "0.00"
            self.accounts_table.setItem(row, 5, QTableWidgetItem(credit_limit))

            # الحالة
            status = "نشط" if account[7] else "غير نشط"
            if account[8]:  # محجوز
                status = "محجوز"
            status_item = QTableWidgetItem(status)

            if account[8]:  # محجوز
                status_item.setBackground(QColor("#fee2e2"))
            elif account[7]:  # نشط
                status_item.setBackground(QColor("#dcfce7"))
            else:  # غير نشط
                status_item.setBackground(QColor("#f3f4f6"))

            self.accounts_table.setItem(row, 6, status_item)

            # آخر معاملة
            last_transaction = ""
            if account[9]:
                try:
                    date_obj = datetime.fromisoformat(account[9].replace('Z', '+00:00'))
                    last_transaction = date_obj.strftime('%Y-%m-%d')
                except:
                    last_transaction = str(account[9])
            self.accounts_table.setItem(row, 7, QTableWidgetItem(last_transaction))

        # تحديث عداد الحسابات
        self.accounts_count_label.setText(f"الحسابات: {len(accounts)}")

    def update_accounts_statistics(self):
        """تحديث إحصائيات الحسابات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إجمالي الحسابات
            cursor.execute("SELECT COUNT(*) FROM supplier_accounts")
            total_accounts = cursor.fetchone()[0]

            # الحسابات النشطة
            cursor.execute("SELECT COUNT(*) FROM supplier_accounts WHERE is_active = 1 AND is_blocked = 0")
            active_accounts = cursor.fetchone()[0]

            # إجمالي الأرصدة (بالعملة الأساسية)
            cursor.execute("""
                SELECT SUM(sa.current_balance * COALESCE(c.exchange_rate, 1.0))
                FROM supplier_accounts sa
                LEFT JOIN currencies c ON sa.currency_id = c.id
                WHERE sa.is_active = 1
            """)
            total_balance = cursor.fetchone()[0] or 0

            # تحديث التسميات
            self.total_accounts_label.setText(f"إجمالي الحسابات: {total_accounts}")
            self.active_accounts_label.setText(f"الحسابات النشطة: {active_accounts}")
            self.total_balance_label.setText(f"إجمالي الأرصدة: {total_balance:,.2f} ريال")

            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث إحصائيات الحسابات: {e}")

    def load_filters_data(self):
        """تحميل بيانات الفلاتر"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل الموردين
            cursor.execute("SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name")
            suppliers = cursor.fetchall()
            for supplier in suppliers:
                self.supplier_filter.addItem(supplier[1], supplier[0])

            # تحميل العملات
            cursor.execute("SELECT id, code, name FROM currencies WHERE is_active = 1 ORDER BY name")
            currencies = cursor.fetchall()
            for currency in currencies:
                self.currency_filter.addItem(f"{currency[1]} - {currency[2]}", currency[0])

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الفلاتر: {e}")

    def apply_filters(self):
        """تطبيق الفلاتر على قائمة الحسابات"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)

            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # بناء الاستعلام مع الفلاتر
            query = """
                SELECT
                    sa.id, s.name as supplier_name, c.code as currency_code,
                    sa.account_number, sa.current_balance, sa.available_balance,
                    sa.credit_limit, sa.is_active, sa.is_blocked,
                    sa.last_transaction_date, c.name as currency_name
                FROM supplier_accounts sa
                LEFT JOIN suppliers s ON sa.supplier_id = s.id
                LEFT JOIN currencies c ON sa.currency_id = c.id
                WHERE 1=1
            """

            params = []

            # فلتر المورد
            supplier_id = self.supplier_filter.currentData()
            if supplier_id:
                query += " AND sa.supplier_id = ?"
                params.append(supplier_id)

            # فلتر العملة
            currency_id = self.currency_filter.currentData()
            if currency_id:
                query += " AND sa.currency_id = ?"
                params.append(currency_id)

            # فلتر الحالة
            status_filter = self.status_filter.currentText()
            if status_filter == "نشط":
                query += " AND sa.is_active = 1 AND sa.is_blocked = 0"
            elif status_filter == "محجوز":
                query += " AND sa.is_blocked = 1"
            elif status_filter == "غير نشط":
                query += " AND sa.is_active = 0"

            query += " ORDER BY s.name, c.code"

            cursor.execute(query, params)
            accounts = cursor.fetchall()

            self.update_accounts_table(accounts)
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تطبيق الفلاتر:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.supplier_filter.setCurrentIndex(0)
        self.currency_filter.setCurrentIndex(0)
        self.status_filter.setCurrentIndex(0)
        self.load_accounts()

    def on_account_selected(self):
        """معالج اختيار حساب من الجدول"""
        current_row = self.accounts_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_accounts):
            account_data = self.current_accounts[current_row]
            self.selected_account_id = account_data[0]
            self.load_account_details(self.selected_account_id)
            self.load_transactions()

    def load_account_details(self, account_id):
        """تحميل تفاصيل الحساب المحدد"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # استعلام تفاصيل الحساب
            query = """
                SELECT
                    sa.*, s.name as supplier_name, s.code as supplier_code,
                    s.supplier_type, s.phone, s.email, c.code as currency_code,
                    c.name as currency_name
                FROM supplier_accounts sa
                LEFT JOIN suppliers s ON sa.supplier_id = s.id
                LEFT JOIN currencies c ON sa.currency_id = c.id
                WHERE sa.id = ?
            """

            cursor.execute(query, (account_id,))
            account = cursor.fetchone()

            if account:
                self.update_account_details_display(account)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل تفاصيل الحساب:\n{str(e)}")

    def update_account_details_display(self, account):
        """تحديث عرض تفاصيل الحساب"""
        # المعلومات الأساسية
        self.supplier_name_label.setText(str(account[17] or "-"))
        self.currency_name_label.setText(f"{account[23]} - {account[24]}" if account[23] else "-")
        self.account_number_label.setText(str(account[3] or "-"))

        if account[16]:  # created_at
            try:
                date_obj = datetime.fromisoformat(account[16].replace('Z', '+00:00'))
                self.created_date_label.setText(date_obj.strftime('%Y-%m-%d'))
            except:
                self.created_date_label.setText(str(account[16]))
        else:
            self.created_date_label.setText("-")

        # معلومات المورد
        self.supplier_code_label.setText(str(account[18] or "-"))
        self.supplier_type_label.setText(str(account[19] or "-"))
        self.supplier_phone_label.setText(str(account[20] or "-"))
        self.supplier_email_label.setText(str(account[21] or "-"))

        # الأرصدة
        self.opening_balance_label.setText(f"{account[4]:,.2f}" if account[4] else "0.00")
        self.current_balance_label.setText(f"{account[5]:,.2f}" if account[5] else "0.00")
        self.available_balance_label.setText(f"{account[6]:,.2f}" if account[6] else "0.00")
        self.blocked_balance_label.setText(f"{account[7]:,.2f}" if account[7] else "0.00")

        # حدود الائتمان
        self.credit_limit_label.setText(f"{account[8]:,.2f}" if account[8] else "0.00")
        self.used_credit_label.setText(f"{account[9]:,.2f}" if account[9] else "0.00")
        self.available_credit_label.setText(f"{account[10]:,.2f}" if account[10] else "0.00")

        # الإعدادات
        self.is_active_label.setText("نشط" if account[11] else "غير نشط")
        self.is_blocked_label.setText("محجوز" if account[12] else "غير محجوز")
        self.block_reason_label.setText(str(account[13] or "-"))

        if account[15]:  # updated_at
            try:
                date_obj = datetime.fromisoformat(account[15].replace('Z', '+00:00'))
                self.last_update_label.setText(date_obj.strftime('%Y-%m-%d %H:%M'))
            except:
                self.last_update_label.setText(str(account[15]))
        else:
            self.last_update_label.setText("-")

        # الملاحظات
        self.account_notes_text.setPlainText(str(account[16] or ""))

        # تحديث أزرار الإجراءات
        self.edit_account_btn.setEnabled(True)
        self.new_transaction_btn.setEnabled(True)

        if account[12]:  # محجوز
            self.block_account_btn.setText("🔓 إلغاء الحجز")
            self.block_account_btn.setStyleSheet("""
                QPushButton {
                    background-color: #10b981;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #059669;
                }
            """)
        else:
            self.block_account_btn.setText("🔒 حجز الحساب")
            self.block_account_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ef4444;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #dc2626;
                }
            """)

    def load_transactions(self):
        """تحميل معاملات الحساب المحدد"""
        if not self.selected_account_id:
            return

        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # بناء الاستعلام مع الفلاتر
            query = """
                SELECT
                    sat.transaction_number, sat.transaction_date, sat.transaction_type,
                    sat.debit_amount, sat.credit_amount, sat.balance_after,
                    sat.description, u.full_name as user_name
                FROM supplier_account_transactions sat
                LEFT JOIN users u ON sat.created_by = u.id
                WHERE sat.account_id = ?
            """

            params = [self.selected_account_id]

            # فلتر نوع المعاملة
            transaction_type = self.transaction_type_filter.currentText()
            if transaction_type != "جميع المعاملات":
                query += " AND sat.transaction_type = ?"
                params.append(transaction_type)

            # فلتر التاريخ
            date_from = self.trans_date_from.date().toString("yyyy-MM-dd")
            date_to = self.trans_date_to.date().toString("yyyy-MM-dd")
            query += " AND DATE(sat.transaction_date) BETWEEN ? AND ?"
            params.extend([date_from, date_to])

            query += " ORDER BY sat.transaction_date DESC"

            cursor.execute(query, params)
            transactions = cursor.fetchall()

            self.update_transactions_table(transactions)
            self.update_transactions_statistics(transactions)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المعاملات:\n{str(e)}")

    # دوال الإجراءات (ستكتمل لاحقاً)
    def create_new_account(self):
        """إنشاء حساب جديد"""
        from .new_supplier_account_dialog import NewSupplierAccountDialog

        dialog = NewSupplierAccountDialog(self)
        if dialog.exec() == QDialog.Accepted:
            self.load_accounts()
            QMessageBox.information(self, "نجح", "تم إنشاء الحساب بنجاح")

    def edit_account(self):
        """تعديل الحساب المحدد"""
        if self.selected_account_id:
            QMessageBox.information(self, "قريباً", "نافذة تعديل الحساب قيد التطوير")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حساب للتعديل")

    def toggle_account_block(self):
        """تبديل حالة حجز الحساب"""
        if self.selected_account_id:
            QMessageBox.information(self, "قريباً", "وظيفة حجز/إلغاء حجز الحساب قيد التطوير")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حساب")

    def create_new_transaction(self):
        """إنشاء معاملة جديدة"""
        if self.selected_account_id:
            QMessageBox.information(self, "قريباً", "نافذة إنشاء معاملة جديدة قيد التطوير")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حساب")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "قريباً", "وظيفة التصدير قيد التطوير")

    def show_reports(self):
        """عرض التقارير"""
        QMessageBox.information(self, "قريباً", "نافذة التقارير قيد التطوير")

    def update_transactions_table(self, transactions):
        """تحديث جدول المعاملات"""
        self.current_transactions = transactions
        self.transactions_table.setRowCount(len(transactions))

        for row, transaction in enumerate(transactions):
            # رقم المعاملة
            self.transactions_table.setItem(row, 0, QTableWidgetItem(str(transaction[0] or "")))

            # التاريخ
            date_str = ""
            if transaction[1]:
                try:
                    date_obj = datetime.fromisoformat(transaction[1].replace('Z', '+00:00'))
                    date_str = date_obj.strftime('%Y-%m-%d %H:%M')
                except:
                    date_str = str(transaction[1])
            self.transactions_table.setItem(row, 1, QTableWidgetItem(date_str))

            # النوع
            type_item = QTableWidgetItem(str(transaction[2] or ""))
            if transaction[2] == "دائن":
                type_item.setBackground(QColor("#dcfce7"))
            elif transaction[2] == "مدين":
                type_item.setBackground(QColor("#fee2e2"))
            self.transactions_table.setItem(row, 2, type_item)

            # المبلغ المدين
            debit_amount = f"{transaction[3]:,.2f}" if transaction[3] else "0.00"
            self.transactions_table.setItem(row, 3, QTableWidgetItem(debit_amount))

            # المبلغ الدائن
            credit_amount = f"{transaction[4]:,.2f}" if transaction[4] else "0.00"
            self.transactions_table.setItem(row, 4, QTableWidgetItem(credit_amount))

            # الرصيد بعد المعاملة
            balance_after = f"{transaction[5]:,.2f}" if transaction[5] else "0.00"
            self.transactions_table.setItem(row, 5, QTableWidgetItem(balance_after))

            # الوصف
            self.transactions_table.setItem(row, 6, QTableWidgetItem(str(transaction[6] or "")))

            # المستخدم
            self.transactions_table.setItem(row, 7, QTableWidgetItem(str(transaction[7] or "غير محدد")))

    def update_transactions_statistics(self, transactions):
        """تحديث إحصائيات المعاملات"""
        total_transactions = len(transactions)
        total_debits = sum(t[3] for t in transactions if t[3])
        total_credits = sum(t[4] for t in transactions if t[4])

        self.total_transactions_label.setText(f"إجمالي المعاملات: {total_transactions}")
        self.total_debits_label.setText(f"إجمالي المدين: {total_debits:,.2f}")
        self.total_credits_label.setText(f"إجمالي الدائن: {total_credits:,.2f}")
