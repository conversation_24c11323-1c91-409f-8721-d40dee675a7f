# -*- coding: utf-8 -*-
"""
مدير اتصالات قاعدة البيانات المتقدم
Advanced Database Connection Manager
"""

import sqlite3
import threading
import time
import json
from pathlib import Path
from datetime import datetime, timedelta
from contextlib import contextmanager
from typing import Optional, Dict, Any, List
import logging
import queue

class ConnectionPool:
    """مجموعة اتصالات قاعدة البيانات"""
    
    def __init__(self, db_path: str, max_connections: int = 10, timeout: int = 30):
        self.db_path = db_path
        self.max_connections = max_connections
        self.timeout = timeout
        self.connections = queue.Queue(maxsize=max_connections)
        self.active_connections = {}
        self.connection_stats = {
            'total_created': 0,
            'total_closed': 0,
            'current_active': 0,
            'peak_usage': 0
        }
        self.lock = threading.Lock()
        
        # إنشاء الاتصالات الأولية
        self._initialize_pool()
        
    def _initialize_pool(self):
        """تهيئة مجموعة الاتصالات"""
        for _ in range(self.max_connections):
            conn = self._create_connection()
            if conn:
                self.connections.put(conn)
                
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """إنشاء اتصال جديد"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=self.timeout,
                check_same_thread=False
            )
            
            # تحسين إعدادات الاتصال
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=8192")
            conn.execute("PRAGMA temp_store=MEMORY")
            conn.execute("PRAGMA mmap_size=268435456")  # 256MB
            
            with self.lock:
                self.connection_stats['total_created'] += 1
                
            return conn
            
        except Exception as e:
            logging.error(f"خطأ في إنشاء اتصال قاعدة البيانات: {e}")
            return None
            
    def get_connection(self) -> Optional[sqlite3.Connection]:
        """الحصول على اتصال من المجموعة"""
        try:
            conn = self.connections.get(timeout=self.timeout)
            
            with self.lock:
                thread_id = threading.get_ident()
                self.active_connections[thread_id] = {
                    'connection': conn,
                    'acquired_at': datetime.now(),
                    'query_count': 0
                }
                self.connection_stats['current_active'] += 1
                if self.connection_stats['current_active'] > self.connection_stats['peak_usage']:
                    self.connection_stats['peak_usage'] = self.connection_stats['current_active']
                    
            return conn
            
        except queue.Empty:
            logging.warning("انتهت مهلة انتظار الحصول على اتصال قاعدة البيانات")
            return None
            
    def return_connection(self, conn: sqlite3.Connection):
        """إرجاع اتصال إلى المجموعة"""
        if conn:
            try:
                # التأكد من عدم وجود معاملات مفتوحة
                conn.rollback()
                
                with self.lock:
                    thread_id = threading.get_ident()
                    if thread_id in self.active_connections:
                        del self.active_connections[thread_id]
                        self.connection_stats['current_active'] -= 1
                        
                self.connections.put(conn)
                
            except Exception as e:
                logging.error(f"خطأ في إرجاع الاتصال: {e}")
                self._close_connection(conn)
                
    def _close_connection(self, conn: sqlite3.Connection):
        """إغلاق اتصال"""
        try:
            conn.close()
            with self.lock:
                self.connection_stats['total_closed'] += 1
        except Exception as e:
            logging.error(f"خطأ في إغلاق الاتصال: {e}")
            
    def close_all(self):
        """إغلاق جميع الاتصالات"""
        # إغلاق الاتصالات النشطة
        with self.lock:
            for thread_data in self.active_connections.values():
                self._close_connection(thread_data['connection'])
            self.active_connections.clear()
            
        # إغلاق الاتصالات في المجموعة
        while not self.connections.empty():
            try:
                conn = self.connections.get_nowait()
                self._close_connection(conn)
            except queue.Empty:
                break
                
    def get_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المجموعة"""
        with self.lock:
            return {
                **self.connection_stats,
                'pool_size': self.connections.qsize(),
                'max_connections': self.max_connections,
                'active_connections': len(self.active_connections)
            }

class DatabaseConnectionManager:
    """مدير اتصالات قاعدة البيانات المتقدم"""
    
    def __init__(self, db_path: str = "data/proshipment.db"):
        self.db_path = Path(db_path)
        self.connection_pool = None
        self.query_stats = {}
        self.performance_monitor = None
        self.settings = self._load_settings()
        
        # إعداد نظام السجلات
        self._setup_logging()
        
        # تهيئة مجموعة الاتصالات
        self._initialize_connection_pool()
        
        # بدء مراقبة الأداء
        self._start_performance_monitoring()
        
    def _load_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات قاعدة البيانات"""
        settings_file = Path("config/database_settings.json")
        default_settings = {
            'max_connections': 10,
            'connection_timeout': 30,
            'query_timeout': 30,
            'cache_size': 8192,
            'journal_mode': 'WAL',
            'synchronous': 'NORMAL',
            'temp_store': 'MEMORY',
            'enable_monitoring': True,
            'log_slow_queries': True,
            'slow_query_threshold': 1000  # milliseconds
        }
        
        try:
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    user_settings = json.load(f)
                    default_settings.update(user_settings)
        except Exception as e:
            logging.warning(f"خطأ في تحميل إعدادات قاعدة البيانات: {e}")
            
        return default_settings
        
    def _setup_logging(self):
        """إعداد نظام السجلات"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "database.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
    def _initialize_connection_pool(self):
        """تهيئة مجموعة الاتصالات"""
        self.connection_pool = ConnectionPool(
            str(self.db_path),
            max_connections=self.settings['max_connections'],
            timeout=self.settings['connection_timeout']
        )
        
    def _start_performance_monitoring(self):
        """بدء مراقبة الأداء"""
        if self.settings['enable_monitoring']:
            self.performance_monitor = threading.Thread(
                target=self._monitor_performance,
                daemon=True
            )
            self.performance_monitor.start()
            
    def _monitor_performance(self):
        """مراقبة أداء قاعدة البيانات"""
        while True:
            try:
                # جمع إحصائيات الأداء
                stats = self.connection_pool.get_stats()
                
                # تسجيل الإحصائيات إذا كان هناك نشاط
                if stats['current_active'] > 0:
                    logging.info(f"إحصائيات قاعدة البيانات: {stats}")
                    
                # تنظيف الاستعلامات القديمة
                self._cleanup_old_query_stats()
                
                time.sleep(60)  # مراقبة كل دقيقة
                
            except Exception as e:
                logging.error(f"خطأ في مراقبة الأداء: {e}")
                time.sleep(60)
                
    def _cleanup_old_query_stats(self):
        """تنظيف إحصائيات الاستعلامات القديمة"""
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        for query_type in list(self.query_stats.keys()):
            stats = self.query_stats[query_type]
            if 'last_executed' in stats:
                if datetime.fromisoformat(stats['last_executed']) < cutoff_time:
                    # إعادة تعيين الإحصائيات للاستعلامات القديمة
                    stats['count'] = 0
                    stats['total_time'] = 0
                    stats['avg_time'] = 0
                    
    @contextmanager
    def get_connection(self):
        """الحصول على اتصال مع إدارة تلقائية للموارد"""
        conn = self.connection_pool.get_connection()
        if not conn:
            raise Exception("فشل في الحصول على اتصال قاعدة البيانات")
            
        try:
            yield conn
        finally:
            self.connection_pool.return_connection(conn)
            
    def execute_query(self, query: str, params: tuple = None, fetch: str = 'none') -> Any:
        """تنفيذ استعلام مع مراقبة الأداء"""
        start_time = time.time()
        query_type = query.strip().split()[0].upper()
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                    
                # تنفيذ العملية المطلوبة
                if fetch == 'one':
                    result = cursor.fetchone()
                elif fetch == 'all':
                    result = cursor.fetchall()
                elif fetch == 'many':
                    result = cursor.fetchmany()
                else:
                    result = cursor.rowcount
                    
                # حفظ التغييرات للاستعلامات التي تعدل البيانات
                if query_type in ['INSERT', 'UPDATE', 'DELETE']:
                    conn.commit()
                    
                # تسجيل إحصائيات الاستعلام
                execution_time = (time.time() - start_time) * 1000  # milliseconds
                self._record_query_stats(query_type, execution_time)
                
                # تسجيل الاستعلامات البطيئة
                if (self.settings['log_slow_queries'] and 
                    execution_time > self.settings['slow_query_threshold']):
                    logging.warning(f"استعلام بطيء ({execution_time:.2f}ms): {query[:100]}...")
                    
                return result
                
        except Exception as e:
            logging.error(f"خطأ في تنفيذ الاستعلام: {e}")
            logging.error(f"الاستعلام: {query}")
            raise e
            
    def _record_query_stats(self, query_type: str, execution_time: float):
        """تسجيل إحصائيات الاستعلام"""
        if query_type not in self.query_stats:
            self.query_stats[query_type] = {
                'count': 0,
                'total_time': 0,
                'avg_time': 0,
                'max_time': 0,
                'last_executed': datetime.now().isoformat()
            }
            
        stats = self.query_stats[query_type]
        stats['count'] += 1
        stats['total_time'] += execution_time
        stats['avg_time'] = stats['total_time'] / stats['count']
        stats['max_time'] = max(stats['max_time'], execution_time)
        stats['last_executed'] = datetime.now().isoformat()
        
    def get_performance_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        return {
            'connection_pool': self.connection_pool.get_stats(),
            'query_stats': self.query_stats,
            'database_size': self.db_path.stat().st_size if self.db_path.exists() else 0,
            'settings': self.settings
        }
        
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # تنفيذ عمليات التحسين
                cursor.execute("PRAGMA optimize")
                cursor.execute("VACUUM")
                cursor.execute("ANALYZE")
                
                conn.commit()
                logging.info("تم تحسين قاعدة البيانات بنجاح")
                
        except Exception as e:
            logging.error(f"خطأ في تحسين قاعدة البيانات: {e}")
            raise e
            
    def backup_database(self, backup_path: str):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            with self.get_connection() as conn:
                backup_conn = sqlite3.connect(backup_path)
                conn.backup(backup_conn)
                backup_conn.close()
                
                logging.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
                
        except Exception as e:
            logging.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            raise e
            
    def close(self):
        """إغلاق مدير الاتصالات"""
        if self.connection_pool:
            self.connection_pool.close_all()
            logging.info("تم إغلاق جميع اتصالات قاعدة البيانات")

# إنشاء مثيل عام لمدير الاتصالات
db_manager = DatabaseConnectionManager()
