#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض سريع لمكتبة القوالب
Quick Template Library Demo
"""

import sys
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

def demo_basic_usage():
    """عرض الاستخدام الأساسي"""
    print("🚀 عرض سريع لمكتبة القوالب")
    print("=" * 50)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # استيراد المكتبة
    from template_library import Templates, نموذج_ادخال_اساسي
    
    # إعداد المكتبة
    Templates.setup_app(app)
    
    print("✅ تم إعداد مكتبة القوالب")
    
    # عرض القوالب المتاحة
    templates = Templates.list_all()
    print(f"📋 القوالب المتاحة: {len(templates)}")
    for template_name in templates:
        info = Templates.get_manager().registry.get_template(template_name)
        print(f"   • {info['display_name']} ({template_name})")
    
    print("\n🎯 اختر طريقة الاستخدام:")
    print("1. الطريقة المباشرة (نموذج_ادخال_اساسي)")
    print("2. الطريقة العامة (Templates.create)")
    print("3. الطريقة المختصرة (Templates.نموذج_ادخال_اساسي)")
    print("4. عرض جميع الطرق")
    
    try:
        choice = input("\nاختيارك (1-4): ").strip()
        
        if choice == "1":
            print("\n🔥 الطريقة المباشرة:")
            print("   from template_library import نموذج_ادخال_اساسي")
            print("   template = نموذج_ادخال_اساسي(fullscreen=True)")
            
            template = نموذج_ادخال_اساسي(fullscreen=True)
            if template:
                show_success_message(app, "الطريقة المباشرة", template)
                return app.exec()
        
        elif choice == "2":
            print("\n🔥 الطريقة العامة:")
            print("   from template_library import Templates")
            print("   template = Templates.create('نموذج_ادخال_اساسي', fullscreen=True)")
            
            template = Templates.create("نموذج_ادخال_اساسي", fullscreen=True)
            if template:
                show_success_message(app, "الطريقة العامة", template)
                return app.exec()
        
        elif choice == "3":
            print("\n🔥 الطريقة المختصرة:")
            print("   from template_library import Templates")
            print("   template = Templates.نموذج_ادخال_اساسي(fullscreen=True)")
            
            template = Templates.نموذج_ادخال_اساسي(fullscreen=True)
            if template:
                show_success_message(app, "الطريقة المختصرة", template)
                return app.exec()
        
        elif choice == "4":
            return demo_all_methods(app)
        
        else:
            print("❌ اختيار غير صحيح")
            return 1
    
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء العرض")
        return 0
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return 1

def demo_all_methods(app):
    """عرض جميع الطرق"""
    print("\n🎊 عرض جميع طرق الاستخدام:")
    print("=" * 50)
    
    from template_library import Templates, نموذج_ادخال_اساسي, create_template
    
    templates = []
    
    try:
        # الطريقة 1: المباشرة
        print("1️⃣ الطريقة المباشرة...")
        template1 = نموذج_ادخال_اساسي(fullscreen=False)
        if template1:
            template1.setWindowTitle("الطريقة المباشرة - نموذج إدخال أساسي")
            templates.append(template1)
            print("   ✅ نجحت")
        
        # الطريقة 2: العامة
        print("2️⃣ الطريقة العامة...")
        template2 = Templates.create("نموذج_ادخال_اساسي", fullscreen=False)
        if template2:
            template2.setWindowTitle("الطريقة العامة - نموذج إدخال أساسي")
            templates.append(template2)
            print("   ✅ نجحت")
        
        # الطريقة 3: المختصرة
        print("3️⃣ الطريقة المختصرة...")
        template3 = Templates.نموذج_ادخال_اساسي(fullscreen=False)
        if template3:
            template3.setWindowTitle("الطريقة المختصرة - نموذج إدخال أساسي")
            templates.append(template3)
            print("   ✅ نجحت")
        
        # الطريقة 4: الدالة المختصرة
        print("4️⃣ الدالة المختصرة...")
        template4 = create_template("نموذج_ادخال_اساسي", fullscreen=False)
        if template4:
            template4.setWindowTitle("الدالة المختصرة - نموذج إدخال أساسي")
            templates.append(template4)
            print("   ✅ نجحت")
        
        print(f"\n🎉 تم إنشاء {len(templates)} قالب بنجاح!")
        
        # ترتيب النوافذ
        arrange_windows(templates)
        
        # رسالة ترحيب
        QMessageBox.information(
            None, "مكتبة القوالب - عرض شامل",
            f"🎊 مرحباً بك في مكتبة القوالب!\n\n"
            f"تم إنشاء {len(templates)} قالب بطرق مختلفة:\n\n"
            f"1️⃣ الطريقة المباشرة\n"
            f"2️⃣ الطريقة العامة\n"
            f"3️⃣ الطريقة المختصرة\n"
            f"4️⃣ الدالة المختصرة\n\n"
            f"جميع الطرق تؤدي لنفس النتيجة!\n\n"
            f"الميزات المتاحة:\n"
            f"• ملء الشاشة (F11)\n"
            f"• العرض العادي (Escape)\n"
            f"• جميع وظائف النموذج\n\n"
            f"استمتع بالاستخدام! 🚀"
        )
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في العرض: {e}")
        return 1

def show_success_message(app, method_name, template):
    """إظهار رسالة نجاح"""
    variables = template.get_form_variables()
    
    QMessageBox.information(
        None, f"نجح الإنشاء - {method_name}",
        f"🎉 تم إنشاء القالب بنجاح!\n\n"
        f"الطريقة: {method_name}\n"
        f"العنوان: {template.windowTitle()}\n"
        f"وضع العرض: {'ملء الشاشة' if template.fullscreen_mode else 'عادي'}\n"
        f"المتغيرات المتاحة: {len(variables)}\n\n"
        f"الميزات المتاحة:\n"
        f"• F11: تبديل ملء الشاشة\n"
        f"• Escape: العرض العادي\n"
        f"• جميع وظائف النموذج\n\n"
        f"استمتع بالاستخدام! 🚀"
    )

def arrange_windows(templates):
    """ترتيب النوافذ"""
    if not templates:
        return
    
    # حساب المواضع
    screen_width = 1200
    screen_height = 800
    window_width = 300
    window_height = 400
    
    for i, template in enumerate(templates):
        # حساب الموضع
        x = (i % 2) * (window_width + 20) + 50
        y = (i // 2) * (window_height + 50) + 50
        
        # تعيين الحجم والموضع
        template.resize(window_width, window_height)
        template.move(x, y)
        template.show()

def interactive_demo():
    """عرض تفاعلي"""
    print("🎮 العرض التفاعلي لمكتبة القوالب")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    from template_library import Templates
    Templates.setup_app(app)
    
    while True:
        print("\n📋 اختر العملية:")
        print("1. إنشاء نموذج إدخال أساسي (ملء الشاشة)")
        print("2. إنشاء نموذج إدخال أساسي (عرض عادي)")
        print("3. عرض معلومات المكتبة")
        print("4. عرض جميع الطرق")
        print("5. خروج")
        
        try:
            choice = input("\nاختيارك (1-5): ").strip()
            
            if choice == "1":
                from template_library import نموذج_ادخال_اساسي
                template = نموذج_ادخال_اساسي(fullscreen=True)
                if template:
                    print("✅ تم إنشاء النموذج بوضع ملء الشاشة")
                    return app.exec()
            
            elif choice == "2":
                from template_library import نموذج_ادخال_اساسي
                template = نموذج_ادخال_اساسي(fullscreen=False)
                if template:
                    print("✅ تم إنشاء النموذج بالوضع العادي")
                    return app.exec()
            
            elif choice == "3":
                show_library_info()
            
            elif choice == "4":
                return demo_all_methods(app)
            
            elif choice == "5":
                print("👋 شكراً لاستخدام مكتبة القوالب!")
                break
            
            else:
                print("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى")
        
        except KeyboardInterrupt:
            print("\n👋 تم إنهاء البرنامج")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    return 0

def show_library_info():
    """عرض معلومات المكتبة"""
    print("\n📊 معلومات مكتبة القوالب:")
    print("   🏛️ مكتبة شاملة لإدارة القوالب")
    print("   📋 نموذج إدخال أساسي متاح")
    print("   🖥️ دعم ملء الشاشة والوضع العادي")
    print("   ⚡ 4 طرق مختلفة للاستخدام")
    print("   🔍 نظام بحث وتصنيف")
    print("   📱 واجهة سهلة الاستخدام")
    print("   🧪 اختبارات شاملة")
    print("   📖 توثيق مفصل")
    
    print("\n🚀 طرق الاستخدام:")
    print("   1. نموذج_ادخال_اساسي()")
    print("   2. Templates.create('نموذج_ادخال_اساسي')")
    print("   3. Templates.نموذج_ادخال_اساسي()")
    print("   4. create_template('نموذج_ادخال_اساسي')")

def main():
    """الدالة الرئيسية"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "--interactive":
            return interactive_demo()
        elif sys.argv[1] == "--all":
            app = QApplication(sys.argv)
            from template_library import Templates
            Templates.setup_app(app)
            return demo_all_methods(app)
    
    return demo_basic_usage()

if __name__ == "__main__":
    sys.exit(main())
