# -*- coding: utf-8 -*-
"""
نافذة إضافة بنك جديد
Add New Bank Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QTextEdit, QPushButton,
                               QGroupBox, QCheckBox, QSpinBox, QDoubleSpinBox,
                               QMessageBox, QFrame, QFileDialog, QProgressBar)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QPixmap, QIcon

import sqlite3
import os
from pathlib import Path
from datetime import datetime

class AddNewBankDialog(QDialog):
    """نافذة إضافة بنك جديد"""
    
    bank_added = Signal(int)  # إشارة عند إضافة بنك جديد
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إضافة بنك جديد - ProShipment")
        self.setMinimumSize(700, 920)
        self.resize(800, 920)
        self.setModal(True)

        # توسيط النافذة
        self.center_window()
        
        # متغيرات النافذة
        self.bank_logo_path = None
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.setup_validators()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2563eb, stop:1 #1d4ed8);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        title_layout = QHBoxLayout(title_frame)
        
        title_label = QLabel("🏦 إضافة بنك جديد")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                background: transparent;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addWidget(title_frame)
        
        # المعلومات الأساسية
        basic_group = QGroupBox("المعلومات الأساسية")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        basic_layout = QGridLayout(basic_group)
        basic_layout.setSpacing(10)
        
        # اسم البنك
        basic_layout.addWidget(QLabel("اسم البنك: *"), 0, 0)
        self.bank_name_input = QLineEdit()
        self.bank_name_input.setPlaceholderText("أدخل اسم البنك...")
        self.bank_name_input.setMinimumHeight(35)
        basic_layout.addWidget(self.bank_name_input, 0, 1, 1, 3)

        # اسم البنك بالإنجليزية
        basic_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 1, 0)
        self.bank_name_en_input = QLineEdit()
        self.bank_name_en_input.setPlaceholderText("Bank Name in English...")
        self.bank_name_en_input.setMinimumHeight(35)
        basic_layout.addWidget(self.bank_name_en_input, 1, 1, 1, 3)

        # رمز البنك
        basic_layout.addWidget(QLabel("رمز البنك: *"), 2, 0)
        self.bank_code_input = QLineEdit()
        self.bank_code_input.setPlaceholderText("مثال: SNB")
        self.bank_code_input.setMaxLength(10)
        self.bank_code_input.setMinimumHeight(35)
        basic_layout.addWidget(self.bank_code_input, 2, 1)

        # رمز SWIFT
        basic_layout.addWidget(QLabel("رمز SWIFT:"), 2, 2)
        self.swift_code_input = QLineEdit()
        self.swift_code_input.setPlaceholderText("مثال: RIBLSARI")
        self.swift_code_input.setMaxLength(11)
        self.swift_code_input.setMinimumHeight(35)
        basic_layout.addWidget(self.swift_code_input, 2, 3)
        
        layout.addWidget(basic_group)
        
        # معلومات الاتصال
        contact_group = QGroupBox("معلومات الاتصال")
        contact_group.setStyleSheet(basic_group.styleSheet())
        contact_layout = QGridLayout(contact_group)
        contact_layout.setSpacing(10)
        
        # العنوان
        contact_layout.addWidget(QLabel("العنوان:"), 0, 0)
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(100)
        self.address_input.setMinimumHeight(100)
        self.address_input.setPlaceholderText("أدخل عنوان المقر الرئيسي...")
        contact_layout.addWidget(self.address_input, 0, 1, 1, 3)

        # الهاتف
        contact_layout.addWidget(QLabel("الهاتف:"), 1, 0)
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("+966 11 123 4567")
        self.phone_input.setMinimumHeight(35)
        contact_layout.addWidget(self.phone_input, 1, 1)

        # الفاكس
        contact_layout.addWidget(QLabel("الفاكس:"), 1, 2)
        self.fax_input = QLineEdit()
        self.fax_input.setPlaceholderText("+966 11 123 4568")
        self.fax_input.setMinimumHeight(35)
        contact_layout.addWidget(self.fax_input, 1, 3)

        # البريد الإلكتروني
        contact_layout.addWidget(QLabel("البريد الإلكتروني:"), 2, 0)
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        self.email_input.setMinimumHeight(35)
        contact_layout.addWidget(self.email_input, 2, 1)

        # الموقع الإلكتروني
        contact_layout.addWidget(QLabel("الموقع الإلكتروني:"), 2, 2)
        self.website_input = QLineEdit()
        self.website_input.setPlaceholderText("https://www.bank.com")
        self.website_input.setMinimumHeight(35)
        contact_layout.addWidget(self.website_input, 2, 3)
        
        layout.addWidget(contact_group)
        
        # المعلومات المالية
        financial_group = QGroupBox("المعلومات المالية")
        financial_group.setStyleSheet(basic_group.styleSheet())
        financial_layout = QGridLayout(financial_group)
        financial_layout.setSpacing(10)
        
        # العملة الأساسية
        financial_layout.addWidget(QLabel("العملة الأساسية: *"), 0, 0)
        self.base_currency_combo = QComboBox()
        self.base_currency_combo.setMinimumHeight(35)
        self.load_currencies()
        financial_layout.addWidget(self.base_currency_combo, 0, 1, 1, 3)

        # رسوم التحويل
        financial_layout.addWidget(QLabel("رسوم التحويل:"), 1, 0)
        self.transfer_fee_input = QDoubleSpinBox()
        self.transfer_fee_input.setRange(0, 999999)
        self.transfer_fee_input.setDecimals(2)
        self.transfer_fee_input.setSuffix(" ريال")
        self.transfer_fee_input.setMinimumHeight(35)
        financial_layout.addWidget(self.transfer_fee_input, 1, 1, 1, 3)

        # الحد الأدنى للتحويل
        financial_layout.addWidget(QLabel("الحد الأدنى للتحويل:"), 2, 0)
        self.min_transfer_input = QDoubleSpinBox()
        self.min_transfer_input.setRange(0, 999999999)
        self.min_transfer_input.setDecimals(2)
        self.min_transfer_input.setSuffix(" ريال")
        self.min_transfer_input.setMinimumHeight(35)
        financial_layout.addWidget(self.min_transfer_input, 2, 1)

        # الحد الأقصى للتحويل
        financial_layout.addWidget(QLabel("الحد الأقصى للتحويل:"), 2, 2)
        self.max_transfer_input = QDoubleSpinBox()
        self.max_transfer_input.setRange(0, 999999999)
        self.max_transfer_input.setDecimals(2)
        self.max_transfer_input.setValue(1000000)
        self.max_transfer_input.setSuffix(" ريال")
        self.max_transfer_input.setMinimumHeight(35)
        financial_layout.addWidget(self.max_transfer_input, 2, 3)
        
        layout.addWidget(financial_group)
        
        # الشعار والملاحظات
        extra_group = QGroupBox("معلومات إضافية")
        extra_group.setStyleSheet(basic_group.styleSheet())
        extra_layout = QGridLayout(extra_group)
        extra_layout.setSpacing(10)
        
        # شعار البنك
        extra_layout.addWidget(QLabel("شعار البنك:"), 0, 0)
        logo_layout = QHBoxLayout()
        self.logo_path_input = QLineEdit()
        self.logo_path_input.setPlaceholderText("اختر ملف الشعار...")
        self.logo_path_input.setReadOnly(True)
        logo_layout.addWidget(self.logo_path_input)
        
        self.browse_logo_btn = QPushButton("تصفح...")
        self.browse_logo_btn.setMaximumWidth(80)
        logo_layout.addWidget(self.browse_logo_btn)
        
        extra_layout.addLayout(logo_layout, 0, 1, 1, 3)
        
        # الملاحظات
        extra_layout.addWidget(QLabel("ملاحظات:"), 1, 0)
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(100)
        self.notes_input.setMinimumHeight(100)
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        extra_layout.addWidget(self.notes_input, 1, 1, 1, 3)

        # حالة البنك
        self.is_active_checkbox = QCheckBox("البنك نشط")
        self.is_active_checkbox.setChecked(True)
        self.is_active_checkbox.setMinimumHeight(30)
        extra_layout.addWidget(self.is_active_checkbox, 2, 0, 1, 2)
        
        layout.addWidget(extra_group)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # أزرار الإجراءات
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.addStretch()
        
        self.save_btn = QPushButton("💾 حفظ البنك")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:pressed {
                background-color: #047857;
            }
        """)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #dc2626;
            }
            QPushButton:pressed {
                background-color: #b91c1c;
            }
        """)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_bank)
        self.cancel_btn.clicked.connect(self.reject)
        self.browse_logo_btn.clicked.connect(self.browse_logo)
        
        # التحقق من صحة البيانات عند التغيير
        self.bank_name_input.textChanged.connect(self.validate_form)
        self.bank_code_input.textChanged.connect(self.validate_form)
        
    def setup_validators(self):
        """إعداد مدققات الإدخال"""
        # تحويل رمز البنك للأحرف الكبيرة
        self.bank_code_input.textChanged.connect(
            lambda: self.bank_code_input.setText(self.bank_code_input.text().upper())
        )
        
        # تحويل رمز SWIFT للأحرف الكبيرة
        self.swift_code_input.textChanged.connect(
            lambda: self.swift_code_input.setText(self.swift_code_input.text().upper())
        )
        
    def load_currencies(self):
        """تحميل العملات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # التأكد من وجود جدول العملات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS currencies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    symbol TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT,
                    updated_at TEXT
                )
            """)

            # إضافة عملات افتراضية إذا لم تكن موجودة
            default_currencies = [
                ('SAR', 'الريال السعودي', 'Saudi Riyal', 'ر.س'),
                ('USD', 'الدولار الأمريكي', 'US Dollar', '$'),
                ('EUR', 'اليورو', 'Euro', '€'),
                ('AED', 'الدرهم الإماراتي', 'UAE Dirham', 'د.إ'),
                ('KWD', 'الدينار الكويتي', 'Kuwaiti Dinar', 'د.ك'),
                ('QAR', 'الريال القطري', 'Qatari Riyal', 'ر.ق'),
                ('BHD', 'الدينار البحريني', 'Bahraini Dinar', 'د.ب'),
                ('OMR', 'الريال العماني', 'Omani Rial', 'ر.ع')
            ]

            for code, name, name_en, symbol in default_currencies:
                cursor.execute("""
                    INSERT OR IGNORE INTO currencies (code, name, name_en, symbol, is_active, created_at)
                    VALUES (?, ?, ?, ?, 1, ?)
                """, (code, name, name_en, symbol, datetime.now().isoformat()))

            conn.commit()

            cursor.execute("SELECT id, code, name FROM currencies WHERE is_active = 1 ORDER BY name")
            currencies = cursor.fetchall()

            self.base_currency_combo.addItem("اختر العملة...", None)
            for currency in currencies:
                self.base_currency_combo.addItem(f"{currency[1]} - {currency[2]}", currency[0])

            # تعيين الريال السعودي كافتراضي
            for i in range(self.base_currency_combo.count()):
                if "SAR" in self.base_currency_combo.itemText(i):
                    self.base_currency_combo.setCurrentIndex(i)
                    break

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل العملات: {e}")
            # إضافة عملات افتراضية
            self.base_currency_combo.addItem("اختر العملة...", None)
            self.base_currency_combo.addItem("SAR - الريال السعودي", 1)
            self.base_currency_combo.addItem("USD - الدولار الأمريكي", 2)
            self.base_currency_combo.addItem("EUR - اليورو", 3)
            self.base_currency_combo.addItem("AED - الدرهم الإماراتي", 4)
            self.base_currency_combo.setCurrentIndex(1)  # SAR كافتراضي
    
    def browse_logo(self):
        """تصفح ملف الشعار"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر شعار البنك",
            "",
            "Image Files (*.png *.jpg *.jpeg *.gif *.bmp)"
        )
        
        if file_path:
            self.bank_logo_path = file_path
            self.logo_path_input.setText(os.path.basename(file_path))
    
    def validate_form(self):
        """التحقق من صحة النموذج"""
        is_valid = (
            self.bank_name_input.text().strip() and
            self.bank_code_input.text().strip()
        )

        self.save_btn.setEnabled(is_valid)
        return is_valid

    def validate_required_fields(self):
        """التحقق من الحقول المطلوبة مع رسائل تفصيلية"""
        missing_fields = []

        if not self.bank_name_input.text().strip():
            missing_fields.append("اسم البنك")

        if not self.bank_code_input.text().strip():
            missing_fields.append("رمز البنك")

        return missing_fields

    def ensure_database_structure(self):
        """التأكد من وجود هيكل قاعدة البيانات الصحيح"""
        try:
            db_path = Path("data/proshipment.db")
            db_path.parent.mkdir(exist_ok=True)

            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء جدول العملات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS currencies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    symbol TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT
                )
            """)

            # إدراج العملات الافتراضية
            currencies = [
                ('SAR', 'الريال السعودي', 'Saudi Riyal', 'ر.س'),
                ('USD', 'الدولار الأمريكي', 'US Dollar', '$'),
                ('EUR', 'اليورو', 'Euro', '€'),
                ('AED', 'الدرهم الإماراتي', 'UAE Dirham', 'د.إ')
            ]

            for code, name, name_en, symbol in currencies:
                cursor.execute("""
                    INSERT OR IGNORE INTO currencies (code, name, name_en, symbol, is_active, created_at)
                    VALUES (?, ?, ?, ?, 1, ?)
                """, (code, name, name_en, symbol, datetime.now().isoformat()))

            # إنشاء جدول البنوك مع جميع الأعمدة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS banks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    code TEXT UNIQUE NOT NULL,
                    swift_code TEXT,
                    address TEXT,
                    phone TEXT,
                    fax TEXT,
                    email TEXT,
                    website TEXT,
                    base_currency_id INTEGER,
                    transfer_fee REAL DEFAULT 0,
                    min_transfer_amount REAL DEFAULT 0,
                    max_transfer_amount REAL DEFAULT 1000000,
                    logo_path TEXT,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT
                )
            """)

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {e}")
            return False

    def save_bank(self):
        """حفظ البنك الجديد"""
        # التحقق من الحقول المطلوبة
        missing_fields = self.validate_required_fields()
        if missing_fields:
            fields_text = "، ".join(missing_fields)
            QMessageBox.warning(
                self,
                "حقول مطلوبة",
                f"يرجى إدخال القيم التالية:\n• {fields_text}"
            )
            return

        # التأكد من هيكل قاعدة البيانات
        if not self.ensure_database_structure():
            QMessageBox.critical(self, "خطأ", "فشل في إنشاء قاعدة البيانات")
            return
        
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.save_btn.setEnabled(False)
            
            # جمع البيانات
            # جمع البيانات مع معالجة الحقول الاختيارية
            base_currency_id = self.base_currency_combo.currentData()
            if base_currency_id is None and self.base_currency_combo.currentIndex() > 0:
                # إذا كان هناك اختيار ولكن لا يوجد بيانات، استخدم الفهرس
                base_currency_id = self.base_currency_combo.currentIndex()

            bank_data = {
                'name': self.bank_name_input.text().strip(),
                'name_en': self.bank_name_en_input.text().strip() or None,
                'code': self.bank_code_input.text().strip(),
                'swift_code': self.swift_code_input.text().strip() or None,
                'address': self.address_input.toPlainText().strip() or None,
                'phone': self.phone_input.text().strip() or None,
                'fax': self.fax_input.text().strip() or None,
                'email': self.email_input.text().strip() or None,
                'website': self.website_input.text().strip() or None,
                'base_currency_id': base_currency_id,
                'transfer_fee': self.transfer_fee_input.value(),
                'min_transfer_amount': self.min_transfer_input.value(),
                'max_transfer_amount': self.max_transfer_input.value(),
                'logo_path': getattr(self, 'bank_logo_path', None),
                'notes': self.notes_input.toPlainText().strip() or None,
                'is_active': self.is_active_checkbox.isChecked(),
                'created_at': datetime.now().isoformat()
            }
            
            # حفظ في قاعدة البيانات
            bank_id = self.save_to_database(bank_data)
            
            if bank_id:
                QMessageBox.information(self, "نجح الحفظ", 
                                      f"تم حفظ البنك '{bank_data['name']}' بنجاح")
                
                # إرسال إشارة
                self.bank_added.emit(bank_id)
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ البنك")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البنك:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.save_btn.setEnabled(True)
    
    def save_to_database(self, data):
        """حفظ البيانات في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # التأكد من وجود جدول البنوك
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS banks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    code TEXT UNIQUE NOT NULL,
                    swift_code TEXT,
                    type TEXT NOT NULL,
                    country TEXT NOT NULL,
                    address TEXT,
                    phone TEXT,
                    fax TEXT,
                    email TEXT,
                    website TEXT,
                    base_currency_id INTEGER,
                    transfer_fee REAL DEFAULT 0,
                    min_transfer_amount REAL DEFAULT 0,
                    max_transfer_amount REAL DEFAULT 1000000,
                    logo_path TEXT,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT,
                    updated_at TEXT
                )
            """)

            # إضافة الأعمدة المفقودة إذا لم تكن موجودة
            try:
                cursor.execute("ALTER TABLE banks ADD COLUMN swift_code TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE banks ADD COLUMN base_currency_id INTEGER")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE banks ADD COLUMN transfer_fee REAL DEFAULT 0")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE banks ADD COLUMN min_transfer_amount REAL DEFAULT 0")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE banks ADD COLUMN max_transfer_amount REAL DEFAULT 1000000")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            try:
                cursor.execute("ALTER TABLE banks ADD COLUMN logo_path TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل
            
            # إدراج البنك الجديد
            cursor.execute("""
                INSERT INTO banks (
                    name, name_en, code, swift_code, address,
                    phone, fax, email, website, base_currency_id, transfer_fee,
                    min_transfer_amount, max_transfer_amount, logo_path, notes,
                    is_active, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['name'], data['name_en'], data['code'], data['swift_code'],
                data['address'], data['phone'], data['fax'], data['email'],
                data['website'], data['base_currency_id'], data['transfer_fee'],
                data['min_transfer_amount'], data['max_transfer_amount'],
                data['logo_path'], data['notes'], data['is_active'], data['created_at']
            ))
            
            bank_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return bank_id
            
        except sqlite3.IntegrityError as e:
            if "UNIQUE constraint failed: banks.code" in str(e):
                QMessageBox.warning(self, "خطأ", "رمز البنك موجود بالفعل. يرجى اختيار رمز آخر.")
            else:
                QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {e}")
            return None
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات: {e}")
            return None
