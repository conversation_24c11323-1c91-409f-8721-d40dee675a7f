#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل الواجهة الرئيسية المطورة - SHIPMENT ERP Enhanced Interface
واجهة رئيسية متطورة بنفس تخطيط وتصميم main_window_prototype.py مع تحسينات متقدمة
"""

import sys

def main():
    """الدالة الرئيسية"""
    print("🚀 تشغيل نظام إدارة الشحنات المتقدم والمطور")
    print("=" * 60)
    print("✨ الواجهة الرئيسية المحسنة")
    print("   🎨 تصميم متطور مع تأثيرات بصرية")
    print("   🏛️ مكتبة قوالب متكاملة ومتطورة")
    print("   📊 تحليلات وتقارير في الوقت الفعلي")
    print("   🤖 ذكاء اصطناعي مدمج")
    print("   🌐 تكامل سحابي متطور")
    print("   📱 واجهة متجاوبة وقابلة للتخصيص")
    print("=" * 60)
    
    try:
        # استيراد الواجهة الرئيسية المطورة
        from main_interface_enhanced import main as enhanced_main
        
        print("✅ تم تحميل الواجهة الرئيسية المطورة")
        
        # تشغيل الواجهة المطورة
        return enhanced_main()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الواجهة المطورة: {e}")
        print("🔄 محاولة تشغيل الواجهة الأساسية كبديل...")
        
        try:
            # تشغيل الواجهة الأساسية كبديل
            from main_window_prototype import main as prototype_main
            
            print("✅ تم تحميل الواجهة الأساسية")
            return prototype_main()
            
        except ImportError as e2:
            print(f"❌ خطأ في استيراد الواجهة الأساسية: {e2}")
            print("🔄 محاولة تشغيل النموذج الأساسي كبديل أخير...")
            
            try:
                # تشغيل النموذج الأساسي كبديل أخير
                from template_library import نموذج_ادخال_اساسي, Templates
                from PySide6.QtWidgets import QApplication, QMessageBox
                from PySide6.QtCore import Qt
                from PySide6.QtGui import QFont
                
                app = QApplication(sys.argv)
                
                # إعداد التطبيق
                font = QFont("Arial", 10)
                app.setFont(font)
                app.setLayoutDirection(Qt.RightToLeft)
                Templates.setup_app(app)
                
                # إنشاء النموذج
                form = نموذج_ادخال_اساسي(fullscreen=False)
                form.show()
                
                # رسالة تنبيه
                QMessageBox.information(
                    form, "تشغيل النموذج الأساسي",
                    "⚠️ تم تشغيل النموذج الأساسي كبديل للواجهة الرئيسية.\n\n"
                    "للحصول على التجربة الكاملة:\n"
                    "• تأكد من وجود ملف main_interface_enhanced.py\n"
                    "• تأكد من وجود ملف main_window_prototype.py\n"
                    "• تأكد من تثبيت جميع المتطلبات\n\n"
                    "يمكنك الآن استخدام النموذج الأساسي."
                )
                
                print("✅ تم تشغيل النموذج الأساسي كبديل")
                return app.exec()
                
            except Exception as e3:
                print(f"❌ خطأ في تشغيل النموذج الأساسي: {e3}")
                print("💡 تأكد من تثبيت PySide6 وجميع المتطلبات")
                return 1
    
    except Exception as e:
        print(f"❌ خطأ عام في التشغيل: {e}")
        print("💡 للمساعدة:")
        print("   • تأكد من تثبيت Python 3.8+")
        print("   • تأكد من تثبيت PySide6")
        print("   • تأكد من وجود جميع الملفات المطلوبة")
        print("   • شغل: pip install PySide6")
        return 1

def show_usage():
    """عرض تعليمات الاستخدام"""
    print("\n📖 تعليمات الاستخدام:")
    print("=" * 40)
    
    print("\n🚀 طرق التشغيل:")
    print("   python run_enhanced_interface.py     # الواجهة المطورة")
    print("   python main_interface_enhanced.py    # مباشرة")
    print("   python main_window_prototype.py      # الواجهة الأساسية")
    print("   python run_main_interface.py         # البديل")
    
    print("\n⚙️ الميزات المتاحة:")
    print("   🎨 واجهة محسنة مع تأثيرات بصرية")
    print("   🏛️ مكتبة قوالب متكاملة ومتطورة")
    print("   📊 تحليلات وتقارير في الوقت الفعلي")
    print("   🤖 ذكاء اصطناعي مدمج")
    print("   🌐 تكامل سحابي متطور")
    print("   📱 واجهة متجاوبة وقابلة للتخصيص")
    
    print("\n🎯 الاختصارات:")
    print("   Ctrl+N: نموذج جديد")
    print("   Ctrl+O: فتح")
    print("   Ctrl+S: حفظ")
    print("   Ctrl+Q: خروج")
    print("   F11: ملء الشاشة (في النماذج)")
    
    print("\n📋 الوظائف الرئيسية:")
    print("   • إنشاء شحنة جديدة")
    print("   • مكتبة القوالب الكاملة")
    print("   • أمثلة القوالب التفاعلية")
    print("   • البحث المتقدم")
    print("   • تتبع الشحنات")
    print("   • إعدادات النظام")
    print("   • المساعدة والدعم")

def check_requirements():
    """فحص المتطلبات"""
    print("\n🔍 فحص المتطلبات:")
    print("-" * 30)
    
    # فحص Python
    import sys
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python {python_version.major}.{python_version.minor}.{python_version.micro} (يتطلب 3.8+)")
        return False
    
    # فحص PySide6
    try:
        import PySide6
        print("✅ PySide6 متوفر")
    except ImportError:
        print("❌ PySide6 غير متوفر - شغل: pip install PySide6")
        return False
    
    # فحص الملفات
    import os
    required_files = [
        "main_interface_enhanced.py",
        "main_window_prototype.py", 
        "template_library.py"
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"⚠️ {file} (اختياري)")
    
    return True

if __name__ == "__main__":
    print("🔧 فحص النظام...")
    
    if "--help" in sys.argv or "-h" in sys.argv:
        show_usage()
        sys.exit(0)
    
    if "--check" in sys.argv:
        if check_requirements():
            print("\n✅ جميع المتطلبات متوفرة!")
        else:
            print("\n❌ بعض المتطلبات مفقودة!")
        sys.exit(0)
    
    # فحص سريع
    if not check_requirements():
        print("\n⚠️ تحذير: بعض المتطلبات مفقودة، قد يفشل التشغيل")
        input("اضغط Enter للمتابعة أو Ctrl+C للإلغاء...")
    
    # تشغيل التطبيق
    exit_code = main()
    
    if exit_code == 0:
        print("\n✅ تم إغلاق التطبيق بنجاح!")
    else:
        print(f"\n❌ خطأ في التشغيل (كود الخطأ: {exit_code})")
        show_usage()
    
    sys.exit(exit_code)
