#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل قالب النموذج
Form Template Runner
"""

import sys
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# استيراد القالب
from form_template import FormTemplate

class FormTemplateApp:
    """تطبيق قالب النموذج"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
    
    def setup_application(self):
        """إعداد التطبيق"""
        self.app = QApplication(sys.argv)
        
        # إعداد الخط الافتراضي
        font = QFont("Arial", 10)
        self.app.setFont(font)
        
        # إعداد اتجاه النص (من اليمين لليسار للعربية)
        self.app.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد معلومات التطبيق
        self.app.setApplicationName("SHIPMENT ERP - Form Template")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("SHIPMENT Solutions")
        
        return self.app
    
    def create_main_window(self):
        """إنشاء النافذة الرئيسية"""
        self.main_window = FormTemplate()
        
        # إعداد النافذة
        self.main_window.setWindowTitle("قالب النموذج - SHIPMENT ERP")
        self.main_window.show()
        
        return self.main_window
    
    def show_welcome_message(self):
        """إظهار رسالة ترحيب"""
        welcome_msg = QMessageBox()
        welcome_msg.setWindowTitle("مرحباً")
        welcome_msg.setText("مرحباً بك في قالب النموذج")
        welcome_msg.setInformativeText(
            "هذا القالب مطابق للتصميم المرفق ويحتوي على:\n\n"
            "• نموذج إدخال البيانات الأساسية\n"
            "• لوحة الخيارات والإعدادات\n"
            "• شريط الأدوات المتكامل\n"
            "• شريط الحالة التفاعلي\n"
            "• جدول المعلومات السفلي\n\n"
            "يمكنك استخدام هذا القالب كأساس لتطوير النماذج في التطبيق."
        )
        welcome_msg.setIcon(QMessageBox.Information)
        welcome_msg.setStandardButtons(QMessageBox.Ok)
        welcome_msg.exec()
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            # إعداد التطبيق
            app = self.setup_application()
            
            # إنشاء النافذة الرئيسية
            main_window = self.create_main_window()
            
            # إظهار رسالة الترحيب
            self.show_welcome_message()
            
            # تشغيل التطبيق
            return app.exec()
            
        except Exception as e:
            print(f"خطأ في تشغيل التطبيق: {e}")
            return 1

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🚀 تشغيل قالب النموذج - SHIPMENT ERP")
    print("=" * 60)
    print("📋 القالب مطابق للتصميم المرفق في الصورة")
    print("🎯 يحتوي على جميع العناصر والمتغيرات المطلوبة")
    print("⚡ جاهز للاستخدام في التطبيق")
    print("=" * 60)
    
    # إنشاء وتشغيل التطبيق
    app = FormTemplateApp()
    exit_code = app.run()
    
    print("\n" + "=" * 60)
    print("👋 شكراً لاستخدام قالب النموذج")
    print("=" * 60)
    
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
