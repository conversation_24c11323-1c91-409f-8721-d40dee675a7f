#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الوضع العادي للقالب
Test Normal Mode for Template
"""

import sys
from PySide6.QtWidgets import QApplication

def test_normal_mode():
    """اختبار الوضع العادي"""
    print("🧪 اختبار الوضع العادي للقالب")
    print("=" * 40)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # استيراد المكتبة
    from template_library import نموذج_ادخال_اساسي, Templates
    
    # إعداد المكتبة
    Templates.setup_app(app)
    
    # اختبار 1: الطريقة المباشرة (افتراضي)
    print("1️⃣ اختبار الطريقة المباشرة (افتراضي):")
    template1 = نموذج_ادخال_اساسي()
    print(f"   وضع ملء الشاشة: {template1.fullscreen_mode}")
    print(f"   النتيجة: {'✅ عادي' if not template1.fullscreen_mode else '❌ ملء الشاشة'}")
    
    # اختبار 2: الطريقة المباشرة (صريح)
    print("\n2️⃣ اختبار الطريقة المباشرة (صريح):")
    template2 = نموذج_ادخال_اساسي(fullscreen=False)
    print(f"   وضع ملء الشاشة: {template2.fullscreen_mode}")
    print(f"   النتيجة: {'✅ عادي' if not template2.fullscreen_mode else '❌ ملء الشاشة'}")
    
    # اختبار 3: الطريقة العامة (افتراضي)
    print("\n3️⃣ اختبار الطريقة العامة (افتراضي):")
    template3 = Templates.create("نموذج_ادخال_اساسي")
    print(f"   وضع ملء الشاشة: {template3.fullscreen_mode}")
    print(f"   النتيجة: {'✅ عادي' if not template3.fullscreen_mode else '❌ ملء الشاشة'}")
    
    # اختبار 4: الطريقة المختصرة (افتراضي)
    print("\n4️⃣ اختبار الطريقة المختصرة (افتراضي):")
    template4 = Templates.نموذج_ادخال_اساسي()
    print(f"   وضع ملء الشاشة: {template4.fullscreen_mode}")
    print(f"   النتيجة: {'✅ عادي' if not template4.fullscreen_mode else '❌ ملء الشاشة'}")
    
    # اختبار 5: ملء الشاشة صريح
    print("\n5️⃣ اختبار ملء الشاشة (صريح):")
    template5 = نموذج_ادخال_اساسي(fullscreen=True)
    print(f"   وضع ملء الشاشة: {template5.fullscreen_mode}")
    print(f"   النتيجة: {'✅ ملء الشاشة' if template5.fullscreen_mode else '❌ عادي'}")
    
    # إغلاق القوالب
    templates = [template1, template2, template3, template4, template5]
    for template in templates:
        template.close()
    
    print("\n" + "=" * 40)
    print("📊 ملخص النتائج:")
    
    normal_count = sum(1 for t in templates[:4] if not t.fullscreen_mode)
    fullscreen_count = sum(1 for t in templates[4:] if t.fullscreen_mode)
    
    print(f"   ✅ الوضع العادي (افتراضي): {normal_count}/4")
    print(f"   ✅ ملء الشاشة (صريح): {fullscreen_count}/1")
    
    if normal_count == 4 and fullscreen_count == 1:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ الوضع العادي هو الافتراضي الآن")
        return True
    else:
        print("\n❌ بعض الاختبارات فشلت!")
        return False

if __name__ == "__main__":
    success = test_normal_mode()
    sys.exit(0 if success else 1)
