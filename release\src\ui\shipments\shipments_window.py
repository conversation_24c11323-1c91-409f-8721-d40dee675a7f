# -*- coding: utf-8 -*-
"""
نافذة إدارة الشحنات
Shipments Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QMessageBox, QHeaderView, QAbstractItemView, QComboBox,
                               QGroupBox, QFormLayout, QDateEdit, QToolBar, QStatusBar, QMenu, QDialog)
from PySide6.QtCore import Qt, QDate, Signal, QTimer
from PySide6.QtGui import QAction, QIcon

from ...database.database_manager import DatabaseManager
from ...database.models import Shipment, Supplier, Container, ShipmentItem
from ...utils.formatters import format_date, format_datetime, format_number, format_currency
from .new_shipment_window import NewShipmentWindow
from .shipment_tracking_window import ShipmentTrackingWindow

class ShipmentsWindow(QMainWindow):
    """نافذة إدارة الشحنات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self._is_refreshing = False  # متغير لتجنب التحديث المتداخل

        # إعداد timer للبحث الفوري
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_instant_search)

        self.setup_ui()
        self.setup_toolbar()
        self.setup_connections()
        self.load_shipments()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة الشحنات")
        self.resize(1200, 800)

        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # منطقة البحث والفلترة
        search_group = QGroupBox("البحث والفلترة")
        search_layout = QFormLayout(search_group)
        
        # البحث بالنص
        search_text_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("بحث فوري في جميع الأعمدة... (اكتب للبحث مباشرة)")
        self.search_button = QPushButton("بحث")
        self.clear_search_button = QPushButton("مسح")
        
        search_text_layout.addWidget(self.search_edit)
        search_text_layout.addWidget(self.search_button)
        search_text_layout.addWidget(self.clear_search_button)
        search_layout.addRow("البحث:", search_text_layout)
        
        # فلترة بحالة الشحنة
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItem("جميع الحالات", "")
        self.status_filter_combo.addItems([
            "تحت الطلب", "مؤكدة", "تم الشحن", "في الطريق", 
            "وصلت الميناء", "في الجمارك", "تم التسليم", "ملغية", "متاخرة"
        ])
        search_layout.addRow("حالة الشحنة:", self.status_filter_combo)
        
        # فلترة بحالة الإفراج
        self.clearance_filter_combo = QComboBox()
        self.clearance_filter_combo.addItem("جميع حالات الإفراج", "")
        self.clearance_filter_combo.addItems(["بدون الافراج", "مع الافراج"])
        search_layout.addRow("حالة الإفراج:", self.clearance_filter_combo)
        
        main_layout.addWidget(search_group)
        
        # جدول الشحنات
        self.shipments_table = QTableWidget()
        self.shipments_table.setColumnCount(22)  # إضافة عمود ميناء الوصول
        self.shipments_table.setHorizontalHeaderLabels([
            "رقم الشحنة", "التاريخ", "المورد", "تفاصيل الأصناف", "الكمية", "عدد الحاويات", "رقم الحاوية",
            "حالة الشحنة", "حالة الإفراج", "بوليصة الشحن", "رقم التتبع", "رقم DHL", "شركة الشحن", "طريقة الشحن",
            "ميناء التحميل", "ميناء التفريغ", "ميناء الوصول", "الوجهة النهائية", "اسم السفينة", "رقم الرحلة",
            "تاريخ المغادرة", "تاريخ الوصول المتوقع"
        ])
        
        # إعداد الجدول
        header = self.shipments_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الشحنة
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # المورد - توسيع لإظهار الاسم كاملاً
        header.resizeSection(2, 200)  # توسيع عمود المورد إلى 200 بكسل
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # تفاصيل الأصناف (قابل للتمدد)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الكمية
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # عدد الحاويات
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # رقم الحاوية
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # حالة الشحنة
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # حالة الإفراج
        header.setSectionResizeMode(9, QHeaderView.ResizeToContents)  # بوليصة الشحن
        header.setSectionResizeMode(10, QHeaderView.ResizeToContents)  # رقم التتبع
        header.setSectionResizeMode(11, QHeaderView.ResizeToContents)  # رقم DHL
        header.setSectionResizeMode(12, QHeaderView.ResizeToContents)  # شركة الشحن
        header.setSectionResizeMode(13, QHeaderView.ResizeToContents)  # طريقة الشحن
        header.setSectionResizeMode(14, QHeaderView.ResizeToContents)  # ميناء التحميل
        header.setSectionResizeMode(15, QHeaderView.ResizeToContents)  # ميناء التفريغ
        header.setSectionResizeMode(16, QHeaderView.ResizeToContents)  # ميناء الوصول
        header.setSectionResizeMode(17, QHeaderView.ResizeToContents)  # الوجهة النهائية
        header.setSectionResizeMode(18, QHeaderView.ResizeToContents)  # اسم السفينة
        header.setSectionResizeMode(19, QHeaderView.ResizeToContents)  # رقم الرحلة
        header.setSectionResizeMode(20, QHeaderView.ResizeToContents)  # تاريخ المغادرة
        header.setSectionResizeMode(21, QHeaderView.ResizeToContents)  # تاريخ الوصول المتوقع
        
        self.shipments_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.shipments_table.setAlternatingRowColors(True)
        # جعل الجدول للقراءة فقط (غير قابل للتعديل)
        self.shipments_table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # إعداد قائمة الزر الأيمن (Context Menu)
        self.shipments_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.shipments_table.customContextMenuRequested.connect(self.show_context_menu)

        main_layout.addWidget(self.shipments_table)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = QToolBar("أدوات الشحنات")
        self.addToolBar(toolbar)
        
        # شحنة جديدة
        new_action = QAction("شحنة جديدة", self)
        new_action.setStatusTip("إنشاء شحنة جديدة")
        new_action.triggered.connect(self.new_shipment)
        toolbar.addAction(new_action)

        toolbar.addSeparator()

        # تتبع الشحنات المتقدم
        advanced_track_action = QAction("تتبع الشحنات المتقدم", self)
        advanced_track_action.setStatusTip("فتح نافذة تتبع الشحنات المتقدمة")
        advanced_track_action.triggered.connect(self.open_advanced_tracking_window)
        toolbar.addAction(advanced_track_action)

        toolbar.addSeparator()

        # تحديث
        refresh_action = QAction("تحديث", self)
        refresh_action.setStatusTip("تحديث قائمة الشحنات")
        refresh_action.triggered.connect(self.load_shipments)
        toolbar.addAction(refresh_action)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.search_button.clicked.connect(self.search_shipments)
        self.clear_search_button.clicked.connect(self.clear_search)
        self.search_edit.returnPressed.connect(self.search_shipments)

        # البحث الفوري السريع - البحث أثناء الكتابة
        self.search_edit.textChanged.connect(self.instant_search)

        # فلترة عند تغيير الكومبو بوكس
        self.status_filter_combo.currentTextChanged.connect(self.filter_shipments)
        self.clearance_filter_combo.currentTextChanged.connect(self.filter_shipments)
        
        # النقر المزدوج لفتح الشحنة
        self.shipments_table.itemDoubleClicked.connect(self.edit_shipment)
        
    def load_shipments(self):
        """تحميل الشحنات"""
        session = None
        try:
            session = self.db_manager.get_session()

            # استعلام محسن مع eager loading
            from sqlalchemy.orm import joinedload
            shipments = session.query(Shipment).options(
                joinedload(Shipment.supplier),
                joinedload(Shipment.containers),
                joinedload(Shipment.items)
            ).filter(
                Shipment.is_active == True
            ).order_by(Shipment.created_at.desc()).all()

            self.populate_table(shipments)
            self.status_bar.showMessage(f"تم تحميل {len(shipments)} شحنة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الشحنات: {str(e)}")
            print(f"خطأ في تحميل الشحنات: {e}")
            import traceback
            traceback.print_exc()
        finally:
            if session:
                session.close()
            
    def populate_table(self, shipments):
        """ملء الجدول بالشحنات"""
        self.shipments_table.setRowCount(len(shipments))

        for row, shipment in enumerate(shipments):
            # رقم الشحنة
            shipment_number_item = QTableWidgetItem(shipment.shipment_number or "")
            shipment_number_item.setData(Qt.UserRole, shipment.id)  # حفظ معرف الشحنة
            self.shipments_table.setItem(row, 0, shipment_number_item)

            # التاريخ (تاريخ الشحنة)
            date_text = format_date(shipment.shipment_date, 'short') if shipment.shipment_date else ""
            date_item = QTableWidgetItem(date_text)
            self.shipments_table.setItem(row, 1, date_item)

            # المورد
            supplier_name = shipment.supplier.name if shipment.supplier else ""
            supplier_item = QTableWidgetItem(supplier_name)
            self.shipments_table.setItem(row, 2, supplier_item)

            # تفاصيل الأصناف (أسماء الأصناف)
            item_names = []
            if hasattr(shipment, 'items') and shipment.items:
                for shipment_item in shipment.items:
                    if shipment_item.item and shipment_item.item.name:
                        item_names.append(shipment_item.item.name)
            items_text = ", ".join(item_names[:3])  # أول 3 أصناف فقط لتوفير المساحة
            if len(item_names) > 3:
                items_text += f" (+{len(item_names) - 3} أخرى)"
            items_item = QTableWidgetItem(items_text)
            items_item.setToolTip(", ".join(item_names))  # عرض جميع الأصناف في tooltip
            self.shipments_table.setItem(row, 3, items_item)

            # الكمية (إجمالي الكميات من الأصناف)
            total_quantity = 0
            if hasattr(shipment, 'items') and shipment.items:
                total_quantity = sum(item.quantity for item in shipment.items)
            quantity_item = QTableWidgetItem(f"{total_quantity:.2f}")
            self.shipments_table.setItem(row, 4, quantity_item)

            # عدد الحاويات
            containers_count = len(shipment.containers) if hasattr(shipment, 'containers') and shipment.containers else 0
            containers_count_item = QTableWidgetItem(str(containers_count))
            self.shipments_table.setItem(row, 5, containers_count_item)

            # رقم الحاوية (من جدول الحاويات)
            container_numbers = []
            if hasattr(shipment, 'containers') and shipment.containers:
                container_numbers = [container.container_number for container in shipment.containers if container.container_number]
            container_text = ", ".join(container_numbers) if container_numbers else ""
            container_item = QTableWidgetItem(container_text)
            self.shipments_table.setItem(row, 6, container_item)

            # حالة الشحنة
            status_item = QTableWidgetItem(shipment.shipment_status or "")
            self.shipments_table.setItem(row, 7, status_item)

            # حالة الإفراج
            clearance_item = QTableWidgetItem(shipment.clearance_status or "")
            self.shipments_table.setItem(row, 8, clearance_item)

            # بوليصة الشحن
            bl_item = QTableWidgetItem(shipment.bill_of_lading or "")
            self.shipments_table.setItem(row, 9, bl_item)

            # رقم التتبع
            tracking_item = QTableWidgetItem(shipment.tracking_number or "")
            self.shipments_table.setItem(row, 10, tracking_item)

            # رقم DHL
            dhl_item = QTableWidgetItem(shipment.dhl_number or "")
            self.shipments_table.setItem(row, 11, dhl_item)

            # شركة الشحن
            shipping_company_item = QTableWidgetItem(shipment.shipping_company or "")
            self.shipments_table.setItem(row, 12, shipping_company_item)

            # طريقة الشحن
            shipping_method_item = QTableWidgetItem(shipment.shipping_method or "")
            self.shipments_table.setItem(row, 13, shipping_method_item)

            # ميناء التحميل
            loading_port_item = QTableWidgetItem(shipment.port_of_loading or "")
            self.shipments_table.setItem(row, 14, loading_port_item)

            # ميناء التفريغ
            discharge_port_item = QTableWidgetItem(shipment.port_of_discharge or "")
            self.shipments_table.setItem(row, 15, discharge_port_item)

            # ميناء الوصول
            arrival_port_item = QTableWidgetItem(shipment.port_of_arrival or "")
            self.shipments_table.setItem(row, 16, arrival_port_item)

            # الوجهة النهائية
            destination_item = QTableWidgetItem(shipment.final_destination or "")
            self.shipments_table.setItem(row, 17, destination_item)

            # اسم السفينة
            vessel_item = QTableWidgetItem(shipment.vessel_name or "")
            self.shipments_table.setItem(row, 18, vessel_item)

            # رقم الرحلة
            voyage_item = QTableWidgetItem(shipment.voyage_number or "")
            self.shipments_table.setItem(row, 19, voyage_item)

            # تاريخ المغادرة - بتنسيق الويندوز
            departure_date = format_date(shipment.actual_departure_date, 'short') if shipment.actual_departure_date else ""
            departure_item = QTableWidgetItem(departure_date)
            self.shipments_table.setItem(row, 20, departure_item)

            # تاريخ الوصول المتوقع - بتنسيق الويندوز
            est_arrival = format_date(shipment.estimated_arrival_date, 'short') if shipment.estimated_arrival_date else ""
            est_arrival_item = QTableWidgetItem(est_arrival)
            self.shipments_table.setItem(row, 21, est_arrival_item)
            
    def search_shipments(self):
        """البحث المتقدم في الشحنات - يبحث في قاعدة البيانات"""
        search_text = self.search_edit.text().strip()
        if not search_text:
            self.load_shipments()
            return

        session = self.db_manager.get_session()
        try:
            # البحث الشامل في جميع الحقول المهمة
            shipments = session.query(Shipment).join(Supplier).filter(
                Shipment.is_active == True,
                (Shipment.shipment_number.contains(search_text) |
                 Supplier.name.contains(search_text) |
                 Supplier.code.contains(search_text) |
                 Shipment.supplier_invoice_number.contains(search_text) |
                 Shipment.tracking_number.contains(search_text) |
                 Shipment.bill_of_lading.contains(search_text) |
                 Shipment.shipping_company.contains(search_text) |
                 Shipment.shipping_method.contains(search_text) |
                 Shipment.loading_port.contains(search_text) |
                 Shipment.discharge_port.contains(search_text) |
                 Shipment.final_destination.contains(search_text) |
                 Shipment.vessel_name.contains(search_text) |
                 Shipment.voyage_number.contains(search_text))
            ).order_by(Shipment.created_at.desc()).all()

            # تحميل بيانات الحاويات والأصناف لكل شحنة
            for shipment in shipments:
                shipment.containers = session.query(Container).filter(
                    Container.shipment_id == shipment.id
                ).all()
                shipment.items = session.query(ShipmentItem).filter(
                    ShipmentItem.shipment_id == shipment.id
                ).all()

            self.populate_table(shipments)
            self.status_bar.showMessage(f"تم العثور على {len(shipments)} شحنة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث: {str(e)}")
        finally:
            session.close()
            
    def filter_shipments(self):
        """فلترة الشحنات"""
        status_filter = self.status_filter_combo.currentData() or self.status_filter_combo.currentText()
        clearance_filter = self.clearance_filter_combo.currentData() or self.clearance_filter_combo.currentText()
        
        session = self.db_manager.get_session()
        try:
            query = session.query(Shipment).join(Supplier).filter(Shipment.is_active == True)
            
            if status_filter and status_filter != "جميع الحالات":
                query = query.filter(Shipment.shipment_status == status_filter)
                
            if clearance_filter and clearance_filter != "جميع حالات الإفراج":
                query = query.filter(Shipment.clearance_status == clearance_filter)
                
            shipments = query.order_by(Shipment.created_at.desc()).all()

            # تحميل بيانات الحاويات والأصناف لكل شحنة
            for shipment in shipments:
                shipment.containers = session.query(Container).filter(
                    Container.shipment_id == shipment.id
                ).all()
                shipment.items = session.query(ShipmentItem).filter(
                    ShipmentItem.shipment_id == shipment.id
                ).all()

            self.populate_table(shipments)
            self.status_bar.showMessage(f"تم فلترة {len(shipments)} شحنة")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في الفلترة: {str(e)}")
        finally:
            session.close()

    def instant_search(self):
        """البحث الفوري السريع أثناء الكتابة - مع تأخير لتحسين الأداء"""
        # إيقاف timer السابق وبدء timer جديد
        self.search_timer.stop()
        self.search_timer.start(300)  # تأخير 300 مللي ثانية

    def perform_instant_search(self):
        """تنفيذ البحث الفوري الفعلي"""
        search_text = self.search_edit.text().strip().lower()

        # إذا كان النص فارغاً، إظهار جميع الصفوف
        if not search_text:
            for row in range(self.shipments_table.rowCount()):
                self.shipments_table.setRowHidden(row, False)
            self.status_bar.showMessage(f"عرض جميع الشحنات ({self.shipments_table.rowCount()})")
            return

        # تقسيم النص إلى كلمات للبحث المتقدم
        search_words = search_text.split()

        # البحث في جميع الأعمدة وإخفاء/إظهار الصفوف حسب النتيجة
        visible_count = 0
        for row in range(self.shipments_table.rowCount()):
            row_matches = False

            # جمع نص جميع الأعمدة في الصف
            row_text = ""
            for col in range(self.shipments_table.columnCount()):
                item = self.shipments_table.item(row, col)
                if item:
                    row_text += item.text().lower() + " "

            # التحقق من وجود جميع الكلمات في الصف (البحث بـ AND)
            if all(word in row_text for word in search_words):
                row_matches = True

            # إخفاء أو إظهار الصف حسب نتيجة البحث
            self.shipments_table.setRowHidden(row, not row_matches)
            if row_matches:
                visible_count += 1

        # تحديث شريط الحالة بعدد النتائج المرئية
        if len(search_words) > 1:
            self.status_bar.showMessage(f"البحث الفوري ({len(search_words)} كلمات): عرض {visible_count} من {self.shipments_table.rowCount()} شحنة")
        else:
            self.status_bar.showMessage(f"البحث الفوري: عرض {visible_count} من {self.shipments_table.rowCount()} شحنة")

    def clear_search(self):
        """مسح البحث"""
        self.search_edit.clear()
        self.status_filter_combo.setCurrentIndex(0)
        self.clearance_filter_combo.setCurrentIndex(0)

        # إظهار جميع الصفوف المخفية من البحث الفوري
        for row in range(self.shipments_table.rowCount()):
            self.shipments_table.setRowHidden(row, False)

        # إعادة تحميل البيانات
        self.load_shipments()
        
    def new_shipment(self):
        """إنشاء شحنة جديدة"""
        dialog = NewShipmentWindow(self)
        dialog.shipment_saved.connect(self.load_shipments)
        dialog.shipment_deleted.connect(self.load_shipments)
        dialog.exec()
        
    def edit_shipment(self):
        """تعديل الشحنة المحددة"""
        current_row = self.shipments_table.currentRow()
        if current_row >= 0:
            shipment_id = self.shipments_table.item(current_row, 0).data(Qt.UserRole)

            # فتح نافذة تعديل الشحنة
            try:
                from .new_shipment_window import NewShipmentWindow

                # إنشاء نافذة التعديل مع تمرير معرف الشحنة
                edit_dialog = NewShipmentWindow(self, shipment_id=shipment_id)
                edit_dialog.shipment_saved.connect(self.load_shipments)
                edit_dialog.shipment_deleted.connect(self.load_shipments)
                edit_dialog.exec()

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"فشل في فتح نافذة تعديل الشحنة:\n{str(e)}"
                )
        else:
            QMessageBox.warning(self, "تحذير", "يجب اختيار شحنة أولاً")
            
    def show_context_menu(self, position):
        """إظهار قائمة الزر الأيمن للجدول"""
        # التحقق من وجود صف محدد
        item = self.shipments_table.itemAt(position)
        if item is None:
            return

        # الحصول على معرف الشحنة من الصف المحدد
        current_row = item.row()
        shipment_id_item = self.shipments_table.item(current_row, 0)
        if not shipment_id_item:
            return

        shipment_id = shipment_id_item.data(Qt.UserRole)
        if not shipment_id:
            return

        # إنشاء قائمة الزر الأيمن
        context_menu = QMenu(self)

        # عملية تعديل الشحنة
        edit_action = context_menu.addAction("تعديل الشحنة")
        edit_action.triggered.connect(lambda: self.edit_shipment_from_context_menu(shipment_id))

        # فاصل
        context_menu.addSeparator()

        # عملية التعبئة التلقائية
        auto_fill_action = context_menu.addAction("🤖 تعبئة تلقائية")
        auto_fill_action.triggered.connect(lambda: self.auto_fill_shipment_data(shipment_id))

        # فاصل
        context_menu.addSeparator()

        # عملية حذف الشحنة
        delete_action = context_menu.addAction("حذف الشحنة")
        delete_action.triggered.connect(lambda: self.delete_shipment_from_context_menu(shipment_id))

        # إظهار القائمة في الموضع المحدد
        context_menu.exec(self.shipments_table.mapToGlobal(position))

    def edit_shipment_from_context_menu(self, shipment_id):
        """تعديل الشحنة من قائمة الزر الأيمن"""
        try:
            from .new_shipment_window import NewShipmentWindow

            # إنشاء نافذة التعديل مع تمرير معرف الشحنة
            edit_dialog = NewShipmentWindow(self, shipment_id=shipment_id)
            edit_dialog.shipment_saved.connect(self.load_shipments)
            edit_dialog.shipment_deleted.connect(self.load_shipments)
            edit_dialog.exec()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة تعديل الشحنة:\n{str(e)}"
            )

    def delete_shipment_from_context_menu(self, shipment_id):
        """حذف الشحنة من قائمة الزر الأيمن"""
        # طلب تأكيد من المستخدم
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "هل أنت متأكد من حذف هذه الشحنة؟\n\n"
            "تحذير: سيتم حذف الشحنة وجميع البيانات المرتبطة بها.\n"
            "هذه العملية لا يمكن التراجع عنها.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                # البحث عن الشحنة
                shipment = session.query(Shipment).filter(Shipment.id == shipment_id).first()
                if shipment:
                    # الحذف المنطقي
                    shipment.is_active = False
                    session.commit()

                    # إعادة تحميل البيانات
                    self.load_shipments()

                    # رسالة نجاح
                    QMessageBox.information(
                        self,
                        "تم الحذف",
                        "تم حذف الشحنة بنجاح"
                    )
                else:
                    QMessageBox.warning(
                        self,
                        "خطأ",
                        "لم يتم العثور على الشحنة المحددة"
                    )

            except Exception as e:
                session.rollback()
                QMessageBox.critical(
                    self,
                    "خطأ في الحذف",
                    f"فشل في حذف الشحنة:\n{str(e)}"
                )
            finally:
                session.close()

    def open_advanced_tracking_window(self):
        """فتح نافذة تتبع الشحنات المتقدمة"""
        try:
            from .advanced_shipment_tracking_window import AdvancedShipmentTrackingWindow
            tracking_window = AdvancedShipmentTrackingWindow(self)
            tracking_window.show()
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة تتبع الشحنات المتقدمة:\n{str(e)}"
            )
            print(f"خطأ في فتح نافذة التتبع المتقدمة: {e}")
            import traceback
            traceback.print_exc()

    def auto_fill_shipment_data(self, shipment_id):
        """تعبئة بيانات الشحنة تلقائياً من خلال البحث عبر الإنترنت"""
        try:
            # الحصول على بيانات الشحنة
            session = self.db_manager.get_session()
            shipment = session.query(Shipment).filter(Shipment.id == shipment_id).first()

            if not shipment:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على الشحنة")
                session.close()
                return

            # التحقق من وجود رقم الحاوية
            container_number = None
            if shipment.container_number:
                container_number = shipment.container_number
            elif shipment.containers and len(shipment.containers) > 0:
                container_number = shipment.containers[0].container_number

            session.close()

            if not container_number:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "لا يوجد رقم حاوية لهذه الشحنة.\nيرجى إضافة رقم الحاوية أولاً لتتمكن من استخدام التعبئة التلقائية."
                )
                return

            # فتح نافذة التعبئة التلقائية
            from ..dialogs.auto_fill_dialog import AutoFillDialog

            auto_fill_dialog = AutoFillDialog(
                parent=self,
                shipment_id=shipment_id,
                container_number=container_number
            )

            # إذا تم قبول النافذة، قم بتحديث الجدول
            result = auto_fill_dialog.exec()

            # التأكد من تنظيف الموارد
            try:
                if auto_fill_dialog.worker and auto_fill_dialog.worker.isRunning():
                    auto_fill_dialog.worker.quit()
                    auto_fill_dialog.worker.wait(1000)
            except:
                pass

            # تحديث الجدول فقط إذا تم تحديث البيانات
            if result == QDialog.Accepted and hasattr(auto_fill_dialog, 'data_updated') and auto_fill_dialog.data_updated:
                # إضافة تأخير أطول للسماح لقاعدة البيانات بالتحديث
                from PySide6.QtCore import QTimer
                QTimer.singleShot(500, self.refresh_table_safely)  # تأخير 500 مللي ثانية

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة التعبئة التلقائية:\n{str(e)}"
            )
            print(f"خطأ في التعبئة التلقائية: {e}")
            import traceback
            traceback.print_exc()

    def refresh_table_safely(self):
        """تحديث الجدول بطريقة آمنة"""
        # تجنب التحديث المتداخل
        if self._is_refreshing:
            print("تحديث الجدول قيد التنفيذ بالفعل، تم تجاهل الطلب")
            return

        try:
            self._is_refreshing = True
            # إضافة تأخير إضافي للتأكد من اكتمال العمليات
            from PySide6.QtCore import QTimer
            QTimer.singleShot(100, self._perform_table_refresh)
        except Exception as e:
            print(f"خطأ في تحديث الجدول: {e}")
            self._is_refreshing = False

    def _perform_table_refresh(self):
        """تنفيذ تحديث الجدول الفعلي"""
        try:
            # إنشاء جلسة جديدة منفصلة
            session = None
            try:
                session = self.db_manager.get_session()

                # استعلام بسيط بدون eager loading لتجنب المشاكل
                shipments = session.query(Shipment).filter(
                    Shipment.is_active == True
                ).order_by(Shipment.created_at.desc()).all()

                # تحميل البيانات المرتبطة بشكل منفصل
                for shipment in shipments:
                    if not hasattr(shipment, '_supplier_loaded'):
                        try:
                            _ = shipment.supplier  # تحميل المورد
                            shipment._supplier_loaded = True
                        except:
                            pass

                self.populate_table(shipments)
                self.status_bar.showMessage(f"تم تحديث {len(shipments)} شحنة")

            finally:
                if session:
                    session.close()
                self._is_refreshing = False  # إعادة تعيين حالة التحديث

        except Exception as e:
            print(f"خطأ في تحديث الجدول: {e}")
            self._is_refreshing = False  # إعادة تعيين حالة التحديث
            # في حالة فشل التحديث، حاول مرة أخرى بعد ثانيتين
            from PySide6.QtCore import QTimer
            QTimer.singleShot(2000, self.load_shipments)
