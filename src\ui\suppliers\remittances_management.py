# -*- coding: utf-8 -*-
"""
ويدجت إدارة الحوالات
Remittances Management Widget
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QLineEdit, QComboBox, QDateEdit,
                               QDoubleSpinBox, QTextEdit, QTableWidget, QTableWidgetItem,
                               QHeaderView, QAbstractItemView, QGroupBox, QFormLayout,
                               QMessageBox, QDialog, QDialogButtonBox, QSplitter,
                               QFrame, QCheckBox, QSpinBox, QProgressBar)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QFont, QIcon, QPalette, QColor

from ...database.database_manager import DatabaseManager
from ...database.models import Remittance, Supplier, Currency, Bank, BankAccount, RemittanceStatus
from .enhanced_form_fields import (FlexibleDateEdit, EnhancedAmountEdit, EnhancedExchangeRateEdit,
                                   AdvancedSupplierCombo, AdvancedSupplierSearchDialog)


class RemittancesManagementWidget(QWidget):
    """ويدجت إدارة الحوالات"""
    
    # الإشارات
    remittance_created = Signal(dict)
    remittance_updated = Signal(dict)
    remittance_deleted = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.current_remittance = None
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        self.create_toolbar()
        layout.addWidget(self.toolbar_frame)
        
        # المحتوى الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # الجانب الأيسر - نموذج الحوالة
        self.create_remittance_form()
        splitter.addWidget(self.form_group)
        
        # الجانب الأيمن - قائمة الحوالات
        self.create_remittances_table()
        splitter.addWidget(self.table_group)
        
        # تعيين النسب
        splitter.setSizes([400, 600])
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar_frame = QFrame()
        self.toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_layout = QHBoxLayout(self.toolbar_frame)
        
        # أزرار العمليات
        self.new_btn = QPushButton("💰 حوالة جديدة")
        self.new_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e7e34, stop:1 #155724);
            }
        """)

        # زر إنشاء حوالة متعددة الموردين
        self.new_multi_btn = QPushButton("👥 حوالة متعددة")
        self.new_multi_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
        """)

        self.save_btn = QPushButton("💾 حفظ")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0056b3, stop:1 #004085);
            }
        """)
        
        self.edit_btn = QPushButton("✏️ تعديل")
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffc107, stop:1 #e0a800);
                border: none;
                border-radius: 6px;
                color: #212529;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e0a800, stop:1 #d39e00);
            }
        """)
        
        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc3545, stop:1 #c82333);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c82333, stop:1 #bd2130);
            }
        """)
        
        self.approve_btn = QPushButton("✅ اعتماد")
        self.approve_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #17a2b8, stop:1 #138496);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #138496, stop:1 #117a8b);
            }
        """)
        
        # إضافة الأزرار
        toolbar_layout.addWidget(self.new_btn)
        toolbar_layout.addWidget(self.new_multi_btn)
        toolbar_layout.addWidget(self.save_btn)
        toolbar_layout.addWidget(self.edit_btn)
        toolbar_layout.addWidget(self.delete_btn)
        toolbar_layout.addWidget(self.approve_btn)
        toolbar_layout.addStretch()
        
        # شريط البحث
        search_label = QLabel("🔍 بحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث برقم الحوالة أو اسم المورد...")
        self.search_edit.setMaximumWidth(300)
        
        toolbar_layout.addWidget(search_label)
        toolbar_layout.addWidget(self.search_edit)
        
    def create_remittance_form(self):
        """إنشاء نموذج الحوالة"""
        self.form_group = QGroupBox("📝 بيانات الحوالة")
        form_layout = QGridLayout(self.form_group)
        form_layout.setSpacing(15)
        form_layout.setColumnStretch(1, 1)
        form_layout.setColumnStretch(3, 1)

        # الصف الأول: رقم الحوالة + تاريخ الحوالة
        form_layout.addWidget(QLabel("رقم الحوالة:"), 0, 0)
        self.number_edit = QLineEdit()
        self.number_edit.setReadOnly(True)
        self.number_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً")
        self.number_edit.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                padding: 8px;
                font-weight: bold;
                color: #495057;
            }
        """)
        form_layout.addWidget(self.number_edit, 0, 1)

        form_layout.addWidget(QLabel("تاريخ الحوالة:"), 0, 2)
        self.date_edit = FlexibleDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setStyleSheet("""
            QDateEdit {
                border: 2px solid #007bff;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
            }
            QDateEdit:focus {
                border-color: #0056b3;
                background-color: #f0f8ff;
            }
            QDateEdit::drop-down {
                border: none;
                background: #007bff;
                border-radius: 3px;
                width: 20px;
            }
            QDateEdit::down-arrow {
                image: none;
                border: 2px solid white;
                width: 6px;
                height: 6px;
                border-top: none;
                border-right: none;
                transform: rotate(-45deg);
            }
        """)
        form_layout.addWidget(self.date_edit, 0, 3)

        # الصف الثاني: المورد (موسع عبر عدة أعمدة)
        form_layout.addWidget(QLabel("المورد:"), 1, 0)
        self.supplier_combo = AdvancedSupplierCombo()
        self.supplier_combo.setEditable(True)
        self.supplier_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #28a745;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #1e7e34;
                background-color: #f0fff4;
            }
            QComboBox::drop-down {
                border: none;
                background: #28a745;
                border-radius: 3px;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid white;
                width: 6px;
                height: 6px;
                border-top: none;
                border-right: none;
                transform: rotate(-45deg);
            }
        """)
        form_layout.addWidget(self.supplier_combo, 1, 1, 1, 3)  # توسيع عبر 3 أعمدة

        # إخفاء زر F9 (لكن الاحتفاظ به للوظائف)
        self.search_supplier_btn = QPushButton("🔍 F9")
        self.search_supplier_btn.setVisible(False)  # إخفاء الزر

        # الصف الثالث: العملة + سعر الصرف + المبلغ (في نفس الصف)
        form_layout.addWidget(QLabel("العملة:"), 2, 0)
        self.currency_combo = QComboBox()
        self.currency_combo.setMaximumWidth(120)  # تقليل العرض
        self.currency_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #ffc107;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #e0a800;
                background-color: #fffbf0;
            }
        """)
        form_layout.addWidget(self.currency_combo, 2, 1)

        # تقليل المسافة بين العملة وسعر الصرف
        form_layout.setColumnMinimumWidth(1, 130)  # عرض ثابت للعملة

        # سعر الصرف
        form_layout.addWidget(QLabel("سعر الصرف:"), 2, 2)
        self.exchange_rate_edit = EnhancedExchangeRateEdit()
        self.exchange_rate_edit.setText("1.000000")
        self.exchange_rate_edit.setMaximumWidth(120)  # تقليل العرض
        form_layout.addWidget(self.exchange_rate_edit, 2, 3)

        # تقليل المسافة بين سعر الصرف والمبلغ
        form_layout.setColumnMinimumWidth(3, 130)  # عرض ثابت لسعر الصرف

        # المبلغ (في نفس الصف)
        form_layout.addWidget(QLabel("المبلغ:"), 2, 4)
        self.amount_edit = EnhancedAmountEdit()
        self.amount_edit.setPlaceholderText("0.000")
        form_layout.addWidget(self.amount_edit, 2, 5)

        # الصف الرابع: البنك المرسل + حساب المرسل
        form_layout.addWidget(QLabel("البنك المرسل:"), 3, 0)
        self.sender_bank_combo = QComboBox()
        self.sender_bank_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #6f42c1;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #5a2d91;
                background-color: #f8f4ff;
            }
        """)
        form_layout.addWidget(self.sender_bank_combo, 3, 1)

        form_layout.addWidget(QLabel("حساب المرسل:"), 3, 2)
        self.sender_account_combo = QComboBox()
        self.sender_account_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #6f42c1;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #5a2d91;
                background-color: #f8f4ff;
            }
        """)
        form_layout.addWidget(self.sender_account_combo, 3, 3)

        # الصف الخامس: البنك المستقبل + حساب المستقبل
        form_layout.addWidget(QLabel("البنك المستقبل:"), 4, 0)
        self.receiver_bank_combo = QComboBox()
        self.receiver_bank_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #e74c3c;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #c0392b;
                background-color: #fdf2f2;
            }
        """)
        form_layout.addWidget(self.receiver_bank_combo, 4, 1)

        form_layout.addWidget(QLabel("حساب المستقبل:"), 4, 2)
        self.receiver_account_combo = QComboBox()
        self.receiver_account_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #e74c3c;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #c0392b;
                background-color: #fdf2f2;
            }
        """)
        form_layout.addWidget(self.receiver_account_combo, 4, 3)

        # الصف السادس: الغرض
        form_layout.addWidget(QLabel("الغرض:"), 5, 0)
        self.purpose_edit = QLineEdit()
        self.purpose_edit.setPlaceholderText("الغرض من الحوالة...")
        self.purpose_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #17a2b8;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #138496;
                background-color: #f0fdff;
            }
        """)
        form_layout.addWidget(self.purpose_edit, 5, 1, 1, 5)  # توسيع عبر 5 أعمدة

        # الصف السابع: الملاحظات
        form_layout.addWidget(QLabel("الملاحظات:"), 6, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                border: 2px solid #6c757d;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
            }
            QTextEdit:focus {
                border-color: #495057;
                background-color: #f8f9fa;
            }
        """)
        form_layout.addWidget(self.notes_edit, 6, 1, 1, 5)  # توسيع عبر 5 أعمدة

        # الصف الثامن: الحالة
        form_layout.addWidget(QLabel("الحالة:"), 7, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItems([status.value for status in RemittanceStatus])
        self.status_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #fd7e14;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
                min-height: 20px;
                font-weight: bold;
            }
            QComboBox:focus {
                border-color: #e8590c;
                background-color: #fff8f0;
            }
        """)
        form_layout.addWidget(self.status_combo, 7, 1)
        
    def create_remittances_table(self):
        """إنشاء جدول الحوالات"""
        self.table_group = QGroupBox("📋 قائمة الحوالات")
        table_layout = QVBoxLayout(self.table_group)
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_layout = QHBoxLayout(stats_frame)
        
        self.total_remittances_label = QLabel("إجمالي الحوالات: 0")
        self.total_amount_label = QLabel("إجمالي المبلغ: 0.000")
        self.pending_count_label = QLabel("في الانتظار: 0")
        
        stats_layout.addWidget(self.total_remittances_label)
        stats_layout.addWidget(self.total_amount_label)
        stats_layout.addWidget(self.pending_count_label)
        stats_layout.addStretch()
        
        table_layout.addWidget(stats_frame)
        
        # الجدول
        self.remittances_table = QTableWidget()
        self.remittances_table.setColumnCount(8)
        self.remittances_table.setHorizontalHeaderLabels([
            "رقم الحوالة", "المورد", "العملة", "المبلغ", 
            "تاريخ الحوالة", "الحالة", "البنك المرسل", "البنك المستقبل"
        ])
        
        # إعداد الجدول
        header = self.remittances_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.remittances_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.remittances_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.remittances_table.setAlternatingRowColors(True)
        self.remittances_table.setSortingEnabled(True)
        
        table_layout.addWidget(self.remittances_table)

    def setup_connections(self):
        """إعداد الاتصالات"""
        # أزرار العمليات
        self.new_btn.clicked.connect(self.new_remittance)
        self.new_multi_btn.clicked.connect(self.new_multi_supplier_remittance)
        self.save_btn.clicked.connect(self.save_remittance)
        self.edit_btn.clicked.connect(self.edit_remittance)
        self.delete_btn.clicked.connect(self.delete_remittance)
        self.approve_btn.clicked.connect(self.approve_remittance)

        # البحث
        self.search_edit.textChanged.connect(self.filter_remittances)

        # الجدول
        self.remittances_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.remittances_table.itemDoubleClicked.connect(self.edit_remittance)

        # تحديث الحسابات عند تغيير البنك
        self.sender_bank_combo.currentTextChanged.connect(self.update_sender_accounts)
        self.receiver_bank_combo.currentTextChanged.connect(self.update_receiver_accounts)

        # الحقول المحسنة
        self.search_supplier_btn.clicked.connect(self.supplier_combo.show_advanced_search)
        self.supplier_combo.supplier_selected.connect(self.on_supplier_selected)
        self.amount_edit.amount_changed.connect(self.calculate_base_amount)
        self.exchange_rate_edit.rate_changed.connect(self.calculate_base_amount)

    def load_data(self):
        """تحميل البيانات"""
        try:
            # تحميل الموردين
            self.load_suppliers()

            # تحميل العملات
            self.load_currencies()

            # تحميل البنوك
            self.load_banks()

            # تحميل الحوالات
            self.load_remittances()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            session = self.db_manager.get_session()
            suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()

            self.supplier_combo.clear()
            self.supplier_combo.addItem("", None)

            for supplier in suppliers:
                display_text = f"{supplier.code} - {supplier.name}"
                self.supplier_combo.addItem(display_text, supplier.id)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الموردين:\n{str(e)}")

    def load_currencies(self):
        """تحميل قائمة العملات"""
        try:
            session = self.db_manager.get_session()
            currencies = session.query(Currency).filter(Currency.is_active == True).all()

            self.currency_combo.clear()
            self.currency_combo.addItem("", None)

            for currency in currencies:
                display_text = f"{currency.code} - {currency.name}"
                self.currency_combo.addItem(display_text, currency.id)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل العملات:\n{str(e)}")

    def load_banks(self):
        """تحميل قائمة البنوك"""
        try:
            session = self.db_manager.get_session()
            banks = session.query(Bank).filter(Bank.is_active == True).all()

            # تنظيف القوائم
            self.sender_bank_combo.clear()
            self.receiver_bank_combo.clear()

            # إضافة خيار فارغ
            self.sender_bank_combo.addItem("", None)
            self.receiver_bank_combo.addItem("", None)

            for bank in banks:
                display_text = f"{bank.code} - {bank.name}"
                self.sender_bank_combo.addItem(display_text, bank.id)
                self.receiver_bank_combo.addItem(display_text, bank.id)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل البنوك:\n{str(e)}")

    def update_sender_accounts(self):
        """تحديث حسابات البنك المرسل"""
        self.update_bank_accounts(self.sender_bank_combo, self.sender_account_combo)

    def update_receiver_accounts(self):
        """تحديث حسابات البنك المستقبل"""
        self.update_bank_accounts(self.receiver_bank_combo, self.receiver_account_combo)

    def update_bank_accounts(self, bank_combo, account_combo):
        """تحديث حسابات البنك"""
        try:
            bank_id = bank_combo.currentData()
            account_combo.clear()
            account_combo.addItem("", None)

            if bank_id:
                session = self.db_manager.get_session()
                accounts = session.query(BankAccount).filter(
                    BankAccount.bank_id == bank_id,
                    BankAccount.is_active == True
                ).all()

                for account in accounts:
                    display_text = f"{account.account_number} - {account.account_name}"
                    account_combo.addItem(display_text, account.id)

                session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل حسابات البنك:\n{str(e)}")

    def on_supplier_selected(self, supplier_data):
        """معالج اختيار المورد"""
        try:
            # يمكن إضافة منطق إضافي هنا مثل تحميل معلومات المورد
            print(f"تم اختيار المورد: {supplier_data['name']}")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء اختيار المورد:\n{str(e)}")

    def calculate_base_amount(self):
        """حساب المبلغ بالعملة الأساسية"""
        try:
            amount = self.amount_edit.get_amount()
            exchange_rate = self.exchange_rate_edit.get_rate()
            base_amount = amount * exchange_rate

            # يمكن عرض المبلغ الأساسي في مكان ما إذا لزم الأمر
            print(f"المبلغ الأساسي: {base_amount:.3f}")

        except Exception as e:
            print(f"خطأ في حساب المبلغ الأساسي: {e}")

        # يمكن إضافة عرض المبلغ بالعملة الأساسية هنا

    def load_remittances(self):
        """تحميل قائمة الحوالات"""
        try:
            session = self.db_manager.get_session()
            remittances = session.query(Remittance).order_by(Remittance.created_at.desc()).all()

            self.remittances_table.setRowCount(len(remittances))

            total_amount = 0
            pending_count = 0

            for row, remittance in enumerate(remittances):
                # رقم الحوالة
                self.remittances_table.setItem(row, 0, QTableWidgetItem(remittance.remittance_number))

                # المورد
                supplier_name = remittance.supplier.name if remittance.supplier else ""
                self.remittances_table.setItem(row, 1, QTableWidgetItem(supplier_name))

                # العملة
                currency_code = remittance.currency.code if remittance.currency else ""
                self.remittances_table.setItem(row, 2, QTableWidgetItem(currency_code))

                # المبلغ
                amount_item = QTableWidgetItem(f"{remittance.amount:.3f}")
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.remittances_table.setItem(row, 3, amount_item)

                # تاريخ الحوالة
                date_str = remittance.remittance_date.strftime("%Y-%m-%d") if remittance.remittance_date else ""
                self.remittances_table.setItem(row, 4, QTableWidgetItem(date_str))

                # الحالة
                status_item = QTableWidgetItem(remittance.status.value if remittance.status else "")
                # تلوين الحالة
                if remittance.status == RemittanceStatus.COMPLETED:
                    status_item.setBackground(QColor("#d4edda"))
                elif remittance.status == RemittanceStatus.PENDING:
                    status_item.setBackground(QColor("#fff3cd"))
                    pending_count += 1
                elif remittance.status == RemittanceStatus.CANCELLED:
                    status_item.setBackground(QColor("#f8d7da"))

                self.remittances_table.setItem(row, 5, status_item)

                # البنك المرسل
                sender_bank = remittance.sender_bank.name if remittance.sender_bank else ""
                self.remittances_table.setItem(row, 6, QTableWidgetItem(sender_bank))

                # البنك المستقبل
                receiver_bank = remittance.receiver_bank.name if remittance.receiver_bank else ""
                self.remittances_table.setItem(row, 7, QTableWidgetItem(receiver_bank))

                # حفظ معرف الحوالة
                self.remittances_table.item(row, 0).setData(Qt.UserRole, remittance.id)

                # إضافة للإجمالي
                if remittance.status != RemittanceStatus.CANCELLED:
                    total_amount += float(remittance.amount or 0)

            # تحديث الإحصائيات
            self.total_remittances_label.setText(f"إجمالي الحوالات: {len(remittances)}")
            self.total_amount_label.setText(f"إجمالي المبلغ: {total_amount:,.3f}")
            self.pending_count_label.setText(f"في الانتظار: {pending_count}")

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الحوالات:\n{str(e)}")

    def filter_remittances(self):
        """تصفية الحوالات حسب النص المدخل"""
        search_text = self.search_edit.text().lower()

        for row in range(self.remittances_table.rowCount()):
            show_row = False

            # البحث في جميع الأعمدة
            for col in range(self.remittances_table.columnCount()):
                item = self.remittances_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break

            self.remittances_table.setRowHidden(row, not show_row)

    def new_remittance(self):
        """إنشاء حوالة جديدة"""
        self.clear_form()
        self.current_remittance = None

        # إنشاء رقم حوالة جديد
        new_number = self.generate_remittance_number()
        self.number_edit.setText(new_number)

        # تعيين القيم الافتراضية
        self.date_edit.setDate(QDate.currentDate())
        self.status_combo.setCurrentText(RemittanceStatus.DRAFT.value)
        self.exchange_rate_edit.set_rate(1.0)

        # تفعيل النموذج للتحرير
        self.set_form_enabled(True)

    def new_multi_supplier_remittance(self):
        """إنشاء حوالة متعددة الموردين"""
        try:
            from .multi_supplier_remittance_dialog import MultiSupplierRemittanceDialog

            dialog = MultiSupplierRemittanceDialog(self)
            dialog.remittance_created.connect(self.on_multi_remittance_created)

            if dialog.exec() == QDialog.Accepted:
                # تحديث قائمة الحوالات
                self.load_data()
                QMessageBox.information(self, "نجح", "تم إنشاء الحوالة متعددة الموردين بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة الحوالة متعددة الموردين:\n{str(e)}")

    def on_multi_remittance_created(self, remittance_data):
        """معالج إنشاء حوالة متعددة الموردين"""
        try:
            # تحديث قائمة الحوالات
            self.load_data()

            # عرض رسالة نجاح مع تفاصيل الحوالة
            message = f"""تم إنشاء الحوالة بنجاح!

رقم الحوالة: {remittance_data.get('remittance_number', 'غير محدد')}
المبلغ الإجمالي: {remittance_data.get('amount', 0):,.3f}
عدد الموردين: {remittance_data.get('suppliers_count', 0)}"""

            QMessageBox.information(self, "تم إنشاء الحوالة", message)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء معالجة الحوالة الجديدة:\n{str(e)}")

    def generate_remittance_number(self):
        """إنشاء رقم حوالة تلقائي"""
        try:
            session = self.db_manager.get_session()

            # الحصول على آخر رقم حوالة
            last_remittance = session.query(Remittance).order_by(Remittance.id.desc()).first()

            if last_remittance and last_remittance.remittance_number:
                # استخراج الرقم من آخر حوالة
                try:
                    last_number = int(last_remittance.remittance_number.split('-')[-1])
                    new_number = last_number + 1
                except:
                    new_number = 1
            else:
                new_number = 1

            # تنسيق الرقم
            from datetime import datetime
            current_year = datetime.now().year
            formatted_number = f"REM-{current_year}-{new_number:06d}"

            session.close()
            return formatted_number

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إنشاء رقم الحوالة:\n{str(e)}")
            return f"REM-{QDate.currentDate().year()}-000001"

    def save_remittance(self):
        """حفظ الحوالة"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_form():
                return

            session = self.db_manager.get_session()

            # إنشاء أو تحديث الحوالة
            if self.current_remittance is None:
                # حوالة جديدة
                remittance = Remittance()
                remittance.remittance_number = self.number_edit.text()
                remittance.created_by = 1  # يجب الحصول على معرف المستخدم الحالي
            else:
                # تحديث حوالة موجودة
                remittance = session.query(Remittance).get(self.current_remittance)
                if not remittance:
                    QMessageBox.warning(self, "خطأ", "لم يتم العثور على الحوالة المحددة")
                    session.close()
                    return

            # تعيين البيانات
            remittance.supplier_id = self.supplier_combo.currentData()
            remittance.currency_id = self.currency_combo.currentData()
            remittance.amount = self.amount_edit.get_amount()
            remittance.exchange_rate = self.exchange_rate_edit.get_rate()
            remittance.amount_in_base_currency = self.amount_edit.get_amount() * self.exchange_rate_edit.get_rate()
            remittance.remittance_date = self.date_edit.date().toPython()
            remittance.sender_bank_id = self.sender_bank_combo.currentData()
            remittance.sender_account_id = self.sender_account_combo.currentData()
            remittance.receiver_bank_id = self.receiver_bank_combo.currentData()
            remittance.receiver_account_id = self.receiver_account_combo.currentData()
            remittance.purpose = self.purpose_edit.text()
            remittance.notes = self.notes_edit.toPlainText()

            # تعيين الحالة
            status_text = self.status_combo.currentText()
            for status in RemittanceStatus:
                if status.value == status_text:
                    remittance.status = status
                    break

            # حفظ في قاعدة البيانات
            if self.current_remittance is None:
                session.add(remittance)

            session.commit()

            # تحديث المعرف الحالي
            if self.current_remittance is None:
                self.current_remittance = remittance.id

            session.close()

            # تحديث الجدول
            self.load_remittances()

            # إرسال إشارة
            remittance_data = {
                'id': remittance.id,
                'number': remittance.remittance_number,
                'supplier_id': remittance.supplier_id,
                'amount': float(remittance.amount),
                'currency_id': remittance.currency_id
            }

            if self.current_remittance:
                self.remittance_updated.emit(remittance_data)
            else:
                self.remittance_created.emit(remittance_data)

            QMessageBox.information(self, "نجح", "تم حفظ الحوالة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الحوالة:\n{str(e)}")

    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.number_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "رقم الحوالة مطلوب")
            return False

        if not self.supplier_combo.currentData():
            QMessageBox.warning(self, "خطأ", "يجب اختيار المورد")
            return False

        if not self.currency_combo.currentData():
            QMessageBox.warning(self, "خطأ", "يجب اختيار العملة")
            return False

        if self.amount_edit.get_amount() <= 0:
            QMessageBox.warning(self, "خطأ", "المبلغ يجب أن يكون أكبر من صفر")
            self.amount_edit.setFocus()
            return False

        if self.exchange_rate_edit.get_rate() <= 0:
            QMessageBox.warning(self, "خطأ", "سعر الصرف يجب أن يكون أكبر من صفر")
            self.exchange_rate_edit.setFocus()
            return False

        return True

    def edit_remittance(self):
        """تعديل الحوالة المحددة"""
        current_row = self.remittances_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يجب اختيار حوالة للتعديل")
            return

        # الحصول على معرف الحوالة
        remittance_id = self.remittances_table.item(current_row, 0).data(Qt.UserRole)
        if not remittance_id:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على معرف الحوالة")
            return

        self.load_remittance_to_form(remittance_id)

    def load_remittance_to_form(self, remittance_id):
        """تحميل بيانات الحوالة إلى النموذج"""
        try:
            session = self.db_manager.get_session()
            remittance = session.query(Remittance).get(remittance_id)

            if not remittance:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الحوالة")
                session.close()
                return

            # تحميل البيانات إلى النموذج
            self.number_edit.setText(remittance.remittance_number or "")

            # تعيين المورد
            if remittance.supplier_id:
                for i in range(self.supplier_combo.count()):
                    if self.supplier_combo.itemData(i) == remittance.supplier_id:
                        self.supplier_combo.setCurrentIndex(i)
                        break

            # تعيين العملة
            if remittance.currency_id:
                for i in range(self.currency_combo.count()):
                    if self.currency_combo.itemData(i) == remittance.currency_id:
                        self.currency_combo.setCurrentIndex(i)
                        break

            # تعيين المبلغ وسعر الصرف
            self.amount_edit.set_amount(float(remittance.amount or 0))
            self.exchange_rate_edit.set_rate(float(remittance.exchange_rate or 1))

            # تعيين التاريخ
            if remittance.remittance_date:
                self.date_edit.setDate(QDate.fromString(remittance.remittance_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

            # تعيين البنوك والحسابات
            if remittance.sender_bank_id:
                for i in range(self.sender_bank_combo.count()):
                    if self.sender_bank_combo.itemData(i) == remittance.sender_bank_id:
                        self.sender_bank_combo.setCurrentIndex(i)
                        break

            if remittance.receiver_bank_id:
                for i in range(self.receiver_bank_combo.count()):
                    if self.receiver_bank_combo.itemData(i) == remittance.receiver_bank_id:
                        self.receiver_bank_combo.setCurrentIndex(i)
                        break

            # تحديث الحسابات
            self.update_sender_accounts()
            self.update_receiver_accounts()

            # تعيين الحسابات
            if remittance.sender_account_id:
                for i in range(self.sender_account_combo.count()):
                    if self.sender_account_combo.itemData(i) == remittance.sender_account_id:
                        self.sender_account_combo.setCurrentIndex(i)
                        break

            if remittance.receiver_account_id:
                for i in range(self.receiver_account_combo.count()):
                    if self.receiver_account_combo.itemData(i) == remittance.receiver_account_id:
                        self.receiver_account_combo.setCurrentIndex(i)
                        break

            # تعيين الغرض والملاحظات
            self.purpose_edit.setText(remittance.purpose or "")
            self.notes_edit.setPlainText(remittance.notes or "")

            # تعيين الحالة
            if remittance.status:
                self.status_combo.setCurrentText(remittance.status.value)

            # حفظ معرف الحوالة الحالية
            self.current_remittance = remittance_id

            # تفعيل النموذج للتحرير
            self.set_form_enabled(True)

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الحوالة:\n{str(e)}")

    def delete_remittance(self):
        """حذف الحوالة المحددة"""
        current_row = self.remittances_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يجب اختيار حوالة للحذف")
            return

        # الحصول على معرف الحوالة
        remittance_id = self.remittances_table.item(current_row, 0).data(Qt.UserRole)
        remittance_number = self.remittances_table.item(current_row, 0).text()

        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الحوالة رقم {remittance_number}؟\n"
            "هذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        try:
            session = self.db_manager.get_session()
            remittance = session.query(Remittance).get(remittance_id)

            if remittance:
                # التحقق من إمكانية الحذف
                if remittance.status in [RemittanceStatus.SENT, RemittanceStatus.RECEIVED, RemittanceStatus.COMPLETED]:
                    QMessageBox.warning(
                        self,
                        "تحذير",
                        "لا يمكن حذف الحوالة في هذه الحالة.\n"
                        "يمكن فقط حذف الحوالات في حالة مسودة أو في الانتظار."
                    )
                    session.close()
                    return

                session.delete(remittance)
                session.commit()

                # إرسال إشارة
                self.remittance_deleted.emit(remittance_number)

                QMessageBox.information(self, "نجح", "تم حذف الحوالة بنجاح")

                # تحديث الجدول
                self.load_remittances()

                # مسح النموذج
                self.clear_form()

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الحوالة:\n{str(e)}")

    def approve_remittance(self):
        """اعتماد الحوالة المحددة"""
        current_row = self.remittances_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يجب اختيار حوالة للاعتماد")
            return

        # الحصول على معرف الحوالة
        remittance_id = self.remittances_table.item(current_row, 0).data(Qt.UserRole)
        remittance_number = self.remittances_table.item(current_row, 0).text()

        try:
            session = self.db_manager.get_session()
            remittance = session.query(Remittance).get(remittance_id)

            if remittance:
                # التحقق من الحالة الحالية
                if remittance.status != RemittanceStatus.PENDING:
                    QMessageBox.warning(
                        self,
                        "تحذير",
                        "يمكن فقط اعتماد الحوالات التي في حالة 'في الانتظار'"
                    )
                    session.close()
                    return

                # تحديث الحالة
                remittance.status = RemittanceStatus.APPROVED
                remittance.approved_by = 1  # يجب الحصول على معرف المستخدم الحالي
                from datetime import datetime
                remittance.approved_at = datetime.now()

                session.commit()

                QMessageBox.information(self, "نجح", f"تم اعتماد الحوالة رقم {remittance_number} بنجاح")

                # تحديث الجدول
                self.load_remittances()

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء اعتماد الحوالة:\n{str(e)}")

    def clear_form(self):
        """مسح النموذج"""
        self.number_edit.clear()
        self.supplier_combo.setCurrentIndex(0)
        self.currency_combo.setCurrentIndex(0)
        self.amount_edit.clear()
        self.exchange_rate_edit.set_rate(1.0)
        self.date_edit.setDate(QDate.currentDate())
        self.sender_bank_combo.setCurrentIndex(0)
        self.sender_account_combo.setCurrentIndex(0)
        self.receiver_bank_combo.setCurrentIndex(0)
        self.receiver_account_combo.setCurrentIndex(0)
        self.purpose_edit.clear()
        self.notes_edit.clear()
        self.status_combo.setCurrentText(RemittanceStatus.DRAFT.value)

        self.current_remittance = None
        self.set_form_enabled(False)

    def set_form_enabled(self, enabled):
        """تفعيل أو تعطيل النموذج"""
        # تفعيل/تعطيل الحقول
        controls = [
            self.supplier_combo, self.currency_combo, self.amount_edit,
            self.exchange_rate_edit, self.date_edit, self.sender_bank_combo,
            self.sender_account_combo, self.receiver_bank_combo,
            self.receiver_account_combo, self.purpose_edit, self.notes_edit,
            self.status_combo, self.search_supplier_btn
        ]

        for control in controls:
            control.setEnabled(enabled)

        # تفعيل/تعطيل الأزرار
        self.save_btn.setEnabled(enabled)

    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.remittances_table.currentRow()
        has_selection = current_row >= 0

        # تفعيل/تعطيل الأزرار حسب التحديد
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.approve_btn.setEnabled(has_selection)

        if has_selection:
            # التحقق من حالة الحوالة لتحديد إمكانية الاعتماد
            status_item = self.remittances_table.item(current_row, 5)
            if status_item:
                status_text = status_item.text()
                self.approve_btn.setEnabled(status_text == RemittanceStatus.PENDING.value)

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
