#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة المزيد من وحدات القياس
Add More Units of Measure
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def add_units():
    """إضافة وحدات القياس الأساسية"""
    
    print("📦 إضافة وحدات القياس الأساسية...")
    
    try:
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        from src.database.models import UnitOfMeasure
        
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        # قائمة وحدات القياس الأساسية
        units_data = [
            # وحدات الوزن
            ('كيلوجرام', 'Kilogram', 'كجم', 'kg', 'وحدة قياس الوزن الأساسية'),
            ('جرام', 'Gram', 'جم', 'g', 'وحدة قياس الوزن الصغيرة'),
            ('طن', 'Ton', 'طن', 't', 'وحدة قياس الوزن الكبيرة'),
            
            # وحدات الطول
            ('متر', 'Meter', 'م', 'm', 'وحدة قياس الطول الأساسية'),
            ('سنتيمتر', 'Centimeter', 'سم', 'cm', 'وحدة قياس الطول الصغيرة'),
            ('مليمتر', 'Millimeter', 'مم', 'mm', 'وحدة قياس الطول الدقيقة'),
            ('كيلومتر', 'Kilometer', 'كم', 'km', 'وحدة قياس الطول الكبيرة'),
            
            # وحدات المساحة والحجم
            ('متر مربع', 'Square Meter', 'م²', 'm²', 'وحدة قياس المساحة'),
            ('متر مكعب', 'Cubic Meter', 'م³', 'm³', 'وحدة قياس الحجم'),
            ('لتر', 'Liter', 'لتر', 'L', 'وحدة قياس السوائل'),
            ('مليلتر', 'Milliliter', 'مل', 'ml', 'وحدة قياس السوائل الصغيرة'),
            
            # وحدات العد والتعبئة
            ('عبوة', 'Package', 'عبوة', 'pkg', 'وحدة التعبئة'),
            ('صندوق', 'Box', 'صندوق', 'box', 'وحدة التعبئة الكبيرة'),
            ('كرتون', 'Carton', 'كرتون', 'ctn', 'وحدة التعبئة المتوسطة'),
            ('زوج', 'Pair', 'زوج', 'pair', 'وحدة للأشياء المزدوجة'),
            ('دستة', 'Dozen', 'دستة', 'dz', '12 قطعة'),
            ('مجموعة', 'Set', 'مجموعة', 'set', 'مجموعة من القطع'),
            
            # وحدات الوقت
            ('ثانية', 'Second', 'ث', 's', 'وحدة قياس الوقت الأساسية'),
            ('دقيقة', 'Minute', 'د', 'min', 'وحدة قياس الوقت'),
            ('ساعة', 'Hour', 'س', 'h', 'وحدة قياس الوقت'),
            ('يوم', 'Day', 'يوم', 'day', 'وحدة قياس الوقت'),
            
            # وحدات الطاقة والقوة
            ('واط', 'Watt', 'واط', 'W', 'وحدة قياس القدرة'),
            ('كيلوواط', 'Kilowatt', 'ك.واط', 'kW', 'وحدة قياس القدرة الكبيرة'),
            ('حصان', 'Horsepower', 'حصان', 'hp', 'وحدة قياس القدرة'),
            
            # وحدات أخرى
            ('درجة مئوية', 'Celsius', '°م', '°C', 'وحدة قياس درجة الحرارة'),
            ('بوصة', 'Inch', 'بوصة', 'in', 'وحدة قياس الطول الإنجليزية'),
            ('قدم', 'Foot', 'قدم', 'ft', 'وحدة قياس الطول الإنجليزية'),
            ('ياردة', 'Yard', 'ياردة', 'yd', 'وحدة قياس الطول الإنجليزية'),
            ('رطل', 'Pound', 'رطل', 'lb', 'وحدة قياس الوزن الإنجليزية'),
        ]
        
        with db_manager.get_session() as session:
            added_count = 0
            existing_count = 0
            
            for name, name_en, symbol, symbol_en, description in units_data:
                # التحقق من عدم وجود الوحدة مسبقاً
                existing = session.query(UnitOfMeasure).filter_by(name=name).first()
                
                if not existing:
                    new_unit = UnitOfMeasure(
                        name=name,
                        name_en=name_en,
                        symbol=symbol,
                        symbol_en=symbol_en,
                        description=description,
                        is_active=True
                    )
                    
                    session.add(new_unit)
                    added_count += 1
                    print(f"   ✅ تم إضافة: {name}")
                else:
                    existing_count += 1
                    print(f"   ⚠️ موجود مسبقاً: {name}")
            
            print(f"\n📊 النتائج:")
            print(f"   وحدات جديدة: {added_count}")
            print(f"   وحدات موجودة: {existing_count}")
            
            # عرض إجمالي الوحدات
            total_units = session.query(UnitOfMeasure).count()
            print(f"   إجمالي الوحدات: {total_units}")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إضافة الوحدات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("📦 إضافة وحدات القياس الأساسية")
    print("=" * 40)
    
    if add_units():
        print("\n✅ تم إضافة وحدات القياس بنجاح!")
        print("💡 يمكنك الآن استخدام هذه الوحدات في التطبيق")
        print("📋 للوصول لإدارة وحدات القياس:")
        print("   1. افتح التطبيق")
        print("   2. اذهب إلى إدارة الأصناف")
        print("   3. اختر وحدات القياس")
    else:
        print("\n❌ فشل في إضافة وحدات القياس")

if __name__ == "__main__":
    main()
