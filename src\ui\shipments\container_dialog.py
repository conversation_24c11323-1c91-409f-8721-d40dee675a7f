# -*- coding: utf-8 -*-
"""
نافذة إضافة/تعديل الحاوية
Container Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QComboBox, QTextEdit,
                               QMessageBox, QDoubleSpinBox)
from PySide6.QtCore import Qt

class ContainerDialog(QDialog):
    """نافذة إضافة/تعديل الحاوية"""
    
    def __init__(self, parent=None, container_data=None):
        super().__init__(parent)
        self.container_data = container_data
        self.is_edit_mode = container_data is not None
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_container_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل حاوية" if self.is_edit_mode else "إضافة حاوية جديدة"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 400)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # رقم الحاوية
        self.container_number_edit = QLineEdit()
        self.container_number_edit.setPlaceholderText("مثال: MSKU1234567")
        form_layout.addRow("رقم الحاوية:", self.container_number_edit)
        
        # نوع الحاوية
        self.container_type_combo = QComboBox()
        self.container_type_combo.addItems([
            "20' Standard", "40' Standard", "40' High Cube", "45' High Cube",
            "20' Open Top", "40' Open Top", "20' Flat Rack", "40' Flat Rack",
            "20' Refrigerated", "40' Refrigerated", "Tank Container", "أخرى"
        ])
        form_layout.addRow("نوع الحاوية:", self.container_type_combo)
        
        # الحجم
        self.size_combo = QComboBox()
        self.size_combo.addItems([
            "20 قدم", "40 قدم", "45 قدم"
        ])
        form_layout.addRow("الحجم:", self.size_combo)
        
        # الوزن الفارغ
        self.empty_weight_spin = QDoubleSpinBox()
        self.empty_weight_spin.setRange(0.00, 99999.99)
        self.empty_weight_spin.setDecimals(2)
        self.empty_weight_spin.setSuffix(" كجم")
        self.empty_weight_spin.setValue(2300.00)  # وزن افتراضي لحاوية 20 قدم
        form_layout.addRow("الوزن الفارغ:", self.empty_weight_spin)
        
        # الوزن المحمل
        self.loaded_weight_spin = QDoubleSpinBox()
        self.loaded_weight_spin.setRange(0.00, 99999.99)
        self.loaded_weight_spin.setDecimals(2)
        self.loaded_weight_spin.setSuffix(" كجم")
        form_layout.addRow("الوزن المحمل:", self.loaded_weight_spin)
        
        # الحد الأقصى للوزن
        self.max_weight_spin = QDoubleSpinBox()
        self.max_weight_spin.setRange(0.00, 99999.99)
        self.max_weight_spin.setDecimals(2)
        self.max_weight_spin.setSuffix(" كجم")
        self.max_weight_spin.setValue(24000.00)  # حد أقصى افتراضي
        form_layout.addRow("الحد الأقصى للوزن:", self.max_weight_spin)
        
        # حالة الحاوية
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "فارغة", "محملة", "في الطريق", "وصلت", "تم التفريغ", "تحت الصيانة"
        ])
        form_layout.addRow("حالة الحاوية:", self.status_combo)
        
        # رقم الختم
        self.seal_number_edit = QLineEdit()
        self.seal_number_edit.setPlaceholderText("رقم ختم الحاوية...")
        form_layout.addRow("رقم الختم:", self.seal_number_edit)
        
        # درجة الحرارة (للحاويات المبردة)
        self.temperature_spin = QDoubleSpinBox()
        self.temperature_spin.setRange(-50.0, 50.0)
        self.temperature_spin.setDecimals(1)
        self.temperature_spin.setSuffix(" °C")
        self.temperature_spin.setValue(0.0)
        form_layout.addRow("درجة الحرارة:", self.temperature_spin)
        
        main_layout.addLayout(form_layout)
        
        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        main_layout.addWidget(notes_label)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات خاصة بالحاوية...")
        main_layout.addWidget(self.notes_edit)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(buttons_layout)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_button.clicked.connect(self.save_container)
        self.cancel_button.clicked.connect(self.reject)
        
        # تحديث الحد الأقصى للوزن عند تغيير نوع الحاوية
        self.container_type_combo.currentTextChanged.connect(self.update_max_weight)
        self.size_combo.currentTextChanged.connect(self.update_max_weight)
        
    def update_max_weight(self):
        """تحديث الحد الأقصى للوزن حسب نوع الحاوية"""
        container_type = self.container_type_combo.currentText()
        size = self.size_combo.currentText()
        
        # أوزان افتراضية حسب النوع والحجم
        if "20'" in container_type or "20 قدم" in size:
            self.empty_weight_spin.setValue(2300.00)
            self.max_weight_spin.setValue(24000.00)
        elif "40'" in container_type or "40 قدم" in size:
            self.empty_weight_spin.setValue(3800.00)
            self.max_weight_spin.setValue(30480.00)
        elif "45'" in container_type or "45 قدم" in size:
            self.empty_weight_spin.setValue(4200.00)
            self.max_weight_spin.setValue(32500.00)
            
    def load_container_data(self):
        """تحميل بيانات الحاوية للتعديل"""
        if self.container_data:
            self.container_number_edit.setText(self.container_data.get('container_number', ''))
            
            # تحديد نوع الحاوية
            container_type = self.container_data.get('container_type', '')
            index = self.container_type_combo.findText(container_type)
            if index >= 0:
                self.container_type_combo.setCurrentIndex(index)
                
            # تحديد الحجم
            size = self.container_data.get('size', '')
            index = self.size_combo.findText(size)
            if index >= 0:
                self.size_combo.setCurrentIndex(index)
                
            self.empty_weight_spin.setValue(self.container_data.get('empty_weight', 0.0))
            self.loaded_weight_spin.setValue(self.container_data.get('loaded_weight', 0.0))
            self.max_weight_spin.setValue(self.container_data.get('max_weight', 0.0))
            
            # تحديد الحالة
            status = self.container_data.get('status', '')
            index = self.status_combo.findText(status)
            if index >= 0:
                self.status_combo.setCurrentIndex(index)
                
            self.seal_number_edit.setText(self.container_data.get('seal_number', ''))
            self.temperature_spin.setValue(self.container_data.get('temperature', 0.0))
            self.notes_edit.setPlainText(self.container_data.get('notes', ''))
            
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.container_number_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يجب إدخال رقم الحاوية")
            self.container_number_edit.setFocus()
            return False
            
        if self.loaded_weight_spin.value() > self.max_weight_spin.value():
            QMessageBox.warning(self, "خطأ", "الوزن المحمل لا يمكن أن يتجاوز الحد الأقصى للوزن")
            self.loaded_weight_spin.setFocus()
            return False
            
        return True
        
    def save_container(self):
        """حفظ بيانات الحاوية"""
        if not self.validate_data():
            return
            
        # إنشاء قاموس البيانات
        self.container_data = {
            'container_number': self.container_number_edit.text().strip(),
            'container_type': self.container_type_combo.currentText(),
            'size': self.size_combo.currentText(),
            'empty_weight': self.empty_weight_spin.value(),
            'loaded_weight': self.loaded_weight_spin.value(),
            'max_weight': self.max_weight_spin.value(),
            'status': self.status_combo.currentText(),
            'seal_number': self.seal_number_edit.text().strip(),
            'temperature': self.temperature_spin.value(),
            'notes': self.notes_edit.toPlainText().strip()
        }
        
        self.accept()
        
    def get_container_data(self):
        """الحصول على بيانات الحاوية"""
        return self.container_data
