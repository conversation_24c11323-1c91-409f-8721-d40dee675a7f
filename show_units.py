#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض وحدات القياس
Show Units of Measure
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def show_units():
    """عرض وحدات القياس"""
    
    try:
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        from src.database.models import UnitOfMeasure
        
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        with db_manager.get_session() as session:
            units = session.query(UnitOfMeasure).order_by(UnitOfMeasure.name).all()
            
            print(f"📊 إجمالي وحدات القياس: {len(units)}")
            print("=" * 50)
            
            if units:
                print("قائمة الوحدات:")
                for i, unit in enumerate(units, 1):
                    status = "✅ نشطة" if unit.is_active else "❌ غير نشطة"
                    print(f"{i:2d}. {unit.name} ({unit.symbol}) - {unit.name_en} - {status}")
                    if unit.description:
                        print(f"    الوصف: {unit.description}")
                    print()
            else:
                print("❌ لا توجد وحدات قياس")
                
            return True
            
    except Exception as e:
        print(f"❌ خطأ في عرض الوحدات: {e}")
        return False

if __name__ == "__main__":
    show_units()
