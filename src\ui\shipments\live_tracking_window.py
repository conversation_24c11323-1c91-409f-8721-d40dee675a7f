"""
نافذة التتبع المباشر للشحنات
تتيح تتبع الشحنات في الوقت الفعلي مع خرائط تفاعلية وإحصائيات فورية
"""

import sys
import json
from datetime import datetime, timed<PERSON>ta
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QFrame,
    QSplitter, QGroupBox, QProgressBar, QTextEdit, QComboBox,
    QLineEdit, QDateEdit, QTimeEdit, QCheckBox, QSpinBox,
    QScrollArea, QTabWidget, QStatusBar, QHeaderView, QApplication,
    QMessageBox, QDialog, QDialogButtonBox, QFormLayout, QSlider
)
from PySide6.QtCore import Qt, Q<PERSON>imer, QThread, Signal, QDate, QTime, QSize
from PySide6.QtGui import QFont, QPainter, QPen, QBrush, QColor, QPixmap, QIcon

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, Supplier, Container, ShipmentItem
from src.ui.styles.style_manager import StyleManager

style_manager = StyleManager()


class LiveTrackingMapWidget(QWidget):
    """ويدجت الخريطة التفاعلية للتتبع المباشر"""
    
    shipment_selected = Signal(int)  # إشارة اختيار شحنة
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(800, 600)
        self.shipments_data = []
        self.selected_shipment_id = None
        self.zoom_level = 1.0
        self.pan_x = 0
        self.pan_y = 0
        self.mouse_pressed = False
        self.last_mouse_pos = None
        
        # إعداد الألوان والأنماط
        self.setup_colors()
        
        # تحديث الخريطة كل 30 ثانية
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_map_data)
        self.update_timer.start(30000)  # 30 ثانية
        
    def setup_colors(self):
        """إعداد الألوان المختلفة لحالات الشحنات"""
        self.status_colors = {
            'في الطريق': QColor(52, 152, 219),      # أزرق
            'وصلت': QColor(46, 204, 113),           # أخضر
            'متأخرة': QColor(231, 76, 60),          # أحمر
            'في الميناء': QColor(241, 196, 15),     # أصفر
            'تم التسليم': QColor(155, 89, 182),     # بنفسجي
            'ملغاة': QColor(149, 165, 166),         # رمادي
            'جديدة': QColor(26, 188, 156)           # تركوازي
        }
        
    def set_shipments_data(self, shipments):
        """تعيين بيانات الشحنات"""
        self.shipments_data = shipments
        self.update()
        
    def paintEvent(self, event):
        """رسم الخريطة والشحنات"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم خلفية الخريطة
        self.draw_map_background(painter)
        
        # رسم الشحنات
        self.draw_shipments(painter)
        
        # رسم معلومات الشحنة المحددة
        if self.selected_shipment_id:
            self.draw_selected_shipment_info(painter)
            
    def draw_map_background(self, painter):
        """رسم خلفية الخريطة"""
        # خلفية زرقاء فاتحة تمثل المحيط
        painter.fillRect(self.rect(), QColor(173, 216, 230))
        
        # رسم خطوط الشبكة
        painter.setPen(QPen(QColor(200, 200, 200), 1, Qt.DashLine))
        
        # خطوط عمودية
        for x in range(0, self.width(), 50):
            painter.drawLine(x, 0, x, self.height())
            
        # خطوط أفقية
        for y in range(0, self.height(), 50):
            painter.drawLine(0, y, self.width(), y)
            
        # رسم القارات بشكل مبسط
        self.draw_continents(painter)
        
    def draw_continents(self, painter):
        """رسم القارات بشكل مبسط"""
        painter.setBrush(QBrush(QColor(139, 69, 19)))  # بني للأرض
        painter.setPen(QPen(QColor(101, 67, 33), 2))
        
        # قارة آسيا (مبسطة)
        asia_points = [
            (self.width() * 0.6, self.height() * 0.2),
            (self.width() * 0.9, self.height() * 0.2),
            (self.width() * 0.9, self.height() * 0.6),
            (self.width() * 0.6, self.height() * 0.6)
        ]
        painter.drawPolygon(asia_points)
        
        # قارة أوروبا (مبسطة)
        europe_points = [
            (self.width() * 0.4, self.height() * 0.15),
            (self.width() * 0.6, self.height() * 0.15),
            (self.width() * 0.6, self.height() * 0.4),
            (self.width() * 0.4, self.height() * 0.4)
        ]
        painter.drawPolygon(europe_points)
        
    def draw_shipments(self, painter):
        """رسم الشحنات على الخريطة"""
        if not self.shipments_data:
            return
            
        for i, shipment in enumerate(self.shipments_data):
            # حساب موقع الشحنة على الخريطة
            x, y = self.calculate_shipment_position(shipment, i)
            
            # اختيار اللون حسب الحالة
            status = getattr(shipment, 'shipment_status', 'جديدة')
            color = self.status_colors.get(status, QColor(100, 100, 100))
            
            # رسم دائرة الشحنة
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(Qt.white, 2))
            
            radius = 8 if shipment.id != self.selected_shipment_id else 12
            painter.drawEllipse(int(x - radius), int(y - radius), radius * 2, radius * 2)
            
            # رسم رقم الشحنة
            painter.setPen(QPen(Qt.black))
            painter.setFont(QFont("Arial", 8, QFont.Bold))
            painter.drawText(int(x + 15), int(y + 5), f"#{shipment.id}")
            
    def calculate_shipment_position(self, shipment, index):
        """حساب موقع الشحنة على الخريطة"""
        # توزيع الشحنات بشكل عشوائي ولكن ثابت
        import random
        random.seed(shipment.id)  # استخدام ID كبذرة للحصول على موقع ثابت
        
        x = random.randint(50, self.width() - 50)
        y = random.randint(50, self.height() - 50)
        
        return x, y
        
    def draw_selected_shipment_info(self, painter):
        """رسم معلومات الشحنة المحددة"""
        selected_shipment = None
        for shipment in self.shipments_data:
            if shipment.id == self.selected_shipment_id:
                selected_shipment = shipment
                break
                
        if not selected_shipment:
            return
            
        # رسم مربع المعلومات
        info_rect = self.rect().adjusted(10, 10, -10, -self.height() + 120)
        painter.fillRect(info_rect, QColor(255, 255, 255, 230))
        painter.setPen(QPen(Qt.black, 1))
        painter.drawRect(info_rect)
        
        # كتابة المعلومات
        painter.setPen(QPen(Qt.black))
        painter.setFont(QFont("Arial", 10, QFont.Bold))
        
        y_offset = 25
        painter.drawText(20, y_offset, f"شحنة رقم: {selected_shipment.id}")
        y_offset += 20
        painter.drawText(20, y_offset, f"الحالة: {getattr(selected_shipment, 'shipment_status', 'غير محدد')}")
        y_offset += 20
        painter.drawText(20, y_offset, f"المورد: {selected_shipment.supplier.name if selected_shipment.supplier else 'غير محدد'}")
        y_offset += 20
        painter.drawText(20, y_offset, f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")
        
    def mousePressEvent(self, event):
        """معالجة النقر على الخريطة"""
        if event.button() == Qt.LeftButton:
            # البحث عن شحنة في موقع النقر
            clicked_shipment = self.find_shipment_at_position(event.position().x(), event.position().y())
            if clicked_shipment:
                self.selected_shipment_id = clicked_shipment.id
                self.shipment_selected.emit(clicked_shipment.id)
                self.update()
                
    def find_shipment_at_position(self, x, y):
        """البحث عن شحنة في موقع معين"""
        for i, shipment in enumerate(self.shipments_data):
            ship_x, ship_y = self.calculate_shipment_position(shipment, i)
            distance = ((x - ship_x) ** 2 + (y - ship_y) ** 2) ** 0.5
            if distance <= 15:  # نصف قطر النقر
                return shipment
        return None
        
    def update_map_data(self):
        """تحديث بيانات الخريطة"""
        # هنا يمكن إضافة منطق تحديث البيانات من قاعدة البيانات
        self.update()


class LiveTrackingWindow(QMainWindow):
    """نافذة التتبع المباشر للشحنات"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setWindowTitle("🔴 التتبع المباشر للشحنات")
        self.setMinimumSize(1400, 900)
        
        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_status_bar()
        
        # تحميل البيانات الأولية
        self.load_live_data()
        
        # إعداد التحديث التلقائي
        self.setup_auto_refresh()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط العنوان والأدوات
        self.create_header_section(main_layout)
        
        # المحتوى الرئيسي
        self.create_main_content(main_layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان والأدوات"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e74c3c, stop:1 #c0392b);
                border-radius: 8px;
                padding: 10px;
            }
        """)
        header_layout = QHBoxLayout(header_frame)
        
        # عنوان النافذة
        title_label = QLabel("🔴 التتبع المباشر للشحنات")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
            }
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # أزرار التحكم
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.clicked.connect(self.refresh_data)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 2px solid white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """)
        header_layout.addWidget(self.refresh_btn)

        # زر إعدادات التحديث
        self.settings_btn = QPushButton("⚙️ إعدادات")
        self.settings_btn.clicked.connect(self.show_refresh_settings)
        self.settings_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 2px solid white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """)
        header_layout.addWidget(self.settings_btn)

        # زر ملء الشاشة
        self.fullscreen_btn = QPushButton("🔳 ملء الشاشة")
        self.fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        self.fullscreen_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 2px solid white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """)
        header_layout.addWidget(self.fullscreen_btn)

        # زر التصدير
        self.export_btn = QPushButton("📊 تصدير")
        self.export_btn.clicked.connect(self.export_tracking_data)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 2px solid white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """)
        header_layout.addWidget(self.export_btn)

        # مؤشر التحديث التلقائي
        self.auto_refresh_label = QLabel("🟢 تحديث تلقائي")
        self.auto_refresh_label.setStyleSheet("""
            QLabel {
                color: white;
                font-weight: bold;
                background: transparent;
                padding: 8px;
            }
        """)
        header_layout.addWidget(self.auto_refresh_label)
        
        layout.addWidget(header_frame)

    def create_main_content(self, layout):
        """إنشاء المحتوى الرئيسي"""
        # إنشاء QSplitter للتقسيم الأفقي
        main_splitter = QSplitter(Qt.Horizontal)

        # الجانب الأيسر - الخريطة والإحصائيات
        left_widget = self.create_left_panel()
        main_splitter.addWidget(left_widget)

        # الجانب الأيمن - قائمة الشحنات والتفاصيل
        right_widget = self.create_right_panel()
        main_splitter.addWidget(right_widget)

        # تعيين النسب (70% للخريطة، 30% للقائمة)
        main_splitter.setSizes([1000, 400])

        layout.addWidget(main_splitter)

    def create_left_panel(self):
        """إنشاء اللوحة اليسرى - الخريطة والإحصائيات"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(10)

        # قسم الإحصائيات السريعة
        stats_frame = self.create_quick_stats()
        left_layout.addWidget(stats_frame)

        # الخريطة التفاعلية
        map_group = QGroupBox("🗺️ الخريطة التفاعلية")
        map_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        map_layout = QVBoxLayout(map_group)

        # ويدجت الخريطة
        self.map_widget = LiveTrackingMapWidget()
        self.map_widget.shipment_selected.connect(self.on_shipment_selected)
        map_layout.addWidget(self.map_widget)

        left_layout.addWidget(map_group)

        return left_widget

    def create_quick_stats(self):
        """إنشاء قسم الإحصائيات السريعة"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        stats_layout = QGridLayout(stats_frame)

        # بيانات الإحصائيات
        self.stats_data = [
            ("📦", "إجمالي الشحنات", "0", "#3498db"),
            ("🚢", "في الطريق", "0", "#f39c12"),
            ("✅", "وصلت", "0", "#27ae60"),
            ("⚠️", "متأخرة", "0", "#e74c3c"),
            ("🏭", "في الميناء", "0", "#9b59b6"),
            ("📍", "تم التسليم", "0", "#1abc9c")
        ]

        self.stats_labels = {}

        for i, (icon, title, value, color) in enumerate(self.stats_data):
            row, col = divmod(i, 3)

            stat_widget = self.create_stat_widget(icon, title, value, color)
            stats_layout.addWidget(stat_widget, row, col)

            # حفظ مرجع للتحديث لاحقاً
            self.stats_labels[title] = stat_widget.findChild(QLabel, "value_label")

        return stats_frame

    def create_stat_widget(self, icon, title, value, color):
        """إنشاء ويدجت إحصائية واحدة"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:1 {self.darken_color(color)});
                border-radius: 8px;
                padding: 15px;
            }}
        """)
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                color: white;
                background: transparent;
            }
        """)
        layout.addWidget(icon_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setObjectName("value_label")
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                background: transparent;
            }
        """)
        layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: white;
                background: transparent;
            }
        """)
        layout.addWidget(title_label)

        return widget

    def darken_color(self, color_hex):
        """تغميق اللون للتدرج"""
        color = QColor(color_hex)
        return color.darker(120).name()

    def create_right_panel(self):
        """إنشاء اللوحة اليمنى - قائمة الشحنات والتفاصيل"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setSpacing(10)

        # قسم البحث والفلترة
        search_frame = self.create_search_section()
        right_layout.addWidget(search_frame)

        # تبويبات المحتوى
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                border-radius: 4px;
            }
            QTabBar::tab {
                background: #f0f0f0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background: #3498db;
                color: white;
            }
        """)

        # تبويب الشحنات النشطة
        active_tab = self.create_active_shipments_tab()
        tabs.addTab(active_tab, "🚢 الشحنات النشطة")

        # تبويب التنبيهات
        alerts_tab = self.create_alerts_tab()
        tabs.addTab(alerts_tab, "🔔 التنبيهات")

        # تبويب التفاصيل
        details_tab = self.create_details_tab()
        tabs.addTab(details_tab, "📋 التفاصيل")

        right_layout.addWidget(tabs)

        return right_widget

    def create_search_section(self):
        """إنشاء قسم البحث والفلترة"""
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        search_layout = QGridLayout(search_frame)

        # حقل البحث
        search_label = QLabel("🔍 البحث:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث برقم الشحنة أو اسم المورد...")
        self.search_input.textChanged.connect(self.filter_shipments)

        # فلتر الحالة
        status_label = QLabel("📊 الحالة:")
        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "جميع الحالات", "في الطريق", "وصلت", "متأخرة",
            "في الميناء", "تم التسليم", "ملغاة", "جديدة"
        ])
        self.status_filter.currentTextChanged.connect(self.filter_shipments)

        # ترتيب العناصر
        search_layout.addWidget(search_label, 0, 0)
        search_layout.addWidget(self.search_input, 0, 1)
        search_layout.addWidget(status_label, 1, 0)
        search_layout.addWidget(self.status_filter, 1, 1)

        return search_frame

    def create_active_shipments_tab(self):
        """إنشاء تبويب الشحنات النشطة"""
        tab_widget = QWidget()
        tab_layout = QVBoxLayout(tab_widget)

        # جدول الشحنات
        self.shipments_table = QTableWidget()
        self.shipments_table.setColumnCount(6)
        self.shipments_table.setHorizontalHeaderLabels([
            "رقم الشحنة", "المورد", "الحالة", "ميناء المغادرة",
            "ميناء الوصول", "آخر تحديث"
        ])

        # تنسيق الجدول
        self.shipments_table.horizontalHeader().setStretchLastSection(True)
        self.shipments_table.setAlternatingRowColors(True)
        self.shipments_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.shipments_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        # ربط إشارة التحديد
        self.shipments_table.itemSelectionChanged.connect(self.on_table_selection_changed)

        tab_layout.addWidget(self.shipments_table)

        return tab_widget

    def create_alerts_tab(self):
        """إنشاء تبويب التنبيهات"""
        tab_widget = QWidget()
        tab_layout = QVBoxLayout(tab_widget)

        # قائمة التنبيهات
        self.alerts_list = QTextEdit()
        self.alerts_list.setReadOnly(True)
        self.alerts_list.setStyleSheet("""
            QTextEdit {
                background-color: #fff5f5;
                border: 1px solid #fed7d7;
                border-radius: 6px;
                padding: 10px;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)

        tab_layout.addWidget(self.alerts_list)

        return tab_widget

    def create_details_tab(self):
        """إنشاء تبويب التفاصيل"""
        tab_widget = QWidget()
        tab_layout = QVBoxLayout(tab_widget)

        # منطقة التفاصيل
        self.details_area = QScrollArea()
        self.details_area.setWidgetResizable(True)
        self.details_area.setStyleSheet("""
            QScrollArea {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 6px;
            }
        """)

        # ويدجت التفاصيل
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)

        # رسالة افتراضية
        default_label = QLabel("اختر شحنة لعرض التفاصيل")
        default_label.setAlignment(Qt.AlignCenter)
        default_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 16px;
                padding: 50px;
            }
        """)
        self.details_layout.addWidget(default_label)

        self.details_area.setWidget(self.details_widget)
        tab_layout.addWidget(self.details_area)

        return tab_widget

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # معلومات الحالة
        self.status_label = QLabel("جاري التحميل...")
        self.status_bar.addWidget(self.status_label)

        self.status_bar.addPermanentWidget(QLabel(""))

        # مؤشر آخر تحديث
        self.last_update_label = QLabel("آخر تحديث: --")
        self.status_bar.addPermanentWidget(self.last_update_label)

    def setup_auto_refresh(self):
        """إعداد التحديث التلقائي"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(60000)  # تحديث كل دقيقة

        # مؤقت للتحقق من التنبيهات الجديدة
        self.alerts_timer = QTimer()
        self.alerts_timer.timeout.connect(self.check_new_alerts)
        self.alerts_timer.start(30000)  # فحص التنبيهات كل 30 ثانية

        # متغيرات لتتبع التنبيهات السابقة
        self.previous_alerts = set()
        self.notification_enabled = True
        self.sound_enabled = True

    def load_live_data(self):
        """تحميل البيانات المباشرة"""
        try:
            session = self.db_manager.get_session()

            # جلب الشحنات النشطة
            shipments = session.query(Shipment).outerjoin(Supplier).filter(
                Shipment.is_active == True
            ).order_by(Shipment.created_at.desc()).all()

            # تحديث البيانات
            self.update_shipments_data(shipments)
            self.update_statistics(shipments)
            self.update_alerts(shipments)

            # تحديث شريط الحالة
            self.status_label.setText(f"تم تحميل {len(shipments)} شحنة نشطة")
            self.last_update_label.setText(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")

    def update_shipments_data(self, shipments):
        """تحديث بيانات الشحنات"""
        # تحديث الخريطة
        self.map_widget.set_shipments_data(shipments)

        # تحديث الجدول
        self.shipments_table.setRowCount(len(shipments))

        for row, shipment in enumerate(shipments):
            # رقم الشحنة
            self.shipments_table.setItem(row, 0, QTableWidgetItem(str(shipment.id)))

            # المورد
            supplier_name = shipment.supplier.name if shipment.supplier else "غير محدد"
            self.shipments_table.setItem(row, 1, QTableWidgetItem(supplier_name))

            # الحالة
            status = getattr(shipment, 'shipment_status', 'جديدة')
            status_item = QTableWidgetItem(status)

            # تلوين الحالة
            if status == "في الطريق":
                status_item.setBackground(QColor(52, 152, 219, 50))
            elif status == "وصلت":
                status_item.setBackground(QColor(46, 204, 113, 50))
            elif status == "متأخرة":
                status_item.setBackground(QColor(231, 76, 60, 50))
            elif status == "في الميناء":
                status_item.setBackground(QColor(241, 196, 15, 50))

            self.shipments_table.setItem(row, 2, status_item)

            # الموانئ
            self.shipments_table.setItem(row, 3, QTableWidgetItem(
                getattr(shipment, 'port_of_loading', 'غير محدد')
            ))
            self.shipments_table.setItem(row, 4, QTableWidgetItem(
                getattr(shipment, 'port_of_discharge', 'غير محدد')
            ))

            # آخر تحديث
            last_update = shipment.updated_at if shipment.updated_at else shipment.created_at
            self.shipments_table.setItem(row, 5, QTableWidgetItem(
                last_update.strftime('%Y-%m-%d %H:%M') if last_update else 'غير محدد'
            ))

    def update_statistics(self, shipments):
        """تحديث الإحصائيات"""
        # حساب الإحصائيات
        total_count = len(shipments)
        status_counts = {}

        for shipment in shipments:
            status = getattr(shipment, 'shipment_status', 'جديدة')
            status_counts[status] = status_counts.get(status, 0) + 1

        # تحديث التسميات
        if "إجمالي الشحنات" in self.stats_labels:
            self.stats_labels["إجمالي الشحنات"].setText(str(total_count))
        if "في الطريق" in self.stats_labels:
            self.stats_labels["في الطريق"].setText(str(status_counts.get("في الطريق", 0)))
        if "وصلت" in self.stats_labels:
            self.stats_labels["وصلت"].setText(str(status_counts.get("وصلت", 0)))
        if "متأخرة" in self.stats_labels:
            self.stats_labels["متأخرة"].setText(str(status_counts.get("متأخرة", 0)))
        if "في الميناء" in self.stats_labels:
            self.stats_labels["في الميناء"].setText(str(status_counts.get("في الميناء", 0)))
        if "تم التسليم" in self.stats_labels:
            self.stats_labels["تم التسليم"].setText(str(status_counts.get("تم التسليم", 0)))

    def update_alerts(self, shipments):
        """تحديث التنبيهات"""
        alerts_html = "<h3 style='color: #e74c3c;'>🔔 التنبيهات الحالية</h3>"
        alert_count = 0

        current_time = datetime.now()

        for shipment in shipments:
            # تحقق من التأخير
            if hasattr(shipment, 'estimated_arrival_date') and shipment.estimated_arrival_date:
                if current_time.date() > shipment.estimated_arrival_date and \
                   getattr(shipment, 'shipment_status', '') not in ['وصلت', 'تم التسليم']:
                    days_late = (current_time.date() - shipment.estimated_arrival_date).days
                    alerts_html += f"""
                    <div style='background-color: #ffebee; border-left: 4px solid #f44336; padding: 10px; margin: 5px 0;'>
                        <strong>⚠️ شحنة متأخرة #{shipment.id}</strong><br>
                        متأخرة بـ {days_late} يوم/أيام<br>
                        المورد: {shipment.supplier.name if shipment.supplier else 'غير محدد'}
                    </div>
                    """
                    alert_count += 1

            # تحقق من الشحنات بدون تتبع
            if not getattr(shipment, 'tracking_number', None):
                alerts_html += f"""
                <div style='background-color: #fff3e0; border-left: 4px solid #ff9800; padding: 10px; margin: 5px 0;'>
                    <strong>📋 شحنة بدون رقم تتبع #{shipment.id}</strong><br>
                    المورد: {shipment.supplier.name if shipment.supplier else 'غير محدد'}
                </div>
                """
                alert_count += 1

        if alert_count == 0:
            alerts_html += """
            <div style='background-color: #e8f5e8; border-left: 4px solid #4caf50; padding: 10px; margin: 5px 0;'>
                <strong>✅ لا توجد تنبيهات حالياً</strong><br>
                جميع الشحنات تسير وفقاً للجدول المحدد
            </div>
            """

        self.alerts_list.setHtml(alerts_html)

    def refresh_data(self):
        """تحديث البيانات"""
        self.auto_refresh_label.setText("🔄 جاري التحديث...")
        self.load_live_data()
        self.auto_refresh_label.setText("🟢 تحديث تلقائي")

    def filter_shipments(self):
        """فلترة الشحنات حسب البحث والحالة"""
        search_text = self.search_input.text().lower()
        status_filter = self.status_filter.currentText()

        for row in range(self.shipments_table.rowCount()):
            show_row = True

            # فلترة البحث
            if search_text:
                shipment_id = self.shipments_table.item(row, 0).text().lower()
                supplier_name = self.shipments_table.item(row, 1).text().lower()

                if search_text not in shipment_id and search_text not in supplier_name:
                    show_row = False

            # فلترة الحالة
            if status_filter != "جميع الحالات":
                row_status = self.shipments_table.item(row, 2).text()
                if row_status != status_filter:
                    show_row = False

            self.shipments_table.setRowHidden(row, not show_row)

    def on_shipment_selected(self, shipment_id):
        """معالجة اختيار شحنة من الخريطة"""
        # البحث عن الصف المطابق في الجدول
        for row in range(self.shipments_table.rowCount()):
            if self.shipments_table.item(row, 0).text() == str(shipment_id):
                self.shipments_table.selectRow(row)
                break

        # تحديث التفاصيل
        self.update_shipment_details(shipment_id)

    def on_table_selection_changed(self):
        """معالجة تغيير التحديد في الجدول"""
        current_row = self.shipments_table.currentRow()
        if current_row >= 0:
            shipment_id = int(self.shipments_table.item(current_row, 0).text())
            self.map_widget.selected_shipment_id = shipment_id
            self.map_widget.update()
            self.update_shipment_details(shipment_id)

    def update_shipment_details(self, shipment_id):
        """تحديث تفاصيل الشحنة المحددة"""
        try:
            session = self.db_manager.get_session()
            shipment = session.query(Shipment).outerjoin(Supplier).filter(
                Shipment.id == shipment_id
            ).first()

            if not shipment:
                return

            # مسح التفاصيل السابقة
            for i in reversed(range(self.details_layout.count())):
                self.details_layout.itemAt(i).widget().setParent(None)

            # إنشاء تفاصيل جديدة
            details_html = f"""
            <div style='font-family: Arial, sans-serif; padding: 20px;'>
                <h2 style='color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;'>
                    📦 تفاصيل الشحنة #{shipment.id}
                </h2>

                <div style='background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>
                    <h3 style='color: #495057; margin-top: 0;'>معلومات أساسية</h3>
                    <p><strong>المورد:</strong> {shipment.supplier.name if shipment.supplier else 'غير محدد'}</p>
                    <p><strong>الحالة:</strong> {getattr(shipment, 'shipment_status', 'غير محدد')}</p>
                    <p><strong>رقم التتبع:</strong> {getattr(shipment, 'tracking_number', 'غير محدد')}</p>
                    <p><strong>تاريخ الإنشاء:</strong> {shipment.created_at.strftime('%Y-%m-%d %H:%M') if shipment.created_at else 'غير محدد'}</p>
                </div>

                <div style='background-color: #e8f4fd; padding: 15px; border-radius: 8px; margin: 10px 0;'>
                    <h3 style='color: #1976d2; margin-top: 0;'>معلومات الشحن</h3>
                    <p><strong>ميناء التحميل:</strong> {getattr(shipment, 'port_of_loading', 'غير محدد')}</p>
                    <p><strong>ميناء التفريغ:</strong> {getattr(shipment, 'port_of_discharge', 'غير محدد')}</p>
                    <p><strong>الوجهة النهائية:</strong> {getattr(shipment, 'final_destination', 'غير محدد')}</p>
                    <p><strong>شركة الشحن:</strong> {getattr(shipment, 'shipping_company', 'غير محدد')}</p>
                </div>

                <div style='background-color: #f3e5f5; padding: 15px; border-radius: 8px; margin: 10px 0;'>
                    <h3 style='color: #7b1fa2; margin-top: 0;'>التواريخ المهمة</h3>
                    <p><strong>تاريخ المغادرة المتوقع:</strong> {shipment.estimated_departure_date.strftime('%Y-%m-%d') if getattr(shipment, 'estimated_departure_date', None) else 'غير محدد'}</p>
                    <p><strong>تاريخ الوصول المتوقع:</strong> {shipment.estimated_arrival_date.strftime('%Y-%m-%d') if getattr(shipment, 'estimated_arrival_date', None) else 'غير محدد'}</p>
                    <p><strong>آخر تحديث:</strong> {shipment.updated_at.strftime('%Y-%m-%d %H:%M') if shipment.updated_at else 'غير محدد'}</p>
                </div>
            </div>
            """

            details_text = QTextEdit()
            details_text.setHtml(details_html)
            details_text.setReadOnly(True)
            details_text.setStyleSheet("""
                QTextEdit {
                    border: none;
                    background-color: white;
                }
            """)

            self.details_layout.addWidget(details_text)

        except Exception as e:
            error_label = QLabel(f"خطأ في تحميل التفاصيل: {str(e)}")
            error_label.setStyleSheet("color: red; padding: 20px;")
            self.details_layout.addWidget(error_label)

    def show_refresh_settings(self):
        """عرض إعدادات التحديث"""
        dialog = QDialog(self)
        dialog.setWindowTitle("إعدادات التحديث التلقائي")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QVBoxLayout(dialog)

        # إعدادات فترة التحديث
        refresh_group = QGroupBox("فترة التحديث")
        refresh_layout = QFormLayout(refresh_group)

        # فترة تحديث البيانات
        data_refresh_spin = QSpinBox()
        data_refresh_spin.setRange(10, 300)  # من 10 ثواني إلى 5 دقائق
        data_refresh_spin.setValue(60)  # القيمة الافتراضية دقيقة واحدة
        data_refresh_spin.setSuffix(" ثانية")
        refresh_layout.addRow("تحديث البيانات:", data_refresh_spin)

        # فترة تحديث الخريطة
        map_refresh_spin = QSpinBox()
        map_refresh_spin.setRange(5, 120)  # من 5 ثواني إلى دقيقتين
        map_refresh_spin.setValue(30)  # القيمة الافتراضية 30 ثانية
        map_refresh_spin.setSuffix(" ثانية")
        refresh_layout.addRow("تحديث الخريطة:", map_refresh_spin)

        layout.addWidget(refresh_group)

        # إعدادات التنبيهات
        alerts_group = QGroupBox("إعدادات التنبيهات")
        alerts_layout = QVBoxLayout(alerts_group)

        sound_alerts_check = QCheckBox("تفعيل التنبيهات الصوتية")
        sound_alerts_check.setChecked(True)
        alerts_layout.addWidget(sound_alerts_check)

        desktop_notifications_check = QCheckBox("إشعارات سطح المكتب")
        desktop_notifications_check.setChecked(True)
        alerts_layout.addWidget(desktop_notifications_check)

        layout.addWidget(alerts_group)

        # أزرار الحوار
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, dialog
        )
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        if dialog.exec() == QDialog.Accepted:
            # تطبيق الإعدادات الجديدة
            self.refresh_timer.setInterval(data_refresh_spin.value() * 1000)
            if hasattr(self.map_widget, 'update_timer'):
                self.map_widget.update_timer.setInterval(map_refresh_spin.value() * 1000)

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        if self.isFullScreen():
            self.showMaximized()
            self.fullscreen_btn.setText("🔳 ملء الشاشة")
        else:
            self.showFullScreen()
            self.fullscreen_btn.setText("🔲 نافذة عادية")

    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_F11:
            self.toggle_fullscreen()
        elif event.key() == Qt.Key_F5:
            self.refresh_data()
        elif event.key() == Qt.Key_Escape and self.isFullScreen():
            self.showMaximized()
            self.fullscreen_btn.setText("🔳 ملء الشاشة")
        else:
            super().keyPressEvent(event)

    def check_new_alerts(self):
        """فحص التنبيهات الجديدة"""
        try:
            session = self.db_manager.get_session()
            shipments = session.query(Shipment).outerjoin(Supplier).filter(
                Shipment.is_active == True
            ).all()

            current_alerts = set()
            current_time = datetime.now()

            for shipment in shipments:
                # تحقق من التأخير
                if hasattr(shipment, 'estimated_arrival_date') and shipment.estimated_arrival_date:
                    if current_time.date() > shipment.estimated_arrival_date and \
                       getattr(shipment, 'shipment_status', '') not in ['وصلت', 'تم التسليم']:
                        alert_key = f"late_{shipment.id}"
                        current_alerts.add(alert_key)

                        # إذا كان تنبيه جديد
                        if alert_key not in self.previous_alerts:
                            self.show_new_alert_notification(
                                "شحنة متأخرة",
                                f"الشحنة #{shipment.id} متأخرة عن الموعد المحدد"
                            )

                # تحقق من الشحنات بدون تتبع
                if not getattr(shipment, 'tracking_number', None):
                    alert_key = f"no_tracking_{shipment.id}"
                    current_alerts.add(alert_key)

                    # إذا كان تنبيه جديد
                    if alert_key not in self.previous_alerts:
                        self.show_new_alert_notification(
                            "شحنة بدون تتبع",
                            f"الشحنة #{shipment.id} تحتاج إلى رقم تتبع"
                        )

            # تحديث التنبيهات السابقة
            self.previous_alerts = current_alerts

        except Exception as e:
            print(f"خطأ في فحص التنبيهات: {str(e)}")

    def show_new_alert_notification(self, title, message):
        """عرض إشعار تنبيه جديد"""
        if not self.notification_enabled:
            return

        # تحديث مؤشر التنبيهات في شريط الحالة
        self.status_bar.showMessage(f"🔔 {title}: {message}", 5000)

        # يمكن إضافة إشعارات سطح المكتب هنا
        # أو أصوات تنبيه حسب الحاجة

        # وميض النافذة للفت الانتباه
        if not self.isActiveWindow():
            self.activateWindow()
            self.raise_()

    def get_shipment_status_color(self, status):
        """الحصول على لون الحالة"""
        colors = {
            'في الطريق': '#3498db',
            'وصلت': '#27ae60',
            'متأخرة': '#e74c3c',
            'في الميناء': '#f39c12',
            'تم التسليم': '#9b59b6',
            'ملغاة': '#95a5a6',
            'جديدة': '#1abc9c'
        }
        return colors.get(status, '#7f8c8d')

    def export_tracking_data(self):
        """تصدير بيانات التتبع"""
        try:
            from PySide6.QtWidgets import QFileDialog
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "تصدير بيانات التتبع",
                f"tracking_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                session = self.db_manager.get_session()
                shipments = session.query(Shipment).outerjoin(Supplier).filter(
                    Shipment.is_active == True
                ).all()

                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    writer.writerow([
                        'رقم الشحنة', 'المورد', 'الحالة', 'ميناء المغادرة',
                        'ميناء الوصول', 'تاريخ المغادرة المتوقع', 'تاريخ الوصول المتوقع',
                        'رقم التتبع', 'شركة الشحن', 'تاريخ الإنشاء'
                    ])

                    # كتابة البيانات
                    for shipment in shipments:
                        writer.writerow([
                            shipment.id,
                            shipment.supplier.name if shipment.supplier else '',
                            getattr(shipment, 'shipment_status', ''),
                            getattr(shipment, 'port_of_loading', ''),
                            getattr(shipment, 'port_of_discharge', ''),
                            shipment.estimated_departure_date.strftime('%Y-%m-%d') if getattr(shipment, 'estimated_departure_date', None) else '',
                            shipment.estimated_arrival_date.strftime('%Y-%m-%d') if getattr(shipment, 'estimated_arrival_date', None) else '',
                            getattr(shipment, 'tracking_number', ''),
                            getattr(shipment, 'shipping_company', ''),
                            shipment.created_at.strftime('%Y-%m-%d %H:%M') if shipment.created_at else ''
                        ])

                QMessageBox.information(
                    self,
                    "تم التصدير",
                    f"تم تصدير بيانات التتبع بنجاح إلى:\n{file_path}"
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في التصدير",
                f"فشل في تصدير البيانات:\n{str(e)}"
            )

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        # إيقاف المؤقتات
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()
        if hasattr(self, 'alerts_timer'):
            self.alerts_timer.stop()
        if hasattr(self, 'map_widget') and hasattr(self.map_widget, 'update_timer'):
            self.map_widget.update_timer.stop()

        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = LiveTrackingWindow()
    window.show()
    sys.exit(app.exec())
