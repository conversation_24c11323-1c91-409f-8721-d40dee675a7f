#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة البحث المتقدمة للأصناف
Advanced Item Search Dialog
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
    QComboBox, QGroupBox, QMessageBox, QHeaderView, QAbstractItemView,
    QSplitter, QFrame, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QIcon

from ...database.database_manager import DatabaseManager
from ...database.models import Item, ItemGroup, UnitOfMeasure


class AdvancedItemSearchDialog(QDialog):
    """نافذة البحث المتقدمة للأصناف"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.selected_item = None
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("البحث المتقدم عن الأصناف")
        self.setModal(True)
        self.resize(1200, 800)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        
        # إنشاء Splitter للتقسيم
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # الجانب الأيسر - البحث والفلترة
        search_frame = self.create_search_frame()
        splitter.addWidget(search_frame)
        
        # الجانب الأيمن - النتائج
        results_frame = self.create_results_frame()
        splitter.addWidget(results_frame)
        
        # تعيين النسب
        splitter.setSizes([300, 900])
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        self.select_button = QPushButton("اختيار الصنف")
        self.select_button.setEnabled(False)
        self.select_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
            }
        """)
        buttons_layout.addWidget(self.select_button)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #757575;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #616161;
            }
        """)
        buttons_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(buttons_layout)
        
    def create_search_frame(self):
        """إنشاء إطار البحث والفلترة"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setMaximumWidth(320)
        
        layout = QVBoxLayout(frame)
        
        # عنوان البحث
        title_label = QLabel("🔍 البحث والفلترة")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setStyleSheet("color: #1976D2; padding: 5px;")
        layout.addWidget(title_label)
        
        # مجموعة البحث السريع
        quick_search_group = QGroupBox("البحث السريع")
        quick_search_layout = QFormLayout(quick_search_group)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالكود أو الاسم...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #E0E0E0;
                border-radius: 4px;
                font-size: 11pt;
            }
            QLineEdit:focus {
                border-color: #2196F3;
            }
        """)
        quick_search_layout.addRow("البحث:", self.search_edit)
        
        layout.addWidget(quick_search_group)
        
        # مجموعة الفلترة المتقدمة
        filter_group = QGroupBox("الفلترة المتقدمة")
        filter_layout = QFormLayout(filter_group)
        
        # فلتر المجموعة
        self.group_combo = QComboBox()
        self.group_combo.addItem("جميع المجموعات", None)
        filter_layout.addRow("المجموعة:", self.group_combo)
        
        # فلتر وحدة القياس
        self.unit_combo = QComboBox()
        self.unit_combo.addItem("جميع الوحدات", None)
        filter_layout.addRow("وحدة القياس:", self.unit_combo)
        
        # فلتر الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItem("جميع الأصناف", None)
        self.status_combo.addItem("النشطة فقط", True)
        self.status_combo.addItem("غير النشطة فقط", False)
        self.status_combo.setCurrentIndex(1)  # النشطة فقط افتراضياً
        filter_layout.addRow("الحالة:", self.status_combo)
        
        layout.addWidget(filter_group)
        
        # أزرار البحث
        search_buttons_layout = QVBoxLayout()
        
        self.search_button = QPushButton("🔍 بحث")
        self.search_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        search_buttons_layout.addWidget(self.search_button)
        
        self.clear_button = QPushButton("🗑️ مسح")
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        search_buttons_layout.addWidget(self.clear_button)
        
        layout.addLayout(search_buttons_layout)
        
        # إحصائيات
        self.stats_label = QLabel("📊 إحصائيات:\nإجمالي الأصناف: 0\nالنتائج المعروضة: 0")
        self.stats_label.setStyleSheet("""
            QLabel {
                background-color: #F5F5F5;
                padding: 10px;
                border-radius: 4px;
                border: 1px solid #E0E0E0;
            }
        """)
        layout.addWidget(self.stats_label)
        
        layout.addStretch()
        
        return frame
        
    def create_results_frame(self):
        """إنشاء إطار النتائج"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(frame)
        
        # عنوان النتائج
        title_label = QLabel("📋 نتائج البحث")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setStyleSheet("color: #1976D2; padding: 5px;")
        layout.addWidget(title_label)
        
        # جدول النتائج
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(8)
        self.results_table.setHorizontalHeaderLabels([
            "كود الصنف", "اسم الصنف", "المجموعة", "وحدة القياس",
            "سعر البيع", "الوزن", "الحالة", "الوصف"
        ])
        
        # تنسيق الجدول
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.results_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.results_table.setSortingEnabled(True)
        
        # تعيين عرض الأعمدة
        header = self.results_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 100)  # كود الصنف
        header.resizeSection(1, 200)  # اسم الصنف
        header.resizeSection(2, 120)  # المجموعة
        header.resizeSection(3, 100)  # وحدة القياس
        header.resizeSection(4, 80)   # سعر البيع
        header.resizeSection(5, 80)   # الوزن
        header.resizeSection(6, 80)   # الحالة

        layout.addWidget(self.results_table)

        return frame

    def setup_connections(self):
        """إعداد الاتصالات"""
        # البحث التلقائي أثناء الكتابة
        self.search_edit.textChanged.connect(self.on_search_text_changed)

        # أزرار البحث
        self.search_button.clicked.connect(self.perform_search)
        self.clear_button.clicked.connect(self.clear_search)

        # فلاتر
        self.group_combo.currentTextChanged.connect(self.perform_search)
        self.unit_combo.currentTextChanged.connect(self.perform_search)
        self.status_combo.currentTextChanged.connect(self.perform_search)

        # تحديد الصنف
        self.results_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.results_table.itemDoubleClicked.connect(self.on_item_double_clicked)

        # أزرار التحكم
        self.select_button.clicked.connect(self.accept)
        self.cancel_button.clicked.connect(self.reject)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            session = self.db_manager.get_session()

            # تحميل المجموعات
            groups = session.query(ItemGroup).filter(ItemGroup.is_active == True).all()
            for group in groups:
                self.group_combo.addItem(group.name, group.id)

            # تحميل وحدات القياس
            units = session.query(UnitOfMeasure).filter(UnitOfMeasure.is_active == True).all()
            for unit in units:
                self.unit_combo.addItem(unit.name, unit.id)

            # تحميل الأصناف الأولية
            self.perform_search()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
        finally:
            session.close()

    def on_search_text_changed(self):
        """البحث التلقائي عند تغيير النص"""
        self.search_timer.stop()
        self.search_timer.start(500)  # انتظار 500ms قبل البحث

    def perform_search(self):
        """تنفيذ البحث"""
        try:
            session = self.db_manager.get_session()

            # بناء الاستعلام الأساسي
            query = session.query(Item).join(ItemGroup, isouter=True).join(UnitOfMeasure, isouter=True)

            # فلتر النص
            search_text = self.search_edit.text().strip()
            if search_text:
                query = query.filter(
                    (Item.code.contains(search_text)) |
                    (Item.name.contains(search_text)) |
                    (Item.name_en.contains(search_text)) |
                    (Item.description.contains(search_text))
                )

            # فلتر المجموعة
            group_id = self.group_combo.currentData()
            if group_id:
                query = query.filter(Item.group_id == group_id)

            # فلتر وحدة القياس
            unit_id = self.unit_combo.currentData()
            if unit_id:
                query = query.filter(Item.unit_id == unit_id)

            # فلتر الحالة
            status = self.status_combo.currentData()
            if status is not None:
                query = query.filter(Item.is_active == status)

            # تنفيذ الاستعلام
            items = query.order_by(Item.name).limit(1000).all()  # حد أقصى 1000 نتيجة

            # ملء الجدول
            self.populate_results_table(items)

            # تحديث الإحصائيات
            total_count = session.query(Item).count()
            self.update_statistics(total_count, len(items))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث: {str(e)}")
        finally:
            session.close()

    def populate_results_table(self, items):
        """ملء جدول النتائج"""
        self.results_table.setRowCount(len(items))

        for row, item in enumerate(items):
            # كود الصنف
            code_item = QTableWidgetItem(item.code or "")
            code_item.setData(Qt.UserRole, item.id)
            self.results_table.setItem(row, 0, code_item)

            # اسم الصنف
            self.results_table.setItem(row, 1, QTableWidgetItem(item.name or ""))

            # المجموعة
            group_name = item.group.name if item.group else "غير محدد"
            self.results_table.setItem(row, 2, QTableWidgetItem(group_name))

            # وحدة القياس
            unit_name = item.unit.name if item.unit else "غير محدد"
            self.results_table.setItem(row, 3, QTableWidgetItem(unit_name))

            # سعر البيع
            price_text = f"{item.selling_price:.2f}" if item.selling_price else "0.00"
            self.results_table.setItem(row, 4, QTableWidgetItem(price_text))

            # الوزن
            weight_text = f"{item.weight:.2f}" if item.weight else "0.00"
            self.results_table.setItem(row, 5, QTableWidgetItem(weight_text))

            # الحالة
            status_text = "نشط" if item.is_active else "غير نشط"
            status_item = QTableWidgetItem(status_text)
            if item.is_active:
                status_item.setBackground(Qt.green)
            else:
                status_item.setBackground(Qt.red)
            self.results_table.setItem(row, 6, status_item)

            # الوصف
            self.results_table.setItem(row, 7, QTableWidgetItem(item.description or ""))

    def update_statistics(self, total_count, displayed_count):
        """تحديث الإحصائيات"""
        stats_text = f"📊 إحصائيات:\nإجمالي الأصناف: {total_count:,}\nالنتائج المعروضة: {displayed_count:,}"
        self.stats_label.setText(stats_text)

    def clear_search(self):
        """مسح البحث"""
        self.search_edit.clear()
        self.group_combo.setCurrentIndex(0)
        self.unit_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(1)  # النشطة فقط
        self.perform_search()

    def on_selection_changed(self):
        """عند تغيير التحديد"""
        has_selection = self.results_table.currentRow() >= 0
        self.select_button.setEnabled(has_selection)

    def on_item_double_clicked(self, item):
        """عند النقر المزدوج على صنف"""
        if self.results_table.currentRow() >= 0:
            self.accept()

    def get_selected_item(self):
        """الحصول على الصنف المحدد"""
        current_row = self.results_table.currentRow()
        if current_row < 0:
            return None

        try:
            # الحصول على معرف الصنف
            item_id = self.results_table.item(current_row, 0).data(Qt.UserRole)

            # الحصول على بيانات الصنف من قاعدة البيانات
            session = self.db_manager.get_session()
            item = session.query(Item).filter(Item.id == item_id).first()

            if item:
                return {
                    'id': item.id,
                    'code': item.code,
                    'name': item.name,
                    'description': item.description or "",
                    'selling_price': item.selling_price or 0.0,
                    'weight': item.weight or 0.0,
                    'is_active': item.is_active
                }

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في الحصول على بيانات الصنف: {str(e)}")
        finally:
            session.close()

        return None
