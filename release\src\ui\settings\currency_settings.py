# -*- coding: utf-8 -*-
"""
تبويب إعدادات العملات
Currency Settings Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QDoubleSpinBox, QCheckBox)
from PySide6.QtCore import Qt

from ...database.database_manager import DatabaseManager
from ...database.models import Currency

class CurrencySettingsWidget(QWidget):
    """ويدجت إعدادات العملات"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # مجموعة إضافة عملة جديدة
        add_group = QGroupBox("إضافة عملة جديدة")
        add_layout = QFormLayout(add_group)
        
        self.code_edit = QLineEdit()
        self.code_edit.setMaxLength(3)
        self.code_edit.setPlaceholderText("مثل: USD, EUR, SAR")
        add_layout.addRow("رمز العملة:", self.code_edit)
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("مثل: دولار أمريكي")
        add_layout.addRow("اسم العملة:", self.name_edit)
        
        self.name_en_edit = QLineEdit()
        self.name_en_edit.setPlaceholderText("مثل: US Dollar")
        add_layout.addRow("الاسم بالإنجليزية:", self.name_en_edit)
        
        self.symbol_edit = QLineEdit()
        self.symbol_edit.setMaxLength(10)
        self.symbol_edit.setPlaceholderText("مثل: $, €, ر.س")
        add_layout.addRow("رمز العملة:", self.symbol_edit)
        
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.001, 999999.999)
        self.exchange_rate_spin.setDecimals(3)
        self.exchange_rate_spin.setValue(1.0)
        add_layout.addRow("سعر الصرف:", self.exchange_rate_spin)
        
        self.is_base_check = QCheckBox("العملة الأساسية")
        add_layout.addRow("", self.is_base_check)
        
        # أزرار الإدارة
        buttons_layout = QHBoxLayout()
        
        add_button = QPushButton("إضافة عملة")
        add_button.clicked.connect(self.add_currency)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        clear_button = QPushButton("مسح الحقول")
        clear_button.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(add_button)
        buttons_layout.addWidget(clear_button)
        buttons_layout.addStretch()
        
        add_layout.addRow("", buttons_layout)
        main_layout.addWidget(add_group)
        
        # جدول العملات
        table_group = QGroupBox("العملات المسجلة")
        table_layout = QVBoxLayout(table_group)
        
        self.currencies_table = QTableWidget()
        self.currencies_table.setColumnCount(6)
        self.currencies_table.setHorizontalHeaderLabels([
            "الرمز", "الاسم", "الاسم بالإنجليزية", "الرمز", "سعر الصرف", "أساسية"
        ])
        
        # تنسيق الجدول
        header = self.currencies_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.currencies_table.setAlternatingRowColors(True)
        self.currencies_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        table_layout.addWidget(self.currencies_table)
        
        # أزرار إدارة الجدول
        table_buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_data)
        
        edit_button = QPushButton("تعديل المحدد")
        edit_button.clicked.connect(self.edit_selected)
        
        delete_button = QPushButton("حذف المحدد")
        delete_button.clicked.connect(self.delete_selected)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(edit_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addStretch()
        
        table_layout.addLayout(table_buttons_layout)
        main_layout.addWidget(table_group)
        
        main_layout.addStretch()
    
    def load_data(self):
        """تحميل بيانات العملات"""
        session = self.db_manager.get_session()
        try:
            currencies = session.query(Currency).filter_by(is_active=True).all()
            
            self.currencies_table.setRowCount(len(currencies))
            
            for row, currency in enumerate(currencies):
                # الرمز
                self.currencies_table.setItem(row, 0, QTableWidgetItem(currency.code))
                
                # الاسم
                self.currencies_table.setItem(row, 1, QTableWidgetItem(currency.name or ""))
                
                # الاسم بالإنجليزية
                self.currencies_table.setItem(row, 2, QTableWidgetItem(currency.name_en or ""))
                
                # رمز العملة
                self.currencies_table.setItem(row, 3, QTableWidgetItem(currency.symbol or ""))
                
                # سعر الصرف
                rate_item = QTableWidgetItem(f"{currency.exchange_rate:.3f}")
                self.currencies_table.setItem(row, 4, rate_item)
                
                # أساسية
                base_text = "نعم" if currency.is_base else "لا"
                base_item = QTableWidgetItem(base_text)
                if currency.is_base:
                    base_item.setBackground(Qt.green)
                self.currencies_table.setItem(row, 5, base_item)
                
                # حفظ ID في البيانات
                self.currencies_table.item(row, 0).setData(Qt.UserRole, currency.id)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل بيانات العملات:\n{str(e)}"
            )
        finally:
            session.close()
    
    def add_currency(self):
        """إضافة عملة جديدة"""
        # التحقق من صحة البيانات
        if not self.code_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رمز العملة")
            return
        
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم العملة")
            return
        
        session = self.db_manager.get_session()
        try:
            # التحقق من عدم وجود العملة مسبقاً
            existing = session.query(Currency).filter_by(code=self.code_edit.text().upper()).first()
            if existing:
                QMessageBox.warning(self, "عملة موجودة", "هذه العملة موجودة بالفعل")
                return
            
            # إذا كانت العملة أساسية، إلغاء تفعيل العملات الأخرى
            if self.is_base_check.isChecked():
                session.query(Currency).update({Currency.is_base: False})
            
            # إنشاء العملة الجديدة
            new_currency = Currency(
                code=self.code_edit.text().upper(),
                name=self.name_edit.text(),
                name_en=self.name_en_edit.text(),
                symbol=self.symbol_edit.text(),
                exchange_rate=self.exchange_rate_spin.value(),
                is_base=self.is_base_check.isChecked()
            )
            
            session.add(new_currency)
            session.commit()
            
            QMessageBox.information(self, "تم الإضافة", "تم إضافة العملة بنجاح")
            
            self.clear_form()
            self.load_data()
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في الإضافة", f"حدث خطأ أثناء إضافة العملة:\n{str(e)}")
        finally:
            session.close()
    
    def clear_form(self):
        """مسح حقول النموذج"""
        self.code_edit.clear()
        self.name_edit.clear()
        self.name_en_edit.clear()
        self.symbol_edit.clear()
        self.exchange_rate_spin.setValue(1.0)
        self.is_base_check.setChecked(False)
    
    def edit_selected(self):
        """تعديل العملة المحددة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "لا يوجد تحديد", "يرجى تحديد عملة للتعديل")
            return
        
        # ملء النموذج ببيانات العملة المحددة
        self.code_edit.setText(self.currencies_table.item(current_row, 0).text())
        self.name_edit.setText(self.currencies_table.item(current_row, 1).text())
        self.name_en_edit.setText(self.currencies_table.item(current_row, 2).text())
        self.symbol_edit.setText(self.currencies_table.item(current_row, 3).text())
        
        rate_text = self.currencies_table.item(current_row, 4).text()
        self.exchange_rate_spin.setValue(float(rate_text))
        
        is_base = self.currencies_table.item(current_row, 5).text() == "نعم"
        self.is_base_check.setChecked(is_base)
    
    def delete_selected(self):
        """حذف العملة المحددة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "لا يوجد تحديد", "يرجى تحديد عملة للحذف")
            return
        
        currency_id = self.currencies_table.item(current_row, 0).data(Qt.UserRole)
        currency_code = self.currencies_table.item(current_row, 0).text()
        
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف العملة {currency_code}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                currency = session.query(Currency).get(currency_id)
                if currency:
                    if currency.is_base:
                        QMessageBox.warning(self, "لا يمكن الحذف", "لا يمكن حذف العملة الأساسية")
                        return
                    
                    currency.is_active = False  # حذف منطقي
                    session.commit()
                    
                    QMessageBox.information(self, "تم الحذف", f"تم حذف العملة {currency_code} بنجاح")
                    self.load_data()
                    
            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف العملة:\n{str(e)}")
            finally:
                session.close()
    
    def save_settings(self):
        """حفظ الإعدادات"""
        pass
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        self.clear_form()
