# نظام CnX ERP - النموذج التجريبي للواجهة الرئيسية

## 📋 نظرة عامة

هذا المشروع هو نموذج تجريبي للواجهة الرئيسية لنظام إدارة موارد المؤسسة (ERP) مطابق للتصميم المرفق في الصورة. تم تطوير النموذج باستخدام Python و PySide6 لإنشاء واجهة مستخدم احترافية وعصرية.

## 🎯 الميزات الرئيسية

### ✅ المكونات المطابقة للنموذج الأصلي:
- **شريط العنوان**: مع اسم النظام وأزرار التحكم
- **شريط الأدوات العلوي**: أيقونات وأزرار متنوعة للوصول السريع
- **القائمة الجانبية اليسرى**: خيارات النظام الرئيسية
- **المنطقة المركزية**: عرض شعار CnX ERP مع خلفية فنية متدرجة
- **القائمة الجانبية اليمنى**: إعدادات النظام والخيارات الإضافية
- **شريط الحالة السفلي**: معلومات المستخدم والنظام

### 🚀 المكونات المحسنة الإضافية:
- **لوحة المعلومات التفاعلية**: بطاقات معلومات ديناميكية
- **الإجراءات السريعة**: أزرار متحركة للوصول السريع
- **الأنشطة الحديثة**: جدول يعرض آخر العمليات
- **حالة النظام**: مراقبة حالة قاعدة البيانات والخادم
- **نوافذ حوار**: إعدادات النظام ومعلومات البرنامج

## 🛠️ المتطلبات التقنية

### البرمجيات المطلوبة:
- **Python 3.8+**
- **PySide6** (Qt for Python)
- **نظام التشغيل**: Windows, macOS, Linux

### المكتبات المستخدمة:
```bash
pip install PySide6
```

## 📁 هيكل المشروع

```
project/
├── main_window_prototype.py      # النموذج الأساسي للواجهة
├── enhanced_ui_components.py     # المكونات المحسنة
├── run_prototype.py             # ملف التشغيل الرئيسي
└── CnX_ERP_README.md           # هذا الملف
```

## 🚀 كيفية التشغيل

### 1. تثبيت المتطلبات:
```bash
pip install PySide6 --upgrade
```

### 2. تشغيل النموذج التجريبي:
```bash
python run_prototype.py
```

### 3. تشغيل النموذج الأساسي فقط:
```bash
python main_window_prototype.py
```

## 🎨 التصميم والألوان

### نظام الألوان المستخدم:
- **الأزرق الأساسي**: `#2E86AB` (للشعار والعناصر الرئيسية)
- **الخلفية**: تدرج من الأزرق الفاتح إلى الوردي الفاتح
- **الأزرار**: تدرجات زرقاء مع تأثيرات hover
- **النصوص**: ألوان متباينة للوضوح

### الخطوط:
- **الخط الأساسي**: Arial
- **أحجام متنوعة**: من 10px إلى 48px حسب العنصر

## 📱 المكونات التفاعلية

### 1. **لوحة المعلومات الرئيسية**:
- بطاقات معلومات تفاعلية
- إحصائيات الشحنات والعملاء
- رسوم بيانية (منطقة محجوزة للتطوير المستقبلي)

### 2. **الإجراءات السريعة**:
- أزرار متحركة مع تأثيرات بصرية
- وصول سريع للوظائف الأساسية

### 3. **الأنشطة الحديثة**:
- جدول يعرض آخر العمليات
- تحديث تلقائي للبيانات

### 4. **حالة النظام**:
- مراقبة اتصال قاعدة البيانات
- معلومات استخدام الموارد

## ⚙️ الإعدادات والتخصيص

### نوافذ الحوار المتاحة:
- **نافذة الإعدادات**: تخصيص إعدادات النظام
- **نافذة حول البرنامج**: معلومات الإصدار والمطور
- **نوافذ تأكيد**: للعمليات الحساسة

### التبويبات:
- **لوحة المعلومات**: العرض الرئيسي المحسن
- **الشعار**: النموذج الأصلي المطابق للصورة
- **الشحنات**: منطقة محجوزة للتطوير
- **العملاء**: منطقة محجوزة للتطوير

## 🔧 التطوير المستقبلي

### المراحل القادمة:
1. **ربط قاعدة البيانات Oracle**: تنفيذ الاتصال الفعلي
2. **إدارة الشحنات**: تطوير وحدة إدارة الشحنات الكاملة
3. **إدارة العملاء**: نظام شامل لإدارة بيانات العملاء
4. **التقارير**: نظام تقارير متقدم مع رسوم بيانية
5. **الأمان**: نظام مصادقة وصلاحيات متقدم

### التحسينات المقترحة:
- إضافة رسوم بيانية تفاعلية
- تحسين الاستجابة للشاشات المختلفة
- إضافة ثيمات متعددة
- دعم لغات إضافية

## 📞 الدعم والمساعدة

### في حالة مواجهة مشاكل:
1. تأكد من تثبيت Python 3.8+ و PySide6
2. تحقق من رسائل الخطأ في وحدة التحكم
3. راجع ملف README للتعليمات التفصيلية

### معلومات الاتصال:
- **المطور**: فريق CnX Solutions
- **الإصدار**: 1.0.0
- **تاريخ الإصدار**: 2024

## 📄 الترخيص

جميع الحقوق محفوظة © 2024 CnX Solutions

---

**ملاحظة**: هذا نموذج تجريبي للواجهة الرئيسية فقط. الوظائف الكاملة للنظام ستكون متاحة في الإصدارات القادمة.
