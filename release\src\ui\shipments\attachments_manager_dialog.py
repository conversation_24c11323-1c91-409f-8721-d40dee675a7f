# -*- coding: utf-8 -*-
"""
نافذة إدارة المرفقات للمستندات
"""

import os
import json
import shutil
from pathlib import Path
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QListWidget, QListWidgetItem,
                               QFileDialog, QMessageBox, QGroupBox, QGridLayout,
                               QProgressBar, QTextEdit, QSplitter, QFrame)
from PySide6.QtCore import Qt, Signal, QThread
from PySide6.QtGui import QIcon, QPixmap, QFont, QDesktopServices
from PySide6.QtCore import QUrl

class FileOperationThread(QThread):
    """خيط لعمليات الملفات"""
    progress_updated = Signal(int)
    operation_completed = Signal(bool, str)
    
    def __init__(self, operation, source_files, destination_folder):
        super().__init__()
        self.operation = operation  # 'copy' or 'move'
        self.source_files = source_files
        self.destination_folder = destination_folder
    
    def run(self):
        try:
            total_files = len(self.source_files)
            for i, source_file in enumerate(self.source_files):
                if self.operation == 'copy':
                    shutil.copy2(source_file, self.destination_folder)
                elif self.operation == 'move':
                    shutil.move(source_file, self.destination_folder)
                
                progress = int((i + 1) / total_files * 100)
                self.progress_updated.emit(progress)
            
            self.operation_completed.emit(True, "تم بنجاح")
        except Exception as e:
            self.operation_completed.emit(False, str(e))

class AttachmentsManagerDialog(QDialog):
    """نافذة إدارة المرفقات"""
    
    def __init__(self, parent=None, document_type="", existing_files=None):
        super().__init__(parent)
        self.document_type = document_type
        self.existing_files = existing_files or []
        self.attachments_folder = self.get_attachments_folder()
        self.setup_ui()
        self.load_existing_files()
        
    def get_attachments_folder(self):
        """الحصول على مجلد المرفقات"""
        base_folder = Path("attachments")
        document_folder = base_folder / self.document_type.replace(" ", "_").lower()
        document_folder.mkdir(parents=True, exist_ok=True)
        return document_folder
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"إدارة مرفقات - {self.document_type}")
        self.setModal(True)
        self.resize(800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # عنوان النافذة
        title_label = QLabel(f"إدارة مرفقات {self.document_type}")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # منطقة المحتوى
        content_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(content_splitter)
        
        # قائمة الملفات
        files_group = QGroupBox("الملفات المرفقة")
        files_layout = QVBoxLayout(files_group)
        
        self.files_list = QListWidget()
        self.files_list.setAlternatingRowColors(True)
        self.files_list.itemDoubleClicked.connect(self.open_file)
        files_layout.addWidget(self.files_list)
        
        # أزرار إدارة الملفات
        files_buttons_layout = QHBoxLayout()
        
        self.add_files_btn = QPushButton("إضافة ملفات")
        self.add_files_btn.setIcon(QIcon("icons/add.png"))
        self.add_files_btn.clicked.connect(self.add_files)
        self.add_files_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        
        self.remove_file_btn = QPushButton("حذف ملف")
        self.remove_file_btn.setIcon(QIcon("icons/delete.png"))
        self.remove_file_btn.clicked.connect(self.remove_file)
        self.remove_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        self.open_file_btn = QPushButton("فتح ملف")
        self.open_file_btn.setIcon(QIcon("icons/open.png"))
        self.open_file_btn.clicked.connect(self.open_file)
        self.open_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        self.open_folder_btn = QPushButton("فتح المجلد")
        self.open_folder_btn.setIcon(QIcon("icons/folder.png"))
        self.open_folder_btn.clicked.connect(self.open_folder)
        self.open_folder_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        files_buttons_layout.addWidget(self.add_files_btn)
        files_buttons_layout.addWidget(self.remove_file_btn)
        files_buttons_layout.addWidget(self.open_file_btn)
        files_buttons_layout.addWidget(self.open_folder_btn)
        files_buttons_layout.addStretch()
        
        files_layout.addLayout(files_buttons_layout)
        content_splitter.addWidget(files_group)
        
        # معلومات الملف
        info_group = QGroupBox("معلومات الملف")
        info_layout = QVBoxLayout(info_group)
        
        self.file_info = QTextEdit()
        self.file_info.setReadOnly(True)
        self.file_info.setMaximumHeight(200)
        info_layout.addWidget(self.file_info)
        
        content_splitter.addWidget(info_group)
        content_splitter.setSizes([500, 300])
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # أزرار النافذة
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ وإغلاق")
        self.save_btn.clicked.connect(self.accept)
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(buttons_layout)
        
        # ربط الأحداث
        self.files_list.currentItemChanged.connect(self.show_file_info)
    
    def load_existing_files(self):
        """تحميل الملفات الموجودة"""
        self.files_list.clear()
        
        for file_path in self.existing_files:
            if os.path.exists(file_path):
                file_name = os.path.basename(file_path)
                item = QListWidgetItem(file_name)
                item.setData(Qt.UserRole, file_path)
                
                # تحديد أيقونة حسب نوع الملف
                file_ext = os.path.splitext(file_name)[1].lower()
                if file_ext in ['.pdf']:
                    item.setIcon(QIcon("icons/pdf.png"))
                elif file_ext in ['.doc', '.docx']:
                    item.setIcon(QIcon("icons/word.png"))
                elif file_ext in ['.xls', '.xlsx']:
                    item.setIcon(QIcon("icons/excel.png"))
                elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                    item.setIcon(QIcon("icons/image.png"))
                else:
                    item.setIcon(QIcon("icons/file.png"))
                
                self.files_list.addItem(item)

    def add_files(self):
        """إضافة ملفات جديدة"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("جميع الملفات (*.*)")

        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            self.copy_files_to_attachments(selected_files)

    def copy_files_to_attachments(self, source_files):
        """نسخ الملفات إلى مجلد المرفقات"""
        if not source_files:
            return

        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # إنشاء خيط لنسخ الملفات
        self.file_thread = FileOperationThread('copy', source_files, str(self.attachments_folder))
        self.file_thread.progress_updated.connect(self.progress_bar.setValue)
        self.file_thread.operation_completed.connect(self.on_files_copied)
        self.file_thread.start()

    def on_files_copied(self, success, message):
        """عند انتهاء نسخ الملفات"""
        self.progress_bar.setVisible(False)

        if success:
            # إعادة تحميل قائمة الملفات
            self.refresh_files_list()
            QMessageBox.information(self, "نجح", "تم إضافة الملفات بنجاح")
        else:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الملفات: {message}")

    def refresh_files_list(self):
        """تحديث قائمة الملفات"""
        self.files_list.clear()

        # البحث عن جميع الملفات في مجلد المرفقات
        if self.attachments_folder.exists():
            for file_path in self.attachments_folder.iterdir():
                if file_path.is_file():
                    file_name = file_path.name
                    item = QListWidgetItem(file_name)
                    item.setData(Qt.UserRole, str(file_path))

                    # تحديد أيقونة حسب نوع الملف
                    file_ext = file_path.suffix.lower()
                    if file_ext in ['.pdf']:
                        item.setIcon(QIcon("icons/pdf.png"))
                    elif file_ext in ['.doc', '.docx']:
                        item.setIcon(QIcon("icons/word.png"))
                    elif file_ext in ['.xls', '.xlsx']:
                        item.setIcon(QIcon("icons/excel.png"))
                    elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                        item.setIcon(QIcon("icons/image.png"))
                    else:
                        item.setIcon(QIcon("icons/file.png"))

                    self.files_list.addItem(item)

    def remove_file(self):
        """حذف ملف محدد"""
        current_item = self.files_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف للحذف")
            return

        file_path = current_item.data(Qt.UserRole)
        file_name = current_item.text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف الملف:\n{file_name}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)

                # إزالة العنصر من القائمة
                row = self.files_list.row(current_item)
                self.files_list.takeItem(row)

                QMessageBox.information(self, "نجح", "تم حذف الملف بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الملف: {str(e)}")

    def open_file(self):
        """فتح ملف محدد"""
        current_item = self.files_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف للفتح")
            return

        file_path = current_item.data(Qt.UserRole)

        try:
            if os.path.exists(file_path):
                QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
            else:
                QMessageBox.warning(self, "تحذير", "الملف غير موجود")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الملف: {str(e)}")

    def open_folder(self):
        """فتح مجلد المرفقات"""
        try:
            QDesktopServices.openUrl(QUrl.fromLocalFile(str(self.attachments_folder)))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح المجلد: {str(e)}")

    def show_file_info(self, current, previous):
        """عرض معلومات الملف المحدد"""
        if not current:
            self.file_info.clear()
            return

        file_path = current.data(Qt.UserRole)

        if os.path.exists(file_path):
            file_stat = os.stat(file_path)
            file_size = file_stat.st_size

            # تحويل حجم الملف إلى وحدة مناسبة
            if file_size < 1024:
                size_str = f"{file_size} بايت"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} كيلوبايت"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} ميجابايت"

            # معلومات الملف
            info_text = f"""
اسم الملف: {os.path.basename(file_path)}
المسار: {file_path}
الحجم: {size_str}
نوع الملف: {os.path.splitext(file_path)[1].upper()}
تاريخ التعديل: {os.path.getmtime(file_path)}
            """

            self.file_info.setPlainText(info_text.strip())
        else:
            self.file_info.setPlainText("الملف غير موجود")

    def get_files_list(self):
        """الحصول على قائمة الملفات الحالية"""
        files = []
        for i in range(self.files_list.count()):
            item = self.files_list.item(i)
            file_path = item.data(Qt.UserRole)
            if os.path.exists(file_path):
                files.append(file_path)
        return files

    def get_attachments(self):
        """الحصول على قائمة المرفقات مع معلوماتها"""
        attachments = []
        for i in range(self.files_list.count()):
            item = self.files_list.item(i)
            file_path = item.data(Qt.UserRole)
            if os.path.exists(file_path):
                file_name = os.path.basename(file_path)
                attachments.append({
                    'name': file_name,
                    'path': file_path,
                    'type': self.document_type
                })
        return attachments
