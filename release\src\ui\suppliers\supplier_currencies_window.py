# -*- coding: utf-8 -*-
"""
نافذة إدارة ربط الموردين بالعملات
Supplier Currencies Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QGroupBox, QLabel, QPushButton, QTableWidget, QTableWidgetItem,
                               QComboBox, QDoubleSpinBox, QCheckBox, QMessageBox, QHeaderView,
                               QSplitter, QFormLayout, QFrame, QLineEdit, QSpacerItem, QSizePolicy)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QIcon

from ...database.database_manager import DatabaseManager
from ...database.models import Supplier, Currency, SupplierCurrency


class SupplierCurrenciesWidget(QWidget):
    """ويدجت إدارة ربط الموردين بالعملات"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_supplier_id = None
        self.setup_ui()
        self.load_suppliers()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        title_label = QLabel("إدارة ربط الموردين بالعملات")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء Splitter للتقسيم الأفقي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # الجانب الأيسر - اختيار المورد والعملات المرتبطة
        self.setup_left_panel(splitter)
        
        # الجانب الأيمن - إضافة/تعديل ربط العملات
        self.setup_right_panel(splitter)
        
        # تعيين نسب التقسيم
        splitter.setSizes([600, 400])
        
    def setup_left_panel(self, parent):
        """إعداد اللوحة اليسرى"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # مجموعة اختيار المورد
        supplier_group = QGroupBox("اختيار المورد")
        supplier_layout = QFormLayout(supplier_group)
        
        self.supplier_combo = QComboBox()
        self.supplier_combo.setMinimumHeight(35)
        self.supplier_combo.currentTextChanged.connect(self.on_supplier_changed)
        supplier_layout.addRow("المورد:", self.supplier_combo)
        
        left_layout.addWidget(supplier_group)
        
        # مجموعة العملات المرتبطة
        currencies_group = QGroupBox("العملات المرتبطة بالمورد")
        currencies_layout = QVBoxLayout(currencies_group)
        
        # جدول العملات المرتبطة
        self.currencies_table = QTableWidget()
        self.currencies_table.setColumnCount(6)
        self.currencies_table.setHorizontalHeaderLabels([
            "العملة", "الرمز", "أساسية", "سعر الصرف المخصص", "حد الائتمان", "الحالة"
        ])
        
        # تعيين عرض الأعمدة
        header = self.currencies_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        
        self.currencies_table.setAlternatingRowColors(True)
        self.currencies_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.currencies_table.itemDoubleClicked.connect(self.edit_currency_link)
        
        currencies_layout.addWidget(self.currencies_table)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        self.edit_button = QPushButton("تعديل")
        self.edit_button.setMinimumHeight(35)
        self.edit_button.clicked.connect(self.edit_currency_link)
        self.edit_button.setEnabled(False)
        
        self.delete_button = QPushButton("حذف الربط")
        self.delete_button.setMinimumHeight(35)
        self.delete_button.clicked.connect(self.delete_currency_link)
        self.delete_button.setEnabled(False)
        
        self.set_primary_button = QPushButton("تعيين كأساسية")
        self.set_primary_button.setMinimumHeight(35)
        self.set_primary_button.clicked.connect(self.set_primary_currency)
        self.set_primary_button.setEnabled(False)
        
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addWidget(self.delete_button)
        buttons_layout.addWidget(self.set_primary_button)
        buttons_layout.addStretch()
        
        currencies_layout.addLayout(buttons_layout)
        left_layout.addWidget(currencies_group)
        
        # ربط إشارة تحديد الصف
        self.currencies_table.itemSelectionChanged.connect(self.on_currency_selection_changed)
        
        parent.addWidget(left_widget)
        
    def setup_right_panel(self, parent):
        """إعداد اللوحة اليمنى"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # مجموعة إضافة ربط جديد
        add_group = QGroupBox("إضافة/تعديل ربط عملة")
        add_layout = QFormLayout(add_group)
        
        # اختيار العملة
        self.currency_combo = QComboBox()
        self.currency_combo.setMinimumHeight(35)
        add_layout.addRow("العملة:", self.currency_combo)
        
        # العملة الأساسية
        self.is_primary_check = QCheckBox("عملة أساسية للمورد")
        add_layout.addRow("", self.is_primary_check)
        
        # سعر الصرف المخصص
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setMinimumHeight(35)
        self.exchange_rate_spin.setDecimals(4)
        self.exchange_rate_spin.setMinimum(0.0001)
        self.exchange_rate_spin.setMaximum(999999.9999)
        self.exchange_rate_spin.setValue(1.0)
        self.exchange_rate_spin.setSpecialValueText("استخدام السعر العام")
        add_layout.addRow("سعر الصرف المخصص:", self.exchange_rate_spin)
        
        # حد الائتمان بالعملة
        self.credit_limit_spin = QDoubleSpinBox()
        self.credit_limit_spin.setMinimumHeight(35)
        self.credit_limit_spin.setDecimals(2)
        self.credit_limit_spin.setMinimum(0.0)
        self.credit_limit_spin.setMaximum(999999999.99)
        self.credit_limit_spin.setValue(0.0)
        add_layout.addRow("حد الائتمان بهذه العملة:", self.credit_limit_spin)
        
        # الحالة
        self.is_active_check = QCheckBox("نشط")
        self.is_active_check.setChecked(True)
        add_layout.addRow("", self.is_active_check)
        
        right_layout.addWidget(add_group)
        
        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ الربط")
        self.save_button.setMinimumHeight(40)
        self.save_button.clicked.connect(self.save_currency_link)
        self.save_button.setEnabled(False)
        
        self.clear_button = QPushButton("مسح")
        self.clear_button.setMinimumHeight(40)
        self.clear_button.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.clear_button)
        
        right_layout.addLayout(buttons_layout)
        
        # إضافة مساحة فارغة
        right_layout.addStretch()
        
        parent.addWidget(right_widget)
        
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter_by(is_active=True).order_by(Supplier.name).all()
            
            self.supplier_combo.clear()
            self.supplier_combo.addItem("-- اختر المورد --", None)
            
            for supplier in suppliers:
                display_text = f"{supplier.name} ({supplier.code})"
                self.supplier_combo.addItem(display_text, supplier.id)
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في تحميل الموردين:\n{str(e)}")
        finally:
            session.close()
            
    def load_currencies(self):
        """تحميل قائمة العملات"""
        session = self.db_manager.get_session()
        try:
            currencies = session.query(Currency).filter_by(is_active=True).order_by(Currency.name).all()
            
            self.currency_combo.clear()
            self.currency_combo.addItem("-- اختر العملة --", None)
            
            for currency in currencies:
                display_text = f"{currency.name} ({currency.code})"
                self.currency_combo.addItem(display_text, currency.id)
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في تحميل العملات:\n{str(e)}")
        finally:
            session.close()
            
    def on_supplier_changed(self):
        """عند تغيير المورد المحدد"""
        current_data = self.supplier_combo.currentData()
        if current_data:
            self.current_supplier_id = current_data
            self.load_supplier_currencies()
            self.load_currencies()
            self.save_button.setEnabled(True)
        else:
            self.current_supplier_id = None
            self.currencies_table.setRowCount(0)
            self.save_button.setEnabled(False)
            
    def load_supplier_currencies(self):
        """تحميل العملات المرتبطة بالمورد"""
        if not self.current_supplier_id:
            return
            
        session = self.db_manager.get_session()
        try:
            # جلب العملات المرتبطة بالمورد
            supplier_currencies = session.query(SupplierCurrency).join(Currency).filter(
                SupplierCurrency.supplier_id == self.current_supplier_id
            ).order_by(SupplierCurrency.is_primary.desc(), Currency.name).all()
            
            self.currencies_table.setRowCount(len(supplier_currencies))
            
            for row, sc in enumerate(supplier_currencies):
                # اسم العملة
                currency_item = QTableWidgetItem(sc.currency.name)
                currency_item.setData(Qt.UserRole, sc.id)
                self.currencies_table.setItem(row, 0, currency_item)
                
                # رمز العملة
                self.currencies_table.setItem(row, 1, QTableWidgetItem(sc.currency.code))
                
                # أساسية
                primary_text = "نعم" if sc.is_primary else "لا"
                primary_item = QTableWidgetItem(primary_text)
                if sc.is_primary:
                    primary_item.setBackground(Qt.green)
                self.currencies_table.setItem(row, 2, primary_item)
                
                # سعر الصرف المخصص
                rate_text = f"{sc.exchange_rate_override:.4f}" if sc.exchange_rate_override else "السعر العام"
                self.currencies_table.setItem(row, 3, QTableWidgetItem(rate_text))
                
                # حد الائتمان
                credit_text = f"{sc.credit_limit_in_currency:.2f}"
                self.currencies_table.setItem(row, 4, QTableWidgetItem(credit_text))
                
                # الحالة
                status_text = "نشط" if sc.is_active else "غير نشط"
                status_item = QTableWidgetItem(status_text)
                if sc.is_active:
                    status_item.setBackground(Qt.green)
                else:
                    status_item.setBackground(Qt.red)
                self.currencies_table.setItem(row, 5, status_item)
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في تحميل العملات:\n{str(e)}")
        finally:
            session.close()

    def on_currency_selection_changed(self):
        """عند تغيير تحديد العملة في الجدول"""
        selected_items = self.currencies_table.selectedItems()
        has_selection = len(selected_items) > 0

        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
        self.set_primary_button.setEnabled(has_selection)

    def edit_currency_link(self):
        """تعديل ربط العملة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            return

        # جلب ID الربط
        link_id = self.currencies_table.item(current_row, 0).data(Qt.UserRole)

        session = self.db_manager.get_session()
        try:
            supplier_currency = session.query(SupplierCurrency).get(link_id)
            if supplier_currency:
                # تعبئة النموذج بالبيانات الحالية
                self.currency_combo.setCurrentText(f"{supplier_currency.currency.name} ({supplier_currency.currency.code})")
                self.is_primary_check.setChecked(supplier_currency.is_primary)

                if supplier_currency.exchange_rate_override:
                    self.exchange_rate_spin.setValue(supplier_currency.exchange_rate_override)
                else:
                    self.exchange_rate_spin.setValue(0.0)

                self.credit_limit_spin.setValue(supplier_currency.credit_limit_in_currency)
                self.is_active_check.setChecked(supplier_currency.is_active)

                # تخزين ID للتعديل
                self.editing_link_id = link_id

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في جلب بيانات الربط:\n{str(e)}")
        finally:
            session.close()

    def delete_currency_link(self):
        """حذف ربط العملة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            return

        # تأكيد الحذف
        currency_name = self.currencies_table.item(current_row, 0).text()
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف ربط العملة '{currency_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            link_id = self.currencies_table.item(current_row, 0).data(Qt.UserRole)

            session = self.db_manager.get_session()
            try:
                supplier_currency = session.query(SupplierCurrency).get(link_id)
                if supplier_currency:
                    session.delete(supplier_currency)
                    session.commit()

                    QMessageBox.information(self, "تم الحذف", f"تم حذف ربط العملة '{currency_name}' بنجاح")
                    self.load_supplier_currencies()
                    self.clear_form()

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف الربط:\n{str(e)}")
            finally:
                session.close()

    def set_primary_currency(self):
        """تعيين العملة كأساسية"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            return

        link_id = self.currencies_table.item(current_row, 0).data(Qt.UserRole)
        currency_name = self.currencies_table.item(current_row, 0).text()

        session = self.db_manager.get_session()
        try:
            # إلغاء تعيين جميع العملات كأساسية لهذا المورد
            session.query(SupplierCurrency).filter_by(
                supplier_id=self.current_supplier_id
            ).update({SupplierCurrency.is_primary: False})

            # تعيين العملة المحددة كأساسية
            supplier_currency = session.query(SupplierCurrency).get(link_id)
            if supplier_currency:
                supplier_currency.is_primary = True
                session.commit()

                QMessageBox.information(self, "تم التحديث", f"تم تعيين '{currency_name}' كعملة أساسية للمورد")
                self.load_supplier_currencies()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في التحديث", f"حدث خطأ أثناء تعيين العملة الأساسية:\n{str(e)}")
        finally:
            session.close()

    def save_currency_link(self):
        """حفظ ربط العملة"""
        if not self.current_supplier_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد أولاً")
            return

        currency_id = self.currency_combo.currentData()
        if not currency_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة")
            return

        session = self.db_manager.get_session()
        try:
            # التحقق من وجود ربط مسبق
            existing_link = session.query(SupplierCurrency).filter_by(
                supplier_id=self.current_supplier_id,
                currency_id=currency_id
            ).first()

            # إذا كان هناك تعديل
            if hasattr(self, 'editing_link_id') and self.editing_link_id:
                supplier_currency = session.query(SupplierCurrency).get(self.editing_link_id)
                if not supplier_currency:
                    QMessageBox.warning(self, "خطأ", "لم يتم العثور على الربط للتعديل")
                    return
            elif existing_link:
                QMessageBox.warning(self, "تحذير", "هذا المورد مرتبط بهذه العملة مسبقاً")
                return
            else:
                # إنشاء ربط جديد
                supplier_currency = SupplierCurrency(
                    supplier_id=self.current_supplier_id,
                    currency_id=currency_id
                )
                session.add(supplier_currency)

            # تحديث البيانات
            supplier_currency.is_primary = self.is_primary_check.isChecked()
            supplier_currency.exchange_rate_override = self.exchange_rate_spin.value() if self.exchange_rate_spin.value() > 0 else None
            supplier_currency.credit_limit_in_currency = self.credit_limit_spin.value()
            supplier_currency.is_active = self.is_active_check.isChecked()

            # إذا تم تعيين هذه العملة كأساسية، إلغاء تعيين الأخريات
            if supplier_currency.is_primary:
                session.query(SupplierCurrency).filter(
                    SupplierCurrency.supplier_id == self.current_supplier_id,
                    SupplierCurrency.id != (supplier_currency.id if hasattr(supplier_currency, 'id') else 0)
                ).update({SupplierCurrency.is_primary: False})

            session.commit()

            action = "تم تحديث" if hasattr(self, 'editing_link_id') and self.editing_link_id else "تم إضافة"
            QMessageBox.information(self, "نجح الحفظ", f"{action} ربط العملة بنجاح")

            self.load_supplier_currencies()
            self.clear_form()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في الحفظ", f"حدث خطأ أثناء حفظ الربط:\n{str(e)}")
        finally:
            session.close()

    def clear_form(self):
        """مسح النموذج"""
        self.currency_combo.setCurrentIndex(0)
        self.is_primary_check.setChecked(False)
        self.exchange_rate_spin.setValue(1.0)
        self.credit_limit_spin.setValue(0.0)
        self.is_active_check.setChecked(True)

        # إزالة معرف التعديل
        if hasattr(self, 'editing_link_id'):
            delattr(self, 'editing_link_id')


class SupplierCurrenciesWindow(QMainWindow):
    """نافذة إدارة ربط الموردين بالعملات المنفصلة"""

    def __init__(self):
        super().__init__()
        self.setup_window()
        self.setup_ui()

    def setup_window(self):
        """إعداد النافذة"""
        self.setWindowTitle("إدارة ربط الموردين بالعملات - ProShipment")
        self.setMinimumSize(1200, 700)

        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()

        # تطبيق الخط العربي
        font = QFont("Segoe UI", 10)
        self.setFont(font)

        # تطبيق تخطيط RTL
        self.setLayoutDirection(Qt.RightToLeft)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء الويدجت الرئيسي
        self.currencies_widget = SupplierCurrenciesWidget()
        self.setCentralWidget(self.currencies_widget)

        # إعداد شريط الحالة
        self.statusBar().showMessage("جاهز - إدارة ربط الموردين بالعملات")
