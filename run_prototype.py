#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النموذج التجريبي المحسن للواجهة الرئيسية
Enhanced Prototype Runner for Main Window
"""

import sys
import os
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QToolBar, QStatusBar, QLabel, QPushButton, QFrame,
    QSplitter, QTabWidget, QMessageBox, QDialog, QDialogButtonBox,
    QTextEdit, QGridLayout, QGroupBox
)
from PySide6.QtCore import Qt, QTimer, QSize, QThread, Signal
from PySide6.QtGui import QFont, QIcon, QPalette, QPixmap, QPainter, QColor

# استيراد المكونات المخصصة
from main_window_prototype import GradientWidget, LogoWidget, SideMenuWidget, SystemTreeWidget
from enhanced_ui_components import (
    EnhancedMainContent, DashboardWidget, QuickActionsWidget,
    RecentActivitiesWidget, SystemStatusWidget, AnimatedButton
)
from advanced_tree_menu import AdvancedSystemTreeWidget

class AboutDialog(QDialog):
    """نافذة حول البرنامج"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("حول نظام CnX ERP")
        self.setFixedSize(400, 300)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة النافذة"""
        layout = QVBoxLayout(self)
        
        # الشعار
        logo_label = QLabel("SHIPMENT ERP")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setFont(QFont("Arial", 24, QFont.Bold))
        logo_label.setStyleSheet("color: #2E86AB; margin: 20px;")
        layout.addWidget(logo_label)
        
        # معلومات البرنامج
        info_text = """
        نظام إدارة الشحنات المتقدم
        Advanced Shipment Management System

        الإصدار: 1.0.0
        تاريخ الإصدار: 2024

        نظام شامل لإدارة الشحنات والحوالات
        مطور بتقنية Python و PySide6

        جميع الحقوق محفوظة © 2024
        """
        
        info_label = QLabel(info_text)
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok)
        buttons.accepted.connect(self.accept)
        layout.addWidget(buttons)

class SettingsDialog(QDialog):
    """نافذة الإعدادات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إعدادات النظام")
        self.setFixedSize(500, 400)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة الإعدادات"""
        layout = QVBoxLayout(self)
        
        # تبويبات الإعدادات
        tabs = QTabWidget()
        
        # تبويب عام
        general_tab = QWidget()
        general_layout = QVBoxLayout(general_tab)
        
        general_group = QGroupBox("الإعدادات العامة")
        general_group_layout = QGridLayout(general_group)
        
        general_group_layout.addWidget(QLabel("اللغة:"), 0, 0)
        general_group_layout.addWidget(QLabel("العربية"), 0, 1)
        
        general_group_layout.addWidget(QLabel("المنطقة الزمنية:"), 1, 0)
        general_group_layout.addWidget(QLabel("GMT+3"), 1, 1)
        
        general_layout.addWidget(general_group)
        general_layout.addStretch()
        
        tabs.addTab(general_tab, "عام")
        
        # تبويب قاعدة البيانات
        db_tab = QWidget()
        db_layout = QVBoxLayout(db_tab)
        
        db_group = QGroupBox("إعدادات قاعدة البيانات")
        db_group_layout = QGridLayout(db_group)
        
        db_group_layout.addWidget(QLabel("نوع قاعدة البيانات:"), 0, 0)
        db_group_layout.addWidget(QLabel("Oracle"), 0, 1)
        
        db_group_layout.addWidget(QLabel("حالة الاتصال:"), 1, 0)
        status_label = QLabel("متصل")
        status_label.setStyleSheet("color: green; font-weight: bold;")
        db_group_layout.addWidget(status_label, 1, 1)
        
        db_layout.addWidget(db_group)
        db_layout.addStretch()
        
        tabs.addTab(db_tab, "قاعدة البيانات")
        
        layout.addWidget(tabs)
        
        # أزرار الحوار
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

class EnhancedMainWindow(QMainWindow):
    """النافذة الرئيسية المحسنة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام إدارة الشحنات - SHIPMENT ERP v1.0")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
        
        # متغيرات النافذة
        self.current_view = "dashboard"
        
        self.setup_ui()
        self.setup_connections()
        self.setup_status_timer()
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.create_menu_bar()
        self.create_toolbar()
        self.create_central_widget()
        self.create_status_bar()
        self.apply_styles()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        file_menu.addAction("جديد", self.new_document)
        file_menu.addAction("فتح", self.open_document)
        file_menu.addAction("حفظ", self.save_document)
        file_menu.addSeparator()
        file_menu.addAction("خروج", self.close)
        
        # قائمة عرض
        view_menu = menubar.addMenu("عرض")
        view_menu.addAction("لوحة المعلومات", lambda: self.switch_view("dashboard"))
        view_menu.addAction("الشحنات", lambda: self.switch_view("shipments"))
        view_menu.addAction("العملاء", lambda: self.switch_view("customers"))
        view_menu.addAction("التقارير", lambda: self.switch_view("reports"))
        
        # قائمة أدوات
        tools_menu = menubar.addMenu("أدوات")
        tools_menu.addAction("إعدادات", self.show_settings)
        tools_menu.addAction("نسخ احتياطي", self.backup_data)
        tools_menu.addAction("استعادة البيانات", self.restore_data)
        
        # قائمة مساعدة
        help_menu = menubar.addMenu("مساعدة")
        help_menu.addAction("دليل المستخدم", self.show_help)
        help_menu.addAction("حول البرنامج", self.show_about)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        toolbar.setIconSize(QSize(32, 32))
        
        # أزرار شريط الأدوات
        actions = [
            ("🏠", "الرئيسية", lambda: self.switch_view("dashboard")),
            ("📦", "الشحنات", lambda: self.switch_view("shipments")),
            ("👥", "العملاء", lambda: self.switch_view("customers")),
            ("📊", "التقارير", lambda: self.switch_view("reports")),
            ("⚙️", "الإعدادات", self.show_settings),
            ("❓", "المساعدة", self.show_help)
        ]
        
        for icon, text, callback in actions:
            action = toolbar.addAction(icon + " " + text)
            action.triggered.connect(callback)
            if text in ["التقارير", "المساعدة"]:
                toolbar.addSeparator()
    
    def create_central_widget(self):
        """إنشاء المحتوى المركزي"""
        # إنشاء التبويبات الرئيسية
        self.tab_widget = QTabWidget()
        self.setCentralWidget(self.tab_widget)
        
        # تبويب لوحة المعلومات
        dashboard_tab = EnhancedMainContent()
        self.tab_widget.addTab(dashboard_tab, "🏠 لوحة المعلومات")
        
        # تبويب الشعار (النموذج الأصلي)
        logo_tab = self.create_logo_tab()
        self.tab_widget.addTab(logo_tab, "🎨 الشعار")
        
        # تبويب وهمي للشحنات
        shipments_tab = self.create_placeholder_tab("إدارة الشحنات")
        self.tab_widget.addTab(shipments_tab, "📦 الشحنات")
        
        # تبويب وهمي للعملاء
        customers_tab = self.create_placeholder_tab("إدارة العملاء")
        self.tab_widget.addTab(customers_tab, "👥 العملاء")
    
    def create_logo_tab(self):
        """إنشاء تبويب الشعار"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # المقسم
        splitter = QSplitter(Qt.Horizontal)
        
        # القائمة اليسرى - شجرة الأنظمة المتقدمة
        left_menu = AdvancedSystemTreeWidget("🏢 أنظمة SHIPMENT ERP")
        # ربط الإشارات
        left_menu.item_selected.connect(self.on_system_item_selected)
        left_menu.system_expanded.connect(self.on_system_expanded)
        splitter.addWidget(left_menu)
        
        # المنطقة المركزية مع الشعار
        center_widget = GradientWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.addStretch(1)
        
        logo = LogoWidget()
        center_layout.addWidget(logo)
        
        subtitle = QLabel("نظام إدارة الشحنات المتقدم")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setFont(QFont("Arial", 14))
        subtitle.setStyleSheet("color: #666; background: transparent;")
        center_layout.addWidget(subtitle)
        
        center_layout.addStretch(1)
        splitter.addWidget(center_widget)
        
        # القائمة اليمنى
        right_menu_items = [
            "إدارة المستخدمين", "صلاحيات النظام", "سجل العمليات",
            "النسخ الاحتياطية", "إعدادات الشبكة", "تحديثات النظام",
            "الدعم الفني", "اتصل بنا"
        ]
        right_menu = SideMenuWidget("إعدادات النظام", right_menu_items)
        splitter.addWidget(right_menu)
        
        splitter.setSizes([280, 720, 200])
        layout.addWidget(splitter)
        
        return widget
    
    def create_placeholder_tab(self, title):
        """إنشاء تبويب مؤقت"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        label = QLabel(f"قسم {title}\n\nسيتم تطوير هذا القسم قريباً...")
        label.setAlignment(Qt.AlignCenter)
        label.setFont(QFont("Arial", 16))
        label.setStyleSheet("""
            QLabel {
                background-color: #F8F9FA;
                border: 2px dashed #DEE2E6;
                border-radius: 12px;
                padding: 40px;
                color: #6C757D;
            }
        """)
        
        layout.addWidget(label)
        return widget
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()
        
        # معلومات المستخدم
        self.user_label = QLabel("المستخدم: مدير النظام")
        status_bar.addWidget(self.user_label)
        
        status_bar.addPermanentWidget(QLabel(" | "))
        
        # التاريخ والوقت
        self.time_label = QLabel()
        self.update_time()
        status_bar.addPermanentWidget(self.time_label)
        
        status_bar.addPermanentWidget(QLabel(" | "))
        
        # حالة الاتصال
        self.connection_label = QLabel("متصل بقاعدة البيانات Oracle")
        self.connection_label.setStyleSheet("color: green; font-weight: bold;")
        status_bar.addPermanentWidget(self.connection_label)
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        pass
    
    def setup_status_timer(self):
        """إعداد مؤقت تحديث شريط الحالة"""
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_time)
        self.status_timer.start(1000)  # تحديث كل ثانية
    
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"التاريخ: {current_time}")
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #F8F9FA;
            }
            QTabWidget::pane {
                border: 1px solid #DEE2E6;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #E9ECEF;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2E86AB;
            }
            QTabBar::tab:hover {
                background-color: #F8F9FA;
            }
        """)
    
    # دوال الأحداث
    def switch_view(self, view_name):
        """تبديل العرض"""
        self.current_view = view_name
        if view_name == "dashboard":
            self.tab_widget.setCurrentIndex(0)
        elif view_name == "shipments":
            self.tab_widget.setCurrentIndex(2)
        elif view_name == "customers":
            self.tab_widget.setCurrentIndex(3)
    
    def new_document(self):
        """إنشاء مستند جديد"""
        QMessageBox.information(self, "جديد", "سيتم إنشاء مستند جديد")
    
    def open_document(self):
        """فتح مستند"""
        QMessageBox.information(self, "فتح", "سيتم فتح مستند موجود")
    
    def save_document(self):
        """حفظ المستند"""
        QMessageBox.information(self, "حفظ", "تم حفظ المستند بنجاح")
    
    def show_settings(self):
        """عرض نافذة الإعدادات"""
        dialog = SettingsDialog(self)
        dialog.exec()
    
    def show_about(self):
        """عرض نافذة حول البرنامج"""
        dialog = AboutDialog(self)
        dialog.exec()
    
    def show_help(self):
        """عرض المساعدة"""
        QMessageBox.information(
            self, "المساعدة", 
            "دليل المستخدم\n\nسيتم إضافة دليل مفصل للمستخدم قريباً"
        )
    
    def backup_data(self):
        """نسخ احتياطي للبيانات"""
        QMessageBox.information(self, "نسخ احتياطي", "تم إنشاء نسخة احتياطية بنجاح")
    
    def restore_data(self):
        """استعادة البيانات"""
        QMessageBox.information(self, "استعادة", "تم استعادة البيانات بنجاح")

    def on_system_item_selected(self, system, function):
        """معالج اختيار عنصر من شجرة الأنظمة"""
        self.statusBar().showMessage(f"تم اختيار: {function} من {system}", 3000)
        print(f"النظام: {system}, الوظيفة: {function}")

        # يمكن إضافة منطق فتح النوافذ المناسبة هنا
        if "إنشاء شحنة" in function:
            self.show_create_shipment_dialog()
        elif "البحث" in function:
            self.show_search_dialog()
        elif "التقارير" in function:
            self.show_reports_dialog()

    def on_system_expanded(self, system):
        """معالج توسيع نظام"""
        self.statusBar().showMessage(f"تم فتح نظام: {system}", 2000)
        print(f"تم توسيع النظام: {system}")

    def show_create_shipment_dialog(self):
        """إظهار نافذة إنشاء شحنة جديدة"""
        QMessageBox.information(
            self, "إنشاء شحنة جديدة",
            "سيتم فتح نافذة إنشاء شحنة جديدة\n\n(قيد التطوير)"
        )

    def show_search_dialog(self):
        """إظهار نافذة البحث"""
        QMessageBox.information(
            self, "البحث",
            "سيتم فتح نافذة البحث المتقدم\n\n(قيد التطوير)"
        )

    def show_reports_dialog(self):
        """إظهار نافذة التقارير"""
        QMessageBox.information(
            self, "التقارير",
            "سيتم فتح نافذة التقارير والإحصائيات\n\n(قيد التطوير)"
        )

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد التطبيق
    app.setApplicationName("SHIPMENT ERP System")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("SHIPMENT Solutions")
    
    # إعداد الخط والاتجاه
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة الرئيسية
    window = EnhancedMainWindow()
    window.show()
    
    # رسالة ترحيب
    QMessageBox.information(
        window, "مرحباً",
        "مرحباً بك في نظام SHIPMENT ERP\n\nنموذج تجريبي للواجهة الرئيسية المطور"
    )
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
