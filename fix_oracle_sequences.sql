-- إصل<PERSON><PERSON> مشكلة الـ Sequences في Oracle
-- Fix Oracle Sequences Issue

-- إنشاء sequences للجداول الرئيسية
-- Create sequences for main tables

-- Sequence لجدول وحدات القياس
CREATE SEQUENCE units_of_measure_seq
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- Sequence لجدول مجموعات الأصناف
CREATE SEQUENCE item_groups_seq
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- Sequence لجدول الأصناف
CREATE SEQUENCE items_seq
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- Sequence لجدول العملات
CREATE SEQUENCE currencies_seq
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- Sequence لجدول إعدادات النظام
CREATE SEQUENCE system_settings_seq
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- Sequence لجدول الشركات
CREATE SEQUENCE companies_seq
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- Sequence لجدول السنوات المالية
CREATE SEQUENCE fiscal_years_seq
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- إنشاء triggers لاستخدام الـ sequences تلقائياً
-- Create triggers to use sequences automatically

-- Trigger لجدول وحدات القياس
CREATE OR REPLACE TRIGGER units_of_measure_trigger
BEFORE INSERT ON units_of_measure
FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := units_of_measure_seq.NEXTVAL;
    END IF;
END;
/

-- Trigger لجدول مجموعات الأصناف
CREATE OR REPLACE TRIGGER item_groups_trigger
BEFORE INSERT ON item_groups
FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := item_groups_seq.NEXTVAL;
    END IF;
END;
/

-- Trigger لجدول الأصناف
CREATE OR REPLACE TRIGGER items_trigger
BEFORE INSERT ON items
FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := items_seq.NEXTVAL;
    END IF;
END;
/

-- Trigger لجدول العملات
CREATE OR REPLACE TRIGGER currencies_trigger
BEFORE INSERT ON currencies
FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := currencies_seq.NEXTVAL;
    END IF;
END;
/

-- Trigger لجدول إعدادات النظام
CREATE OR REPLACE TRIGGER system_settings_trigger
BEFORE INSERT ON system_settings
FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := system_settings_seq.NEXTVAL;
    END IF;
END;
/

-- Trigger لجدول الشركات
CREATE OR REPLACE TRIGGER companies_trigger
BEFORE INSERT ON companies
FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := companies_seq.NEXTVAL;
    END IF;
END;
/

-- Trigger لجدول السنوات المالية
CREATE OR REPLACE TRIGGER fiscal_years_trigger
BEFORE INSERT ON fiscal_years
FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := fiscal_years_seq.NEXTVAL;
    END IF;
END;
/

-- الآن إدراج البيانات الأساسية
-- Now insert basic data

-- إدراج وحدات القياس الأساسية
INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
VALUES ('قطعة', 'Piece', 'قطعة', 'pcs', 'وحدة العد الأساسية', 1, SYSDATE, SYSDATE);

INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
VALUES ('كيلوجرام', 'Kilogram', 'كجم', 'kg', 'وحدة قياس الوزن الأساسية', 1, SYSDATE, SYSDATE);

INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
VALUES ('جرام', 'Gram', 'جم', 'g', 'وحدة قياس الوزن الصغيرة', 1, SYSDATE, SYSDATE);

INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
VALUES ('متر', 'Meter', 'م', 'm', 'وحدة قياس الطول الأساسية', 1, SYSDATE, SYSDATE);

INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
VALUES ('لتر', 'Liter', 'لتر', 'L', 'وحدة قياس السوائل', 1, SYSDATE, SYSDATE);

INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
VALUES ('صندوق', 'Box', 'صندوق', 'box', 'وحدة التعبئة الكبيرة', 1, SYSDATE, SYSDATE);

INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
VALUES ('زوج', 'Pair', 'زوج', 'pair', 'وحدة للأشياء المزدوجة', 1, SYSDATE, SYSDATE);

-- إدراج مجموعات الأصناف الأساسية
INSERT INTO item_groups (name, name_en, description, is_active, created_at, updated_at)
VALUES ('إلكترونيات', 'Electronics', 'الأجهزة والمعدات الإلكترونية', 1, SYSDATE, SYSDATE);

INSERT INTO item_groups (name, name_en, description, is_active, created_at, updated_at)
VALUES ('ملابس', 'Clothing', 'الملابس والأزياء', 1, SYSDATE, SYSDATE);

INSERT INTO item_groups (name, name_en, description, is_active, created_at, updated_at)
VALUES ('أغذية', 'Food', 'المواد الغذائية والمشروبات', 1, SYSDATE, SYSDATE);

INSERT INTO item_groups (name, name_en, description, is_active, created_at, updated_at)
VALUES ('أدوات منزلية', 'Home Appliances', 'الأدوات والأجهزة المنزلية', 1, SYSDATE, SYSDATE);

-- إدراج العملات الأساسية
INSERT INTO currencies (code, name, name_en, symbol, exchange_rate, is_base, is_active, created_at, updated_at)
VALUES ('SAR', 'ريال سعودي', 'Saudi Riyal', 'ر.س', 1.0, 1, 1, SYSDATE, SYSDATE);

INSERT INTO currencies (code, name, name_en, symbol, exchange_rate, is_base, is_active, created_at, updated_at)
VALUES ('USD', 'دولار أمريكي', 'US Dollar', '$', 3.75, 0, 1, SYSDATE, SYSDATE);

-- إدراج إعدادات النظام الأساسية
INSERT INTO system_settings (key, value, description, category, data_type, is_system, created_at, updated_at)
VALUES ('app_name', 'نظام إدارة الشحنات', 'اسم التطبيق', 'general', 'string', 0, SYSDATE, SYSDATE);

INSERT INTO system_settings (key, value, description, category, data_type, is_system, created_at, updated_at)
VALUES ('app_version', '2.0.0', 'إصدار التطبيق', 'general', 'string', 0, SYSDATE, SYSDATE);

INSERT INTO system_settings (key, value, description, category, data_type, is_system, created_at, updated_at)
VALUES ('database_type', 'ORACLE', 'نوع قاعدة البيانات', 'system', 'string', 1, SYSDATE, SYSDATE);

-- إدراج بيانات الشركة الافتراضية
INSERT INTO companies (name, name_en, address, phone, email, is_active, created_at, updated_at)
VALUES ('شركة الشحنات المتقدمة', 'Advanced Shipping Company', 'الرياض، المملكة العربية السعودية', '+966-11-1234567', '<EMAIL>', 1, SYSDATE, SYSDATE);

-- إدراج السنة المالية الحالية
INSERT INTO fiscal_years (year, start_date, end_date, is_current, created_at, updated_at)
VALUES (EXTRACT(YEAR FROM SYSDATE), 
        TO_DATE('01/01/' || EXTRACT(YEAR FROM SYSDATE), 'DD/MM/YYYY'),
        TO_DATE('31/12/' || EXTRACT(YEAR FROM SYSDATE), 'DD/MM/YYYY'),
        1, SYSDATE, SYSDATE);

-- تأكيد التغييرات
COMMIT;

-- عرض النتائج
SELECT 'Sequences Created' as status FROM dual;

SELECT 'Units of Measure' as table_name, COUNT(*) as record_count FROM units_of_measure
UNION ALL
SELECT 'Item Groups', COUNT(*) FROM item_groups
UNION ALL
SELECT 'Currencies', COUNT(*) FROM currencies
UNION ALL
SELECT 'System Settings', COUNT(*) FROM system_settings
UNION ALL
SELECT 'Companies', COUNT(*) FROM companies
UNION ALL
SELECT 'Fiscal Years', COUNT(*) FROM fiscal_years;

-- عرض وحدات القياس المدرجة
SELECT 'وحدات القياس:' as info FROM dual;
SELECT id, name, symbol, name_en FROM units_of_measure ORDER BY id;

-- عرض مجموعات الأصناف المدرجة
SELECT 'مجموعات الأصناف:' as info FROM dual;
SELECT id, name, name_en FROM item_groups ORDER BY id;
