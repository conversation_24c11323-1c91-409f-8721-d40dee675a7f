#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات Oracle المتقدمة - ProShipment V2.0.0
Advanced Oracle Configuration
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

class DatabaseType(Enum):
    """أنواع قواعد البيانات المدعومة"""
    SQLITE = "sqlite"
    ORACLE = "oracle"
    POSTGRESQL = "postgresql"  # للمستقبل
    MYSQL = "mysql"           # للمستقبل

class OracleConnectionType(Enum):
    """أنواع اتصال Oracle"""
    SERVICE_NAME = "service_name"
    SID = "sid"
    TNS = "tns"

@dataclass
class OracleConfig:
    """إعدادات اتصال Oracle"""
    host: str = "localhost"
    port: int = 1521
    service_name: Optional[str] = "XE"
    sid: Optional[str] = None
    username: str = "proshipment"
    password: str = ""
    connection_type: OracleConnectionType = OracleConnectionType.SERVICE_NAME
    
    # إعدادات الأداء
    pool_size: int = 20
    max_overflow: int = 30
    pool_timeout: int = 30
    pool_recycle: int = 3600
    pool_pre_ping: bool = True
    
    # إعدادات الأمان
    use_ssl: bool = False
    ssl_cert_path: Optional[str] = None
    wallet_location: Optional[str] = None
    
    # إعدادات الترميز
    encoding: str = "UTF-8"
    nencoding: str = "UTF-8"
    
    # إعدادات إضافية
    threaded: bool = True
    auto_commit: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        data = asdict(self)
        # تحويل enum إلى string
        if 'connection_type' in data:
            data['connection_type'] = data['connection_type'].value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OracleConfig':
        """إنشاء من قاموس"""
        # تحويل connection_type إلى enum
        if 'connection_type' in data and isinstance(data['connection_type'], str):
            data['connection_type'] = OracleConnectionType(data['connection_type'])
        return cls(**data)

@dataclass
class SQLiteConfig:
    """إعدادات اتصال SQLite"""
    path: str = "data/proshipment.db"
    timeout: int = 30
    check_same_thread: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SQLiteConfig':
        """إنشاء من قاموس"""
        return cls(**data)

@dataclass
class DatabaseConfig:
    """إعدادات قاعدة البيانات الشاملة"""
    type: DatabaseType = DatabaseType.SQLITE
    sqlite_config: Optional[SQLiteConfig] = None
    oracle_config: Optional[OracleConfig] = None
    
    def __post_init__(self):
        """تهيئة الإعدادات الافتراضية"""
        if self.sqlite_config is None:
            self.sqlite_config = SQLiteConfig()
        if self.oracle_config is None:
            self.oracle_config = OracleConfig()
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'type': self.type.value,
            'sqlite_config': self.sqlite_config.to_dict() if self.sqlite_config else None,
            'oracle_config': self.oracle_config.to_dict() if self.oracle_config else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DatabaseConfig':
        """إنشاء من قاموس"""
        db_type = DatabaseType(data.get('type', 'sqlite'))
        sqlite_config = None
        oracle_config = None
        
        if data.get('sqlite_config'):
            sqlite_config = SQLiteConfig.from_dict(data['sqlite_config'])
        if data.get('oracle_config'):
            oracle_config = OracleConfig.from_dict(data['oracle_config'])
        
        return cls(
            type=db_type,
            sqlite_config=sqlite_config,
            oracle_config=oracle_config
        )

class DatabaseConfigManager:
    """مدير إعدادات قاعدة البيانات"""
    
    def __init__(self, config_file: str = "config/database.json"):
        self.config_file = Path(config_file)
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        self._config: Optional[DatabaseConfig] = None
    
    def load_config(self) -> DatabaseConfig:
        """تحميل الإعدادات من الملف"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self._config = DatabaseConfig.from_dict(data)
                print(f"✅ تم تحميل إعدادات قاعدة البيانات من: {self.config_file}")
            except Exception as e:
                print(f"⚠️ خطأ في تحميل الإعدادات: {e}")
                self._config = self._create_default_config()
        else:
            self._config = self._create_default_config()
            self.save_config()
        
        return self._config
    
    def save_config(self) -> bool:
        """حفظ الإعدادات إلى الملف"""
        if self._config is None:
            return False
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config.to_dict(), f, indent=2, ensure_ascii=False)
            print(f"✅ تم حفظ إعدادات قاعدة البيانات إلى: {self.config_file}")
            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ الإعدادات: {e}")
            return False
    
    def _create_default_config(self) -> DatabaseConfig:
        """إنشاء إعدادات افتراضية"""
        return DatabaseConfig(
            type=DatabaseType.SQLITE,
            sqlite_config=SQLiteConfig(),
            oracle_config=OracleConfig()
        )
    
    def get_config(self) -> DatabaseConfig:
        """الحصول على الإعدادات الحالية"""
        if self._config is None:
            return self.load_config()
        return self._config
    
    def set_database_type(self, db_type: DatabaseType) -> bool:
        """تعيين نوع قاعدة البيانات"""
        if self._config is None:
            self.load_config()
        
        self._config.type = db_type
        return self.save_config()
    
    def update_oracle_config(self, oracle_config: OracleConfig) -> bool:
        """تحديث إعدادات Oracle"""
        if self._config is None:
            self.load_config()
        
        self._config.oracle_config = oracle_config
        return self.save_config()
    
    def update_sqlite_config(self, sqlite_config: SQLiteConfig) -> bool:
        """تحديث إعدادات SQLite"""
        if self._config is None:
            self.load_config()
        
        self._config.sqlite_config = sqlite_config
        return self.save_config()

def load_oracle_config_from_env() -> OracleConfig:
    """تحميل إعدادات Oracle من متغيرات البيئة"""
    return OracleConfig(
        host=os.getenv('ORACLE_HOST', 'localhost'),
        port=int(os.getenv('ORACLE_PORT', '1521')),
        service_name=os.getenv('ORACLE_SERVICE_NAME', 'XE'),
        sid=os.getenv('ORACLE_SID'),
        username=os.getenv('ORACLE_USERNAME', 'proshipment'),
        password=os.getenv('ORACLE_PASSWORD', ''),
        connection_type=OracleConnectionType(
            os.getenv('ORACLE_CONNECTION_TYPE', 'service_name')
        ),
        pool_size=int(os.getenv('ORACLE_POOL_SIZE', '20')),
        max_overflow=int(os.getenv('ORACLE_MAX_OVERFLOW', '30')),
        use_ssl=os.getenv('ORACLE_USE_SSL', 'false').lower() == 'true',
        wallet_location=os.getenv('ORACLE_WALLET_LOCATION')
    )

def create_sample_configs():
    """إنشاء ملفات إعدادات نموذجية"""
    
    # إعدادات التطوير
    dev_config = DatabaseConfig(
        type=DatabaseType.SQLITE,
        sqlite_config=SQLiteConfig(path="data/proshipment_dev.db")
    )
    
    # إعدادات الإنتاج
    prod_config = DatabaseConfig(
        type=DatabaseType.ORACLE,
        oracle_config=OracleConfig(
            host="production-oracle-server",
            port=1521,
            service_name="PROSHIP_PROD",
            username="proshipment_prod",
            password="",  # يجب تعيينها من متغيرات البيئة
            pool_size=50,
            max_overflow=100,
            use_ssl=True
        )
    )
    
    # إعدادات الاختبار
    test_config = DatabaseConfig(
        type=DatabaseType.ORACLE,
        oracle_config=OracleConfig(
            host="test-oracle-server",
            port=1521,
            service_name="PROSHIP_TEST",
            username="proshipment_test",
            password="test_password",
            pool_size=10,
            max_overflow=20
        )
    )
    
    # حفظ الإعدادات
    configs = {
        "development": dev_config,
        "production": prod_config,
        "testing": test_config
    }
    
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    for env_name, config in configs.items():
        config_file = config_dir / f"database_{env_name}.json"
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config.to_dict(), f, indent=2, ensure_ascii=False)
            print(f"✅ تم إنشاء ملف الإعدادات: {config_file}")
        except Exception as e:
            print(f"❌ خطأ في إنشاء {config_file}: {e}")

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء ملفات الإعدادات النموذجية
    create_sample_configs()
    
    # اختبار مدير الإعدادات
    config_manager = DatabaseConfigManager()
    config = config_manager.load_config()
    
    print(f"نوع قاعدة البيانات الحالي: {config.type.value}")
    print(f"إعدادات SQLite: {config.sqlite_config.to_dict()}")
    print(f"إعدادات Oracle: {config.oracle_config.to_dict()}")
