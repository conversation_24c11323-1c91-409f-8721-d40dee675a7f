# متطلبات نظام CnX ERP - النموذج التجريبي
# CnX ERP System Requirements - Prototype Version

# مكتبة واجهة المستخدم الرئيسية
# Main UI Library
PySide6>=6.5.0

# مكتبات إضافية للتطوير المستقبلي
# Additional libraries for future development

# للرسوم البيانية (اختياري)
# For charts and graphs (optional)
# matplotlib>=3.7.0
# plotly>=5.15.0

# لقاعدة البيانات Oracle (سيتم إضافتها لاحقاً)
# For Oracle database (to be added later)
# cx_Oracle>=8.3.0
# oracledb>=1.4.0

# للتقارير (اختياري)
# For reports (optional)
# reportlab>=4.0.0
# openpyxl>=3.1.0

# للتاريخ والوقت المحسن
# For enhanced date/time handling
# python-dateutil>=2.8.0

# للتشفير والأمان (للمراحل المتقدمة)
# For encryption and security (for advanced stages)
# cryptography>=41.0.0
# bcrypt>=4.0.0
