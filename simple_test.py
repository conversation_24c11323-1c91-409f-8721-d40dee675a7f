#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_connection():
    try:
        print("Testing imports...")
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        from src.database.models import UnitOfMeasure, ItemGroup
        print("Imports successful")
        
        print("Loading config...")
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        print(f"Config loaded: {config.type.value}")
        
        print("Creating database manager...")
        db_manager = UniversalDatabaseManager(config)
        print("Database manager created")
        
        print("Testing connection...")
        if db_manager.test_connection():
            print("Connection successful")
            
            print("Checking units count...")
            with db_manager.get_session() as session:
                units_count = session.query(UnitOfMeasure).count()
                groups_count = session.query(ItemGroup).count()
                print(f"Units: {units_count}, Groups: {groups_count}")
                
                if units_count == 0:
                    print("No units found, adding sample unit...")
                    sample_unit = UnitOfMeasure(
                        name="قطعة",
                        name_en="Piece",
                        symbol="قطعة",
                        symbol_en="pcs",
                        description="وحدة العد الأساسية"
                    )
                    session.add(sample_unit)
                    print("Sample unit added")
                
                if groups_count == 0:
                    print("No groups found, adding sample group...")
                    sample_group = ItemGroup(
                        name="إلكترونيات",
                        name_en="Electronics",
                        description="الأجهزة والمعدات الإلكترونية"
                    )
                    session.add(sample_group)
                    print("Sample group added")
                    
            print("Test completed successfully")
            return True
        else:
            print("Connection failed")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_connection()
    print(f"Result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
