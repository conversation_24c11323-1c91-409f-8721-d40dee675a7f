{"type": "oracle", "sqlite_config": {"path": "data/proshipment.db", "timeout": 30, "check_same_thread": false}, "oracle_config": {"host": "test-oracle-server", "port": 1521, "service_name": "PROSHIP_TEST", "sid": null, "username": "proshipment_test", "password": "test_password", "connection_type": "service_name", "pool_size": 10, "max_overflow": 20, "pool_timeout": 30, "pool_recycle": 3600, "pool_pre_ping": true, "use_ssl": false, "ssl_cert_path": null, "wallet_location": null, "encoding": "UTF-8", "nencoding": "UTF-8", "threaded": true, "auto_commit": false}}