# -*- coding: utf-8 -*-
"""
ويدجت إدارة حسابات الموردين
Supplier Accounts Management Widget
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QLineEdit, QComboBox, QDateEdit,
                               QDoubleSpinBox, QTableWidget, QTableWidgetItem, QHeaderView,
                               QAbstractItemView, QGroupBox, QFormLayout, QMessageBox,
                               QDialog, QDialogButtonBox, QSplitter, QFrame, QTabWidget,
                               QTextEdit, QProgressBar, QTreeWidget, QTreeWidgetItem)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QFont, QIcon, QPalette, QColor

from ...database.database_manager import DatabaseManager
from ...database.models import (SupplierAccount, SupplierTransaction, Supplier, Currency, 
                                TransactionType, Remittance)


class SupplierAccountsWidget(QWidget):
    """ويدجت إدارة حسابات الموردين"""
    
    # الإشارات
    account_created = Signal(dict)
    account_updated = Signal(dict)
    transaction_created = Signal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.current_account = None
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        self.create_toolbar()
        layout.addWidget(self.toolbar_frame)
        
        # المحتوى الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # الجانب الأيسر - قائمة الحسابات
        self.create_accounts_list()
        splitter.addWidget(self.accounts_group)
        
        # الجانب الأيمن - تفاصيل الحساب
        self.create_account_details()
        splitter.addWidget(self.details_group)
        
        # تعيين النسب
        splitter.setSizes([400, 600])
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar_frame = QFrame()
        self.toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_layout = QHBoxLayout(self.toolbar_frame)
        
        # أزرار العمليات
        self.new_account_btn = QPushButton("➕ حساب جديد")
        self.new_account_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e7e34, stop:1 #155724);
            }
        """)
        
        self.edit_account_btn = QPushButton("✏️ تعديل")
        self.edit_account_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffc107, stop:1 #e0a800);
                border: none;
                border-radius: 6px;
                color: #212529;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e0a800, stop:1 #d39e00);
            }
        """)
        
        self.new_transaction_btn = QPushButton("💰 معاملة جديدة")
        self.new_transaction_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0056b3, stop:1 #004085);
            }
        """)
        
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #17a2b8, stop:1 #138496);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #138496, stop:1 #117a8b);
            }
        """)
        
        # إضافة الأزرار
        toolbar_layout.addWidget(self.new_account_btn)
        toolbar_layout.addWidget(self.edit_account_btn)
        toolbar_layout.addWidget(self.new_transaction_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addStretch()
        
        # شريط البحث
        search_label = QLabel("🔍 بحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث باسم المورد أو العملة...")
        self.search_edit.setMaximumWidth(300)
        
        toolbar_layout.addWidget(search_label)
        toolbar_layout.addWidget(self.search_edit)
        
    def create_accounts_list(self):
        """إنشاء قائمة الحسابات"""
        self.accounts_group = QGroupBox("📊 حسابات الموردين")
        accounts_layout = QVBoxLayout(self.accounts_group)
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_layout = QGridLayout(stats_frame)
        
        self.total_accounts_label = QLabel("إجمالي الحسابات: 0")
        self.total_balance_label = QLabel("إجمالي الأرصدة: 0.000")
        self.active_accounts_label = QLabel("الحسابات النشطة: 0")
        self.total_credit_label = QLabel("إجمالي الائتمان: 0.000")
        
        stats_layout.addWidget(self.total_accounts_label, 0, 0)
        stats_layout.addWidget(self.total_balance_label, 0, 1)
        stats_layout.addWidget(self.active_accounts_label, 1, 0)
        stats_layout.addWidget(self.total_credit_label, 1, 1)
        
        accounts_layout.addWidget(stats_frame)
        
        # الجدول
        self.accounts_table = QTableWidget()
        self.accounts_table.setColumnCount(7)
        self.accounts_table.setHorizontalHeaderLabels([
            "المورد", "العملة", "رقم الحساب", "الرصيد", "حد الائتمان", 
            "الائتمان المتاح", "الحالة"
        ])
        
        # إعداد الجدول
        header = self.accounts_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.accounts_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.accounts_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.accounts_table.setAlternatingRowColors(True)
        self.accounts_table.setSortingEnabled(True)
        
        accounts_layout.addWidget(self.accounts_table)
        
    def create_account_details(self):
        """إنشاء تفاصيل الحساب"""
        self.details_group = QGroupBox("📋 تفاصيل الحساب")
        details_layout = QVBoxLayout(self.details_group)
        
        # تبويبات التفاصيل
        self.details_tab = QTabWidget()
        details_layout.addWidget(self.details_tab)
        
        # تبويب معلومات الحساب
        self.create_account_info_tab()
        self.details_tab.addTab(self.info_widget, "📄 معلومات الحساب")
        
        # تبويب المعاملات
        self.create_transactions_tab()
        self.details_tab.addTab(self.transactions_widget, "💰 المعاملات")
        
        # تبويب الإحصائيات
        self.create_statistics_tab()
        self.details_tab.addTab(self.statistics_widget, "📈 الإحصائيات")
        
    def create_account_info_tab(self):
        """إنشاء تبويب معلومات الحساب"""
        self.info_widget = QWidget()
        info_layout = QFormLayout(self.info_widget)
        
        # معلومات أساسية
        self.info_supplier_label = QLabel("-")
        self.info_currency_label = QLabel("-")
        self.info_account_number_label = QLabel("-")
        self.info_balance_label = QLabel("-")
        self.info_credit_limit_label = QLabel("-")
        self.info_available_credit_label = QLabel("-")
        self.info_total_remittances_label = QLabel("-")
        self.info_total_payments_label = QLabel("-")
        self.info_last_transaction_label = QLabel("-")
        self.info_status_label = QLabel("-")
        self.info_created_at_label = QLabel("-")
        
        # إضافة الحقول
        info_layout.addRow("المورد:", self.info_supplier_label)
        info_layout.addRow("العملة:", self.info_currency_label)
        info_layout.addRow("رقم الحساب:", self.info_account_number_label)
        info_layout.addRow("الرصيد الحالي:", self.info_balance_label)
        info_layout.addRow("حد الائتمان:", self.info_credit_limit_label)
        info_layout.addRow("الائتمان المتاح:", self.info_available_credit_label)
        info_layout.addRow("إجمالي الحوالات:", self.info_total_remittances_label)
        info_layout.addRow("إجمالي المدفوعات:", self.info_total_payments_label)
        info_layout.addRow("آخر معاملة:", self.info_last_transaction_label)
        info_layout.addRow("الحالة:", self.info_status_label)
        info_layout.addRow("تاريخ الإنشاء:", self.info_created_at_label)
        
    def create_transactions_tab(self):
        """إنشاء تبويب المعاملات"""
        self.transactions_widget = QWidget()
        transactions_layout = QVBoxLayout(self.transactions_widget)
        
        # شريط تصفية المعاملات
        filter_frame = QFrame()
        filter_layout = QHBoxLayout(filter_frame)
        
        # تصفية نوع المعاملة
        type_label = QLabel("نوع المعاملة:")
        self.transaction_type_filter = QComboBox()
        self.transaction_type_filter.addItem("جميع الأنواع", "")
        for trans_type in TransactionType:
            self.transaction_type_filter.addItem(trans_type.value, trans_type.name)
            
        # تصفية التاريخ
        date_label = QLabel("من تاريخ:")
        self.trans_date_from = QDateEdit()
        self.trans_date_from.setDate(QDate.currentDate().addDays(-30))
        self.trans_date_from.setCalendarPopup(True)
        
        to_date_label = QLabel("إلى تاريخ:")
        self.trans_date_to = QDateEdit()
        self.trans_date_to.setDate(QDate.currentDate())
        self.trans_date_to.setCalendarPopup(True)
        
        # زر التصفية
        self.filter_transactions_btn = QPushButton("🔍 تصفية")
        
        filter_layout.addWidget(type_label)
        filter_layout.addWidget(self.transaction_type_filter)
        filter_layout.addWidget(date_label)
        filter_layout.addWidget(self.trans_date_from)
        filter_layout.addWidget(to_date_label)
        filter_layout.addWidget(self.trans_date_to)
        filter_layout.addWidget(self.filter_transactions_btn)
        filter_layout.addStretch()
        
        transactions_layout.addWidget(filter_frame)
        
        # جدول المعاملات
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(8)
        self.transactions_table.setHorizontalHeaderLabels([
            "رقم المعاملة", "النوع", "المبلغ", "الرصيد قبل", "الرصيد بعد",
            "التاريخ", "الوصف", "المرجع"
        ])
        
        # إعداد الجدول
        header = self.transactions_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.transactions_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.transactions_table.setAlternatingRowColors(True)
        self.transactions_table.setSortingEnabled(True)
        
        transactions_layout.addWidget(self.transactions_table)
        
    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        self.statistics_widget = QWidget()
        stats_layout = QVBoxLayout(self.statistics_widget)
        
        # إحصائيات شهرية
        monthly_group = QGroupBox("📊 الإحصائيات الشهرية")
        monthly_layout = QGridLayout(monthly_group)
        
        self.monthly_remittances_label = QLabel("الحوالات هذا الشهر: 0")
        self.monthly_payments_label = QLabel("المدفوعات هذا الشهر: 0.000")
        self.monthly_transactions_label = QLabel("المعاملات هذا الشهر: 0")
        self.avg_transaction_label = QLabel("متوسط المعاملة: 0.000")
        
        monthly_layout.addWidget(self.monthly_remittances_label, 0, 0)
        monthly_layout.addWidget(self.monthly_payments_label, 0, 1)
        monthly_layout.addWidget(self.monthly_transactions_label, 1, 0)
        monthly_layout.addWidget(self.avg_transaction_label, 1, 1)
        
        stats_layout.addWidget(monthly_group)
        
        # إحصائيات سنوية
        yearly_group = QGroupBox("📈 الإحصائيات السنوية")
        yearly_layout = QGridLayout(yearly_group)
        
        self.yearly_remittances_label = QLabel("الحوالات هذا العام: 0")
        self.yearly_payments_label = QLabel("المدفوعات هذا العام: 0.000")
        self.yearly_transactions_label = QLabel("المعاملات هذا العام: 0")
        self.growth_rate_label = QLabel("معدل النمو: 0%")
        
        yearly_layout.addWidget(self.yearly_remittances_label, 0, 0)
        yearly_layout.addWidget(self.yearly_payments_label, 0, 1)
        yearly_layout.addWidget(self.yearly_transactions_label, 1, 0)
        yearly_layout.addWidget(self.growth_rate_label, 1, 1)
        
        stats_layout.addWidget(yearly_group)
        
        # مساحة فارغة
        stats_layout.addStretch()

    def setup_connections(self):
        """إعداد الاتصالات"""
        # أزرار العمليات
        self.new_account_btn.clicked.connect(self.new_account)
        self.edit_account_btn.clicked.connect(self.edit_account)
        self.new_transaction_btn.clicked.connect(self.new_transaction)
        self.refresh_btn.clicked.connect(self.refresh_data)

        # البحث
        self.search_edit.textChanged.connect(self.filter_accounts)

        # الجدول
        self.accounts_table.itemSelectionChanged.connect(self.on_account_selection_changed)

        # تصفية المعاملات
        self.filter_transactions_btn.clicked.connect(self.filter_transactions)

    def load_data(self):
        """تحميل البيانات"""
        try:
            self.load_accounts()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}")

    def load_accounts(self):
        """تحميل قائمة الحسابات"""
        try:
            session = self.db_manager.get_session()
            accounts = session.query(SupplierAccount).all()

            self.accounts_table.setRowCount(len(accounts))

            total_balance = 0
            total_credit = 0
            active_count = 0

            for row, account in enumerate(accounts):
                # المورد
                supplier_name = account.supplier.name if account.supplier else ""
                self.accounts_table.setItem(row, 0, QTableWidgetItem(supplier_name))

                # العملة
                currency_code = account.currency.code if account.currency else ""
                self.accounts_table.setItem(row, 1, QTableWidgetItem(currency_code))

                # رقم الحساب
                self.accounts_table.setItem(row, 2, QTableWidgetItem(account.account_number or ""))

                # الرصيد
                balance_item = QTableWidgetItem(f"{account.balance:.3f}")
                balance_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                if account.balance < 0:
                    balance_item.setBackground(QColor("#f8d7da"))
                elif account.balance > 0:
                    balance_item.setBackground(QColor("#d4edda"))
                self.accounts_table.setItem(row, 3, balance_item)

                # حد الائتمان
                credit_item = QTableWidgetItem(f"{account.credit_limit:.3f}")
                credit_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.accounts_table.setItem(row, 4, credit_item)

                # الائتمان المتاح
                available_credit = account.available_credit or 0
                available_item = QTableWidgetItem(f"{available_credit:.3f}")
                available_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.accounts_table.setItem(row, 5, available_item)

                # الحالة
                status_item = QTableWidgetItem("نشط" if account.is_active else "غير نشط")
                if account.is_active:
                    status_item.setBackground(QColor("#d4edda"))
                    active_count += 1
                else:
                    status_item.setBackground(QColor("#f8d7da"))
                self.accounts_table.setItem(row, 6, status_item)

                # حفظ معرف الحساب
                self.accounts_table.item(row, 0).setData(Qt.UserRole, account.id)

                # إضافة للإجماليات
                total_balance += float(account.balance or 0)
                total_credit += float(account.credit_limit or 0)

            # تحديث الإحصائيات
            self.total_accounts_label.setText(f"إجمالي الحسابات: {len(accounts)}")
            self.total_balance_label.setText(f"إجمالي الأرصدة: {total_balance:,.3f}")
            self.active_accounts_label.setText(f"الحسابات النشطة: {active_count}")
            self.total_credit_label.setText(f"إجمالي الائتمان: {total_credit:,.3f}")

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الحسابات:\n{str(e)}")

    def filter_accounts(self):
        """تصفية الحسابات حسب النص المدخل"""
        search_text = self.search_edit.text().lower()

        for row in range(self.accounts_table.rowCount()):
            show_row = False

            # البحث في اسم المورد والعملة
            for col in [0, 1]:
                item = self.accounts_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break

            self.accounts_table.setRowHidden(row, not show_row)

    def on_account_selection_changed(self):
        """عند تغيير التحديد في جدول الحسابات"""
        current_row = self.accounts_table.currentRow()

        if current_row >= 0:
            # الحصول على معرف الحساب
            account_id = self.accounts_table.item(current_row, 0).data(Qt.UserRole)
            if account_id:
                self.load_account_details(account_id)
                self.edit_account_btn.setEnabled(True)
                self.new_transaction_btn.setEnabled(True)
        else:
            self.clear_account_details()
            self.edit_account_btn.setEnabled(False)
            self.new_transaction_btn.setEnabled(False)

    def load_account_details(self, account_id):
        """تحميل تفاصيل الحساب"""
        try:
            session = self.db_manager.get_session()
            account = session.query(SupplierAccount).get(account_id)

            if not account:
                session.close()
                return

            self.current_account = account_id

            # تحديث معلومات الحساب
            self.update_account_info(account)

            # تحميل المعاملات
            self.load_account_transactions(account_id)

            # تحديث الإحصائيات
            self.update_account_statistics(account_id)

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل تفاصيل الحساب:\n{str(e)}")

    def update_account_info(self, account):
        """تحديث معلومات الحساب"""
        self.info_supplier_label.setText(account.supplier.name if account.supplier else "-")
        self.info_currency_label.setText(account.currency.code if account.currency else "-")
        self.info_account_number_label.setText(account.account_number or "-")
        self.info_balance_label.setText(f"{account.balance:.3f}" if account.balance else "0.000")
        self.info_credit_limit_label.setText(f"{account.credit_limit:.3f}" if account.credit_limit else "0.000")
        self.info_available_credit_label.setText(f"{account.available_credit:.3f}" if account.available_credit else "0.000")
        self.info_total_remittances_label.setText(f"{account.total_remittances:.3f}" if account.total_remittances else "0.000")
        self.info_total_payments_label.setText(f"{account.total_payments:.3f}" if account.total_payments else "0.000")

        # آخر معاملة
        if account.last_transaction_date:
            self.info_last_transaction_label.setText(account.last_transaction_date.strftime("%Y-%m-%d"))
        else:
            self.info_last_transaction_label.setText("-")

        self.info_status_label.setText("نشط" if account.is_active else "غير نشط")
        self.info_created_at_label.setText(account.created_at.strftime("%Y-%m-%d %H:%M:%S") if account.created_at else "-")

    def load_account_transactions(self, account_id):
        """تحميل معاملات الحساب"""
        try:
            session = self.db_manager.get_session()

            # تطبيق التصفية
            query = session.query(SupplierTransaction).filter(
                SupplierTransaction.supplier_account_id == account_id
            )

            # تصفية نوع المعاملة
            trans_type_filter = self.transaction_type_filter.currentData()
            if trans_type_filter:
                trans_type_enum = getattr(TransactionType, trans_type_filter)
                query = query.filter(SupplierTransaction.transaction_type == trans_type_enum)

            # تصفية التاريخ
            date_from = self.trans_date_from.date().toPython()
            date_to = self.trans_date_to.date().toPython()
            query = query.filter(SupplierTransaction.transaction_date >= date_from)
            query = query.filter(SupplierTransaction.transaction_date <= date_to)

            transactions = query.order_by(SupplierTransaction.created_at.desc()).all()

            self.transactions_table.setRowCount(len(transactions))

            for row, transaction in enumerate(transactions):
                # رقم المعاملة
                self.transactions_table.setItem(row, 0, QTableWidgetItem(transaction.transaction_number))

                # النوع
                trans_type = transaction.transaction_type.value if transaction.transaction_type else ""
                type_item = QTableWidgetItem(trans_type)

                # تلوين حسب النوع
                if transaction.transaction_type == TransactionType.CREDIT:
                    type_item.setBackground(QColor("#d4edda"))
                elif transaction.transaction_type == TransactionType.DEBIT:
                    type_item.setBackground(QColor("#f8d7da"))
                elif transaction.transaction_type == TransactionType.REMITTANCE:
                    type_item.setBackground(QColor("#d1ecf1"))

                self.transactions_table.setItem(row, 1, type_item)

                # المبلغ
                amount_item = QTableWidgetItem(f"{transaction.amount:.3f}")
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.transactions_table.setItem(row, 2, amount_item)

                # الرصيد قبل
                balance_before_item = QTableWidgetItem(f"{transaction.balance_before:.3f}" if transaction.balance_before else "0.000")
                balance_before_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.transactions_table.setItem(row, 3, balance_before_item)

                # الرصيد بعد
                balance_after_item = QTableWidgetItem(f"{transaction.balance_after:.3f}" if transaction.balance_after else "0.000")
                balance_after_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.transactions_table.setItem(row, 4, balance_after_item)

                # التاريخ
                date_str = transaction.transaction_date.strftime("%Y-%m-%d") if transaction.transaction_date else ""
                self.transactions_table.setItem(row, 5, QTableWidgetItem(date_str))

                # الوصف
                self.transactions_table.setItem(row, 6, QTableWidgetItem(transaction.description or ""))

                # المرجع
                self.transactions_table.setItem(row, 7, QTableWidgetItem(transaction.reference_number or ""))

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل المعاملات:\n{str(e)}")

    def update_account_statistics(self, account_id):
        """تحديث إحصائيات الحساب"""
        try:
            session = self.db_manager.get_session()

            from datetime import datetime, timedelta
            now = datetime.now()

            # إحصائيات شهرية
            month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            monthly_transactions = session.query(SupplierTransaction).filter(
                SupplierTransaction.supplier_account_id == account_id,
                SupplierTransaction.created_at >= month_start
            ).all()

            monthly_remittances = len([t for t in monthly_transactions if t.transaction_type == TransactionType.REMITTANCE])
            monthly_payments = sum([float(t.amount) for t in monthly_transactions if t.transaction_type == TransactionType.PAYMENT])
            monthly_count = len(monthly_transactions)
            avg_transaction = monthly_payments / monthly_count if monthly_count > 0 else 0

            self.monthly_remittances_label.setText(f"الحوالات هذا الشهر: {monthly_remittances}")
            self.monthly_payments_label.setText(f"المدفوعات هذا الشهر: {monthly_payments:,.3f}")
            self.monthly_transactions_label.setText(f"المعاملات هذا الشهر: {monthly_count}")
            self.avg_transaction_label.setText(f"متوسط المعاملة: {avg_transaction:,.3f}")

            # إحصائيات سنوية
            year_start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            yearly_transactions = session.query(SupplierTransaction).filter(
                SupplierTransaction.supplier_account_id == account_id,
                SupplierTransaction.created_at >= year_start
            ).all()

            yearly_remittances = len([t for t in yearly_transactions if t.transaction_type == TransactionType.REMITTANCE])
            yearly_payments = sum([float(t.amount) for t in yearly_transactions if t.transaction_type == TransactionType.PAYMENT])
            yearly_count = len(yearly_transactions)

            # حساب معدل النمو (مقارنة بالعام السابق)
            last_year_start = year_start.replace(year=year_start.year - 1)
            last_year_end = year_start
            last_year_transactions = session.query(SupplierTransaction).filter(
                SupplierTransaction.supplier_account_id == account_id,
                SupplierTransaction.created_at >= last_year_start,
                SupplierTransaction.created_at < last_year_end
            ).all()

            last_year_payments = sum([float(t.amount) for t in last_year_transactions if t.transaction_type == TransactionType.PAYMENT])
            growth_rate = ((yearly_payments - last_year_payments) / last_year_payments * 100) if last_year_payments > 0 else 0

            self.yearly_remittances_label.setText(f"الحوالات هذا العام: {yearly_remittances}")
            self.yearly_payments_label.setText(f"المدفوعات هذا العام: {yearly_payments:,.3f}")
            self.yearly_transactions_label.setText(f"المعاملات هذا العام: {yearly_count}")
            self.growth_rate_label.setText(f"معدل النمو: {growth_rate:+.1f}%")

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحديث الإحصائيات:\n{str(e)}")

    def filter_transactions(self):
        """تصفية المعاملات"""
        if self.current_account:
            self.load_account_transactions(self.current_account)

    def clear_account_details(self):
        """مسح تفاصيل الحساب"""
        self.current_account = None

        # مسح معلومات الحساب
        labels = [
            self.info_supplier_label, self.info_currency_label, self.info_account_number_label,
            self.info_balance_label, self.info_credit_limit_label, self.info_available_credit_label,
            self.info_total_remittances_label, self.info_total_payments_label,
            self.info_last_transaction_label, self.info_status_label, self.info_created_at_label
        ]

        for label in labels:
            label.setText("-")

        # مسح المعاملات
        self.transactions_table.setRowCount(0)

        # مسح الإحصائيات
        stats_labels = [
            self.monthly_remittances_label, self.monthly_payments_label,
            self.monthly_transactions_label, self.avg_transaction_label,
            self.yearly_remittances_label, self.yearly_payments_label,
            self.yearly_transactions_label, self.growth_rate_label
        ]

        for label in stats_labels:
            label.setText(label.text().split(':')[0] + ": 0")

    def new_account(self):
        """إنشاء حساب جديد"""
        dialog = AccountDialog(self)
        if dialog.exec() == QDialog.Accepted:
            account_data = dialog.get_account_data()
            self.create_account(account_data)

    def edit_account(self):
        """تعديل الحساب المحدد"""
        if not self.current_account:
            QMessageBox.warning(self, "تحذير", "يجب اختيار حساب للتعديل")
            return

        try:
            session = self.db_manager.get_session()
            account = session.query(SupplierAccount).get(self.current_account)

            if account:
                dialog = AccountDialog(self, account)
                if dialog.exec() == QDialog.Accepted:
                    account_data = dialog.get_account_data()
                    self.update_account(account_data)

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الحساب:\n{str(e)}")

    def new_transaction(self):
        """إنشاء معاملة جديدة"""
        if not self.current_account:
            QMessageBox.warning(self, "تحذير", "يجب اختيار حساب أولاً")
            return

        dialog = TransactionDialog(self, self.current_account)
        if dialog.exec() == QDialog.Accepted:
            transaction_data = dialog.get_transaction_data()
            self.create_transaction(transaction_data)

    def create_account(self, account_data):
        """إنشاء حساب جديد في قاعدة البيانات"""
        try:
            session = self.db_manager.get_session()

            # التحقق من عدم وجود حساب مشابه
            existing = session.query(SupplierAccount).filter(
                SupplierAccount.supplier_id == account_data['supplier_id'],
                SupplierAccount.currency_id == account_data['currency_id']
            ).first()

            if existing:
                QMessageBox.warning(self, "تحذير", "يوجد حساب بالفعل لهذا المورد بنفس العملة")
                session.close()
                return

            # إنشاء الحساب الجديد
            account = SupplierAccount(
                supplier_id=account_data['supplier_id'],
                currency_id=account_data['currency_id'],
                account_number=account_data['account_number'],
                credit_limit=account_data['credit_limit'],
                balance=account_data.get('initial_balance', 0.0),
                available_credit=account_data['credit_limit'],
                created_by=1  # يجب الحصول على معرف المستخدم الحالي
            )

            session.add(account)
            session.commit()

            # إرسال إشارة
            self.account_created.emit({
                'id': account.id,
                'supplier_id': account.supplier_id,
                'currency_id': account.currency_id,
                'account_number': account.account_number
            })

            QMessageBox.information(self, "نجح", "تم إنشاء الحساب بنجاح")

            # تحديث البيانات
            self.load_accounts()

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء الحساب:\n{str(e)}")

    def update_account(self, account_data):
        """تحديث الحساب في قاعدة البيانات"""
        try:
            session = self.db_manager.get_session()
            account = session.query(SupplierAccount).get(self.current_account)

            if account:
                account.account_number = account_data['account_number']
                account.credit_limit = account_data['credit_limit']
                account.available_credit = account_data['credit_limit'] - (account.balance or 0)

                session.commit()

                # إرسال إشارة
                self.account_updated.emit({
                    'id': account.id,
                    'account_number': account.account_number,
                    'credit_limit': account.credit_limit
                })

                QMessageBox.information(self, "نجح", "تم تحديث الحساب بنجاح")

                # تحديث البيانات
                self.load_accounts()
                self.load_account_details(self.current_account)

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث الحساب:\n{str(e)}")

    def create_transaction(self, transaction_data):
        """إنشاء معاملة جديدة"""
        try:
            session = self.db_manager.get_session()
            account = session.query(SupplierAccount).get(self.current_account)

            if not account:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الحساب")
                session.close()
                return

            # حساب الرصيد الجديد
            current_balance = float(account.balance or 0)
            transaction_amount = transaction_data['amount']

            if transaction_data['type'] in [TransactionType.CREDIT, TransactionType.REMITTANCE]:
                new_balance = current_balance + transaction_amount
            else:  # DEBIT, PAYMENT
                new_balance = current_balance - transaction_amount

            # إنشاء رقم معاملة تلقائي
            transaction_number = self.generate_transaction_number()

            # إنشاء المعاملة
            transaction = SupplierTransaction(
                transaction_number=transaction_number,
                supplier_account_id=self.current_account,
                transaction_type=transaction_data['type'],
                amount=transaction_amount,
                balance_before=current_balance,
                balance_after=new_balance,
                transaction_date=transaction_data['date'],
                description=transaction_data['description'],
                reference_number=transaction_data['reference'],
                created_by=1  # يجب الحصول على معرف المستخدم الحالي
            )

            # تحديث رصيد الحساب
            account.balance = new_balance
            account.last_transaction_date = transaction_data['date']

            # تحديث الإجماليات
            if transaction_data['type'] == TransactionType.REMITTANCE:
                account.total_remittances = (account.total_remittances or 0) + transaction_amount
            elif transaction_data['type'] == TransactionType.PAYMENT:
                account.total_payments = (account.total_payments or 0) + transaction_amount

            # تحديث الائتمان المتاح
            account.available_credit = (account.credit_limit or 0) - new_balance

            session.add(transaction)
            session.commit()

            # إرسال إشارة
            self.transaction_created.emit({
                'id': transaction.id,
                'number': transaction.transaction_number,
                'type': transaction.transaction_type.value,
                'amount': float(transaction.amount),
                'account_id': self.current_account
            })

            QMessageBox.information(self, "نجح", "تم إنشاء المعاملة بنجاح")

            # تحديث البيانات
            self.load_accounts()
            self.load_account_details(self.current_account)

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء المعاملة:\n{str(e)}")

    def generate_transaction_number(self):
        """إنشاء رقم معاملة تلقائي"""
        try:
            session = self.db_manager.get_session()

            # الحصول على آخر رقم معاملة
            last_transaction = session.query(SupplierTransaction).order_by(SupplierTransaction.id.desc()).first()

            if last_transaction and last_transaction.transaction_number:
                try:
                    last_number = int(last_transaction.transaction_number.split('-')[-1])
                    new_number = last_number + 1
                except:
                    new_number = 1
            else:
                new_number = 1

            # تنسيق الرقم
            from datetime import datetime
            current_year = datetime.now().year
            formatted_number = f"TXN-{current_year}-{new_number:08d}"

            session.close()
            return formatted_number

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إنشاء رقم المعاملة:\n{str(e)}")
            return f"TXN-{QDate.currentDate().year()}-********"

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
        if self.current_account:
            self.load_account_details(self.current_account)


# نوافذ الحوار المساعدة
class AccountDialog(QDialog):
    """نافذة حوار إنشاء/تعديل الحساب"""

    def __init__(self, parent=None, account=None):
        super().__init__(parent)
        self.account = account
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.load_data()

        if account:
            self.load_account_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إنشاء حساب جديد" if not self.account else "تعديل الحساب")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout(self)

        # النموذج
        form_layout = QFormLayout()

        # المورد
        self.supplier_combo = QComboBox()
        self.supplier_combo.setEnabled(not self.account)  # تعطيل التعديل للحسابات الموجودة
        form_layout.addRow("المورد:", self.supplier_combo)

        # العملة
        self.currency_combo = QComboBox()
        self.currency_combo.setEnabled(not self.account)  # تعطيل التعديل للحسابات الموجودة
        form_layout.addRow("العملة:", self.currency_combo)

        # رقم الحساب
        self.account_number_edit = QLineEdit()
        form_layout.addRow("رقم الحساب:", self.account_number_edit)

        # حد الائتمان
        self.credit_limit_spin = QDoubleSpinBox()
        self.credit_limit_spin.setRange(0, *********.999)
        self.credit_limit_spin.setDecimals(3)
        form_layout.addRow("حد الائتمان:", self.credit_limit_spin)

        # الرصيد الأولي (للحسابات الجديدة فقط)
        if not self.account:
            self.initial_balance_spin = QDoubleSpinBox()
            self.initial_balance_spin.setRange(-*********.999, *********.999)
            self.initial_balance_spin.setDecimals(3)
            form_layout.addRow("الرصيد الأولي:", self.initial_balance_spin)

        layout.addLayout(form_layout)

        # الأزرار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def load_data(self):
        """تحميل البيانات"""
        try:
            session = self.db_manager.get_session()

            # تحميل الموردين
            suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()
            self.supplier_combo.addItem("", None)
            for supplier in suppliers:
                self.supplier_combo.addItem(f"{supplier.code} - {supplier.name}", supplier.id)

            # تحميل العملات
            currencies = session.query(Currency).filter(Currency.is_active == True).all()
            self.currency_combo.addItem("", None)
            for currency in currencies:
                self.currency_combo.addItem(f"{currency.code} - {currency.name}", currency.id)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")

    def load_account_data(self):
        """تحميل بيانات الحساب للتعديل"""
        if self.account:
            # تعيين المورد
            for i in range(self.supplier_combo.count()):
                if self.supplier_combo.itemData(i) == self.account.supplier_id:
                    self.supplier_combo.setCurrentIndex(i)
                    break

            # تعيين العملة
            for i in range(self.currency_combo.count()):
                if self.currency_combo.itemData(i) == self.account.currency_id:
                    self.currency_combo.setCurrentIndex(i)
                    break

            # تعيين البيانات الأخرى
            self.account_number_edit.setText(self.account.account_number or "")
            self.credit_limit_spin.setValue(float(self.account.credit_limit or 0))

    def get_account_data(self):
        """الحصول على بيانات الحساب"""
        data = {
            'supplier_id': self.supplier_combo.currentData(),
            'currency_id': self.currency_combo.currentData(),
            'account_number': self.account_number_edit.text().strip(),
            'credit_limit': self.credit_limit_spin.value()
        }

        if not self.account and hasattr(self, 'initial_balance_spin'):
            data['initial_balance'] = self.initial_balance_spin.value()

        return data


class TransactionDialog(QDialog):
    """نافذة حوار إنشاء معاملة جديدة"""

    def __init__(self, parent=None, account_id=None):
        super().__init__(parent)
        self.account_id = account_id
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إنشاء معاملة جديدة")
        self.setModal(True)
        self.resize(400, 350)

        layout = QVBoxLayout(self)

        # النموذج
        form_layout = QFormLayout()

        # نوع المعاملة
        self.transaction_type_combo = QComboBox()
        for trans_type in TransactionType:
            self.transaction_type_combo.addItem(trans_type.value, trans_type)
        form_layout.addRow("نوع المعاملة:", self.transaction_type_combo)

        # المبلغ
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0.001, *********.999)
        self.amount_spin.setDecimals(3)
        form_layout.addRow("المبلغ:", self.amount_spin)

        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        form_layout.addRow("التاريخ:", self.date_edit)

        # الوصف
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("وصف المعاملة...")
        form_layout.addRow("الوصف:", self.description_edit)

        # الرقم المرجعي
        self.reference_edit = QLineEdit()
        self.reference_edit.setPlaceholderText("الرقم المرجعي...")
        form_layout.addRow("المرجع:", self.reference_edit)

        layout.addLayout(form_layout)

        # الأزرار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def get_transaction_data(self):
        """الحصول على بيانات المعاملة"""
        return {
            'type': self.transaction_type_combo.currentData(),
            'amount': self.amount_spin.value(),
            'date': self.date_edit.date().toPython(),
            'description': self.description_edit.text().strip(),
            'reference': self.reference_edit.text().strip()
        }
