#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة نقل البيانات من SQLite إلى Oracle - ProShipment V2.0.0
Data Migration Tool from SQLite to Oracle
"""

import sys
import os
import logging
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime
import traceback

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.universal_database_manager import (
    UniversalDatabaseManager, create_sqlite_manager, create_oracle_manager
)
from src.database.oracle_config import DatabaseConfig, DatabaseType, OracleConfig, SQLiteConfig

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/migration.log', encoding='utf-8'),
        logging.<PERSON>Handler()
    ]
)
logger = logging.getLogger(__name__)

class DataMigrationTool:
    """أداة نقل البيانات الشاملة"""
    
    def __init__(self, sqlite_path: str = "data/proshipment.db"):
        self.sqlite_path = sqlite_path
        self.sqlite_manager = None
        self.oracle_manager = None
        self.migration_stats = {
            'tables_migrated': 0,
            'records_migrated': 0,
            'errors': 0,
            'warnings': 0,
            'start_time': None,
            'end_time': None
        }
        
        # إنشاء مجلد السجلات
        Path("logs").mkdir(exist_ok=True)
    
    def setup_connections(self, oracle_config: OracleConfig) -> bool:
        """إعداد الاتصالات"""
        try:
            # إعداد SQLite
            self.sqlite_manager = create_sqlite_manager(self.sqlite_path)
            if not self.sqlite_manager.test_connection():
                logger.error("فشل في الاتصال بـ SQLite")
                return False
            
            # إعداد Oracle
            self.oracle_manager = create_oracle_manager(
                host=oracle_config.host,
                port=oracle_config.port,
                service_name=oracle_config.service_name,
                username=oracle_config.username,
                password=oracle_config.password
            )
            
            if not self.oracle_manager.test_connection():
                logger.error("فشل في الاتصال بـ Oracle")
                return False
            
            logger.info("تم إعداد الاتصالات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إعداد الاتصالات: {e}")
            return False
    
    def analyze_source_data(self) -> Dict[str, Any]:
        """تحليل البيانات المصدر"""
        logger.info("🔍 تحليل البيانات المصدر...")
        
        analysis = {
            'tables': {},
            'total_records': 0,
            'database_size': 0,
            'foreign_keys': [],
            'indexes': []
        }
        
        try:
            with self.sqlite_manager.get_session() as session:
                # الحصول على معلومات الجداول
                from src.database.models import Base
                
                for table_name, table in Base.metadata.tables.items():
                    # عدد السجلات
                    from sqlalchemy import text
                    result = session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                    record_count = result.scalar()
                    
                    # معلومات الأعمدة
                    columns = []
                    for column in table.columns:
                        columns.append({
                            'name': column.name,
                            'type': str(column.type),
                            'nullable': column.nullable,
                            'primary_key': column.primary_key
                        })
                    
                    # معلومات المفاتيح الخارجية
                    foreign_keys = []
                    for fk in table.foreign_keys:
                        foreign_keys.append({
                            'column': fk.parent.name,
                            'references': f"{fk.column.table.name}.{fk.column.name}"
                        })
                    
                    analysis['tables'][table_name] = {
                        'record_count': record_count,
                        'columns': columns,
                        'foreign_keys': foreign_keys
                    }
                    
                    analysis['total_records'] += record_count
                    
                    logger.info(f"   📊 {table_name}: {record_count} سجل")
            
            # حجم قاعدة البيانات
            db_path = Path(self.sqlite_path)
            if db_path.exists():
                analysis['database_size'] = db_path.stat().st_size
            
            logger.info(f"📈 إجمالي السجلات: {analysis['total_records']}")
            logger.info(f"💾 حجم قاعدة البيانات: {analysis['database_size'] / 1024 / 1024:.2f} MB")
            
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في تحليل البيانات: {e}")
            return analysis
    
    def prepare_target_database(self) -> bool:
        """تحضير قاعدة البيانات الهدف"""
        logger.info("🔧 تحضير قاعدة البيانات الهدف...")
        
        try:
            # تهيئة قاعدة البيانات
            if not self.oracle_manager.initialize_database():
                logger.error("فشل في تهيئة Oracle")
                return False
            
            logger.info("✅ تم تحضير Oracle بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحضير Oracle: {e}")
            return False
    
    def migrate_table_data(self, table_name: str, batch_size: int = 1000) -> bool:
        """نقل بيانات جدول واحد"""
        logger.info(f"📦 نقل بيانات جدول: {table_name}")
        
        try:
            # الحصول على النموذج
            from src.database.models import Base
            
            if table_name not in Base.metadata.tables:
                logger.warning(f"جدول غير موجود: {table_name}")
                return False
            
            # الحصول على فئة النموذج
            model_class = None
            for mapper in Base.registry.mappers:
                if mapper.class_.__tablename__ == table_name:
                    model_class = mapper.class_
                    break
            
            if not model_class:
                logger.warning(f"فئة النموذج غير موجودة: {table_name}")
                return False
            
            # عدد السجلات في المصدر
            with self.sqlite_manager.get_session() as sqlite_session:
                total_records = sqlite_session.query(model_class).count()
            
            if total_records == 0:
                logger.info(f"   📊 جدول {table_name} فارغ")
                return True
            
            logger.info(f"   📊 عدد السجلات: {total_records}")
            
            # نقل البيانات على دفعات
            migrated_count = 0
            
            with self.sqlite_manager.get_session() as sqlite_session:
                with self.oracle_manager.get_session() as oracle_session:
                    
                    # التحقق من وجود بيانات في الهدف
                    existing_count = oracle_session.query(model_class).count()
                    if existing_count > 0:
                        logger.warning(f"   ⚠️ يوجد {existing_count} سجل في الهدف")
                        
                        # خيار الحذف والإعادة
                        response = input(f"هل تريد حذف البيانات الموجودة في {table_name}؟ (y/n): ")
                        if response.lower() in ['y', 'yes', 'نعم']:
                            oracle_session.query(model_class).delete()
                            oracle_session.commit()
                            logger.info(f"   🗑️ تم حذف البيانات الموجودة")
                        else:
                            logger.info(f"   ⏭️ تخطي جدول {table_name}")
                            return True
                    
                    # نقل البيانات على دفعات
                    offset = 0
                    while offset < total_records:
                        # قراءة دفعة من SQLite
                        batch = sqlite_session.query(model_class).offset(offset).limit(batch_size).all()
                        
                        if not batch:
                            break
                        
                        # إدراج الدفعة في Oracle
                        for record in batch:
                            # إنشاء نسخة جديدة من السجل
                            new_record = model_class()
                            
                            # نسخ البيانات
                            for column in model_class.__table__.columns:
                                if hasattr(record, column.name):
                                    value = getattr(record, column.name)
                                    setattr(new_record, column.name, value)
                            
                            oracle_session.add(new_record)
                        
                        # حفظ الدفعة
                        oracle_session.commit()
                        
                        migrated_count += len(batch)
                        offset += batch_size
                        
                        # عرض التقدم
                        progress = (migrated_count / total_records) * 100
                        logger.info(f"   📈 تقدم {table_name}: {migrated_count}/{total_records} ({progress:.1f}%)")
            
            logger.info(f"   ✅ تم نقل {migrated_count} سجل من {table_name}")
            self.migration_stats['records_migrated'] += migrated_count
            return True
            
        except Exception as e:
            logger.error(f"خطأ في نقل جدول {table_name}: {e}")
            traceback.print_exc()
            self.migration_stats['errors'] += 1
            return False
    
    def migrate_all_data(self, batch_size: int = 1000) -> bool:
        """نقل جميع البيانات"""
        logger.info("🚀 بدء نقل جميع البيانات...")
        
        self.migration_stats['start_time'] = datetime.now()
        
        # ترتيب الجداول حسب التبعيات
        table_order = [
            'system_settings',
            'currencies',
            'fiscal_years',
            'companies',
            'branches',
            'users',
            'item_groups',
            'items',
            'suppliers',
            'shipments',
            'shipment_items',
            'purchase_orders',
            'purchase_order_items',
            'remittance_requests'
        ]
        
        success_count = 0
        
        for table_name in table_order:
            try:
                if self.migrate_table_data(table_name, batch_size):
                    success_count += 1
                    self.migration_stats['tables_migrated'] += 1
                else:
                    logger.warning(f"فشل في نقل جدول: {table_name}")
                    
            except Exception as e:
                logger.error(f"خطأ في نقل جدول {table_name}: {e}")
                self.migration_stats['errors'] += 1
        
        self.migration_stats['end_time'] = datetime.now()
        
        # عرض الإحصائيات النهائية
        self._show_migration_summary()
        
        return success_count > 0
    
    def verify_migration(self) -> bool:
        """التحقق من صحة النقل"""
        logger.info("🔍 التحقق من صحة النقل...")
        
        verification_results = {}
        all_verified = True
        
        try:
            from src.database.models import Base
            
            for table_name in Base.metadata.tables.keys():
                # الحصول على فئة النموذج
                model_class = None
                for mapper in Base.registry.mappers:
                    if mapper.class_.__tablename__ == table_name:
                        model_class = mapper.class_
                        break
                
                if not model_class:
                    continue
                
                # عدد السجلات في المصدر والهدف
                with self.sqlite_manager.get_session() as sqlite_session:
                    source_count = sqlite_session.query(model_class).count()
                
                with self.oracle_manager.get_session() as oracle_session:
                    target_count = oracle_session.query(model_class).count()
                
                # مقارنة العدد
                verified = source_count == target_count
                verification_results[table_name] = {
                    'source_count': source_count,
                    'target_count': target_count,
                    'verified': verified
                }
                
                if verified:
                    logger.info(f"   ✅ {table_name}: {source_count} = {target_count}")
                else:
                    logger.error(f"   ❌ {table_name}: {source_count} ≠ {target_count}")
                    all_verified = False
            
            if all_verified:
                logger.info("🎉 تم التحقق من صحة النقل بنجاح!")
            else:
                logger.warning("⚠️ هناك اختلافات في البيانات")
            
            return all_verified
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من النقل: {e}")
            return False
    
    def _show_migration_summary(self):
        """عرض ملخص النقل"""
        duration = self.migration_stats['end_time'] - self.migration_stats['start_time']
        
        logger.info("\n" + "="*60)
        logger.info("📊 ملخص عملية النقل")
        logger.info("="*60)
        logger.info(f"⏱️ وقت البدء: {self.migration_stats['start_time']}")
        logger.info(f"⏱️ وقت الانتهاء: {self.migration_stats['end_time']}")
        logger.info(f"⏱️ المدة الإجمالية: {duration}")
        logger.info(f"📊 الجداول المنقولة: {self.migration_stats['tables_migrated']}")
        logger.info(f"📊 السجلات المنقولة: {self.migration_stats['records_migrated']}")
        logger.info(f"❌ الأخطاء: {self.migration_stats['errors']}")
        logger.info(f"⚠️ التحذيرات: {self.migration_stats['warnings']}")
        
        if self.migration_stats['records_migrated'] > 0 and duration.total_seconds() > 0:
            rate = self.migration_stats['records_migrated'] / duration.total_seconds()
            logger.info(f"⚡ معدل النقل: {rate:.2f} سجل/ثانية")

def run_interactive_migration():
    """تشغيل النقل التفاعلي"""
    print("🔄 أداة نقل البيانات من SQLite إلى Oracle")
    print("="*60)
    
    # إعدادات SQLite
    sqlite_path = input("مسار ملف SQLite [data/proshipment.db]: ").strip()
    if not sqlite_path:
        sqlite_path = "data/proshipment.db"
    
    if not Path(sqlite_path).exists():
        print(f"❌ ملف SQLite غير موجود: {sqlite_path}")
        return False
    
    # إعدادات Oracle
    print("\n📝 إعدادات Oracle:")
    oracle_host = input("عنوان الخادم [localhost]: ").strip() or "localhost"
    oracle_port = int(input("المنفذ [1521]: ").strip() or "1521")
    oracle_service = input("اسم الخدمة [XE]: ").strip() or "XE"
    oracle_username = input("اسم المستخدم [proshipment]: ").strip() or "proshipment"
    
    import getpass
    oracle_password = getpass.getpass("كلمة المرور: ")
    
    # إعدادات النقل
    print("\n⚙️ إعدادات النقل:")
    batch_size = int(input("حجم الدفعة [1000]: ").strip() or "1000")
    
    # إنشاء أداة النقل
    migration_tool = DataMigrationTool(sqlite_path)
    
    # إعداد Oracle
    oracle_config = OracleConfig(
        host=oracle_host,
        port=oracle_port,
        service_name=oracle_service,
        username=oracle_username,
        password=oracle_password
    )
    
    # تشغيل النقل
    try:
        # إعداد الاتصالات
        if not migration_tool.setup_connections(oracle_config):
            print("❌ فشل في إعداد الاتصالات")
            return False
        
        # تحليل البيانات
        analysis = migration_tool.analyze_source_data()
        
        # تأكيد النقل
        print(f"\n📊 سيتم نقل {analysis['total_records']} سجل من {len(analysis['tables'])} جدول")
        confirm = input("هل تريد المتابعة؟ (y/n): ").strip().lower()
        
        if confirm not in ['y', 'yes', 'نعم']:
            print("❌ تم إلغاء النقل")
            return False
        
        # تحضير الهدف
        if not migration_tool.prepare_target_database():
            print("❌ فشل في تحضير قاعدة البيانات الهدف")
            return False
        
        # تشغيل النقل
        if migration_tool.migrate_all_data(batch_size):
            # التحقق من النقل
            if migration_tool.verify_migration():
                print("\n🎉 تم النقل والتحقق بنجاح!")
                return True
            else:
                print("\n⚠️ تم النقل لكن هناك اختلافات في البيانات")
                return False
        else:
            print("\n❌ فشل في النقل")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ في عملية النقل: {e}")
        traceback.print_exc()
        return False

def test_migration_tool():
    """اختبار أداة النقل"""
    print("🧪 اختبار أداة نقل البيانات...")

    # إنشاء أداة النقل
    migration_tool = DataMigrationTool("data/proshipment.db")

    # اختبار تحليل البيانات
    if Path("data/proshipment.db").exists():
        # إعداد SQLite فقط للاختبار
        migration_tool.sqlite_manager = create_sqlite_manager("data/proshipment.db")

        if migration_tool.sqlite_manager.test_connection():
            print("✅ اتصال SQLite نجح")

            # تحليل البيانات
            analysis = migration_tool.analyze_source_data()
            print(f"📊 تم تحليل {len(analysis['tables'])} جدول")
            print(f"📊 إجمالي السجلات: {analysis['total_records']}")

            return True
        else:
            print("❌ فشل اتصال SQLite")
            return False
    else:
        print("⚠️ ملف SQLite غير موجود للاختبار")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔄 أداة نقل البيانات من SQLite إلى Oracle - ProShipment V2.0.0")
    print("="*70)

    # اختيار نوع التشغيل
    print("اختر نوع التشغيل:")
    print("1. نقل تفاعلي كامل")
    print("2. اختبار الأداة")

    choice = input("اختر رقم (1-2): ").strip()

    if choice == "1":
        success = run_interactive_migration()
    elif choice == "2":
        success = test_migration_tool()
    else:
        print("❌ خيار غير صحيح")
        success = False

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
