# -*- coding: utf-8 -*-
"""
نافذة إنشاء حساب مورد جديد
New Supplier Account Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QDoubleSpinBox,
                               QPushButton, QGroupBox, QTextEdit, QCheckBox,
                               QMessageBox, QFrame, QTabWidget, QWidget,
                               QDateEdit, QSpinBox, QProgressBar)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont, QDoubleValidator, QRegularExpressionValidator

import sqlite3
from pathlib import Path
from datetime import datetime
import re

from ...utils.arabic_support import reshape_arabic_text

class NewSupplierAccountDialog(QDialog):
    """نافذة إنشاء حساب مورد جديد"""
    
    account_created = Signal(int)  # إشارة إنشاء الحساب
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إنشاء حساب مورد جديد - ProShipment")
        self.setMinimumSize(800, 600)
        self.resize(900, 700)
        self.setModal(True)
        
        # متغيرات النافذة
        self.suppliers_data = []
        self.currencies_data = []
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        
        # تحميل البيانات
        self.load_initial_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        self.create_header_section(layout)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_info_tab()
        self.tabs.addTab(basic_tab, "المعلومات الأساسية")
        
        # تبويب الحدود والأرصدة
        limits_tab = self.create_limits_tab()
        self.tabs.addTab(limits_tab, "الحدود والأرصدة")
        
        # تبويب الإعدادات المتقدمة
        advanced_tab = self.create_advanced_tab()
        self.tabs.addTab(advanced_tab, "الإعدادات المتقدمة")
        
        layout.addWidget(self.tabs)
        
        # أزرار الإجراءات
        self.create_action_buttons(layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #10b981, stop:1 #059669);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة الحساب
        icon_label = QLabel("💳")
        icon_label.setStyleSheet("font-size: 32px; color: white;")
        header_layout.addWidget(icon_label)
        
        # معلومات العنوان
        info_layout = QVBoxLayout()
        
        title_label = QLabel("إنشاء حساب مورد جديد")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        
        subtitle_label = QLabel("إعداد حساب مالي جديد للمورد مع تحديد الحدود والصلاحيات")
        subtitle_label.setFont(QFont("Arial", 11))
        subtitle_label.setStyleSheet("color: #d1fae5;")
        
        info_layout.addWidget(title_label)
        info_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        layout.addWidget(header_frame)
        
    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات المورد
        supplier_group = QGroupBox("معلومات المورد")
        supplier_layout = QGridLayout(supplier_group)
        
        # اختيار المورد
        supplier_layout.addWidget(QLabel("المورد: *"), 0, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.setMinimumWidth(250)
        supplier_layout.addWidget(self.supplier_combo, 0, 1)
        
        # معلومات المورد المحددة (للعرض فقط)
        supplier_layout.addWidget(QLabel("كود المورد:"), 1, 0)
        self.supplier_code_label = QLabel("-")
        self.supplier_code_label.setStyleSheet("background-color: #f3f4f6; padding: 5px; border-radius: 3px;")
        supplier_layout.addWidget(self.supplier_code_label, 1, 1)
        
        supplier_layout.addWidget(QLabel("نوع المورد:"), 2, 0)
        self.supplier_type_label = QLabel("-")
        self.supplier_type_label.setStyleSheet("background-color: #f3f4f6; padding: 5px; border-radius: 3px;")
        supplier_layout.addWidget(self.supplier_type_label, 2, 1)
        
        supplier_layout.addWidget(QLabel("هاتف المورد:"), 3, 0)
        self.supplier_phone_label = QLabel("-")
        self.supplier_phone_label.setStyleSheet("background-color: #f3f4f6; padding: 5px; border-radius: 3px;")
        supplier_layout.addWidget(self.supplier_phone_label, 3, 1)
        
        layout.addWidget(supplier_group)
        
        # معلومات الحساب
        account_group = QGroupBox("معلومات الحساب")
        account_layout = QGridLayout(account_group)
        
        # رقم الحساب
        account_layout.addWidget(QLabel("رقم الحساب: *"), 0, 0)
        self.account_number_input = QLineEdit()
        self.account_number_input.setPlaceholderText("سيتم توليده تلقائياً إذا ترك فارغاً")
        account_layout.addWidget(self.account_number_input, 0, 1)
        
        # العملة
        account_layout.addWidget(QLabel("العملة: *"), 1, 0)
        self.currency_combo = QComboBox()
        self.currency_combo.setMinimumWidth(200)
        account_layout.addWidget(self.currency_combo, 1, 1)
        
        # الرصيد الافتتاحي
        account_layout.addWidget(QLabel("الرصيد الافتتاحي:"), 2, 0)
        self.opening_balance_input = QDoubleSpinBox()
        self.opening_balance_input.setRange(-*********.99, *********.99)
        self.opening_balance_input.setDecimals(2)
        self.opening_balance_input.setValue(0.00)
        account_layout.addWidget(self.opening_balance_input, 2, 1)
        
        # تاريخ فتح الحساب
        account_layout.addWidget(QLabel("تاريخ فتح الحساب:"), 3, 0)
        self.opening_date = QDateEdit()
        self.opening_date.setDate(QDate.currentDate())
        self.opening_date.setCalendarPopup(True)
        account_layout.addWidget(self.opening_date, 3, 1)
        
        layout.addWidget(account_group)
        
        # ملاحظات
        notes_group = QGroupBox("ملاحظات الحساب")
        notes_layout = QVBoxLayout(notes_group)
        
        self.account_notes_input = QTextEdit()
        self.account_notes_input.setMaximumHeight(100)
        self.account_notes_input.setPlaceholderText("أدخل أي ملاحظات خاصة بالحساب...")
        notes_layout.addWidget(self.account_notes_input)
        
        layout.addWidget(notes_group)
        layout.addStretch()
        
        return tab
        
    def create_limits_tab(self):
        """إنشاء تبويب الحدود والأرصدة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # حدود الائتمان
        credit_group = QGroupBox("حدود الائتمان")
        credit_layout = QGridLayout(credit_group)
        
        # حد الائتمان
        credit_layout.addWidget(QLabel("حد الائتمان:"), 0, 0)
        self.credit_limit_input = QDoubleSpinBox()
        self.credit_limit_input.setRange(0.00, *********.99)
        self.credit_limit_input.setDecimals(2)
        self.credit_limit_input.setValue(0.00)
        credit_layout.addWidget(self.credit_limit_input, 0, 1)
        
        # الحد الأدنى للرصيد
        credit_layout.addWidget(QLabel("الحد الأدنى للرصيد:"), 1, 0)
        self.minimum_balance_input = QDoubleSpinBox()
        self.minimum_balance_input.setRange(-*********.99, *********.99)
        self.minimum_balance_input.setDecimals(2)
        self.minimum_balance_input.setValue(0.00)
        credit_layout.addWidget(self.minimum_balance_input, 1, 1)
        
        # الحد الأقصى للرصيد
        credit_layout.addWidget(QLabel("الحد الأقصى للرصيد:"), 2, 0)
        self.maximum_balance_input = QDoubleSpinBox()
        self.maximum_balance_input.setRange(0.00, *********.99)
        self.maximum_balance_input.setDecimals(2)
        self.maximum_balance_input.setValue(*********.99)
        credit_layout.addWidget(self.maximum_balance_input, 2, 1)
        
        layout.addWidget(credit_group)
        
        # حدود المعاملات
        transaction_group = QGroupBox("حدود المعاملات")
        transaction_layout = QGridLayout(transaction_group)
        
        # الحد الأقصى للمعاملة الواحدة
        transaction_layout.addWidget(QLabel("الحد الأقصى للمعاملة:"), 0, 0)
        self.max_transaction_input = QDoubleSpinBox()
        self.max_transaction_input.setRange(0.00, *********.99)
        self.max_transaction_input.setDecimals(2)
        self.max_transaction_input.setValue(100000.00)
        transaction_layout.addWidget(self.max_transaction_input, 0, 1)
        
        # الحد الأقصى للمعاملات اليومية
        transaction_layout.addWidget(QLabel("الحد الأقصى اليومي:"), 1, 0)
        self.daily_limit_input = QDoubleSpinBox()
        self.daily_limit_input.setRange(0.00, *********.99)
        self.daily_limit_input.setDecimals(2)
        self.daily_limit_input.setValue(500000.00)
        transaction_layout.addWidget(self.daily_limit_input, 1, 1)
        
        # الحد الأقصى للمعاملات الشهرية
        transaction_layout.addWidget(QLabel("الحد الأقصى الشهري:"), 2, 0)
        self.monthly_limit_input = QDoubleSpinBox()
        self.monthly_limit_input.setRange(0.00, *********.99)
        self.monthly_limit_input.setDecimals(2)
        self.monthly_limit_input.setValue(********.00)
        transaction_layout.addWidget(self.monthly_limit_input, 2, 1)
        
        layout.addWidget(transaction_group)
        
        # تنبيهات الأرصدة
        alerts_group = QGroupBox("تنبيهات الأرصدة")
        alerts_layout = QGridLayout(alerts_group)
        
        # تفعيل التنبيهات
        self.enable_alerts_checkbox = QCheckBox("تفعيل تنبيهات الأرصدة")
        self.enable_alerts_checkbox.setChecked(True)
        alerts_layout.addWidget(self.enable_alerts_checkbox, 0, 0, 1, 2)
        
        # حد التنبيه للرصيد المنخفض
        alerts_layout.addWidget(QLabel("تنبيه الرصيد المنخفض:"), 1, 0)
        self.low_balance_alert_input = QDoubleSpinBox()
        self.low_balance_alert_input.setRange(0.00, *********.99)
        self.low_balance_alert_input.setDecimals(2)
        self.low_balance_alert_input.setValue(1000.00)
        alerts_layout.addWidget(self.low_balance_alert_input, 1, 1)
        
        # حد التنبيه للرصيد المرتفع
        alerts_layout.addWidget(QLabel("تنبيه الرصيد المرتفع:"), 2, 0)
        self.high_balance_alert_input = QDoubleSpinBox()
        self.high_balance_alert_input.setRange(0.00, *********.99)
        self.high_balance_alert_input.setDecimals(2)
        self.high_balance_alert_input.setValue(100000.00)
        alerts_layout.addWidget(self.high_balance_alert_input, 2, 1)
        
        layout.addWidget(alerts_group)
        layout.addStretch()
        
        return tab

    def create_advanced_tab(self):
        """إنشاء تبويب الإعدادات المتقدمة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات الحساب
        settings_group = QGroupBox("إعدادات الحساب")
        settings_layout = QGridLayout(settings_group)

        # حالة الحساب
        self.is_active_checkbox = QCheckBox("حساب نشط")
        self.is_active_checkbox.setChecked(True)
        settings_layout.addWidget(self.is_active_checkbox, 0, 0, 1, 2)

        # السماح بالسحب على المكشوف
        self.allow_overdraft_checkbox = QCheckBox("السماح بالسحب على المكشوف")
        settings_layout.addWidget(self.allow_overdraft_checkbox, 1, 0, 1, 2)

        # تجميد الحساب
        self.is_frozen_checkbox = QCheckBox("تجميد الحساب")
        settings_layout.addWidget(self.is_frozen_checkbox, 2, 0, 1, 2)

        # فترة السماح (بالأيام)
        settings_layout.addWidget(QLabel("فترة السماح (أيام):"), 3, 0)
        self.grace_period_input = QSpinBox()
        self.grace_period_input.setRange(0, 365)
        self.grace_period_input.setValue(30)
        settings_layout.addWidget(self.grace_period_input, 3, 1)

        layout.addWidget(settings_group)

        # إعدادات الفوائد والرسوم
        fees_group = QGroupBox("الفوائد والرسوم")
        fees_layout = QGridLayout(fees_group)

        # معدل الفائدة السنوي
        fees_layout.addWidget(QLabel("معدل الفائدة السنوي (%):"), 0, 0)
        self.interest_rate_input = QDoubleSpinBox()
        self.interest_rate_input.setRange(0.00, 100.00)
        self.interest_rate_input.setDecimals(2)
        self.interest_rate_input.setValue(0.00)
        fees_layout.addWidget(self.interest_rate_input, 0, 1)

        # رسوم الصيانة الشهرية
        fees_layout.addWidget(QLabel("رسوم الصيانة الشهرية:"), 1, 0)
        self.maintenance_fee_input = QDoubleSpinBox()
        self.maintenance_fee_input.setRange(0.00, 999999.99)
        self.maintenance_fee_input.setDecimals(2)
        self.maintenance_fee_input.setValue(0.00)
        fees_layout.addWidget(self.maintenance_fee_input, 1, 1)

        # رسوم المعاملة
        fees_layout.addWidget(QLabel("رسوم المعاملة:"), 2, 0)
        self.transaction_fee_input = QDoubleSpinBox()
        self.transaction_fee_input.setRange(0.00, 999999.99)
        self.transaction_fee_input.setDecimals(2)
        self.transaction_fee_input.setValue(0.00)
        fees_layout.addWidget(self.transaction_fee_input, 2, 1)

        layout.addWidget(fees_group)

        # إعدادات التقارير
        reports_group = QGroupBox("إعدادات التقارير")
        reports_layout = QGridLayout(reports_group)

        # تكرار الكشوفات
        reports_layout.addWidget(QLabel("تكرار الكشوفات:"), 0, 0)
        self.statement_frequency_combo = QComboBox()
        self.statement_frequency_combo.addItems(["شهري", "ربع سنوي", "نصف سنوي", "سنوي", "عند الطلب"])
        reports_layout.addWidget(self.statement_frequency_combo, 0, 1)

        # إرسال التقارير بالبريد الإلكتروني
        self.email_reports_checkbox = QCheckBox("إرسال التقارير بالبريد الإلكتروني")
        reports_layout.addWidget(self.email_reports_checkbox, 1, 0, 1, 2)

        # إرسال تنبيهات SMS
        self.sms_alerts_checkbox = QCheckBox("إرسال تنبيهات SMS")
        reports_layout.addWidget(self.sms_alerts_checkbox, 2, 0, 1, 2)

        layout.addWidget(reports_group)
        layout.addStretch()

        return tab

    def create_action_buttons(self, layout):
        """إنشاء أزرار الإجراءات"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        buttons_layout.addWidget(self.progress_bar)

        buttons_layout.addStretch()

        # زر المعاينة
        preview_btn = QPushButton("👁️ معاينة")
        preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        preview_btn.clicked.connect(self.preview_account)
        buttons_layout.addWidget(preview_btn)

        # زر الحفظ
        save_btn = QPushButton("💾 إنشاء الحساب")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        save_btn.clicked.connect(self.save_account)
        buttons_layout.addWidget(save_btn)

        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #dc2626;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addWidget(buttons_frame)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # تحديث معلومات المورد عند التغيير
        self.supplier_combo.currentTextChanged.connect(self.update_supplier_info)

        # التحقق من صحة البيانات
        self.supplier_combo.currentTextChanged.connect(self.validate_form)
        self.currency_combo.currentTextChanged.connect(self.validate_form)

        # تحديث الحدود عند تغيير الائتمان
        self.credit_limit_input.valueChanged.connect(self.update_limits)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل الموردين
            cursor.execute("SELECT id, name, code, supplier_type, phone FROM suppliers WHERE is_active = 1 ORDER BY name")
            self.suppliers_data = cursor.fetchall()

            self.supplier_combo.addItem("اختر المورد...", None)
            for supplier in self.suppliers_data:
                self.supplier_combo.addItem(supplier[1], supplier[0])

            # تحميل العملات
            cursor.execute("SELECT id, code, name FROM currencies WHERE is_active = 1 ORDER BY name")
            self.currencies_data = cursor.fetchall()

            self.currency_combo.addItem("اختر العملة...", None)
            for currency in self.currencies_data:
                self.currency_combo.addItem(f"{currency[1]} - {currency[2]}", currency[0])

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def update_supplier_info(self):
        """تحديث معلومات المورد المحددة"""
        supplier_id = self.supplier_combo.currentData()
        if supplier_id:
            # البحث عن بيانات المورد
            for supplier in self.suppliers_data:
                if supplier[0] == supplier_id:
                    self.supplier_code_label.setText(supplier[2] or "-")
                    self.supplier_type_label.setText(supplier[3] or "-")
                    self.supplier_phone_label.setText(supplier[4] or "-")
                    break
        else:
            self.supplier_code_label.setText("-")
            self.supplier_type_label.setText("-")
            self.supplier_phone_label.setText("-")

        # توليد رقم حساب تلقائي إذا لم يكن محدداً
        if supplier_id and not self.account_number_input.text():
            self.generate_account_number()

    def generate_account_number(self):
        """توليد رقم حساب تلقائي"""
        try:
            supplier_id = self.supplier_combo.currentData()
            currency_id = self.currency_combo.currentData()

            if supplier_id and currency_id:
                # توليد رقم حساب بناءً على المورد والعملة
                account_number = f"SA{supplier_id:04d}{currency_id:02d}{datetime.now().strftime('%Y%m%d%H%M%S')}"
                self.account_number_input.setText(account_number)

        except Exception as e:
            print(f"خطأ في توليد رقم الحساب: {e}")

    def update_limits(self):
        """تحديث الحدود بناءً على حد الائتمان"""
        credit_limit = self.credit_limit_input.value()

        # تحديث الحد الأقصى للمعاملة (25% من حد الائتمان)
        if credit_limit > 0:
            max_transaction = credit_limit * 0.25
            self.max_transaction_input.setValue(max_transaction)

            # تحديث الحد اليومي (50% من حد الائتمان)
            daily_limit = credit_limit * 0.5
            self.daily_limit_input.setValue(daily_limit)

            # تحديث الحد الشهري (100% من حد الائتمان)
            self.monthly_limit_input.setValue(credit_limit)

    def validate_form(self):
        """التحقق من صحة النموذج"""
        supplier_id = self.supplier_combo.currentData()
        currency_id = self.currency_combo.currentData()

        is_valid = (supplier_id is not None and currency_id is not None)

        # تفعيل/تعطيل أزرار الحفظ والمعاينة
        for button in self.findChildren(QPushButton):
            if "إنشاء الحساب" in button.text() or "معاينة" in button.text():
                button.setEnabled(is_valid)

    def preview_account(self):
        """معاينة بيانات الحساب"""
        if not self.validate_data():
            return

        data = self.collect_form_data()

        preview_text = f"""
معاينة بيانات الحساب الجديد:

المورد: {self.supplier_combo.currentText()}
العملة: {self.currency_combo.currentText()}
رقم الحساب: {data['account_number']}
الرصيد الافتتاحي: {data['opening_balance']:,.2f}
حد الائتمان: {data['credit_limit']:,.2f}
الحد الأقصى للمعاملة: {data['max_transaction_amount']:,.2f}
حالة الحساب: {"نشط" if data['is_active'] else "غير نشط"}
        """

        QMessageBox.information(self, "معاينة الحساب", preview_text)

    def save_account(self):
        """حفظ الحساب الجديد"""
        if not self.validate_data():
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)

            data = self.collect_form_data()
            account_id = self.save_to_database(data)

            if account_id:
                self.account_created.emit(account_id)
                QMessageBox.information(self, "نجح", "تم إنشاء الحساب بنجاح")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إنشاء الحساب")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الحساب: {str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.supplier_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المورد")
            return False

        if not self.currency_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار العملة")
            return False

        if not self.account_number_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الحساب")
            return False

        # التحقق من عدم تكرار رقم الحساب
        if self.check_account_number_exists():
            QMessageBox.warning(self, "تحذير", "رقم الحساب موجود بالفعل")
            return False

        return True

    def check_account_number_exists(self):
        """التحقق من وجود رقم الحساب"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM supplier_accounts WHERE account_number = ?",
                         (self.account_number_input.text().strip(),))
            count = cursor.fetchone()[0]

            conn.close()
            return count > 0

        except Exception as e:
            print(f"خطأ في التحقق من رقم الحساب: {e}")
            return False

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        return {
            'supplier_id': self.supplier_combo.currentData(),
            'currency_id': self.currency_combo.currentData(),
            'account_number': self.account_number_input.text().strip(),
            'opening_balance': self.opening_balance_input.value(),
            'current_balance': self.opening_balance_input.value(),
            'available_balance': self.opening_balance_input.value(),
            'blocked_balance': 0.00,
            'credit_limit': self.credit_limit_input.value(),
            'used_credit': 0.00,
            'available_credit': self.credit_limit_input.value(),
            'minimum_balance': self.minimum_balance_input.value(),
            'maximum_balance': self.maximum_balance_input.value(),
            'max_transaction_amount': self.max_transaction_input.value(),
            'daily_limit': self.daily_limit_input.value(),
            'monthly_limit': self.monthly_limit_input.value(),
            'is_active': self.is_active_checkbox.isChecked(),
            'allow_overdraft': self.allow_overdraft_checkbox.isChecked(),
            'is_frozen': self.is_frozen_checkbox.isChecked(),
            'grace_period_days': self.grace_period_input.value(),
            'interest_rate': self.interest_rate_input.value(),
            'maintenance_fee': self.maintenance_fee_input.value(),
            'transaction_fee': self.transaction_fee_input.value(),
            'statement_frequency': self.statement_frequency_combo.currentText(),
            'enable_email_reports': self.email_reports_checkbox.isChecked(),
            'enable_sms_alerts': self.sms_alerts_checkbox.isChecked(),
            'enable_balance_alerts': self.enable_alerts_checkbox.isChecked(),
            'low_balance_alert_threshold': self.low_balance_alert_input.value(),
            'high_balance_alert_threshold': self.high_balance_alert_input.value(),
            'notes': self.account_notes_input.toPlainText().strip(),
            'opening_date': self.opening_date.date().toString("yyyy-MM-dd")
        }

    def save_to_database(self, data):
        """حفظ البيانات في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # التأكد من وجود الجدول
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS supplier_accounts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    supplier_id INTEGER NOT NULL,
                    currency_id INTEGER NOT NULL,
                    account_number TEXT UNIQUE NOT NULL,
                    opening_balance DECIMAL(15,2) DEFAULT 0.00,
                    current_balance DECIMAL(15,2) DEFAULT 0.00,
                    available_balance DECIMAL(15,2) DEFAULT 0.00,
                    blocked_balance DECIMAL(15,2) DEFAULT 0.00,
                    credit_limit DECIMAL(15,2) DEFAULT 0.00,
                    used_credit DECIMAL(15,2) DEFAULT 0.00,
                    available_credit DECIMAL(15,2) DEFAULT 0.00,
                    minimum_balance DECIMAL(15,2) DEFAULT 0.00,
                    maximum_balance DECIMAL(15,2) DEFAULT *********.99,
                    max_transaction_amount DECIMAL(15,2) DEFAULT 100000.00,
                    daily_limit DECIMAL(15,2) DEFAULT 500000.00,
                    monthly_limit DECIMAL(15,2) DEFAULT ********.00,
                    is_active BOOLEAN DEFAULT 1,
                    allow_overdraft BOOLEAN DEFAULT 0,
                    is_frozen BOOLEAN DEFAULT 0,
                    grace_period_days INTEGER DEFAULT 30,
                    interest_rate DECIMAL(5,2) DEFAULT 0.00,
                    maintenance_fee DECIMAL(10,2) DEFAULT 0.00,
                    transaction_fee DECIMAL(10,2) DEFAULT 0.00,
                    statement_frequency TEXT DEFAULT 'شهري',
                    enable_email_reports BOOLEAN DEFAULT 0,
                    enable_sms_alerts BOOLEAN DEFAULT 0,
                    enable_balance_alerts BOOLEAN DEFAULT 1,
                    low_balance_alert_threshold DECIMAL(15,2) DEFAULT 1000.00,
                    high_balance_alert_threshold DECIMAL(15,2) DEFAULT 100000.00,
                    notes TEXT,
                    opening_date DATE,
                    last_transaction_date TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                    FOREIGN KEY (currency_id) REFERENCES currencies(id)
                )
            """)

            # إدراج الحساب الجديد
            insert_query = """
                INSERT INTO supplier_accounts (
                    supplier_id, currency_id, account_number, opening_balance,
                    current_balance, available_balance, blocked_balance,
                    credit_limit, used_credit, available_credit,
                    minimum_balance, maximum_balance, max_transaction_amount,
                    daily_limit, monthly_limit, is_active, allow_overdraft,
                    is_frozen, grace_period_days, interest_rate,
                    maintenance_fee, transaction_fee, statement_frequency,
                    enable_email_reports, enable_sms_alerts, enable_balance_alerts,
                    low_balance_alert_threshold, high_balance_alert_threshold,
                    notes, opening_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            cursor.execute(insert_query, (
                data['supplier_id'], data['currency_id'], data['account_number'],
                data['opening_balance'], data['current_balance'], data['available_balance'],
                data['blocked_balance'], data['credit_limit'], data['used_credit'],
                data['available_credit'], data['minimum_balance'], data['maximum_balance'],
                data['max_transaction_amount'], data['daily_limit'], data['monthly_limit'],
                data['is_active'], data['allow_overdraft'], data['is_frozen'],
                data['grace_period_days'], data['interest_rate'], data['maintenance_fee'],
                data['transaction_fee'], data['statement_frequency'], data['enable_email_reports'],
                data['enable_sms_alerts'], data['enable_balance_alerts'],
                data['low_balance_alert_threshold'], data['high_balance_alert_threshold'],
                data['notes'], data['opening_date']
            ))

            account_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return account_id

        except Exception as e:
            if conn:
                conn.rollback()
                conn.close()
            raise e
