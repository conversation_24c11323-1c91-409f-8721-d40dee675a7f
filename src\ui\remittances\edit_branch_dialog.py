# -*- coding: utf-8 -*-
"""
نافذة تعديل الفرع
Edit Branch Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QTextEdit, QPushButton,
                               QGroupBox, QCheckBox, QMessageBox, QFrame, QProgressBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

import sqlite3
from pathlib import Path
from datetime import datetime

class EditBranchDialog(QDialog):
    """نافذة تعديل الفرع"""
    
    branch_updated = Signal(int)  # إشارة عند تحديث الفرع
    
    def __init__(self, branch_id, parent=None):
        super().__init__(parent)
        self.branch_id = branch_id
        self.setWindowTitle("تعديل الفرع - ProShipment")
        self.setMinimumSize(700, 700)
        self.resize(800, 700)
        self.setModal(True)

        # توسيط النافذة
        self.center_window()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.load_branch_data()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel("تعديل بيانات الفرع")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # الجهة الأم
        parent_group = QGroupBox("الجهة الأم")
        parent_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        parent_layout = QGridLayout(parent_group)
        parent_layout.setSpacing(10)
        
        # نوع الجهة الأم
        parent_layout.addWidget(QLabel("نوع الجهة: *"), 0, 0)
        self.parent_type_combo = QComboBox()
        self.parent_type_combo.addItems(["بنك", "صراف"])
        self.parent_type_combo.setMinimumHeight(35)
        parent_layout.addWidget(self.parent_type_combo, 0, 1, 1, 3)

        # اختيار الجهة الأم
        parent_layout.addWidget(QLabel("اختيار الجهة: *"), 1, 0)
        self.parent_entity_combo = QComboBox()
        self.parent_entity_combo.setMinimumHeight(35)
        parent_layout.addWidget(self.parent_entity_combo, 1, 1, 1, 3)
        
        layout.addWidget(parent_group)
        
        # المعلومات الأساسية
        basic_group = QGroupBox("المعلومات الأساسية")
        basic_group.setStyleSheet(parent_group.styleSheet())
        basic_layout = QGridLayout(basic_group)
        basic_layout.setSpacing(10)
        
        # اسم الفرع
        basic_layout.addWidget(QLabel("اسم الفرع: *"), 0, 0)
        self.branch_name_input = QLineEdit()
        self.branch_name_input.setPlaceholderText("أدخل اسم الفرع...")
        self.branch_name_input.setMinimumHeight(35)
        basic_layout.addWidget(self.branch_name_input, 0, 1, 1, 3)

        # اسم الفرع بالإنجليزية
        basic_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 1, 0)
        self.branch_name_en_input = QLineEdit()
        self.branch_name_en_input.setPlaceholderText("Branch Name in English...")
        self.branch_name_en_input.setMinimumHeight(35)
        basic_layout.addWidget(self.branch_name_en_input, 1, 1, 1, 3)

        # رمز الفرع
        basic_layout.addWidget(QLabel("رمز الفرع:"), 2, 0)
        self.branch_code_input = QLineEdit()
        self.branch_code_input.setPlaceholderText("مثال: BR001")
        self.branch_code_input.setMaxLength(10)
        self.branch_code_input.setMinimumHeight(35)
        basic_layout.addWidget(self.branch_code_input, 2, 1)

        # نوع الفرع
        basic_layout.addWidget(QLabel("نوع الفرع:"), 2, 2)
        self.branch_type_combo = QComboBox()
        self.branch_type_combo.addItems([
            "فرع رئيسي",
            "فرع فرعي",
            "مكتب تمثيلي",
            "نقطة خدمة",
            "صراف آلي",
            "مركز تحويل"
        ])
        self.branch_type_combo.setMinimumHeight(35)
        basic_layout.addWidget(self.branch_type_combo, 2, 3)
        
        layout.addWidget(basic_group)
        
        # معلومات الاتصال والموقع
        contact_group = QGroupBox("معلومات الاتصال والموقع")
        contact_group.setStyleSheet(parent_group.styleSheet())
        contact_layout = QGridLayout(contact_group)
        contact_layout.setSpacing(10)
        
        # العنوان التفصيلي
        contact_layout.addWidget(QLabel("العنوان التفصيلي:"), 0, 0)
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(100)
        self.address_input.setMinimumHeight(100)
        self.address_input.setPlaceholderText("أدخل العنوان التفصيلي للفرع...")
        contact_layout.addWidget(self.address_input, 0, 1, 1, 3)

        # الهاتف
        contact_layout.addWidget(QLabel("الهاتف:"), 1, 0)
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("+966 11 123 4567")
        self.phone_input.setMinimumHeight(35)
        contact_layout.addWidget(self.phone_input, 1, 1)

        # الفاكس
        contact_layout.addWidget(QLabel("الفاكس:"), 1, 2)
        self.fax_input = QLineEdit()
        self.fax_input.setPlaceholderText("+966 11 123 4568")
        self.fax_input.setMinimumHeight(35)
        contact_layout.addWidget(self.fax_input, 1, 3)

        # البريد الإلكتروني
        contact_layout.addWidget(QLabel("البريد الإلكتروني:"), 2, 0)
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        self.email_input.setMinimumHeight(35)
        contact_layout.addWidget(self.email_input, 2, 1, 1, 3)
        
        layout.addWidget(contact_group)
        
        # معلومات إضافية
        extra_group = QGroupBox("معلومات إضافية")
        extra_group.setStyleSheet(parent_group.styleSheet())
        extra_layout = QGridLayout(extra_group)
        extra_layout.setSpacing(10)
        
        # اسم مدير الفرع
        extra_layout.addWidget(QLabel("مدير الفرع:"), 0, 0)
        self.manager_name_input = QLineEdit()
        self.manager_name_input.setPlaceholderText("اسم مدير الفرع...")
        self.manager_name_input.setMinimumHeight(35)
        extra_layout.addWidget(self.manager_name_input, 0, 1)

        # هاتف المدير
        extra_layout.addWidget(QLabel("هاتف المدير:"), 0, 2)
        self.manager_phone_input = QLineEdit()
        self.manager_phone_input.setPlaceholderText("+966 50 123 4567")
        self.manager_phone_input.setMinimumHeight(35)
        extra_layout.addWidget(self.manager_phone_input, 0, 3)

        # الملاحظات
        extra_layout.addWidget(QLabel("ملاحظات:"), 1, 0)
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(100)
        self.notes_input.setMinimumHeight(100)
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        extra_layout.addWidget(self.notes_input, 1, 1, 1, 3)

        # حالة الفرع
        self.is_active_checkbox = QCheckBox("الفرع نشط")
        self.is_active_checkbox.setChecked(True)
        self.is_active_checkbox.setMinimumHeight(30)
        extra_layout.addWidget(self.is_active_checkbox, 2, 0, 1, 2)
        
        layout.addWidget(extra_group)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # أزرار الإجراءات
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.addStretch()
        
        self.save_btn = QPushButton("💾 حفظ التعديلات")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_branch)
        self.cancel_btn.clicked.connect(self.reject)
        
        # تحديث قائمة الجهات عند تغيير النوع
        self.parent_type_combo.currentTextChanged.connect(self.update_parent_entities)
        
        # تحويل رمز الفرع للأحرف الكبيرة
        self.branch_code_input.textChanged.connect(
            lambda: self.branch_code_input.setText(self.branch_code_input.text().upper())
        )

    def update_parent_entities(self):
        """تحديث قائمة الجهات الأم"""
        self.parent_entity_combo.clear()
        parent_type = self.parent_type_combo.currentText()

        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            if parent_type == "بنك":
                cursor.execute("SELECT id, name FROM banks WHERE is_active = 1 ORDER BY name")
            else:  # صراف
                cursor.execute("SELECT id, name FROM exchanges WHERE is_active = 1 ORDER BY name")

            entities = cursor.fetchall()

            self.parent_entity_combo.addItem("اختر الجهة...", None)
            for entity in entities:
                self.parent_entity_combo.addItem(entity[1], entity[0])

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل الجهات: {e}")
            self.parent_entity_combo.addItem("لا توجد جهات متاحة", None)

    def load_branch_data(self):
        """تحميل بيانات الفرع للتعديل"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("""
                SELECT name, name_en, code, type, parent_type, parent_id, bank_id, exchange_id,
                       address, phone, fax, email, manager_name, manager_phone, notes, is_active
                FROM branches WHERE id = ?
            """, (self.branch_id,))

            branch_data = cursor.fetchone()
            conn.close()

            if branch_data:
                self.branch_name_input.setText(branch_data[0] or "")
                self.branch_name_en_input.setText(branch_data[1] or "")
                self.branch_code_input.setText(branch_data[2] or "")

                # تعيين نوع الفرع
                if branch_data[3]:
                    index = self.branch_type_combo.findText(branch_data[3])
                    if index >= 0:
                        self.branch_type_combo.setCurrentIndex(index)

                # تعيين نوع الجهة الأم
                if branch_data[4]:
                    if branch_data[4] == "bank":
                        self.parent_type_combo.setCurrentText("بنك")
                    elif branch_data[4] == "exchange":
                        self.parent_type_combo.setCurrentText("صراف")
                    else:
                        self.parent_type_combo.setCurrentText(branch_data[4])

                # تحديث قائمة الجهات
                self.update_parent_entities()

                # تعيين الجهة الأم
                if branch_data[5]:
                    for i in range(self.parent_entity_combo.count()):
                        if self.parent_entity_combo.itemData(i) == branch_data[5]:
                            self.parent_entity_combo.setCurrentIndex(i)
                            break

                self.address_input.setPlainText(branch_data[8] or "")
                self.phone_input.setText(branch_data[9] or "")
                self.fax_input.setText(branch_data[10] or "")
                self.email_input.setText(branch_data[11] or "")
                self.manager_name_input.setText(branch_data[12] or "")
                self.manager_phone_input.setText(branch_data[13] or "")
                self.notes_input.setPlainText(branch_data[14] or "")
                self.is_active_checkbox.setChecked(bool(branch_data[15]))
            else:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على بيانات الفرع")
                self.reject()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الفرع:\n{str(e)}")
            self.reject()

    def validate_form(self):
        """التحقق من صحة النموذج"""
        if not self.branch_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم الفرع")
            self.branch_name_input.setFocus()
            return False

        if self.parent_entity_combo.currentData() is None:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى اختيار الجهة الأم")
            self.parent_entity_combo.setFocus()
            return False

        return True

    def save_branch(self):
        """حفظ تعديلات الفرع"""
        if not self.validate_form():
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.save_btn.setEnabled(False)

            # جمع البيانات
            branch_data = {
                'name': self.branch_name_input.text().strip(),
                'name_en': self.branch_name_en_input.text().strip() or None,
                'code': self.branch_code_input.text().strip() or None,
                'type': self.branch_type_combo.currentText(),
                'parent_type': self.parent_type_combo.currentText(),
                'parent_id': self.parent_entity_combo.currentData(),
                'address': self.address_input.toPlainText().strip() or None,
                'phone': self.phone_input.text().strip() or None,
                'fax': self.fax_input.text().strip() or None,
                'email': self.email_input.text().strip() or None,
                'manager_name': self.manager_name_input.text().strip() or None,
                'manager_phone': self.manager_phone_input.text().strip() or None,
                'notes': self.notes_input.toPlainText().strip() or None,
                'is_active': self.is_active_checkbox.isChecked(),
                'updated_at': datetime.now().isoformat()
            }

            # حفظ في قاعدة البيانات
            success = self.update_branch_in_database(branch_data)

            if success:
                QMessageBox.information(self, "نجح الحفظ",
                                      f"تم تحديث الفرع '{branch_data['name']}' بنجاح")

                # إرسال إشارة
                self.branch_updated.emit(self.branch_id)
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في تحديث الفرع")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ التعديلات:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.save_btn.setEnabled(True)

    def update_branch_in_database(self, data):
        """تحديث الفرع في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحديد bank_id أو exchange_id بناءً على نوع الجهة الأم
            bank_id = None
            exchange_id = None

            if data['parent_type'] == "بنك":
                bank_id = data['parent_id']
            else:  # صراف
                exchange_id = data['parent_id']

            # التحقق من وجود عمود updated_at
            cursor.execute("PRAGMA table_info(branches)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]

            # تحديث الفرع
            if 'updated_at' in column_names:
                cursor.execute("""
                    UPDATE branches SET
                        name = ?, name_en = ?, code = ?, type = ?, parent_type = ?, parent_id = ?,
                        bank_id = ?, exchange_id = ?, address = ?, phone = ?, fax = ?, email = ?,
                        manager_name = ?, manager_phone = ?, notes = ?, is_active = ?, updated_at = ?
                    WHERE id = ?
                """, (
                    data['name'], data['name_en'], data['code'], data['type'],
                    data['parent_type'], data['parent_id'], bank_id, exchange_id,
                    data['address'], data['phone'], data['fax'], data['email'],
                    data['manager_name'], data['manager_phone'], data['notes'],
                    data['is_active'], data['updated_at'], self.branch_id
                ))
            else:
                cursor.execute("""
                    UPDATE branches SET
                        name = ?, name_en = ?, code = ?, type = ?, parent_type = ?, parent_id = ?,
                        bank_id = ?, exchange_id = ?, address = ?, phone = ?, fax = ?, email = ?,
                        manager_name = ?, manager_phone = ?, notes = ?, is_active = ?
                    WHERE id = ?
                """, (
                    data['name'], data['name_en'], data['code'], data['type'],
                    data['parent_type'], data['parent_id'], bank_id, exchange_id,
                    data['address'], data['phone'], data['fax'], data['email'],
                    data['manager_name'], data['manager_phone'], data['notes'],
                    data['is_active'], self.branch_id
                ))

            conn.commit()
            conn.close()

            return True

        except sqlite3.IntegrityError as e:
            if "UNIQUE constraint failed: branches.code" in str(e):
                QMessageBox.warning(self, "خطأ", "رمز الفرع موجود بالفعل. يرجى اختيار رمز آخر.")
            else:
                QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {e}")
            return False
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات: {e}")
            return False
