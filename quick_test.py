#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للنموذج التجريبي
Quick Test for the Prototype
"""

import sys
import os

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    # فحص إصدار Python
    python_version = sys.version_info
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        return False
    
    # فحص PySide6
    try:
        import PySide6
        print(f"✅ PySide6 {PySide6.__version__}")
    except ImportError:
        print("❌ خطأ: PySide6 غير مثبت")
        print("💡 قم بتثبيته باستخدام: pip install PySide6")
        return False
    
    # فحص الملفات المطلوبة
    required_files = [
        "main_window_prototype.py",
        "enhanced_ui_components.py",
        "run_prototype.py"
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ ملف مفقود: {file}")
            return False
    
    return True

def run_test():
    """تشغيل الاختبار"""
    print("\n🚀 بدء الاختبار...")
    
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        return False
    
    print("\n✅ جميع المتطلبات متوفرة")
    print("🎯 يمكنك الآن تشغيل النموذج باستخدام:")
    print("   python run_prototype.py")
    
    # اختبار استيراد المكونات
    try:
        print("\n🧪 اختبار استيراد المكونات...")
        
        from main_window_prototype import MainWindowPrototype, GradientWidget, LogoWidget
        print("✅ main_window_prototype")
        
        from enhanced_ui_components import EnhancedMainContent, DashboardWidget
        print("✅ enhanced_ui_components")
        
        print("\n🎉 جميع المكونات تعمل بشكل صحيح!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في استيراد المكونات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🧪 اختبار سريع لنظام CnX ERP")
    print("=" * 50)
    
    success = run_test()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ الاختبار مكتمل بنجاح!")
        print("🚀 يمكنك الآن تشغيل النموذج التجريبي")
    else:
        print("❌ فشل الاختبار!")
        print("🔧 يرجى إصلاح المشاكل المذكورة أعلاه")
    print("=" * 50)

if __name__ == "__main__":
    main()
