# -*- coding: utf-8 -*-
"""
أدوات تنسيق التواريخ والأرقام
Date and Number Formatters
"""

import locale
import datetime
from decimal import Decimal
from PySide6.QtCore import QLocale

class WindowsFormatters:
    """فئة لتنسيق التواريخ والأرقام بنفس تنسيق نظام الويندوز"""
    
    def __init__(self):
        """تهيئة المنسقات"""
        try:
            # محاولة تعيين اللغة العربية
            locale.setlocale(locale.LC_ALL, 'Arabic_Saudi Arabia.1256')
        except:
            try:
                # محاولة تعيين اللغة العربية بصيغة أخرى
                locale.setlocale(locale.LC_ALL, 'ar_SA.UTF-8')
            except:
                try:
                    # استخدام الإعدادات الافتراضية للنظام
                    locale.setlocale(locale.LC_ALL, '')
                except:
                    # في حالة فشل كل المحاولات، استخدم الإعدادات الافتراضية
                    pass
        
        # إعداد QLocale للتنسيق
        self.qt_locale = QLocale(QLocale.Arabic, QLocale.SaudiArabia)
        
    def format_date(self, date_obj, format_type='short'):
        """
        تنسيق التاريخ بنفس تنسيق الويندوز
        
        Args:
            date_obj: كائن التاريخ (datetime, date, أو string)
            format_type: نوع التنسيق ('short', 'long', 'medium')
        
        Returns:
            str: التاريخ منسق
        """
        if not date_obj:
            return ""
            
        try:
            # تحويل إلى datetime إذا لزم الأمر
            if isinstance(date_obj, str):
                if 'T' in date_obj:
                    date_obj = datetime.datetime.fromisoformat(date_obj.replace('Z', '+00:00'))
                else:
                    date_obj = datetime.datetime.strptime(date_obj, '%Y-%m-%d')
            elif isinstance(date_obj, datetime.date) and not isinstance(date_obj, datetime.datetime):
                date_obj = datetime.datetime.combine(date_obj, datetime.time())
            
            # تنسيقات مختلفة حسب النوع المطلوب
            if format_type == 'short':
                # تنسيق قصير: dd/MM/yyyy
                return date_obj.strftime('%d/%m/%Y')
            elif format_type == 'long':
                # تنسيق طويل: dddd، dd MMMM yyyy
                arabic_months = [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                ]
                arabic_days = [
                    'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
                ]
                
                day_name = arabic_days[date_obj.weekday()]
                month_name = arabic_months[date_obj.month - 1]
                
                return f"{day_name}، {date_obj.day:02d} {month_name} {date_obj.year}"
            else:  # medium
                # تنسيق متوسط: dd MMM yyyy
                arabic_months_short = [
                    'ينا', 'فبر', 'مار', 'أبر', 'ماي', 'يون',
                    'يول', 'أغس', 'سبت', 'أكت', 'نوف', 'ديس'
                ]
                month_name = arabic_months_short[date_obj.month - 1]
                return f"{date_obj.day:02d} {month_name} {date_obj.year}"
                
        except Exception as e:
            # في حالة الخطأ، إرجاع تنسيق بسيط
            if hasattr(date_obj, 'strftime'):
                return date_obj.strftime('%Y-%m-%d')
            else:
                return str(date_obj)
    
    def format_datetime(self, datetime_obj, format_type='short'):
        """
        تنسيق التاريخ والوقت بنفس تنسيق الويندوز
        
        Args:
            datetime_obj: كائن التاريخ والوقت
            format_type: نوع التنسيق ('short', 'long', 'medium')
        
        Returns:
            str: التاريخ والوقت منسق
        """
        if not datetime_obj:
            return ""
            
        try:
            # تحويل إلى datetime إذا لزم الأمر
            if isinstance(datetime_obj, str):
                if 'T' in datetime_obj:
                    datetime_obj = datetime.datetime.fromisoformat(datetime_obj.replace('Z', '+00:00'))
                else:
                    datetime_obj = datetime.datetime.strptime(datetime_obj, '%Y-%m-%d %H:%M:%S')
            
            # تنسيق التاريخ
            date_part = self.format_date(datetime_obj, format_type)
            
            # تنسيق الوقت
            if format_type == 'short':
                time_part = datetime_obj.strftime('%H:%M')
            else:
                time_part = datetime_obj.strftime('%H:%M:%S')
            
            return f"{date_part} {time_part}"
            
        except Exception as e:
            # في حالة الخطأ، إرجاع تنسيق بسيط
            if hasattr(datetime_obj, 'strftime'):
                return datetime_obj.strftime('%Y-%m-%d %H:%M:%S')
            else:
                return str(datetime_obj)
    
    def format_number(self, number, decimal_places=2, use_grouping=True):
        """
        تنسيق الأرقام بنفس تنسيق الويندوز
        
        Args:
            number: الرقم المراد تنسيقه
            decimal_places: عدد الخانات العشرية
            use_grouping: استخدام فواصل الآلاف
        
        Returns:
            str: الرقم منسق
        """
        if number is None or number == "":
            return "0.00"
            
        try:
            # تحويل إلى رقم
            if isinstance(number, str):
                # استبدال الفاصلة العربية بالنقطة الإنجليزية
                number = number.replace('،', '.')
                number = float(number)
            elif isinstance(number, Decimal):
                number = float(number)
            
            # تنسيق الرقم
            if use_grouping:
                # استخدام فواصل الآلاف
                formatted = f"{number:,.{decimal_places}f}"
                # استبدال الفاصلة الإنجليزية بالفاصلة العربية للعشريات
                if '.' in formatted:
                    integer_part, decimal_part = formatted.rsplit('.', 1)
                    formatted = f"{integer_part}،{decimal_part}"
            else:
                formatted = f"{number:.{decimal_places}f}"
                # استبدال النقطة بالفاصلة العربية
                formatted = formatted.replace('.', '،')
            
            return formatted
            
        except Exception as e:
            return str(number)
    
    def format_currency(self, amount, currency='ريال', decimal_places=2):
        """
        تنسيق المبالغ المالية
        
        Args:
            amount: المبلغ
            currency: العملة
            decimal_places: عدد الخانات العشرية
        
        Returns:
            str: المبلغ منسق مع العملة
        """
        if amount is None or amount == "":
            return f"0،00 {currency}"
            
        try:
            formatted_number = self.format_number(amount, decimal_places, use_grouping=True)
            return f"{formatted_number} {currency}"
        except:
            return f"{amount} {currency}"
    
    def format_percentage(self, value, decimal_places=1):
        """
        تنسيق النسب المئوية
        
        Args:
            value: القيمة (0.15 = 15%)
            decimal_places: عدد الخانات العشرية
        
        Returns:
            str: النسبة منسقة
        """
        if value is None or value == "":
            return "0%"
            
        try:
            percentage = float(value) * 100
            formatted = self.format_number(percentage, decimal_places, use_grouping=False)
            return f"{formatted}%"
        except:
            return f"{value}%"

# إنشاء مثيل عام للاستخدام
windows_formatter = WindowsFormatters()

# دوال مساعدة للاستخدام السريع
def format_date(date_obj, format_type='short'):
    """تنسيق التاريخ"""
    return windows_formatter.format_date(date_obj, format_type)

def format_datetime(datetime_obj, format_type='short'):
    """تنسيق التاريخ والوقت"""
    return windows_formatter.format_datetime(datetime_obj, format_type)

def format_number(number, decimal_places=2, use_grouping=True):
    """تنسيق الأرقام"""
    return windows_formatter.format_number(number, decimal_places, use_grouping)

def format_currency(amount, currency='ريال', decimal_places=2):
    """تنسيق المبالغ المالية"""
    return windows_formatter.format_currency(amount, currency, decimal_places)

def format_percentage(value, decimal_places=1):
    """تنسيق النسب المئوية"""
    return windows_formatter.format_percentage(value, decimal_places)
