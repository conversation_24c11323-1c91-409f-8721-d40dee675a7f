# -*- coding: utf-8 -*-
"""
ويدجت تتبع الحوالات
Remittances Tracking Widget
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QLineEdit, QComboBox, QDateEdit,
                               QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
                               QGroupBox, QFormLayout, QMessageBox, QDialog, QDialogButtonBox,
                               QSplitter, QFrame, QTextEdit, QProgressBar, QTreeWidget,
                               QTreeWidgetItem, QTabWidget, QScrollArea)
from PySide6.QtCore import Qt, Signal, QDate, QTimer
from PySide6.QtGui import QFont, QIcon, QPalette, QColor, QPainter

from ...database.database_manager import DatabaseManager
from ...database.models import (Remittance, RemittanceStatusHistory, RemittanceStatus, 
                                Supplier, Currency, Bank, User)


class RemittancesTrackingWidget(QWidget):
    """ويدجت تتبع الحوالات"""
    
    # الإشارات
    status_updated = Signal(str, str, str)  # remittance_number, old_status, new_status
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.current_remittance = None
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # شريط البحث والتصفية
        self.create_search_bar()
        layout.addWidget(self.search_frame)
        
        # المحتوى الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # الجانب الأيسر - قائمة الحوالات
        self.create_remittances_list()
        splitter.addWidget(self.list_group)
        
        # الجانب الأيمن - تفاصيل التتبع
        self.create_tracking_details()
        splitter.addWidget(self.details_group)
        
        # تعيين النسب
        splitter.setSizes([400, 600])
        
    def create_search_bar(self):
        """إنشاء شريط البحث والتصفية"""
        self.search_frame = QFrame()
        self.search_frame.setFrameStyle(QFrame.StyledPanel)
        search_layout = QHBoxLayout(self.search_frame)
        
        # البحث
        search_label = QLabel("🔍 بحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث برقم الحوالة أو اسم المورد...")
        self.search_edit.setMaximumWidth(300)
        
        # تصفية الحالة
        status_label = QLabel("الحالة:")
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItem("جميع الحالات", "")
        for status in RemittanceStatus:
            self.status_filter_combo.addItem(status.value, status.name)
            
        # تصفية التاريخ
        date_label = QLabel("من تاريخ:")
        self.date_from_edit = QDateEdit()
        self.date_from_edit.setDate(QDate.currentDate().addDays(-30))
        self.date_from_edit.setCalendarPopup(True)
        
        to_date_label = QLabel("إلى تاريخ:")
        self.date_to_edit = QDateEdit()
        self.date_to_edit.setDate(QDate.currentDate())
        self.date_to_edit.setCalendarPopup(True)
        
        # زر التصفية
        self.filter_btn = QPushButton("🔍 تصفية")
        self.filter_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #17a2b8, stop:1 #138496);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #138496, stop:1 #117a8b);
            }
        """)
        
        # زر مسح التصفية
        self.clear_filter_btn = QPushButton("🗑️ مسح")
        self.clear_filter_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6c757d, stop:1 #5a6268);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a6268, stop:1 #545b62);
            }
        """)
        
        # إضافة العناصر
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(status_label)
        search_layout.addWidget(self.status_filter_combo)
        search_layout.addWidget(date_label)
        search_layout.addWidget(self.date_from_edit)
        search_layout.addWidget(to_date_label)
        search_layout.addWidget(self.date_to_edit)
        search_layout.addWidget(self.filter_btn)
        search_layout.addWidget(self.clear_filter_btn)
        search_layout.addStretch()
        
    def create_remittances_list(self):
        """إنشاء قائمة الحوالات"""
        self.list_group = QGroupBox("📋 قائمة الحوالات")
        list_layout = QVBoxLayout(self.list_group)
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_layout = QGridLayout(stats_frame)
        
        # عدادات الحالات
        self.draft_count_label = QLabel("مسودة: 0")
        self.pending_count_label = QLabel("في الانتظار: 0")
        self.approved_count_label = QLabel("معتمدة: 0")
        self.sent_count_label = QLabel("مرسلة: 0")
        self.received_count_label = QLabel("مستلمة: 0")
        self.completed_count_label = QLabel("مكتملة: 0")
        self.cancelled_count_label = QLabel("ملغية: 0")
        
        # تطبيق ألوان مختلفة للحالات
        self.draft_count_label.setStyleSheet("color: #6c757d; font-weight: bold;")
        self.pending_count_label.setStyleSheet("color: #ffc107; font-weight: bold;")
        self.approved_count_label.setStyleSheet("color: #17a2b8; font-weight: bold;")
        self.sent_count_label.setStyleSheet("color: #007bff; font-weight: bold;")
        self.received_count_label.setStyleSheet("color: #fd7e14; font-weight: bold;")
        self.completed_count_label.setStyleSheet("color: #28a745; font-weight: bold;")
        self.cancelled_count_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        
        stats_layout.addWidget(self.draft_count_label, 0, 0)
        stats_layout.addWidget(self.pending_count_label, 0, 1)
        stats_layout.addWidget(self.approved_count_label, 0, 2)
        stats_layout.addWidget(self.sent_count_label, 1, 0)
        stats_layout.addWidget(self.received_count_label, 1, 1)
        stats_layout.addWidget(self.completed_count_label, 1, 2)
        stats_layout.addWidget(self.cancelled_count_label, 1, 3)
        
        list_layout.addWidget(stats_frame)
        
        # الجدول
        self.remittances_table = QTableWidget()
        self.remittances_table.setColumnCount(6)
        self.remittances_table.setHorizontalHeaderLabels([
            "رقم الحوالة", "المورد", "المبلغ", "العملة", "التاريخ", "الحالة"
        ])
        
        # إعداد الجدول
        header = self.remittances_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.remittances_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.remittances_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.remittances_table.setAlternatingRowColors(True)
        self.remittances_table.setSortingEnabled(True)
        
        list_layout.addWidget(self.remittances_table)
        
    def create_tracking_details(self):
        """إنشاء تفاصيل التتبع"""
        self.details_group = QGroupBox("📍 تفاصيل التتبع")
        details_layout = QVBoxLayout(self.details_group)
        
        # تبويبات التفاصيل
        self.details_tab = QTabWidget()
        details_layout.addWidget(self.details_tab)
        
        # تبويب معلومات الحوالة
        self.create_remittance_info_tab()
        self.details_tab.addTab(self.info_widget, "📄 معلومات الحوالة")
        
        # تبويب تاريخ الحالات
        self.create_status_history_tab()
        self.details_tab.addTab(self.history_widget, "📈 تاريخ الحالات")
        
        # تبويب تحديث الحالة
        self.create_status_update_tab()
        self.details_tab.addTab(self.update_widget, "✏️ تحديث الحالة")
        
        # تبويب الخط الزمني
        self.create_timeline_tab()
        self.details_tab.addTab(self.timeline_widget, "⏰ الخط الزمني")
        
    def create_remittance_info_tab(self):
        """إنشاء تبويب معلومات الحوالة"""
        self.info_widget = QWidget()
        info_layout = QFormLayout(self.info_widget)
        
        # معلومات أساسية
        self.info_number_label = QLabel("-")
        self.info_supplier_label = QLabel("-")
        self.info_amount_label = QLabel("-")
        self.info_currency_label = QLabel("-")
        self.info_date_label = QLabel("-")
        self.info_status_label = QLabel("-")
        
        # معلومات البنوك
        self.info_sender_bank_label = QLabel("-")
        self.info_receiver_bank_label = QLabel("-")
        self.info_purpose_label = QLabel("-")
        
        # معلومات التتبع
        self.info_created_by_label = QLabel("-")
        self.info_created_at_label = QLabel("-")
        self.info_approved_by_label = QLabel("-")
        self.info_approved_at_label = QLabel("-")
        
        # إضافة الحقول
        info_layout.addRow("رقم الحوالة:", self.info_number_label)
        info_layout.addRow("المورد:", self.info_supplier_label)
        info_layout.addRow("المبلغ:", self.info_amount_label)
        info_layout.addRow("العملة:", self.info_currency_label)
        info_layout.addRow("التاريخ:", self.info_date_label)
        info_layout.addRow("الحالة:", self.info_status_label)
        info_layout.addRow("البنك المرسل:", self.info_sender_bank_label)
        info_layout.addRow("البنك المستقبل:", self.info_receiver_bank_label)
        info_layout.addRow("الغرض:", self.info_purpose_label)
        info_layout.addRow("أنشئ بواسطة:", self.info_created_by_label)
        info_layout.addRow("تاريخ الإنشاء:", self.info_created_at_label)
        info_layout.addRow("اعتمد بواسطة:", self.info_approved_by_label)
        info_layout.addRow("تاريخ الاعتماد:", self.info_approved_at_label)
        
    def create_status_history_tab(self):
        """إنشاء تبويب تاريخ الحالات"""
        self.history_widget = QWidget()
        history_layout = QVBoxLayout(self.history_widget)
        
        # جدول التاريخ
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(5)
        self.history_table.setHorizontalHeaderLabels([
            "التاريخ", "الحالة السابقة", "الحالة الجديدة", "المستخدم", "السبب"
        ])
        
        # إعداد الجدول
        header = self.history_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.history_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.history_table.setAlternatingRowColors(True)
        self.history_table.setSortingEnabled(True)
        
        history_layout.addWidget(self.history_table)
        
    def create_status_update_tab(self):
        """إنشاء تبويب تحديث الحالة"""
        self.update_widget = QWidget()
        update_layout = QFormLayout(self.update_widget)
        
        # الحالة الجديدة
        self.new_status_combo = QComboBox()
        for status in RemittanceStatus:
            self.new_status_combo.addItem(status.value, status)
            
        # سبب التغيير
        self.change_reason_edit = QLineEdit()
        self.change_reason_edit.setPlaceholderText("سبب تغيير الحالة...")
        
        # ملاحظات
        self.change_notes_edit = QTextEdit()
        self.change_notes_edit.setMaximumHeight(100)
        self.change_notes_edit.setPlaceholderText("ملاحظات إضافية...")
        
        # زر التحديث
        self.update_status_btn = QPushButton("🔄 تحديث الحالة")
        self.update_status_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e7e34, stop:1 #155724);
            }
        """)
        
        # إضافة الحقول
        update_layout.addRow("الحالة الجديدة:", self.new_status_combo)
        update_layout.addRow("سبب التغيير:", self.change_reason_edit)
        update_layout.addRow("ملاحظات:", self.change_notes_edit)
        update_layout.addRow("", self.update_status_btn)
        
    def create_timeline_tab(self):
        """إنشاء تبويب الخط الزمني"""
        self.timeline_widget = QWidget()
        timeline_layout = QVBoxLayout(self.timeline_widget)
        
        # منطقة التمرير للخط الزمني
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.timeline_content = QWidget()
        self.timeline_layout = QVBoxLayout(self.timeline_content)
        
        scroll_area.setWidget(self.timeline_content)
        timeline_layout.addWidget(scroll_area)

    def setup_connections(self):
        """إعداد الاتصالات"""
        # البحث والتصفية
        self.search_edit.textChanged.connect(self.filter_remittances)
        self.filter_btn.clicked.connect(self.apply_filters)
        self.clear_filter_btn.clicked.connect(self.clear_filters)

        # الجدول
        self.remittances_table.itemSelectionChanged.connect(self.on_selection_changed)

        # تحديث الحالة
        self.update_status_btn.clicked.connect(self.update_remittance_status)

    def load_data(self):
        """تحميل البيانات"""
        try:
            self.load_remittances()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}")

    def load_remittances(self):
        """تحميل قائمة الحوالات"""
        try:
            session = self.db_manager.get_session()

            # تطبيق التصفية
            query = session.query(Remittance)

            # تصفية التاريخ
            date_from = self.date_from_edit.date().toPython()
            date_to = self.date_to_edit.date().toPython()
            query = query.filter(Remittance.remittance_date >= date_from)
            query = query.filter(Remittance.remittance_date <= date_to)

            # تصفية الحالة
            status_filter = self.status_filter_combo.currentData()
            if status_filter:
                status_enum = getattr(RemittanceStatus, status_filter)
                query = query.filter(Remittance.status == status_enum)

            remittances = query.order_by(Remittance.created_at.desc()).all()

            # تحديث الجدول
            self.remittances_table.setRowCount(len(remittances))

            # عدادات الحالات
            status_counts = {status: 0 for status in RemittanceStatus}

            for row, remittance in enumerate(remittances):
                # رقم الحوالة
                self.remittances_table.setItem(row, 0, QTableWidgetItem(remittance.remittance_number))

                # المورد
                supplier_name = remittance.supplier.name if remittance.supplier else ""
                self.remittances_table.setItem(row, 1, QTableWidgetItem(supplier_name))

                # المبلغ
                amount_item = QTableWidgetItem(f"{remittance.amount:.3f}")
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.remittances_table.setItem(row, 2, amount_item)

                # العملة
                currency_code = remittance.currency.code if remittance.currency else ""
                self.remittances_table.setItem(row, 3, QTableWidgetItem(currency_code))

                # التاريخ
                date_str = remittance.remittance_date.strftime("%Y-%m-%d") if remittance.remittance_date else ""
                self.remittances_table.setItem(row, 4, QTableWidgetItem(date_str))

                # الحالة
                status_item = QTableWidgetItem(remittance.status.value if remittance.status else "")

                # تلوين الحالة
                if remittance.status == RemittanceStatus.DRAFT:
                    status_item.setBackground(QColor("#f8f9fa"))
                elif remittance.status == RemittanceStatus.PENDING:
                    status_item.setBackground(QColor("#fff3cd"))
                elif remittance.status == RemittanceStatus.APPROVED:
                    status_item.setBackground(QColor("#d1ecf1"))
                elif remittance.status == RemittanceStatus.SENT:
                    status_item.setBackground(QColor("#cce5ff"))
                elif remittance.status == RemittanceStatus.RECEIVED:
                    status_item.setBackground(QColor("#ffe0b3"))
                elif remittance.status == RemittanceStatus.COMPLETED:
                    status_item.setBackground(QColor("#d4edda"))
                elif remittance.status == RemittanceStatus.CANCELLED:
                    status_item.setBackground(QColor("#f8d7da"))
                elif remittance.status == RemittanceStatus.REJECTED:
                    status_item.setBackground(QColor("#f5c6cb"))

                self.remittances_table.setItem(row, 5, status_item)

                # حفظ معرف الحوالة
                self.remittances_table.item(row, 0).setData(Qt.UserRole, remittance.id)

                # عد الحالات
                if remittance.status:
                    status_counts[remittance.status] += 1

            # تحديث عدادات الحالات
            self.draft_count_label.setText(f"مسودة: {status_counts[RemittanceStatus.DRAFT]}")
            self.pending_count_label.setText(f"في الانتظار: {status_counts[RemittanceStatus.PENDING]}")
            self.approved_count_label.setText(f"معتمدة: {status_counts[RemittanceStatus.APPROVED]}")
            self.sent_count_label.setText(f"مرسلة: {status_counts[RemittanceStatus.SENT]}")
            self.received_count_label.setText(f"مستلمة: {status_counts[RemittanceStatus.RECEIVED]}")
            self.completed_count_label.setText(f"مكتملة: {status_counts[RemittanceStatus.COMPLETED]}")
            self.cancelled_count_label.setText(f"ملغية: {status_counts[RemittanceStatus.CANCELLED]}")

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الحوالات:\n{str(e)}")

    def filter_remittances(self):
        """تصفية الحوالات حسب النص المدخل"""
        search_text = self.search_edit.text().lower()

        for row in range(self.remittances_table.rowCount()):
            show_row = False

            # البحث في الأعمدة المحددة
            for col in [0, 1]:  # رقم الحوالة واسم المورد
                item = self.remittances_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break

            self.remittances_table.setRowHidden(row, not show_row)

    def apply_filters(self):
        """تطبيق التصفية"""
        self.load_remittances()

    def clear_filters(self):
        """مسح التصفية"""
        self.search_edit.clear()
        self.status_filter_combo.setCurrentIndex(0)
        self.date_from_edit.setDate(QDate.currentDate().addDays(-30))
        self.date_to_edit.setDate(QDate.currentDate())
        self.load_remittances()

    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.remittances_table.currentRow()

        if current_row >= 0:
            # الحصول على معرف الحوالة
            remittance_id = self.remittances_table.item(current_row, 0).data(Qt.UserRole)
            if remittance_id:
                self.load_remittance_details(remittance_id)
        else:
            self.clear_details()

    def load_remittance_details(self, remittance_id):
        """تحميل تفاصيل الحوالة"""
        try:
            session = self.db_manager.get_session()
            remittance = session.query(Remittance).get(remittance_id)

            if not remittance:
                session.close()
                return

            self.current_remittance = remittance_id

            # تحديث معلومات الحوالة
            self.update_remittance_info(remittance)

            # تحميل تاريخ الحالات
            self.load_status_history(remittance_id)

            # تحديث الخط الزمني
            self.update_timeline(remittance)

            # تحديث تبويب تحديث الحالة
            self.update_status_update_tab(remittance)

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل تفاصيل الحوالة:\n{str(e)}")

    def update_remittance_info(self, remittance):
        """تحديث معلومات الحوالة"""
        self.info_number_label.setText(remittance.remittance_number or "-")
        self.info_supplier_label.setText(remittance.supplier.name if remittance.supplier else "-")
        self.info_amount_label.setText(f"{remittance.amount:.3f}" if remittance.amount else "-")
        self.info_currency_label.setText(remittance.currency.code if remittance.currency else "-")
        self.info_date_label.setText(remittance.remittance_date.strftime("%Y-%m-%d") if remittance.remittance_date else "-")
        self.info_status_label.setText(remittance.status.value if remittance.status else "-")
        self.info_sender_bank_label.setText(remittance.sender_bank.name if remittance.sender_bank else "-")
        self.info_receiver_bank_label.setText(remittance.receiver_bank.name if remittance.receiver_bank else "-")
        self.info_purpose_label.setText(remittance.purpose or "-")

        # معلومات التتبع
        self.info_created_by_label.setText(remittance.created_by_user.username if remittance.created_by_user else "-")
        self.info_created_at_label.setText(remittance.created_at.strftime("%Y-%m-%d %H:%M:%S") if remittance.created_at else "-")
        self.info_approved_by_label.setText(remittance.approved_by_user.username if remittance.approved_by_user else "-")
        self.info_approved_at_label.setText(remittance.approved_at.strftime("%Y-%m-%d %H:%M:%S") if remittance.approved_at else "-")

    def load_status_history(self, remittance_id):
        """تحميل تاريخ الحالات"""
        try:
            session = self.db_manager.get_session()
            history = session.query(RemittanceStatusHistory).filter(
                RemittanceStatusHistory.remittance_id == remittance_id
            ).order_by(RemittanceStatusHistory.changed_at.desc()).all()

            self.history_table.setRowCount(len(history))

            for row, record in enumerate(history):
                # التاريخ
                date_str = record.changed_at.strftime("%Y-%m-%d %H:%M:%S") if record.changed_at else ""
                self.history_table.setItem(row, 0, QTableWidgetItem(date_str))

                # الحالة السابقة
                old_status = record.old_status.value if record.old_status else "-"
                self.history_table.setItem(row, 1, QTableWidgetItem(old_status))

                # الحالة الجديدة
                new_status = record.new_status.value if record.new_status else ""
                self.history_table.setItem(row, 2, QTableWidgetItem(new_status))

                # المستخدم
                user_name = record.changed_by_user.username if record.changed_by_user else ""
                self.history_table.setItem(row, 3, QTableWidgetItem(user_name))

                # السبب
                self.history_table.setItem(row, 4, QTableWidgetItem(record.change_reason or ""))

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل تاريخ الحالات:\n{str(e)}")

    def update_timeline(self, remittance):
        """تحديث الخط الزمني"""
        # مسح المحتوى السابق
        for i in reversed(range(self.timeline_layout.count())):
            child = self.timeline_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # إنشاء عناصر الخط الزمني
        timeline_steps = [
            (RemittanceStatus.DRAFT, "📝", "إنشاء الحوالة"),
            (RemittanceStatus.PENDING, "⏳", "في الانتظار"),
            (RemittanceStatus.APPROVED, "✅", "معتمدة"),
            (RemittanceStatus.SENT, "📤", "مرسلة"),
            (RemittanceStatus.RECEIVED, "📥", "مستلمة"),
            (RemittanceStatus.COMPLETED, "🎉", "مكتملة")
        ]

        current_status = remittance.status

        for i, (status, icon, title) in enumerate(timeline_steps):
            step_widget = self.create_timeline_step(status, icon, title, current_status, i == len(timeline_steps) - 1)
            self.timeline_layout.addWidget(step_widget)

        # إضافة مساحة فارغة في النهاية
        self.timeline_layout.addStretch()

    def create_timeline_step(self, status, icon, title, current_status, is_last):
        """إنشاء خطوة في الخط الزمني"""
        step_frame = QFrame()
        step_layout = QHBoxLayout(step_frame)
        step_layout.setContentsMargins(10, 10, 10, 10)

        # تحديد حالة الخطوة
        is_completed = False
        is_current = False

        if current_status:
            status_order = list(RemittanceStatus)
            current_index = status_order.index(current_status)
            step_index = status_order.index(status)

            is_completed = step_index <= current_index
            is_current = step_index == current_index

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setFixedSize(40, 40)

        if is_completed:
            icon_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #28a745, stop:1 #20c997);
                    border-radius: 20px;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                }
            """)
        elif is_current:
            icon_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ffc107, stop:1 #fd7e14);
                    border-radius: 20px;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                }
            """)
        else:
            icon_label.setStyleSheet("""
                QLabel {
                    background: #e9ecef;
                    border-radius: 20px;
                    color: #6c757d;
                    font-size: 16px;
                }
            """)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 10, QFont.Bold if is_current else QFont.Normal))

        if is_completed:
            title_label.setStyleSheet("color: #28a745;")
        elif is_current:
            title_label.setStyleSheet("color: #ffc107;")
        else:
            title_label.setStyleSheet("color: #6c757d;")

        # الخط الواصل
        if not is_last:
            line_label = QLabel("|")
            line_label.setAlignment(Qt.AlignCenter)
            line_label.setFixedSize(20, 30)

            if is_completed:
                line_label.setStyleSheet("color: #28a745; font-size: 20px; font-weight: bold;")
            else:
                line_label.setStyleSheet("color: #e9ecef; font-size: 20px;")

        # تجميع العناصر
        step_layout.addWidget(icon_label)
        step_layout.addWidget(title_label)
        step_layout.addStretch()

        return step_frame

    def update_status_update_tab(self, remittance):
        """تحديث تبويب تحديث الحالة"""
        # تعيين الحالة الحالية
        if remittance.status:
            for i in range(self.new_status_combo.count()):
                if self.new_status_combo.itemData(i) == remittance.status:
                    self.new_status_combo.setCurrentIndex(i)
                    break

        # تفعيل/تعطيل الزر حسب الحالة
        can_update = remittance.status not in [RemittanceStatus.COMPLETED, RemittanceStatus.CANCELLED]
        self.update_status_btn.setEnabled(can_update)

    def update_remittance_status(self):
        """تحديث حالة الحوالة"""
        if not self.current_remittance:
            QMessageBox.warning(self, "تحذير", "يجب اختيار حوالة أولاً")
            return

        new_status = self.new_status_combo.currentData()
        change_reason = self.change_reason_edit.text().strip()
        change_notes = self.change_notes_edit.toPlainText().strip()

        if not change_reason:
            QMessageBox.warning(self, "تحذير", "يجب إدخال سبب التغيير")
            return

        try:
            session = self.db_manager.get_session()
            remittance = session.query(Remittance).get(self.current_remittance)

            if not remittance:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الحوالة")
                session.close()
                return

            old_status = remittance.status

            # التحقق من صحة التغيير
            if old_status == new_status:
                QMessageBox.information(self, "معلومات", "الحالة الجديدة مطابقة للحالة الحالية")
                session.close()
                return

            # تحديث الحالة
            remittance.status = new_status

            # إنشاء سجل في تاريخ الحالات
            status_history = RemittanceStatusHistory(
                remittance_id=self.current_remittance,
                old_status=old_status,
                new_status=new_status,
                change_reason=change_reason,
                notes=change_notes,
                changed_by=1  # يجب الحصول على معرف المستخدم الحالي
            )

            session.add(status_history)
            session.commit()

            # إرسال إشارة
            old_status_text = old_status.value if old_status else ""
            new_status_text = new_status.value if new_status else ""
            self.status_updated.emit(remittance.remittance_number, old_status_text, new_status_text)

            QMessageBox.information(self, "نجح", "تم تحديث حالة الحوالة بنجاح")

            # تحديث البيانات
            self.load_remittances()
            self.load_remittance_details(self.current_remittance)

            # مسح النموذج
            self.change_reason_edit.clear()
            self.change_notes_edit.clear()

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث الحالة:\n{str(e)}")

    def clear_details(self):
        """مسح التفاصيل"""
        self.current_remittance = None

        # مسح معلومات الحوالة
        labels = [
            self.info_number_label, self.info_supplier_label, self.info_amount_label,
            self.info_currency_label, self.info_date_label, self.info_status_label,
            self.info_sender_bank_label, self.info_receiver_bank_label, self.info_purpose_label,
            self.info_created_by_label, self.info_created_at_label,
            self.info_approved_by_label, self.info_approved_at_label
        ]

        for label in labels:
            label.setText("-")

        # مسح تاريخ الحالات
        self.history_table.setRowCount(0)

        # مسح الخط الزمني
        for i in reversed(range(self.timeline_layout.count())):
            child = self.timeline_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # تعطيل تحديث الحالة
        self.update_status_btn.setEnabled(False)

    def show_search(self):
        """إظهار نافذة البحث المتقدم"""
        # يمكن إضافة نافذة بحث متقدمة هنا
        self.search_edit.setFocus()

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
