#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حل مبسط لإصلاح Oracle
Simple Oracle Fix
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    print("🔧 إصلاح Oracle - الحل المبسط")
    print("=" * 40)
    
    try:
        import cx_Oracle
        print("✅ cx_Oracle متاح")
        
        # معلومات الاتصال
        username = "proshipment"
        password = "ys123"
        host = "localhost"
        port = 1521
        sid = "orcl"
        
        print(f"🔗 محاولة الاتصال بـ {username}@{host}:{port}/{sid}")
        
        # إنشاء DSN والاتصال
        dsn = cx_Oracle.makedsn(host, port, sid=sid)
        connection = cx_Oracle.connect(username, password, dsn)
        cursor = connection.cursor()
        
        print("✅ تم الاتصال بـ Oracle بنجاح")
        
        # إنشاء sequence بسيط لوحدات القياس
        try:
            cursor.execute("CREATE SEQUENCE units_of_measure_seq START WITH 1 INCREMENT BY 1")
            print("✅ تم إنشاء sequence لوحدات القياس")
        except:
            print("⚠️ sequence موجود مسبقاً")
        
        # إنشاء trigger بسيط لوحدات القياس
        try:
            cursor.execute("""
                CREATE OR REPLACE TRIGGER units_of_measure_trigger
                BEFORE INSERT ON units_of_measure
                FOR EACH ROW
                BEGIN
                    IF :NEW.id IS NULL THEN
                        :NEW.id := units_of_measure_seq.NEXTVAL;
                    END IF;
                END;
            """)
            print("✅ تم إنشاء trigger لوحدات القياس")
        except Exception as e:
            print(f"❌ خطأ في trigger: {e}")
        
        # إنشاء sequence لمجموعات الأصناف
        try:
            cursor.execute("CREATE SEQUENCE item_groups_seq START WITH 1 INCREMENT BY 1")
            print("✅ تم إنشاء sequence لمجموعات الأصناف")
        except:
            print("⚠️ sequence موجود مسبقاً")
        
        # إنشاء trigger لمجموعات الأصناف
        try:
            cursor.execute("""
                CREATE OR REPLACE TRIGGER item_groups_trigger
                BEFORE INSERT ON item_groups
                FOR EACH ROW
                BEGIN
                    IF :NEW.id IS NULL THEN
                        :NEW.id := item_groups_seq.NEXTVAL;
                    END IF;
                END;
            """)
            print("✅ تم إنشاء trigger لمجموعات الأصناف")
        except Exception as e:
            print(f"❌ خطأ في trigger: {e}")
        
        # إدراج وحدة قياس تجريبية
        try:
            cursor.execute("""
                INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
                VALUES ('قطعة', 'Piece', 'قطعة', 'pcs', 'وحدة العد الأساسية', 1, SYSDATE, SYSDATE)
            """)
            print("✅ تم إدراج وحدة قياس تجريبية")
        except:
            print("⚠️ وحدة القياس موجودة مسبقاً")
        
        # إدراج مجموعة أصناف تجريبية
        try:
            cursor.execute("""
                INSERT INTO item_groups (name, name_en, description, is_active, created_at, updated_at)
                VALUES ('إلكترونيات', 'Electronics', 'الأجهزة الإلكترونية', 1, SYSDATE, SYSDATE)
            """)
            print("✅ تم إدراج مجموعة أصناف تجريبية")
        except:
            print("⚠️ مجموعة الأصناف موجودة مسبقاً")
        
        # تأكيد التغييرات
        connection.commit()
        
        # فحص النتائج
        cursor.execute("SELECT COUNT(*) FROM units_of_measure")
        units_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM item_groups")
        groups_count = cursor.fetchone()[0]
        
        print(f"\n📊 النتائج:")
        print(f"   وحدات القياس: {units_count}")
        print(f"   مجموعات الأصناف: {groups_count}")
        
        cursor.close()
        connection.close()
        
        if units_count > 0 and groups_count > 0:
            print("\n✅ تم إصلاح Oracle بنجاح!")
            
            # اختبار SQLAlchemy
            print("\n🧪 اختبار SQLAlchemy...")
            from src.database.universal_database_manager import UniversalDatabaseManager
            from src.database.oracle_config import DatabaseConfigManager
            
            config_manager = DatabaseConfigManager("src/database/config/database.json")
            config = config_manager.load_config()
            db_manager = UniversalDatabaseManager(config)
            
            if db_manager.test_connection():
                print("✅ SQLAlchemy يعمل بشكل صحيح")
                
                # تشغيل التطبيق
                print("\n🚀 تشغيل التطبيق...")
                import subprocess
                subprocess.Popen([sys.executable, "main.py"])
                print("✅ تم تشغيل التطبيق")
                print("💡 يمكنك الآن استخدام إدارة وحدات القياس")
                
                return True
            else:
                print("❌ مشكلة في SQLAlchemy")
                return False
        else:
            print("❌ لم يتم إدراج البيانات بشكل صحيح")
            return False
            
    except ImportError:
        print("❌ cx_Oracle غير مثبت")
        print("💡 قم بتثبيته: pip install cx_Oracle")
        return False
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 تم إصلاح Oracle وتشغيل التطبيق بنجاح!")
        print("📋 للوصول لإدارة وحدات القياس:")
        print("   1. افتح التطبيق")
        print("   2. اذهب إلى إدارة الأصناف")
        print("   3. اختر وحدات القياس")
    else:
        print("\n❌ فشل في الإصلاح")
    
    sys.exit(0 if success else 1)
