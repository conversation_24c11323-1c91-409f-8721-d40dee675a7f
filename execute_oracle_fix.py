#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنفيذ إصلاح Oracle وتشغيل التطبيق
Execute Oracle Fix and Run Application
"""

import sys
import os
from pathlib import Path
import subprocess

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def execute_sql_file():
    """تنفيذ ملف SQL لإصلاح Oracle"""
    
    print("🔧 تنفيذ إصلاح Oracle...")
    
    try:
        # معلومات الاتصال
        username = "proshipment"
        password = "ys123"
        host = "localhost"
        port = "1521"
        sid = "orcl"
        
        # إنشاء connection string
        connection_string = f"{username}/{password}@{host}:{port}/{sid}"
        
        # تنفيذ ملف SQL
        sql_file = "oracle_direct_fix.sql"
        
        if not Path(sql_file).exists():
            print(f"❌ ملف SQL غير موجود: {sql_file}")
            return False
        
        print(f"📄 تنفيذ ملف: {sql_file}")
        print(f"🔗 الاتصال: {username}@{host}:{port}/{sid}")
        
        # تنفيذ SQL باستخدام sqlplus إذا كان متاحاً
        try:
            cmd = f'sqlplus -S {connection_string} @{sql_file}'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print("✅ تم تنفيذ SQL بنجاح")
                print("📊 النتائج:")
                print(result.stdout)
                return True
            else:
                print(f"❌ خطأ في تنفيذ SQL: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("⏰ انتهت مهلة تنفيذ SQL")
            return False
        except FileNotFoundError:
            print("⚠️ sqlplus غير متاح، سنحاول طريقة أخرى...")
            return execute_sql_with_python()
            
    except Exception as e:
        print(f"❌ خطأ في تنفيذ SQL: {e}")
        return False

def execute_sql_with_python():
    """تنفيذ SQL باستخدام Python"""
    
    print("🐍 تنفيذ SQL باستخدام Python...")
    
    try:
        import cx_Oracle
        
        # معلومات الاتصال
        username = "proshipment"
        password = "ys123"
        host = "localhost"
        port = 1521
        sid = "orcl"
        
        # إنشاء DSN
        dsn = cx_Oracle.makedsn(host, port, sid=sid)
        
        # الاتصال
        connection = cx_Oracle.connect(username, password, dsn)
        cursor = connection.cursor()
        
        print("✅ تم الاتصال بـ Oracle")
        
        # قراءة وتنفيذ ملف SQL
        sql_file = Path("oracle_direct_fix.sql")
        if not sql_file.exists():
            print("❌ ملف SQL غير موجود")
            return False
        
        sql_content = sql_file.read_text(encoding='utf-8')
        
        # تقسيم SQL إلى أوامر منفصلة
        sql_commands = []
        current_command = ""
        
        for line in sql_content.split('\n'):
            line = line.strip()
            if line.startswith('--') or not line:
                continue
            
            current_command += line + " "
            
            if line.endswith(';') or line.endswith('/'):
                if current_command.strip():
                    sql_commands.append(current_command.strip().rstrip(';').rstrip('/'))
                current_command = ""
        
        # تنفيذ الأوامر
        success_count = 0
        error_count = 0
        
        for i, command in enumerate(sql_commands):
            if not command.strip():
                continue
                
            try:
                cursor.execute(command)
                success_count += 1
                print(f"✅ تم تنفيذ أمر {i+1}")
            except cx_Oracle.DatabaseError as e:
                error_code, error_message = e.args[0].code, e.args[0].message
                if "already exists" in error_message or "name is already used" in error_message:
                    print(f"⚠️ أمر {i+1}: موجود مسبقاً")
                    success_count += 1
                else:
                    print(f"❌ خطأ في أمر {i+1}: {error_message}")
                    error_count += 1
        
        # تأكيد التغييرات
        connection.commit()
        
        # فحص النتائج
        cursor.execute("SELECT COUNT(*) FROM units_of_measure")
        units_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM item_groups")
        groups_count = cursor.fetchone()[0]
        
        print(f"\n📊 النتائج:")
        print(f"   أوامر نجحت: {success_count}")
        print(f"   أوامر فشلت: {error_count}")
        print(f"   وحدات القياس: {units_count}")
        print(f"   مجموعات الأصناف: {groups_count}")
        
        cursor.close()
        connection.close()
        
        return units_count > 0 and groups_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في تنفيذ SQL بـ Python: {e}")
        return False

def test_application():
    """اختبار التطبيق"""
    
    print("\n🧪 اختبار التطبيق...")
    
    try:
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        
        print(f"📋 نوع قاعدة البيانات: {config.type.value}")
        
        db_manager = UniversalDatabaseManager(config)
        
        if db_manager.test_connection():
            print("✅ اتصال التطبيق ناجح")
            
            with db_manager.get_session() as session:
                from src.database.models import UnitOfMeasure, ItemGroup
                
                units_count = session.query(UnitOfMeasure).count()
                groups_count = session.query(ItemGroup).count()
                
                print(f"   وحدات القياس: {units_count}")
                print(f"   مجموعات الأصناف: {groups_count}")
                
                if units_count > 0 and groups_count > 0:
                    print("✅ البيانات متاحة في التطبيق")
                    return True
                else:
                    print("❌ البيانات غير متاحة في التطبيق")
                    return False
        else:
            print("❌ فشل اتصال التطبيق")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_application():
    """تشغيل التطبيق"""
    
    print("\n🚀 تشغيل التطبيق...")
    
    try:
        # تشغيل التطبيق
        subprocess.Popen([sys.executable, "main.py"], cwd=str(Path(__file__).parent))
        print("✅ تم تشغيل التطبيق")
        print("💡 يمكنك الآن استخدام التطبيق")
        print("💡 للوصول لإدارة وحدات القياس: إدارة الأصناف → وحدات القياس")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 إصلاح Oracle وتشغيل التطبيق")
    print("=" * 50)
    
    # تنفيذ إصلاح Oracle
    if not execute_sql_file():
        print("❌ فشل في إصلاح Oracle")
        return False
    
    # اختبار التطبيق
    if not test_application():
        print("❌ فشل في اختبار التطبيق")
        return False
    
    # تشغيل التطبيق
    if not run_application():
        print("❌ فشل في تشغيل التطبيق")
        return False
    
    print("\n🎉 تم إصلاح Oracle وتشغيل التطبيق بنجاح!")
    print("✅ قاعدة البيانات: Oracle")
    print("✅ البيانات: متاحة")
    print("✅ التطبيق: يعمل")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 يرجى التحقق من:")
        print("   - تشغيل خدمة Oracle")
        print("   - صحة معلومات الاتصال")
        print("   - صلاحيات المستخدم")
    sys.exit(0 if success else 1)
