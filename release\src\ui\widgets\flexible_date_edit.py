"""
حقل تاريخ مرن يدعم الإدخال اليدوي والتحويل التلقائي للتنسيق
"""

from PySide6.QtWidgets import QLineEdit, QDateEdit, QHBoxLayout, QWidget, QPushButton
from PySide6.QtCore import QDate, Signal, Qt
from PySide6.QtGui import QValidator
import re
from datetime import datetime


class FlexibleDateValidator(QValidator):
    """مدقق مخصص لحقول التاريخ المرنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
    
    def validate(self, input_str, pos):
        """التحقق من صحة النص المدخل"""
        if not input_str:
            return QValidator.Acceptable, input_str, pos
        
        # السماح بالأرقام والفواصل فقط
        if re.match(r'^[\d/\-\.]*$', input_str):
            return QValidator.Acceptable, input_str, pos
        
        return QValidator.Invalid, input_str, pos


class FlexibleDateEdit(QWidget):
    """حقل تاريخ مرن يدعم الإدخال اليدوي والتحويل التلقائي"""
    
    dateChanged = Signal(QDate)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # حقل النص للإدخال المرن
        self.text_edit = QLineEdit()
        self.text_edit.setPlaceholderText("dd/mm/yyyy أو ddmmyyyy")
        self.text_edit.setValidator(FlexibleDateValidator())
        
        # حقل التاريخ المخفي للتحويل
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setVisible(False)
        
        # زر التقويم
        self.calendar_btn = QPushButton("📅")
        self.calendar_btn.setMaximumWidth(30)
        self.calendar_btn.setToolTip("فتح التقويم")
        
        layout.addWidget(self.text_edit)
        layout.addWidget(self.calendar_btn)
        
        # تطبيق الأنماط
        self.apply_styles()
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        style = """
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
                selection-background-color: #3498db;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QPushButton {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: #ecf0f1;
                font-size: 16px;
                padding: 6px;
            }
            QPushButton:hover {
                background-color: #d5dbdb;
                border-color: #95a5a6;
            }
            QPushButton:pressed {
                background-color: #bdc3c7;
            }
        """
        self.setStyleSheet(style)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.text_edit.textChanged.connect(self.on_text_changed)
        self.text_edit.editingFinished.connect(self.on_editing_finished)
        self.calendar_btn.clicked.connect(self.show_calendar)
        self.date_edit.dateChanged.connect(self.on_date_changed)
        
    def on_text_changed(self, text):
        """معالج تغيير النص"""
        # تحويل النص تلقائياً أثناء الكتابة
        if len(text) >= 6:  # الحد الأدنى للتاريخ
            converted_date = self.parse_date_string(text)
            if converted_date.isValid():
                self.date_edit.setDate(converted_date)
                
    def on_editing_finished(self):
        """معالج انتهاء التحرير"""
        text = self.text_edit.text().strip()
        if text:
            converted_date = self.parse_date_string(text)
            if converted_date.isValid():
                # تحديث النص بالتنسيق الصحيح
                formatted_text = converted_date.toString("dd/MM/yyyy")
                self.text_edit.setText(formatted_text)
                self.date_edit.setDate(converted_date)
                self.dateChanged.emit(converted_date)
            else:
                # إذا كان التاريخ غير صحيح، استخدم التاريخ الحالي
                current_date = QDate.currentDate()
                self.text_edit.setText(current_date.toString("dd/MM/yyyy"))
                self.date_edit.setDate(current_date)
                self.dateChanged.emit(current_date)
                
    def show_calendar(self):
        """عرض التقويم"""
        # إنشاء نافذة تقويم مؤقتة
        temp_date_edit = QDateEdit(self)
        temp_date_edit.setDate(self.date_edit.date())
        temp_date_edit.setCalendarPopup(True)
        temp_date_edit.dateChanged.connect(self.on_calendar_date_selected)
        
        # فتح التقويم
        temp_date_edit.calendarWidget().show()
        
    def on_calendar_date_selected(self, date):
        """معالج اختيار التاريخ من التقويم"""
        self.set_date(date)
        
    def on_date_changed(self, date):
        """معالج تغيير التاريخ"""
        self.dateChanged.emit(date)
        
    def parse_date_string(self, date_str):
        """تحليل نص التاريخ وتحويله إلى QDate"""
        if not date_str:
            return QDate()
            
        # إزالة المسافات والرموز غير المرغوب فيها
        clean_str = re.sub(r'[^\d/\-\.]', '', date_str)
        
        # أنماط التاريخ المختلفة
        patterns = [
            # ddmmyyyy (بدون فواصل)
            (r'^(\d{2})(\d{2})(\d{4})$', 'ddmmyyyy'),
            # dmmyyyy (تاريخ قصير بدون فواصل)
            (r'^(\d{1})(\d{2})(\d{4})$', 'dmmyyyy'),
            # dd/mm/yyyy
            (r'^(\d{1,2})/(\d{1,2})/(\d{4})$', 'dd/mm/yyyy'),
            # dd-mm-yyyy
            (r'^(\d{1,2})-(\d{1,2})-(\d{4})$', 'dd-mm-yyyy'),
            # dd.mm.yyyy
            (r'^(\d{1,2})\.(\d{1,2})\.(\d{4})$', 'dd.mm.yyyy'),
            # yyyy/mm/dd
            (r'^(\d{4})/(\d{1,2})/(\d{1,2})$', 'yyyy/mm/dd'),
            # yyyy-mm-dd
            (r'^(\d{4})-(\d{1,2})-(\d{1,2})$', 'yyyy-mm-dd'),
            # dd/mm/yy
            (r'^(\d{1,2})/(\d{1,2})/(\d{2})$', 'dd/mm/yy'),
        ]
        
        for pattern, format_name in patterns:
            match = re.match(pattern, clean_str)
            if match:
                try:
                    groups = match.groups()

                    if format_name == 'ddmmyyyy':
                        # تنسيق ddmmyyyy
                        day, month, year = groups
                        return QDate(int(year), int(month), int(day))

                    elif format_name == 'dmmyyyy':
                        # تنسيق dmmyyyy (تاريخ قصير)
                        day, month, year = groups
                        return QDate(int(year), int(month), int(day))

                    elif format_name in ['dd/mm/yyyy', 'dd-mm-yyyy', 'dd.mm.yyyy']:
                        # تنسيق dd/mm/yyyy وما شابه
                        day, month, year = groups
                        return QDate(int(year), int(month), int(day))

                    elif format_name in ['yyyy/mm/dd', 'yyyy-mm-dd']:
                        # تنسيق yyyy/mm/dd وما شابه
                        year, month, day = groups
                        return QDate(int(year), int(month), int(day))

                    elif format_name == 'dd/mm/yy':
                        # تنسيق dd/mm/yy
                        day, month, year = groups
                        # تحويل السنة المكونة من رقمين
                        year_int = int(year)
                        if year_int < 50:
                            year_int += 2000
                        else:
                            year_int += 1900
                        return QDate(year_int, int(month), int(day))

                except (ValueError, TypeError):
                    continue
                    
        return QDate()  # تاريخ غير صحيح
        
    def set_date(self, date):
        """تعيين التاريخ"""
        if isinstance(date, QDate) and date.isValid():
            self.date_edit.setDate(date)
            self.text_edit.setText(date.toString("dd/MM/yyyy"))
            self.dateChanged.emit(date)
            
    def date(self):
        """الحصول على التاريخ الحالي"""
        return self.date_edit.date()
        
    def setDate(self, date):
        """تعيين التاريخ (للتوافق مع QDateEdit)"""
        self.set_date(date)
        
    def setCalendarPopup(self, enable):
        """تفعيل/إلغاء تفعيل التقويم المنبثق"""
        self.calendar_btn.setVisible(enable)
        
    def setDisplayFormat(self, format_str):
        """تعيين تنسيق العرض"""
        # يمكن تطوير هذه الدالة لاحقاً لدعم تنسيقات مختلفة
        pass
        
    def setLocale(self, locale):
        """تعيين اللغة المحلية"""
        self.date_edit.setLocale(locale)
        
    def setStyleSheet(self, style):
        """تطبيق الأنماط المخصصة"""
        super().setStyleSheet(style)
        
    def setMinimumHeight(self, height):
        """تعيين الحد الأدنى للارتفاع"""
        self.text_edit.setMinimumHeight(height)
        super().setMinimumHeight(height)
        
    def setPlaceholderText(self, text):
        """تعيين النص التوضيحي"""
        self.text_edit.setPlaceholderText(text)

    def clear(self):
        """مسح محتوى الحقل"""
        self.text_edit.clear()
        self.date_edit.setDate(QDate.currentDate())

    def setText(self, text):
        """تعيين النص"""
        self.text_edit.setText(text)

    def text(self):
        """الحصول على النص"""
        return self.text_edit.text()
