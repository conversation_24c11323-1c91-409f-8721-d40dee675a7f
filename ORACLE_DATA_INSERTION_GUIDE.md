# دليل إدراج البيانات في Oracle
# Oracle Data Insertion Guide

## 🚨 المشكلة المحددة

لم يتم إدراج البيانات الافتراضية (وحدات القياس ومجموعات الأصناف) في قاعدة بيانات Oracle.

## 🔍 سبب المشكلة

الكود يتحقق من وجود بيانات في جدول `SystemSettings` وإذا وجد أي بيانات، فإنه يتوقف عن إدراج باقي البيانات الافتراضية.

## 🛠️ الحلول المتاحة

### الحل الأول: استخدام SQL مباشرة

#### 1. تشغيل سكريپت SQL
```sql
-- تشغيل الملف: insert_units_oracle.sql
-- في Oracle SQL Developer أو SQL*Plus
@insert_units_oracle.sql
```

#### 2. أو تنفيذ الأوامر يدوياً:

```sql
-- إدراج وحدة قياس أساسية
INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
VALUES ('قطعة', 'Piece', 'قطعة', 'pcs', 'وحدة العد الأساسية', 1, SYSDATE, SYSDATE);

-- إدراج مجموعة أصناف أساسية
INSERT INTO item_groups (name, name_en, description, is_active, created_at, updated_at)
VALUES ('إلكترونيات', 'Electronics', 'الأجهزة والمعدات الإلكترونية', 1, SYSDATE, SYSDATE);

COMMIT;
```

### الحل الثاني: استخدام Python Scripts

#### 1. تشغيل سكريپت الإدراج المباشر:
```bash
python direct_insert.py
```

#### 2. تشغيل سكريپت الإدراج بالقوة:
```bash
python force_insert_default_data.py
```

### الحل الثالث: إدراج البيانات من خلال التطبيق

#### 1. تشغيل التطبيق:
```bash
python main.py
```

#### 2. الذهاب إلى إدارة وحدات القياس وإضافة الوحدات يدوياً

## 📋 قائمة وحدات القياس الأساسية للإدراج

### وحدات الوزن:
- كيلوجرام (كجم / kg)
- جرام (جم / g)
- طن (طن / t)

### وحدات الطول:
- متر (م / m)
- سنتيمتر (سم / cm)
- مليمتر (مم / mm)

### وحدات الحجم:
- متر مكعب (م³ / m³)
- لتر (لتر / L)

### وحدات العد:
- قطعة (قطعة / pcs)
- عبوة (عبوة / pkg)
- صندوق (صندوق / box)
- كرتون (كرتون / ctn)
- زوج (زوج / pair)
- دستة (دستة / dz)

## 📂 مجموعات الأصناف الأساسية

1. إلكترونيات (Electronics)
2. ملابس (Clothing)
3. أغذية (Food)
4. أدوات منزلية (Home Appliances)
5. مواد بناء (Construction Materials)
6. قطع غيار (Spare Parts)
7. مستحضرات تجميل (Cosmetics)
8. كتب ومطبوعات (Books & Publications)

## 🔧 خطوات التحقق من نجاح الإدراج

### 1. من خلال Oracle SQL Developer:
```sql
-- فحص وحدات القياس
SELECT COUNT(*) as units_count FROM units_of_measure;
SELECT name, symbol, name_en FROM units_of_measure ORDER BY id;

-- فحص مجموعات الأصناف
SELECT COUNT(*) as groups_count FROM item_groups;
SELECT name, name_en FROM item_groups ORDER BY id;
```

### 2. من خلال Python:
```python
# تشغيل سكريپت الفحص
python simple_test.py
```

### 3. من خلال التطبيق:
1. تشغيل التطبيق: `python main.py`
2. الذهاب إلى "إدارة الأصناف" → "وحدات القياس"
3. التحقق من وجود البيانات في الجدول

## 🚨 استكشاف الأخطاء

### خطأ في الاتصال:
```bash
# اختبار الاتصال
python test_oracle_connection.py
```

### خطأ في الجداول:
```sql
-- التحقق من وجود الجداول
SELECT table_name FROM user_tables 
WHERE table_name IN ('UNITS_OF_MEASURE', 'ITEM_GROUPS');
```

### خطأ في الصلاحيات:
```sql
-- التحقق من الصلاحيات
SELECT * FROM user_tab_privs 
WHERE table_name IN ('UNITS_OF_MEASURE', 'ITEM_GROUPS');
```

## 📝 سكريپت إدراج سريع

إذا كنت تريد إدراج البيانات بسرعة، استخدم هذا السكريپت:

```sql
-- إدراج سريع لوحدات القياس الأساسية
INSERT ALL
  INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
    VALUES ('قطعة', 'Piece', 'قطعة', 'pcs', 'وحدة العد الأساسية', 1, SYSDATE, SYSDATE)
  INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
    VALUES ('كيلوجرام', 'Kilogram', 'كجم', 'kg', 'وحدة قياس الوزن الأساسية', 1, SYSDATE, SYSDATE)
  INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
    VALUES ('متر', 'Meter', 'م', 'm', 'وحدة قياس الطول الأساسية', 1, SYSDATE, SYSDATE)
  INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
    VALUES ('لتر', 'Liter', 'لتر', 'L', 'وحدة قياس السوائل', 1, SYSDATE, SYSDATE)
SELECT 1 FROM dual;

-- إدراج سريع لمجموعات الأصناف الأساسية
INSERT ALL
  INTO item_groups (name, name_en, description, is_active, created_at, updated_at)
    VALUES ('إلكترونيات', 'Electronics', 'الأجهزة والمعدات الإلكترونية', 1, SYSDATE, SYSDATE)
  INTO item_groups (name, name_en, description, is_active, created_at, updated_at)
    VALUES ('ملابس', 'Clothing', 'الملابس والأزياء', 1, SYSDATE, SYSDATE)
  INTO item_groups (name, name_en, description, is_active, created_at, updated_at)
    VALUES ('أغذية', 'Food', 'المواد الغذائية والمشروبات', 1, SYSDATE, SYSDATE)
SELECT 1 FROM dual;

COMMIT;
```

## ✅ التحقق من النجاح

بعد تنفيذ أي من الحلول أعلاه، تحقق من النجاح:

```sql
SELECT 'وحدات القياس' as نوع_البيانات, COUNT(*) as العدد FROM units_of_measure
UNION ALL
SELECT 'مجموعات الأصناف', COUNT(*) FROM item_groups;
```

يجب أن ترى:
- وحدات القياس: 4 أو أكثر
- مجموعات الأصناف: 3 أو أكثر

## 🎯 الخطوة التالية

بعد إدراج البيانات بنجاح:
1. تشغيل التطبيق: `python main.py`
2. الذهاب إلى "إدارة الأصناف" → "وحدات القياس"
3. التحقق من ظهور البيانات في الواجهة
4. إضافة المزيد من الوحدات حسب الحاجة

---

**ملاحظة**: تأكد من أن لديك صلاحيات INSERT على الجداول في قاعدة بيانات Oracle.
