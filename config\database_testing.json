{"type": "oracle", "oracle_config": {"host": "test-oracle-server.company.com", "port": 1521, "service_name": "PROSHIP_TEST", "sid": null, "username": "proshipment_test", "password": "test_password_456", "connection_type": "service_name", "pool_size": 10, "max_overflow": 20, "pool_timeout": 30, "pool_recycle": 3600, "pool_pre_ping": true, "use_ssl": true, "ssl_cert_path": "/path/to/ssl/cert.pem", "wallet_location": "/path/to/oracle/wallet", "encoding": "UTF-8", "nencoding": "UTF-8", "threaded": true, "auto_commit": false}, "sqlite_config": {"path": "data/proshipment_test.db", "timeout": 30, "check_same_thread": false}}