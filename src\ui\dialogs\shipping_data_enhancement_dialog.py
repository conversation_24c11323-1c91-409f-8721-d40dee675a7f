"""
نافذة إدارة تحسين بيانات شركات الشحن
تسمح للمستخدم بمراجعة وتصحيح أسماء شركات الشحن في قاعدة البيانات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                               QTableWidget, QTableWidgetItem, QHeaderView,
                               QLabel, QTextEdit, QTabWidget, QWidget,
                               QProgressBar, QMessageBox, QCheckBox,
                               QSpinBox, QGroupBox, QFormLayout, QSplitter)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont

from utils.shipping_data_enhancer import ShippingDataEnhancer

class EnhancementWorker(QThread):
    """عامل خيط منفصل لتحسين البيانات"""
    progress_updated = Signal(int)
    status_updated = Signal(str)
    finished = Signal(dict)
    
    def __init__(self, auto_correct=False, confidence_threshold=0.8):
        super().__init__()
        self.auto_correct = auto_correct
        self.confidence_threshold = confidence_threshold
        self.enhancer = ShippingDataEnhancer()
    
    def run(self):
        """تشغيل عملية التحسين"""
        try:
            self.status_updated.emit("🔍 تحليل البيانات...")
            self.progress_updated.emit(25)
            
            # تحليل البيانات
            analysis = self.enhancer.analyze_shipping_companies()
            
            self.status_updated.emit("🔧 تحسين البيانات...")
            self.progress_updated.emit(50)
            
            # تحسين البيانات
            enhancement_result = self.enhancer.enhance_shipping_companies(
                auto_correct=self.auto_correct,
                confidence_threshold=self.confidence_threshold
            )
            
            self.progress_updated.emit(100)
            self.status_updated.emit("✅ اكتمل التحسين")
            
            # دمج النتائج
            result = {
                "analysis": analysis,
                "enhancement": enhancement_result
            }
            
            self.finished.emit(result)
            
        except Exception as e:
            self.status_updated.emit(f"❌ خطأ: {str(e)}")
            self.finished.emit({"error": str(e)})

class ShippingDataEnhancementDialog(QDialog):
    """نافذة تحسين بيانات شركات الشحن"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.enhancer = ShippingDataEnhancer()
        self.enhancement_worker = None
        self.current_analysis = None
        self.setup_ui()
        self.setup_connections()
        
        # تحليل أولي للبيانات
        self.analyze_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تحسين بيانات شركات الشحن")
        self.setModal(True)
        self.resize(1000, 700)
        
        layout = QVBoxLayout(self)
        
        # العنوان
        title_label = QLabel("🚢 تحسين وتصحيح بيانات شركات الشحن")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # التبويبات
        self.tab_widget = QTabWidget()
        
        # تبويب التحليل
        self.analysis_tab = self.create_analysis_tab()
        self.tab_widget.addTab(self.analysis_tab, "📊 التحليل")
        
        # تبويب التصحيحات
        self.corrections_tab = self.create_corrections_tab()
        self.tab_widget.addTab(self.corrections_tab, "🔧 التصحيحات")
        
        # تبويب الإعدادات
        self.settings_tab = self.create_settings_tab()
        self.tab_widget.addTab(self.settings_tab, "⚙️ الإعدادات")
        
        layout.addWidget(self.tab_widget)
        
        # شريط التقدم والحالة
        status_layout = QHBoxLayout()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("جاهز")
        self.status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        status_layout.addWidget(self.status_label)
        
        layout.addLayout(status_layout)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.analyze_button = QPushButton("🔍 تحليل البيانات")
        self.analyze_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        self.enhance_button = QPushButton("🔧 تحسين البيانات")
        self.enhance_button.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        
        self.backup_button = QPushButton("💾 نسخة احتياطية")
        self.backup_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        
        self.close_button = QPushButton("إغلاق")
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addWidget(self.analyze_button)
        buttons_layout.addWidget(self.enhance_button)
        buttons_layout.addWidget(self.backup_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.close_button)
        
        layout.addLayout(buttons_layout)
    
    def create_analysis_tab(self):
        """إنشاء تبويب التحليل"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # ملخص الإحصائيات
        stats_group = QGroupBox("📈 ملخص الإحصائيات")
        stats_layout = QFormLayout(stats_group)
        
        self.total_shipments_label = QLabel("0")
        self.unique_companies_label = QLabel("0")
        self.valid_companies_label = QLabel("0")
        self.correctable_companies_label = QLabel("0")
        self.invalid_companies_label = QLabel("0")
        self.empty_companies_label = QLabel("0")
        
        stats_layout.addRow("إجمالي الشحنات:", self.total_shipments_label)
        stats_layout.addRow("شركات فريدة:", self.unique_companies_label)
        stats_layout.addRow("شركات صحيحة:", self.valid_companies_label)
        stats_layout.addRow("شركات قابلة للتصحيح:", self.correctable_companies_label)
        stats_layout.addRow("شركات غير معروفة:", self.invalid_companies_label)
        stats_layout.addRow("شحنات بدون شركة:", self.empty_companies_label)
        
        layout.addWidget(stats_group)
        
        # تقرير مفصل
        report_group = QGroupBox("📋 التقرير المفصل")
        report_layout = QVBoxLayout(report_group)
        
        self.report_text = QTextEdit()
        self.report_text.setReadOnly(True)
        self.report_text.setFont(QFont("Courier", 10))
        report_layout.addWidget(self.report_text)
        
        layout.addWidget(report_group)
        
        return tab
    
    def create_corrections_tab(self):
        """إنشاء تبويب التصحيحات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # جدول التصحيحات
        self.corrections_table = QTableWidget()
        self.corrections_table.setColumnCount(6)
        self.corrections_table.setHorizontalHeaderLabels([
            "رقم الشحنة", "الاسم الأصلي", "الاسم المقترح", "الثقة %", "الدولة", "النوع"
        ])
        
        # تعديل عرض الأعمدة
        header = self.corrections_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.corrections_table)
        
        # أزرار التصحيح
        corrections_buttons_layout = QHBoxLayout()
        
        self.apply_selected_button = QPushButton("✅ تطبيق المحدد")
        self.apply_all_button = QPushButton("✅ تطبيق الكل")
        self.reject_selected_button = QPushButton("❌ رفض المحدد")
        
        corrections_buttons_layout.addWidget(self.apply_selected_button)
        corrections_buttons_layout.addWidget(self.apply_all_button)
        corrections_buttons_layout.addWidget(self.reject_selected_button)
        corrections_buttons_layout.addStretch()
        
        layout.addLayout(corrections_buttons_layout)
        
        return tab
    
    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # إعدادات التحسين
        enhancement_group = QGroupBox("🔧 إعدادات التحسين")
        enhancement_layout = QFormLayout(enhancement_group)
        
        self.auto_correct_checkbox = QCheckBox("تصحيح تلقائي")
        self.auto_correct_checkbox.setToolTip("تطبيق التصحيحات تلقائياً بدون مراجعة يدوية")
        
        self.confidence_spinbox = QSpinBox()
        self.confidence_spinbox.setRange(50, 100)
        self.confidence_spinbox.setValue(80)
        self.confidence_spinbox.setSuffix("%")
        self.confidence_spinbox.setToolTip("الحد الأدنى للثقة في التصحيح التلقائي")
        
        enhancement_layout.addRow("التصحيح التلقائي:", self.auto_correct_checkbox)
        enhancement_layout.addRow("حد الثقة:", self.confidence_spinbox)
        
        layout.addWidget(enhancement_group)
        
        # معلومات إضافية
        info_group = QGroupBox("ℹ️ معلومات")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QLabel("""
        <b>نصائح الاستخدام:</b><br>
        • قم بإنشاء نسخة احتياطية قبل التحسين<br>
        • راجع التصحيحات المقترحة قبل التطبيق<br>
        • استخدم حد ثقة عالي (80%+) للتصحيح التلقائي<br>
        • يمكن التراجع عن التغييرات باستخدام النسخة الاحتياطية
        """)
        info_text.setWordWrap(True)
        info_text.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        
        info_layout.addWidget(info_text)
        layout.addWidget(info_group)
        
        layout.addStretch()
        
        return tab
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.analyze_button.clicked.connect(self.analyze_data)
        self.enhance_button.clicked.connect(self.enhance_data)
        self.backup_button.clicked.connect(self.create_backup)
        self.close_button.clicked.connect(self.accept)
        
        self.apply_selected_button.clicked.connect(self.apply_selected_corrections)
        self.apply_all_button.clicked.connect(self.apply_all_corrections)
        self.reject_selected_button.clicked.connect(self.reject_selected_corrections)
    
    def analyze_data(self):
        """تحليل البيانات"""
        try:
            self.status_label.setText("🔍 جاري التحليل...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # تحليل البيانات
            analysis = self.enhancer.analyze_shipping_companies()
            self.current_analysis = analysis
            
            # تحديث الإحصائيات
            self.total_shipments_label.setText(str(analysis["total_shipments"]))
            self.unique_companies_label.setText(str(len(analysis["unique_companies"])))
            self.valid_companies_label.setText(str(len(analysis["valid_companies"])))
            self.correctable_companies_label.setText(str(len(analysis["correctable_companies"])))
            self.invalid_companies_label.setText(str(len(analysis["invalid_companies"])))
            self.empty_companies_label.setText(str(analysis["empty_companies"]))
            
            # إنشاء التقرير
            report = self.enhancer.generate_enhancement_report()
            self.report_text.setPlainText(report)
            
            # ملء جدول التصحيحات
            self.populate_corrections_table(analysis["correctable_companies"])
            
            self.progress_bar.setValue(100)
            self.status_label.setText("✅ اكتمل التحليل")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحليل البيانات:\n{str(e)}")
            self.status_label.setText("❌ فشل التحليل")
        finally:
            self.progress_bar.setVisible(False)
    
    def populate_corrections_table(self, correctable_companies):
        """ملء جدول التصحيحات"""
        self.corrections_table.setRowCount(len(correctable_companies))
        
        for row, company in enumerate(correctable_companies):
            if company.get("best_match"):
                best_match = company["best_match"]
                company_data = best_match["company_data"]
                
                # رقم الشحنة (سيتم ملؤه لاحقاً)
                self.corrections_table.setItem(row, 0, QTableWidgetItem("-"))
                
                # الاسم الأصلي
                self.corrections_table.setItem(row, 1, QTableWidgetItem(company["original"]))
                
                # الاسم المقترح
                self.corrections_table.setItem(row, 2, QTableWidgetItem(best_match["suggested_name"]))
                
                # نسبة الثقة
                confidence = int(best_match["similarity"] * 100)
                self.corrections_table.setItem(row, 3, QTableWidgetItem(f"{confidence}%"))
                
                # الدولة
                self.corrections_table.setItem(row, 4, QTableWidgetItem(company_data["country"]))
                
                # النوع
                self.corrections_table.setItem(row, 5, QTableWidgetItem(company_data["type"]))
    
    def enhance_data(self):
        """تحسين البيانات"""
        auto_correct = self.auto_correct_checkbox.isChecked()
        confidence_threshold = self.confidence_spinbox.value() / 100.0
        
        if auto_correct:
            reply = QMessageBox.question(
                self, "تأكيد التحسين",
                f"هل تريد تطبيق التصحيحات التلقائية؟\n"
                f"حد الثقة: {self.confidence_spinbox.value()}%\n"
                f"سيتم تعديل البيانات في قاعدة البيانات مباشرة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
        
        # تشغيل التحسين في خيط منفصل
        self.enhancement_worker = EnhancementWorker(auto_correct, confidence_threshold)
        self.enhancement_worker.progress_updated.connect(self.progress_bar.setValue)
        self.enhancement_worker.status_updated.connect(self.status_label.setText)
        self.enhancement_worker.finished.connect(self.on_enhancement_finished)
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.enhance_button.setEnabled(False)
        
        self.enhancement_worker.start()
    
    def on_enhancement_finished(self, result):
        """عند انتهاء التحسين"""
        self.enhance_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        if "error" in result:
            QMessageBox.critical(self, "خطأ", f"فشل في تحسين البيانات:\n{result['error']}")
        else:
            enhancement = result["enhancement"]
            message = f"✅ اكتمل التحسين!\n\n"
            message += f"الشحنات المعالجة: {enhancement['processed_shipments']}\n"
            message += f"الشركات المصححة: {enhancement['corrected_companies']}\n"
            message += f"الشركات المتجاهلة: {enhancement['skipped_companies']}\n"
            
            QMessageBox.information(self, "نجح التحسين", message)
            
            # إعادة تحليل البيانات
            self.analyze_data()
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            self.status_label.setText("💾 إنشاء نسخة احتياطية...")
            backup_file = self.enhancer.backup_shipping_data()
            
            if backup_file:
                QMessageBox.information(
                    self, "نجح الحفظ",
                    f"تم إنشاء النسخة الاحتياطية:\n{backup_file}"
                )
                self.status_label.setText("✅ تم إنشاء النسخة الاحتياطية")
            else:
                QMessageBox.warning(self, "تحذير", "فشل في إنشاء النسخة الاحتياطية")
                self.status_label.setText("❌ فشل في النسخ الاحتياطي")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في النسخ الاحتياطي:\n{str(e)}")
            self.status_label.setText("❌ خطأ في النسخ الاحتياطي")
    
    def apply_selected_corrections(self):
        """تطبيق التصحيحات المحددة"""
        # TODO: تنفيذ تطبيق التصحيحات المحددة
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ هذه الميزة قريباً")
    
    def apply_all_corrections(self):
        """تطبيق جميع التصحيحات"""
        # TODO: تنفيذ تطبيق جميع التصحيحات
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ هذه الميزة قريباً")
    
    def reject_selected_corrections(self):
        """رفض التصحيحات المحددة"""
        # TODO: تنفيذ رفض التصحيحات المحددة
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ هذه الميزة قريباً")
