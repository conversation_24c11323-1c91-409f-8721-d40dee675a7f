# -*- coding: utf-8 -*-
"""
ويدجت إدارة وحدات القياس
Units of Measure Management Widget
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QTextEdit, QCheckBox, QComboBox)
from PySide6.QtCore import Qt
from datetime import datetime

from ...database.universal_database_manager import UniversalDatabaseManager
from ...database.oracle_config import DatabaseConfigManager
from ...database.models import UnitOfMeasure

class UnitsManagementWidget(QWidget):
    """ويدجت إدارة وحدات القياس"""
    
    def __init__(self):
        super().__init__()
        # إعداد قاعدة البيانات
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        self.db_manager = UniversalDatabaseManager(config)
        self.current_unit_id = None
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(15)
        
        # الجانب الأيسر - نموذج الإدخال
        form_group = QGroupBox("بيانات وحدة القياس")
        form_group.setMaximumWidth(400)
        form_layout = QFormLayout(form_group)
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("مثل: كيلوجرام، متر، قطعة")
        form_layout.addRow("اسم الوحدة:", self.name_edit)
        
        self.name_en_edit = QLineEdit()
        self.name_en_edit.setPlaceholderText("مثل: Kilogram, Meter, Piece")
        form_layout.addRow("الاسم بالإنجليزية:", self.name_en_edit)
        
        self.symbol_edit = QLineEdit()
        self.symbol_edit.setMaxLength(10)
        self.symbol_edit.setPlaceholderText("مثل: كجم، م، قطعة")
        form_layout.addRow("الرمز:", self.symbol_edit)
        
        self.symbol_en_edit = QLineEdit()
        self.symbol_en_edit.setMaxLength(10)
        self.symbol_en_edit.setPlaceholderText("مثل: kg, m, pcs")
        form_layout.addRow("الرمز بالإنجليزية:", self.symbol_en_edit)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("وصف اختياري لوحدة القياس")
        form_layout.addRow("الوصف:", self.description_edit)
        
        self.is_active_check = QCheckBox("نشطة")
        self.is_active_check.setChecked(True)
        form_layout.addRow("", self.is_active_check)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton("إضافة")
        self.add_button.clicked.connect(self.add_unit)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.update_button = QPushButton("تحديث")
        self.update_button.clicked.connect(self.update_unit)
        self.update_button.setEnabled(False)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        
        self.clear_button = QPushButton("مسح")
        self.clear_button.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.clear_button)
        
        form_layout.addRow("", buttons_layout)
        
        main_layout.addWidget(form_group)
        
        # الجانب الأيمن - جدول الوحدات
        table_group = QGroupBox("وحدات القياس المسجلة")
        table_layout = QVBoxLayout(table_group)

        # شريط البحث والتصفية
        search_layout = QHBoxLayout()

        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث في اسم الوحدة أو الرمز...")
        self.search_edit.textChanged.connect(self.filter_data)

        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["جميع الوحدات", "الوحدات النشطة", "الوحدات غير النشطة"])
        self.filter_combo.currentTextChanged.connect(self.filter_data)

        # زر تصدير البيانات
        export_button = QPushButton("تصدير إلى Excel")
        export_button.clicked.connect(self.export_to_excel)
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(QLabel("التصفية:"))
        search_layout.addWidget(self.filter_combo)
        search_layout.addStretch()
        search_layout.addWidget(export_button)

        table_layout.addLayout(search_layout)

        self.units_table = QTableWidget()
        self.units_table.setColumnCount(6)
        self.units_table.setHorizontalHeaderLabels([
            "الرقم", "الاسم", "الاسم بالإنجليزية", "الرمز", "الرمز بالإنجليزية", "الحالة"
        ])
        
        # تنسيق الجدول
        header = self.units_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.units_table.setAlternatingRowColors(True)
        self.units_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.units_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        table_layout.addWidget(self.units_table)
        
        # أزرار إدارة الجدول
        table_buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_data)
        
        edit_button = QPushButton("تعديل المحدد")
        edit_button.clicked.connect(self.edit_selected)
        
        delete_button = QPushButton("حذف المحدد")
        delete_button.clicked.connect(self.delete_selected)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(edit_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addStretch()
        
        table_layout.addLayout(table_buttons_layout)
        main_layout.addWidget(table_group)
    
    def load_data(self):
        """تحميل بيانات وحدات القياس"""
        with self.db_manager.get_session() as session:
            try:
                # تحميل جميع الوحدات (نشطة وغير نشطة)
                units = session.query(UnitOfMeasure).order_by(UnitOfMeasure.name).all()

                # حفظ البيانات للتصفية
                self.all_units = units

                # عرض البيانات
                self.display_units(units)

            except Exception as e:
                QMessageBox.warning(
                    self,
                    "خطأ في تحميل البيانات",
                    f"حدث خطأ أثناء تحميل بيانات وحدات القياس:\n{str(e)}"
                )

    def display_units(self, units):
        """عرض وحدات القياس في الجدول"""
        self.units_table.setRowCount(len(units))

        for row, unit in enumerate(units):
            # الرقم
            self.units_table.setItem(row, 0, QTableWidgetItem(str(unit.id)))

            # الاسم
            name_item = QTableWidgetItem(unit.name or "")
            name_item.setData(Qt.UserRole, unit.id)
            self.units_table.setItem(row, 1, name_item)

            # الاسم بالإنجليزية
            self.units_table.setItem(row, 2, QTableWidgetItem(unit.name_en or ""))

            # الرمز
            self.units_table.setItem(row, 3, QTableWidgetItem(unit.symbol or ""))

            # الرمز بالإنجليزية
            self.units_table.setItem(row, 4, QTableWidgetItem(unit.symbol_en or ""))

            # الحالة
            status_text = "نشطة" if unit.is_active else "غير نشطة"
            status_item = QTableWidgetItem(status_text)
            if unit.is_active:
                status_item.setBackground(Qt.GlobalColor.green)
                status_item.setForeground(Qt.GlobalColor.white)
            else:
                status_item.setBackground(Qt.GlobalColor.red)
                status_item.setForeground(Qt.GlobalColor.white)
            self.units_table.setItem(row, 5, status_item)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل بيانات وحدات القياس:\n{str(e)}"
            )

    def filter_data(self):
        """تصفية البيانات حسب البحث والحالة"""
        if not hasattr(self, 'all_units'):
            return

        search_text = self.search_edit.text().lower()
        filter_status = self.filter_combo.currentText()

        filtered_units = []

        for unit in self.all_units:
            # تصفية حسب الحالة
            if filter_status == "الوحدات النشطة" and not unit.is_active:
                continue
            elif filter_status == "الوحدات غير النشطة" and unit.is_active:
                continue

            # تصفية حسب البحث
            if search_text:
                if (search_text not in (unit.name or "").lower() and
                    search_text not in (unit.name_en or "").lower() and
                    search_text not in (unit.symbol or "").lower() and
                    search_text not in (unit.symbol_en or "").lower()):
                    continue

            filtered_units.append(unit)

        self.display_units(filtered_units)

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            import pandas as pd
            from PySide6.QtWidgets import QFileDialog

            if not hasattr(self, 'all_units') or not self.all_units:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات للتصدير")
                return

            # إعداد البيانات للتصدير
            data = []
            for unit in self.all_units:
                data.append({
                    'الرقم': unit.id,
                    'اسم الوحدة': unit.name or "",
                    'الاسم بالإنجليزية': unit.name_en or "",
                    'الرمز': unit.symbol or "",
                    'الرمز بالإنجليزية': unit.symbol_en or "",
                    'الوصف': unit.description or "",
                    'الحالة': "نشطة" if unit.is_active else "غير نشطة",
                    'تاريخ الإنشاء': unit.created_at.strftime('%Y-%m-%d %H:%M:%S') if unit.created_at else ""
                })

            # إنشاء DataFrame
            df = pd.DataFrame(data)

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ ملف Excel",
                f"units_of_measure_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )

            if file_path:
                df.to_excel(file_path, index=False, engine='openpyxl')
                QMessageBox.information(
                    self,
                    "تم التصدير",
                    f"تم تصدير البيانات بنجاح إلى:\n{file_path}"
                )

        except ImportError:
            QMessageBox.warning(
                self,
                "خطأ",
                "مكتبة pandas غير مثبتة. يرجى تثبيتها لاستخدام ميزة التصدير."
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في التصدير",
                f"حدث خطأ أثناء تصدير البيانات:\n{str(e)}"
            )
    
    def add_unit(self):
        """إضافة وحدة قياس جديدة"""
        if not self.validate_form():
            return
        
        session = self.db_manager.get_session()
        try:
            # التحقق من عدم وجود الوحدة مسبقاً
            existing = session.query(UnitOfMeasure).filter_by(name=self.name_edit.text()).first()
            if existing:
                QMessageBox.warning(self, "وحدة موجودة", "هذه الوحدة موجودة بالفعل")
                return
            
            # إنشاء الوحدة الجديدة
            new_unit = UnitOfMeasure(
                name=self.name_edit.text(),
                name_en=self.name_en_edit.text(),
                symbol=self.symbol_edit.text(),
                symbol_en=self.symbol_en_edit.text(),
                description=self.description_edit.toPlainText(),
                is_active=self.is_active_check.isChecked()
            )
            
            session.add(new_unit)
            session.commit()
            
            QMessageBox.information(self, "تم الإضافة", "تم إضافة وحدة القياس بنجاح")
            
            self.clear_form()
            self.load_data()
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في الإضافة", f"حدث خطأ أثناء إضافة وحدة القياس:\n{str(e)}")
        finally:
            session.close()
    
    def update_unit(self):
        """تحديث وحدة القياس"""
        if not self.current_unit_id or not self.validate_form():
            return
        
        session = self.db_manager.get_session()
        try:
            unit = session.query(UnitOfMeasure).get(self.current_unit_id)
            if unit:
                unit.name = self.name_edit.text()
                unit.name_en = self.name_en_edit.text()
                unit.symbol = self.symbol_edit.text()
                unit.symbol_en = self.symbol_en_edit.text()
                unit.description = self.description_edit.toPlainText()
                unit.is_active = self.is_active_check.isChecked()
                
                session.commit()
                
                QMessageBox.information(self, "تم التحديث", "تم تحديث وحدة القياس بنجاح")
                
                self.clear_form()
                self.load_data()
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في التحديث", f"حدث خطأ أثناء تحديث وحدة القياس:\n{str(e)}")
        finally:
            session.close()
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم الوحدة")
            return False
        
        if not self.symbol_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رمز الوحدة")
            return False
        
        return True
    
    def clear_form(self):
        """مسح النموذج"""
        self.current_unit_id = None
        self.name_edit.clear()
        self.name_en_edit.clear()
        self.symbol_edit.clear()
        self.symbol_en_edit.clear()
        self.description_edit.clear()
        self.is_active_check.setChecked(True)
        
        self.add_button.setEnabled(True)
        self.update_button.setEnabled(False)
    
    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.units_table.currentRow()
        if current_row >= 0:
            self.edit_selected()
    
    def edit_selected(self):
        """تعديل الوحدة المحددة"""
        current_row = self.units_table.currentRow()
        if current_row < 0:
            return
        
        unit_id = self.units_table.item(current_row, 0).data(Qt.UserRole)
        
        session = self.db_manager.get_session()
        try:
            unit = session.query(UnitOfMeasure).get(unit_id)
            if unit:
                self.current_unit_id = unit.id
                self.name_edit.setText(unit.name or "")
                self.name_en_edit.setText(unit.name_en or "")
                self.symbol_edit.setText(unit.symbol or "")
                self.symbol_en_edit.setText(unit.symbol_en or "")
                self.description_edit.setPlainText(unit.description or "")
                self.is_active_check.setChecked(unit.is_active)
                
                self.add_button.setEnabled(False)
                self.update_button.setEnabled(True)
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الوحدة:\n{str(e)}")
        finally:
            session.close()
    
    def delete_selected(self):
        """حذف الوحدة المحددة"""
        current_row = self.units_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "لا يوجد تحديد", "يرجى تحديد وحدة للحذف")
            return
        
        unit_id = self.units_table.item(current_row, 0).data(Qt.UserRole)
        unit_name = self.units_table.item(current_row, 0).text()
        
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف وحدة القياس '{unit_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                unit = session.query(UnitOfMeasure).get(unit_id)
                if unit:
                    unit.is_active = False  # حذف منطقي
                    session.commit()
                    
                    QMessageBox.information(self, "تم الحذف", f"تم حذف وحدة القياس '{unit_name}' بنجاح")
                    self.load_data()
                    self.clear_form()
                    
            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف وحدة القياس:\n{str(e)}")
            finally:
                session.close()
