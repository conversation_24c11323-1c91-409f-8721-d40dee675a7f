"""
نافذة خرائط الشحنات
تتيح عرض جميع الشحنات على خريطة تفاعلية مع إمكانيات التصفح والفلترة المتقدمة
"""

import sys
import json
import math
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QFrame,
    QSplitter, QGroupBox, QProgressBar, QTextEdit, QComboBox,
    QLineEdit, QDateEdit, QTimeEdit, QCheckBox, QSpinBox,
    QScrollArea, QTabWidget, QStatusBar, QHeaderView, QApplication,
    QMessageBox, QDialog, QDialogButtonBox, QFormLayout, Q<PERSON><PERSON>r,
    QButtonGroup, QRadioButton, QListWidget, QListWidgetItem
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QDate, QTime, QSize, QPoint
from PySide6.QtGui import QFont, QPainter, QPen, QBrush, QColor, QPixmap, QIcon, QPolygon

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, Supplier, Container, ShipmentItem
from src.ui.styles.style_manager import StyleManager

style_manager = StyleManager()


class ShipmentMapWidget(QWidget):
    """ويدجت الخريطة التفاعلية لعرض الشحنات"""
    
    shipment_clicked = Signal(int)  # إشارة النقر على شحنة
    route_requested = Signal(int)   # إشارة طلب عرض المسار
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(1000, 700)
        self.shipments_data = []
        self.filtered_shipments = []
        self.selected_shipment_id = None
        
        # إعدادات الخريطة
        self.zoom_level = 1.0
        self.min_zoom = 0.5
        self.max_zoom = 3.0
        self.pan_x = 0
        self.pan_y = 0
        self.mouse_pressed = False
        self.last_mouse_pos = None
        self.show_routes = False
        self.show_labels = True
        
        # إعداد الألوان والأنماط
        self.setup_colors_and_styles()
        
        # إعداد الموانئ الرئيسية
        self.setup_major_ports()
        
        # تمكين تتبع الماوس
        self.setMouseTracking(True)
        
    def setup_colors_and_styles(self):
        """إعداد الألوان والأنماط"""
        self.status_colors = {
            'في الطريق': QColor(52, 152, 219),      # أزرق
            'وصلت': QColor(46, 204, 113),           # أخضر
            'متأخرة': QColor(231, 76, 60),          # أحمر
            'في الميناء': QColor(241, 196, 15),     # أصفر
            'تم التسليم': QColor(155, 89, 182),     # بنفسجي
            'ملغاة': QColor(149, 165, 166),         # رمادي
            'جديدة': QColor(26, 188, 156)           # تركوازي
        }
        
        # ألوان الخريطة
        self.ocean_color = QColor(173, 216, 230)
        self.land_color = QColor(139, 69, 19)
        self.grid_color = QColor(200, 200, 200, 100)
        self.port_color = QColor(255, 255, 255)
        self.route_color = QColor(255, 165, 0, 150)  # برتقالي شفاف
        
    def setup_major_ports(self):
        """إعداد المواقع التقريبية للموانئ الرئيسية"""
        self.major_ports = {
            # موانئ الشرق الأوسط
            'جدة': (0.45, 0.6),
            'الدمام': (0.5, 0.58),
            'دبي': (0.52, 0.62),
            'أبو ظبي': (0.51, 0.63),
            'الكويت': (0.48, 0.55),
            'البحرين': (0.49, 0.58),
            'الدوحة': (0.5, 0.6),
            'مسقط': (0.54, 0.65),
            
            # موانئ آسيا
            'شنغهاي': (0.8, 0.45),
            'هونغ كونغ': (0.78, 0.52),
            'سنغافورة': (0.72, 0.7),
            'بانكوك': (0.7, 0.65),
            'مومباي': (0.6, 0.65),
            'كراتشي': (0.58, 0.6),
            'كولومبو': (0.62, 0.7),
            
            # موانئ أوروبا
            'روتردام': (0.35, 0.25),
            'هامبورغ': (0.37, 0.22),
            'أنتويرب': (0.34, 0.26),
            'فالنسيا': (0.32, 0.35),
            'جنوة': (0.36, 0.32),
            'بيرايوس': (0.4, 0.35),
            
            # موانئ أمريكا
            'لوس أنجلوس': (0.1, 0.45),
            'نيويورك': (0.15, 0.35),
            'ميامي': (0.18, 0.55),
            'سافانا': (0.17, 0.45),
            
            # موانئ أفريقيا
            'الإسكندرية': (0.42, 0.45),
            'بورسعيد': (0.43, 0.44),
            'السويس': (0.43, 0.46),
            'الدار البيضاء': (0.28, 0.48),
            'لاغوس': (0.35, 0.65),
            'ديربان': (0.45, 0.85)
        }
        
    def set_shipments_data(self, shipments):
        """تعيين بيانات الشحنات"""
        self.shipments_data = shipments
        self.filtered_shipments = shipments.copy()
        self.update()
        
    def filter_shipments(self, status_filter=None, date_filter=None, supplier_filter=None):
        """فلترة الشحنات حسب المعايير المحددة"""
        self.filtered_shipments = []
        
        for shipment in self.shipments_data:
            include = True
            
            # فلترة الحالة
            if status_filter and status_filter != "جميع الحالات":
                if getattr(shipment, 'shipment_status', 'جديدة') != status_filter:
                    include = False
                    
            # فلترة التاريخ
            if date_filter and hasattr(shipment, 'created_at') and shipment.created_at:
                shipment_date = shipment.created_at.date()
                if date_filter == "اليوم":
                    if shipment_date != datetime.now().date():
                        include = False
                elif date_filter == "هذا الأسبوع":
                    week_start = datetime.now().date() - timedelta(days=7)
                    if shipment_date < week_start:
                        include = False
                elif date_filter == "هذا الشهر":
                    month_start = datetime.now().date().replace(day=1)
                    if shipment_date < month_start:
                        include = False
                        
            # فلترة المورد
            if supplier_filter and supplier_filter != "جميع الموردين":
                supplier_name = shipment.supplier.name if shipment.supplier else ""
                if supplier_name != supplier_filter:
                    include = False
                    
            if include:
                self.filtered_shipments.append(shipment)
                
        self.update()
        
    def paintEvent(self, event):
        """رسم الخريطة والشحنات"""
        painter = QPainter(self)

        # التحقق من صحة الرسام
        if not painter.isActive():
            return

        try:
            painter.setRenderHint(QPainter.Antialiasing)

            # تطبيق التحويلات (التكبير والتحريك)
            painter.translate(self.pan_x, self.pan_y)
            painter.scale(self.zoom_level, self.zoom_level)

            # رسم خلفية الخريطة
            self.draw_map_background(painter)

            # رسم الموانئ الرئيسية
            self.draw_major_ports(painter)
        
            # رسم المسارات إذا كانت مفعلة
            if self.show_routes:
                self.draw_shipping_routes(painter)

            # رسم الشحنات
            self.draw_shipments(painter)

            # رسم معلومات الشحنة المحددة
            if self.selected_shipment_id:
                self.draw_selected_shipment_info(painter)

            # رسم عنوان الخريطة ومعلومات إضافية
            self.draw_map_info(painter)

            # رسم أزرار التحكم
            self.draw_control_buttons(painter)

        except Exception as e:
            print(f"خطأ في رسم الخريطة: {str(e)}")
        finally:
            if painter.isActive():
                painter.end()

    def draw_map_info(self, painter):
        """رسم معلومات الخريطة"""
        width = int(self.width() / self.zoom_level)

        # رسم عنوان الخريطة
        painter.setPen(QPen(QColor(25, 25, 112)))  # Midnight Blue
        painter.setFont(QFont("Arial", max(16, int(20 * self.zoom_level)), QFont.Bold))
        title = "🗺️ خريطة الشحنات العالمية"
        title_rect = painter.fontMetrics().boundingRect(title)

        # خلفية العنوان
        painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
        painter.setPen(QPen(QColor(0, 0, 0, 100), 1))
        painter.drawRoundedRect(10, 10, title_rect.width() + 20, title_rect.height() + 10, 8, 8)

        # رسم العنوان
        painter.setPen(QPen(QColor(25, 25, 112)))
        painter.drawText(20, 30, title)

        # رسم مفتاح الألوان (Legend)
        if self.zoom_level > 0.8:
            self.draw_color_legend(painter, width)

    def draw_color_legend(self, painter, width):
        """رسم مفتاح الألوان"""
        legend_x = width - 250
        legend_y = 20

        # خلفية المفتاح
        painter.setBrush(QBrush(QColor(255, 255, 255, 220)))
        painter.setPen(QPen(QColor(0, 0, 0, 100), 1))
        painter.drawRoundedRect(legend_x, legend_y, 230, 200, 8, 8)

        # عنوان المفتاح
        painter.setPen(QPen(QColor(25, 25, 112)))
        painter.setFont(QFont("Arial", 12, QFont.Bold))
        painter.drawText(legend_x + 10, legend_y + 20, "حالات الشحنات:")

        # رسم عناصر المفتاح
        y_offset = 40
        painter.setFont(QFont("Arial", 10))

        for status, color in self.status_colors.items():
            # رسم دائرة اللون
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color.darker(150), 2))
            painter.drawEllipse(legend_x + 15, legend_y + y_offset - 8, 16, 16)

            # رسم النص
            painter.setPen(QPen(Qt.black))
            painter.drawText(legend_x + 40, legend_y + y_offset, status)

            y_offset += 25

    def draw_control_buttons(self, painter):
        """رسم أزرار التحكم على الخريطة"""
        width = int(self.width() / self.zoom_level)
        height = int(self.height() / self.zoom_level)

        # موقع أزرار التحكم
        button_x = 20
        button_y = height - 120
        button_size = 35
        button_spacing = 40

        # قائمة الأزرار
        buttons = [
            ("🔍+", "تكبير"),
            ("🔍-", "تصغير"),
            ("🏠", "إعادة تعيين"),
            ("🛣️", "المسارات")
        ]

        for i, (icon, tooltip) in enumerate(buttons):
            btn_x = button_x
            btn_y = button_y + (i * button_spacing)

            # خلفية الزر
            painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
            painter.setPen(QPen(QColor(100, 100, 100), 2))
            painter.drawRoundedRect(btn_x, btn_y, button_size, button_size, 8, 8)

            # أيقونة الزر
            painter.setPen(QPen(Qt.black))
            painter.setFont(QFont("Arial", 14, QFont.Bold))
            text_rect = painter.fontMetrics().boundingRect(icon)
            text_x = btn_x + (button_size - text_rect.width()) // 2
            text_y = btn_y + (button_size + text_rect.height()) // 2
            painter.drawText(text_x, text_y, icon)

        # رسم مؤشر التكبير
        zoom_text = f"التكبير: {self.zoom_level:.1f}x"
        painter.setPen(QPen(QColor(25, 25, 112)))
        painter.setFont(QFont("Arial", 10, QFont.Bold))

        # خلفية مؤشر التكبير
        zoom_rect = painter.fontMetrics().boundingRect(zoom_text)
        painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
        painter.setPen(QPen(QColor(0, 0, 0, 100), 1))
        painter.drawRoundedRect(button_x, button_y - 50, zoom_rect.width() + 10,
                              zoom_rect.height() + 6, 5, 5)

        # نص مؤشر التكبير
        painter.setPen(QPen(QColor(25, 25, 112)))
        painter.drawText(button_x + 5, button_y - 35, zoom_text)
            
    def draw_map_background(self, painter):
        """رسم خلفية الخريطة"""
        width = int(self.width() / self.zoom_level)
        height = int(self.height() / self.zoom_level)

        # خلفية المحيط مع تدرج لوني
        gradient = QLinearGradient(0, 0, width, height)
        gradient.setColorAt(0, QColor(135, 206, 250))  # Sky Blue
        gradient.setColorAt(1, QColor(70, 130, 180))   # Steel Blue
        painter.fillRect(0, 0, width, height, QBrush(gradient))

        # رسم القارات بشكل مبسط
        self.draw_continents_detailed(painter, width, height)

        # رسم خطوط الطول والعرض
        painter.setPen(QPen(QColor(200, 200, 200), 1, Qt.DashLine))

        # خطوط الطول (عمودية)
        for i in range(0, 13):  # 12 خط طول
            x = int(width * i / 12)
            painter.drawLine(x, 0, x, height)

        # خطوط العرض (أفقية)
        for i in range(0, 9):  # 8 خطوط عرض
            y = int(height * i / 8)
            painter.drawLine(0, y, width, y)

    def draw_continents_detailed(self, painter, width, height):
        """رسم القارات بتفاصيل أكثر"""
        painter.setBrush(QBrush(QColor(144, 238, 144)))  # Light Green
        painter.setPen(QPen(QColor(34, 139, 34), 2))     # Forest Green

        # آسيا (الجزء الأكبر)
        asia_points = [
            QPoint(int(width * 0.55), int(height * 0.15)),  # شمال آسيا
            QPoint(int(width * 0.85), int(height * 0.25)),  # شرق آسيا
            QPoint(int(width * 0.80), int(height * 0.45)),  # جنوب شرق آسيا
            QPoint(int(width * 0.65), int(height * 0.55)),  # الهند
            QPoint(int(width * 0.55), int(height * 0.45)),  # وسط آسيا
            QPoint(int(width * 0.50), int(height * 0.25)),  # غرب آسيا
        ]
        painter.drawPolygon(asia_points)

        # أوروبا
        europe_points = [
            QPoint(int(width * 0.45), int(height * 0.20)),
            QPoint(int(width * 0.52), int(height * 0.18)),
            QPoint(int(width * 0.50), int(height * 0.35)),
            QPoint(int(width * 0.42), int(height * 0.38)),
        ]
        painter.drawPolygon(europe_points)

        # أفريقيا
        africa_points = [
            QPoint(int(width * 0.45), int(height * 0.35)),
            QPoint(int(width * 0.55), int(height * 0.40)),
            QPoint(int(width * 0.52), int(height * 0.75)),
            QPoint(int(width * 0.42), int(height * 0.72)),
        ]
        painter.drawPolygon(africa_points)

        # أمريكا الشمالية
        north_america_points = [
            QPoint(int(width * 0.15), int(height * 0.15)),
            QPoint(int(width * 0.35), int(height * 0.20)),
            QPoint(int(width * 0.30), int(height * 0.45)),
            QPoint(int(width * 0.10), int(height * 0.40)),
        ]
        painter.drawPolygon(north_america_points)

        # أمريكا الجنوبية
        south_america_points = [
            QPoint(int(width * 0.25), int(height * 0.50)),
            QPoint(int(width * 0.35), int(height * 0.55)),
            QPoint(int(width * 0.30), int(height * 0.85)),
            QPoint(int(width * 0.20), int(height * 0.80)),
        ]
        painter.drawPolygon(south_america_points)

        # أستراليا
        australia_points = [
            QPoint(int(width * 0.75), int(height * 0.70)),
            QPoint(int(width * 0.85), int(height * 0.72)),
            QPoint(int(width * 0.82), int(height * 0.80)),
            QPoint(int(width * 0.72), int(height * 0.78)),
        ]
        painter.drawPolygon(australia_points)
            

        

        
    def draw_major_ports(self, painter):
        """رسم الموانئ الرئيسية"""
        width = int(self.width() / self.zoom_level)
        height = int(self.height() / self.zoom_level)

        for port_name, (x_ratio, y_ratio) in self.major_ports.items():
            x = int(width * x_ratio)
            y = int(height * y_ratio)

            # رسم دائرة الميناء مع تدرج لوني
            port_size = max(6, int(10 * self.zoom_level))

            # دائرة خارجية (حدود)
            painter.setBrush(QBrush(QColor(255, 69, 0)))  # Red Orange
            painter.setPen(QPen(QColor(139, 0, 0), 2))    # Dark Red
            painter.drawEllipse(x - port_size, y - port_size, port_size * 2, port_size * 2)

            # دائرة داخلية (مركز)
            inner_size = max(3, port_size - 3)
            painter.setBrush(QBrush(QColor(255, 215, 0)))  # Gold
            painter.setPen(QPen(Qt.NoPen))
            painter.drawEllipse(x - inner_size, y - inner_size, inner_size * 2, inner_size * 2)

            # رسم اسم الميناء إذا كانت التسميات مفعلة
            if self.show_labels and self.zoom_level > 0.6:
                # خلفية للنص
                font = QFont("Arial", max(9, int(11 * self.zoom_level)), QFont.Bold)
                painter.setFont(font)

                text_rect = painter.fontMetrics().boundingRect(port_name)
                text_x = x + port_size + 5
                text_y = y - port_size

                # رسم خلفية النص
                painter.setBrush(QBrush(QColor(255, 255, 255, 220)))
                painter.setPen(QPen(QColor(0, 0, 0, 100), 1))
                painter.drawRoundedRect(text_x - 3, text_y - text_rect.height() - 2,
                                      text_rect.width() + 6, text_rect.height() + 4, 4, 4)

                # رسم النص
                painter.setPen(QPen(QColor(25, 25, 112)))  # Midnight Blue
                painter.drawText(text_x, text_y, port_name)

    def draw_shipping_routes(self, painter):
        """رسم مسارات الشحن"""
        width = int(self.width() / self.zoom_level)
        height = int(self.height() / self.zoom_level)

        # رسم مسارات رئيسية بين الموانئ المهمة
        main_routes = [
            ('جدة', 'شنغهاي'),
            ('دبي', 'سنغافورة'),
            ('الدمام', 'هونغ كونغ'),
            ('جدة', 'روتردام'),
            ('دبي', 'هامبورغ'),
            ('الكويت', 'لوس أنجلوس'),
            ('الإسكندرية', 'نيويورك')
        ]

        for start_port, end_port in main_routes:
            if start_port in self.major_ports and end_port in self.major_ports:
                start_x = int(width * self.major_ports[start_port][0])
                start_y = int(height * self.major_ports[start_port][1])
                end_x = int(width * self.major_ports[end_port][0])
                end_y = int(height * self.major_ports[end_port][1])

                # رسم خط المسار مع تأثير متدرج
                # خط الظل
                painter.setPen(QPen(QColor(0, 0, 0, 100), 4))
                painter.drawLine(start_x + 1, start_y + 1, end_x + 1, end_y + 1)

                # الخط الرئيسي
                painter.setPen(QPen(QColor(255, 69, 0), 3, Qt.DashLine))  # Red Orange
                painter.drawLine(start_x, start_y, end_x, end_y)

                # نقاط الاتجاه على المسار
                mid_x = (start_x + end_x) // 2
                mid_y = (start_y + end_y) // 2
                painter.setBrush(QBrush(QColor(255, 215, 0)))  # Gold
                painter.setPen(QPen(QColor(255, 140, 0), 2))
                painter.drawEllipse(mid_x - 3, mid_y - 3, 6, 6)

    def draw_shipments(self, painter):
        """رسم الشحنات على الخريطة"""
        width = int(self.width() / self.zoom_level)
        height = int(self.height() / self.zoom_level)

        for shipment in self.filtered_shipments:
            # تحديد موقع الشحنة بناءً على الموانئ
            position = self.get_shipment_position(shipment, width, height)
            if not position:
                continue

            x, y = position
            status = getattr(shipment, 'shipment_status', 'جديدة')
            color = self.status_colors.get(status, QColor(128, 128, 128))

            # تحديد حجم الدائرة بناءً على التكبير
            radius = max(8, int(12 * self.zoom_level))

            # رسم ظل للشحنة
            shadow_offset = 2
            painter.setBrush(QBrush(QColor(0, 0, 0, 100)))
            painter.setPen(QPen(Qt.NoPen))
            painter.drawEllipse(x - radius + shadow_offset, y - radius + shadow_offset,
                              radius * 2, radius * 2)

            # رسم دائرة الشحنة الرئيسية
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color.darker(150), 3))
            painter.drawEllipse(x - radius, y - radius, radius * 2, radius * 2)

            # رسم دائرة داخلية للتأثير ثلاثي الأبعاد
            inner_radius = max(3, radius - 4)
            painter.setBrush(QBrush(color.lighter(150)))
            painter.setPen(QPen(Qt.NoPen))
            painter.drawEllipse(x - inner_radius, y - inner_radius,
                              inner_radius * 2, inner_radius * 2)

            # إضافة رقم الشحنة إذا كان التكبير كافياً
            if self.zoom_level > 1.0:
                painter.setPen(QPen(Qt.white))
                painter.setFont(QFont("Arial", max(8, int(10 * self.zoom_level)), QFont.Bold))
                text = str(shipment.id)
                text_rect = painter.fontMetrics().boundingRect(text)
                painter.drawText(x - text_rect.width()//2, y + text_rect.height()//4, text)

            # تمييز الشحنة المحددة
            if self.selected_shipment_id == shipment.id:
                painter.setBrush(QBrush(Qt.transparent))
                painter.setPen(QPen(QColor(255, 215, 0), 4))  # Gold
                painter.drawEllipse(x - radius - 5, y - radius - 5,
                                  (radius + 5) * 2, (radius + 5) * 2)

    def get_shipment_position(self, shipment, width, height):
        """تحديد موقع الشحنة على الخريطة"""
        # محاولة تحديد الموقع بناءً على ميناء المغادرة أو الوصول
        departure_port = getattr(shipment, 'port_of_loading', '')
        arrival_port = getattr(shipment, 'port_of_discharge', '')

        # البحث عن الميناء في قائمة الموانئ الرئيسية
        port_position = None

        if departure_port in self.major_ports:
            port_position = self.major_ports[departure_port]
        elif arrival_port in self.major_ports:
            port_position = self.major_ports[arrival_port]
        else:
            # البحث الجزئي في أسماء الموانئ
            for port_name, position in self.major_ports.items():
                if departure_port and port_name in departure_port:
                    port_position = position
                    break
                elif arrival_port and port_name in arrival_port:
                    port_position = position
                    break

        if port_position:
            x = int(width * port_position[0])
            y = int(height * port_position[1])

            # إضافة تشويش عشوائي لتجنب التداخل
            import random
            x += random.randint(-15, 15)
            y += random.randint(-15, 15)

            return (x, y)

        # إذا لم يتم العثور على الميناء، استخدم موقع افتراضي
        return (int(width * 0.5), int(height * 0.5))

    def draw_selected_shipment_info(self, painter):
        """رسم معلومات الشحنة المحددة"""
        selected_shipment = None
        for shipment in self.filtered_shipments:
            if shipment.id == self.selected_shipment_id:
                selected_shipment = shipment
                break

        if not selected_shipment:
            return

        # رسم مربع المعلومات
        info_width = 250
        info_height = 120
        info_x = 10
        info_y = 10

        # خلفية شفافة
        painter.setBrush(QBrush(QColor(0, 0, 0, 180)))
        painter.setPen(QPen(Qt.white, 2))
        painter.drawRoundedRect(info_x, info_y, info_width, info_height, 10, 10)

        # النص
        painter.setPen(QPen(Qt.white))
        painter.setFont(QFont("Arial", 10, QFont.Bold))

        y_offset = info_y + 20
        painter.drawText(info_x + 10, y_offset, f"الشحنة #{selected_shipment.id}")

        y_offset += 20
        supplier_name = selected_shipment.supplier.name if selected_shipment.supplier else "غير محدد"
        painter.drawText(info_x + 10, y_offset, f"المورد: {supplier_name}")

        y_offset += 20
        status = getattr(selected_shipment, 'shipment_status', 'جديدة')
        painter.drawText(info_x + 10, y_offset, f"الحالة: {status}")

        y_offset += 20
        departure_port = getattr(selected_shipment, 'port_of_loading', 'غير محدد')
        painter.drawText(info_x + 10, y_offset, f"ميناء المغادرة: {departure_port}")

        y_offset += 20
        arrival_port = getattr(selected_shipment, 'port_of_discharge', 'غير محدد')
        painter.drawText(info_x + 10, y_offset, f"ميناء الوصول: {arrival_port}")

    def mousePressEvent(self, event):
        """معالجة النقر بالماوس"""
        if event.button() == Qt.LeftButton:
            self.mouse_pressed = True
            self.last_mouse_pos = event.position()

            # التحقق من النقر على شحنة
            clicked_shipment = self.get_shipment_at_position(event.position())
            if clicked_shipment:
                self.selected_shipment_id = clicked_shipment.id
                self.shipment_clicked.emit(clicked_shipment.id)
                self.update()
            else:
                self.selected_shipment_id = None
                self.update()

    def mouseMoveEvent(self, event):
        """معالجة حركة الماوس"""
        if self.mouse_pressed and self.last_mouse_pos:
            # تحريك الخريطة
            delta = event.position() - self.last_mouse_pos
            self.pan_x += delta.x()
            self.pan_y += delta.y()
            self.last_mouse_pos = event.position()
            self.update()

    def mouseReleaseEvent(self, event):
        """معالجة تحرير الماوس"""
        if event.button() == Qt.LeftButton:
            self.mouse_pressed = False
            self.last_mouse_pos = None

    def wheelEvent(self, event):
        """معالجة عجلة الماوس للتكبير والتصغير"""
        delta = event.angleDelta().y()
        zoom_factor = 1.1 if delta > 0 else 0.9

        new_zoom = self.zoom_level * zoom_factor
        if self.min_zoom <= new_zoom <= self.max_zoom:
            self.zoom_level = new_zoom
            self.update()

    def get_shipment_at_position(self, pos):
        """العثور على الشحنة في الموقع المحدد"""
        # تحويل الإحداثيات مع مراعاة التكبير والتحريك
        map_x = (pos.x() - self.pan_x) / self.zoom_level
        map_y = (pos.y() - self.pan_y) / self.zoom_level

        width = int(self.width() / self.zoom_level)
        height = int(self.height() / self.zoom_level)

        for shipment in self.filtered_shipments:
            shipment_pos = self.get_shipment_position(shipment, width, height)
            if not shipment_pos:
                continue

            x, y = shipment_pos
            radius = max(6, int(8 * self.zoom_level))

            # التحقق من المسافة
            distance = math.sqrt((map_x - x) ** 2 + (map_y - y) ** 2)
            if distance <= radius:
                return shipment

        return None

    def reset_view(self):
        """إعادة تعيين العرض"""
        self.zoom_level = 1.0
        self.pan_x = 0
        self.pan_y = 0
        self.update()

    def toggle_routes(self):
        """تبديل عرض المسارات"""
        self.show_routes = not self.show_routes
        self.update()

    def toggle_labels(self):
        """تبديل عرض التسميات"""
        self.show_labels = not self.show_labels
        self.update()


class ShipmentMapsWindow(QMainWindow):
    """نافذة خرائط الشحنات الرئيسية"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🗺️ خرائط الشحنات")
        self.setMinimumSize(1400, 900)
        self.showMaximized()

        # تهيئة المتغيرات
        self.shipments_data = []
        self.stats_labels = {}
        self.status_label = None
        self.supplier_filter = None
        self.shipments_list = None
        self.map_widget = None

        # إعداد قاعدة البيانات
        self.db_manager = DatabaseManager()

        # إعداد شريط الحالة أولاً
        self.setup_status_bar()

        # إعداد الواجهة
        self.setup_ui()

        # تحميل البيانات
        self.load_shipments_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # شريط العنوان والأدوات
        self.setup_header(main_layout)

        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(content_splitter)

        # لوحة التحكم الجانبية
        self.setup_control_panel(content_splitter)

        # منطقة الخريطة
        self.setup_map_area(content_splitter)

        # تعيين نسب التقسيم
        content_splitter.setSizes([300, 1100])

    def setup_header(self, parent_layout):
        """إعداد شريط العنوان والأدوات"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2c3e50, stop:1 #3498db);
                border: none;
            }
        """)
        parent_layout.addWidget(header_frame)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)

        # العنوان
        title_label = QLabel("🗺️ خرائط الشحنات")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
            }
        """)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # أزرار التحكم
        self.setup_header_buttons(header_layout)

    def setup_header_buttons(self, header_layout):
        """إعداد أزرار التحكم في الشريط العلوي"""
        button_style = """
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 2px solid white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """

        # زر إعادة تعيين العرض
        self.reset_view_btn = QPushButton("🔄 إعادة تعيين")
        self.reset_view_btn.setStyleSheet(button_style)
        self.reset_view_btn.clicked.connect(self.reset_map_view)
        header_layout.addWidget(self.reset_view_btn)

        # زر تبديل المسارات
        self.toggle_routes_btn = QPushButton("🛤️ المسارات")
        self.toggle_routes_btn.setStyleSheet(button_style)
        self.toggle_routes_btn.setCheckable(True)
        self.toggle_routes_btn.clicked.connect(self.toggle_routes)
        header_layout.addWidget(self.toggle_routes_btn)

        # زر تبديل التسميات
        self.toggle_labels_btn = QPushButton("🏷️ التسميات")
        self.toggle_labels_btn.setStyleSheet(button_style)
        self.toggle_labels_btn.setCheckable(True)
        self.toggle_labels_btn.setChecked(True)
        self.toggle_labels_btn.clicked.connect(self.toggle_labels)
        header_layout.addWidget(self.toggle_labels_btn)

        # زر التحديث
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setStyleSheet(button_style)
        self.refresh_btn.clicked.connect(self.refresh_data)
        header_layout.addWidget(self.refresh_btn)

        # زر ملء الشاشة
        self.fullscreen_btn = QPushButton("🔳 ملء الشاشة")
        self.fullscreen_btn.setStyleSheet(button_style)
        self.fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        header_layout.addWidget(self.fullscreen_btn)

    def setup_control_panel(self, parent_splitter):
        """إعداد لوحة التحكم الجانبية"""
        control_frame = QFrame()
        control_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-right: 2px solid #dee2e6;
            }
        """)
        parent_splitter.addWidget(control_frame)

        control_layout = QVBoxLayout(control_frame)
        control_layout.setContentsMargins(15, 15, 15, 15)
        control_layout.setSpacing(15)

        # مجموعة الفلاتر
        self.setup_filters_group(control_layout)

        # مجموعة الإحصائيات
        self.setup_statistics_group(control_layout)

        # مجموعة قائمة الشحنات
        self.setup_shipments_list(control_layout)

        control_layout.addStretch()

    def setup_filters_group(self, parent_layout):
        """إعداد مجموعة الفلاتر"""
        filters_group = QGroupBox("🔍 الفلاتر والبحث")
        filters_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        parent_layout.addWidget(filters_group)

        filters_layout = QVBoxLayout(filters_group)
        filters_layout.setSpacing(10)

        # فلتر الحالة
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("الحالة:"))

        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "جميع الحالات", "في الطريق", "وصلت", "متأخرة",
            "في الميناء", "تم التسليم", "ملغاة", "جديدة"
        ])
        self.status_filter.currentTextChanged.connect(self.apply_filters)
        status_layout.addWidget(self.status_filter)
        filters_layout.addLayout(status_layout)

        # فلتر التاريخ
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("الفترة:"))

        self.date_filter = QComboBox()
        self.date_filter.addItems([
            "جميع الفترات", "اليوم", "هذا الأسبوع", "هذا الشهر"
        ])
        self.date_filter.currentTextChanged.connect(self.apply_filters)
        date_layout.addWidget(self.date_filter)
        filters_layout.addLayout(date_layout)

        # فلتر المورد
        supplier_layout = QHBoxLayout()
        supplier_layout.addWidget(QLabel("المورد:"))

        self.supplier_filter = QComboBox()
        self.supplier_filter.addItem("جميع الموردين")
        self.supplier_filter.currentTextChanged.connect(self.apply_filters)
        supplier_layout.addWidget(self.supplier_filter)
        filters_layout.addLayout(supplier_layout)

        # بحث نصي
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("رقم الشحنة أو اسم المورد...")
        self.search_input.textChanged.connect(self.apply_search)
        search_layout.addWidget(self.search_input)
        filters_layout.addLayout(search_layout)

    def setup_statistics_group(self, parent_layout):
        """إعداد مجموعة الإحصائيات"""
        stats_group = QGroupBox("📊 الإحصائيات")
        stats_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        parent_layout.addWidget(stats_group)

        stats_layout = QVBoxLayout(stats_group)
        stats_layout.setSpacing(8)

        # إحصائيات الحالات
        self.stats_labels = {}
        status_stats = [
            ("إجمالي الشحنات", "#3498db"),
            ("في الطريق", "#2980b9"),
            ("وصلت", "#27ae60"),
            ("متأخرة", "#e74c3c"),
            ("في الميناء", "#f39c12"),
            ("تم التسليم", "#9b59b6")
        ]

        for status, color in status_stats:
            stat_frame = QFrame()
            stat_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {color};
                    border-radius: 5px;
                    padding: 5px;
                }}
            """)

            stat_layout = QHBoxLayout(stat_frame)
            stat_layout.setContentsMargins(8, 4, 8, 4)

            label = QLabel(status)
            label.setStyleSheet("color: white; font-weight: bold;")
            stat_layout.addWidget(label)

            count_label = QLabel("0")
            count_label.setStyleSheet("color: white; font-weight: bold;")
            count_label.setAlignment(Qt.AlignRight)
            stat_layout.addWidget(count_label)

            self.stats_labels[status] = count_label
            stats_layout.addWidget(stat_frame)

    def setup_shipments_list(self, parent_layout):
        """إعداد قائمة الشحنات"""
        list_group = QGroupBox("📋 قائمة الشحنات")
        list_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        parent_layout.addWidget(list_group)

        list_layout = QVBoxLayout(list_group)

        # قائمة الشحنات
        self.shipments_list = QListWidget()
        self.shipments_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #ecf0f1;
            }
        """)
        self.shipments_list.itemClicked.connect(self.on_shipment_selected)
        list_layout.addWidget(self.shipments_list)

    def setup_map_area(self, parent_splitter):
        """إعداد منطقة الخريطة"""
        map_frame = QFrame()
        map_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: none;
            }
        """)
        parent_splitter.addWidget(map_frame)

        map_layout = QVBoxLayout(map_frame)
        map_layout.setContentsMargins(0, 0, 0, 0)
        map_layout.setSpacing(0)

        # الخريطة التفاعلية
        self.map_widget = ShipmentMapWidget()
        self.map_widget.shipment_clicked.connect(self.on_map_shipment_clicked)
        map_layout.addWidget(self.map_widget)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # معلومات الحالة
        self.status_label = QLabel("جاهز")
        self.status_bar.addWidget(self.status_label)

        self.status_bar.addPermanentWidget(QLabel("استخدم عجلة الماوس للتكبير والتصغير"))

    def load_shipments_data(self):
        """تحميل بيانات الشحنات"""
        try:
            session = self.db_manager.get_session()
            shipments = session.query(Shipment).outerjoin(Supplier).filter(
                Shipment.is_active == True
            ).all()

            self.shipments_data = shipments

            # التحقق من وجود ويدجت الخريطة
            if hasattr(self, 'map_widget') and self.map_widget:
                self.map_widget.set_shipments_data(shipments)

            # تحديث قائمة الموردين في الفلتر
            self.update_supplier_filter(shipments)

            # تحديث قائمة الشحنات
            self.update_shipments_list(shipments)

            # تحديث الإحصائيات
            self.update_statistics(shipments)

            if hasattr(self, 'status_label') and self.status_label:
                self.status_label.setText(f"تم تحميل {len(shipments)} شحنة")

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")
            if hasattr(self, 'status_label') and self.status_label:
                self.status_label.setText("خطأ في تحميل البيانات")
            # عرض رسالة خطأ فقط إذا كانت النافذة مرئية
            if self.isVisible():
                QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")

    def update_supplier_filter(self, shipments):
        """تحديث فلتر الموردين"""
        try:
            suppliers = set()
            for shipment in shipments:
                if shipment.supplier and shipment.supplier.name:
                    suppliers.add(shipment.supplier.name)

            if hasattr(self, 'supplier_filter') and self.supplier_filter:
                self.supplier_filter.clear()
                self.supplier_filter.addItem("جميع الموردين")
                for supplier in sorted(suppliers):
                    self.supplier_filter.addItem(supplier)
        except Exception as e:
            print(f"خطأ في تحديث فلتر الموردين: {str(e)}")

    def update_shipments_list(self, shipments):
        """تحديث قائمة الشحنات"""
        try:
            if not hasattr(self, 'shipments_list') or not self.shipments_list:
                return

            self.shipments_list.clear()

            for shipment in shipments:
                supplier_name = shipment.supplier.name if shipment.supplier else "غير محدد"
                status = getattr(shipment, 'shipment_status', 'جديدة')

                item_text = f"#{shipment.id} - {supplier_name}\n{status}"

                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, shipment.id)

                # تلوين العنصر حسب الحالة
                status_colors = {
                    'في الطريق': QColor(52, 152, 219),
                    'وصلت': QColor(46, 204, 113),
                    'متأخرة': QColor(231, 76, 60),
                    'في الميناء': QColor(241, 196, 15),
                    'تم التسليم': QColor(155, 89, 182),
                    'ملغاة': QColor(149, 165, 166),
                    'جديدة': QColor(26, 188, 156)
                }

                if status in status_colors:
                    item.setBackground(status_colors[status].lighter(180))

                self.shipments_list.addItem(item)
        except Exception as e:
            print(f"خطأ في تحديث قائمة الشحنات: {str(e)}")

    def update_statistics(self, shipments):
        """تحديث الإحصائيات"""
        try:
            if not hasattr(self, 'stats_labels') or not self.stats_labels:
                return

            stats = {
                "إجمالي الشحنات": len(shipments),
                "في الطريق": 0,
                "وصلت": 0,
                "متأخرة": 0,
                "في الميناء": 0,
                "تم التسليم": 0
            }

            for shipment in shipments:
                status = getattr(shipment, 'shipment_status', 'جديدة')
                if status in stats:
                    stats[status] += 1

            for status, count in stats.items():
                if status in self.stats_labels:
                    self.stats_labels[status].setText(str(count))
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def apply_filters(self):
        """تطبيق الفلاتر"""
        status_filter = self.status_filter.currentText()
        date_filter = self.date_filter.currentText()
        supplier_filter = self.supplier_filter.currentText()

        # تطبيق الفلاتر على الخريطة
        self.map_widget.filter_shipments(
            status_filter if status_filter != "جميع الحالات" else None,
            date_filter if date_filter != "جميع الفترات" else None,
            supplier_filter if supplier_filter != "جميع الموردين" else None
        )

        # تحديث قائمة الشحنات
        filtered_shipments = self.map_widget.filtered_shipments
        self.update_shipments_list(filtered_shipments)
        self.update_statistics(filtered_shipments)

        if hasattr(self, 'status_label') and self.status_label:
            self.status_label.setText(f"عرض {len(filtered_shipments)} من {len(self.shipments_data)} شحنة")

    def apply_search(self):
        """تطبيق البحث النصي"""
        search_text = self.search_input.text().strip().lower()

        if not search_text:
            self.apply_filters()
            return

        # فلترة الشحنات بناءً على النص
        filtered_shipments = []
        for shipment in self.shipments_data:
            # البحث في رقم الشحنة
            if search_text in str(shipment.id):
                filtered_shipments.append(shipment)
                continue

            # البحث في اسم المورد
            if shipment.supplier and shipment.supplier.name:
                if search_text in shipment.supplier.name.lower():
                    filtered_shipments.append(shipment)
                    continue

            # البحث في رقم التتبع
            tracking_number = getattr(shipment, 'tracking_number', '')
            if tracking_number and search_text in tracking_number.lower():
                filtered_shipments.append(shipment)
                continue

        # تحديث العرض
        self.map_widget.set_shipments_data(filtered_shipments)
        self.update_shipments_list(filtered_shipments)
        self.update_statistics(filtered_shipments)

        if hasattr(self, 'status_label') and self.status_label:
            self.status_label.setText(f"عثر على {len(filtered_shipments)} نتيجة")

    def on_shipment_selected(self, item):
        """معالجة تحديد شحنة من القائمة"""
        shipment_id = item.data(Qt.UserRole)
        if shipment_id:
            self.map_widget.selected_shipment_id = shipment_id
            self.map_widget.update()

    def on_map_shipment_clicked(self, shipment_id):
        """معالجة النقر على شحنة في الخريطة"""
        # تحديد العنصر المقابل في القائمة
        for i in range(self.shipments_list.count()):
            item = self.shipments_list.item(i)
            if item.data(Qt.UserRole) == shipment_id:
                self.shipments_list.setCurrentItem(item)
                break

    def reset_map_view(self):
        """إعادة تعيين عرض الخريطة"""
        self.map_widget.reset_view()

    def toggle_routes(self):
        """تبديل عرض المسارات"""
        self.map_widget.toggle_routes()

    def toggle_labels(self):
        """تبديل عرض التسميات"""
        self.map_widget.toggle_labels()

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_shipments_data()

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        if self.isFullScreen():
            self.showMaximized()
            self.fullscreen_btn.setText("🔳 ملء الشاشة")
        else:
            self.showFullScreen()
            self.fullscreen_btn.setText("🔲 نافذة عادية")

    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_F11:
            self.toggle_fullscreen()
        elif event.key() == Qt.Key_F5:
            self.refresh_data()
        elif event.key() == Qt.Key_Escape and self.isFullScreen():
            self.showMaximized()
            self.fullscreen_btn.setText("🔳 ملء الشاشة")
        else:
            super().keyPressEvent(event)

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        event.accept()


# للاختبار المباشر
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # تطبيق الستايل العربي
    app.setLayoutDirection(Qt.RightToLeft)

    window = ShipmentMapsWindow()
    window.show()

    sys.exit(app.exec())
