#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مكتبة القوالب - Template Library Test
"""

import sys
import os
from datetime import datetime

def print_banner():
    """طباعة شعار الاختبار"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║            🧪 اختبار مكتبة القوالب 🧪                       ║
    ║                                                              ║
    ║           SHIPMENT ERP Template Library Test                 ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    # فحص Python
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python {python_version.major}.{python_version.minor}.{python_version.micro} - يتطلب 3.8+")
        return False
    
    # فحص PySide6
    try:
        import PySide6
        print(f"✅ PySide6 {PySide6.__version__}")
    except ImportError:
        print("❌ PySide6 غير مثبت")
        return False
    
    # فحص الملفات المطلوبة
    required_files = [
        "form_template.py",
        "template_library.py",
        "run_template_library.py",
        "template_examples.py"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    return True

def test_imports():
    """اختبار استيراد المكونات"""
    print("\n🧪 اختبار استيراد المكونات...")
    
    try:
        from form_template import FormTemplate
        print("✅ form_template.FormTemplate")
    except ImportError as e:
        print(f"❌ form_template.FormTemplate - {e}")
        return False
    
    try:
        from template_library import TemplateLibrary, Templates, TemplateManager, TemplateRegistry
        print("✅ template_library.TemplateLibrary")
        print("✅ template_library.Templates")
        print("✅ template_library.TemplateManager")
        print("✅ template_library.TemplateRegistry")
    except ImportError as e:
        print(f"❌ template_library - {e}")
        return False
    
    try:
        from template_library import create_template, نموذج_ادخال_اساسي
        print("✅ template_library.create_template")
        print("✅ template_library.نموذج_ادخال_اساسي")
    except ImportError as e:
        print(f"❌ template_library shortcuts - {e}")
        return False
    
    return True

def test_template_registry():
    """اختبار سجل القوالب"""
    print("\n📋 اختبار سجل القوالب...")
    
    try:
        from template_library import TemplateRegistry
        
        # إنشاء سجل القوالب
        registry = TemplateRegistry()
        print("✅ تم إنشاء سجل القوالب")
        
        # فحص القوالب المسجلة
        templates = registry.get_all_templates()
        print(f"✅ تم العثور على {len(templates)} قالب مسجل")
        
        # فحص القالب الأساسي
        basic_template = registry.get_template("نموذج_ادخال_اساسي")
        if basic_template:
            print("✅ تم العثور على نموذج الإدخال الأساسي")
            print(f"   الاسم: {basic_template['display_name']}")
            print(f"   الوصف: {basic_template['description']}")
            print(f"   الفئة: {basic_template['category']}")
            print(f"   الإصدار: {basic_template['version']}")
        else:
            print("❌ لم يتم العثور على نموذج الإدخال الأساسي")
            return False
        
        # اختبار البحث
        search_results = registry.search_templates("أساسي")
        print(f"✅ البحث عن 'أساسي': {len(search_results)} نتيجة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار سجل القوالب: {e}")
        return False

def test_template_manager():
    """اختبار مدير القوالب"""
    print("\n⚙️ اختبار مدير القوالب...")
    
    try:
        from template_library import TemplateManager
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء مدير القوالب
        manager = TemplateManager()
        manager.set_application(app)
        print("✅ تم إنشاء مدير القوالب")
        
        # اختبار إنشاء قالب
        template = manager.create_template("نموذج_ادخال_اساسي", fullscreen=False, show_immediately=False)
        if template:
            print("✅ تم إنشاء القالب بنجاح")
            print(f"   العنوان: {template.windowTitle()}")
            
            # فحص القوالب النشطة
            active_templates = manager.get_active_templates()
            print(f"✅ القوالب النشطة: {len(active_templates)}")
            
            # إغلاق القالب
            template.close()
            print("✅ تم إغلاق القالب")
        else:
            print("❌ فشل في إنشاء القالب")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير القوالب: {e}")
        return False

def test_template_library():
    """اختبار مكتبة القوالب الرئيسية"""
    print("\n🏛️ اختبار مكتبة القوالب الرئيسية...")
    
    try:
        from template_library import TemplateLibrary, Templates
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إعداد المكتبة
        Templates.setup_app(app)
        print("✅ تم إعداد مكتبة القوالب")
        
        # اختبار قائمة القوالب
        template_list = Templates.list_all()
        print(f"✅ قائمة القوالب: {len(template_list)} قالب")
        for template_name in template_list:
            print(f"   • {template_name}")
        
        # اختبار إنشاء قالب
        template = Templates.create("نموذج_ادخال_اساسي", fullscreen=False)
        if template:
            print("✅ تم إنشاء القالب باستخدام Templates.create()")
            
            # اختبار الحصول على المتغيرات
            variables = template.get_form_variables()
            print(f"✅ تم الحصول على {len(variables)} متغير")
            
            # اختبار جمع البيانات
            data = template.collect_form_data()
            print("✅ تم جمع البيانات بنجاح")
            
            # إغلاق القالب
            template.close()
            print("✅ تم إغلاق القالب")
        else:
            print("❌ فشل في إنشاء القالب")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مكتبة القوالب: {e}")
        return False

def test_shortcuts():
    """اختبار الاختصارات"""
    print("\n⚡ اختبار الاختصارات...")
    
    try:
        from template_library import create_template, نموذج_ادخال_اساسي, Templates
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        Templates.setup_app(app)
        
        # اختبار الدالة المختصرة العامة
        template1 = create_template("نموذج_ادخال_اساسي", fullscreen=False)
        if template1:
            print("✅ create_template() يعمل بنجاح")
            template1.close()
        else:
            print("❌ create_template() فشل")
            return False
        
        # اختبار الدالة المختصرة المخصصة
        template2 = نموذج_ادخال_اساسي(fullscreen=False)
        if template2:
            print("✅ نموذج_ادخال_اساسي() يعمل بنجاح")
            template2.close()
        else:
            print("❌ نموذج_ادخال_اساسي() فشل")
            return False
        
        # اختبار الطريقة المباشرة
        template3 = Templates.نموذج_ادخال_اساسي(fullscreen=False)
        if template3:
            print("✅ Templates.نموذج_ادخال_اساسي() يعمل بنجاح")
            template3.close()
        else:
            print("❌ Templates.نموذج_ادخال_اساسي() فشل")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاختصارات: {e}")
        return False

def test_fullscreen_functionality():
    """اختبار وظائف ملء الشاشة"""
    print("\n🖥️ اختبار وظائف ملء الشاشة...")
    
    try:
        from template_library import Templates
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        Templates.setup_app(app)
        
        # اختبار إنشاء قالب بملء الشاشة
        template_fullscreen = Templates.create("نموذج_ادخال_اساسي", fullscreen=True)
        if template_fullscreen:
            print("✅ تم إنشاء القالب بوضع ملء الشاشة")
            print(f"   حالة ملء الشاشة: {template_fullscreen.fullscreen_mode}")
            template_fullscreen.close()
        else:
            print("❌ فشل في إنشاء القالب بوضع ملء الشاشة")
            return False
        
        # اختبار إنشاء قالب بالوضع العادي
        template_normal = Templates.create("نموذج_ادخال_اساسي", fullscreen=False)
        if template_normal:
            print("✅ تم إنشاء القالب بالوضع العادي")
            print(f"   حالة ملء الشاشة: {template_normal.fullscreen_mode}")
            template_normal.close()
        else:
            print("❌ فشل في إنشاء القالب بالوضع العادي")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظائف ملء الشاشة: {e}")
        return False

def show_summary():
    """عرض ملخص المكتبة"""
    print("\n📊 ملخص مكتبة القوالب:")
    print("   🏛️ مكتبة شاملة لإدارة القوالب")
    print("   📋 نموذج إدخال أساسي متاح")
    print("   🖥️ دعم ملء الشاشة والوضع العادي")
    print("   ⚡ اختصارات سريعة للاستخدام")
    print("   🔍 نظام بحث وتصنيف")
    print("   📱 واجهة سهلة الاستخدام")

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n🚀 تعليمات الاستخدام:")
    print("   📌 تشغيل نافذة اختيار القوالب:")
    print("      python run_template_library.py")
    print("")
    print("   📌 تشغيل الأمثلة:")
    print("      python template_examples.py")
    print("")
    print("   📌 الاستخدام في الكود:")
    print("      from template_library import Templates")
    print("      template = Templates.نموذج_ادخال_اساسي()")
    print("")
    print("   📌 الاختصارات السريعة:")
    print("      from template_library import نموذج_ادخال_اساسي")
    print("      template = نموذج_ادخال_اساسي(fullscreen=True)")

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 65)
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات!")
        input("\nاضغط Enter للخروج...")
        return
    
    print("\n✅ جميع المتطلبات متوفرة")
    
    # اختبار الاستيراد
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيراد!")
        input("\nاضغط Enter للخروج...")
        return
    
    # اختبار سجل القوالب
    if not test_template_registry():
        print("\n❌ فشل في اختبار سجل القوالب!")
        input("\nاضغط Enter للخروج...")
        return
    
    # اختبار مدير القوالب
    if not test_template_manager():
        print("\n❌ فشل في اختبار مدير القوالب!")
        input("\nاضغط Enter للخروج...")
        return
    
    # اختبار مكتبة القوالب
    if not test_template_library():
        print("\n❌ فشل في اختبار مكتبة القوالب!")
        input("\nاضغط Enter للخروج...")
        return
    
    # اختبار الاختصارات
    if not test_shortcuts():
        print("\n❌ فشل في اختبار الاختصارات!")
        input("\nاضغط Enter للخروج...")
        return
    
    # اختبار وظائف ملء الشاشة
    if not test_fullscreen_functionality():
        print("\n❌ فشل في اختبار وظائف ملء الشاشة!")
        input("\nاضغط Enter للخروج...")
        return
    
    print("\n🎉 جميع الاختبارات نجحت!")
    
    # عرض الملخص والتعليمات
    show_summary()
    show_usage_instructions()
    
    print("\n" + "=" * 65)
    print("✅ الاختبار مكتمل بنجاح!")
    print("🎯 مكتبة القوالب جاهزة للاستخدام")
    print("=" * 65)

if __name__ == "__main__":
    main()
