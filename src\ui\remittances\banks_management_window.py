# -*- coding: utf-8 -*-
"""
نافذة إدارة البنوك والصرافين المتقدمة
Advanced Banks and Exchange Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTabWidget, QGroupBox, QGridLayout, QLabel,
                               QPushButton, QLineEdit, QComboBox, QTableWidget,
                               QTableWidgetItem, QHeaderView, QAbstractItemView,
                               QMessageBox, QFrame, QTextEdit, QCheckBox,
                               QDoubleSpinBox, QSpinBox, QDateEdit, QTimeEdit,
                               QProgressBar, QToolBar, QStatusBar, QSplitter,
                               QTreeWidget, QTreeWidgetItem, QScrollArea, QDialog)
from PySide6.QtCore import Qt, QTimer, Signal, QDate, QTime
from PySide6.QtGui import <PERSON><PERSON>ont, QIcon, QPixmap, QColor, QPalette

import sqlite3
import json
from pathlib import Path
from datetime import datetime, timedelta
import requests
import threading

from ...utils.arabic_support import reshape_arabic_text

# استيراد نوافذ الحوار
from .add_new_bank_dialog import AddNewBankDialog
from .add_new_exchange_dialog import AddNewExchangeDialog
from .add_new_branch_dialog import AddNewBranchDialog
from .edit_bank_dialog import EditBankDialog
from .edit_exchange_dialog import EditExchangeDialog
from .edit_branch_dialog import EditBranchDialog
from .delete_bank_dialog import DeleteBankDialog
from .delete_exchange_dialog import DeleteExchangeDialog
from .delete_branch_dialog import DeleteBranchDialog

class BanksManagementWindow(QMainWindow):
    """نافذة إدارة البنوك والصرافين المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة البنوك والصرافين - ProShipment")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
        
        # متغيرات النافذة
        self.banks_data = []
        self.exchange_rates_data = []
        self.branches_data = []
        self.update_timer = QTimer()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        
        # تحميل البيانات
        self.load_initial_data()
        self.start_auto_update()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # عنوان النافذة
        self.create_header_section(layout)
        
        # التبويبات الرئيسية
        self.tabs = QTabWidget()
        
        # تبويب البنوك
        banks_tab = self.create_banks_tab()
        self.tabs.addTab(banks_tab, "🏦 البنوك")
        
        # تبويب الصرافين
        exchanges_tab = self.create_exchanges_tab()
        self.tabs.addTab(exchanges_tab, "💱 الصرافين")
        
        # تبويب أسعار الصرف
        rates_tab = self.create_exchange_rates_tab()
        self.tabs.addTab(rates_tab, "📈 أسعار الصرف")
        
        # تبويب الفروع
        branches_tab = self.create_branches_tab()
        self.tabs.addTab(branches_tab, "🏢 الفروع")
        
        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        self.tabs.addTab(reports_tab, "📊 التقارير")
        
        # تبويب الإعدادات
        settings_tab = self.create_settings_tab()
        self.tabs.addTab(settings_tab, "⚙️ الإعدادات")
        
        layout.addWidget(self.tabs)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1e3a8a, stop:1 #3b82f6);
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة البنوك
        icon_label = QLabel("🏦")
        icon_label.setStyleSheet("font-size: 36px; color: white;")
        header_layout.addWidget(icon_label)
        
        # معلومات العنوان
        info_layout = QVBoxLayout()
        
        title_label = QLabel("إدارة البنوك والصرافين")
        title_label.setFont(QFont("Arial", 20, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        
        subtitle_label = QLabel("إدارة شاملة للبنوك والصرافين وأسعار الصرف والفروع")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setStyleSheet("color: #dbeafe;")
        
        info_layout.addWidget(title_label)
        info_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        stats_layout = QGridLayout(stats_frame)
        
        self.banks_count_label = QLabel("البنوك\n0")
        self.exchanges_count_label = QLabel("الصرافين\n0")
        self.branches_count_label = QLabel("الفروع\n0")
        self.last_update_label = QLabel("آخر تحديث\nغير محدد")
        
        for i, label in enumerate([self.banks_count_label, self.exchanges_count_label, 
                                 self.branches_count_label, self.last_update_label]):
            label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    text-align: center;
                    font-size: 11px;
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(label, 0, i)
        
        header_layout.addWidget(stats_frame)
        
        layout.addWidget(header_frame)
        
    def create_banks_tab(self):
        """إنشاء تبويب البنوك"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أدوات البحث والتصفية
        search_frame = QFrame()
        search_layout = QHBoxLayout(search_frame)
        
        search_layout.addWidget(QLabel("البحث:"))
        self.banks_search_input = QLineEdit()
        self.banks_search_input.setPlaceholderText("ابحث عن بنك...")
        search_layout.addWidget(self.banks_search_input)
        
        search_layout.addWidget(QLabel("النوع:"))
        self.banks_type_filter = QComboBox()
        self.banks_type_filter.addItems(["الكل", "بنك تجاري", "بنك إسلامي", "بنك استثماري", "بنك مركزي"])
        search_layout.addWidget(self.banks_type_filter)
        
        search_layout.addWidget(QLabel("الدولة:"))
        self.banks_country_filter = QComboBox()
        self.banks_country_filter.addItem("الكل")
        search_layout.addWidget(self.banks_country_filter)
        
        search_layout.addStretch()
        
        # أزرار الإجراءات
        add_bank_btn = QPushButton("➕ إضافة بنك")
        add_bank_btn.clicked.connect(self.add_new_bank)
        search_layout.addWidget(add_bank_btn)
        
        edit_bank_btn = QPushButton("✏️ تعديل")
        edit_bank_btn.clicked.connect(self.edit_selected_bank)
        search_layout.addWidget(edit_bank_btn)
        
        delete_bank_btn = QPushButton("🗑️ حذف")
        delete_bank_btn.clicked.connect(self.delete_selected_bank)
        search_layout.addWidget(delete_bank_btn)
        
        layout.addWidget(search_frame)
        
        # جدول البنوك
        self.banks_table = QTableWidget()
        self.setup_banks_table()
        layout.addWidget(self.banks_table)
        
        return tab
        
    def create_exchanges_tab(self):
        """إنشاء تبويب الصرافين"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أدوات البحث والتصفية
        search_frame = QFrame()
        search_layout = QHBoxLayout(search_frame)
        
        search_layout.addWidget(QLabel("البحث:"))
        self.exchanges_search_input = QLineEdit()
        self.exchanges_search_input.setPlaceholderText("ابحث عن صراف...")
        search_layout.addWidget(self.exchanges_search_input)
        
        search_layout.addWidget(QLabel("التصنيف:"))
        self.exchanges_category_filter = QComboBox()
        self.exchanges_category_filter.addItems(["الكل", "صراف معتمد", "صراف محلي", "صراف دولي"])
        search_layout.addWidget(self.exchanges_category_filter)
        
        search_layout.addStretch()
        
        # أزرار الإجراءات
        add_exchange_btn = QPushButton("➕ إضافة صراف")
        add_exchange_btn.clicked.connect(self.add_new_exchange)
        search_layout.addWidget(add_exchange_btn)
        
        edit_exchange_btn = QPushButton("✏️ تعديل")
        edit_exchange_btn.clicked.connect(self.edit_selected_exchange)
        search_layout.addWidget(edit_exchange_btn)
        
        delete_exchange_btn = QPushButton("🗑️ حذف")
        delete_exchange_btn.clicked.connect(self.delete_selected_exchange)
        search_layout.addWidget(delete_exchange_btn)
        
        layout.addWidget(search_frame)
        
        # جدول الصرافين
        self.exchanges_table = QTableWidget()
        self.setup_exchanges_table()
        layout.addWidget(self.exchanges_table)
        
        return tab
        
    def create_exchange_rates_tab(self):
        """إنشاء تبويب أسعار الصرف"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أدوات التحكم
        controls_frame = QFrame()
        controls_layout = QHBoxLayout(controls_frame)
        
        # تحديث الأسعار
        update_rates_btn = QPushButton("🔄 تحديث الأسعار")
        update_rates_btn.clicked.connect(self.update_exchange_rates)
        controls_layout.addWidget(update_rates_btn)
        
        # إضافة سعر يدوي
        add_rate_btn = QPushButton("➕ إضافة سعر")
        add_rate_btn.clicked.connect(self.add_manual_rate)
        controls_layout.addWidget(add_rate_btn)
        
        # تصدير الأسعار
        export_rates_btn = QPushButton("📤 تصدير")
        export_rates_btn.clicked.connect(self.export_exchange_rates)
        controls_layout.addWidget(export_rates_btn)
        
        controls_layout.addStretch()
        
        # حالة التحديث
        self.rates_update_status = QLabel("آخر تحديث: غير محدد")
        controls_layout.addWidget(self.rates_update_status)
        
        layout.addWidget(controls_frame)
        
        # جدول أسعار الصرف
        self.rates_table = QTableWidget()
        self.setup_rates_table()
        layout.addWidget(self.rates_table)
        
        return tab
        
    def create_branches_tab(self):
        """إنشاء تبويب الفروع"""
        tab = QWidget()
        layout = QHBoxLayout(tab)
        
        # الجانب الأيسر - شجرة البنوك والفروع
        left_frame = QFrame()
        left_layout = QVBoxLayout(left_frame)
        
        left_layout.addWidget(QLabel("البنوك والفروع:"))
        
        self.branches_tree = QTreeWidget()
        self.branches_tree.setHeaderLabels(["الاسم", "النوع", "الحالة"])
        left_layout.addWidget(self.branches_tree)
        
        # أزرار إدارة الفروع
        branches_buttons_frame = QFrame()
        branches_buttons_layout = QHBoxLayout(branches_buttons_frame)
        
        add_branch_btn = QPushButton("➕ إضافة فرع")
        add_branch_btn.clicked.connect(self.add_new_branch)
        branches_buttons_layout.addWidget(add_branch_btn)
        
        edit_branch_btn = QPushButton("✏️ تعديل فرع")
        edit_branch_btn.clicked.connect(self.edit_selected_branch)
        branches_buttons_layout.addWidget(edit_branch_btn)
        
        delete_branch_btn = QPushButton("🗑️ حذف فرع")
        delete_branch_btn.clicked.connect(self.delete_selected_branch)
        branches_buttons_layout.addWidget(delete_branch_btn)
        
        left_layout.addWidget(branches_buttons_frame)
        
        # الجانب الأيمن - تفاصيل الفرع
        right_frame = QFrame()
        right_layout = QVBoxLayout(right_frame)
        
        right_layout.addWidget(QLabel("تفاصيل الفرع:"))
        
        self.branch_details_widget = QScrollArea()
        self.setup_branch_details_widget()
        right_layout.addWidget(self.branch_details_widget)
        
        # تقسيم النافذة
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_frame)
        splitter.addWidget(right_frame)
        splitter.setSizes([400, 600])
        
        layout.addWidget(splitter)
        
        return tab

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # أدوات التقارير
        reports_controls_frame = QFrame()
        reports_controls_layout = QGridLayout(reports_controls_frame)

        # نوع التقرير
        reports_controls_layout.addWidget(QLabel("نوع التقرير:"), 0, 0)
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "تقرير البنوك الشامل",
            "تقرير الصرافين",
            "تقرير أسعار الصرف",
            "تقرير الفروع",
            "تقرير المعاملات",
            "تقرير الأداء"
        ])
        reports_controls_layout.addWidget(self.report_type_combo, 0, 1)

        # فترة التقرير
        reports_controls_layout.addWidget(QLabel("من تاريخ:"), 1, 0)
        self.report_from_date = QDateEdit()
        self.report_from_date.setDate(QDate.currentDate().addDays(-30))
        self.report_from_date.setCalendarPopup(True)
        reports_controls_layout.addWidget(self.report_from_date, 1, 1)

        reports_controls_layout.addWidget(QLabel("إلى تاريخ:"), 1, 2)
        self.report_to_date = QDateEdit()
        self.report_to_date.setDate(QDate.currentDate())
        self.report_to_date.setCalendarPopup(True)
        reports_controls_layout.addWidget(self.report_to_date, 1, 3)

        # أزرار التقارير
        generate_report_btn = QPushButton("📊 إنشاء التقرير")
        generate_report_btn.clicked.connect(self.generate_report)
        reports_controls_layout.addWidget(generate_report_btn, 2, 0)

        export_report_btn = QPushButton("📤 تصدير التقرير")
        export_report_btn.clicked.connect(self.export_report)
        reports_controls_layout.addWidget(export_report_btn, 2, 1)

        print_report_btn = QPushButton("🖨️ طباعة التقرير")
        print_report_btn.clicked.connect(self.print_report)
        reports_controls_layout.addWidget(print_report_btn, 2, 2)

        layout.addWidget(reports_controls_frame)

        # منطقة عرض التقرير
        self.report_display = QTextEdit()
        self.report_display.setReadOnly(True)
        layout.addWidget(self.report_display)

        return tab

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات التحديث التلقائي
        auto_update_group = QGroupBox("التحديث التلقائي")
        auto_update_layout = QGridLayout(auto_update_group)

        self.auto_update_rates_checkbox = QCheckBox("تحديث أسعار الصرف تلقائياً")
        self.auto_update_rates_checkbox.setChecked(True)
        auto_update_layout.addWidget(self.auto_update_rates_checkbox, 0, 0, 1, 2)

        auto_update_layout.addWidget(QLabel("فترة التحديث (دقائق):"), 1, 0)
        self.update_interval_spinbox = QSpinBox()
        self.update_interval_spinbox.setRange(1, 1440)  # 1 دقيقة إلى 24 ساعة
        self.update_interval_spinbox.setValue(30)
        auto_update_layout.addWidget(self.update_interval_spinbox, 1, 1)

        layout.addWidget(auto_update_group)

        # إعدادات مصادر البيانات
        data_sources_group = QGroupBox("مصادر البيانات")
        data_sources_layout = QGridLayout(data_sources_group)

        data_sources_layout.addWidget(QLabel("مصدر أسعار الصرف:"), 0, 0)
        self.exchange_source_combo = QComboBox()
        self.exchange_source_combo.addItems([
            "البنك المركزي السعودي",
            "XE.com",
            "CurrencyAPI",
            "يدوي"
        ])
        data_sources_layout.addWidget(self.exchange_source_combo, 0, 1)

        data_sources_layout.addWidget(QLabel("API Key:"), 1, 0)
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.Password)
        data_sources_layout.addWidget(self.api_key_input, 1, 1)

        layout.addWidget(data_sources_group)

        # إعدادات التنبيهات
        alerts_group = QGroupBox("التنبيهات")
        alerts_layout = QGridLayout(alerts_group)

        self.rate_change_alerts_checkbox = QCheckBox("تنبيهات تغيير الأسعار")
        self.rate_change_alerts_checkbox.setChecked(True)
        alerts_layout.addWidget(self.rate_change_alerts_checkbox, 0, 0, 1, 2)

        alerts_layout.addWidget(QLabel("حد التغيير للتنبيه (%):"), 1, 0)
        self.rate_change_threshold_spinbox = QDoubleSpinBox()
        self.rate_change_threshold_spinbox.setRange(0.1, 50.0)
        self.rate_change_threshold_spinbox.setValue(2.0)
        self.rate_change_threshold_spinbox.setDecimals(1)
        alerts_layout.addWidget(self.rate_change_threshold_spinbox, 1, 1)

        layout.addWidget(alerts_group)

        # أزرار الحفظ
        settings_buttons_frame = QFrame()
        settings_buttons_layout = QHBoxLayout(settings_buttons_frame)

        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.clicked.connect(self.save_settings)
        settings_buttons_layout.addWidget(save_settings_btn)

        reset_settings_btn = QPushButton("🔄 إعادة تعيين")
        reset_settings_btn.clicked.connect(self.reset_settings)
        settings_buttons_layout.addWidget(reset_settings_btn)

        settings_buttons_layout.addStretch()
        layout.addWidget(settings_buttons_frame)

        layout.addStretch()
        return tab

    def setup_banks_table(self):
        """إعداد جدول البنوك"""
        headers = ["الرقم", "اسم البنك", "الكود", "النوع", "الدولة", "رقم الترخيص", "الحالة", "تاريخ الإنشاء"]
        self.banks_table.setColumnCount(len(headers))
        self.banks_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.banks_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.banks_table.setAlternatingRowColors(True)
        self.banks_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.banks_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_exchanges_table(self):
        """إعداد جدول الصرافين"""
        headers = ["الرقم", "اسم الصراف", "التصنيف", "رقم الترخيص", "الهاتف", "العنوان", "الحالة", "التقييم"]
        self.exchanges_table.setColumnCount(len(headers))
        self.exchanges_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.exchanges_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.exchanges_table.setAlternatingRowColors(True)
        self.exchanges_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.exchanges_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_rates_table(self):
        """إعداد جدول أسعار الصرف"""
        headers = ["العملة", "الرمز", "سعر الشراء", "سعر البيع", "التغيير", "النسبة %", "آخر تحديث", "المصدر"]
        self.rates_table.setColumnCount(len(headers))
        self.rates_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.rates_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.rates_table.setAlternatingRowColors(True)
        self.rates_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.rates_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_branch_details_widget(self):
        """إعداد ويدجت تفاصيل الفرع"""
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)

        # معلومات أساسية
        basic_info_group = QGroupBox("المعلومات الأساسية")
        basic_info_layout = QGridLayout(basic_info_group)

        basic_info_layout.addWidget(QLabel("اسم الفرع:"), 0, 0)
        self.branch_name_label = QLabel("-")
        basic_info_layout.addWidget(self.branch_name_label, 0, 1)

        basic_info_layout.addWidget(QLabel("كود الفرع:"), 1, 0)
        self.branch_code_label = QLabel("-")
        basic_info_layout.addWidget(self.branch_code_label, 1, 1)

        basic_info_layout.addWidget(QLabel("المدير:"), 2, 0)
        self.branch_manager_label = QLabel("-")
        basic_info_layout.addWidget(self.branch_manager_label, 2, 1)

        details_layout.addWidget(basic_info_group)

        # معلومات الاتصال
        contact_info_group = QGroupBox("معلومات الاتصال")
        contact_info_layout = QGridLayout(contact_info_group)

        contact_info_layout.addWidget(QLabel("الهاتف:"), 0, 0)
        self.branch_phone_label = QLabel("-")
        contact_info_layout.addWidget(self.branch_phone_label, 0, 1)

        contact_info_layout.addWidget(QLabel("البريد الإلكتروني:"), 1, 0)
        self.branch_email_label = QLabel("-")
        contact_info_layout.addWidget(self.branch_email_label, 1, 1)

        contact_info_layout.addWidget(QLabel("العنوان:"), 2, 0)
        self.branch_address_label = QLabel("-")
        self.branch_address_label.setWordWrap(True)
        contact_info_layout.addWidget(self.branch_address_label, 2, 1)

        details_layout.addWidget(contact_info_group)

        # ساعات العمل
        working_hours_group = QGroupBox("ساعات العمل")
        working_hours_layout = QGridLayout(working_hours_group)

        working_hours_layout.addWidget(QLabel("من:"), 0, 0)
        self.branch_open_time_label = QLabel("-")
        working_hours_layout.addWidget(self.branch_open_time_label, 0, 1)

        working_hours_layout.addWidget(QLabel("إلى:"), 0, 2)
        self.branch_close_time_label = QLabel("-")
        working_hours_layout.addWidget(self.branch_close_time_label, 0, 3)

        working_hours_layout.addWidget(QLabel("أيام العمل:"), 1, 0)
        self.branch_working_days_label = QLabel("-")
        working_hours_layout.addWidget(self.branch_working_days_label, 1, 1, 1, 3)

        details_layout.addWidget(working_hours_group)

        details_layout.addStretch()
        self.branch_details_widget.setWidget(details_widget)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # تحديث البيانات
        refresh_action = toolbar.addAction("🔄 تحديث")
        refresh_action.setToolTip("تحديث جميع البيانات")
        refresh_action.triggered.connect(self.refresh_all_data)

        toolbar.addSeparator()

        # إضافة بنك جديد
        add_bank_action = toolbar.addAction("🏦 إضافة بنك")
        add_bank_action.setToolTip("إضافة بنك جديد")
        add_bank_action.triggered.connect(self.add_new_bank)

        # إضافة صراف جديد
        add_exchange_action = toolbar.addAction("💱 إضافة صراف")
        add_exchange_action.setToolTip("إضافة صراف جديد")
        add_exchange_action.triggered.connect(self.add_new_exchange)

        toolbar.addSeparator()

        # تحديث أسعار الصرف
        update_rates_action = toolbar.addAction("📈 تحديث الأسعار")
        update_rates_action.setToolTip("تحديث أسعار الصرف")
        update_rates_action.triggered.connect(self.update_exchange_rates)

        # تصدير البيانات
        export_action = toolbar.addAction("📤 تصدير")
        export_action.setToolTip("تصدير البيانات")
        export_action.triggered.connect(self.export_data)

        toolbar.addSeparator()

        # زر الخروج
        exit_action = toolbar.addAction("❌ خروج")
        exit_action.setToolTip("إغلاق نافذة إدارة البنوك")
        exit_action.triggered.connect(self.close)

    def setup_statusbar(self):
        """إعداد شريط الحالة"""
        self.statusbar = self.statusBar()

        # حالة الاتصال
        self.connection_status = QLabel("متصل")
        self.connection_status.setStyleSheet("color: green; font-weight: bold;")
        self.statusbar.addWidget(self.connection_status)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # آخر تحديث للأسعار
        self.rates_status = QLabel("آخر تحديث للأسعار: غير محدد")
        self.statusbar.addPermanentWidget(self.rates_status)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.statusbar.addPermanentWidget(self.progress_bar)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # البحث في البنوك
        self.banks_search_input.textChanged.connect(self.filter_banks)
        self.banks_type_filter.currentTextChanged.connect(self.filter_banks)
        self.banks_country_filter.currentTextChanged.connect(self.filter_banks)

        # البحث في الصرافين
        self.exchanges_search_input.textChanged.connect(self.filter_exchanges)
        self.exchanges_category_filter.currentTextChanged.connect(self.filter_exchanges)

        # تحديد فرع في الشجرة
        self.branches_tree.itemClicked.connect(self.show_branch_details)

        # تحديث تلقائي للأسعار
        self.auto_update_rates_checkbox.toggled.connect(self.toggle_auto_update)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            self.load_banks_data()
            self.load_exchanges_data()
            self.load_exchange_rates_data()
            self.load_branches_data()
            self.update_statistics()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def start_auto_update(self):
        """بدء التحديث التلقائي"""
        if self.auto_update_rates_checkbox.isChecked():
            interval = self.update_interval_spinbox.value() * 60000  # تحويل إلى ميلي ثانية
            self.update_timer.timeout.connect(self.update_exchange_rates)
            self.update_timer.start(interval)

    # دوال تحميل البيانات
    def load_banks_data(self):
        """تحميل بيانات البنوك"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء جدول البنوك إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS banks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    code TEXT UNIQUE,
                    bank_type TEXT,
                    country TEXT,
                    license_number TEXT,
                    swift_code TEXT,
                    website TEXT,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            cursor.execute("SELECT * FROM banks WHERE is_active = 1 ORDER BY name")
            self.banks_data = cursor.fetchall()

            # تحديث جدول البنوك
            self.update_banks_table()

            # تحديث فلتر الدول
            countries = set()
            for bank in self.banks_data:
                if bank[4]:  # country
                    countries.add(bank[4])

            self.banks_country_filter.clear()
            self.banks_country_filter.addItem("الكل")
            for country in sorted(countries):
                self.banks_country_filter.addItem(country)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات البنوك: {e}")

    def load_exchanges_data(self):
        """تحميل بيانات الصرافين"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء جدول الصرافين إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS exchanges (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    category TEXT,
                    license_number TEXT,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    website TEXT,
                    rating DECIMAL(2,1) DEFAULT 0.0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            cursor.execute("SELECT * FROM exchanges WHERE is_active = 1 ORDER BY name")
            self.exchanges_data = cursor.fetchall()

            # تحديث جدول الصرافين
            self.update_exchanges_table()

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الصرافين: {e}")

    def load_exchange_rates_data(self):
        """تحميل بيانات أسعار الصرف"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء جدول أسعار الصرف إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS exchange_rates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    currency_code TEXT NOT NULL,
                    currency_name TEXT,
                    buy_rate DECIMAL(10,4),
                    sell_rate DECIMAL(10,4),
                    change_amount DECIMAL(10,4) DEFAULT 0.0,
                    change_percentage DECIMAL(5,2) DEFAULT 0.0,
                    source TEXT DEFAULT 'يدوي',
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            """)

            cursor.execute("SELECT * FROM exchange_rates WHERE is_active = 1 ORDER BY currency_code")
            self.exchange_rates_data = cursor.fetchall()

            # تحديث جدول أسعار الصرف
            self.update_rates_table()

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل أسعار الصرف: {e}")

    def load_branches_data(self):
        """تحميل بيانات الفروع"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء جدول الفروع إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS branches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    bank_id INTEGER,
                    exchange_id INTEGER,
                    name TEXT NOT NULL,
                    code TEXT,
                    manager_name TEXT,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    open_time TIME,
                    close_time TIME,
                    working_days TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (bank_id) REFERENCES banks(id),
                    FOREIGN KEY (exchange_id) REFERENCES exchanges(id)
                )
            """)

            # تحميل الفروع مع معلومات البنوك/الصرافين
            cursor.execute("""
                SELECT b.id, b.name, b.name_en, b.code, b.type, b.parent_type, b.parent_id,
                       b.bank_id, b.exchange_id, b.address, b.phone, b.fax, b.email,
                       b.manager_name, b.manager_phone, b.notes, b.is_active, b.created_at,
                       banks.name as bank_name, exchanges.name as exchange_name
                FROM branches b
                LEFT JOIN banks ON b.bank_id = banks.id
                LEFT JOIN exchanges ON b.exchange_id = exchanges.id
                WHERE b.is_active = 1
                ORDER BY b.name
            """)
            self.branches_data = cursor.fetchall()

            # تحديث شجرة الفروع
            self.update_branches_tree()

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الفروع: {e}")

    # دوال تحديث الجداول
    def update_banks_table(self):
        """تحديث جدول البنوك"""
        self.banks_table.setRowCount(len(self.banks_data))

        for row, bank in enumerate(self.banks_data):
            # إنشاء العناصر مع تعيين bank_id في UserRole
            id_item = QTableWidgetItem(str(bank[0]))
            id_item.setData(Qt.UserRole, bank[0])  # تعيين معرف البنك
            self.banks_table.setItem(row, 0, id_item)  # ID

            self.banks_table.setItem(row, 1, QTableWidgetItem(bank[1] or ""))  # Name
            self.banks_table.setItem(row, 2, QTableWidgetItem(bank[2] or ""))  # Code
            self.banks_table.setItem(row, 3, QTableWidgetItem(bank[3] or ""))  # Type
            self.banks_table.setItem(row, 4, QTableWidgetItem(bank[4] or ""))  # Country
            self.banks_table.setItem(row, 5, QTableWidgetItem(bank[5] or ""))  # License
            self.banks_table.setItem(row, 6, QTableWidgetItem("نشط" if bank[11] else "غير نشط"))  # Status
            self.banks_table.setItem(row, 7, QTableWidgetItem(bank[12] or ""))  # Created

    def update_exchanges_table(self):
        """تحديث جدول الصرافين"""
        self.exchanges_table.setRowCount(len(self.exchanges_data))

        for row, exchange in enumerate(self.exchanges_data):
            # إنشاء العناصر مع تعيين exchange_id في UserRole
            id_item = QTableWidgetItem(str(exchange[0]))
            id_item.setData(Qt.UserRole, exchange[0])  # تعيين معرف الصراف
            self.exchanges_table.setItem(row, 0, id_item)  # ID

            self.exchanges_table.setItem(row, 1, QTableWidgetItem(exchange[1] or ""))  # Name
            self.exchanges_table.setItem(row, 2, QTableWidgetItem(exchange[2] or ""))  # Category
            self.exchanges_table.setItem(row, 3, QTableWidgetItem(exchange[3] or ""))  # License
            self.exchanges_table.setItem(row, 4, QTableWidgetItem(exchange[4] or ""))  # Phone
            self.exchanges_table.setItem(row, 5, QTableWidgetItem(exchange[6] or ""))  # Address
            self.exchanges_table.setItem(row, 6, QTableWidgetItem("نشط" if exchange[9] else "غير نشط"))  # Status
            self.exchanges_table.setItem(row, 7, QTableWidgetItem(f"{exchange[8]:.1f}" if exchange[8] else "0.0"))  # Rating

    def update_rates_table(self):
        """تحديث جدول أسعار الصرف"""
        self.rates_table.setRowCount(len(self.exchange_rates_data))

        for row, rate in enumerate(self.exchange_rates_data):
            self.rates_table.setItem(row, 0, QTableWidgetItem(rate[2] or rate[1]))  # Currency name
            self.rates_table.setItem(row, 1, QTableWidgetItem(rate[1] or ""))  # Currency code
            self.rates_table.setItem(row, 2, QTableWidgetItem(f"{rate[3]:.4f}" if rate[3] else "0.0000"))  # Buy rate
            self.rates_table.setItem(row, 3, QTableWidgetItem(f"{rate[4]:.4f}" if rate[4] else "0.0000"))  # Sell rate

            # تلوين التغيير
            change_item = QTableWidgetItem(f"{rate[5]:.4f}" if rate[5] else "0.0000")
            if rate[5] and rate[5] > 0:
                change_item.setBackground(QColor("#dcfce7"))  # أخضر فاتح
            elif rate[5] and rate[5] < 0:
                change_item.setBackground(QColor("#fef2f2"))  # أحمر فاتح
            self.rates_table.setItem(row, 4, change_item)

            # نسبة التغيير
            percentage_item = QTableWidgetItem(f"{rate[6]:.2f}%" if rate[6] else "0.00%")
            if rate[6] and rate[6] > 0:
                percentage_item.setBackground(QColor("#dcfce7"))
            elif rate[6] and rate[6] < 0:
                percentage_item.setBackground(QColor("#fef2f2"))
            self.rates_table.setItem(row, 5, percentage_item)

            self.rates_table.setItem(row, 6, QTableWidgetItem(rate[8] or ""))  # Last updated
            self.rates_table.setItem(row, 7, QTableWidgetItem(rate[7] or "يدوي"))  # Source

    def update_branches_tree(self):
        """تحديث شجرة الفروع"""
        self.branches_tree.clear()

        # تجميع الفروع حسب البنك/الصراف
        banks_branches = {}
        exchanges_branches = {}

        for branch in self.branches_data:
            if branch[7]:  # bank_id (الفهرس الجديد)
                bank_name = branch[18] or f"بنك {branch[7]}"  # bank_name (الفهرس الجديد)
                if bank_name not in banks_branches:
                    banks_branches[bank_name] = []
                banks_branches[bank_name].append(branch)
            elif branch[8]:  # exchange_id (الفهرس الجديد)
                exchange_name = branch[19] or f"صراف {branch[8]}"  # exchange_name (الفهرس الجديد)
                if exchange_name not in exchanges_branches:
                    exchanges_branches[exchange_name] = []
                exchanges_branches[exchange_name].append(branch)

        # إضافة البنوك وفروعها
        for bank_name, branches in banks_branches.items():
            bank_item = QTreeWidgetItem(self.branches_tree, [bank_name, "بنك", "نشط"])
            for branch in branches:
                branch_item = QTreeWidgetItem(bank_item, [
                    branch[1],  # name (الفهرس الجديد)
                    "فرع",
                    "نشط" if branch[16] else "غير نشط"  # is_active (الفهرس الجديد)
                ])
                branch_item.setData(0, Qt.UserRole, branch[0])  # branch ID

        # إضافة الصرافين وفروعهم
        for exchange_name, branches in exchanges_branches.items():
            exchange_item = QTreeWidgetItem(self.branches_tree, [exchange_name, "صراف", "نشط"])
            for branch in branches:
                branch_item = QTreeWidgetItem(exchange_item, [
                    branch[1],  # name (الفهرس الجديد)
                    "فرع",
                    "نشط" if branch[16] else "غير نشط"  # is_active (الفهرس الجديد)
                ])
                branch_item.setData(0, Qt.UserRole, branch[0])  # branch ID

        self.branches_tree.expandAll()

    def update_statistics(self):
        """تحديث الإحصائيات"""
        banks_count = len([b for b in self.banks_data if b[11]])  # active banks
        exchanges_count = len([e for e in self.exchanges_data if e[9]])  # active exchanges
        branches_count = len([b for b in self.branches_data if b[16]])  # active branches (الفهرس الجديد)

        self.banks_count_label.setText(f"البنوك\n{banks_count}")
        self.exchanges_count_label.setText(f"الصرافين\n{exchanges_count}")
        self.branches_count_label.setText(f"الفروع\n{branches_count}")

        # آخر تحديث
        if self.exchange_rates_data:
            last_update = max([r[8] for r in self.exchange_rates_data if r[8]])
            if last_update:
                self.last_update_label.setText(f"آخر تحديث\n{last_update[:16]}")

    # دوال التصفية والبحث
    def filter_banks(self):
        """تصفية البنوك"""
        search_text = self.banks_search_input.text().lower()
        type_filter = self.banks_type_filter.currentText()
        country_filter = self.banks_country_filter.currentText()

        for row in range(self.banks_table.rowCount()):
            show_row = True

            # تصفية النص
            if search_text:
                bank_name = self.banks_table.item(row, 1).text().lower()
                bank_code = self.banks_table.item(row, 2).text().lower()
                if search_text not in bank_name and search_text not in bank_code:
                    show_row = False

            # تصفية النوع
            if type_filter != "الكل":
                bank_type = self.banks_table.item(row, 3).text()
                if bank_type != type_filter:
                    show_row = False

            # تصفية الدولة
            if country_filter != "الكل":
                bank_country = self.banks_table.item(row, 4).text()
                if bank_country != country_filter:
                    show_row = False

            self.banks_table.setRowHidden(row, not show_row)

    def filter_exchanges(self):
        """تصفية الصرافين"""
        search_text = self.exchanges_search_input.text().lower()
        category_filter = self.exchanges_category_filter.currentText()

        for row in range(self.exchanges_table.rowCount()):
            show_row = True

            # تصفية النص
            if search_text:
                exchange_name = self.exchanges_table.item(row, 1).text().lower()
                if search_text not in exchange_name:
                    show_row = False

            # تصفية التصنيف
            if category_filter != "الكل":
                exchange_category = self.exchanges_table.item(row, 2).text()
                if exchange_category != category_filter:
                    show_row = False

            self.exchanges_table.setRowHidden(row, not show_row)

    def show_branch_details(self, item):
        """عرض تفاصيل الفرع المحدد"""
        branch_id = item.data(0, Qt.UserRole)
        if not branch_id:
            return

        # البحث عن الفرع
        branch = None
        for b in self.branches_data:
            if b[0] == branch_id:
                branch = b
                break

        if not branch:
            return

        # تحديث تفاصيل الفرع
        self.branch_name_label.setText(branch[3] or "-")
        self.branch_code_label.setText(branch[4] or "-")
        self.branch_manager_label.setText(branch[5] or "-")
        self.branch_phone_label.setText(branch[6] or "-")
        self.branch_email_label.setText(branch[7] or "-")
        self.branch_address_label.setText(branch[8] or "-")
        self.branch_open_time_label.setText(branch[9] or "-")
        self.branch_close_time_label.setText(branch[10] or "-")
        self.branch_working_days_label.setText(branch[11] or "-")

    # دوال الإجراءات
    def add_new_bank(self):
        """إضافة بنك جديد"""
        try:
            from .add_new_bank_dialog import AddNewBankDialog

            dialog = AddNewBankDialog(self)
            dialog.bank_added.connect(self.on_bank_added)

            if dialog.exec() == QDialog.Accepted:
                self.load_banks_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إضافة البنك:\n{str(e)}")

    def on_bank_added(self, bank_id):
        """معالج إضافة بنك جديد"""
        self.load_banks_data()
        QMessageBox.information(self, "نجح", "تم إضافة البنك بنجاح وتحديث القائمة")

    def edit_selected_bank(self):
        """تعديل البنك المحدد"""
        selected_items = self.banks_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار بنك للتعديل")
            return

        # الحصول على معرف البنك
        row = selected_items[0].row()
        bank_id = self.banks_table.item(row, 0).data(Qt.UserRole)

        if bank_id:
            dialog = EditBankDialog(bank_id, self)
            dialog.bank_updated.connect(self.on_bank_updated)
            dialog.exec()

    def delete_selected_bank(self):
        """حذف البنك المحدد"""
        selected_items = self.banks_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار بنك للحذف")
            return

        # الحصول على معرف واسم البنك
        row = selected_items[0].row()
        bank_id = self.banks_table.item(row, 0).data(Qt.UserRole)
        bank_name = self.banks_table.item(row, 1).text()

        if bank_id:
            dialog = DeleteBankDialog(bank_id, bank_name, self)
            dialog.bank_deleted.connect(self.on_bank_deleted)
            dialog.exec()

    def on_bank_updated(self, bank_id):
        """معالج تحديث البنك"""
        self.load_banks_data()
        self.update_statistics()
        QMessageBox.information(self, "نجح", f"تم تحديث البنك بنجاح (ID: {bank_id})")

    def on_bank_deleted(self, bank_id):
        """معالج حذف البنك"""
        self.load_banks_data()
        self.update_statistics()
        # لا نحتاج رسالة هنا لأن نافذة الحذف تظهر رسالة بالفعل

    def add_new_exchange(self):
        """إضافة صراف جديد"""
        try:
            from .add_new_exchange_dialog import AddNewExchangeDialog

            dialog = AddNewExchangeDialog(self)
            dialog.exchange_added.connect(self.on_exchange_added)

            if dialog.exec() == QDialog.Accepted:
                self.load_exchanges_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إضافة الصراف:\n{str(e)}")

    def on_exchange_added(self, exchange_id):
        """معالج إضافة صراف جديد"""
        self.load_exchanges_data()
        QMessageBox.information(self, "نجح", "تم إضافة الصراف بنجاح وتحديث القائمة")

    def edit_selected_exchange(self):
        """تعديل الصراف المحدد"""
        selected_items = self.exchanges_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صراف للتعديل")
            return

        # الحصول على معرف الصراف
        row = selected_items[0].row()
        exchange_id = self.exchanges_table.item(row, 0).data(Qt.UserRole)

        if exchange_id:
            dialog = EditExchangeDialog(exchange_id, self)
            dialog.exchange_updated.connect(self.on_exchange_updated)
            dialog.exec()

    def delete_selected_exchange(self):
        """حذف الصراف المحدد"""
        selected_items = self.exchanges_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صراف للحذف")
            return

        # الحصول على معرف واسم الصراف
        row = selected_items[0].row()
        exchange_id = self.exchanges_table.item(row, 0).data(Qt.UserRole)
        exchange_name = self.exchanges_table.item(row, 1).text()

        if exchange_id:
            dialog = DeleteExchangeDialog(exchange_id, exchange_name, self)
            dialog.exchange_deleted.connect(self.on_exchange_deleted)
            dialog.exec()

    def on_exchange_updated(self, exchange_id):
        """معالج تحديث الصراف"""
        self.load_exchanges_data()
        self.update_statistics()
        QMessageBox.information(self, "نجح", f"تم تحديث الصراف بنجاح (ID: {exchange_id})")

    def on_exchange_deleted(self, exchange_id):
        """معالج حذف الصراف"""
        self.load_exchanges_data()
        self.update_statistics()
        # لا نحتاج رسالة هنا لأن نافذة الحذف تظهر رسالة بالفعل

    def add_new_branch(self):
        """إضافة فرع جديد"""
        try:
            from .add_new_branch_dialog import AddNewBranchDialog

            dialog = AddNewBranchDialog(self)
            dialog.branch_added.connect(self.on_branch_added)

            if dialog.exec() == QDialog.Accepted:
                self.load_branches_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إضافة الفرع:\n{str(e)}")

    def on_branch_added(self, branch_id):
        """معالج إضافة فرع جديد"""
        print(f"تم استلام إشارة إضافة فرع جديد: {branch_id}")
        self.load_branches_data()
        self.update_statistics()
        print("تم تحديث قائمة الفروع والإحصائيات")
        QMessageBox.information(self, "نجح", f"تم إضافة الفرع بنجاح (ID: {branch_id}) وتحديث القائمة")

    def edit_selected_branch(self):
        """تعديل الفرع المحدد"""
        selected_items = self.branches_tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فرع للتعديل")
            return

        # التأكد من أن العنصر المحدد هو فرع وليس بنك/صراف
        selected_item = selected_items[0]
        branch_id = selected_item.data(0, Qt.UserRole)

        if not branch_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فرع وليس بنك أو صراف")
            return

        dialog = EditBranchDialog(branch_id, self)
        dialog.branch_updated.connect(self.on_branch_updated)
        dialog.exec()

    def delete_selected_branch(self):
        """حذف الفرع المحدد"""
        selected_items = self.branches_tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فرع للحذف")
            return

        # التأكد من أن العنصر المحدد هو فرع وليس بنك/صراف
        selected_item = selected_items[0]
        branch_id = selected_item.data(0, Qt.UserRole)
        branch_name = selected_item.text(0)

        if not branch_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فرع وليس بنك أو صراف")
            return

        dialog = DeleteBranchDialog(branch_id, branch_name, self)
        dialog.branch_deleted.connect(self.on_branch_deleted)
        dialog.exec()

    def on_branch_updated(self, branch_id):
        """معالج تحديث الفرع"""
        self.load_branches_data()
        self.update_statistics()
        QMessageBox.information(self, "نجح", f"تم تحديث الفرع بنجاح (ID: {branch_id})")

    def on_branch_deleted(self, branch_id):
        """معالج حذف الفرع"""
        self.load_branches_data()
        self.update_statistics()
        # لا نحتاج رسالة هنا لأن نافذة الحذف تظهر رسالة بالفعل

    def update_exchange_rates(self):
        """تحديث أسعار الصرف"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        self.rates_status.setText("جاري تحديث الأسعار...")

        # محاكاة تحديث الأسعار
        QTimer.singleShot(2000, self.finish_rates_update)

    def finish_rates_update(self):
        """إنهاء تحديث الأسعار"""
        self.progress_bar.setVisible(False)
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        self.rates_status.setText(f"آخر تحديث للأسعار: {current_time}")
        self.last_update_label.setText(f"آخر تحديث\n{current_time}")
        QMessageBox.information(self, "تم", "تم تحديث أسعار الصرف بنجاح")

    def add_manual_rate(self):
        """إضافة سعر صرف يدوي"""
        QMessageBox.information(self, "قريباً", "نافذة إضافة سعر صرف يدوي قيد التطوير")

    def export_exchange_rates(self):
        """تصدير أسعار الصرف"""
        QMessageBox.information(self, "قريباً", "وظيفة تصدير أسعار الصرف قيد التطوير")

    def generate_report(self):
        """إنشاء التقرير"""
        report_type = self.report_type_combo.currentText()
        from_date = self.report_from_date.date().toString("yyyy-MM-dd")
        to_date = self.report_to_date.date().toString("yyyy-MM-dd")

        report_content = f"""
تقرير: {report_type}
الفترة: من {from_date} إلى {to_date}
تاريخ الإنشاء: {datetime.now().strftime("%Y-%m-%d %H:%M")}

========================================

إحصائيات عامة:
- عدد البنوك النشطة: {len([b for b in self.banks_data if b[11]])}
- عدد الصرافين النشطين: {len([e for e in self.exchanges_data if e[9]])}
- عدد الفروع النشطة: {len([b for b in self.branches_data if b[12]])}
- عدد أسعار الصرف المتاحة: {len(self.exchange_rates_data)}

========================================

ملاحظة: هذا تقرير تجريبي. التقارير التفصيلية قيد التطوير.
        """

        self.report_display.setPlainText(report_content)

    def export_report(self):
        """تصدير التقرير"""
        QMessageBox.information(self, "قريباً", "وظيفة تصدير التقرير قيد التطوير")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قريباً", "وظيفة طباعة التقرير قيد التطوير")

    def save_settings(self):
        """حفظ الإعدادات"""
        QMessageBox.information(self, "تم", "تم حفظ الإعدادات بنجاح")

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        self.auto_update_rates_checkbox.setChecked(True)
        self.update_interval_spinbox.setValue(30)
        self.exchange_source_combo.setCurrentIndex(0)
        self.api_key_input.clear()
        self.rate_change_alerts_checkbox.setChecked(True)
        self.rate_change_threshold_spinbox.setValue(2.0)

    def toggle_auto_update(self, enabled):
        """تفعيل/تعطيل التحديث التلقائي"""
        if enabled:
            self.start_auto_update()
        else:
            self.update_timer.stop()

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        self.load_initial_data()
        QMessageBox.information(self, "تم", "تم تحديث جميع البيانات")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "قريباً", "وظيفة تصدير البيانات قيد التطوير")
