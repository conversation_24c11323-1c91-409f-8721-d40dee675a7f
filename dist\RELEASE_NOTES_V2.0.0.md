# ProShipment V2.0.0 - ملاحظات الإصدار

## تاريخ الإصدار: 2025-07-11

### الميزات الجديدة الرئيسية:
- 🗄️ دعم Oracle Database الكامل
- 🔄 أداة نقل البيانات الشاملة
- 🔧 معالج إعداد Oracle التفاعلي
- 📊 نظام مراقبة وتشخيص متقدم
- 💾 نظام النسخ الاحتياطي المتطور

### التحسينات:
- ✅ إصلاح مشكلة تحديث طلب الحوالة
- ✅ إضافة حقل Bank Country
- ✅ تحسين دفتر العناوين
- ✅ واجهة مستخدم محدثة

### التثبيت:
1. فك ضغط الملف
2. تثبيت المتطلبات: `pip install -r requirements.txt`
3. فحص البيئة: `python tools/environment_checker.py`
4. تشغيل التطبيق: `python main.py`

### للترقية من V1.x:
1. إنشاء نسخة احتياطية من البيانات
2. إعداد Oracle (اختياري): `python tools/oracle_setup_wizard.py`
3. نقل البيانات: `python tools/data_migration_tool.py`

### الدعم:
- 📧 البريد الإلكتروني: <EMAIL>
- 📚 الوثائق: docs/oracle_integration_guide.md
- 🚀 البدء السريع: docs/quick_start_oracle.md

---
ProShipment V2.0.0 - نظام إدارة الشحنات المتقدم
