#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدراج البيانات الافتراضية بالقوة
Force Insert Default Data
"""

import sys
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.universal_database_manager import UniversalDatabaseManager
from src.database.oracle_config import DatabaseConfigManager
from src.database.models import UnitOfMeasure, ItemGroup, SystemSettings, Currency, FiscalYear, Company

def force_insert_units():
    """إدراج وحدات القياس بالقوة"""
    
    print("📏 إدراج وحدات القياس...")
    
    try:
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        # وحدات القياس الافتراضية
        default_units = [
            # وحدات الوزن
            UnitOfMeasure(
                name="كيلوجرام",
                name_en="Kilogram",
                symbol="كجم",
                symbol_en="kg",
                description="وحدة قياس الوزن الأساسية"
            ),
            UnitOfMeasure(
                name="جرام",
                name_en="Gram",
                symbol="جم",
                symbol_en="g",
                description="وحدة قياس الوزن الصغيرة"
            ),
            UnitOfMeasure(
                name="طن",
                name_en="Ton",
                symbol="طن",
                symbol_en="t",
                description="وحدة قياس الوزن الكبيرة"
            ),
            # وحدات الطول
            UnitOfMeasure(
                name="متر",
                name_en="Meter",
                symbol="م",
                symbol_en="m",
                description="وحدة قياس الطول الأساسية"
            ),
            UnitOfMeasure(
                name="سنتيمتر",
                name_en="Centimeter",
                symbol="سم",
                symbol_en="cm",
                description="وحدة قياس الطول الصغيرة"
            ),
            UnitOfMeasure(
                name="مليمتر",
                name_en="Millimeter",
                symbol="مم",
                symbol_en="mm",
                description="وحدة قياس الطول الدقيقة"
            ),
            # وحدات المساحة
            UnitOfMeasure(
                name="متر مربع",
                name_en="Square Meter",
                symbol="م²",
                symbol_en="m²",
                description="وحدة قياس المساحة"
            ),
            # وحدات الحجم
            UnitOfMeasure(
                name="متر مكعب",
                name_en="Cubic Meter",
                symbol="م³",
                symbol_en="m³",
                description="وحدة قياس الحجم"
            ),
            UnitOfMeasure(
                name="لتر",
                name_en="Liter",
                symbol="لتر",
                symbol_en="L",
                description="وحدة قياس السوائل"
            ),
            # وحدات العد
            UnitOfMeasure(
                name="قطعة",
                name_en="Piece",
                symbol="قطعة",
                symbol_en="pcs",
                description="وحدة العد الأساسية"
            ),
            UnitOfMeasure(
                name="عبوة",
                name_en="Package",
                symbol="عبوة",
                symbol_en="pkg",
                description="وحدة التعبئة"
            ),
            UnitOfMeasure(
                name="صندوق",
                name_en="Box",
                symbol="صندوق",
                symbol_en="box",
                description="وحدة التعبئة الكبيرة"
            ),
            UnitOfMeasure(
                name="كرتون",
                name_en="Carton",
                symbol="كرتون",
                symbol_en="ctn",
                description="وحدة التعبئة المتوسطة"
            ),
            # وحدات خاصة
            UnitOfMeasure(
                name="زوج",
                name_en="Pair",
                symbol="زوج",
                symbol_en="pair",
                description="وحدة للأشياء المزدوجة"
            ),
            UnitOfMeasure(
                name="دستة",
                name_en="Dozen",
                symbol="دستة",
                symbol_en="dz",
                description="12 قطعة"
            )
        ]
        
        with db_manager.get_session() as session:
            added_count = 0
            skipped_count = 0
            
            for unit in default_units:
                # التحقق من عدم وجود الوحدة مسبقاً
                existing = session.query(UnitOfMeasure).filter_by(name=unit.name).first()
                
                if existing:
                    print(f"   ⚠️ الوحدة '{unit.name}' موجودة مسبقاً - تم التخطي")
                    skipped_count += 1
                    continue
                
                session.add(unit)
                print(f"   ✅ تم إضافة الوحدة: {unit.name} ({unit.symbol})")
                added_count += 1
            
            print(f"\n📊 ملخص وحدات القياس:")
            print(f"   ✅ تم إضافة: {added_count} وحدة")
            print(f"   ⚠️ تم تخطي: {skipped_count} وحدة")
            
            return added_count > 0
            
    except Exception as e:
        print(f"❌ خطأ في إدراج وحدات القياس: {e}")
        import traceback
        traceback.print_exc()
        return False

def force_insert_item_groups():
    """إدراج مجموعات الأصناف بالقوة"""
    
    print("\n📂 إدراج مجموعات الأصناف...")
    
    try:
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        # مجموعات الأصناف الافتراضية
        default_item_groups = [
            ItemGroup(
                name="إلكترونيات",
                name_en="Electronics",
                description="الأجهزة والمعدات الإلكترونية"
            ),
            ItemGroup(
                name="ملابس",
                name_en="Clothing",
                description="الملابس والأزياء"
            ),
            ItemGroup(
                name="أغذية",
                name_en="Food",
                description="المواد الغذائية والمشروبات"
            ),
            ItemGroup(
                name="أدوات منزلية",
                name_en="Home Appliances",
                description="الأدوات والأجهزة المنزلية"
            ),
            ItemGroup(
                name="مواد بناء",
                name_en="Construction Materials",
                description="مواد ومعدات البناء"
            ),
            ItemGroup(
                name="قطع غيار",
                name_en="Spare Parts",
                description="قطع الغيار والمكونات"
            ),
            ItemGroup(
                name="مستحضرات تجميل",
                name_en="Cosmetics",
                description="مستحضرات التجميل والعناية"
            ),
            ItemGroup(
                name="كتب ومطبوعات",
                name_en="Books & Publications",
                description="الكتب والمطبوعات والقرطاسية"
            )
        ]
        
        with db_manager.get_session() as session:
            added_count = 0
            skipped_count = 0
            
            for group in default_item_groups:
                # التحقق من عدم وجود المجموعة مسبقاً
                existing = session.query(ItemGroup).filter_by(name=group.name).first()
                
                if existing:
                    print(f"   ⚠️ المجموعة '{group.name}' موجودة مسبقاً - تم التخطي")
                    skipped_count += 1
                    continue
                
                session.add(group)
                print(f"   ✅ تم إضافة المجموعة: {group.name}")
                added_count += 1
            
            print(f"\n📊 ملخص مجموعات الأصناف:")
            print(f"   ✅ تم إضافة: {added_count} مجموعة")
            print(f"   ⚠️ تم تخطي: {skipped_count} مجموعة")
            
            return added_count > 0
            
    except Exception as e:
        print(f"❌ خطأ في إدراج مجموعات الأصناف: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_status():
    """فحص حالة قاعدة البيانات"""
    
    print("🔍 فحص حالة قاعدة البيانات...")
    
    try:
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        with db_manager.get_session() as session:
            # فحص الجداول
            units_count = session.query(UnitOfMeasure).count()
            groups_count = session.query(ItemGroup).count()
            settings_count = session.query(SystemSettings).count()
            
            print(f"   📏 وحدات القياس: {units_count}")
            print(f"   📂 مجموعات الأصناف: {groups_count}")
            print(f"   ⚙️ إعدادات النظام: {settings_count}")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 إدراج البيانات الافتراضية بالقوة")
    print("=" * 50)
    
    # فحص الحالة الحالية
    check_database_status()
    
    # إدراج وحدات القياس
    units_success = force_insert_units()
    
    # إدراج مجموعات الأصناف
    groups_success = force_insert_item_groups()
    
    # فحص الحالة النهائية
    print("\n" + "=" * 50)
    print("📊 الحالة النهائية:")
    check_database_status()
    
    if units_success or groups_success:
        print("\n🎉 تم إدراج البيانات بنجاح!")
        print("💡 يمكنك الآن استخدام النظام مع البيانات الافتراضية")
    else:
        print("\n💡 جميع البيانات موجودة مسبقاً أو حدث خطأ")
    
    return units_success or groups_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
