# دليل البدء السريع - Oracle مع ProShipment V2.0.0

## 🚀 البدء السريع (5 دقائق)

### الخطوة 1: التحقق من المتطلبات
```bash
# فحص البيئة
python tools/environment_checker.py
```

### الخطوة 2: تثبيت Oracle Instant Client
```bash
# Windows
# حمل من: https://www.oracle.com/database/technologies/instant-client/downloads.html
# فك الضغط وأضف إلى PATH

# Linux (Ubuntu/Debian)
sudo apt-get install oracle-instantclient-basic

# macOS
brew install instantclient-basic
```

### الخطوة 3: إعداد Oracle Database
```sql
-- إنشاء مستخدم
CREATE USER proshipment IDENTIFIED BY "password123";
GRANT CONNECT, RESOURCE TO proshipment;
GRANT CREATE SESSION TO proshipment;
ALTER USER proshipment QUOTA UNLIMITED ON USERS;
```

### الخطوة 4: تكوين التطبيق
```bash
# تشغيل معالج الإعداد
python tools/oracle_setup_wizard.py

# أو نسخ إعدادات جاهزة
cp config/database_development.json config/database.json
```

### الخطوة 5: نقل البيانات (اختياري)
```bash
# نقل من SQLite إلى Oracle
python tools/data_migration_tool.py
```

### الخطوة 6: تشغيل التطبيق
```bash
python main.py
```

---

## 🔧 إعدادات سريعة

### Oracle XE المحلي:
```json
{
  "type": "oracle",
  "oracle_config": {
    "host": "localhost",
    "port": 1521,
    "service_name": "XE",
    "username": "proshipment",
    "password": "password123"
  }
}
```

### Oracle Cloud:
```json
{
  "type": "oracle",
  "oracle_config": {
    "host": "your-cloud-instance.oraclecloud.com",
    "port": 1522,
    "service_name": "proship_high",
    "username": "ADMIN",
    "password": "CloudPassword123",
    "use_ssl": true,
    "wallet_location": "/path/to/wallet"
  }
}
```

---

## ⚡ أوامر سريعة

### اختبار الاتصال:
```bash
python -c "
from src.database.universal_database_manager import create_oracle_manager
manager = create_oracle_manager('localhost', 1521, 'XE', 'proshipment', 'password123')
print('✅ نجح' if manager.test_connection() else '❌ فشل')
"
```

### نقل جدول واحد:
```bash
python -c "
from tools.data_migration_tool import DataMigrationTool
tool = DataMigrationTool()
# إعداد الاتصالات ثم
tool.migrate_table_data('shipments')
"
```

### إنشاء نسخة احتياطية:
```bash
python -c "
from src.database.universal_database_manager import create_oracle_manager
manager = create_oracle_manager('localhost', 1521, 'XE', 'proshipment', 'password123')
manager.backup_database()
"
```

---

## 🔍 استكشاف الأخطاء السريع

### خطأ "TNS:listener does not currently know of service":
```bash
# تحقق من Oracle
lsnrctl status
# إعادة تشغيل
lsnrctl stop && lsnrctl start
```

### خطأ "cx_Oracle not found":
```bash
pip install cx_Oracle
```

### خطأ "ORA-12154":
```bash
# تحقق من متغيرات البيئة
echo $ORACLE_HOME
echo $TNS_ADMIN
```

---

## 📱 واجهة المستخدم

### فتح إعدادات قاعدة البيانات:
1. افتح التطبيق
2. اذهب إلى **الإعدادات** → **إعدادات قاعدة البيانات**
3. اختر **Oracle Database**
4. أدخل بيانات الاتصال
5. اضغط **اختبار الاتصال**
6. احفظ الإعدادات

### نقل البيانات من الواجهة:
1. اذهب إلى **إدارة قاعدة البيانات** → **نقل البيانات**
2. اختر المصدر والهدف
3. اضغط **بدء النقل**
4. انتظر انتهاء العملية

---

## 🎯 نصائح مهمة

### للأداء الأفضل:
- استخدم `pool_size` بين 20-50
- فعل `pool_pre_ping`
- استخدم SSL في الإنتاج

### للأمان:
- لا تحفظ كلمات المرور في الملفات
- استخدم متغيرات البيئة
- فعل SSL/TLS

### للصيانة:
- انشئ نسخ احتياطية دورية
- راقب سجلات الأخطاء
- حدث الإحصائيات بانتظام

---

## 📞 مساعدة سريعة

### أوامر مفيدة:
```bash
# فحص شامل للبيئة
python tools/environment_checker.py

# اختبار أداة النقل
python tools/data_migration_tool.py

# إنشاء إعدادات نموذجية
python config/oracle_samples.py

# تشغيل معالج الإعداد
python tools/oracle_setup_wizard.py
```

### ملفات مهمة:
- `config/database.json` - إعدادات قاعدة البيانات
- `logs/migration.log` - سجل نقل البيانات
- `docs/oracle_integration_guide.md` - الدليل الكامل

---

## ✅ قائمة التحقق

- [ ] تثبيت Oracle Instant Client
- [ ] إنشاء مستخدم Oracle
- [ ] تكوين إعدادات التطبيق
- [ ] اختبار الاتصال
- [ ] نقل البيانات (إذا لزم الأمر)
- [ ] تشغيل التطبيق
- [ ] إنشاء نسخة احتياطية

---

*للمزيد من التفاصيل، راجع [الدليل الكامل](oracle_integration_guide.md)*
