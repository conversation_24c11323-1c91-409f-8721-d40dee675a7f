# -*- coding: utf-8 -*-
"""
نافذة طلب التحويل الجديدة
New Transfer Request Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QPushButton, QLineEdit, QComboBox, 
                               QTextEdit, QDateEdit, QDoubleSpinBox, QFrame,
                               QGroupBox, QGridLayout, QMessageBox, QProgressBar,
                               QCheckBox, QSpinBox, QTabWidget, QWidget)
from PySide6.QtCore import Qt, Signal, QDate, QTimer
from PySide6.QtGui import QFont, QPixmap, QIcon

import sqlite3
from pathlib import Path
from datetime import datetime
import json
import uuid

class TransferRequestDialog(QDialog):
    """نافذة طلب التحويل الجديدة"""
    
    # إشارات للتواصل مع النوافذ الأخرى
    transfer_request_created = Signal(dict)  # إشارة عند إنشاء طلب التحويل
    send_to_remittance = Signal(dict)  # إشارة لإرسال البيانات لنافذة إنشاء الحوالة
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("طلب تحويل جديد - نظام إدارة الحوالات")
        self.setMinimumSize(1000, 800)
        self.resize(1100, 850)
        self.setModal(True)

        # معرف فريد لطلب التحويل
        self.request_id = str(uuid.uuid4())
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.load_data()
        
        # تطبيق الستايل العصري
        self.apply_modern_style()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)

        # العنوان الرئيسي مع أيقونة
        header_frame = self.create_header()
        layout.addWidget(header_frame)

        # التبويبات الرئيسية
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #3498db;
                border-radius: 10px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)

        # تبويب المعلومات الأساسية
        basic_info_tab = self.create_basic_info_tab()
        self.tab_widget.addTab(basic_info_tab, "📋 المعلومات الأساسية")

        # تبويب تفاصيل التحويل
        transfer_details_tab = self.create_transfer_details_tab()
        self.tab_widget.addTab(transfer_details_tab, "💰 تفاصيل التحويل")

        # تبويب الملاحظات والمرفقات
        notes_tab = self.create_notes_tab()
        self.tab_widget.addTab(notes_tab, "📝 ملاحظات ومرفقات")

        layout.addWidget(self.tab_widget)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 12px;
                height: 25px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 6px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # أزرار الإجراءات
        buttons_frame = self.create_buttons_section()
        layout.addWidget(buttons_frame)

    def create_header(self):
        """إنشاء رأس النافذة"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(20, 15, 20, 15)

        # أيقونة
        icon_label = QLabel("📤")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: white;
                background: transparent;
            }
        """)
        layout.addWidget(icon_label)

        # النص
        text_layout = QVBoxLayout()
        
        title_label = QLabel("طلب تحويل جديد")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: white;
                background: transparent;
            }
        """)
        text_layout.addWidget(title_label)

        subtitle_label = QLabel("نظام إدارة الحوالات المتقدم")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #ecf0f1;
                background: transparent;
            }
        """)
        text_layout.addWidget(subtitle_label)

        layout.addLayout(text_layout)
        layout.addStretch()

        # معلومات الطلب
        info_layout = QVBoxLayout()
        
        request_id_label = QLabel(f"رقم الطلب: {self.request_id[:8]}")
        request_id_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: white;
                background: rgba(255,255,255,0.2);
                padding: 5px 10px;
                border-radius: 5px;
            }
        """)
        info_layout.addWidget(request_id_label)

        date_label = QLabel(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        date_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: white;
                background: rgba(255,255,255,0.2);
                padding: 5px 10px;
                border-radius: 5px;
            }
        """)
        info_layout.addWidget(date_label)

        layout.addLayout(info_layout)

        return frame

    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # معلومات المرسل
        sender_group = self.create_sender_section()
        layout.addWidget(sender_group)

        # معلومات المستقبل
        receiver_group = self.create_receiver_section()
        layout.addWidget(receiver_group)

        return widget

    def create_sender_section(self):
        """إنشاء قسم معلومات المرسل"""
        group = QGroupBox("👤 معلومات المرسل")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #3498db;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)
        
        layout = QFormLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # اسم المرسل
        self.sender_name_input = QLineEdit()
        self.sender_name_input.setPlaceholderText("أدخل الاسم الكامل للمرسل...")
        layout.addRow("👤 الاسم الكامل:", self.sender_name_input)

        # رقم الهوية
        self.sender_id_input = QLineEdit()
        self.sender_id_input.setPlaceholderText("أدخل رقم الهوية أو الجواز...")
        layout.addRow("🆔 رقم الهوية:", self.sender_id_input)

        # رقم الهاتف
        self.sender_phone_input = QLineEdit()
        self.sender_phone_input.setPlaceholderText("أدخل رقم الهاتف مع رمز البلد...")
        layout.addRow("📱 رقم الهاتف:", self.sender_phone_input)

        # البريد الإلكتروني
        self.sender_email_input = QLineEdit()
        self.sender_email_input.setPlaceholderText("أدخل البريد الإلكتروني (اختياري)...")
        layout.addRow("📧 البريد الإلكتروني:", self.sender_email_input)

        # العنوان
        self.sender_address_input = QLineEdit()
        self.sender_address_input.setPlaceholderText("أدخل العنوان الكامل...")
        layout.addRow("📍 العنوان:", self.sender_address_input)

        # المدينة
        self.sender_city_input = QLineEdit()
        self.sender_city_input.setPlaceholderText("أدخل المدينة...")
        layout.addRow("🏙️ المدينة:", self.sender_city_input)

        return group

    def create_receiver_section(self):
        """إنشاء قسم معلومات المستقبل"""
        group = QGroupBox("👥 معلومات المستقبل")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #e74c3c;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)
        
        layout = QFormLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # اسم المستقبل
        self.receiver_name_input = QLineEdit()
        self.receiver_name_input.setPlaceholderText("أدخل الاسم الكامل للمستقبل...")
        layout.addRow("👤 الاسم الكامل:", self.receiver_name_input)

        # رقم الهوية
        self.receiver_id_input = QLineEdit()
        self.receiver_id_input.setPlaceholderText("أدخل رقم الهوية أو الجواز...")
        layout.addRow("🆔 رقم الهوية:", self.receiver_id_input)

        # رقم الهاتف
        self.receiver_phone_input = QLineEdit()
        self.receiver_phone_input.setPlaceholderText("أدخل رقم الهاتف مع رمز البلد...")
        layout.addRow("📱 رقم الهاتف:", self.receiver_phone_input)

        # البلد
        self.receiver_country_combo = QComboBox()
        self.receiver_country_combo.setEditable(True)
        self.receiver_country_combo.setPlaceholderText("اختر أو أدخل البلد...")
        layout.addRow("🌍 البلد:", self.receiver_country_combo)

        # المدينة
        self.receiver_city_input = QLineEdit()
        self.receiver_city_input.setPlaceholderText("أدخل المدينة...")
        layout.addRow("🏙️ المدينة:", self.receiver_city_input)

        # العنوان
        self.receiver_address_input = QLineEdit()
        self.receiver_address_input.setPlaceholderText("أدخل العنوان الكامل...")
        layout.addRow("📍 العنوان:", self.receiver_address_input)

        return group

    def create_transfer_details_tab(self):
        """إنشاء تبويب تفاصيل التحويل"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # تفاصيل المبلغ
        amount_group = self.create_amount_section()
        layout.addWidget(amount_group)

        # تفاصيل البنوك
        banks_group = self.create_banks_section()
        layout.addWidget(banks_group)

        # تفاصيل إضافية
        additional_group = self.create_additional_details_section()
        layout.addWidget(additional_group)

        layout.addStretch()

        return widget

    def create_amount_section(self):
        """إنشاء قسم تفاصيل المبلغ"""
        group = QGroupBox("💰 تفاصيل المبلغ والعملة")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #f39c12;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # المبلغ
        layout.addWidget(QLabel("💵 المبلغ المطلوب:"), 0, 0)
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(1.00, 999999999.99)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" ريال")
        self.amount_input.setMinimumWidth(200)
        layout.addWidget(self.amount_input, 0, 1)

        # العملة المرسلة
        layout.addWidget(QLabel("💱 العملة المرسلة:"), 0, 2)
        self.source_currency_combo = QComboBox()
        self.source_currency_combo.setMinimumWidth(150)
        layout.addWidget(self.source_currency_combo, 0, 3)

        # العملة المستقبلة
        layout.addWidget(QLabel("💱 العملة المستقبلة:"), 1, 0)
        self.target_currency_combo = QComboBox()
        self.target_currency_combo.setMinimumWidth(150)
        layout.addWidget(self.target_currency_combo, 1, 1)

        # سعر الصرف المتوقع
        layout.addWidget(QLabel("📈 سعر الصرف المتوقع:"), 1, 2)
        self.exchange_rate_input = QDoubleSpinBox()
        self.exchange_rate_input.setRange(0.0001, 9999.9999)
        self.exchange_rate_input.setDecimals(4)
        self.exchange_rate_input.setValue(1.0000)
        self.exchange_rate_input.setReadOnly(True)
        layout.addWidget(self.exchange_rate_input, 1, 3)

        return group

    def create_banks_section(self):
        """إنشاء قسم تفاصيل البنوك"""
        group = QGroupBox("🏦 تفاصيل البنوك والصرافات")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #9b59b6;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # البنك/الصراف المرسل
        layout.addWidget(QLabel("🏦 البنك/الصراف المرسل:"), 0, 0)
        self.sender_bank_combo = QComboBox()
        self.sender_bank_combo.setMinimumWidth(250)
        layout.addWidget(self.sender_bank_combo, 0, 1)

        # فرع البنك المرسل
        layout.addWidget(QLabel("🏢 فرع البنك المرسل:"), 0, 2)
        self.sender_branch_combo = QComboBox()
        self.sender_branch_combo.setMinimumWidth(200)
        layout.addWidget(self.sender_branch_combo, 0, 3)

        # البنك/الصراف المستقبل
        layout.addWidget(QLabel("🏪 البنك/الصراف المستقبل:"), 1, 0)
        self.receiver_bank_combo = QComboBox()
        self.receiver_bank_combo.setMinimumWidth(250)
        layout.addWidget(self.receiver_bank_combo, 1, 1)

        # فرع البنك المستقبل
        layout.addWidget(QLabel("🏢 فرع البنك المستقبل:"), 1, 2)
        self.receiver_branch_combo = QComboBox()
        self.receiver_branch_combo.setMinimumWidth(200)
        layout.addWidget(self.receiver_branch_combo, 1, 3)

        return group

    def create_additional_details_section(self):
        """إنشاء قسم التفاصيل الإضافية"""
        group = QGroupBox("⚙️ تفاصيل إضافية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #1abc9c;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # تاريخ التحويل المطلوب
        layout.addWidget(QLabel("📅 تاريخ التحويل المطلوب:"), 0, 0)
        self.transfer_date_input = QDateEdit()
        self.transfer_date_input.setDate(QDate.currentDate())
        self.transfer_date_input.setCalendarPopup(True)
        self.transfer_date_input.setMinimumWidth(150)
        layout.addWidget(self.transfer_date_input, 0, 1)

        # نوع التحويل
        layout.addWidget(QLabel("📋 نوع التحويل:"), 0, 2)
        self.transfer_type_combo = QComboBox()
        self.transfer_type_combo.addItems([
            "تحويل عادي",
            "تحويل سريع",
            "تحويل فوري",
            "تحويل مؤجل"
        ])
        self.transfer_type_combo.setMinimumWidth(150)
        layout.addWidget(self.transfer_type_combo, 0, 3)

        # أولوية التحويل
        layout.addWidget(QLabel("⭐ أولوية التحويل:"), 1, 0)
        self.priority_combo = QComboBox()
        self.priority_combo.addItems([
            "عادي",
            "مهم",
            "عاجل",
            "طارئ"
        ])
        layout.addWidget(self.priority_combo, 1, 1)

        # طريقة الاستلام
        layout.addWidget(QLabel("📦 طريقة الاستلام:"), 1, 2)
        self.delivery_method_combo = QComboBox()
        self.delivery_method_combo.addItems([
            "استلام من الفرع",
            "تحويل لحساب بنكي",
            "توصيل منزلي",
            "محفظة إلكترونية"
        ])
        layout.addWidget(self.delivery_method_combo, 1, 3)

        return group

    def create_notes_tab(self):
        """إنشاء تبويب الملاحظات والمرفقات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # ملاحظات عامة
        notes_group = QGroupBox("📝 ملاحظات عامة")
        notes_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #34495e;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)

        notes_layout = QVBoxLayout(notes_group)
        notes_layout.setContentsMargins(20, 25, 20, 20)

        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية حول طلب التحويل...")
        self.notes_input.setMaximumHeight(120)
        self.notes_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
                font-size: 12px;
                background-color: #fafafa;
            }
            QTextEdit:focus {
                border-color: #3498db;
                background-color: white;
            }
        """)
        notes_layout.addWidget(self.notes_input)

        layout.addWidget(notes_group)

        # خيارات إضافية
        options_group = QGroupBox("⚙️ خيارات إضافية")
        options_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #e67e22;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)

        options_layout = QVBoxLayout(options_group)
        options_layout.setContentsMargins(20, 25, 20, 20)

        # خيارات الإشعارات
        self.sms_notification_check = QCheckBox("📱 إرسال إشعار SMS للمستقبل")
        self.sms_notification_check.setChecked(True)
        options_layout.addWidget(self.sms_notification_check)

        self.email_notification_check = QCheckBox("📧 إرسال إشعار بريد إلكتروني")
        options_layout.addWidget(self.email_notification_check)

        self.auto_create_remittance_check = QCheckBox("🚀 إنشاء حوالة تلقائياً بعد الموافقة")
        self.auto_create_remittance_check.setChecked(True)
        options_layout.addWidget(self.auto_create_remittance_check)

        layout.addWidget(options_group)
        layout.addStretch()

        return widget

    def create_buttons_section(self):
        """إنشاء قسم الأزرار"""
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(0, 20, 0, 0)
        layout.addStretch()

        # زر إرسال الطلب
        self.send_request_btn = QPushButton("📤 إرسال طلب التحويل")
        self.send_request_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 200px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #1e8449);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e8449, stop:1 #196f3d);
            }
        """)
        layout.addWidget(self.send_request_btn)

        # زر إرسال لإنشاء حوالة
        self.send_to_remittance_btn = QPushButton("💸 إرسال لإنشاء حوالة")
        self.send_to_remittance_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 200px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #a93226, stop:1 #922b21);
            }
        """)
        layout.addWidget(self.send_to_remittance_btn)

        # زر حفظ كمسودة
        self.save_draft_btn = QPushButton("💾 حفظ كمسودة")
        self.save_draft_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f39c12, stop:1 #e67e22);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e67e22, stop:1 #d35400);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d35400, stop:1 #ba4a00);
            }
        """)
        layout.addWidget(self.save_draft_btn)

        # زر إلغاء
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #95a5a6, stop:1 #7f8c8d);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7f8c8d, stop:1 #6c7b7d);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6c7b7d, stop:1 #5d6d6e);
            }
        """)
        layout.addWidget(self.cancel_btn)

        return frame

    def apply_modern_style(self):
        """تطبيق الستايل العصري على النافذة"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: white;
                min-height: 20px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
                margin-right: 10px;
            }
            QCheckBox {
                font-size: 12px;
                color: #2c3e50;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #3498db;
                border-color: #3498db;
                image: none;
            }
            QLabel {
                color: #2c3e50;
                font-weight: 500;
            }
        """)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.send_request_btn.clicked.connect(self.send_transfer_request)
        self.send_to_remittance_btn.clicked.connect(self.send_to_remittance_window)
        self.save_draft_btn.clicked.connect(self.save_as_draft)
        self.cancel_btn.clicked.connect(self.reject)

        # ربط تغيير البنك بتحديث الفروع
        self.sender_bank_combo.currentTextChanged.connect(self.update_sender_branches)
        self.receiver_bank_combo.currentTextChanged.connect(self.update_receiver_branches)

        # ربط تغيير العملة بتحديث سعر الصرف
        self.source_currency_combo.currentTextChanged.connect(self.update_exchange_rate)
        self.target_currency_combo.currentTextChanged.connect(self.update_exchange_rate)

    def load_data(self):
        """تحميل البيانات الأساسية"""
        self.load_currencies()
        self.load_banks_and_exchanges()
        self.load_countries()

    def load_currencies(self):
        """تحميل العملات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT code, name FROM currencies WHERE is_active = 1 ORDER BY name")
            currencies = cursor.fetchall()

            # تنظيف القوائم
            self.source_currency_combo.clear()
            self.target_currency_combo.clear()

            # إضافة خيار افتراضي
            self.source_currency_combo.addItem("اختر العملة المرسلة...", None)
            self.target_currency_combo.addItem("اختر العملة المستقبلة...", None)

            # إضافة العملات
            for currency in currencies:
                display_text = f"{currency[1]} ({currency[0]})"
                self.source_currency_combo.addItem(display_text, currency[0])
                self.target_currency_combo.addItem(display_text, currency[0])

            # تعيين الريال اليمني كافتراضي للعملة المرسلة
            for i in range(self.source_currency_combo.count()):
                if self.source_currency_combo.itemData(i) == "YER":
                    self.source_currency_combo.setCurrentIndex(i)
                    break

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل العملات: {e}")

    def load_banks_and_exchanges(self):
        """تحميل البنوك والصرافات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل البنوك
            cursor.execute("SELECT id, name FROM banks WHERE is_active = 1 ORDER BY name")
            banks = cursor.fetchall()

            # تحميل الصرافات
            cursor.execute("SELECT id, name FROM exchanges WHERE is_active = 1 ORDER BY name")
            exchanges = cursor.fetchall()

            # تنظيف القوائم
            self.sender_bank_combo.clear()
            self.receiver_bank_combo.clear()

            # إضافة خيارات افتراضية
            self.sender_bank_combo.addItem("اختر البنك/الصراف المرسل...", None)
            self.receiver_bank_combo.addItem("اختر البنك/الصراف المستقبل...", None)

            # إضافة البنوك
            for bank in banks:
                display_text = f"🏦 {bank[1]}"
                bank_data = f"bank_{bank[0]}"
                self.sender_bank_combo.addItem(display_text, bank_data)
                self.receiver_bank_combo.addItem(display_text, bank_data)

            # إضافة الصرافات
            for exchange in exchanges:
                display_text = f"💱 {exchange[1]}"
                exchange_data = f"exchange_{exchange[0]}"
                self.sender_bank_combo.addItem(display_text, exchange_data)
                self.receiver_bank_combo.addItem(display_text, exchange_data)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل البنوك والصرافات: {e}")

    def load_countries(self):
        """تحميل قائمة البلدان"""
        countries = [
            "اليمن", "السعودية", "الإمارات", "قطر", "الكويت", "البحرين", "عمان",
            "الأردن", "لبنان", "سوريا", "العراق", "مصر", "السودان", "ليبيا",
            "المغرب", "الجزائر", "تونس", "موريتانيا", "الصومال", "جيبوتي",
            "أمريكا", "بريطانيا", "ألمانيا", "فرنسا", "إيطاليا", "هولندا",
            "السويد", "النرويج", "الدنمارك", "كندا", "أستراليا", "ماليزيا",
            "إندونيسيا", "تركيا", "الهند", "باكستان", "بنغلاديش", "الفلبين"
        ]

        self.receiver_country_combo.clear()
        self.receiver_country_combo.addItem("اختر البلد...")
        self.receiver_country_combo.addItems(countries)

    def update_sender_branches(self):
        """تحديث فروع البنك المرسل"""
        self.update_branches(self.sender_bank_combo, self.sender_branch_combo)

    def update_receiver_branches(self):
        """تحديث فروع البنك المستقبل"""
        self.update_branches(self.receiver_bank_combo, self.receiver_branch_combo)

    def update_branches(self, bank_combo, branch_combo):
        """تحديث قائمة الفروع حسب البنك المختار"""
        try:
            branch_combo.clear()
            branch_combo.addItem("اختر الفرع...", None)

            bank_data = bank_combo.currentData()
            if not bank_data:
                return

            # استخراج نوع ومعرف البنك
            bank_type, bank_id = bank_data.split('_')

            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            if bank_type == "bank":
                cursor.execute("SELECT id, name FROM branches WHERE bank_id = ? AND is_active = 1 ORDER BY name", (bank_id,))
            else:  # exchange
                cursor.execute("SELECT id, name FROM branches WHERE exchange_id = ? AND is_active = 1 ORDER BY name", (bank_id,))

            branches = cursor.fetchall()

            for branch in branches:
                branch_combo.addItem(f"🏢 {branch[1]}", branch[0])

            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث الفروع: {e}")

    def update_exchange_rate(self):
        """تحديث سعر الصرف"""
        try:
            source_currency = self.source_currency_combo.currentData()
            target_currency = self.target_currency_combo.currentData()

            if not source_currency or not target_currency:
                self.exchange_rate_input.setValue(1.0000)
                return

            if source_currency == target_currency:
                self.exchange_rate_input.setValue(1.0000)
                return

            # هنا يمكن إضافة منطق لجلب سعر الصرف من API أو قاعدة البيانات
            # للآن سنستخدم قيم افتراضية
            exchange_rates = {
                ("YER", "USD"): 0.004,
                ("USD", "YER"): 250.0,
                ("YER", "SAR"): 0.015,
                ("SAR", "YER"): 66.67,
                ("USD", "SAR"): 3.75,
                ("SAR", "USD"): 0.267
            }

            rate = exchange_rates.get((source_currency, target_currency), 1.0000)
            self.exchange_rate_input.setValue(rate)

        except Exception as e:
            print(f"خطأ في تحديث سعر الصرف: {e}")
            self.exchange_rate_input.setValue(1.0000)

    def validate_form(self):
        """التحقق من صحة النموذج"""
        # التحقق من المعلومات الأساسية
        if not self.sender_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المرسل")
            self.tab_widget.setCurrentIndex(0)
            self.sender_name_input.setFocus()
            return False

        if not self.sender_phone_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال رقم هاتف المرسل")
            self.tab_widget.setCurrentIndex(0)
            self.sender_phone_input.setFocus()
            return False

        if not self.receiver_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المستقبل")
            self.tab_widget.setCurrentIndex(0)
            self.receiver_name_input.setFocus()
            return False

        if not self.receiver_phone_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال رقم هاتف المستقبل")
            self.tab_widget.setCurrentIndex(0)
            self.receiver_phone_input.setFocus()
            return False

        # التحقق من تفاصيل التحويل
        if self.amount_input.value() <= 0:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال مبلغ التحويل")
            self.tab_widget.setCurrentIndex(1)
            self.amount_input.setFocus()
            return False

        if self.source_currency_combo.currentData() is None:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى اختيار العملة المرسلة")
            self.tab_widget.setCurrentIndex(1)
            self.source_currency_combo.setFocus()
            return False

        if self.target_currency_combo.currentData() is None:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى اختيار العملة المستقبلة")
            self.tab_widget.setCurrentIndex(1)
            self.target_currency_combo.setFocus()
            return False

        return True

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        return {
            'request_id': self.request_id,
            'sender_name': self.sender_name_input.text().strip(),
            'sender_id': self.sender_id_input.text().strip(),
            'sender_phone': self.sender_phone_input.text().strip(),
            'sender_email': self.sender_email_input.text().strip(),
            'sender_address': self.sender_address_input.text().strip(),
            'sender_city': self.sender_city_input.text().strip(),
            'receiver_name': self.receiver_name_input.text().strip(),
            'receiver_id': self.receiver_id_input.text().strip(),
            'receiver_phone': self.receiver_phone_input.text().strip(),
            'receiver_country': self.receiver_country_combo.currentText(),
            'receiver_city': self.receiver_city_input.text().strip(),
            'receiver_address': self.receiver_address_input.text().strip(),
            'amount': self.amount_input.value(),
            'source_currency': self.source_currency_combo.currentData(),
            'target_currency': self.target_currency_combo.currentData(),
            'exchange_rate': self.exchange_rate_input.value(),
            'sender_bank': self.sender_bank_combo.currentData(),
            'sender_branch': self.sender_branch_combo.currentData(),
            'receiver_bank': self.receiver_bank_combo.currentData(),
            'receiver_branch': self.receiver_branch_combo.currentData(),
            'transfer_date': self.transfer_date_input.date().toString("yyyy-MM-dd"),
            'transfer_type': self.transfer_type_combo.currentText(),
            'priority': self.priority_combo.currentText(),
            'delivery_method': self.delivery_method_combo.currentText(),
            'notes': self.notes_input.toPlainText().strip(),
            'sms_notification': self.sms_notification_check.isChecked(),
            'email_notification': self.email_notification_check.isChecked(),
            'auto_create_remittance': self.auto_create_remittance_check.isChecked(),
            'created_at': datetime.now().isoformat(),
            'status': 'pending'
        }

    def send_transfer_request(self):
        """إرسال طلب التحويل"""
        if not self.validate_form():
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.send_request_btn.setEnabled(False)

            # جمع البيانات
            request_data = self.collect_form_data()

            # حفظ الطلب في قاعدة البيانات
            request_id = self.save_request_to_database(request_data)

            if request_id:
                # إضافة معرف الطلب للبيانات
                request_data['id'] = request_id

                QMessageBox.information(self, "نجح الإرسال",
                                      f"تم إرسال طلب التحويل بنجاح!\n\n"
                                      f"رقم الطلب: {self.request_id[:8]}\n"
                                      f"المرسل: {request_data['sender_name']}\n"
                                      f"المستقبل: {request_data['receiver_name']}\n"
                                      f"المبلغ: {request_data['amount']} {request_data['source_currency']}")

                # إرسال إشارة مع بيانات الطلب
                self.transfer_request_created.emit(request_data)
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إرسال طلب التحويل")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إرسال الطلب:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.send_request_btn.setEnabled(True)

    def send_to_remittance_window(self):
        """إرسال البيانات لنافذة إنشاء الحوالة"""
        if not self.validate_form():
            return

        try:
            # جمع البيانات
            request_data = self.collect_form_data()

            # إرسال إشارة لفتح نافذة إنشاء الحوالة
            self.send_to_remittance.emit(request_data)

            QMessageBox.information(self, "تم الإرسال",
                                  "تم إرسال البيانات لنافذة إنشاء الحوالة بنجاح!")

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إرسال البيانات:\n{str(e)}")

    def save_as_draft(self):
        """حفظ الطلب كمسودة"""
        try:
            # جمع البيانات
            request_data = self.collect_form_data()
            request_data['status'] = 'draft'

            # حفظ المسودة
            draft_id = self.save_request_to_database(request_data)

            if draft_id:
                QMessageBox.information(self, "تم الحفظ",
                                      f"تم حفظ المسودة بنجاح!\nرقم المسودة: {self.request_id[:8]}")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ المسودة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ المسودة:\n{str(e)}")

    def save_request_to_database(self, data):
        """حفظ الطلب في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء جدول طلبات التحويل إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS transfer_requests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    request_id TEXT UNIQUE NOT NULL,
                    sender_name TEXT NOT NULL,
                    sender_id TEXT,
                    sender_phone TEXT NOT NULL,
                    sender_email TEXT,
                    sender_address TEXT,
                    sender_city TEXT,
                    receiver_name TEXT NOT NULL,
                    receiver_id TEXT,
                    receiver_phone TEXT NOT NULL,
                    receiver_country TEXT,
                    receiver_city TEXT,
                    receiver_address TEXT,
                    amount DECIMAL(15,2) NOT NULL,
                    source_currency TEXT NOT NULL,
                    target_currency TEXT NOT NULL,
                    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
                    sender_bank TEXT,
                    sender_branch TEXT,
                    receiver_bank TEXT,
                    receiver_branch TEXT,
                    transfer_date DATE,
                    transfer_type TEXT,
                    priority TEXT,
                    delivery_method TEXT,
                    notes TEXT,
                    sms_notification BOOLEAN DEFAULT 1,
                    email_notification BOOLEAN DEFAULT 0,
                    auto_create_remittance BOOLEAN DEFAULT 1,
                    status TEXT DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # إدراج الطلب
            cursor.execute("""
                INSERT INTO transfer_requests (
                    request_id, sender_name, sender_id, sender_phone, sender_email, sender_address, sender_city,
                    receiver_name, receiver_id, receiver_phone, receiver_country, receiver_city, receiver_address,
                    amount, source_currency, target_currency, exchange_rate,
                    sender_bank, sender_branch, receiver_bank, receiver_branch,
                    transfer_date, transfer_type, priority, delivery_method, notes,
                    sms_notification, email_notification, auto_create_remittance, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['request_id'], data['sender_name'], data['sender_id'], data['sender_phone'],
                data['sender_email'], data['sender_address'], data['sender_city'],
                data['receiver_name'], data['receiver_id'], data['receiver_phone'],
                data['receiver_country'], data['receiver_city'], data['receiver_address'],
                data['amount'], data['source_currency'], data['target_currency'], data['exchange_rate'],
                data['sender_bank'], data['sender_branch'], data['receiver_bank'], data['receiver_branch'],
                data['transfer_date'], data['transfer_type'], data['priority'], data['delivery_method'], data['notes'],
                data['sms_notification'], data['email_notification'], data['auto_create_remittance'],
                data['status'], data['created_at']
            ))

            request_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return request_id

        except Exception as e:
            print(f"خطأ في حفظ الطلب: {e}")
            return None
