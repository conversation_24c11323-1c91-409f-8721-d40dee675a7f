# -*- coding: utf-8 -*-
"""
نافذة البحث عن الأصناف
Item Search Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QMessageBox, QHeaderView, QAbstractItemView)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QKeySequence, QShortcut

from ...database.database_manager import DatabaseManager
from ...database.models import Item, ItemGroup, UnitOfMeasure

class ItemSearchDialog(QDialog):
    """نافذة البحث عن الأصناف"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.selected_item = None
        self.setup_ui()
        self.setup_connections()
        self.load_items()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("البحث عن صنف")
        self.setModal(True)
        self.resize(900, 600)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # منطقة البحث
        search_layout = QHBoxLayout()
        
        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالكود أو الاسم...")
        search_layout.addWidget(self.search_edit)
        
        self.search_button = QPushButton("بحث")
        search_layout.addWidget(self.search_button)
        
        self.clear_button = QPushButton("مسح")
        search_layout.addWidget(self.clear_button)
        
        main_layout.addLayout(search_layout)
        
        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(8)
        self.items_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "المجموعة", "وحدة القياس", 
            "سعر التكلفة", "سعر البيع", "الوزن", "نشط"
        ])
        
        # إعداد الجدول
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # الكود
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # الاسم
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # المجموعة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # وحدة القياس
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # سعر التكلفة
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # سعر البيع
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الوزن
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # نشط
        
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setAlternatingRowColors(True)
        
        main_layout.addWidget(self.items_table)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.select_button = QPushButton("اختيار")
        self.select_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.select_button.setEnabled(False)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.select_button)
        buttons_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(buttons_layout)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.search_button.clicked.connect(self.search_items)
        self.clear_button.clicked.connect(self.clear_search)
        self.select_button.clicked.connect(self.select_item)
        self.cancel_button.clicked.connect(self.reject)
        
        # البحث عند الضغط على Enter
        self.search_edit.returnPressed.connect(self.search_items)
        
        # تفعيل زر الاختيار عند تحديد صف
        self.items_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        # اختيار الصنف عند النقر المزدوج
        self.items_table.itemDoubleClicked.connect(self.select_item)
        
    def load_items(self):
        """تحميل جميع الأصناف"""
        session = self.db_manager.get_session()
        try:
            items = session.query(Item).join(ItemGroup, isouter=True).join(
                UnitOfMeasure, isouter=True
            ).filter(Item.is_active == True).all()
            
            self.populate_table(items)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الأصناف: {str(e)}")
        finally:
            session.close()
            
    def search_items(self):
        """البحث عن الأصناف"""
        search_text = self.search_edit.text().strip()
        if not search_text:
            self.load_items()
            return
            
        session = self.db_manager.get_session()
        try:
            items = session.query(Item).join(ItemGroup, isouter=True).join(
                UnitOfMeasure, isouter=True
            ).filter(
                Item.is_active == True,
                (Item.code.contains(search_text) | 
                 Item.name.contains(search_text) |
                 Item.name_en.contains(search_text))
            ).all()
            
            self.populate_table(items)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث: {str(e)}")
        finally:
            session.close()
            
    def clear_search(self):
        """مسح البحث"""
        self.search_edit.clear()
        self.load_items()
        
    def populate_table(self, items):
        """ملء الجدول بالأصناف"""
        self.items_table.setRowCount(len(items))
        
        for row, item in enumerate(items):
            # الكود
            code_item = QTableWidgetItem(item.code or "")
            code_item.setData(Qt.UserRole, item.id)
            self.items_table.setItem(row, 0, code_item)
            
            # الاسم
            name_item = QTableWidgetItem(item.name or "")
            self.items_table.setItem(row, 1, name_item)
            
            # المجموعة
            group_name = item.group.name if item.group else ""
            group_item = QTableWidgetItem(group_name)
            self.items_table.setItem(row, 2, group_item)
            
            # وحدة القياس
            unit_name = item.unit.name if item.unit else ""
            unit_item = QTableWidgetItem(unit_name)
            self.items_table.setItem(row, 3, unit_item)
            
            # سعر التكلفة
            cost_price_item = QTableWidgetItem(f"{item.cost_price:.2f}" if item.cost_price else "0.00")
            self.items_table.setItem(row, 4, cost_price_item)
            
            # سعر البيع
            selling_price_item = QTableWidgetItem(f"{item.selling_price:.2f}" if item.selling_price else "0.00")
            self.items_table.setItem(row, 5, selling_price_item)
            
            # الوزن
            weight_item = QTableWidgetItem(f"{item.weight:.2f}" if item.weight else "")
            self.items_table.setItem(row, 6, weight_item)
            
            # نشط
            active_item = QTableWidgetItem("نعم" if item.is_active else "لا")
            self.items_table.setItem(row, 7, active_item)
            
    def on_selection_changed(self):
        """عند تغيير التحديد"""
        has_selection = len(self.items_table.selectedItems()) > 0
        self.select_button.setEnabled(has_selection)
        
    def select_item(self):
        """اختيار الصنف"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            item_id = self.items_table.item(current_row, 0).data(Qt.UserRole)
            
            # الحصول على بيانات الصنف
            session = self.db_manager.get_session()
            try:
                self.selected_item = session.query(Item).filter(
                    Item.id == item_id
                ).first()
                
                if self.selected_item:
                    self.accept()
                else:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على الصنف")
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في اختيار الصنف: {str(e)}")
            finally:
                session.close()
        else:
            QMessageBox.warning(self, "تحذير", "يجب اختيار صنف أولاً")
            
    def get_selected_item(self):
        """الحصول على الصنف المختار"""
        return self.selected_item
