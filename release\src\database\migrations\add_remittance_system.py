# -*- coding: utf-8 -*-
"""
إضافة نظام إدارة الحوالات المتقدم
Add Advanced Remittance Management System
"""

import sqlite3
import os
from pathlib import Path

def create_remittance_system_tables():
    """إنشاء جداول نظام الحوالات"""

    # الحصول على مسار قاعدة البيانات
    db_path = Path(__file__).parent.parent.parent / "data" / "proshipment.db"

    # إنشاء الاتصال بقاعدة البيانات
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    try:
        # جدول البنوك
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS banks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(200) NOT NULL,
                name_en VARCHAR(200),
                swift_code VARCHAR(20),
                country VARCHAR(100),
                city VARCHAR(100),
                address TEXT,
                phone VARCHAR(50),
                email VARCHAR(100),
                website VARCHAR(200),
                bank_type VARCHAR(50),
                is_active BOOLEAN DEFAULT 1,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول حسابات البنوك
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS bank_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bank_id INTEGER NOT NULL,
                account_number VARCHAR(50) NOT NULL,
                account_name VARCHAR(200) NOT NULL,
                account_type VARCHAR(50),
                currency_id INTEGER NOT NULL,
                iban VARCHAR(50),
                balance REAL DEFAULT 0.0,
                available_balance REAL DEFAULT 0.0,
                credit_limit REAL DEFAULT 0.0,
                is_active BOOLEAN DEFAULT 1,
                is_default BOOLEAN DEFAULT 0,
                allow_overdraft BOOLEAN DEFAULT 0,
                branch_name VARCHAR(200),
                contact_person VARCHAR(100),
                contact_phone VARCHAR(50),
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bank_id) REFERENCES banks (id),
                FOREIGN KEY (currency_id) REFERENCES currencies (id)
            )
        """))
        
        # جدول حالات الحوالات
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS remittance_statuses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                name_en VARCHAR(100),
                description TEXT,
                color VARCHAR(7),
                icon VARCHAR(50),
                order_index INTEGER DEFAULT 0,
                is_final BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """))
        
        # جدول الحوالات
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS remittances (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                remittance_number VARCHAR(50) UNIQUE NOT NULL,
                reference_number VARCHAR(100),
                sender_name VARCHAR(200) NOT NULL,
                sender_id_number VARCHAR(50),
                sender_phone VARCHAR(50),
                sender_address TEXT,
                receiver_name VARCHAR(200) NOT NULL,
                receiver_id_number VARCHAR(50),
                receiver_phone VARCHAR(50),
                receiver_address TEXT,
                supplier_id INTEGER NOT NULL,
                amount REAL NOT NULL,
                currency_id INTEGER NOT NULL,
                exchange_rate REAL DEFAULT 1.0,
                amount_in_base_currency REAL,
                transfer_fee REAL DEFAULT 0.0,
                bank_charges REAL DEFAULT 0.0,
                other_charges REAL DEFAULT 0.0,
                total_charges REAL DEFAULT 0.0,
                net_amount REAL,
                sender_bank_id INTEGER,
                sender_account_id INTEGER,
                receiver_bank_id INTEGER,
                receiver_account_id INTEGER,
                status_id INTEGER,
                current_status VARCHAR(50) DEFAULT 'مسودة',
                remittance_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                value_date DATETIME,
                expected_completion_date DATETIME,
                actual_completion_date DATETIME,
                purpose VARCHAR(200),
                notes TEXT,
                internal_notes TEXT,
                is_active BOOLEAN DEFAULT 1,
                is_locked BOOLEAN DEFAULT 0,
                created_by INTEGER,
                approved_by INTEGER,
                approved_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (currency_id) REFERENCES currencies (id),
                FOREIGN KEY (sender_bank_id) REFERENCES banks (id),
                FOREIGN KEY (receiver_bank_id) REFERENCES banks (id),
                FOREIGN KEY (sender_account_id) REFERENCES bank_accounts (id),
                FOREIGN KEY (receiver_account_id) REFERENCES bank_accounts (id),
                FOREIGN KEY (status_id) REFERENCES remittance_statuses (id),
                FOREIGN KEY (created_by) REFERENCES users (id),
                FOREIGN KEY (approved_by) REFERENCES users (id)
            )
        """))
        
        # جدول تاريخ حالات الحوالات
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS remittance_status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                remittance_id INTEGER NOT NULL,
                status_id INTEGER,
                old_status VARCHAR(50),
                new_status VARCHAR(50) NOT NULL,
                change_reason TEXT,
                notes TEXT,
                changed_by INTEGER NOT NULL,
                changed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (remittance_id) REFERENCES remittances (id),
                FOREIGN KEY (status_id) REFERENCES remittance_statuses (id),
                FOREIGN KEY (changed_by) REFERENCES users (id)
            )
        """))
        
        # جدول حسابات الموردين
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS supplier_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_id INTEGER NOT NULL,
                currency_id INTEGER NOT NULL,
                account_number VARCHAR(50),
                opening_balance REAL DEFAULT 0.0,
                current_balance REAL DEFAULT 0.0,
                available_balance REAL DEFAULT 0.0,
                blocked_balance REAL DEFAULT 0.0,
                credit_limit REAL DEFAULT 0.0,
                used_credit REAL DEFAULT 0.0,
                available_credit REAL DEFAULT 0.0,
                is_active BOOLEAN DEFAULT 1,
                is_blocked BOOLEAN DEFAULT 0,
                block_reason TEXT,
                last_transaction_date DATETIME,
                last_balance_update DATETIME,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (currency_id) REFERENCES currencies (id),
                UNIQUE(supplier_id, currency_id)
            )
        """))
        
        # جدول معاملات حسابات الموردين
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS supplier_account_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id INTEGER NOT NULL,
                remittance_id INTEGER,
                transaction_number VARCHAR(50) UNIQUE NOT NULL,
                transaction_type VARCHAR(50) NOT NULL,
                transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                value_date DATETIME,
                debit_amount REAL DEFAULT 0.0,
                credit_amount REAL DEFAULT 0.0,
                balance_before REAL,
                balance_after REAL,
                description TEXT NOT NULL,
                reference_number VARCHAR(100),
                notes TEXT,
                is_reversed BOOLEAN DEFAULT 0,
                reversed_by INTEGER,
                reversed_at DATETIME,
                reversal_reason TEXT,
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES supplier_accounts (id),
                FOREIGN KEY (remittance_id) REFERENCES remittances (id),
                FOREIGN KEY (created_by) REFERENCES users (id),
                FOREIGN KEY (reversed_by) REFERENCES users (id)
            )
        """))
        
        # جدول مستندات الحوالات
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS remittance_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                remittance_id INTEGER NOT NULL,
                document_name VARCHAR(200) NOT NULL,
                document_type VARCHAR(100),
                file_path VARCHAR(500),
                file_size INTEGER,
                file_extension VARCHAR(10),
                mime_type VARCHAR(100),
                description TEXT,
                is_required BOOLEAN DEFAULT 0,
                is_verified BOOLEAN DEFAULT 0,
                verified_by INTEGER,
                verified_at DATETIME,
                uploaded_by INTEGER NOT NULL,
                uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (remittance_id) REFERENCES remittances (id),
                FOREIGN KEY (uploaded_by) REFERENCES users (id),
                FOREIGN KEY (verified_by) REFERENCES users (id)
            )
        """))
        
        # جدول تقارير الحوالات
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS remittance_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_name VARCHAR(200) NOT NULL,
                report_type VARCHAR(50) NOT NULL,
                date_from DATETIME,
                date_to DATETIME,
                supplier_id INTEGER,
                currency_id INTEGER,
                total_remittances INTEGER DEFAULT 0,
                total_amount REAL DEFAULT 0.0,
                total_charges REAL DEFAULT 0.0,
                net_amount REAL DEFAULT 0.0,
                pending_count INTEGER DEFAULT 0,
                completed_count INTEGER DEFAULT 0,
                cancelled_count INTEGER DEFAULT 0,
                report_data TEXT,
                file_path VARCHAR(500),
                generated_by INTEGER NOT NULL,
                generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (currency_id) REFERENCES currencies (id),
                FOREIGN KEY (generated_by) REFERENCES users (id)
            )
        """))
        
        conn.commit()
        print("✅ تم إنشاء جداول نظام الحوالات بنجاح")

def insert_default_data():
    """إدراج البيانات الافتراضية"""
    
    db_manager = DatabaseManager()
    engine = db_manager.get_engine()
    
    with engine.connect() as conn:
        
        # إدراج حالات الحوالات الافتراضية
        default_statuses = [
            ('DRAFT', 'مسودة', 'Draft', 'حوالة في مرحلة الإعداد', '#6b7280', 'edit', 1, 0),
            ('PENDING', 'معلقة', 'Pending', 'في انتظار المراجعة', '#f59e0b', 'clock', 2, 0),
            ('APPROVED', 'معتمدة', 'Approved', 'تم اعتماد الحوالة', '#3b82f6', 'check', 3, 0),
            ('PROCESSING', 'قيد المعالجة', 'Processing', 'جاري معالجة الحوالة', '#8b5cf6', 'refresh', 4, 0),
            ('SENT', 'مرسلة', 'Sent', 'تم إرسال الحوالة', '#06b6d4', 'send', 5, 0),
            ('COMPLETED', 'مكتملة', 'Completed', 'تم إكمال الحوالة بنجاح', '#10b981', 'check-circle', 6, 1),
            ('CANCELLED', 'ملغية', 'Cancelled', 'تم إلغاء الحوالة', '#ef4444', 'x-circle', 7, 1),
            ('FAILED', 'فاشلة', 'Failed', 'فشل في معالجة الحوالة', '#dc2626', 'alert-circle', 8, 1)
        ]
        
        for status in default_statuses:
            conn.execute(text("""
                INSERT OR IGNORE INTO remittance_statuses 
                (code, name, name_en, description, color, icon, order_index, is_final)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """), status)
        
        conn.commit()
        print("✅ تم إدراج البيانات الافتراضية لنظام الحوالات")

if __name__ == "__main__":
    create_remittance_system_tables()
    insert_default_data()
