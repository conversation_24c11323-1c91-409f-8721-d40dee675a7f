# -*- coding: utf-8 -*-
"""
نافذة إنشاء حوالة جديدة
New Remittance Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QDateEdit, QTextEdit,
                               QPushButton, QGroupBox, QDoubleSpinBox, QSpinBox,
                               QTabWidget, QWidget, QFrame, QMessageBox,
                               QCompleter, QCheckBox, QProgressBar, QTableWidget,
                               QTableWidgetItem, QHeaderView, QAbstractItemView)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QRegularExpression
from PySide6.QtGui import QFont, QRegularExpressionValidator, QDoubleValidator, QColor

import sqlite3
from pathlib import Path
from datetime import datetime
import uuid

from ...utils.arabic_support import reshape_arabic_text

class NewRemittanceDialog(QDialog):
    """نافذة إنشاء حوالة جديدة"""
    
    remittance_created = Signal(int)  # إشارة عند إنشاء حوالة جديدة
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إنشاء حوالة جديدة - ProShipment")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 1000)
        self.setModal(True)
        
        # متغيرات النافذة
        self.suppliers_data = []
        self.currencies_data = []
        self.banks_data = []
        self.bank_accounts_data = []

        # متغيرات الموردين المتعددين
        self.multiple_suppliers = []  # قائمة الموردين في الحوالة
        self.suppliers_total_amount = 0.0  # إجمالي مبلغ جميع الموردين
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_validators()
        self.setup_connections()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
        self.generate_remittance_number()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("إنشاء حوالة جديدة")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                background-color: #f1f5f9;
                padding: 15px;
                border-radius: 8px;
                border: 1px solid #cbd5e1;
            }
        """)
        layout.addWidget(title_label)
        
        # التبويبات الرئيسية
        self.tabs = QTabWidget()
        
        # تبويب معلومات الحوالة
        remittance_tab = self.create_remittance_info_tab()
        self.tabs.addTab(remittance_tab, "معلومات الحوالة")
        
        # تبويب البيانات المالية
        financial_tab = self.create_financial_tab()
        self.tabs.addTab(financial_tab, "البيانات المالية")
        
        # تبويب معلومات البنوك
        banks_tab = self.create_banks_tab()
        self.tabs.addTab(banks_tab, "معلومات البنوك")
        
        # تبويب الملاحظات والتفاصيل
        notes_tab = self.create_notes_tab()
        self.tabs.addTab(notes_tab, "الملاحظات والتفاصيل")

        # تبويب الموردين المتعددين (تم دمجه في التبويب الأول)
        # suppliers_tab = self.create_multiple_suppliers_tab()
        # self.tabs.addTab(suppliers_tab, "الموردين المتعددين")

        layout.addWidget(self.tabs)
        
        # أزرار الإجراءات
        self.create_action_buttons(layout)
        
    def create_remittance_info_tab(self):
        """إنشاء تبويب معلومات الحوالة المحسن"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)

        # القسم الأول: معلومات الحوالة الأساسية
        basic_group = QGroupBox("معلومات الحوالة الأساسية")
        basic_layout = QGridLayout(basic_group)
        basic_layout.setSpacing(15)

        # التاريخ
        basic_layout.addWidget(QLabel("التاريخ:"), 0, 0)
        self.remittance_date = QDateEdit()
        self.remittance_date.setDate(QDate.currentDate())
        self.remittance_date.setCalendarPopup(True)
        self.remittance_date.setMinimumWidth(150)
        basic_layout.addWidget(self.remittance_date, 0, 1)

        # رقم الحوالة
        basic_layout.addWidget(QLabel("رقم الحوالة:"), 0, 2)
        self.remittance_number_input = QLineEdit()
        self.remittance_number_input.setReadOnly(True)
        self.remittance_number_input.setStyleSheet("background-color: #f0f0f0;")
        basic_layout.addWidget(self.remittance_number_input, 0, 3)

        # جهة التحويل
        basic_layout.addWidget(QLabel("جهة التحويل:"), 1, 0)
        self.transfer_entity_combo = QComboBox()
        self.transfer_entity_combo.addItems(["بنك", "صراف"])
        self.transfer_entity_combo.setMinimumWidth(150)
        basic_layout.addWidget(self.transfer_entity_combo, 1, 1)

        # اسم جهة التحويل (متغير حسب النوع)
        self.transfer_entity_name_label = QLabel("اسم البنك:")
        basic_layout.addWidget(self.transfer_entity_name_label, 1, 2)
        self.transfer_entity_name_combo = QComboBox()
        self.transfer_entity_name_combo.setEditable(True)
        basic_layout.addWidget(self.transfer_entity_name_combo, 1, 3)

        # الرقم المرجعي
        basic_layout.addWidget(QLabel("الرقم المرجعي:"), 2, 0)
        self.reference_number_input = QLineEdit()
        self.reference_number_input.setPlaceholderText("أدخل الرقم المرجعي")
        basic_layout.addWidget(self.reference_number_input, 2, 1)

        # العملة
        basic_layout.addWidget(QLabel("العملة:"), 2, 2)
        self.currency_combo = QComboBox()
        basic_layout.addWidget(self.currency_combo, 2, 3)

        # المبلغ
        basic_layout.addWidget(QLabel("المبلغ:"), 3, 0)
        self.amount_input = QLineEdit()
        self.amount_input.setPlaceholderText("0.00")
        basic_layout.addWidget(self.amount_input, 3, 1)

        # حالة الحوالة
        basic_layout.addWidget(QLabel("حالة الحوالة:"), 3, 2)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["مسودة", "معلقة", "قيد التنفيذ", "مكتملة", "ملغية"])
        self.status_combo.setCurrentText("مسودة")
        basic_layout.addWidget(self.status_combo, 3, 3)

        # ملاحظات
        basic_layout.addWidget(QLabel("ملاحظات:"), 4, 0)
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        basic_layout.addWidget(self.notes_input, 4, 1, 1, 3)

        layout.addWidget(basic_group)

        # القسم الثاني: الموردين المحول لهم
        suppliers_group = QGroupBox("الموردين المحول لهم")
        suppliers_layout = QVBoxLayout(suppliers_group)

        # أدوات إدارة الموردين
        suppliers_tools_layout = QHBoxLayout()

        add_supplier_btn = QPushButton("➕ إضافة مورد")
        add_supplier_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #059669; }
        """)
        add_supplier_btn.clicked.connect(self.add_supplier_to_remittance)
        suppliers_tools_layout.addWidget(add_supplier_btn)

        remove_supplier_btn = QPushButton("➖ حذف مورد")
        remove_supplier_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #dc2626; }
        """)
        remove_supplier_btn.clicked.connect(self.remove_supplier_from_remittance)
        suppliers_tools_layout.addWidget(remove_supplier_btn)

        suppliers_tools_layout.addStretch()

        # عرض إجمالي المبلغ الموزع
        self.distributed_amount_label = QLabel("إجمالي المبلغ الموزع: 0.00")
        self.distributed_amount_label.setStyleSheet("font-weight: bold; color: #1f2937;")
        suppliers_tools_layout.addWidget(self.distributed_amount_label)

        suppliers_layout.addLayout(suppliers_tools_layout)

        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.setup_suppliers_table()
        suppliers_layout.addWidget(self.suppliers_table)

        layout.addWidget(suppliers_group)
        layout.addStretch()

        return tab

    def setup_suppliers_table(self):
        """إعداد جدول الموردين"""
        headers = ["المورد", "المبلغ", "العملة", "الوصف", "الحالة"]
        self.suppliers_table.setColumnCount(len(headers))
        self.suppliers_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setMaximumHeight(200)

        # تعديل عرض الأعمدة
        header = self.suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def add_supplier_to_remittance(self):
        """إضافة مورد للحوالة"""
        from .add_supplier_to_remittance_dialog import AddSupplierToRemittanceDialog

        dialog = AddSupplierToRemittanceDialog(self)
        if dialog.exec() == QDialog.Accepted:
            supplier_data = dialog.get_supplier_data()
            self.add_supplier_row(supplier_data)
            self.update_distributed_amount()

    def remove_supplier_from_remittance(self):
        """حذف مورد من الحوالة"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا المورد من الحوالة؟",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.suppliers_table.removeRow(current_row)
                self.update_distributed_amount()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد لحذفه")

    def add_supplier_row(self, supplier_data):
        """إضافة صف مورد للجدول"""
        row = self.suppliers_table.rowCount()
        self.suppliers_table.insertRow(row)

        self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier_data['name']))
        self.suppliers_table.setItem(row, 1, QTableWidgetItem(f"{supplier_data['amount']:.2f}"))
        self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier_data['currency']))
        self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier_data.get('description', '')))
        self.suppliers_table.setItem(row, 4, QTableWidgetItem("معلق"))

    def update_distributed_amount(self):
        """تحديث إجمالي المبلغ الموزع"""
        total = 0.0
        for row in range(self.suppliers_table.rowCount()):
            amount_item = self.suppliers_table.item(row, 1)
            if amount_item:
                try:
                    total += float(amount_item.text())
                except ValueError:
                    pass

        self.distributed_amount_label.setText(f"إجمالي المبلغ الموزع: {total:.2f}")

    def update_transfer_entity_name(self):
        """تحديث اسم جهة التحويل حسب النوع"""
        entity_type = self.transfer_entity_combo.currentText()

        if entity_type == "بنك":
            self.transfer_entity_name_label.setText("اسم البنك:")
            self.load_banks_data()
        elif entity_type == "صراف":
            self.transfer_entity_name_label.setText("اسم الصراف:")
            self.load_exchange_offices_data()

    def load_banks_data(self):
        """تحميل بيانات البنوك"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT id, name FROM banks WHERE is_active = 1 ORDER BY name")
            banks = cursor.fetchall()

            self.transfer_entity_name_combo.clear()
            for bank in banks:
                self.transfer_entity_name_combo.addItem(bank[1], bank[0])

            conn.close()
        except Exception as e:
            print(f"خطأ في تحميل البنوك: {e}")

    def load_exchange_offices_data(self):
        """تحميل بيانات الصرافين"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT id, name FROM exchange_offices WHERE is_active = 1 ORDER BY name")
            offices = cursor.fetchall()

            self.transfer_entity_name_combo.clear()
            for office in offices:
                self.transfer_entity_name_combo.addItem(office[1], office[0])

            conn.close()
        except Exception as e:
            print(f"خطأ في تحميل الصرافين: {e}")

    def create_financial_tab(self):
        """إنشاء تبويب البيانات المالية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # المبلغ والعملة
        amount_group = QGroupBox("المبلغ والعملة")
        amount_layout = QGridLayout(amount_group)
        amount_layout.setSpacing(10)
        
        # مبلغ الحوالة (تم نقله للتبويب الأول)
        # amount_layout.addWidget(QLabel("مبلغ الحوالة: *"), 0, 0)
        # self.amount_input_financial = QDoubleSpinBox()
        # self.amount_input_financial.setRange(0.01, 999999999.99)
        # self.amount_input_financial.setDecimals(2)
        # self.amount_input_financial.setSuffix(" ")
        # self.amount_input_financial.setMinimumWidth(150)
        # amount_layout.addWidget(self.amount_input_financial, 0, 1)
        
        # العملة
        amount_layout.addWidget(QLabel("العملة: *"), 0, 2)
        self.currency_combo = QComboBox()
        self.currency_combo.setPlaceholderText("اختر العملة...")
        amount_layout.addWidget(self.currency_combo, 0, 3)
        
        # سعر الصرف
        amount_layout.addWidget(QLabel("سعر الصرف:"), 1, 0)
        self.exchange_rate_input = QDoubleSpinBox()
        self.exchange_rate_input.setRange(0.0001, 9999.9999)
        self.exchange_rate_input.setDecimals(4)
        self.exchange_rate_input.setValue(1.0000)
        amount_layout.addWidget(self.exchange_rate_input, 1, 1)
        
        # المبلغ بالعملة الأساسية
        amount_layout.addWidget(QLabel("المبلغ بالعملة الأساسية:"), 1, 2)
        self.base_amount_label = QLabel("0.00 ريال سعودي")
        self.base_amount_label.setStyleSheet("""
            QLabel {
                background-color: #f0f9ff;
                padding: 8px;
                border-radius: 4px;
                border: 1px solid #0ea5e9;
                font-weight: bold;
            }
        """)
        amount_layout.addWidget(self.base_amount_label, 1, 3)
        
        layout.addWidget(amount_group)
        
        # الرسوم والتكاليف
        charges_group = QGroupBox("الرسوم والتكاليف")
        charges_layout = QGridLayout(charges_group)
        charges_layout.setSpacing(10)
        
        # رسوم التحويل
        charges_layout.addWidget(QLabel("رسوم التحويل:"), 0, 0)
        self.transfer_fee_input = QDoubleSpinBox()
        self.transfer_fee_input.setRange(0.00, 99999.99)
        self.transfer_fee_input.setDecimals(2)
        charges_layout.addWidget(self.transfer_fee_input, 0, 1)
        
        # رسوم البنك
        charges_layout.addWidget(QLabel("رسوم البنك:"), 0, 2)
        self.bank_charges_input = QDoubleSpinBox()
        self.bank_charges_input.setRange(0.00, 99999.99)
        self.bank_charges_input.setDecimals(2)
        charges_layout.addWidget(self.bank_charges_input, 0, 3)
        
        # رسوم أخرى
        charges_layout.addWidget(QLabel("رسوم أخرى:"), 1, 0)
        self.other_charges_input = QDoubleSpinBox()
        self.other_charges_input.setRange(0.00, 99999.99)
        self.other_charges_input.setDecimals(2)
        charges_layout.addWidget(self.other_charges_input, 1, 1)
        
        # إجمالي الرسوم
        charges_layout.addWidget(QLabel("إجمالي الرسوم:"), 1, 2)
        self.total_charges_label = QLabel("0.00")
        self.total_charges_label.setStyleSheet("""
            QLabel {
                background-color: #fef3c7;
                padding: 8px;
                border-radius: 4px;
                border: 1px solid #f59e0b;
                font-weight: bold;
            }
        """)
        charges_layout.addWidget(self.total_charges_label, 1, 3)
        
        layout.addWidget(charges_group)
        
        # المبلغ الصافي
        net_group = QGroupBox("المبلغ الصافي")
        net_layout = QHBoxLayout(net_group)
        
        net_label = QLabel("المبلغ الصافي للمستقبل:")
        net_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        self.net_amount_label = QLabel("0.00")
        self.net_amount_label.setFont(QFont("Arial", 16, QFont.Bold))
        self.net_amount_label.setStyleSheet("""
            QLabel {
                background-color: #dcfce7;
                color: #166534;
                padding: 15px;
                border-radius: 8px;
                border: 2px solid #22c55e;
            }
        """)
        
        net_layout.addWidget(net_label)
        net_layout.addStretch()
        net_layout.addWidget(self.net_amount_label)
        
        layout.addWidget(net_group)
        layout.addStretch()
        
        return tab

    def create_banks_tab(self):
        """إنشاء تبويب معلومات البنوك"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)

        # البنك المرسل
        sender_bank_group = QGroupBox("البنك المرسل")
        sender_bank_layout = QGridLayout(sender_bank_group)
        sender_bank_layout.setSpacing(10)

        # اختيار البنك المرسل
        sender_bank_layout.addWidget(QLabel("البنك المرسل:"), 0, 0)
        self.sender_bank_combo = QComboBox()
        self.sender_bank_combo.setEditable(True)
        self.sender_bank_combo.setPlaceholderText("اختر البنك المرسل...")
        sender_bank_layout.addWidget(self.sender_bank_combo, 0, 1)

        # حساب المرسل
        sender_bank_layout.addWidget(QLabel("حساب المرسل:"), 0, 2)
        self.sender_account_combo = QComboBox()
        self.sender_account_combo.setEditable(True)
        self.sender_account_combo.setPlaceholderText("اختر الحساب...")
        sender_bank_layout.addWidget(self.sender_account_combo, 0, 3)

        layout.addWidget(sender_bank_group)

        # البنك المستقبل
        receiver_bank_group = QGroupBox("البنك المستقبل")
        receiver_bank_layout = QGridLayout(receiver_bank_group)
        receiver_bank_layout.setSpacing(10)

        # اختيار البنك المستقبل
        receiver_bank_layout.addWidget(QLabel("البنك المستقبل:"), 0, 0)
        self.receiver_bank_combo = QComboBox()
        self.receiver_bank_combo.setEditable(True)
        self.receiver_bank_combo.setPlaceholderText("اختر البنك المستقبل...")
        receiver_bank_layout.addWidget(self.receiver_bank_combo, 0, 1)

        # حساب المستقبل
        receiver_bank_layout.addWidget(QLabel("حساب المستقبل:"), 0, 2)
        self.receiver_account_combo = QComboBox()
        self.receiver_account_combo.setEditable(True)
        self.receiver_account_combo.setPlaceholderText("اختر الحساب...")
        receiver_bank_layout.addWidget(self.receiver_account_combo, 0, 3)

        layout.addWidget(receiver_bank_group)

        # معلومات إضافية
        additional_info_group = QGroupBox("معلومات إضافية")
        additional_layout = QGridLayout(additional_info_group)
        additional_layout.setSpacing(10)

        # تاريخ القيمة
        additional_layout.addWidget(QLabel("تاريخ القيمة:"), 0, 0)
        self.value_date = QDateEdit()
        self.value_date.setDate(QDate.currentDate())
        self.value_date.setCalendarPopup(True)
        additional_layout.addWidget(self.value_date, 0, 1)

        # تاريخ الإنجاز المتوقع
        additional_layout.addWidget(QLabel("تاريخ الإنجاز المتوقع:"), 0, 2)
        self.expected_completion_date = QDateEdit()
        self.expected_completion_date.setDate(QDate.currentDate().addDays(1))
        self.expected_completion_date.setCalendarPopup(True)
        additional_layout.addWidget(self.expected_completion_date, 0, 3)

        layout.addWidget(additional_info_group)
        layout.addStretch()

        return tab

    def create_notes_tab(self):
        """إنشاء تبويب الملاحظات والتفاصيل"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)

        # الغرض من الحوالة
        purpose_group = QGroupBox("الغرض من الحوالة")
        purpose_layout = QVBoxLayout(purpose_group)

        self.purpose_input = QLineEdit()
        self.purpose_input.setPlaceholderText("أدخل الغرض من الحوالة (مثل: دفع مستحقات، تحويل شخصي، إلخ)")
        purpose_layout.addWidget(self.purpose_input)

        layout.addWidget(purpose_group)

        # الملاحظات العامة
        notes_group = QGroupBox("الملاحظات العامة")
        notes_layout = QVBoxLayout(notes_group)

        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        self.notes_input.setMaximumHeight(120)
        notes_layout.addWidget(self.notes_input)

        layout.addWidget(notes_group)

        # الملاحظات الداخلية
        internal_notes_group = QGroupBox("الملاحظات الداخلية")
        internal_notes_layout = QVBoxLayout(internal_notes_group)

        self.internal_notes_input = QTextEdit()
        self.internal_notes_input.setPlaceholderText("ملاحظات داخلية للموظفين فقط...")
        self.internal_notes_input.setMaximumHeight(120)
        internal_notes_layout.addWidget(self.internal_notes_input)

        layout.addWidget(internal_notes_group)

        # خيارات إضافية
        options_group = QGroupBox("خيارات إضافية")
        options_layout = QGridLayout(options_group)

        # حفظ كمسودة
        self.save_as_draft_checkbox = QCheckBox("حفظ كمسودة")
        self.save_as_draft_checkbox.setChecked(True)
        options_layout.addWidget(self.save_as_draft_checkbox, 0, 0)

        # إرسال تنبيه
        self.send_notification_checkbox = QCheckBox("إرسال تنبيه للمورد")
        options_layout.addWidget(self.send_notification_checkbox, 0, 1)

        # طباعة بعد الحفظ
        self.print_after_save_checkbox = QCheckBox("طباعة بعد الحفظ")
        options_layout.addWidget(self.print_after_save_checkbox, 1, 0)

        layout.addWidget(options_group)
        layout.addStretch()

        return tab

    def create_action_buttons(self, layout):
        """إنشاء أزرار الإجراءات"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        buttons_layout.addWidget(self.progress_bar)

        buttons_layout.addStretch()

        # زر الإلغاء
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setMinimumSize(100, 35)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        buttons_layout.addWidget(self.cancel_btn)

        # زر المسح
        self.clear_btn = QPushButton("مسح الكل")
        self.clear_btn.setMinimumSize(100, 35)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #f59e0b;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d97706;
            }
        """)
        buttons_layout.addWidget(self.clear_btn)

        # زر الحفظ كمسودة
        self.save_draft_btn = QPushButton("💾 حفظ كمسودة")
        self.save_draft_btn.setMinimumSize(120, 35)
        self.save_draft_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        buttons_layout.addWidget(self.save_draft_btn)

        # زر تأكيد الحوالة
        self.confirm_btn = QPushButton("✅ تأكيد الحوالة")
        self.confirm_btn.setMinimumSize(120, 35)
        self.confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
            QPushButton:disabled {
                background-color: #9ca3af;
            }
        """)
        buttons_layout.addWidget(self.confirm_btn)

        # زر ترحيل للموردين
        self.transfer_to_suppliers_btn = QPushButton("🔄 ترحيل للموردين")
        self.transfer_to_suppliers_btn.setMinimumSize(140, 35)
        self.transfer_to_suppliers_btn.setEnabled(False)  # معطل حتى يتم تأكيد الحوالة
        self.transfer_to_suppliers_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:disabled {
                background-color: #9ca3af;
                color: #6b7280;
            }
        """)
        buttons_layout.addWidget(self.transfer_to_suppliers_btn)

        layout.addWidget(buttons_frame)

    def setup_validators(self):
        """إعداد مدققات الإدخال"""
        # مدقق المبلغ (QLineEdit)
        if hasattr(self, 'amount_input') and isinstance(self.amount_input, QLineEdit):
            amount_validator = QDoubleValidator(0.0, 999999999.99, 2)
            self.amount_input.setValidator(amount_validator)

        # مدقق الرقم المرجعي
        reference_regex = QRegularExpression(r"^[A-Za-z0-9\-_]+$")
        reference_validator = QRegularExpressionValidator(reference_regex)
        self.reference_number_input.setValidator(reference_validator)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # أزرار الإجراءات
        self.cancel_btn.clicked.connect(self.reject)
        self.clear_btn.clicked.connect(self.clear_all_fields)
        self.save_draft_btn.clicked.connect(self.save_as_draft)
        self.confirm_btn.clicked.connect(self.confirm_remittance)
        self.transfer_to_suppliers_btn.clicked.connect(self.transfer_to_suppliers)

        # تحديث اسم جهة التحويل عند تغيير النوع
        self.transfer_entity_combo.currentTextChanged.connect(self.update_transfer_entity_name)

        # تحديث المبالغ عند التغيير (للحقول الموجودة فقط)
        if hasattr(self, 'amount_input'):
            self.amount_input.textChanged.connect(self.update_calculations)
        # الحقول التالية غير موجودة في التصميم الجديد
        # self.exchange_rate_input.valueChanged.connect(self.update_calculations)
        # self.transfer_fee_input.valueChanged.connect(self.update_calculations)
        # self.bank_charges_input.valueChanged.connect(self.update_calculations)
        # self.other_charges_input.valueChanged.connect(self.update_calculations)

        # تحديث حسابات البنوك عند تغيير البنك
        self.sender_bank_combo.currentTextChanged.connect(self.update_sender_accounts)
        self.receiver_bank_combo.currentTextChanged.connect(self.update_receiver_accounts)

        # التحقق من صحة البيانات عند التغيير (للحقول الموجودة فقط)
        if hasattr(self, 'amount_input'):
            self.amount_input.textChanged.connect(self.validate_form)
        if hasattr(self, 'currency_combo'):
            self.currency_combo.currentTextChanged.connect(self.validate_form)
        if hasattr(self, 'transfer_entity_name_combo'):
            self.transfer_entity_name_combo.currentTextChanged.connect(self.validate_form)
        # الحقول التالية غير موجودة في التصميم الجديد
        # self.sender_name_input.textChanged.connect(self.validate_form)
        # self.receiver_name_input.textChanged.connect(self.validate_form)
        # self.supplier_combo.currentTextChanged.connect(self.validate_form)

        # اتصالات الموردين (تم نقلها للتصميم الجديد)
        # هذه الحقول غير موجودة في التصميم الجديد
        # self.add_supplier_btn.clicked.connect(self.add_supplier_to_list)
        # self.new_supplier_currency.currentTextChanged.connect(self.update_supplier_exchange_rate)
        # self.new_supplier_amount.valueChanged.connect(self.validate_supplier_form)
        # self.new_supplier_combo.currentTextChanged.connect(self.validate_supplier_form)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل الموردين (للحقول الموجودة فقط)
            cursor.execute("SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name")
            self.suppliers_data = cursor.fetchall()
            # الحقل supplier_combo غير موجود في التصميم الجديد
            # self.supplier_combo.addItem("اختر المورد...", None)
            # for supplier in self.suppliers_data:
            #     self.supplier_combo.addItem(supplier[1], supplier[0])

            # تحميل العملات
            cursor.execute("SELECT id, code, name FROM currencies WHERE is_active = 1 ORDER BY name")
            self.currencies_data = cursor.fetchall()
            self.currency_combo.addItem("اختر العملة...", None)
            for currency in self.currencies_data:
                self.currency_combo.addItem(f"{currency[1]} - {currency[2]}", currency[0])

            # تحميل البيانات الأولية لجهة التحويل
            self.update_transfer_entity_name()

            # تحميل البنوك
            cursor.execute("SELECT id, name FROM banks WHERE is_active = 1 ORDER BY name")
            self.banks_data = cursor.fetchall()

            # إضافة البنوك للكومبو بوكس
            for combo in [self.sender_bank_combo, self.receiver_bank_combo]:
                combo.addItem("اختر البنك...", None)
                for bank in self.banks_data:
                    combo.addItem(bank[1], bank[0])

            # تحميل البيانات للموردين المتعددين
            self.load_multiple_suppliers_data()

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات الأولية:\n{str(e)}")

    def generate_remittance_number(self):
        """توليد رقم حوالة تلقائي"""
        try:
            # تنسيق: REM-YYYYMMDD-XXXX
            today = datetime.now()
            date_part = today.strftime("%Y%m%d")

            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # البحث عن آخر رقم في نفس اليوم
            cursor.execute("""
                SELECT remittance_number FROM remittances
                WHERE remittance_number LIKE ?
                ORDER BY remittance_number DESC LIMIT 1
            """, (f"REM-{date_part}-%",))

            result = cursor.fetchone()

            if result:
                # استخراج الرقم التسلسلي وزيادته
                last_number = result[0]
                sequence = int(last_number.split('-')[-1]) + 1
            else:
                sequence = 1

            # تكوين الرقم الجديد
            new_number = f"REM-{date_part}-{sequence:04d}"
            self.remittance_number_input.setText(new_number)

            conn.close()

        except Exception as e:
            # في حالة الخطأ، استخدم رقم عشوائي
            random_id = str(uuid.uuid4())[:8].upper()
            self.remittance_number_input.setText(f"REM-{random_id}")

    def update_calculations(self):
        """تحديث الحسابات المالية"""
        try:
            # الحصول على القيم (للحقول الموجودة فقط)
            amount = 0.0
            if hasattr(self, 'amount_input') and self.amount_input.text():
                try:
                    amount = float(self.amount_input.text())
                except ValueError:
                    amount = 0.0

            # الحقول التالية غير موجودة في التصميم الجديد
            # exchange_rate = self.exchange_rate_input.value()
            # transfer_fee = self.transfer_fee_input.value()
            # bank_charges = self.bank_charges_input.value()
            # other_charges = self.other_charges_input.value()

            # حساب المبلغ بالعملة الأساسية (مبسط)
            # base_amount = amount * exchange_rate
            # self.base_amount_label.setText(f"{base_amount:,.2f} ريال سعودي")

            # حساب إجمالي الرسوم
            total_charges = transfer_fee + bank_charges + other_charges
            self.total_charges_label.setText(f"{total_charges:,.2f}")

            # حساب المبلغ الصافي
            net_amount = amount - total_charges
            self.net_amount_label.setText(f"{net_amount:,.2f}")

            # تغيير لون المبلغ الصافي حسب القيمة
            if net_amount < 0:
                self.net_amount_label.setStyleSheet("""
                    QLabel {
                        background-color: #fee2e2;
                        color: #dc2626;
                        padding: 15px;
                        border-radius: 8px;
                        border: 2px solid #ef4444;
                    }
                """)
            else:
                self.net_amount_label.setStyleSheet("""
                    QLabel {
                        background-color: #dcfce7;
                        color: #166534;
                        padding: 15px;
                        border-radius: 8px;
                        border: 2px solid #22c55e;
                    }
                """)

        except Exception as e:
            print(f"خطأ في تحديث الحسابات: {e}")

    def update_sender_accounts(self):
        """تحديث حسابات البنك المرسل"""
        self.sender_account_combo.clear()
        self.sender_account_combo.addItem("اختر الحساب...", None)

        bank_id = self.sender_bank_combo.currentData()
        if bank_id:
            try:
                db_path = Path("data/proshipment.db")
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT id, account_number, account_name
                    FROM bank_accounts
                    WHERE bank_id = ? AND is_active = 1
                    ORDER BY account_name
                """, (bank_id,))

                accounts = cursor.fetchall()
                for account in accounts:
                    display_text = f"{account[1]} - {account[2]}"
                    self.sender_account_combo.addItem(display_text, account[0])

                conn.close()

            except Exception as e:
                print(f"خطأ في تحميل حسابات البنك المرسل: {e}")

    def update_receiver_accounts(self):
        """تحديث حسابات البنك المستقبل"""
        self.receiver_account_combo.clear()
        self.receiver_account_combo.addItem("اختر الحساب...", None)

        bank_id = self.receiver_bank_combo.currentData()
        if bank_id:
            try:
                db_path = Path("data/proshipment.db")
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT id, account_number, account_name
                    FROM bank_accounts
                    WHERE bank_id = ? AND is_active = 1
                    ORDER BY account_name
                """, (bank_id,))

                accounts = cursor.fetchall()
                for account in accounts:
                    display_text = f"{account[1]} - {account[2]}"
                    self.receiver_account_combo.addItem(display_text, account[0])

                conn.close()

            except Exception as e:
                print(f"خطأ في تحميل حسابات البنك المستقبل: {e}")

    def validate_form(self):
        """التحقق من صحة البيانات المدخلة"""
        is_valid = True

        # التحقق من الحقول المطلوبة
        try:
            amount = float(self.amount_input.text()) if self.amount_input.text() else 0
        except ValueError:
            amount = 0

        required_fields = [
            (amount > 0, "مبلغ الحوالة"),
            (self.currency_combo.currentData() is not None, "العملة"),
            (self.transfer_entity_name_combo.currentText().strip(), "جهة التحويل")
        ]

        for field_value, field_name in required_fields:
            if not field_value:
                is_valid = False
                break

        return is_valid

    def clear_all_fields(self):
        """مسح جميع الحقول"""
        reply = QMessageBox.question(self, "تأكيد المسح",
                                   "هل أنت متأكد من مسح جميع البيانات المدخلة؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            # مسح المعلومات الأساسية
            self.reference_number_input.clear()
            self.amount_input.clear()
            self.currency_combo.setCurrentIndex(0)
            self.transfer_entity_combo.setCurrentIndex(0)
            self.transfer_entity_name_combo.setCurrentIndex(0)
            self.status_combo.setCurrentText("مسودة")
            self.notes_input.clear()

            # مسح جدول الموردين
            self.suppliers_table.setRowCount(0)
            self.update_distributed_amount()

            # إعادة تعيين التاريخ
            self.remittance_date.setDate(QDate.currentDate())

            # إعادة توليد رقم الحوالة
            self.generate_remittance_number()

            # إعادة تعيين التواريخ
            self.remittance_date.setDate(QDate.currentDate())
            self.value_date.setDate(QDate.currentDate())
            self.expected_completion_date.setDate(QDate.currentDate().addDays(1))

            # مسح الموردين المتعددين
            self.multiple_suppliers.clear()
            self.suppliers_total_amount = 0.0
            self.clear_supplier_form()
            self.update_suppliers_table()
            self.update_suppliers_summary()

            # إعادة توليد رقم الحوالة
            self.generate_remittance_number()

    def save_remittance(self):
        """حفظ الحوالة الجديدة"""
        if not self.validate_form():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إكمال جميع الحقول المطلوبة")
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.save_btn.setEnabled(False)

            # جمع البيانات
            remittance_data = self.collect_form_data()

            # حفظ في قاعدة البيانات
            remittance_id = self.save_to_database(remittance_data)

            if remittance_id:
                # إنشاء سجل في تاريخ الحالات
                self.create_status_history(remittance_id, "مسودة", "تم إنشاء الحوالة")

                # إنشاء معاملة في حساب المورد
                self.create_supplier_transaction(remittance_id, remittance_data)

                QMessageBox.information(self, "نجح الحفظ",
                                      f"تم حفظ الحوالة بنجاح\nرقم الحوالة: {remittance_data['remittance_number']}")

                # إرسال إشارة إنشاء الحوالة
                self.remittance_created.emit(remittance_id)

                # طباعة إذا كان مطلوباً
                if self.print_after_save_checkbox.isChecked():
                    self.print_remittance(remittance_id)

                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ الحوالة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الحوالة:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.save_btn.setEnabled(True)

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        try:
            amount = float(self.amount_input.text()) if self.amount_input.text() else 0.0
        except ValueError:
            amount = 0.0

        return {
            'remittance_number': self.remittance_number_input.text().strip(),
            'reference_number': self.reference_number_input.text().strip() or None,
            'remittance_date': self.remittance_date.date().toString("yyyy-MM-dd"),

            # معلومات جهة التحويل
            'transfer_entity_type': self.transfer_entity_combo.currentText(),
            'transfer_entity_name': self.transfer_entity_name_combo.currentText(),
            'transfer_entity_id': self.transfer_entity_name_combo.currentData(),

            # البيانات المالية
            'amount': amount,
            'currency_id': self.currency_combo.currentData(),
            'currency_code': self.currency_combo.currentText().split(' - ')[0] if self.currency_combo.currentText() else '',
            'status': self.status_combo.currentText(),

            # بيانات الموردين
            'suppliers_data': self.get_suppliers_data_for_save(),
            'total_distributed_amount': self.get_total_distributed_amount(),

            # الحالة
            'current_status': self.status_combo.currentText(),
            'created_by': 1,  # سيتم تحديثه لاحقاً مع نظام المستخدمين
        }

    def get_suppliers_data_for_save(self):
        """جمع بيانات الموردين للحفظ"""
        suppliers_data = []
        for row in range(self.suppliers_table.rowCount()):
            supplier_data = {
                'name': self.suppliers_table.item(row, 0).text(),
                'amount': float(self.suppliers_table.item(row, 1).text()),
                'currency': self.suppliers_table.item(row, 2).text(),
                'description': self.suppliers_table.item(row, 3).text(),
                'status': self.suppliers_table.item(row, 4).text()
            }
            suppliers_data.append(supplier_data)
        return suppliers_data

    def get_total_distributed_amount(self):
        """حساب إجمالي المبلغ الموزع"""
        total = 0.0
        for row in range(self.suppliers_table.rowCount()):
            amount_item = self.suppliers_table.item(row, 1)
            if amount_item:
                try:
                    total += float(amount_item.text())
                except ValueError:
                    pass
        return total

    def save_to_database(self, data):
        """حفظ البيانات في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # التأكد من وجود جدول الموردين المتعددين
            self.ensure_suppliers_table_exists(cursor)

            # إدراج الحوالة الجديدة
            insert_query = """
                INSERT INTO remittances (
                    remittance_number, reference_number, remittance_date, value_date,
                    expected_completion_date, sender_name, sender_id_number, sender_phone,
                    sender_address, receiver_name, receiver_id_number, receiver_phone,
                    receiver_address, supplier_id, amount, currency_id, exchange_rate,
                    amount_in_base_currency, transfer_fee, bank_charges, other_charges,
                    total_charges, net_amount, sender_bank_id, sender_account_id,
                    receiver_bank_id, receiver_account_id, purpose, notes, internal_notes,
                    current_status, created_by, created_at
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP
                )
            """

            cursor.execute(insert_query, (
                data['remittance_number'], data['reference_number'], data['remittance_date'],
                data['value_date'], data['expected_completion_date'], data['sender_name'],
                data['sender_id_number'], data['sender_phone'], data['sender_address'],
                data['receiver_name'], data['receiver_id_number'], data['receiver_phone'],
                data['receiver_address'], data['supplier_id'], data['amount'],
                data['currency_id'], data['exchange_rate'], data['amount_in_base_currency'],
                data['transfer_fee'], data['bank_charges'], data['other_charges'],
                data['total_charges'], data['net_amount'], data['sender_bank_id'],
                data['sender_account_id'], data['receiver_bank_id'], data['receiver_account_id'],
                data['purpose'], data['notes'], data['internal_notes'], data['current_status'],
                data['created_by']
            ))

            remittance_id = cursor.lastrowid

            # حفظ الموردين المتعددين إذا كانوا موجودين
            if data.get('has_multiple_suppliers') and data.get('multiple_suppliers'):
                self.save_multiple_suppliers(cursor, remittance_id, data['multiple_suppliers']['suppliers'])

            conn.commit()
            conn.close()

            return remittance_id

        except Exception as e:
            if conn:
                conn.rollback()
                conn.close()
            raise e

    def create_status_history(self, remittance_id, status, notes):
        """إنشاء سجل في تاريخ الحالات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO remittance_status_history (
                    remittance_id, old_status, new_status, notes, changed_by, changed_at
                ) VALUES (?, NULL, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (remittance_id, status, notes, 1))  # سيتم تحديث المستخدم لاحقاً

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء سجل تاريخ الحالة: {e}")

    def create_supplier_transaction(self, remittance_id, data):
        """إنشاء معاملة في حساب المورد"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # البحث عن حساب المورد أو إنشاؤه
            cursor.execute("""
                SELECT id, current_balance FROM supplier_accounts
                WHERE supplier_id = ? AND currency_id = ?
            """, (data['supplier_id'], data['currency_id']))

            account = cursor.fetchone()

            if not account:
                # إنشاء حساب جديد للمورد
                cursor.execute("""
                    INSERT INTO supplier_accounts (
                        supplier_id, currency_id, current_balance, available_balance
                    ) VALUES (?, ?, 0.0, 0.0)
                """, (data['supplier_id'], data['currency_id']))

                account_id = cursor.lastrowid
                current_balance = 0.0
            else:
                account_id = account[0]
                current_balance = account[1] or 0.0

            # إنشاء معاملة دائنة (إضافة المبلغ لحساب المورد)
            new_balance = current_balance + data['net_amount']

            # توليد رقم معاملة
            transaction_number = f"TXN-{remittance_id}-{datetime.now().strftime('%Y%m%d%H%M%S')}"

            cursor.execute("""
                INSERT INTO supplier_account_transactions (
                    account_id, remittance_id, transaction_number, transaction_type,
                    transaction_date, credit_amount, balance_before, balance_after,
                    description, created_by
                ) VALUES (?, ?, ?, 'دائن', CURRENT_TIMESTAMP, ?, ?, ?, ?, ?)
            """, (
                account_id, remittance_id, transaction_number, data['net_amount'],
                current_balance, new_balance,
                f"حوالة رقم {data['remittance_number']}", 1
            ))

            # تحديث رصيد الحساب
            cursor.execute("""
                UPDATE supplier_accounts
                SET current_balance = ?, available_balance = ?, last_transaction_date = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (new_balance, new_balance, account_id))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء معاملة المورد: {e}")

    def create_multiple_suppliers_tab(self):
        """إنشاء تبويب الموردين المتعددين"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)

        # عنوان القسم
        title_frame = QFrame()
        title_frame.setFrameStyle(QFrame.StyledPanel)
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #3b82f6;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_icon = QLabel("👥")
        title_icon.setStyleSheet("font-size: 24px; color: white;")
        title_layout.addWidget(title_icon)

        title_label = QLabel("إدارة الموردين المتعددين")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # معلومات سريعة
        self.suppliers_count_label = QLabel("عدد الموردين: 0")
        self.suppliers_count_label.setStyleSheet("color: white; font-weight: bold;")
        title_layout.addWidget(self.suppliers_count_label)

        self.suppliers_total_label = QLabel("إجمالي المبلغ: 0.00")
        self.suppliers_total_label.setStyleSheet("color: white; font-weight: bold;")
        title_layout.addWidget(self.suppliers_total_label)

        layout.addWidget(title_frame)

        # قسم إضافة مورد جديد
        add_supplier_frame = QFrame()
        add_supplier_frame.setFrameStyle(QFrame.StyledPanel)
        add_supplier_frame.setStyleSheet("""
            QFrame {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 15px;
            }
        """)

        add_supplier_layout = QGridLayout(add_supplier_frame)

        # اختيار المورد
        add_supplier_layout.addWidget(QLabel("المورد:"), 0, 0)
        self.new_supplier_combo = QComboBox()
        self.new_supplier_combo.setMinimumWidth(200)
        add_supplier_layout.addWidget(self.new_supplier_combo, 0, 1)

        # المبلغ
        add_supplier_layout.addWidget(QLabel("المبلغ:"), 0, 2)
        self.new_supplier_amount = QDoubleSpinBox()
        self.new_supplier_amount.setRange(0.01, 999999999.99)
        self.new_supplier_amount.setDecimals(2)
        self.new_supplier_amount.setSuffix(" ريال")
        self.new_supplier_amount.setMinimumWidth(150)
        add_supplier_layout.addWidget(self.new_supplier_amount, 0, 3)

        # العملة
        add_supplier_layout.addWidget(QLabel("العملة:"), 1, 0)
        self.new_supplier_currency = QComboBox()
        self.new_supplier_currency.setMinimumWidth(150)
        add_supplier_layout.addWidget(self.new_supplier_currency, 1, 1)

        # سعر الصرف
        add_supplier_layout.addWidget(QLabel("سعر الصرف:"), 1, 2)
        self.new_supplier_exchange_rate = QDoubleSpinBox()
        self.new_supplier_exchange_rate.setRange(0.0001, 9999.9999)
        self.new_supplier_exchange_rate.setDecimals(4)
        self.new_supplier_exchange_rate.setValue(1.0000)
        self.new_supplier_exchange_rate.setMinimumWidth(150)
        add_supplier_layout.addWidget(self.new_supplier_exchange_rate, 1, 3)

        # الملاحظات
        add_supplier_layout.addWidget(QLabel("ملاحظات:"), 2, 0)
        self.new_supplier_notes = QLineEdit()
        self.new_supplier_notes.setPlaceholderText("ملاحظات خاصة بهذا المورد...")
        add_supplier_layout.addWidget(self.new_supplier_notes, 2, 1, 1, 2)

        # زر الإضافة
        self.add_supplier_btn = QPushButton("➕ إضافة المورد")
        self.add_supplier_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:disabled {
                background-color: #9ca3af;
            }
        """)
        add_supplier_layout.addWidget(self.add_supplier_btn, 2, 3)

        layout.addWidget(add_supplier_frame)

        # جدول الموردين
        suppliers_table_frame = QFrame()
        suppliers_table_layout = QVBoxLayout(suppliers_table_frame)

        table_title = QLabel("قائمة الموردين في الحوالة")
        table_title.setFont(QFont("Arial", 12, QFont.Bold))
        table_title.setStyleSheet("color: #374151; padding: 10px 0;")
        suppliers_table_layout.addWidget(table_title)

        self.suppliers_table = QTableWidget()
        self.setup_suppliers_table()
        suppliers_table_layout.addWidget(self.suppliers_table)

        layout.addWidget(suppliers_table_frame)

        # ملخص المبالغ
        summary_frame = QFrame()
        summary_frame.setFrameStyle(QFrame.StyledPanel)
        summary_frame.setStyleSheet("""
            QFrame {
                background-color: #f1f5f9;
                border: 1px solid #cbd5e1;
                border-radius: 8px;
                padding: 15px;
            }
        """)

        summary_layout = QGridLayout(summary_frame)

        summary_layout.addWidget(QLabel("إجمالي عدد الموردين:"), 0, 0)
        self.summary_suppliers_count = QLabel("0")
        self.summary_suppliers_count.setStyleSheet("font-weight: bold; color: #3b82f6;")
        summary_layout.addWidget(self.summary_suppliers_count, 0, 1)

        summary_layout.addWidget(QLabel("إجمالي المبلغ:"), 0, 2)
        self.summary_total_amount = QLabel("0.00 ريال")
        self.summary_total_amount.setStyleSheet("font-weight: bold; color: #059669; font-size: 14px;")
        summary_layout.addWidget(self.summary_total_amount, 0, 3)

        summary_layout.addWidget(QLabel("متوسط المبلغ لكل مورد:"), 1, 0)
        self.summary_avg_amount = QLabel("0.00 ريال")
        self.summary_avg_amount.setStyleSheet("font-weight: bold; color: #f59e0b;")
        summary_layout.addWidget(self.summary_avg_amount, 1, 1)

        summary_layout.addWidget(QLabel("أعلى مبلغ:"), 1, 2)
        self.summary_max_amount = QLabel("0.00 ريال")
        self.summary_max_amount.setStyleSheet("font-weight: bold; color: #ef4444;")
        summary_layout.addWidget(self.summary_max_amount, 1, 3)

        layout.addWidget(summary_frame)

        return tab

    def setup_suppliers_table(self):
        """إعداد جدول الموردين"""
        headers = ["المورد", "المبلغ", "العملة", "سعر الصرف", "المبلغ بالريال", "الملاحظات", "إجراءات"]

        self.suppliers_table.setColumnCount(len(headers))
        self.suppliers_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.suppliers_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)           # المورد
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # العملة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # سعر الصرف
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # المبلغ بالريال
        header.setSectionResizeMode(5, QHeaderView.Stretch)           # الملاحظات
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الإجراءات

        # تنسيق الجدول
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e2e8f0;
                background-color: white;
                alternate-background-color: #f8fafc;
                selection-background-color: #3b82f6;
                selection-color: white;
                border: 1px solid #d1d5db;
                border-radius: 6px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e2e8f0;
            }
            QHeaderView::section {
                background-color: #f1f5f9;
                color: #374151;
                padding: 10px;
                border: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)

    def add_supplier_to_list(self):
        """إضافة مورد إلى قائمة الموردين (دالة قديمة - تم استبدالها)"""
        # هذه الدالة تستخدم الحقول القديمة وتم استبدالها بـ add_supplier_to_remittance
        pass
        # supplier_id = self.new_supplier_combo.currentData()
        # supplier_name = self.new_supplier_combo.currentText()
        # amount = self.new_supplier_amount.value()
        # currency_id = self.new_supplier_currency.currentData()
        # currency_code = self.new_supplier_currency.currentText().split(' - ')[0] if ' - ' in self.new_supplier_currency.currentText() else self.new_supplier_currency.currentText()
        # exchange_rate = self.new_supplier_exchange_rate.value()
        # notes = self.new_supplier_notes.text().strip()

        # الكود التالي معلق لأنه يستخدم متغيرات غير معرفة
        # if not supplier_id:
        #     QMessageBox.warning(self, "تحذير", "يرجى اختيار المورد")
        #     return
        # if amount <= 0:
        #     QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
        #     return
        # if not currency_id:
        #     QMessageBox.warning(self, "تحذير", "يرجى اختيار العملة")
        #     return
        # # التحقق من عدم تكرار المورد
        # for supplier in self.multiple_suppliers:
        #     if supplier['supplier_id'] == supplier_id:
        #         QMessageBox.warning(self, "تحذير", "هذا المورد موجود بالفعل في القائمة")
        #         return
        # # حساب المبلغ بالريال السعودي
        # amount_in_sar = amount * exchange_rate
        # # إضافة المورد إلى القائمة
        # supplier_data = {
        #     'supplier_id': supplier_id,
        #     'supplier_name': supplier_name,
        #     'amount': amount,
        #     'currency_id': currency_id,
        #     'currency_code': currency_code,
        #     'exchange_rate': exchange_rate,
        #     'amount_in_sar': amount_in_sar,
        #     'notes': notes
        # }

        # self.multiple_suppliers.append(supplier_data)
        # # تحديث الجدول والملخص
        # self.update_suppliers_table()
        # self.update_suppliers_summary()
        # # مسح النموذج
        # self.clear_supplier_form()
        # QMessageBox.information(self, "نجح", f"تم إضافة المورد '{supplier_name}' بنجاح")

    def remove_supplier_from_list(self, row):
        """حذف مورد من القائمة (دالة قديمة)"""
        # هذه الدالة تستخدم النظام القديم
        pass
        # if 0 <= row < len(self.multiple_suppliers):
        #     supplier_name = self.multiple_suppliers[row]['supplier_name']
        #     reply = QMessageBox.question(
        #         self, "تأكيد الحذف",
        #         f"هل تريد حذف المورد '{supplier_name}' من القائمة؟",
        #         QMessageBox.Yes | QMessageBox.No,
        #         QMessageBox.No
        #     )
        #     if reply == QMessageBox.Yes:
        #         del self.multiple_suppliers[row]
        #         self.update_suppliers_table()
        #         self.update_suppliers_summary()
        #         QMessageBox.information(self, "تم الحذف", f"تم حذف المورد '{supplier_name}' من القائمة")

    def edit_supplier_in_list(self, row):
        """تعديل مورد في القائمة (دالة قديمة)"""
        # هذه الدالة تستخدم النظام القديم
        pass
        # if 0 <= row < len(self.multiple_suppliers):
        #     supplier = self.multiple_suppliers[row]
        #     # ملء النموذج ببيانات المورد
        #     for i in range(self.new_supplier_combo.count()):
        #         if self.new_supplier_combo.itemData(i) == supplier['supplier_id']:
        #             self.new_supplier_combo.setCurrentIndex(i)
        #             break
        #     self.new_supplier_amount.setValue(supplier['amount'])
        #     for i in range(self.new_supplier_currency.count()):
        #         if self.new_supplier_currency.itemData(i) == supplier['currency_id']:
        #             self.new_supplier_currency.setCurrentIndex(i)
        #             break
        #     self.new_supplier_exchange_rate.setValue(supplier['exchange_rate'])
        #     self.new_supplier_notes.setText(supplier['notes'])
        #     # حذف المورد من القائمة مؤقتاً
        #     del self.multiple_suppliers[row]
        #     self.update_suppliers_table()
        #     self.update_suppliers_summary()
        #     QMessageBox.information(self, "تعديل", "تم تحميل بيانات المورد للتعديل. يرجى تعديل البيانات والضغط على 'إضافة المورد'")

    def update_suppliers_table(self):
        """تحديث جدول الموردين (دالة قديمة)"""
        # هذه الدالة تستخدم النظام القديم
        pass
        # self.suppliers_table.setRowCount(len(self.multiple_suppliers))
        # الكود التالي معلق لأنه يستخدم النظام القديم
        # for row, supplier in enumerate(self.multiple_suppliers):
        #     # المورد
        #     self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier['supplier_name']))
        #     # المبلغ
        #     amount_item = QTableWidgetItem(f"{supplier['amount']:,.2f}")
        #     amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        #     self.suppliers_table.setItem(row, 1, amount_item)
        #     # العملة
        #     self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier['currency_code']))
        #     # سعر الصرف
        #     rate_item = QTableWidgetItem(f"{supplier['exchange_rate']:.4f}")
        #     rate_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        #     self.suppliers_table.setItem(row, 3, rate_item)
        #     # المبلغ بالريال
        #     sar_amount_item = QTableWidgetItem(f"{supplier['amount_in_sar']:,.2f}")
        #     sar_amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        #     sar_amount_item.setBackground(QColor("#ecfdf5"))
        #     self.suppliers_table.setItem(row, 4, sar_amount_item)
        #     # الملاحظات
        #     self.suppliers_table.setItem(row, 5, QTableWidgetItem(supplier['notes']))
        #     # أزرار الإجراءات
        #     actions_widget = QWidget()
        #     actions_layout = QHBoxLayout(actions_widget)
        #     actions_layout.setContentsMargins(5, 2, 5, 2)
        #     edit_btn = QPushButton("✏️")
        #     edit_btn.setToolTip("تعديل")
        #     edit_btn.setMaximumSize(30, 30)
        #     edit_btn.setStyleSheet("""
        #         QPushButton {
        #             background-color: #f59e0b;
        #             color: white;
        #             border: none;
        #             border-radius: 4px;
        #         }
        #         QPushButton:hover {
        #             background-color: #d97706;
        #         }
        #     """)
        #     edit_btn.clicked.connect(lambda checked, r=row: self.edit_supplier_in_list(r))
        #     actions_layout.addWidget(edit_btn)
        #     delete_btn = QPushButton("🗑️")
        #     delete_btn.setToolTip("حذف")
        #     delete_btn.setMaximumSize(30, 30)
        #     delete_btn.setStyleSheet("""
        #         QPushButton {
        #             background-color: #ef4444;
        #             color: white;
        #             border: none;
        #             border-radius: 4px;
        #         }
        #         QPushButton:hover {
        #             background-color: #dc2626;
        #         }
        #     """)
        #     delete_btn.clicked.connect(lambda checked, r=row: self.remove_supplier_from_list(r))
        #     actions_layout.addWidget(delete_btn)
        #     actions_layout.addStretch()
        #     self.suppliers_table.setCellWidget(row, 6, actions_widget)

    def update_suppliers_summary(self):
        """تحديث ملخص الموردين (دالة قديمة)"""
        # هذه الدالة تستخدم النظام القديم
        pass
        # count = len(self.multiple_suppliers)
        # total_amount = sum(s['amount_in_sar'] for s in self.multiple_suppliers)
        # avg_amount = total_amount / count if count > 0 else 0
        # max_amount = max((s['amount_in_sar'] for s in self.multiple_suppliers), default=0)
        # # تحديث التسميات في العنوان
        # self.suppliers_count_label.setText(f"عدد الموردين: {count}")
        # self.suppliers_total_label.setText(f"إجمالي المبلغ: {total_amount:,.2f}")
        # # تحديث الملخص التفصيلي
        # self.summary_suppliers_count.setText(str(count))
        # self.summary_total_amount.setText(f"{total_amount:,.2f} ريال")
        # self.summary_avg_amount.setText(f"{avg_amount:,.2f} ريال")
        # self.summary_max_amount.setText(f"{max_amount:,.2f} ريال")
        # # تحديث المتغير العام
        # self.suppliers_total_amount = total_amount

    def clear_supplier_form(self):
        """مسح نموذج إضافة المورد (دالة قديمة)"""
        # هذه الدالة تستخدم الحقول القديمة
        pass
        # self.new_supplier_combo.setCurrentIndex(0)
        # self.new_supplier_amount.setValue(0.00)
        # self.new_supplier_currency.setCurrentIndex(0)
        # self.new_supplier_exchange_rate.setValue(1.0000)
        # self.new_supplier_notes.clear()

    def update_supplier_exchange_rate(self):
        """تحديث سعر الصرف للمورد الجديد (دالة قديمة)"""
        # هذه الدالة تستخدم الحقول القديمة
        pass
        # currency_id = self.new_supplier_currency.currentData()
        # if currency_id:
        #     try:
        #         db_path = Path("data/proshipment.db")
        #         conn = sqlite3.connect(str(db_path))
        #         cursor = conn.cursor()
        #         cursor.execute("SELECT exchange_rate FROM currencies WHERE id = ?", (currency_id,))
        #         result = cursor.fetchone()
        #         if result:
        #             self.new_supplier_exchange_rate.setValue(result[0])
        #         conn.close()
        #     except Exception as e:
        #         print(f"خطأ في تحديث سعر الصرف: {e}")

    def validate_supplier_form(self):
        """التحقق من صحة نموذج المورد (دالة قديمة)"""
        # هذه الدالة تستخدم الحقول القديمة
        return True
        # supplier_id = self.new_supplier_combo.currentData()
        # amount = self.new_supplier_amount.value()
        # currency_id = self.new_supplier_currency.currentData()
        # is_valid = (supplier_id is not None and
        #            amount > 0 and
        #            currency_id is not None)
        # self.add_supplier_btn.setEnabled(is_valid)

    def get_suppliers_data_for_save(self):
        """الحصول على بيانات الموردين للحفظ"""
        if not self.multiple_suppliers:
            return None

        return {
            'suppliers': self.multiple_suppliers,
            'total_amount': self.suppliers_total_amount,
            'suppliers_count': len(self.multiple_suppliers)
        }

    def load_multiple_suppliers_data(self):
        """تحميل البيانات لتبويب الموردين المتعددين (دالة قديمة)"""
        # هذه الدالة تستخدم الحقول القديمة
        pass
        # try:
        #     # تحميل الموردين
        #     self.new_supplier_combo.addItem("اختر المورد...", None)
        #     for supplier in self.suppliers_data:
        #         self.new_supplier_combo.addItem(supplier[1], supplier[0])
        #     # تحميل العملات
        #     self.new_supplier_currency.addItem("اختر العملة...", None)
        #     for currency in self.currencies_data:
        #         self.new_supplier_currency.addItem(f"{currency[1]} - {currency[2]}", currency[0])

        # الكود التالي معلق لأنه يستخدم حقول غير موجودة
        # try:
        #     # تعطيل زر الإضافة في البداية
        #     self.add_supplier_btn.setEnabled(False)
        #     # تحديث الملخص
        #     self.update_suppliers_summary()
        # except Exception as e:
        #     print(f"خطأ في تحميل بيانات الموردين المتعددين: {e}")

    def ensure_suppliers_table_exists(self, cursor):
        """التأكد من وجود جدول الموردين المتعددين"""
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS remittance_suppliers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    remittance_id INTEGER NOT NULL,
                    supplier_id INTEGER NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    currency_id INTEGER NOT NULL,
                    exchange_rate DECIMAL(10,4) NOT NULL DEFAULT 1.0000,
                    amount_in_base_currency DECIMAL(15,2) NOT NULL,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (remittance_id) REFERENCES remittances(id) ON DELETE CASCADE,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                    FOREIGN KEY (currency_id) REFERENCES currencies(id)
                )
            """)

            # إنشاء الفهارس
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_remittance_suppliers_remittance_id
                ON remittance_suppliers(remittance_id)
            """)
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_remittance_suppliers_supplier_id
                ON remittance_suppliers(supplier_id)
            """)

        except Exception as e:
            print(f"خطأ في إنشاء جدول الموردين المتعددين: {e}")

    def save_multiple_suppliers(self, cursor, remittance_id, suppliers):
        """حفظ الموردين المتعددين"""
        try:
            for supplier in suppliers:
                cursor.execute("""
                    INSERT INTO remittance_suppliers (
                        remittance_id, supplier_id, amount, currency_id,
                        exchange_rate, amount_in_base_currency, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    remittance_id,
                    supplier['supplier_id'],
                    supplier['amount'],
                    supplier['currency_id'],
                    supplier['exchange_rate'],
                    supplier['amount_in_sar'],
                    supplier['notes']
                ))

        except Exception as e:
            print(f"خطأ في حفظ الموردين المتعددين: {e}")
            raise e

    def print_remittance(self, remittance_id):
        """طباعة الحوالة"""
        # ستكتمل لاحقاً مع نظام الطباعة
        QMessageBox.information(self, "طباعة", "تم إرسال الحوالة للطباعة")

    def save_as_draft(self):
        """حفظ الحوالة كمسودة"""
        if not self.validate_basic_data():
            return

        try:
            # تعيين الحالة كمسودة
            original_status = self.status_combo.currentText()
            self.status_combo.setCurrentText("مسودة")

            # حفظ الحوالة
            self.save_remittance()

            # استعادة الحالة الأصلية
            self.status_combo.setCurrentText(original_status)

            QMessageBox.information(self, "تم الحفظ", "تم حفظ الحوالة كمسودة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ المسودة:\n{str(e)}")

    def confirm_remittance(self):
        """تأكيد الحوالة"""
        if not self.validate_complete_data():
            return

        # التحقق من توزيع المبالغ
        if not self.validate_amount_distribution():
            return

        reply = QMessageBox.question(
            self, "تأكيد الحوالة",
            "هل أنت متأكد من تأكيد هذه الحوالة؟\n"
            "بعد التأكيد لن يمكن تعديل البيانات الأساسية.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # تغيير الحالة إلى معلقة
                self.status_combo.setCurrentText("معلقة")

                # حفظ الحوالة
                self.save_remittance()

                # تفعيل زر الترحيل
                self.transfer_to_suppliers_btn.setEnabled(True)

                # تعطيل بعض الحقول
                self.disable_basic_fields()

                QMessageBox.information(self, "تم التأكيد", "تم تأكيد الحوالة بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في تأكيد الحوالة:\n{str(e)}")

    def transfer_to_suppliers(self):
        """ترحيل المبالغ لحسابات الموردين"""
        if self.suppliers_table.rowCount() == 0:
            QMessageBox.warning(self, "تحذير", "لا توجد موردين للترحيل إليهم")
            return

        reply = QMessageBox.question(
            self, "تأكيد الترحيل",
            "هل أنت متأكد من ترحيل المبالغ لحسابات الموردين؟\n"
            "هذه العملية لا يمكن التراجع عنها.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.perform_supplier_transfers()

                # تغيير الحالة إلى مكتملة
                self.status_combo.setCurrentText("مكتملة")

                # تعطيل زر الترحيل
                self.transfer_to_suppliers_btn.setEnabled(False)

                QMessageBox.information(self, "تم الترحيل", "تم ترحيل المبالغ لحسابات الموردين بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في الترحيل:\n{str(e)}")

    def validate_basic_data(self):
        """التحقق من البيانات الأساسية"""
        if not self.amount_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ الحوالة")
            return False

        if not self.currency_combo.currentText():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار العملة")
            return False

        return True

    def validate_complete_data(self):
        """التحقق من اكتمال جميع البيانات"""
        if not self.validate_basic_data():
            return False

        if not self.reference_number_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الرقم المرجعي")
            return False

        if not self.transfer_entity_name_combo.currentText():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار جهة التحويل")
            return False

        return True

    def validate_amount_distribution(self):
        """التحقق من توزيع المبالغ"""
        try:
            total_amount = float(self.amount_input.text())
            distributed_amount = 0.0

            for row in range(self.suppliers_table.rowCount()):
                amount_item = self.suppliers_table.item(row, 1)
                if amount_item:
                    distributed_amount += float(amount_item.text())

            if abs(total_amount - distributed_amount) > 0.01:
                QMessageBox.warning(
                    self, "تحذير",
                    f"إجمالي المبلغ ({total_amount:.2f}) لا يتطابق مع المبلغ الموزع ({distributed_amount:.2f})"
                )
                return False

            return True

        except ValueError:
            QMessageBox.warning(self, "تحذير", "خطأ في قيم المبالغ")
            return False

    def disable_basic_fields(self):
        """تعطيل الحقول الأساسية بعد التأكيد"""
        self.amount_input.setReadOnly(True)
        self.currency_combo.setEnabled(False)
        self.transfer_entity_combo.setEnabled(False)
        self.transfer_entity_name_combo.setEnabled(False)

    def perform_supplier_transfers(self):
        """تنفيذ ترحيل المبالغ للموردين"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء معاملات للموردين
            for row in range(self.suppliers_table.rowCount()):
                supplier_name = self.suppliers_table.item(row, 0).text()
                amount = float(self.suppliers_table.item(row, 1).text())
                currency = self.suppliers_table.item(row, 2).text()
                description = self.suppliers_table.item(row, 3).text()

                # البحث عن معرف المورد
                cursor.execute("SELECT id FROM suppliers WHERE name = ?", (supplier_name,))
                supplier_result = cursor.fetchone()

                if supplier_result:
                    supplier_id = supplier_result[0]

                    # إنشاء معاملة للمورد
                    cursor.execute("""
                        INSERT INTO supplier_transactions (
                            supplier_id, transaction_type, amount, currency,
                            description, reference_number, transaction_date
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        supplier_id, "credit", amount, currency,
                        f"ترحيل من حوالة {self.remittance_number_input.text()}: {description}",
                        self.reference_number_input.text(),
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في ترحيل المبالغ: {e}")
            raise e
