# ProShipment V2.0.0 - نظام إدارة الشحنات المتقدم

نظام شامل لإدارة الشحنات والحوالات مع واجهة مستخدم عربية متقدمة وميزات تتبع حديثة.

## 🆕 الجديد في الإصدار 2.0.0

### 🗄️ دعم Oracle Database
- **دعم كامل لـ Oracle Database** بالإضافة إلى SQLite
- **نقل البيانات التلقائي** من SQLite إلى Oracle
- **إعدادات متقدمة** للأداء والأمان
- **دعم Oracle Cloud Infrastructure (OCI)**

### 🔧 أدوات جديدة
- **معالج إعداد Oracle** تفاعلي
- **أداة نقل البيانات** الشاملة
- **فاحص البيئة** للتأكد من جاهزية النظام
- **واجهة إعدادات قاعدة البيانات** محدثة

## ✨ الميزات الرئيسية

### 📦 إدارة الشحنات
- تتبع الشحنات في الوقت الفعلي
- إدارة حالات الشحن المختلفة
- تقارير شاملة ومفصلة
- دعم الباركود والـ QR Code

### 💰 إدارة الحوالات
- نظام حوالات متقدم مع دعم عدة عملات
- طباعة نماذج الحوالات بالعربية والإنجليزية
- إدارة أسعار الصرف
- تتبع حالة الحوالات

### 🏢 إدارة الموردين
- قاعدة بيانات شاملة للموردين
- تقييم أداء الموردين
- إدارة العقود والاتفاقيات
- تقارير مالية مفصلة

### 📊 التقارير والإحصائيات
- تقارير مالية شاملة
- إحصائيات الأداء
- تحليل البيانات المتقدم
- تصدير للـ Excel و PDF

### 🗺️ التتبع والخرائط
- تتبع مباشر للشحنات
- خرائط تفاعلية
- تنبيهات ذكية
- دعم GPS

## 🖥️ متطلبات النظام

### الحد الأدنى:
- **نظام التشغيل**: Windows 10/11, Linux, macOS
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 4 GB RAM
- **المساحة**: 2 GB مساحة فارغة

### لاستخدام Oracle:
- **Oracle Database**: 11g أو أحدث
- **Oracle Instant Client**: مثبت ومكون
- **الذاكرة**: 8 GB RAM (مستحسن)

## 🚀 التثبيت والإعداد

### 1. تحميل المشروع:
```bash
git clone https://github.com/your-repo/ProShipment.git
cd ProShipment
```

### 2. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

### 3. فحص البيئة:
```bash
python tools/environment_checker.py
```

### 4. إعداد قاعدة البيانات:

#### للاستخدام مع SQLite (افتراضي):
```bash
python main.py
```

#### للاستخدام مع Oracle:
```bash
# تشغيل معالج الإعداد
python tools/oracle_setup_wizard.py

# أو نسخ إعدادات جاهزة
cp config/database_development.json config/database.json
```

### 5. نقل البيانات (اختياري):
```bash
# نقل من SQLite إلى Oracle
python tools/data_migration_tool.py
```

## 📚 الوثائق

- [دليل دمج Oracle الكامل](docs/oracle_integration_guide.md)
- [دليل البدء السريع مع Oracle](docs/quick_start_oracle.md)
- [إعدادات قاعدة البيانات](config/README.md)

## 🔧 الأدوات المساعدة

### فحص البيئة:
```bash
python tools/environment_checker.py
```

### معالج إعداد Oracle:
```bash
python tools/oracle_setup_wizard.py
```

### أداة نقل البيانات:
```bash
python tools/data_migration_tool.py
```

### إنشاء إعدادات نموذجية:
```bash
python config/oracle_samples.py
```

## 🗂️ هيكل المشروع

```
ProShipment/
├── src/                          # الكود المصدري
│   ├── database/                 # إدارة قواعد البيانات
│   │   ├── models.py            # نماذج البيانات
│   │   ├── oracle_config.py     # إعدادات Oracle
│   │   └── universal_database_manager.py  # مدير قواعد البيانات
│   ├── ui/                      # واجهة المستخدم
│   │   ├── dialogs/             # النوافذ الحوارية
│   │   └── main_window.py       # النافذة الرئيسية
│   └── utils/                   # أدوات مساعدة
├── tools/                       # أدوات التطوير والصيانة
│   ├── environment_checker.py   # فاحص البيئة
│   ├── oracle_setup_wizard.py   # معالج إعداد Oracle
│   └── data_migration_tool.py   # أداة نقل البيانات
├── config/                      # ملفات الإعدادات
│   ├── database_*.json          # إعدادات قواعد البيانات
│   └── oracle_samples.py        # إنشاء إعدادات نموذجية
├── docs/                        # الوثائق
├── scripts/                     # سكريبتات الإعداد
├── data/                        # ملفات البيانات
├── logs/                        # ملفات السجلات
└── main.py                      # نقطة البداية
```

## 🔒 الأمان

- **تشفير البيانات الحساسة**
- **دعم SSL/TLS لـ Oracle**
- **إدارة صلاحيات المستخدمين**
- **سجلات مراجعة شاملة**

## 🔄 النسخ الاحتياطي

### SQLite:
```bash
# نسخ تلقائي
python -c "
from src.database.universal_database_manager import create_sqlite_manager
manager = create_sqlite_manager()
manager.backup_database()
"
```

### Oracle:
```bash
# نسخ احتياطي كامل
expdp username/password@host:port/service \
  directory=backup_dir \
  dumpfile=backup.dmp
```

## 🐛 استكشاف الأخطاء

### مشاكل Oracle الشائعة:

#### خطأ "TNS:listener does not currently know of service":
```bash
lsnrctl status
lsnrctl stop && lsnrctl start
```

#### خطأ "cx_Oracle not found":
```bash
pip install cx_Oracle
```

#### خطأ "ORA-12154":
```bash
# تحقق من متغيرات البيئة
echo $ORACLE_HOME
echo $TNS_ADMIN
```

## 📞 الدعم

- **البريد الإلكتروني**: <EMAIL>
- **الوثائق**: [docs/](docs/)
- **السجلات**: [logs/](logs/)

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📈 خارطة الطريق

### الإصدار القادم (2.1.0):
- [ ] دعم PostgreSQL
- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] ذكاء اصطناعي للتنبؤ

### المميزات المخططة:
- [ ] دعم عدة شركات
- [ ] نظام إشعارات متقدم
- [ ] تكامل مع خدمات الشحن
- [ ] لوحة تحكم تحليلية

---

## 🏆 الإنجازات

- ✅ **دعم كامل للغة العربية**
- ✅ **واجهة مستخدم حديثة ومتجاوبة**
- ✅ **دعم قواعد بيانات متعددة**
- ✅ **أدوات نقل وصيانة متقدمة**
- ✅ **نظام تقارير شامل**

---

*ProShipment V2.0.0 - تطوير فريق ProShipment*
*آخر تحديث: ديسمبر 2024*
