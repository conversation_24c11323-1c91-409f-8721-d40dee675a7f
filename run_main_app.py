#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق الرئيسي - SHIPMENT ERP Main Application Runner
الواجهة الرئيسية الجديدة للنظام
"""

import sys
import os

def main():
    """الدالة الرئيسية"""
    print("🚀 تشغيل نظام إدارة الشحنات المتقدم")
    print("=" * 50)
    print("📋 الواجهة الرئيسية الجديدة")
    print("   • واجهة رئيسية محدثة")
    print("   • مكتبة قوالب متكاملة")
    print("   • نماذج تجريبية جاهزة")
    print("   • إدارة متقدمة للنماذج")
    print("   • شاشة بداية احترافية")
    print("=" * 50)
    
    try:
        # استيراد التطبيق الرئيسي
        from main_application import main as main_app
        
        print("✅ تم تحميل التطبيق الرئيسي")
        
        # تشغيل التطبيق الرئيسي
        return main_app()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق الرئيسي: {e}")
        print("🔄 محاولة تشغيل النموذج الأساسي كبديل...")
        
        try:
            # تشغيل النموذج الأساسي كبديل
            from template_library import نموذج_ادخال_اساسي, Templates
            from PySide6.QtWidgets import QApplication, QMessageBox
            from PySide6.QtCore import Qt
            from PySide6.QtGui import QFont
            
            app = QApplication(sys.argv)
            
            # إعداد التطبيق
            font = QFont("Arial", 10)
            app.setFont(font)
            app.setLayoutDirection(Qt.RightToLeft)
            Templates.setup_app(app)
            
            # إنشاء النموذج
            form = نموذج_ادخال_اساسي(fullscreen=False)
            form.show()
            
            # رسالة تنبيه
            QMessageBox.information(
                form, "تشغيل النموذج الأساسي",
                "⚠️ تم تشغيل النموذج الأساسي كبديل للتطبيق الرئيسي.\n\n"
                "للحصول على التجربة الكاملة:\n"
                "• تأكد من وجود ملف main_application.py\n"
                "• تأكد من تثبيت جميع المتطلبات\n"
                "• شغل python main_application.py مباشرة\n\n"
                "يمكنك الآن استخدام النموذج الأساسي."
            )
            
            print("✅ تم تشغيل النموذج الأساسي كبديل")
            return app.exec()
            
        except Exception as e2:
            print(f"❌ خطأ في تشغيل النموذج الأساسي: {e2}")
            print("💡 تأكد من تثبيت PySide6 وجميع المتطلبات")
            return 1
    
    except Exception as e:
        print(f"❌ خطأ عام في التشغيل: {e}")
        print("💡 للمساعدة:")
        print("   • تأكد من تثبيت Python 3.8+")
        print("   • تأكد من تثبيت PySide6")
        print("   • تأكد من وجود جميع الملفات المطلوبة")
        return 1

if __name__ == "__main__":
    sys.exit(main())
