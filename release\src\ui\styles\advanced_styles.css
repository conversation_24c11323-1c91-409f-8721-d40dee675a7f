/* ملف الأنماط المتقدمة للواجهة الرئيسية */
/* Advanced Styles for Main Interface */

/* الخلفية الرئيسية المتدرجة */
.main-background {
    background: linear-gradient(135deg, 
        #667eea 0%, 
        #764ba2 30%, 
        #f093fb 70%, 
        #f5576c 100%);
    background-attachment: fixed;
}

/* أنماط الشفافية والضبابية */
.glass-effect {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* تأثيرات الظلال المتقدمة */
.advanced-shadow {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.hover-shadow {
    transition: all 0.3s ease;
}

.hover-shadow:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* الأزرار المتدرجة */
.gradient-button-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    color: white;
    font-weight: bold;
    transition: all 0.3s ease;
}

.gradient-button-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
}

.gradient-button-secondary {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.gradient-button-success {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.gradient-button-info {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.gradient-button-warning {
    background: linear-gradient(135deg, #ffecd2, #fcb69f);
}

.gradient-button-light {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
}

/* النصوص مع التأثيرات */
.text-shadow-light {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.text-shadow-medium {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.text-shadow-strong {
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
}

.letter-spacing-wide {
    letter-spacing: 1px;
}

.letter-spacing-normal {
    letter-spacing: 0.5px;
}

/* الخطوط الفاصلة المتدرجة */
.gradient-separator {
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(44, 62, 80, 0.3) 50%, 
        transparent 100%);
    border: none;
    height: 2px;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* عناصر الأخبار */
.news-item {
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0.9) 0%, 
        rgba(248, 249, 250, 0.9) 100%);
    border-radius: 12px;
    padding: 15px;
    margin: 5px 0px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.news-item:hover {
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 1.0) 0%, 
        rgba(248, 249, 250, 1.0) 100%);
    transform: translateX(5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* شريط التمرير المخصص */
.custom-scrollbar::-webkit-scrollbar {
    width: 14px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 7px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, 
        rgba(102, 126, 234, 0.8), 
        rgba(118, 75, 162, 0.8));
    border-radius: 7px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

/* القوائم المتقدمة */
.advanced-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.advanced-menu-item {
    transition: all 0.2s ease;
}

.advanced-menu-item:hover {
    background: linear-gradient(90deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-1px);
}

/* شريط الأدوات المتقدم */
.advanced-toolbar {
    background: linear-gradient(90deg, 
        rgba(52, 73, 94, 0.9) 0%, 
        rgba(44, 62, 80, 0.9) 50%, 
        rgba(52, 73, 94, 0.9) 100%);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.advanced-toolbar-button {
    background: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.advanced-toolbar-button:hover {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.25), 
        rgba(255, 255, 255, 0.15));
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* شريط الحالة المتقدم */
.advanced-statusbar {
    background: linear-gradient(90deg, 
        rgba(236, 240, 241, 0.95), 
        rgba(189, 195, 199, 0.95));
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(149, 165, 166, 0.3);
    color: #2c3e50;
    font-weight: 500;
}

/* الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* الاستجابة للشاشات المختلفة */
@media (max-width: 1200px) {
    .main-container {
        padding: 20px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .buttons-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .buttons-grid {
        grid-template-columns: 1fr;
    }
}
