# قالب النموذج - SHIPMENT ERP Form Template

## 🎯 نظرة عامة

تم إنشاء هذا القالب بناءً على **فحص دقيق وشامل** للنموذج المرفق في الصورة. القالب **مطابق تماماً** للتصميم الأصلي ويحتوي على جميع العناصر والمتغيرات اللازمة للاستخدام المباشر في التطبيق.

## 📋 الملفات المتضمنة

### الملفات الأساسية:
- **`form_template.py`** - القالب الرئيسي (829 سطر)
- **`run_form_template.py`** - ملف التشغيل المباشر
- **`example_usage.py`** - مثال عملي للاستخدام
- **`test_form_template.py`** - ملف الاختبار الشامل

### ملفات التوثيق:
- **`دليل_قالب_النموذج.md`** - الدليل الشامل
- **`ملخص_قالب_النموذج.md`** - ملخص المشروع
- **`FORM_TEMPLATE_README.md`** - هذا الملف

## 🚀 التشغيل السريع

### 1. التشغيل المباشر:
```bash
python run_form_template.py
```

### 2. تشغيل المثال العملي:
```bash
python example_usage.py
```

### 3. تشغيل الاختبار:
```bash
python test_form_template.py
```

## 💻 الاستخدام في التطبيق

### استيراد القالب:
```python
from form_template import FormTemplate

# إنشاء النموذج
form = FormTemplate()
form.show()
```

### الحصول على المتغيرات:
```python
# الحصول على جميع المتغيرات
variables = form.get_form_variables()

# الوصول للحقول
document_number = variables['document_number']
customer_name = variables['customer_name']
save_button = variables['save_btn']

# استخدام الحقول
document_number.setText("SHP-2024-001")
customer_name.setText("اسم العميل")
save_button.clicked.connect(my_save_function)
```

### جمع البيانات:
```python
# جمع جميع البيانات
data = form.collect_form_data()

# الوصول للبيانات
print(f"رقم المستند: {data['document_number']}")
print(f"اسم العميل: {data['customer_name']}")
print(f"نوع الشحنة: {data['shipment_type']}")
```

## 📊 المتغيرات المتاحة

### حقول البيانات الأساسية (12 حقل):
```python
self.document_number      # رقم المستند
self.document_date        # تاريخ المستند
self.customer_name        # اسم العميل
self.customer_number      # رقم العميل
self.customer_address     # عنوان العميل
self.shipment_type        # نوع الشحنة
self.shipment_status      # حالة الشحنة
self.weight              # الوزن
self.value               # القيمة
self.sender_city         # مدينة المرسل
self.receiver_city       # مدينة المستقبل
self.notes               # الملاحظات
```

### خيارات الطباعة (3 خيارات):
```python
self.print_header        # طباعة الرأسية
self.print_footer        # طباعة التذييل
self.print_logo          # طباعة الشعار
```

### إعدادات التحقق (5 إعدادات):
```python
self.validate_required   # التحقق من الحقول المطلوبة
self.validate_format     # التحقق من تنسيق البيانات
self.auto_save          # الحفظ التلقائي
self.show_tooltips      # إظهار التلميحات
self.highlight_errors   # تمييز الأخطاء
```

### أزرار العمليات (7 أزرار):
```python
self.save_btn           # حفظ
self.clear_btn          # مسح
self.print_btn          # طباعة
self.close_btn          # إغلاق
self.template_btn       # تحميل قالب
self.export_btn         # تصدير البيانات
self.import_btn         # استيراد البيانات
```

## ⚙️ الوظائف المتاحة

### وظائف إدارة البيانات:
```python
form.collect_form_data()     # جمع البيانات
form.validate_form_data()    # التحقق من البيانات
form.clear_form()           # مسح النموذج
form.get_form_variables()   # الحصول على المتغيرات
```

### وظائف العمليات:
```python
form.save_form()            # حفظ النموذج
form.print_form()           # طباعة النموذج
form.export_data()          # تصدير البيانات
form.import_data()          # استيراد البيانات
```

## 🎨 التصميم والأنماط

### نظام الألوان:
- **الأساسي**: `#4A90E2` (أزرق)
- **التركيز**: `#357ABD` (أزرق داكن)
- **الخلفية**: `#F5F5F5` (رمادي فاتح)
- **الحدود**: `#E0E0E0` (رمادي)

### الخطوط:
- **الأساسي**: Arial 11px
- **العناوين**: Arial 12px Bold
- **الأزرار**: Arial 11px Bold

## 🔧 التخصيص

### إضافة حقول جديدة:
```python
# في دالة create_main_form
self.new_field = QLineEdit()
form_layout.addWidget(QLabel("حقل جديد:"), row, 0)
form_layout.addWidget(self.new_field, row, 1)
```

### إضافة وظائف جديدة:
```python
def new_function(self):
    """وظيفة جديدة"""
    # منطق الوظيفة
    self.statusBar().showMessage("تم تنفيذ الوظيفة الجديدة")
```

### تعديل الأنماط:
```python
# في دالة setup_styles
self.setStyleSheet("""
    /* أنماط جديدة */
""")
```

## 🧪 الاختبار

### تشغيل الاختبار الشامل:
```bash
python test_form_template.py
```

### نتائج الاختبار المتوقعة:
```
✅ Python 3.8+
✅ PySide6
✅ form_template.py
✅ run_form_template.py
✅ تم إنشاء النموذج بنجاح
✅ تم الحصول على 30 متغير
✅ تم جمع البيانات بنجاح
🎉 جميع الاختبارات نجحت!
```

## 📖 أمثلة عملية

### مثال 1: إنشاء نموذج بسيط
```python
from form_template import FormTemplate
from PySide6.QtWidgets import QApplication

app = QApplication([])
form = FormTemplate()

# ملء البيانات
form.document_number.setText("SHP-001")
form.customer_name.setText("شركة النقل")

form.show()
app.exec()
```

### مثال 2: معالجة البيانات
```python
def process_form_data():
    data = form.collect_form_data()
    
    if form.validate_form_data():
        print(f"حفظ البيانات: {data['document_number']}")
        # منطق الحفظ هنا
    else:
        print("خطأ في البيانات")

form.save_btn.clicked.connect(process_form_data)
```

### مثال 3: تخصيص الأحداث
```python
def on_customer_changed():
    customer = form.customer_name.text()
    form.statusBar().showMessage(f"العميل: {customer}")

form.customer_name.textChanged.connect(on_customer_changed)
```

## 📋 قائمة المراجعة

### العناصر المطابقة للصورة:
- [x] شريط القوائم (ملف، تحرير، عرض، أدوات)
- [x] شريط الأدوات مع الأيقونات
- [x] نموذج البيانات الرئيسي (الجانب الأيمن)
- [x] لوحة الخيارات والإعدادات (الجانب الأيسر)
- [x] جدول المعلومات السفلي
- [x] شريط الحالة مع المعلومات
- [x] الألوان والأنماط المطابقة
- [x] التخطيط والتقسيم المطابق

### الوظائف المكتملة:
- [x] إدخال وتحرير البيانات
- [x] التحقق من صحة البيانات
- [x] حفظ ومسح النموذج
- [x] طباعة وتصدير البيانات
- [x] إعدادات الطباعة والتحقق
- [x] معالجة الأحداث والإشارات
- [x] شريط حالة تفاعلي

## 🎯 الاستخدام المتقدم

### تكامل مع قاعدة البيانات:
```python
def save_to_database(self):
    data = self.collect_form_data()
    
    # اتصال قاعدة البيانات
    connection = create_database_connection()
    
    # إدراج البيانات
    query = """
    INSERT INTO shipments (document_number, customer_name, ...)
    VALUES (?, ?, ...)
    """
    
    connection.execute(query, (
        data['document_number'],
        data['customer_name'],
        # باقي البيانات...
    ))
    
    connection.commit()
```

### تصدير لملفات مختلفة:
```python
def export_to_excel(self):
    import pandas as pd
    
    data = self.collect_form_data()
    df = pd.DataFrame([data])
    df.to_excel("shipment_data.xlsx", index=False)
```

## 🔗 الروابط المفيدة

- **الدليل الشامل**: `دليل_قالب_النموذج.md`
- **ملخص المشروع**: `ملخص_قالب_النموذج.md`
- **المثال العملي**: `example_usage.py`
- **ملف الاختبار**: `test_form_template.py`

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

1. راجع الدليل الشامل
2. شغل ملف الاختبار
3. جرب المثال العملي
4. تحقق من التوثيق

## 📄 الترخيص

هذا القالب جزء من مشروع SHIPMENT ERP ومتاح للاستخدام في التطبيق.

---

## ✅ النتيجة النهائية

🎉 **قالب مطابق 100% للتصميم المرفق مع جميع المتغيرات جاهزة للاستخدام!**

**📅 تاريخ الإنشاء**: 2024  
**👨‍💻 المطور**: فريق SHIPMENT Solutions  
**🎯 الحالة**: مكتمل وجاهز للاستخدام ✅
