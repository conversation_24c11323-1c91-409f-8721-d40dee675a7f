"""
نافذة تعبئة البيانات المفقودة في الشحنات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                               QWidget, QLabel, QTableWidget, QTableWidgetItem,
                               QPushButton, QTextEdit, QProgressBar, QGroupBox,
                               QFormLayout, QSpinBox, QDoubleSpinBox, QCheckBox,
                               QComboBox, QLineEdit, QMessageBox, QHeaderView,
                               QSplitter, QFrame)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QIcon

from database.database_manager import DatabaseManager
from database.models import Shipment

class DataFillerWorker(QThread):
    """عامل خيط منفصل لتعبئة البيانات"""
    progress_updated = Signal(int, str)
    analysis_completed = Signal(dict)
    filling_completed = Signal(dict)
    web_search_completed = Signal(dict)
    smart_search_completed = Signal(dict)
    error_occurred = Signal(str)

    def __init__(self, operation_type, **kwargs):
        super().__init__()
        self.operation_type = operation_type
        self.kwargs = kwargs
        self.filler = None
        self.web_scraper = None

    def init_services(self):
        """تهيئة الخدمات"""
        if not self.filler:
            from utils.shipment_data_filler import ShipmentDataFiller
            self.filler = ShipmentDataFiller()
        if not self.web_scraper:
            from services.web_scraping_service import WebScrapingService
            self.web_scraper = WebScrapingService()

    def run(self):
        try:
            # تهيئة الخدمات عند الحاجة
            self.init_services()

            if self.operation_type == "analyze":
                self.progress_updated.emit(25, "تحليل البيانات...")
                analysis = self.filler.analyze_missing_data()
                self.progress_updated.emit(100, "اكتمل التحليل")
                self.analysis_completed.emit(analysis)
                
            elif self.operation_type == "fill_single":
                shipment_id = self.kwargs.get('shipment_id')
                confidence = self.kwargs.get('confidence', 0.8)
                
                self.progress_updated.emit(50, f"تعبئة الشحنة {shipment_id}...")
                result = self.filler.auto_fill_missing_fields(shipment_id, confidence)
                self.progress_updated.emit(100, "اكتملت التعبئة")
                self.filling_completed.emit(result)
                
            elif self.operation_type == "fill_batch":
                shipment_ids = self.kwargs.get('shipment_ids', [])
                confidence = self.kwargs.get('confidence', 0.8)
                
                results = []
                for i, shipment_id in enumerate(shipment_ids):
                    progress = int((i / len(shipment_ids)) * 100)
                    self.progress_updated.emit(progress, f"تعبئة الشحنة {shipment_id}...")
                    
                    result = self.filler.auto_fill_missing_fields(shipment_id, confidence)
                    results.append(result)
                
                self.progress_updated.emit(100, "اكتملت التعبئة المجمعة")
                self.filling_completed.emit({'batch_results': results})

            elif self.operation_type == "web_search":
                container_number = self.kwargs.get('container_number')
                bill_of_lading = self.kwargs.get('bill_of_lading')
                carrier_name = self.kwargs.get('carrier_name')

                self.progress_updated.emit(25, "البحث عبر الإنترنت...")

                # تشغيل البحث غير المتزامن
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    results = loop.run_until_complete(
                        self.web_scraper.search_all_carriers(
                            container_number=container_number,
                            bill_of_lading=bill_of_lading,
                            carrier_name=carrier_name
                        )
                    )

                    self.progress_updated.emit(75, "معالجة النتائج...")

                    # استخراج أفضل البيانات
                    best_data = self.web_scraper.extract_best_data(results)

                    self.progress_updated.emit(100, "اكتمل البحث")
                    self.web_search_completed.emit({
                        'results': results,
                        'best_data': best_data,
                        'search_params': {
                            'container_number': container_number,
                            'bill_of_lading': bill_of_lading,
                            'carrier_name': carrier_name
                        }
                    })

                finally:
                    loop.close()

            elif self.operation_type == "smart_search":
                container_number = self.kwargs.get('container_number')
                shipping_company = self.kwargs.get('shipping_company')
                bill_of_lading = self.kwargs.get('bill_of_lading')
                carrier_name = self.kwargs.get('carrier_name')

                self.progress_updated.emit(20, "البحث في قاعدة البيانات...")

                # البحث في قاعدة البيانات أولاً
                search_criteria = {}
                if container_number:
                    search_criteria['container_number'] = container_number
                if shipping_company:
                    search_criteria['shipping_company'] = shipping_company
                if bill_of_lading:
                    search_criteria['bill_of_lading'] = bill_of_lading

                shipments = self.filler.search_similar_shipments(search_criteria)

                self.progress_updated.emit(50, "البحث عبر الإنترنت...")

                # البحث عبر الإنترنت
                web_results = []
                if container_number:
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        web_results = loop.run_until_complete(
                            self.web_scraper.search_all_carriers(
                                container_number=container_number,
                                bill_of_lading=bill_of_lading,
                                carrier_name=carrier_name
                            )
                        )
                    except Exception as web_error:
                        print(f"خطأ في البحث عبر الإنترنت: {web_error}")
                    finally:
                        loop.close()

                self.progress_updated.emit(80, "معالجة النتائج...")

                # تحويل النتائج إلى تنسيق مناسب
                formatted_shipments = []
                for shipment in shipments:
                    formatted_shipments.append({
                        'id': shipment.get('shipment_id'),
                        'shipment_number': shipment.get('shipment_number', ''),
                        'shipping_company': shipment.get('shipping_company', ''),
                        'container_number': shipment.get('container_number', ''),
                        'bill_of_lading': shipment.get('bill_of_lading', ''),
                        'shipment_status': shipment.get('shipment_status', ''),
                        'similarity': shipment.get('similarity_score', 0) * 100
                    })

                formatted_web_results = []
                for result in web_results:
                    if hasattr(result, '__dict__'):
                        formatted_web_results.append({
                            'carrier': getattr(result, 'carrier', ''),
                            'status': getattr(result, 'status', ''),
                            'vessel_name': getattr(result, 'vessel_name', ''),
                            'voyage_number': getattr(result, 'voyage_number', ''),
                            'port_of_loading': getattr(result, 'port_of_loading', ''),
                            'port_of_discharge': getattr(result, 'port_of_discharge', ''),
                            'departure_date': getattr(result, 'departure_date', ''),
                            'arrival_date': getattr(result, 'arrival_date', ''),
                            'shipping_method': getattr(result, 'shipping_method', ''),
                            'tracking_number': getattr(result, 'tracking_number', ''),
                            'dhl_number': getattr(result, 'dhl_number', ''),
                            'final_destination': getattr(result, 'final_destination', '')
                        })

                self.progress_updated.emit(100, "اكتمل البحث الذكي")
                self.smart_search_completed.emit({
                    'shipments': formatted_shipments,
                    'web_data': formatted_web_results,
                    'search_params': {
                        'container_number': container_number,
                        'shipping_company': shipping_company,
                        'bill_of_lading': bill_of_lading,
                        'carrier_name': carrier_name
                    }
                })

        except Exception as e:
            self.error_occurred.emit(str(e))

class ShipmentDataFillerDialog(QDialog):
    """نافذة تعبئة البيانات المفقودة"""
    
    def __init__(self, db_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager if db_manager else DatabaseManager()
        self.filler = None
        self.web_scraper = None
        self.worker = None
        self.analysis_data = None
        self.web_search_results = []

        self.setup_ui()
        self.setup_connections()

        # تحديث البيانات عند فتح النافذة
        QTimer.singleShot(500, self.delayed_init)

    def delayed_init(self):
        """تهيئة مؤجلة للخدمات والبيانات"""
        try:
            # تهيئة الخدمات
            self.init_services()
            # تحليل البيانات
            self.analyze_data()
        except Exception as e:
            print(f"خطأ في التهيئة المؤجلة: {e}")

    def init_services(self):
        """تهيئة الخدمات التي تحتاج QApplication"""
        try:
            if not self.filler:
                from utils.shipment_data_filler import ShipmentDataFiller
                self.filler = ShipmentDataFiller()
            if not self.web_scraper:
                from services.web_scraping_service import WebScrapingService
                self.web_scraper = WebScrapingService()
        except Exception as e:
            print(f"تحذير: فشل في تهيئة بعض الخدمات: {e}")

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # يمكن إضافة اتصالات إضافية هنا عند الحاجة
        pass

    def update_progress(self, value, message):
        """تحديث شريط التقدم"""
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setValue(value)
        if hasattr(self, 'status_label'):
            self.status_label.setText(message)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔧 تعبئة البيانات المفقودة في الشحنات")
        self.setModal(True)
        self.resize(1000, 700)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # شريط العنوان
        title_label = QLabel("🔧 نظام تعبئة البيانات المفقودة في الشحنات")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; padding: 10px; background-color: #ecf0f1; border-radius: 5px;")
        main_layout.addWidget(title_label)
        
        # التبويبات
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # تبويب التحليل (مخفي)
        # self.create_analysis_tab()

        # تبويب البحث والتعبئة المدمج (يشمل البحث عبر الإنترنت)
        self.create_integrated_search_fill_tab()

        # تبويب التعبئة المجمعة
        self.create_batch_fill_tab()

        # تبويب الإعدادات
        self.create_settings_tab()
        
        # شريط التقدم والحالة
        status_layout = QHBoxLayout()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("جاهز")
        self.status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        status_layout.addWidget(self.status_label)
        
        main_layout.addLayout(status_layout)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("🔄 تحديث البيانات")
        self.refresh_button.clicked.connect(self.analyze_data)
        buttons_layout.addWidget(self.refresh_button)
        
        buttons_layout.addStretch()
        
        self.close_button = QPushButton("إغلاق")
        self.close_button.clicked.connect(self.close)
        buttons_layout.addWidget(self.close_button)
        
        main_layout.addLayout(buttons_layout)
    
    def create_analysis_tab(self):
        """إنشاء تبويب التحليل"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات عامة
        info_group = QGroupBox("📊 معلومات عامة")
        info_layout = QFormLayout(info_group)
        
        self.total_shipments_label = QLabel("0")
        info_layout.addRow("إجمالي الشحنات:", self.total_shipments_label)
        
        layout.addWidget(info_group)
        
        # جدول الحقول المفقودة
        missing_group = QGroupBox("📋 الحقول المفقودة")
        missing_layout = QVBoxLayout(missing_group)
        
        self.missing_fields_table = QTableWidget()
        self.missing_fields_table.setColumnCount(3)
        self.missing_fields_table.setHorizontalHeaderLabels(["الحقل", "عدد المفقود", "النسبة المئوية"])
        self.missing_fields_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        missing_layout.addWidget(self.missing_fields_table)
        
        layout.addWidget(missing_group)
        
        self.tab_widget.addTab(tab, "📊 التحليل")

    def create_integrated_search_fill_tab(self):
        """إنشاء تبويب البحث والتعبئة المدمج مع البحث عبر الإنترنت"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # معلومات التبويب
        info_label = QLabel("🔍 البحث الذكي والتعبئة التلقائية للبيانات المفقودة")
        info_label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 10px; background-color: #ecf0f1; border-radius: 5px;")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)

        # منطقة البحث المدمجة
        search_group = QGroupBox("🔍 البحث عن الشحنات والتعبئة التلقائية")
        search_layout = QFormLayout(search_group)

        # حقل رقم الحاوية (الحقل الرئيسي)
        container_layout = QHBoxLayout()
        self.search_container_number = QLineEdit()
        self.search_container_number.setPlaceholderText("أدخل رقم الحاوية للبحث التلقائي...")
        self.search_container_number.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #3498db;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QLineEdit:focus {
                border-color: #2980b9;
            }
        """)
        container_layout.addWidget(self.search_container_number)

        # زر البحث الذكي
        self.smart_search_button = QPushButton("🚀 بحث ذكي وتعبئة")
        self.smart_search_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.smart_search_button.clicked.connect(self.start_smart_search)
        container_layout.addWidget(self.smart_search_button)

        search_layout.addRow("رقم الحاوية:", container_layout)

        # حقول البحث الإضافية
        self.search_shipping_company = QLineEdit()
        self.search_shipping_company.setPlaceholderText("اسم شركة الشحن (اختياري)...")
        search_layout.addRow("شركة الشحن:", self.search_shipping_company)

        self.search_bill_of_lading = QLineEdit()
        self.search_bill_of_lading.setPlaceholderText("رقم بوليصة الشحن (اختياري)...")
        search_layout.addRow("بوليصة الشحن:", self.search_bill_of_lading)

        # شركة الملاحة للبحث عبر الإنترنت
        self.web_carrier_name = QComboBox()
        self.web_carrier_name.addItems([
            "تحديد تلقائي",
            "Maersk",
            "MSC",
            "COSCO",
            "Evergreen",
            "CMA CGM",
            "OOCL",
            "Hapag-Lloyd",
            "ONE",
            "Yang Ming",
            "HMM"
        ])
        search_layout.addRow("شركة الملاحة:", self.web_carrier_name)

        # أزرار إضافية
        additional_buttons_layout = QHBoxLayout()

        self.clear_search_button = QPushButton("🗑️ مسح")
        self.clear_search_button.clicked.connect(self.clear_integrated_search)
        additional_buttons_layout.addWidget(self.clear_search_button)

        additional_buttons_layout.addStretch()

        search_layout.addRow("", additional_buttons_layout)

        layout.addWidget(search_group)

        # جدول النتائج المدمج
        results_group = QGroupBox("📋 نتائج البحث والبيانات المستخرجة")
        results_layout = QVBoxLayout(results_group)

        # تبويبات فرعية للنتائج
        self.results_tab_widget = QTabWidget()

        # تبويب الشحنات الموجودة
        self.create_shipments_results_tab()

        # تبويب البيانات من الإنترنت
        self.create_web_results_tab()

        results_layout.addWidget(self.results_tab_widget)

        layout.addWidget(results_group)

        self.tab_widget.addTab(tab, "🔍 البحث والتعبئة الذكية")

    def create_shipments_results_tab(self):
        """إنشاء تبويب نتائج الشحنات الموجودة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # جدول الشحنات الموجودة
        self.search_results_table = QTableWidget()
        self.search_results_table.setColumnCount(7)
        self.search_results_table.setHorizontalHeaderLabels([
            "رقم الشحنة", "شركة الشحن", "رقم الحاوية",
            "بوليصة الشحن", "الحالة", "درجة التشابه", "إجراءات"
        ])
        self.search_results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.search_results_table.setAlternatingRowColors(True)
        self.search_results_table.setSelectionBehavior(QTableWidget.SelectRows)
        layout.addWidget(self.search_results_table)

        self.results_tab_widget.addTab(tab, "📦 الشحنات الموجودة")

    def create_web_results_tab(self):
        """إنشاء تبويب نتائج البحث عبر الإنترنت"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # معلومات البحث
        info_layout = QHBoxLayout()
        self.web_search_status = QLabel("جاهز للبحث عبر الإنترنت")
        self.web_search_status.setStyleSheet("color: #7f8c8d; font-style: italic;")
        info_layout.addWidget(self.web_search_status)
        info_layout.addStretch()

        layout.addLayout(info_layout)

        # جدول النتائج من الإنترنت
        self.web_results_table = QTableWidget()
        self.web_results_table.setColumnCount(10)
        self.web_results_table.setHorizontalHeaderLabels([
            "شركة الملاحة", "الحالة", "اسم السفينة", "رقم الرحلة",
            "ميناء المغادرة", "ميناء الوصول", "تاريخ المغادرة", "تاريخ الوصول",
            "نوع الشحن", "إجراءات"
        ])

        # تخصيص الجدول
        header = self.web_results_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.web_results_table.setAlternatingRowColors(True)
        self.web_results_table.setSelectionBehavior(QTableWidget.SelectRows)
        layout.addWidget(self.web_results_table)

        # أزرار العمليات
        operations_layout = QHBoxLayout()

        self.apply_web_data_button = QPushButton("✅ تطبيق البيانات على الشحنة")
        self.apply_web_data_button.setEnabled(False)
        self.apply_web_data_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.apply_web_data_button.clicked.connect(self.apply_web_data_to_shipment)
        operations_layout.addWidget(self.apply_web_data_button)

        self.export_web_data_button = QPushButton("📤 تصدير البيانات")
        self.export_web_data_button.setEnabled(False)
        operations_layout.addWidget(self.export_web_data_button)

        operations_layout.addStretch()

        layout.addLayout(operations_layout)

        self.results_tab_widget.addTab(tab, "🌐 بيانات الإنترنت")


    def create_batch_fill_tab(self):
        """إنشاء تبويب التعبئة المجمعة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # إعدادات التعبئة المجمعة
        settings_group = QGroupBox("⚙️ إعدادات التعبئة المجمعة")
        settings_layout = QFormLayout(settings_group)
        
        self.batch_confidence_spinbox = QDoubleSpinBox()
        self.batch_confidence_spinbox.setRange(0.1, 1.0)
        self.batch_confidence_spinbox.setSingleStep(0.1)
        self.batch_confidence_spinbox.setValue(0.8)
        self.batch_confidence_spinbox.setSuffix("%")
        settings_layout.addRow("حد الثقة:", self.batch_confidence_spinbox)
        
        self.max_shipments_spinbox = QSpinBox()
        self.max_shipments_spinbox.setRange(1, 1000)
        self.max_shipments_spinbox.setValue(50)
        settings_layout.addRow("الحد الأقصى للشحنات:", self.max_shipments_spinbox)
        
        batch_buttons_layout = QHBoxLayout()
        self.start_batch_fill_button = QPushButton("🚀 بدء التعبئة المجمعة")
        self.start_batch_fill_button.clicked.connect(self.start_batch_fill)
        batch_buttons_layout.addWidget(self.start_batch_fill_button)
        
        settings_layout.addRow(batch_buttons_layout)
        
        layout.addWidget(settings_group)
        
        # سجل التعبئة
        log_group = QGroupBox("📝 سجل التعبئة")
        log_layout = QVBoxLayout(log_group)
        
        self.fill_log_text = QTextEdit()
        self.fill_log_text.setReadOnly(True)
        self.fill_log_text.setMaximumHeight(200)
        log_layout.addWidget(self.fill_log_text)
        
        layout.addWidget(log_group)
        
        self.tab_widget.addTab(tab, "📦 التعبئة المجمعة")
    
    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        settings_group = QGroupBox("⚙️ إعدادات النظام")
        settings_layout = QFormLayout(settings_group)
        
        self.confidence_threshold_spinbox = QDoubleSpinBox()
        self.confidence_threshold_spinbox.setRange(0.1, 1.0)
        self.confidence_threshold_spinbox.setSingleStep(0.1)
        self.confidence_threshold_spinbox.setValue(0.7)
        settings_layout.addRow("حد الثقة الافتراضي:", self.confidence_threshold_spinbox)
        
        self.auto_backup_checkbox = QCheckBox("إنشاء نسخة احتياطية تلقائياً")
        self.auto_backup_checkbox.setChecked(True)
        settings_layout.addRow(self.auto_backup_checkbox)
        
        layout.addWidget(settings_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "⚙️ الإعدادات")




    
    def analyze_data(self):
        """تحليل البيانات"""
        if self.worker and self.worker.isRunning():
            return
        
        self.progress_bar.setVisible(True)
        self.status_label.setText("جاري التحليل...")
        
        self.worker = DataFillerWorker("analyze")
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.analysis_completed.connect(self.on_analysis_completed)
        self.worker.error_occurred.connect(self.on_error)
        self.worker.start()
    
    def on_analysis_completed(self, analysis):
        """عند اكتمال التحليل"""
        self.analysis_data = analysis
        self.progress_bar.setVisible(False)
        self.status_label.setText("جاهز")
        
        # تحديث المعلومات العامة (إذا كان التبويب موجود)
        if hasattr(self, 'total_shipments_label'):
            self.total_shipments_label.setText(str(analysis['total_shipments']))

        # تحديث جدول الحقول المفقودة (إذا كان التبويب موجود)
        if hasattr(self, 'missing_fields_table'):
            missing_fields = analysis['missing_fields']
            self.missing_fields_table.setRowCount(len(missing_fields))

            row = 0
            for field, data in missing_fields.items():
                if data['count'] > 0:  # عرض الحقول المفقودة فقط
                    self.missing_fields_table.setItem(row, 0, QTableWidgetItem(field))
                    self.missing_fields_table.setItem(row, 1, QTableWidgetItem(str(data['count'])))
                    self.missing_fields_table.setItem(row, 2, QTableWidgetItem(f"{data['percentage']:.1f}%"))
                    row += 1

            self.missing_fields_table.setRowCount(row)

    def start_smart_search(self):
        """بدء البحث الذكي المدمج"""
        # التحقق من تهيئة الخدمات
        if not self.filler or not self.web_scraper:
            QMessageBox.warning(self, "خطأ", "لم يتم تهيئة الخدمات بشكل صحيح")
            return

        container_number = self.search_container_number.text().strip()

        if not container_number:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الحاوية للبحث الذكي")
            return

        if self.worker and self.worker.isRunning():
            return

        # تحديث واجهة المستخدم
        self.progress_bar.setVisible(True)
        self.status_label.setText("جاري البحث الذكي...")
        self.smart_search_button.setEnabled(False)
        self.web_search_status.setText("جاري البحث في قاعدة البيانات والإنترنت...")

        # الحصول على معايير البحث الإضافية
        shipping_company = self.search_shipping_company.text().strip()
        bill_of_lading = self.search_bill_of_lading.text().strip()
        carrier_name = self.web_carrier_name.currentText()

        if carrier_name == "تحديد تلقائي":
            carrier_name = None

        # بدء البحث المدمج
        self.worker = DataFillerWorker(
            "smart_search",
            container_number=container_number,
            shipping_company=shipping_company,
            bill_of_lading=bill_of_lading,
            carrier_name=carrier_name
        )
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.smart_search_completed.connect(self.on_smart_search_completed)
        self.worker.error_occurred.connect(self.on_error)
        self.worker.start()

    def clear_integrated_search(self):
        """مسح البحث المدمج"""
        self.search_container_number.clear()
        self.search_shipping_company.clear()
        self.search_bill_of_lading.clear()
        self.web_carrier_name.setCurrentIndex(0)
        self.search_results_table.setRowCount(0)
        self.web_results_table.setRowCount(0)
        self.web_search_status.setText("جاهز للبحث عبر الإنترنت")
        self.apply_web_data_button.setEnabled(False)
        self.export_web_data_button.setEnabled(False)

    def on_smart_search_completed(self, results):
        """معالجة نتائج البحث الذكي"""
        try:
            # تحديث نتائج الشحنات الموجودة
            if 'shipments' in results and results['shipments']:
                self.populate_shipments_table(results['shipments'])
                self.results_tab_widget.setCurrentIndex(0)  # التبديل لتبويب الشحنات

            # تحديث نتائج البحث عبر الإنترنت
            if 'web_data' in results and results['web_data']:
                self.populate_web_results_table(results['web_data'])
                if not results.get('shipments'):  # إذا لم توجد شحنات، اعرض بيانات الإنترنت
                    self.results_tab_widget.setCurrentIndex(1)

            # تحديث الحالة
            shipments_count = len(results.get('shipments', []))
            web_results_count = len(results.get('web_data', []))

            status_msg = f"تم العثور على {shipments_count} شحنة"
            if web_results_count > 0:
                status_msg += f" و {web_results_count} نتيجة من الإنترنت"

            self.status_label.setText(status_msg)
            self.web_search_status.setText(f"تم العثور على {web_results_count} نتيجة من الإنترنت")

            if web_results_count > 0:
                self.apply_web_data_button.setEnabled(True)
                self.export_web_data_button.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في معالجة نتائج البحث: {str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.smart_search_button.setEnabled(True)

    def populate_shipments_table(self, shipments):
        """ملء جدول الشحنات الموجودة"""
        self.search_results_table.setRowCount(len(shipments))

        for row, shipment in enumerate(shipments):
            self.search_results_table.setItem(row, 0, QTableWidgetItem(str(shipment.get('shipment_number', ''))))
            self.search_results_table.setItem(row, 1, QTableWidgetItem(str(shipment.get('shipping_company', ''))))
            self.search_results_table.setItem(row, 2, QTableWidgetItem(str(shipment.get('container_number', ''))))
            self.search_results_table.setItem(row, 3, QTableWidgetItem(str(shipment.get('bill_of_lading', ''))))
            self.search_results_table.setItem(row, 4, QTableWidgetItem(str(shipment.get('shipment_status', ''))))
            self.search_results_table.setItem(row, 5, QTableWidgetItem(f"{shipment.get('similarity', 0):.1f}%"))

            # زر التعبئة
            fill_button = QPushButton("🔧 تعبئة")
            fill_button.clicked.connect(lambda checked, sid=shipment.get('id'): self.fill_single_shipment(sid))
            self.search_results_table.setCellWidget(row, 6, fill_button)

    def populate_web_results_table(self, web_results):
        """ملء جدول نتائج البحث عبر الإنترنت"""
        self.web_results_table.setRowCount(len(web_results))

        for row, result in enumerate(web_results):
            self.web_results_table.setItem(row, 0, QTableWidgetItem(str(result.get('carrier', ''))))
            self.web_results_table.setItem(row, 1, QTableWidgetItem(str(result.get('status', ''))))
            self.web_results_table.setItem(row, 2, QTableWidgetItem(str(result.get('vessel_name', ''))))
            self.web_results_table.setItem(row, 3, QTableWidgetItem(str(result.get('voyage_number', ''))))
            self.web_results_table.setItem(row, 4, QTableWidgetItem(str(result.get('port_of_loading', ''))))
            self.web_results_table.setItem(row, 5, QTableWidgetItem(str(result.get('port_of_discharge', ''))))
            self.web_results_table.setItem(row, 6, QTableWidgetItem(str(result.get('departure_date', ''))))
            self.web_results_table.setItem(row, 7, QTableWidgetItem(str(result.get('arrival_date', ''))))
            self.web_results_table.setItem(row, 8, QTableWidgetItem(str(result.get('shipping_method', ''))))

            # زر التطبيق
            apply_button = QPushButton("✅ تطبيق")
            apply_button.clicked.connect(lambda checked, data=result: self.apply_single_web_result(data))
            self.web_results_table.setCellWidget(row, 9, apply_button)

    def apply_web_data_to_shipment(self):
        """تطبيق البيانات من الإنترنت على شحنة محددة"""
        # سيتم تنفيذ هذه الدالة لاحقاً
        QMessageBox.information(self, "معلومات", "سيتم تطبيق البيانات على الشحنة المحددة")

    def apply_single_web_result(self, web_data):
        """تطبيق نتيجة واحدة من الإنترنت"""
        # سيتم تنفيذ هذه الدالة لاحقاً
        QMessageBox.information(self, "معلومات", f"سيتم تطبيق بيانات {web_data.get('carrier', 'غير محدد')}")

    def search_shipments(self):
        """البحث عن الشحنات"""
        search_criteria = {}
        
        if self.search_shipping_company.text().strip():
            search_criteria['shipping_company'] = self.search_shipping_company.text().strip()
        
        if self.search_container_number.text().strip():
            search_criteria['container_number'] = self.search_container_number.text().strip()
        
        if self.search_bill_of_lading.text().strip():
            search_criteria['bill_of_lading'] = self.search_bill_of_lading.text().strip()
        
        if not search_criteria:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال معايير البحث")
            return
        
        # البحث عن الشحنات المشابهة
        similar_shipments = self.filler.find_similar_shipments(search_criteria)
        
        # عرض النتائج
        self.search_results_table.setRowCount(len(similar_shipments))
        
        for row, (shipment, similarity) in enumerate(similar_shipments):
            self.search_results_table.setItem(row, 0, QTableWidgetItem(shipment.shipment_number or ""))
            self.search_results_table.setItem(row, 1, QTableWidgetItem(shipment.shipping_company or ""))
            self.search_results_table.setItem(row, 2, QTableWidgetItem(shipment.container_number or ""))
            self.search_results_table.setItem(row, 3, QTableWidgetItem(shipment.bill_of_lading or ""))
            self.search_results_table.setItem(row, 4, QTableWidgetItem(f"{similarity:.2f}"))
            
            # زر التعبئة
            fill_button = QPushButton("تعبئة")
            fill_button.clicked.connect(lambda checked, s_id=shipment.id: self.fill_single_shipment(s_id))
            self.search_results_table.setCellWidget(row, 5, fill_button)
    
    def clear_search(self):
        """مسح البحث"""
        self.search_shipping_company.clear()
        self.search_container_number.clear()
        self.search_bill_of_lading.clear()
        self.search_results_table.setRowCount(0)
    
    def fill_single_shipment(self, shipment_id):
        """تعبئة شحنة واحدة"""
        if self.worker and self.worker.isRunning():
            return
        
        confidence = self.confidence_threshold_spinbox.value()
        
        self.progress_bar.setVisible(True)
        self.status_label.setText(f"جاري تعبئة الشحنة {shipment_id}...")
        
        self.worker = DataFillerWorker("fill_single", shipment_id=shipment_id, confidence=confidence)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.filling_completed.connect(self.on_filling_completed)
        self.worker.error_occurred.connect(self.on_error)
        self.worker.start()
    
    def start_batch_fill(self):
        """بدء التعبئة المجمعة"""
        if self.worker and self.worker.isRunning():
            return
        
        # الحصول على الشحنات التي تحتاج تعبئة
        session = self.db_manager.get_session()
        try:
            shipments = session.query(Shipment).limit(self.max_shipments_spinbox.value()).all()
            shipment_ids = [s.id for s in shipments]
            
            if not shipment_ids:
                QMessageBox.information(self, "معلومات", "لا توجد شحنات للتعبئة")
                return
            
            confidence = self.batch_confidence_spinbox.value()
            
            self.progress_bar.setVisible(True)
            self.status_label.setText("جاري التعبئة المجمعة...")
            
            self.worker = DataFillerWorker("fill_batch", shipment_ids=shipment_ids, confidence=confidence)
            self.worker.progress_updated.connect(self.update_progress)
            self.worker.filling_completed.connect(self.on_batch_filling_completed)
            self.worker.error_occurred.connect(self.on_error)
            self.worker.start()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في بدء التعبئة المجمعة: {str(e)}")
        finally:
            session.close()









    def on_error(self, error_message):
        """عند حدوث خطأ"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("خطأ")
        QMessageBox.critical(self, "خطأ", f"حدث خطأ: {error_message}")
























    def apply_web_data_to_shipment(self):
        """تطبيق البيانات من الإنترنت على شحنة محددة"""
        # التحقق من وجود نتائج إنترنت محددة
        selected_web_rows = self.web_results_table.selectionModel().selectedRows()
        if not selected_web_rows:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد نتيجة من البحث عبر الإنترنت أولاً")
            return

        # التحقق من وجود شحنات محددة
        selected_shipment_rows = self.shipments_table.selectionModel().selectedRows()
        if not selected_shipment_rows:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد شحنة من الجدول أولاً")
            return

        web_row = selected_web_rows[0].row()
        shipment_row = selected_shipment_rows[0].row()

        # استخراج بيانات الإنترنت
        web_data = {}
        for col in range(self.web_results_table.columnCount()):
            header = self.web_results_table.horizontalHeaderItem(col).text()
            item = self.web_results_table.item(web_row, col)
            value = item.text() if item else ""
            if value and value != "-":
                web_data[header] = value

        # استخراج معرف الشحنة
        shipment_id_item = self.shipments_table.item(shipment_row, 0)
        if not shipment_id_item:
            QMessageBox.warning(self, "خطأ", "لا يمكن تحديد معرف الشحنة")
            return

        shipment_id = int(shipment_id_item.text())

        # تطبيق البيانات
        success = self.apply_single_web_result(shipment_id, web_data)

        if success:
            QMessageBox.information(self, "نجح", "تم تطبيق البيانات بنجاح")
            # تحديث جدول الشحنات
            self.load_shipments_data()
        else:
            QMessageBox.warning(self, "فشل", "فشل في تطبيق البيانات")

    def apply_single_web_result(self, shipment_id, web_data):
        """تطبيق نتيجة واحدة من الإنترنت على شحنة محددة"""
        try:
            session = self.db_manager.get_session()
            shipment = session.query(Shipment).filter_by(id=shipment_id).first()

            if not shipment:
                return False

            updated_fields = []

            # خريطة ربط حقول الإنترنت بحقول قاعدة البيانات
            field_mapping = {
                'شركة الملاحة': 'shipping_company',
                'الحالة': 'status',
                'اسم السفينة': 'vessel_name',
                'رقم الرحلة': 'voyage_number',
                'ميناء المغادرة': 'port_of_loading',
                'ميناء الوصول': 'port_of_discharge',
                'تاريخ المغادرة': 'actual_departure_date',
                'تاريخ الوصول': 'estimated_arrival_date'
            }

            # تطبيق البيانات
            for web_field, db_field in field_mapping.items():
                if web_field in web_data:
                    value = web_data[web_field].strip()
                    if value and value != "-":
                        # التحقق من أن الحقل فارغ أو يحتاج تحديث
                        current_value = getattr(shipment, db_field, None)
                        if not current_value or str(current_value).strip() == "":
                            # معالجة خاصة للتواريخ
                            if 'date' in db_field:
                                try:
                                    from datetime import datetime
                                    date_formats = ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d %H:%M:%S']
                                    parsed_date = None

                                    for fmt in date_formats:
                                        try:
                                            parsed_date = datetime.strptime(value, fmt)
                                            break
                                        except ValueError:
                                            continue

                                    if parsed_date:
                                        setattr(shipment, db_field, parsed_date)
                                        updated_fields.append(f"{web_field}: {value}")
                                except Exception:
                                    pass  # تجاهل أخطاء التاريخ
                            else:
                                setattr(shipment, db_field, value)
                                updated_fields.append(f"{web_field}: {value}")

            # حفظ التغييرات
            if updated_fields:
                session.commit()

                # إضافة ملاحظة عن التحديث
                update_note = f"تم تحديث البيانات من البحث عبر الإنترنت:\n" + "\n".join(updated_fields)
                if shipment.notes:
                    shipment.notes += f"\n\n{update_note}"
                else:
                    shipment.notes = update_note
                session.commit()

                return True

            return False

        except Exception as e:
            print(f"خطأ في تطبيق البيانات: {str(e)}")
            if 'session' in locals():
                session.rollback()
            return False
        finally:
            if 'session' in locals():
                session.close()

    def on_error(self, error_message):
        """عند حدوث خطأ"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("خطأ")
        QMessageBox.critical(self, "خطأ", f"حدث خطأ: {error_message}")

def main():
    """دالة رئيسية للاختبار"""
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    dialog = ShipmentDataFillerDialog()
    dialog.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
