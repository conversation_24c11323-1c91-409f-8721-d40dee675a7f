# نظام البحث عبر الإنترنت في مواقع شركات الملاحة
# Web Scraping System for Shipping Companies

## نظرة عامة | Overview

تم تطوير نظام شامل للبحث عبر الإنترنت في مواقع شركات الملاحة العالمية لاستخراج بيانات الشحنات وتعبئة الحقول الفارغة تلقائياً.

A comprehensive system has been developed to search shipping company websites online to extract shipment data and automatically fill empty fields.

## الميزات الرئيسية | Key Features

### 🌐 البحث المتعدد المصادر | Multi-Source Search
- البحث في مواقع شركات الملاحة الكبرى
- دعم البحث برقم الحاوية أو رقم بوليصة الشحن
- البحث المتوازي في عدة مواقع في نفس الوقت

### 🔍 شركات الملاحة المدعومة | Supported Carriers
- **Maersk** - أكبر شركة حاويات في العالم
- **MSC** - شركة البحر الأبيض المتوسط للشحن
- **COSCO** - شركة الصين للشحن البحري
- **Evergreen** - شركة إيفرجرين للخطوط الملاحية
- **CMA CGM** - مجموعة سي إم إيه سي جي إم
- **OOCL** - شركة الشرق للحاويات البحرية
- **Hapag-Lloyd** - شركة هاباغ لويد
- **ONE** - شركة أوشن نتوورك إكسبريس
- **Yang Ming** - شركة يانغ مينغ
- **HMM** - شركة هيونداي التجارية البحرية

### 🤖 التقنيات المستخدمة | Technologies Used
- **BeautifulSoup4** - تحليل HTML
- **Selenium WebDriver** - أتمتة المتصفح
- **aiohttp** - طلبات HTTP غير متزامنة
- **Scrapy** - إطار عمل الاستخراج المتقدم
- **fake-useragent** - تدوير وكلاء المستخدم

### 📊 معالجة البيانات | Data Processing
- استخراج ذكي للبيانات من مصادر متعددة
- حساب درجات الثقة للبيانات المستخرجة
- التحقق من جودة البيانات وصحتها
- دمج البيانات من مصادر مختلفة

## هيكل النظام | System Architecture

```
src/
├── services/
│   └── web_scraping_service.py     # خدمة البحث الرئيسية
├── utils/
│   └── shipment_data_filler.py     # نظام تعبئة البيانات المحسن
└── ui/dialogs/
    └── shipment_data_filler_dialog.py  # واجهة المستخدم
```

## الملفات الرئيسية | Main Files

### 1. WebScrapingService
**المسار:** `src/services/web_scraping_service.py`

**الوظائف الرئيسية:**
- `search_all_carriers()` - البحث في جميع الشركات
- `extract_best_data()` - استخراج أفضل البيانات
- `calculate_confidence_score()` - حساب درجة الثقة
- `validate_data_quality()` - التحقق من جودة البيانات

### 2. ShipmentDataFiller (محسن)
**المسار:** `src/utils/shipment_data_filler.py`

**الوظائف الجديدة:**
- `search_web_data()` - البحث عبر الإنترنت
- `fill_from_web_data()` - تعبئة من البيانات المستخرجة
- `hybrid_fill_shipment()` - تعبئة هجينة (قاعدة البيانات + الويب)

### 3. واجهة المستخدم المحسنة
**المسار:** `src/ui/dialogs/shipment_data_filler_dialog.py`

**التحسينات:**
- تبويب جديد للبحث عبر الإنترنت
- جدول عرض النتائج
- أزرار التحكم والتطبيق

## كيفية الاستخدام | How to Use

### 1. التثبيت | Installation

```bash
# تثبيت المكتبات المطلوبة
pip install requests aiohttp beautifulsoup4 lxml html5lib
pip install selenium webdriver-manager scrapy fake-useragent python-dotenv
```

### 2. الإعداد | Configuration

```bash
# نسخ ملف الإعدادات
cp .env.example .env

# تحرير الإعدادات حسب الحاجة
nano .env
```

### 3. الاختبار | Testing

```bash
# تشغيل اختبار النظام
python test_web_scraping.py

# اختبار نظام التعبئة المحسن
python src/utils/shipment_data_filler.py
```

### 4. الاستخدام من الواجهة | GUI Usage

1. افتح نافذة تعبئة البيانات المفقودة
2. انتقل إلى تبويب "البحث عبر الإنترنت"
3. أدخل رقم الحاوية أو رقم بوليصة الشحن
4. اختر شركة الملاحة (اختياري)
5. اضغط "بحث عبر الإنترنت"
6. راجع النتائج وطبق البيانات المطلوبة

## البيانات المستخرجة | Extracted Data

### البيانات الأساسية | Basic Data
- رقم الحاوية (Container Number)
- رقم بوليصة الشحن (Bill of Lading)
- شركة الملاحة (Carrier)
- حالة الشحنة (Status)

### بيانات الرحلة | Voyage Data
- اسم السفينة (Vessel Name)
- رقم الرحلة (Voyage Number)
- ميناء المغادرة (Origin Port)
- ميناء الوصول (Destination Port)
- تاريخ المغادرة (Departure Date)
- تاريخ الوصول (Arrival Date)

### بيانات إضافية | Additional Data
- الوزن (Weight)
- الحجم (Volume)
- نوع البضاعة (Commodity)
- المرسل إليه (Consignee)
- المرسل (Shipper)

## الأمان والأداء | Security & Performance

### إعدادات الأمان | Security Settings
- احترام ملفات robots.txt
- تأخير بين الطلبات لتجنب الحظر
- تدوير وكلاء المستخدم
- حد أقصى للطلبات المتزامنة

### تحسين الأداء | Performance Optimization
- البحث المتوازي في عدة مواقع
- التخزين المؤقت للنتائج
- إعادة المحاولة التلقائية عند الفشل
- معالجة الأخطاء الشاملة

## استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues

1. **فشل الاتصال بالإنترنت**
   - تحقق من الاتصال بالإنترنت
   - تحقق من إعدادات البروكسي

2. **عدم العثور على بيانات**
   - تأكد من صحة رقم الحاوية
   - جرب البحث بشركة ملاحة مختلفة

3. **بطء في الاستجابة**
   - قلل عدد الشركات المبحوث فيها
   - زد قيمة timeout في الإعدادات

### السجلات | Logs
```bash
# عرض سجلات البحث
tail -f logs/web_scraping.log

# مستوى السجلات
export LOG_LEVEL=DEBUG
```

## التطوير المستقبلي | Future Development

### ميزات مخططة | Planned Features
- دعم المزيد من شركات الملاحة
- تحسين خوارزميات الاستخراج
- واجهة مستخدم محسنة
- تقارير تحليلية متقدمة

### المساهمة | Contributing
- إضافة دعم لشركات ملاحة جديدة
- تحسين دقة استخراج البيانات
- تطوير اختبارات شاملة
- تحسين التوثيق

## الدعم | Support

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع ملف استكشاف الأخطاء
- تحقق من السجلات
- اختبر الاتصال بالإنترنت
- تأكد من تحديث المكتبات

---

**ملاحظة:** هذا النظام مصمم للاستخدام التجاري المشروع ويحترم شروط الخدمة لمواقع شركات الملاحة.
