#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي - SHIPMENT ERP Main Application
الواجهة الرئيسية المحدثة بناءً على الواجهة التجريبية
"""

import sys
import os
from datetime import datetime
from PySide6.QtWidgets import (QApplication, QMainWindow, QMessageBox, QVBoxLayout, 
                               QHBoxLayout, QWidget, QPushButton, QLabel, QFrame,
                               QMenuBar, QStatusBar, QToolBar, QSplashScreen)
from PySide6.QtCore import Qt, QTimer, QThread, pyqtSignal
from PySide6.QtGui import QFont, QPixmap, QPainter, QIcon, QAction

# استيراد مكتبة القوالب
from template_library import Templates, نموذج_ادخال_اساسي

class SplashScreen(QSplashScreen):
    """شاشة البداية"""
    
    def __init__(self):
        # إنشاء صورة بسيطة للشاشة
        pixmap = QPixmap(400, 300)
        pixmap.fill(Qt.white)
        
        painter = QPainter(pixmap)
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        painter.setPen(Qt.blue)
        painter.drawText(pixmap.rect(), Qt.AlignCenter, 
                        "SHIPMENT ERP\nنظام إدارة الشحنات\n\nجاري التحميل...")
        painter.end()
        
        super().__init__(pixmap)
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)

class MainApplication(QMainWindow):
    """التطبيق الرئيسي"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SHIPMENT ERP - نظام إدارة الشحنات المتقدم")
        self.setMinimumSize(1000, 700)
        
        # متغيرات التطبيق
        self.active_forms = []
        self.current_user = "المدير العام"
        self.system_status = "متصل"
        
        # إعداد التطبيق
        self.setup_ui()
        self.setup_styles()
        self.setup_connections()
        self.setup_timer()
        
        # رسالة ترحيب
        self.show_welcome_message()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء القوائم
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء المحتوى الرئيسي
        self.create_main_content()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        new_action = QAction("جديد", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_shipment_form)
        file_menu.addAction(new_action)
        
        open_action = QAction("فتح", self)
        open_action.setShortcut("Ctrl+O")
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة النماذج
        forms_menu = menubar.addMenu("النماذج")
        
        basic_form_action = QAction("نموذج إدخال أساسي", self)
        basic_form_action.triggered.connect(self.open_basic_form)
        forms_menu.addAction(basic_form_action)
        
        template_library_action = QAction("مكتبة القوالب", self)
        template_library_action.triggered.connect(self.open_template_library)
        forms_menu.addAction(template_library_action)
        
        # قائمة الأدوات
        tools_menu = menubar.addMenu("الأدوات")
        
        settings_action = QAction("الإعدادات", self)
        tools_menu.addAction(settings_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # زر نموذج جديد
        new_form_action = QAction("📋 نموذج جديد", self)
        new_form_action.triggered.connect(self.new_shipment_form)
        toolbar.addAction(new_form_action)
        
        toolbar.addSeparator()
        
        # زر مكتبة القوالب
        templates_action = QAction("🏛️ مكتبة القوالب", self)
        templates_action.triggered.connect(self.open_template_library)
        toolbar.addAction(templates_action)
        
        # زر الإعدادات
        settings_action = QAction("⚙️ الإعدادات", self)
        toolbar.addAction(settings_action)
        
        toolbar.addSeparator()
        
        # زر المساعدة
        help_action = QAction("❓ مساعدة", self)
        help_action.triggered.connect(self.show_help)
        toolbar.addAction(help_action)
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # عنوان رئيسي
        title_label = QLabel("🚢 نظام إدارة الشحنات المتقدم")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A90E2, stop:1 #357ABD);
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # منطقة الأزرار الرئيسية
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.Box)
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 2px solid #E0E0E0;
                border-radius: 10px;
                margin: 10px;
                padding: 20px;
            }
        """)
        layout.addWidget(buttons_frame)
        
        buttons_layout = QVBoxLayout(buttons_frame)
        
        # صف الأزرار الأول
        first_row = QHBoxLayout()
        
        # زر نموذج إدخال أساسي
        self.basic_form_btn = QPushButton("📋 نموذج إدخال أساسي")
        self.basic_form_btn.setMinimumHeight(80)
        self.basic_form_btn.setFont(QFont("Arial", 12, QFont.Bold))
        self.basic_form_btn.clicked.connect(self.open_basic_form)
        first_row.addWidget(self.basic_form_btn)
        
        # زر مكتبة القوالب
        self.templates_btn = QPushButton("🏛️ مكتبة القوالب")
        self.templates_btn.setMinimumHeight(80)
        self.templates_btn.setFont(QFont("Arial", 12, QFont.Bold))
        self.templates_btn.clicked.connect(self.open_template_library)
        first_row.addWidget(self.templates_btn)
        
        buttons_layout.addLayout(first_row)
        
        # صف الأزرار الثاني
        second_row = QHBoxLayout()
        
        # زر التقارير
        self.reports_btn = QPushButton("📊 التقارير")
        self.reports_btn.setMinimumHeight(80)
        self.reports_btn.setFont(QFont("Arial", 12, QFont.Bold))
        self.reports_btn.clicked.connect(self.show_reports)
        second_row.addWidget(self.reports_btn)
        
        # زر الإعدادات
        self.settings_btn = QPushButton("⚙️ الإعدادات")
        self.settings_btn.setMinimumHeight(80)
        self.settings_btn.setFont(QFont("Arial", 12, QFont.Bold))
        self.settings_btn.clicked.connect(self.show_settings)
        second_row.addWidget(self.settings_btn)
        
        buttons_layout.addLayout(second_row)
        
        # منطقة المعلومات
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.Box)
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #E8F4FD;
                border: 2px solid #B3D9FF;
                border-radius: 10px;
                margin: 10px;
                padding: 15px;
            }
        """)
        layout.addWidget(info_frame)
        
        info_layout = QVBoxLayout(info_frame)
        
        self.info_label = QLabel("💡 مرحباً بك في نظام إدارة الشحنات المتقدم")
        self.info_label.setFont(QFont("Arial", 11))
        self.info_label.setWordWrap(True)
        info_layout.addWidget(self.info_label)
        
        # إضافة مساحة مرنة
        layout.addStretch()
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()
        
        # معلومات المستخدم
        self.user_label = QLabel(f"المستخدم: {self.current_user}")
        status_bar.addWidget(self.user_label)
        
        # فاصل
        status_bar.addWidget(QLabel(" | "))
        
        # حالة النظام
        self.status_label = QLabel(f"الحالة: {self.system_status}")
        status_bar.addWidget(self.status_label)
        
        # فاصل
        status_bar.addWidget(QLabel(" | "))
        
        # الوقت
        self.time_label = QLabel()
        status_bar.addWidget(self.time_label)
        
        # فاصل
        status_bar.addWidget(QLabel(" | "))
        
        # عدد النماذج النشطة
        self.forms_count_label = QLabel("النماذج النشطة: 0")
        status_bar.addWidget(self.forms_count_label)
    
    def setup_styles(self):
        """إعداد الأنماط"""
        button_style = """
            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #357ABD;
            }
            QPushButton:pressed {
                background-color: #2E5A87;
            }
        """
        
        self.basic_form_btn.setStyleSheet(button_style)
        self.templates_btn.setStyleSheet(button_style)
        self.reports_btn.setStyleSheet(button_style)
        self.settings_btn.setStyleSheet(button_style)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارات مكتبة القوالب
        manager = Templates.get_manager()
        manager.template_created.connect(self.on_form_created)
        manager.template_closed.connect(self.on_form_closed)
    
    def setup_timer(self):
        """إعداد مؤقت تحديث الوقت"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # تحديث كل ثانية
        self.update_time()
    
    def update_time(self):
        """تحديث الوقت"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"الوقت: {current_time}")
    
    def new_shipment_form(self):
        """إنشاء نموذج شحنة جديد"""
        self.open_basic_form()
    
    def open_basic_form(self):
        """فتح نموذج إدخال أساسي"""
        try:
            form = نموذج_ادخال_اساسي(fullscreen=False)
            if form:
                form.show()
                self.update_info_label("تم فتح نموذج إدخال أساسي جديد")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح النموذج:\n{str(e)}")
    
    def open_template_library(self):
        """فتح مكتبة القوالب"""
        try:
            from run_template_library import TemplateSelector
            selector = TemplateSelector(self)
            selector.exec()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح مكتبة القوالب:\n{str(e)}")
    
    def show_reports(self):
        """عرض التقارير"""
        QMessageBox.information(
            self, "التقارير",
            "🚧 قسم التقارير قيد التطوير\n\n"
            "سيتم إضافة التقارير التالية قريباً:\n"
            "• تقارير الشحنات\n"
            "• تقارير العملاء\n"
            "• تقارير مالية\n"
            "• إحصائيات شاملة"
        )
    
    def show_settings(self):
        """عرض الإعدادات"""
        QMessageBox.information(
            self, "الإعدادات",
            "⚙️ إعدادات النظام\n\n"
            "الإعدادات المتاحة:\n"
            "• إعدادات قاعدة البيانات\n"
            "• إعدادات المستخدمين\n"
            "• إعدادات الطباعة\n"
            "• إعدادات النسخ الاحتياطي\n\n"
            "🚧 قيد التطوير"
        )
    
    def show_help(self):
        """عرض المساعدة"""
        QMessageBox.information(
            self, "المساعدة",
            "❓ مساعدة نظام إدارة الشحنات\n\n"
            "للحصول على المساعدة:\n"
            "• راجع دليل المستخدم\n"
            "• تواصل مع الدعم الفني\n"
            "• زر مكتبة القوالب للأمثلة\n\n"
            "الاختصارات:\n"
            "• Ctrl+N: نموذج جديد\n"
            "• Ctrl+O: فتح\n"
            "• Ctrl+Q: خروج"
        )
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(
            self, "حول البرنامج",
            "🚢 SHIPMENT ERP\n"
            "نظام إدارة الشحنات المتقدم\n\n"
            "الإصدار: 2.0.0\n"
            "تاريخ الإصدار: 2024\n\n"
            "المطور: فريق SHIPMENT Solutions\n\n"
            "نظام شامل لإدارة الشحنات والحوالات\n"
            "مع واجهة مستخدم عربية متقدمة\n"
            "وميزات تتبع حديثة"
        )
    
    def show_welcome_message(self):
        """عرض رسالة ترحيب"""
        QMessageBox.information(
            self, "مرحباً",
            "🎉 مرحباً بك في نظام إدارة الشحنات المتقدم!\n\n"
            "الميزات المتاحة:\n"
            "📋 نموذج إدخال أساسي مع بيانات تجريبية\n"
            "🏛️ مكتبة قوالب شاملة\n"
            "⚙️ إعدادات متقدمة\n"
            "📊 تقارير مفصلة (قيد التطوير)\n\n"
            "ابدأ بالضغط على 'نموذج إدخال أساسي' لتجربة النظام!"
        )
    
    def update_info_label(self, message):
        """تحديث رسالة المعلومات"""
        self.info_label.setText(f"💡 {message}")
        
        # إعادة تعيين الرسالة بعد 5 ثوان
        QTimer.singleShot(5000, lambda: self.info_label.setText(
            "💡 مرحباً بك في نظام إدارة الشحنات المتقدم"
        ))
    
    def on_form_created(self, template_name, template_instance):
        """معالج إنشاء نموذج جديد"""
        self.active_forms.append(template_instance)
        self.update_forms_count()
        self.update_info_label(f"تم إنشاء نموذج: {template_name}")
    
    def on_form_closed(self, template_name):
        """معالج إغلاق نموذج"""
        # تحديث قائمة النماذج النشطة
        self.active_forms = [f for f in self.active_forms if not f.isHidden()]
        self.update_forms_count()
        self.update_info_label(f"تم إغلاق نموذج: {template_name}")
    
    def update_forms_count(self):
        """تحديث عدد النماذج النشطة"""
        count = len(self.active_forms)
        self.forms_count_label.setText(f"النماذج النشطة: {count}")
    
    def closeEvent(self, event):
        """معالج إغلاق التطبيق"""
        if self.active_forms:
            reply = QMessageBox.question(
                self, "تأكيد الإغلاق",
                f"يوجد {len(self.active_forms)} نموذج نشط.\n"
                "هل تريد إغلاق التطبيق؟\n"
                "سيتم إغلاق جميع النماذج النشطة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.No:
                event.ignore()
                return
        
        # إيقاف المؤقت
        if hasattr(self, 'timer'):
            self.timer.stop()
        
        # إغلاق جميع النماذج النشطة
        for form in self.active_forms:
            form.close()
        
        event.accept()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام إدارة الشحنات المتقدم")
    print("=" * 50)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد مكتبة القوالب
    Templates.setup_app(app)
    
    # عرض شاشة البداية
    splash = SplashScreen()
    splash.show()
    
    # محاكاة تحميل
    app.processEvents()
    
    # إنشاء التطبيق الرئيسي
    main_app = MainApplication()
    
    # إخفاء شاشة البداية وعرض التطبيق
    splash.finish(main_app)
    main_app.show()
    
    print("✅ تم تشغيل التطبيق بنجاح!")
    print("🎯 التطبيق جاهز للاستخدام")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
