#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي المتقدم - ProShipment V2.0.0
Advanced Backup System for SQLite and Oracle
"""

import sys
import os
import shutil
import subprocess
import schedule
import time
import json
import zipfile
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.universal_database_manager import UniversalDatabaseManager
from src.database.oracle_config import DatabaseConfigManager, DatabaseType

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/backup_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BackupType(Enum):
    """أنواع النسخ الاحتياطي"""
    FULL = "full"
    INCREMENTAL = "incremental"
    DIFFERENTIAL = "differential"

class BackupStatus(Enum):
    """حالات النسخ الاحتياطي"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class BackupJob:
    """مهمة النسخ الاحتياطي"""
    id: str
    name: str
    database_type: str
    backup_type: BackupType
    schedule_pattern: str
    enabled: bool
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    status: BackupStatus = BackupStatus.PENDING
    retention_days: int = 30
    compress: bool = True
    encrypt: bool = False

@dataclass
class BackupResult:
    """نتيجة النسخ الاحتياطي"""
    job_id: str
    start_time: datetime
    end_time: Optional[datetime]
    status: BackupStatus
    file_path: Optional[str]
    file_size: Optional[int]
    error_message: Optional[str] = None
    tables_backed_up: int = 0
    records_backed_up: int = 0

class AdvancedBackupSystem:
    """نظام النسخ الاحتياطي المتقدم"""
    
    def __init__(self):
        self.config_manager = DatabaseConfigManager()
        self.db_manager = None
        self.backup_jobs = []
        self.backup_history = []
        self.backup_dir = Path("backups")
        self.config_file = Path("config/backup_config.json")
        
        # إنشاء المجلدات المطلوبة
        self.backup_dir.mkdir(exist_ok=True)
        Path("logs").mkdir(exist_ok=True)
        
        # تحميل الإعدادات
        self.load_configuration()
        self.setup_database_manager()
    
    def setup_database_manager(self):
        """إعداد مدير قاعدة البيانات"""
        try:
            config = self.config_manager.load_config()
            self.db_manager = UniversalDatabaseManager(config)
            logger.info(f"تم إعداد مدير قاعدة البيانات: {config.type.value}")
        except Exception as e:
            logger.error(f"خطأ في إعداد مدير قاعدة البيانات: {e}")
    
    def load_configuration(self):
        """تحميل إعدادات النسخ الاحتياطي"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # تحميل المهام
                for job_data in config_data.get('jobs', []):
                    job = BackupJob(
                        id=job_data['id'],
                        name=job_data['name'],
                        database_type=job_data['database_type'],
                        backup_type=BackupType(job_data['backup_type']),
                        schedule_pattern=job_data['schedule_pattern'],
                        enabled=job_data['enabled'],
                        retention_days=job_data.get('retention_days', 30),
                        compress=job_data.get('compress', True),
                        encrypt=job_data.get('encrypt', False)
                    )
                    
                    if job_data.get('last_run'):
                        job.last_run = datetime.fromisoformat(job_data['last_run'])
                    
                    self.backup_jobs.append(job)
                
                logger.info(f"تم تحميل {len(self.backup_jobs)} مهمة نسخ احتياطي")
            else:
                # إنشاء إعدادات افتراضية
                self.create_default_jobs()
                
        except Exception as e:
            logger.error(f"خطأ في تحميل إعدادات النسخ الاحتياطي: {e}")
            self.create_default_jobs()
    
    def create_default_jobs(self):
        """إنشاء مهام افتراضية"""
        config = self.config_manager.load_config()
        
        # مهمة يومية
        daily_job = BackupJob(
            id="daily_backup",
            name="نسخة احتياطية يومية",
            database_type=config.type.value,
            backup_type=BackupType.FULL,
            schedule_pattern="daily",
            enabled=True,
            retention_days=7
        )
        
        # مهمة أسبوعية
        weekly_job = BackupJob(
            id="weekly_backup",
            name="نسخة احتياطية أسبوعية",
            database_type=config.type.value,
            backup_type=BackupType.FULL,
            schedule_pattern="weekly",
            enabled=True,
            retention_days=30
        )
        
        self.backup_jobs = [daily_job, weekly_job]
        self.save_configuration()
        
        logger.info("تم إنشاء مهام النسخ الاحتياطي الافتراضية")
    
    def save_configuration(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        try:
            config_data = {
                'jobs': []
            }
            
            for job in self.backup_jobs:
                job_data = asdict(job)
                # تحويل التواريخ إلى strings
                if job_data['last_run']:
                    job_data['last_run'] = job.last_run.isoformat()
                if job_data['next_run']:
                    job_data['next_run'] = job.next_run.isoformat()
                
                # تحويل enums إلى strings
                job_data['backup_type'] = job.backup_type.value
                job_data['status'] = job.status.value
                
                config_data['jobs'].append(job_data)
            
            # إنشاء مجلد config إذا لم يكن موجوداً
            self.config_file.parent.mkdir(exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info("تم حفظ إعدادات النسخ الاحتياطي")
            
        except Exception as e:
            logger.error(f"خطأ في حفظ إعدادات النسخ الاحتياطي: {e}")
    
    def create_backup(self, job: BackupJob) -> BackupResult:
        """إنشاء نسخة احتياطية"""
        logger.info(f"بدء النسخ الاحتياطي: {job.name}")
        
        result = BackupResult(
            job_id=job.id,
            start_time=datetime.now(),
            end_time=None,
            status=BackupStatus.RUNNING,
            file_path=None,
            file_size=None
        )
        
        try:
            job.status = BackupStatus.RUNNING
            job.last_run = result.start_time
            
            # تحديد نوع قاعدة البيانات
            config = self.config_manager.load_config()
            
            if config.type == DatabaseType.SQLITE:
                result = self.create_sqlite_backup(job, result)
            elif config.type == DatabaseType.ORACLE:
                result = self.create_oracle_backup(job, result)
            else:
                raise ValueError(f"نوع قاعدة البيانات غير مدعوم: {config.type}")
            
            # ضغط النسخة الاحتياطية إذا كان مطلوباً
            if job.compress and result.file_path:
                result.file_path = self.compress_backup(result.file_path)
                result.file_size = Path(result.file_path).stat().st_size
            
            # تشفير النسخة الاحتياطية إذا كان مطلوباً
            if job.encrypt and result.file_path:
                result.file_path = self.encrypt_backup(result.file_path)
            
            result.end_time = datetime.now()
            result.status = BackupStatus.COMPLETED
            job.status = BackupStatus.COMPLETED
            
            logger.info(f"تم إنشاء النسخة الاحتياطية بنجاح: {result.file_path}")
            
        except Exception as e:
            result.end_time = datetime.now()
            result.status = BackupStatus.FAILED
            result.error_message = str(e)
            job.status = BackupStatus.FAILED
            
            logger.error(f"فشل في إنشاء النسخة الاحتياطية: {e}")
        
        # حفظ النتيجة في التاريخ
        self.backup_history.append(result)
        
        # تنظيف النسخ القديمة
        self.cleanup_old_backups(job)
        
        return result
    
    def create_sqlite_backup(self, job: BackupJob, result: BackupResult) -> BackupResult:
        """إنشاء نسخة احتياطية من SQLite"""
        try:
            config = self.config_manager.load_config()
            source_path = Path(config.sqlite_config.path)
            
            if not source_path.exists():
                raise FileNotFoundError(f"ملف قاعدة البيانات غير موجود: {source_path}")
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"sqlite_backup_{job.id}_{timestamp}.db"
            backup_path = self.backup_dir / backup_filename
            
            # نسخ الملف
            shutil.copy2(source_path, backup_path)
            
            # الحصول على إحصائيات
            if self.db_manager and self.db_manager.test_connection():
                with self.db_manager.get_session() as session:
                    from src.database.models import Base
                    
                    tables_count = 0
                    records_count = 0
                    
                    for table_name in Base.metadata.tables.keys():
                        try:
                            from sqlalchemy import text
                            count_result = session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                            table_records = count_result.scalar() or 0
                            records_count += table_records
                            tables_count += 1
                        except:
                            pass
                    
                    result.tables_backed_up = tables_count
                    result.records_backed_up = records_count
            
            result.file_path = str(backup_path)
            result.file_size = backup_path.stat().st_size
            
            return result
            
        except Exception as e:
            raise Exception(f"خطأ في نسخ SQLite: {e}")
    
    def create_oracle_backup(self, job: BackupJob, result: BackupResult) -> BackupResult:
        """إنشاء نسخة احتياطية من Oracle"""
        try:
            config = self.config_manager.load_config()
            oracle_config = config.oracle_config
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"oracle_backup_{job.id}_{timestamp}.dmp"
            backup_path = self.backup_dir / backup_filename
            
            # بناء أمر expdp
            connection_string = f"{oracle_config.username}/{oracle_config.password}@{oracle_config.host}:{oracle_config.port}/{oracle_config.service_name}"
            
            expdp_command = [
                "expdp",
                connection_string,
                f"directory=DATA_PUMP_DIR",
                f"dumpfile={backup_filename}",
                f"logfile=backup_{timestamp}.log"
            ]
            
            # تحديد نوع النسخ الاحتياطي
            if job.backup_type == BackupType.FULL:
                expdp_command.append("full=y")
            else:
                # نسخ احتياطي للمخطط فقط
                expdp_command.append(f"schemas={oracle_config.username}")
            
            # تشغيل الأمر
            process = subprocess.run(
                expdp_command,
                capture_output=True,
                text=True,
                timeout=3600  # مهلة ساعة واحدة
            )
            
            if process.returncode != 0:
                raise Exception(f"فشل expdp: {process.stderr}")
            
            # نقل الملف من Oracle directory إلى مجلد النسخ الاحتياطي
            # هذا يتطلب صلاحيات خاصة في Oracle
            
            result.file_path = str(backup_path)
            
            # الحصول على إحصائيات من سجل expdp
            log_content = process.stdout
            if "exported" in log_content:
                # استخراج عدد الجداول والسجلات من السجل
                import re
                tables_match = re.search(r'(\d+) tables', log_content)
                if tables_match:
                    result.tables_backed_up = int(tables_match.group(1))
                
                rows_match = re.search(r'(\d+) rows', log_content)
                if rows_match:
                    result.records_backed_up = int(rows_match.group(1))
            
            return result
            
        except subprocess.TimeoutExpired:
            raise Exception("انتهت مهلة النسخ الاحتياطي لـ Oracle")
        except Exception as e:
            raise Exception(f"خطأ في نسخ Oracle: {e}")
    
    def compress_backup(self, file_path: str) -> str:
        """ضغط النسخة الاحتياطية"""
        try:
            source_path = Path(file_path)
            compressed_path = source_path.with_suffix(source_path.suffix + '.zip')
            
            with zipfile.ZipFile(compressed_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(source_path, source_path.name)
            
            # حذف الملف الأصلي
            source_path.unlink()
            
            logger.info(f"تم ضغط النسخة الاحتياطية: {compressed_path}")
            return str(compressed_path)
            
        except Exception as e:
            logger.error(f"خطأ في ضغط النسخة الاحتياطية: {e}")
            return file_path
    
    def encrypt_backup(self, file_path: str) -> str:
        """تشفير النسخة الاحتياطية"""
        try:
            # تشفير بسيط باستخدام cryptography
            from cryptography.fernet import Fernet
            
            # إنشاء مفتاح التشفير (يجب حفظه بأمان)
            key = Fernet.generate_key()
            cipher_suite = Fernet(key)
            
            source_path = Path(file_path)
            encrypted_path = source_path.with_suffix(source_path.suffix + '.enc')
            
            # قراءة وتشفير الملف
            with open(source_path, 'rb') as f:
                file_data = f.read()
            
            encrypted_data = cipher_suite.encrypt(file_data)
            
            # كتابة الملف المشفر
            with open(encrypted_path, 'wb') as f:
                f.write(encrypted_data)
            
            # حفظ المفتاح (في بيئة إنتاجية، يجب حفظه بأمان)
            key_path = encrypted_path.with_suffix('.key')
            with open(key_path, 'wb') as f:
                f.write(key)
            
            # حذف الملف الأصلي
            source_path.unlink()
            
            logger.info(f"تم تشفير النسخة الاحتياطية: {encrypted_path}")
            return str(encrypted_path)
            
        except ImportError:
            logger.warning("مكتبة cryptography غير متاحة - تم تخطي التشفير")
            return file_path
        except Exception as e:
            logger.error(f"خطأ في تشفير النسخة الاحتياطية: {e}")
            return file_path
    
    def cleanup_old_backups(self, job: BackupJob):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=job.retention_days)
            
            # البحث عن ملفات النسخ الاحتياطي للمهمة
            pattern = f"*backup_{job.id}_*"
            old_files = []
            
            for backup_file in self.backup_dir.glob(pattern):
                try:
                    # استخراج التاريخ من اسم الملف
                    file_stat = backup_file.stat()
                    file_date = datetime.fromtimestamp(file_stat.st_mtime)
                    
                    if file_date < cutoff_date:
                        old_files.append(backup_file)
                except:
                    continue
            
            # حذف الملفات القديمة
            for old_file in old_files:
                try:
                    old_file.unlink()
                    logger.info(f"تم حذف النسخة الاحتياطية القديمة: {old_file}")
                    
                    # حذف ملف المفتاح إذا كان موجوداً
                    key_file = old_file.with_suffix('.key')
                    if key_file.exists():
                        key_file.unlink()
                        
                except Exception as e:
                    logger.error(f"خطأ في حذف الملف القديم {old_file}: {e}")
            
            if old_files:
                logger.info(f"تم تنظيف {len(old_files)} نسخة احتياطية قديمة")
                
        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
    
    def schedule_jobs(self):
        """جدولة مهام النسخ الاحتياطي"""
        schedule.clear()
        
        for job in self.backup_jobs:
            if not job.enabled:
                continue
            
            if job.schedule_pattern == "daily":
                schedule.every().day.at("02:00").do(self.run_scheduled_job, job)
            elif job.schedule_pattern == "weekly":
                schedule.every().sunday.at("03:00").do(self.run_scheduled_job, job)
            elif job.schedule_pattern == "monthly":
                schedule.every().month.do(self.run_scheduled_job, job)
            elif ":" in job.schedule_pattern:  # وقت محدد مثل "14:30"
                schedule.every().day.at(job.schedule_pattern).do(self.run_scheduled_job, job)
        
        logger.info(f"تم جدولة {len([j for j in self.backup_jobs if j.enabled])} مهمة نسخ احتياطي")
    
    def run_scheduled_job(self, job: BackupJob):
        """تشغيل مهمة مجدولة"""
        logger.info(f"تشغيل المهمة المجدولة: {job.name}")
        result = self.create_backup(job)
        
        if result.status == BackupStatus.COMPLETED:
            logger.info(f"نجحت المهمة المجدولة: {job.name}")
        else:
            logger.error(f"فشلت المهمة المجدولة: {job.name} - {result.error_message}")
    
    def start_scheduler(self):
        """بدء جدولة المهام"""
        self.schedule_jobs()
        
        logger.info("بدء جدولة النسخ الاحتياطي...")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
                
        except KeyboardInterrupt:
            logger.info("تم إيقاف جدولة النسخ الاحتياطي")
    
    def get_backup_status(self) -> Dict[str, Any]:
        """الحصول على حالة النسخ الاحتياطي"""
        return {
            'total_jobs': len(self.backup_jobs),
            'enabled_jobs': len([j for j in self.backup_jobs if j.enabled]),
            'last_backup': max([r.start_time for r in self.backup_history]) if self.backup_history else None,
            'successful_backups': len([r for r in self.backup_history if r.status == BackupStatus.COMPLETED]),
            'failed_backups': len([r for r in self.backup_history if r.status == BackupStatus.FAILED]),
            'total_backup_size': sum([Path(r.file_path).stat().st_size for r in self.backup_history 
                                    if r.file_path and Path(r.file_path).exists()]),
            'jobs': [asdict(job) for job in self.backup_jobs],
            'recent_history': [asdict(r) for r in self.backup_history[-10:]]
        }

    def restore_backup(self, backup_file: str, target_location: str = None) -> bool:
        """استعادة نسخة احتياطية"""
        try:
            backup_path = Path(backup_file)

            if not backup_path.exists():
                raise FileNotFoundError(f"ملف النسخة الاحتياطية غير موجود: {backup_file}")

            logger.info(f"بدء استعادة النسخة الاحتياطية: {backup_file}")

            # فك التشفير إذا كان مطلوباً
            if backup_path.suffix == '.enc':
                backup_file = self.decrypt_backup(backup_file)
                backup_path = Path(backup_file)

            # فك الضغط إذا كان مطلوباً
            if backup_path.suffix == '.zip':
                backup_file = self.decompress_backup(backup_file)
                backup_path = Path(backup_file)

            # تحديد نوع النسخة الاحتياطية
            if backup_path.suffix == '.db':
                return self.restore_sqlite_backup(backup_file, target_location)
            elif backup_path.suffix == '.dmp':
                return self.restore_oracle_backup(backup_file, target_location)
            else:
                raise ValueError(f"نوع ملف النسخة الاحتياطية غير مدعوم: {backup_path.suffix}")

        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False

    def decrypt_backup(self, encrypted_file: str) -> str:
        """فك تشفير النسخة الاحتياطية"""
        try:
            from cryptography.fernet import Fernet

            encrypted_path = Path(encrypted_file)
            key_path = encrypted_path.with_suffix('.key')

            if not key_path.exists():
                raise FileNotFoundError("ملف مفتاح التشفير غير موجود")

            # قراءة المفتاح
            with open(key_path, 'rb') as f:
                key = f.read()

            cipher_suite = Fernet(key)

            # قراءة وفك تشفير الملف
            with open(encrypted_path, 'rb') as f:
                encrypted_data = f.read()

            decrypted_data = cipher_suite.decrypt(encrypted_data)

            # كتابة الملف المفكوك التشفير
            decrypted_path = encrypted_path.with_suffix('')
            with open(decrypted_path, 'wb') as f:
                f.write(decrypted_data)

            logger.info(f"تم فك تشفير النسخة الاحتياطية: {decrypted_path}")
            return str(decrypted_path)

        except Exception as e:
            logger.error(f"خطأ في فك التشفير: {e}")
            raise

    def decompress_backup(self, compressed_file: str) -> str:
        """فك ضغط النسخة الاحتياطية"""
        try:
            compressed_path = Path(compressed_file)
            extract_dir = compressed_path.parent / "temp_extract"
            extract_dir.mkdir(exist_ok=True)

            with zipfile.ZipFile(compressed_path, 'r') as zipf:
                zipf.extractall(extract_dir)

            # البحث عن الملف المستخرج
            extracted_files = list(extract_dir.glob("*"))
            if not extracted_files:
                raise Exception("لم يتم العثور على ملفات مستخرجة")

            extracted_file = extracted_files[0]
            final_path = compressed_path.parent / extracted_file.name

            # نقل الملف إلى المكان النهائي
            shutil.move(str(extracted_file), str(final_path))

            # تنظيف المجلد المؤقت
            shutil.rmtree(extract_dir)

            logger.info(f"تم فك ضغط النسخة الاحتياطية: {final_path}")
            return str(final_path)

        except Exception as e:
            logger.error(f"خطأ في فك الضغط: {e}")
            raise

    def restore_sqlite_backup(self, backup_file: str, target_location: str = None) -> bool:
        """استعادة نسخة احتياطية SQLite"""
        try:
            config = self.config_manager.load_config()

            if target_location:
                target_path = Path(target_location)
            else:
                target_path = Path(config.sqlite_config.path)

            # إنشاء نسخة احتياطية من الملف الحالي
            if target_path.exists():
                backup_current = target_path.with_suffix(f'.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db')
                shutil.copy2(target_path, backup_current)
                logger.info(f"تم إنشاء نسخة احتياطية من الملف الحالي: {backup_current}")

            # نسخ ملف النسخة الاحتياطية
            shutil.copy2(backup_file, target_path)

            logger.info(f"تم استعادة SQLite بنجاح: {target_path}")
            return True

        except Exception as e:
            logger.error(f"خطأ في استعادة SQLite: {e}")
            return False

    def restore_oracle_backup(self, backup_file: str, target_location: str = None) -> bool:
        """استعادة نسخة احتياطية Oracle"""
        try:
            config = self.config_manager.load_config()
            oracle_config = config.oracle_config

            # بناء أمر impdp
            connection_string = f"{oracle_config.username}/{oracle_config.password}@{oracle_config.host}:{oracle_config.port}/{oracle_config.service_name}"

            backup_path = Path(backup_file)

            impdp_command = [
                "impdp",
                connection_string,
                f"directory=DATA_PUMP_DIR",
                f"dumpfile={backup_path.name}",
                f"logfile=restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log",
                "table_exists_action=replace"
            ]

            # تشغيل الأمر
            process = subprocess.run(
                impdp_command,
                capture_output=True,
                text=True,
                timeout=3600  # مهلة ساعة واحدة
            )

            if process.returncode != 0:
                raise Exception(f"فشل impdp: {process.stderr}")

            logger.info("تم استعادة Oracle بنجاح")
            return True

        except subprocess.TimeoutExpired:
            logger.error("انتهت مهلة استعادة Oracle")
            return False
        except Exception as e:
            logger.error(f"خطأ في استعادة Oracle: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    print("💾 نظام النسخ الاحتياطي المتقدم - ProShipment V2.0.0")
    print("="*60)

    backup_system = AdvancedBackupSystem()

    print("اختر العملية:")
    print("1. إنشاء نسخة احتياطية فورية")
    print("2. عرض حالة النسخ الاحتياطي")
    print("3. بدء جدولة المهام")
    print("4. استعادة نسخة احتياطية")
    print("5. إدارة مهام النسخ الاحتياطي")

    choice = input("اختر رقم (1-5): ").strip()
    
    if choice == "1":
        if backup_system.backup_jobs:
            print("\nالمهام المتاحة:")
            for i, job in enumerate(backup_system.backup_jobs):
                print(f"{i+1}. {job.name}")
            
            job_choice = input("اختر رقم المهمة: ").strip()
            try:
                job_index = int(job_choice) - 1
                if 0 <= job_index < len(backup_system.backup_jobs):
                    job = backup_system.backup_jobs[job_index]
                    result = backup_system.create_backup(job)
                    
                    if result.status == BackupStatus.COMPLETED:
                        print(f"✅ تم إنشاء النسخة الاحتياطية: {result.file_path}")
                        print(f"📊 الجداول: {result.tables_backed_up}")
                        print(f"📊 السجلات: {result.records_backed_up}")
                        if result.file_size:
                            print(f"📊 الحجم: {result.file_size / 1024 / 1024:.2f} MB")
                    else:
                        print(f"❌ فشل في إنشاء النسخة الاحتياطية: {result.error_message}")
                else:
                    print("❌ رقم مهمة غير صحيح")
            except ValueError:
                print("❌ يرجى إدخال رقم صحيح")
        else:
            print("⚠️ لا توجد مهام نسخ احتياطي")
    
    elif choice == "2":
        status = backup_system.get_backup_status()
        
        print(f"\n📊 حالة النسخ الاحتياطي:")
        print(f"إجمالي المهام: {status['total_jobs']}")
        print(f"المهام المفعلة: {status['enabled_jobs']}")
        print(f"النسخ الناجحة: {status['successful_backups']}")
        print(f"النسخ الفاشلة: {status['failed_backups']}")
        
        if status['last_backup']:
            print(f"آخر نسخة احتياطية: {status['last_backup']}")
        
        if status['total_backup_size']:
            size_mb = status['total_backup_size'] / 1024 / 1024
            print(f"الحجم الإجمالي: {size_mb:.2f} MB")
    
    elif choice == "3":
        print("\n🕐 بدء جدولة المهام...")
        print("اضغط Ctrl+C لإيقاف الجدولة")
        backup_system.start_scheduler()

    elif choice == "4":
        print("\n🔄 استعادة نسخة احتياطية")

        # عرض النسخ الاحتياطية المتاحة
        backup_files = list(backup_system.backup_dir.glob("*backup_*"))

        if not backup_files:
            print("⚠️ لا توجد نسخ احتياطية متاحة")
        else:
            print("النسخ الاحتياطية المتاحة:")
            for i, backup_file in enumerate(backup_files):
                size_mb = backup_file.stat().st_size / 1024 / 1024
                mod_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                print(f"{i+1}. {backup_file.name} ({size_mb:.2f} MB) - {mod_time}")

            file_choice = input("اختر رقم الملف: ").strip()
            try:
                file_index = int(file_choice) - 1
                if 0 <= file_index < len(backup_files):
                    selected_file = backup_files[file_index]

                    confirm = input(f"هل تريد استعادة {selected_file.name}؟ (y/n): ").strip().lower()
                    if confirm in ['y', 'yes', 'نعم']:
                        if backup_system.restore_backup(str(selected_file)):
                            print("✅ تم استعادة النسخة الاحتياطية بنجاح")
                        else:
                            print("❌ فشل في استعادة النسخة الاحتياطية")
                    else:
                        print("تم إلغاء الاستعادة")
                else:
                    print("❌ رقم ملف غير صحيح")
            except ValueError:
                print("❌ يرجى إدخال رقم صحيح")

    elif choice == "5":
        print("\n⚙️ إدارة مهام النسخ الاحتياطي قيد التطوير")

    else:
        print("❌ خيار غير صحيح")

if __name__ == "__main__":
    main()
