#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                               QTabWidget, QWidget, QGroupBox, QFormLayout, 
                               QLineEdit, QComboBox, QTextEdit, QDateEdit, 
                               QSpinBox, QTableWidget, QMessageBox, QShortcut,
                               QApplication, QLabel, QFrame)
from PySide6.QtCore import Signal, QDate, Qt, QKeySequence
from PySide6.QtGui import QFont

from src.database.database_manager import DatabaseManager

class NewShipmentWindowWorking(QDialog):
    """نافذة شحنة جديدة مع أزرار التحكم العاملة"""
    
    shipment_saved = Signal(int)  # إشارة عند حفظ الشحنة
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.current_shipment_id = None
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("شحنة جديدة - مع أزرار التحكم")
        self.setModal(True)
        self.resize(1000, 750)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("🚢 نظام إدارة الشحنات - شحنة جديدة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # أزرار التحكم
        self.create_control_buttons(main_layout)
        
        # التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 20px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        main_layout.addWidget(self.tab_widget)
        
        # إنشاء التبويبات
        self.create_basic_tab()
        self.create_items_tab()
        self.create_financial_tab()
        
    def create_control_buttons(self, main_layout):
        """إنشاء أزرار التحكم"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel | QFrame.Raised)
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 2px solid #3498db;
                border-radius: 12px;
                padding: 10px;
                margin: 5px;
            }
        """)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(25)
        buttons_layout.setContentsMargins(25, 20, 25, 20)

        # زر إضافة
        self.new_button = QPushButton("🆕 إضافة")
        self.new_button.setFixedSize(150, 55)
        self.new_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #2980b9;
                border-radius: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #2980b9, stop:1 #1f5f8b);
                border: 3px solid #1f5f8b;
            }
            QPushButton:pressed {
                background: #1f5f8b;
            }
        """)

        # زر حفظ
        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setFixedSize(150, 55)
        self.save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #229954;
                border-radius: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #229954, stop:1 #1e8449);
                border: 3px solid #1e8449;
            }
            QPushButton:pressed {
                background: #1e8449;
            }
        """)

        # زر تعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setFixedSize(150, 55)
        self.edit_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #f39c12, stop:1 #e67e22);
                color: white;
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #e67e22;
                border-radius: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #e67e22, stop:1 #d35400);
                border: 3px solid #d35400;
            }
            QPushButton:pressed {
                background: #d35400;
            }
            QPushButton:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
                border: 3px solid #95a5a6;
            }
        """)
        self.edit_button.setEnabled(False)

        # زر خروج
        self.exit_button = QPushButton("🚪 خروج")
        self.exit_button.setFixedSize(150, 55)
        self.exit_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #c0392b;
                border-radius: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #c0392b, stop:1 #a93226);
                border: 3px solid #a93226;
            }
            QPushButton:pressed {
                background: #a93226;
            }
        """)

        # ترتيب الأزرار
        buttons_layout.addWidget(self.new_button)
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.exit_button)
        
        # إضافة إطار الأزرار
        main_layout.addWidget(buttons_frame)
        
    def create_basic_tab(self):
        """إنشاء التبويب الأساسي"""
        basic_tab = QWidget()
        self.tab_widget.addTab(basic_tab, "البيانات الأساسية")
        
        layout = QVBoxLayout(basic_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
            }
        """)
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(10)
        
        # رقم الشحنة
        self.shipment_number_edit = QLineEdit()
        self.shipment_number_edit.setReadOnly(True)
        self.shipment_number_edit.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        self.shipment_number_edit.setText("SH-2024-001")
        basic_layout.addRow("رقم الشحنة:", self.shipment_number_edit)
        
        # المورد
        self.supplier_edit = QLineEdit()
        self.supplier_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        basic_layout.addRow("المورد:", self.supplier_edit)
        
        # حالة الشحنة
        self.shipment_status_combo = QComboBox()
        self.shipment_status_combo.addItems([
            "تحت الطلب", "مؤكدة", "تم الشحن", "في الطريق", 
            "وصلت الميناء", "في الجمارك", "تم التسليم"
        ])
        self.shipment_status_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
            }
        """)
        basic_layout.addRow("حالة الشحنة:", self.shipment_status_combo)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(120)
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
            QTextEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        basic_layout.addRow("ملاحظات:", self.notes_edit)
        
        layout.addWidget(basic_group)
        layout.addStretch()
        
    def create_items_tab(self):
        """إنشاء تبويب الأصناف"""
        items_tab = QWidget()
        self.tab_widget.addTab(items_tab, "الأصناف")
        
        layout = QVBoxLayout(items_tab)
        items_label = QLabel("تبويب الأصناف - قيد التطوير")
        items_label.setStyleSheet("font-size: 16px; padding: 20px;")
        layout.addWidget(items_label)
        
    def create_financial_tab(self):
        """إنشاء التبويب المالي"""
        financial_tab = QWidget()
        self.tab_widget.addTab(financial_tab, "البيانات المالية")
        
        layout = QVBoxLayout(financial_tab)
        financial_label = QLabel("التبويب المالي - قيد التطوير")
        financial_label.setStyleSheet("font-size: 16px; padding: 20px;")
        layout.addWidget(financial_label)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.new_button.clicked.connect(self.new_shipment)
        self.save_button.clicked.connect(self.save_shipment)
        self.edit_button.clicked.connect(self.edit_shipment)
        self.exit_button.clicked.connect(self.reject)
        
    def new_shipment(self):
        """إنشاء شحنة جديدة"""
        reply = QMessageBox.question(
            self,
            "شحنة جديدة",
            "هل تريد إنشاء شحنة جديدة؟\nسيتم مسح البيانات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.clear_form()
            self.current_shipment_id = None
            self.edit_button.setEnabled(False)
            QMessageBox.information(self, "شحنة جديدة", "✅ تم إنشاء نموذج شحنة جديد")
    
    def save_shipment(self):
        """حفظ الشحنة"""
        # محاكاة عملية الحفظ
        QMessageBox.information(self, "حفظ", "✅ تم حفظ الشحنة بنجاح")
        self.edit_button.setEnabled(True)
        self.current_shipment_id = 1  # محاكاة ID
    
    def edit_shipment(self):
        """تعديل الشحنة"""
        QMessageBox.information(self, "تعديل", "✏️ يمكنك الآن تعديل الشحنة")
    
    def clear_form(self):
        """مسح النموذج"""
        self.supplier_edit.clear()
        self.shipment_status_combo.setCurrentIndex(0)
        self.notes_edit.clear()

def main():
    """اختبار النافذة العاملة"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = NewShipmentWindowWorking()
    window.show()
    
    print("✅ تم فتح النافذة العاملة مع أزرار التحكم")
    print("🔍 تحقق من وجود الأزرار التالية في الأعلى:")
    print("   • 🆕 إضافة - أزرق")
    print("   • 💾 حفظ - أخضر") 
    print("   • ✏️ تعديل - برتقالي (معطل)")
    print("   • 🚪 خروج - أحمر")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
