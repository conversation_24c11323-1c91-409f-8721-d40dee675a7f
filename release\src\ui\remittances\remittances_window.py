# -*- coding: utf-8 -*-
"""
نافذة إدارة الحوالات الرئيسية
Main Remittances Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTableWidget, QTableWidgetItem, QPushButton, QLabel,
                               QLineEdit, QComboBox, QDateEdit, QFrame, QSplitter,
                               QGroupBox, QGridLayout, QMessageBox, QHeaderView,
                               QAbstractItemView, QMenu, QToolBar, QStatusBar,
                               QProgressBar, QTextEdit, QTabWidget, QScrollArea)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QThread
from PySide6.QtGui import QIcon, QFont, QPixmap, QAction, QPalette, QColor

import sqlite3
from pathlib import Path
from datetime import datetime, timedelta
import json

from ...utils.arabic_support import reshape_arabic_text

class RemittancesWindow(QMainWindow):
    """نافذة إدارة الحوالات الرئيسية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة الحوالات المتقدمة - ProShipment")
        self.setMinimumSize(1400, 800)
        self.resize(1600, 900)
        
        # متغيرات النافذة
        self.current_remittances = []
        self.selected_remittance_id = None
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        
        # تحميل البيانات الأولية
        self.load_remittances()
        self.load_filters_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط البحث والفلاتر
        self.create_search_filters_section(main_layout)
        
        # المحتوى الرئيسي - قسمين
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - قائمة الحوالات
        left_panel = self.create_remittances_list_panel()
        splitter.addWidget(left_panel)
        
        # الجانب الأيمن - تفاصيل الحوالة
        right_panel = self.create_remittance_details_panel()
        splitter.addWidget(right_panel)
        
        # تعيين النسب
        splitter.setSizes([800, 600])
        main_layout.addWidget(splitter)
        
    def create_search_filters_section(self, layout):
        """إنشاء قسم البحث والفلاتر"""
        filters_frame = QFrame()
        filters_frame.setFrameStyle(QFrame.StyledPanel)
        filters_frame.setStyleSheet("""
            QFrame {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        filters_layout = QGridLayout(filters_frame)
        filters_layout.setSpacing(10)
        
        # البحث السريع
        search_label = QLabel("البحث السريع:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث برقم الحوالة، اسم المرسل، أو المستقبل...")
        self.search_input.setMinimumWidth(300)
        
        # فلتر المورد
        supplier_label = QLabel("المورد:")
        self.supplier_combo = QComboBox()
        self.supplier_combo.addItem("جميع الموردين", "")
        self.supplier_combo.setMinimumWidth(200)
        
        # فلتر العملة
        currency_label = QLabel("العملة:")
        self.currency_combo = QComboBox()
        self.currency_combo.addItem("جميع العملات", "")
        self.currency_combo.setMinimumWidth(150)
        
        # فلتر الحالة
        status_label = QLabel("الحالة:")
        self.status_combo = QComboBox()
        self.status_combo.addItem("جميع الحالات", "")
        self.status_combo.setMinimumWidth(150)
        
        # فلتر التاريخ
        date_from_label = QLabel("من تاريخ:")
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        
        date_to_label = QLabel("إلى تاريخ:")
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        
        # أزرار الإجراءات
        self.search_btn = QPushButton("🔍 بحث")
        self.search_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        
        self.clear_btn = QPushButton("🗑️ مسح")
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        
        # ترتيب العناصر في الشبكة
        filters_layout.addWidget(search_label, 0, 0)
        filters_layout.addWidget(self.search_input, 0, 1, 1, 2)
        filters_layout.addWidget(supplier_label, 0, 3)
        filters_layout.addWidget(self.supplier_combo, 0, 4)
        
        filters_layout.addWidget(currency_label, 1, 0)
        filters_layout.addWidget(self.currency_combo, 1, 1)
        filters_layout.addWidget(status_label, 1, 2)
        filters_layout.addWidget(self.status_combo, 1, 3)
        
        filters_layout.addWidget(date_from_label, 2, 0)
        filters_layout.addWidget(self.date_from, 2, 1)
        filters_layout.addWidget(date_to_label, 2, 2)
        filters_layout.addWidget(self.date_to, 2, 3)
        
        filters_layout.addWidget(self.search_btn, 2, 4)
        filters_layout.addWidget(self.clear_btn, 2, 5)
        
        layout.addWidget(filters_frame)
        
    def create_remittances_list_panel(self):
        """إنشاء لوحة قائمة الحوالات"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # عنوان القسم
        title_label = QLabel("قائمة الحوالات")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #1e293b; padding: 10px;")
        layout.addWidget(title_label)
        
        # جدول الحوالات
        self.remittances_table = QTableWidget()
        self.setup_remittances_table()
        layout.addWidget(self.remittances_table)
        
        # شريط الإحصائيات السريعة
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.StyledPanel)
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f1f5f9;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        
        stats_layout = QHBoxLayout(stats_frame)
        
        self.total_count_label = QLabel("الإجمالي: 0")
        self.pending_count_label = QLabel("معلقة: 0")
        self.completed_count_label = QLabel("مكتملة: 0")
        self.total_amount_label = QLabel("المبلغ الإجمالي: 0")
        
        for label in [self.total_count_label, self.pending_count_label, 
                     self.completed_count_label, self.total_amount_label]:
            label.setStyleSheet("font-weight: bold; color: #374151;")
            stats_layout.addWidget(label)
        
        stats_layout.addStretch()
        layout.addWidget(stats_frame)
        
        return panel
        
    def create_remittance_details_panel(self):
        """إنشاء لوحة تفاصيل الحوالة"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # عنوان القسم
        title_label = QLabel("تفاصيل الحوالة")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #1e293b; padding: 10px;")
        layout.addWidget(title_label)
        
        # تبويبات التفاصيل
        self.details_tabs = QTabWidget()
        
        # تبويب المعلومات الأساسية
        basic_info_tab = self.create_basic_info_tab()
        self.details_tabs.addTab(basic_info_tab, "المعلومات الأساسية")
        
        # تبويب البيانات المالية
        financial_tab = self.create_financial_tab()
        self.details_tabs.addTab(financial_tab, "البيانات المالية")
        
        # تبويب تتبع الحالة
        status_tab = self.create_status_tracking_tab()
        self.details_tabs.addTab(status_tab, "تتبع الحالة")
        
        # تبويب المستندات
        documents_tab = self.create_documents_tab()
        self.details_tabs.addTab(documents_tab, "المستندات")
        
        layout.addWidget(self.details_tabs)
        
        # أزرار الإجراءات
        actions_frame = QFrame()
        actions_layout = QHBoxLayout(actions_frame)
        
        self.edit_btn = QPushButton("✏️ تعديل")
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #f59e0b;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d97706;
            }
        """)
        
        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #dc2626;
            }
        """)
        
        self.print_btn = QPushButton("🖨️ طباعة")
        self.print_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        
        actions_layout.addWidget(self.edit_btn)
        actions_layout.addWidget(self.delete_btn)
        actions_layout.addWidget(self.print_btn)
        actions_layout.addStretch()
        
        layout.addWidget(actions_frame)
        
        return panel

    def setup_remittances_table(self):
        """إعداد جدول الحوالات"""
        headers = [
            "رقم الحوالة", "تاريخ الحوالة", "المرسل", "المستقبل",
            "المورد", "المبلغ", "العملة", "الحالة", "تاريخ الإنشاء"
        ]

        self.remittances_table.setColumnCount(len(headers))
        self.remittances_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.remittances_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.remittances_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.remittances_table.setAlternatingRowColors(True)
        self.remittances_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.remittances_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الحوالة
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # المرسل
        header.setSectionResizeMode(3, QHeaderView.Stretch)           # المستقبل
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # المورد
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # العملة
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # الحالة

        # تنسيق الجدول
        self.remittances_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e2e8f0;
                background-color: white;
                alternate-background-color: #f8fafc;
                selection-background-color: #3b82f6;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e2e8f0;
            }
            QHeaderView::section {
                background-color: #f1f5f9;
                color: #374151;
                padding: 10px;
                border: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)

    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # معلومات الحوالة
        remittance_group = QGroupBox("معلومات الحوالة")
        remittance_layout = QGridLayout(remittance_group)

        self.remittance_number_label = QLabel("-")
        self.remittance_date_label = QLabel("-")
        self.reference_number_label = QLabel("-")
        self.current_status_label = QLabel("-")

        remittance_layout.addWidget(QLabel("رقم الحوالة:"), 0, 0)
        remittance_layout.addWidget(self.remittance_number_label, 0, 1)
        remittance_layout.addWidget(QLabel("تاريخ الحوالة:"), 0, 2)
        remittance_layout.addWidget(self.remittance_date_label, 0, 3)

        remittance_layout.addWidget(QLabel("الرقم المرجعي:"), 1, 0)
        remittance_layout.addWidget(self.reference_number_label, 1, 1)
        remittance_layout.addWidget(QLabel("الحالة الحالية:"), 1, 2)
        remittance_layout.addWidget(self.current_status_label, 1, 3)

        layout.addWidget(remittance_group)

        # معلومات المرسل
        sender_group = QGroupBox("معلومات المرسل")
        sender_layout = QGridLayout(sender_group)

        self.sender_name_label = QLabel("-")
        self.sender_id_label = QLabel("-")
        self.sender_phone_label = QLabel("-")
        self.sender_address_label = QLabel("-")

        sender_layout.addWidget(QLabel("اسم المرسل:"), 0, 0)
        sender_layout.addWidget(self.sender_name_label, 0, 1)
        sender_layout.addWidget(QLabel("رقم الهوية:"), 0, 2)
        sender_layout.addWidget(self.sender_id_label, 0, 3)

        sender_layout.addWidget(QLabel("الهاتف:"), 1, 0)
        sender_layout.addWidget(self.sender_phone_label, 1, 1)
        sender_layout.addWidget(QLabel("العنوان:"), 1, 2)
        sender_layout.addWidget(self.sender_address_label, 1, 3)

        layout.addWidget(sender_group)

        # معلومات المستقبل
        receiver_group = QGroupBox("معلومات المستقبل")
        receiver_layout = QGridLayout(receiver_group)

        self.receiver_name_label = QLabel("-")
        self.receiver_id_label = QLabel("-")
        self.receiver_phone_label = QLabel("-")
        self.receiver_address_label = QLabel("-")

        receiver_layout.addWidget(QLabel("اسم المستقبل:"), 0, 0)
        receiver_layout.addWidget(self.receiver_name_label, 0, 1)
        receiver_layout.addWidget(QLabel("رقم الهوية:"), 0, 2)
        receiver_layout.addWidget(self.receiver_id_label, 0, 3)

        receiver_layout.addWidget(QLabel("الهاتف:"), 1, 0)
        receiver_layout.addWidget(self.receiver_phone_label, 1, 1)
        receiver_layout.addWidget(QLabel("العنوان:"), 1, 2)
        receiver_layout.addWidget(self.receiver_address_label, 1, 3)

        layout.addWidget(receiver_group)

        # ملاحظات
        notes_group = QGroupBox("الملاحظات")
        notes_layout = QVBoxLayout(notes_group)

        self.notes_text = QTextEdit()
        self.notes_text.setReadOnly(True)
        self.notes_text.setMaximumHeight(100)
        notes_layout.addWidget(self.notes_text)

        layout.addWidget(notes_group)
        layout.addStretch()

        return tab

    def create_financial_tab(self):
        """إنشاء تبويب البيانات المالية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # المبالغ الأساسية
        amounts_group = QGroupBox("المبالغ")
        amounts_layout = QGridLayout(amounts_group)

        self.amount_label = QLabel("-")
        self.currency_label = QLabel("-")
        self.exchange_rate_label = QLabel("-")
        self.amount_base_currency_label = QLabel("-")

        amounts_layout.addWidget(QLabel("مبلغ الحوالة:"), 0, 0)
        amounts_layout.addWidget(self.amount_label, 0, 1)
        amounts_layout.addWidget(QLabel("العملة:"), 0, 2)
        amounts_layout.addWidget(self.currency_label, 0, 3)

        amounts_layout.addWidget(QLabel("سعر الصرف:"), 1, 0)
        amounts_layout.addWidget(self.exchange_rate_label, 1, 1)
        amounts_layout.addWidget(QLabel("المبلغ بالعملة الأساسية:"), 1, 2)
        amounts_layout.addWidget(self.amount_base_currency_label, 1, 3)

        layout.addWidget(amounts_group)

        # الرسوم والتكاليف
        charges_group = QGroupBox("الرسوم والتكاليف")
        charges_layout = QGridLayout(charges_group)

        self.transfer_fee_label = QLabel("-")
        self.bank_charges_label = QLabel("-")
        self.other_charges_label = QLabel("-")
        self.total_charges_label = QLabel("-")
        self.net_amount_label = QLabel("-")

        charges_layout.addWidget(QLabel("رسوم التحويل:"), 0, 0)
        charges_layout.addWidget(self.transfer_fee_label, 0, 1)
        charges_layout.addWidget(QLabel("رسوم البنك:"), 0, 2)
        charges_layout.addWidget(self.bank_charges_label, 0, 3)

        charges_layout.addWidget(QLabel("رسوم أخرى:"), 1, 0)
        charges_layout.addWidget(self.other_charges_label, 1, 1)
        charges_layout.addWidget(QLabel("إجمالي الرسوم:"), 1, 2)
        charges_layout.addWidget(self.total_charges_label, 1, 3)

        charges_layout.addWidget(QLabel("المبلغ الصافي:"), 2, 0)
        charges_layout.addWidget(self.net_amount_label, 2, 1, 1, 3)

        # تنسيق المبلغ الصافي
        self.net_amount_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #059669;
                background-color: #ecfdf5;
                padding: 8px;
                border-radius: 6px;
                border: 1px solid #a7f3d0;
            }
        """)

        layout.addWidget(charges_group)

        # معلومات البنوك
        banks_group = QGroupBox("معلومات البنوك")
        banks_layout = QGridLayout(banks_group)

        self.sender_bank_label = QLabel("-")
        self.sender_account_label = QLabel("-")
        self.receiver_bank_label = QLabel("-")
        self.receiver_account_label = QLabel("-")

        banks_layout.addWidget(QLabel("البنك المرسل:"), 0, 0)
        banks_layout.addWidget(self.sender_bank_label, 0, 1)
        banks_layout.addWidget(QLabel("حساب المرسل:"), 0, 2)
        banks_layout.addWidget(self.sender_account_label, 0, 3)

        banks_layout.addWidget(QLabel("البنك المستقبل:"), 1, 0)
        banks_layout.addWidget(self.receiver_bank_label, 1, 1)
        banks_layout.addWidget(QLabel("حساب المستقبل:"), 1, 2)
        banks_layout.addWidget(self.receiver_account_label, 1, 3)

        layout.addWidget(banks_group)
        layout.addStretch()

        return tab

    def create_status_tracking_tab(self):
        """إنشاء تبويب تتبع الحالة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # الحالة الحالية
        current_status_group = QGroupBox("الحالة الحالية")
        current_status_layout = QHBoxLayout(current_status_group)

        self.current_status_icon = QLabel("⏳")
        self.current_status_icon.setStyleSheet("font-size: 24px;")
        self.current_status_name = QLabel("غير محدد")
        self.current_status_name.setStyleSheet("font-size: 18px; font-weight: bold;")

        current_status_layout.addWidget(self.current_status_icon)
        current_status_layout.addWidget(self.current_status_name)
        current_status_layout.addStretch()

        layout.addWidget(current_status_group)

        # تاريخ الحالات
        history_group = QGroupBox("تاريخ الحالات")
        history_layout = QVBoxLayout(history_group)

        self.status_history_table = QTableWidget()
        self.status_history_table.setColumnCount(5)
        self.status_history_table.setHorizontalHeaderLabels([
            "التاريخ", "الحالة السابقة", "الحالة الجديدة", "المستخدم", "الملاحظات"
        ])

        # إعدادات جدول التاريخ
        history_header = self.status_history_table.horizontalHeader()
        history_header.setStretchLastSection(True)
        history_header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        history_header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        history_header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        history_header.setSectionResizeMode(3, QHeaderView.ResizeToContents)

        self.status_history_table.setAlternatingRowColors(True)
        self.status_history_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        history_layout.addWidget(self.status_history_table)
        layout.addWidget(history_group)

        return tab

    def create_documents_tab(self):
        """إنشاء تبويب المستندات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # أزرار إدارة المستندات
        docs_actions_frame = QFrame()
        docs_actions_layout = QHBoxLayout(docs_actions_frame)

        self.upload_doc_btn = QPushButton("📁 رفع مستند")
        self.upload_doc_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)

        self.download_doc_btn = QPushButton("⬇️ تحميل")
        self.download_doc_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)

        self.delete_doc_btn = QPushButton("🗑️ حذف")
        self.delete_doc_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #dc2626;
            }
        """)

        docs_actions_layout.addWidget(self.upload_doc_btn)
        docs_actions_layout.addWidget(self.download_doc_btn)
        docs_actions_layout.addWidget(self.delete_doc_btn)
        docs_actions_layout.addStretch()

        layout.addWidget(docs_actions_frame)

        # جدول المستندات
        self.documents_table = QTableWidget()
        self.documents_table.setColumnCount(6)
        self.documents_table.setHorizontalHeaderLabels([
            "اسم المستند", "النوع", "الحجم", "تاريخ الرفع", "رفع بواسطة", "محقق"
        ])

        # إعدادات جدول المستندات
        docs_header = self.documents_table.horizontalHeader()
        docs_header.setStretchLastSection(True)
        docs_header.setSectionResizeMode(0, QHeaderView.Stretch)
        docs_header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        docs_header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        docs_header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        docs_header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        docs_header.setSectionResizeMode(5, QHeaderView.ResizeToContents)

        self.documents_table.setAlternatingRowColors(True)
        self.documents_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        layout.addWidget(self.documents_table)

        return tab

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # إضافة حوالة جديدة
        new_action = QAction("➕ حوالة جديدة", self)
        new_action.setStatusTip("إنشاء حوالة جديدة")
        new_action.triggered.connect(self.create_new_remittance)
        toolbar.addAction(new_action)

        toolbar.addSeparator()

        # تحديث البيانات
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setStatusTip("تحديث قائمة الحوالات")
        refresh_action.triggered.connect(self.load_remittances)
        toolbar.addAction(refresh_action)

        # تصدير البيانات
        export_action = QAction("📊 تصدير", self)
        export_action.setStatusTip("تصدير البيانات إلى Excel")
        export_action.triggered.connect(self.export_data)
        toolbar.addAction(export_action)

        toolbar.addSeparator()

        # التقارير
        reports_action = QAction("📈 التقارير", self)
        reports_action.setStatusTip("عرض التقارير المالية")
        reports_action.triggered.connect(self.show_reports)
        toolbar.addAction(reports_action)

        # الإعدادات
        settings_action = QAction("⚙️ الإعدادات", self)
        settings_action.setStatusTip("إعدادات نظام الحوالات")
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)

        toolbar.addSeparator()

        # زر الخروج
        exit_action = QAction("❌ خروج", self)
        exit_action.setStatusTip("إغلاق نافذة إدارة الحوالات")
        exit_action.triggered.connect(self.close)
        toolbar.addAction(exit_action)

    def setup_statusbar(self):
        """إعداد شريط الحالة"""
        self.statusbar = self.statusBar()

        # عداد الحوالات
        self.count_label = QLabel("الحوالات: 0")
        self.statusbar.addWidget(self.count_label)

        # فاصل
        self.statusbar.addPermanentWidget(QLabel("|"))

        # حالة الاتصال بقاعدة البيانات
        self.db_status_label = QLabel("قاعدة البيانات: متصل")
        self.db_status_label.setStyleSheet("color: green;")
        self.statusbar.addPermanentWidget(self.db_status_label)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusbar.addPermanentWidget(self.progress_bar)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # البحث والفلاتر
        self.search_input.textChanged.connect(self.on_search_changed)
        self.search_btn.clicked.connect(self.apply_filters)
        self.clear_btn.clicked.connect(self.clear_filters)

        # فلاتر الكومبو بوكس
        self.supplier_combo.currentTextChanged.connect(self.apply_filters)
        self.currency_combo.currentTextChanged.connect(self.apply_filters)
        self.status_combo.currentTextChanged.connect(self.apply_filters)

        # تغيير التواريخ
        self.date_from.dateChanged.connect(self.apply_filters)
        self.date_to.dateChanged.connect(self.apply_filters)

        # جدول الحوالات
        self.remittances_table.itemSelectionChanged.connect(self.on_remittance_selected)
        self.remittances_table.itemDoubleClicked.connect(self.edit_remittance)

        # أزرار الإجراءات
        self.edit_btn.clicked.connect(self.edit_remittance)
        self.delete_btn.clicked.connect(self.delete_remittance)
        self.print_btn.clicked.connect(self.print_remittance)

        # أزرار المستندات
        self.upload_doc_btn.clicked.connect(self.upload_document)
        self.download_doc_btn.clicked.connect(self.download_document)
        self.delete_doc_btn.clicked.connect(self.delete_document)

    def load_remittances(self):
        """تحميل قائمة الحوالات"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد

            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # استعلام الحوالات مع معلومات المورد والعملة
            query = """
                SELECT
                    r.id, r.remittance_number, r.remittance_date,
                    r.sender_name, r.receiver_name, s.name as supplier_name,
                    r.amount, c.code as currency_code, r.current_status,
                    r.created_at
                FROM remittances r
                LEFT JOIN suppliers s ON r.supplier_id = s.id
                LEFT JOIN currencies c ON r.currency_id = c.id
                WHERE r.is_active = 1
                ORDER BY r.created_at DESC
            """

            cursor.execute(query)
            remittances = cursor.fetchall()

            # تحديث الجدول
            self.update_remittances_table(remittances)

            # تحديث الإحصائيات
            self.update_statistics()

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الحوالات:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def update_remittances_table(self, remittances):
        """تحديث جدول الحوالات"""
        self.current_remittances = remittances
        self.remittances_table.setRowCount(len(remittances))

        for row, remittance in enumerate(remittances):
            # رقم الحوالة
            self.remittances_table.setItem(row, 0, QTableWidgetItem(str(remittance[1] or "")))

            # تاريخ الحوالة
            date_str = ""
            if remittance[2]:
                try:
                    date_obj = datetime.fromisoformat(remittance[2].replace('Z', '+00:00'))
                    date_str = date_obj.strftime('%Y-%m-%d')
                except:
                    date_str = str(remittance[2])
            self.remittances_table.setItem(row, 1, QTableWidgetItem(date_str))

            # المرسل والمستقبل
            self.remittances_table.setItem(row, 2, QTableWidgetItem(str(remittance[3] or "")))
            self.remittances_table.setItem(row, 3, QTableWidgetItem(str(remittance[4] or "")))

            # المورد
            self.remittances_table.setItem(row, 4, QTableWidgetItem(str(remittance[5] or "")))

            # المبلغ
            amount_str = f"{remittance[6]:,.2f}" if remittance[6] else "0.00"
            self.remittances_table.setItem(row, 5, QTableWidgetItem(amount_str))

            # العملة
            self.remittances_table.setItem(row, 6, QTableWidgetItem(str(remittance[7] or "")))

            # الحالة
            status_item = QTableWidgetItem(str(remittance[8] or ""))
            # تلوين الحالة
            if remittance[8] == "مكتملة":
                status_item.setBackground(QColor("#dcfce7"))
            elif remittance[8] == "معلقة":
                status_item.setBackground(QColor("#fef3c7"))
            elif remittance[8] == "ملغية":
                status_item.setBackground(QColor("#fee2e2"))
            self.remittances_table.setItem(row, 7, status_item)

            # تاريخ الإنشاء
            created_str = ""
            if remittance[9]:
                try:
                    created_obj = datetime.fromisoformat(remittance[9].replace('Z', '+00:00'))
                    created_str = created_obj.strftime('%Y-%m-%d %H:%M')
                except:
                    created_str = str(remittance[9])
            self.remittances_table.setItem(row, 8, QTableWidgetItem(created_str))

        # تحديث عداد الحوالات
        self.count_label.setText(f"الحوالات: {len(remittances)}")

    def update_statistics(self):
        """تحديث الإحصائيات السريعة"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إجمالي الحوالات
            cursor.execute("SELECT COUNT(*) FROM remittances WHERE is_active = 1")
            total_count = cursor.fetchone()[0]

            # الحوالات المعلقة
            cursor.execute("SELECT COUNT(*) FROM remittances WHERE is_active = 1 AND current_status = 'معلقة'")
            pending_count = cursor.fetchone()[0]

            # الحوالات المكتملة
            cursor.execute("SELECT COUNT(*) FROM remittances WHERE is_active = 1 AND current_status = 'مكتملة'")
            completed_count = cursor.fetchone()[0]

            # إجمالي المبلغ
            cursor.execute("SELECT SUM(amount) FROM remittances WHERE is_active = 1")
            total_amount = cursor.fetchone()[0] or 0

            # تحديث التسميات
            self.total_count_label.setText(f"الإجمالي: {total_count}")
            self.pending_count_label.setText(f"معلقة: {pending_count}")
            self.completed_count_label.setText(f"مكتملة: {completed_count}")
            self.total_amount_label.setText(f"المبلغ الإجمالي: {total_amount:,.2f}")

            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def load_filters_data(self):
        """تحميل بيانات الفلاتر"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل الموردين
            cursor.execute("SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name")
            suppliers = cursor.fetchall()
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier[1], supplier[0])

            # تحميل العملات
            cursor.execute("SELECT id, code, name FROM currencies WHERE is_active = 1 ORDER BY name")
            currencies = cursor.fetchall()
            for currency in currencies:
                self.currency_combo.addItem(f"{currency[1]} - {currency[2]}", currency[0])

            # تحميل حالات الحوالات
            cursor.execute("SELECT id, name FROM remittance_statuses WHERE is_active = 1 ORDER BY order_index")
            statuses = cursor.fetchall()
            for status in statuses:
                self.status_combo.addItem(status[1], status[0])

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الفلاتر: {e}")

    def on_search_changed(self):
        """معالج تغيير نص البحث"""
        # تطبيق البحث بعد تأخير قصير
        if hasattr(self, 'search_timer'):
            self.search_timer.stop()

        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.apply_filters)
        self.search_timer.start(500)  # تأخير 500 مللي ثانية

    def apply_filters(self):
        """تطبيق الفلاتر على قائمة الحوالات"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)

            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # بناء الاستعلام مع الفلاتر
            query = """
                SELECT
                    r.id, r.remittance_number, r.remittance_date,
                    r.sender_name, r.receiver_name, s.name as supplier_name,
                    r.amount, c.code as currency_code, r.current_status,
                    r.created_at
                FROM remittances r
                LEFT JOIN suppliers s ON r.supplier_id = s.id
                LEFT JOIN currencies c ON r.currency_id = c.id
                WHERE r.is_active = 1
            """

            params = []

            # فلتر البحث النصي
            search_text = self.search_input.text().strip()
            if search_text:
                query += """ AND (
                    r.remittance_number LIKE ? OR
                    r.sender_name LIKE ? OR
                    r.receiver_name LIKE ? OR
                    r.reference_number LIKE ?
                )"""
                search_param = f"%{search_text}%"
                params.extend([search_param, search_param, search_param, search_param])

            # فلتر المورد
            supplier_id = self.supplier_combo.currentData()
            if supplier_id:
                query += " AND r.supplier_id = ?"
                params.append(supplier_id)

            # فلتر العملة
            currency_id = self.currency_combo.currentData()
            if currency_id:
                query += " AND r.currency_id = ?"
                params.append(currency_id)

            # فلتر الحالة
            status_id = self.status_combo.currentData()
            if status_id:
                query += " AND r.status_id = ?"
                params.append(status_id)

            # فلتر التاريخ
            date_from = self.date_from.date().toString("yyyy-MM-dd")
            date_to = self.date_to.date().toString("yyyy-MM-dd")
            query += " AND DATE(r.remittance_date) BETWEEN ? AND ?"
            params.extend([date_from, date_to])

            query += " ORDER BY r.created_at DESC"

            cursor.execute(query, params)
            remittances = cursor.fetchall()

            self.update_remittances_table(remittances)
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تطبيق الفلاتر:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.search_input.clear()
        self.supplier_combo.setCurrentIndex(0)
        self.currency_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(0)
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_to.setDate(QDate.currentDate())
        self.load_remittances()

    def on_remittance_selected(self):
        """معالج اختيار حوالة من الجدول"""
        current_row = self.remittances_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_remittances):
            remittance_data = self.current_remittances[current_row]
            self.selected_remittance_id = remittance_data[0]
            self.load_remittance_details(self.selected_remittance_id)

    def load_remittance_details(self, remittance_id):
        """تحميل تفاصيل الحوالة المحددة"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # استعلام تفاصيل الحوالة
            query = """
                SELECT
                    r.*, s.name as supplier_name, c.code as currency_code, c.name as currency_name,
                    sb.name as sender_bank_name, rb.name as receiver_bank_name,
                    sa.account_number as sender_account_number, ra.account_number as receiver_account_number
                FROM remittances r
                LEFT JOIN suppliers s ON r.supplier_id = s.id
                LEFT JOIN currencies c ON r.currency_id = c.id
                LEFT JOIN banks sb ON r.sender_bank_id = sb.id
                LEFT JOIN banks rb ON r.receiver_bank_id = rb.id
                LEFT JOIN bank_accounts sa ON r.sender_account_id = sa.id
                LEFT JOIN bank_accounts ra ON r.receiver_account_id = ra.id
                WHERE r.id = ?
            """

            cursor.execute(query, (remittance_id,))
            remittance = cursor.fetchone()

            if remittance:
                self.update_remittance_details_display(remittance)
                self.load_status_history(remittance_id)
                self.load_documents(remittance_id)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل تفاصيل الحوالة:\n{str(e)}")

    def update_remittance_details_display(self, remittance):
        """تحديث عرض تفاصيل الحوالة"""
        # المعلومات الأساسية
        self.remittance_number_label.setText(str(remittance[1] or ""))

        if remittance[2]:
            try:
                date_obj = datetime.fromisoformat(remittance[2].replace('Z', '+00:00'))
                self.remittance_date_label.setText(date_obj.strftime('%Y-%m-%d'))
            except:
                self.remittance_date_label.setText(str(remittance[2]))
        else:
            self.remittance_date_label.setText("-")

        self.reference_number_label.setText(str(remittance[3] or "-"))
        self.current_status_label.setText(str(remittance[26] or "-"))

        # معلومات المرسل
        self.sender_name_label.setText(str(remittance[4] or "-"))
        self.sender_id_label.setText(str(remittance[5] or "-"))
        self.sender_phone_label.setText(str(remittance[6] or "-"))
        self.sender_address_label.setText(str(remittance[7] or "-"))

        # معلومات المستقبل
        self.receiver_name_label.setText(str(remittance[8] or "-"))
        self.receiver_id_label.setText(str(remittance[9] or "-"))
        self.receiver_phone_label.setText(str(remittance[10] or "-"))
        self.receiver_address_label.setText(str(remittance[11] or "-"))

        # البيانات المالية
        self.amount_label.setText(f"{remittance[13]:,.2f}" if remittance[13] else "0.00")
        self.currency_label.setText(f"{remittance[35]} - {remittance[36]}" if remittance[35] else "-")
        self.exchange_rate_label.setText(str(remittance[15] or "1.0"))
        self.amount_base_currency_label.setText(f"{remittance[16]:,.2f}" if remittance[16] else "0.00")

        # الرسوم
        self.transfer_fee_label.setText(f"{remittance[17]:,.2f}" if remittance[17] else "0.00")
        self.bank_charges_label.setText(f"{remittance[18]:,.2f}" if remittance[18] else "0.00")
        self.other_charges_label.setText(f"{remittance[19]:,.2f}" if remittance[19] else "0.00")
        self.total_charges_label.setText(f"{remittance[20]:,.2f}" if remittance[20] else "0.00")
        self.net_amount_label.setText(f"{remittance[21]:,.2f}" if remittance[21] else "0.00")

        # معلومات البنوك
        self.sender_bank_label.setText(str(remittance[37] or "-"))
        self.sender_account_label.setText(str(remittance[39] or "-"))
        self.receiver_bank_label.setText(str(remittance[38] or "-"))
        self.receiver_account_label.setText(str(remittance[40] or "-"))

        # الملاحظات
        self.notes_text.setPlainText(str(remittance[32] or ""))

    # دوال الإجراءات
    def create_new_remittance(self):
        """إنشاء حوالة جديدة"""
        from .new_remittance_dialog import NewRemittanceDialog

        dialog = NewRemittanceDialog(self)
        dialog.remittance_created.connect(self.on_remittance_created)
        dialog.exec()

    def on_remittance_created(self, remittance_id):
        """معالج إنشاء حوالة جديدة"""
        # تحديث قائمة الحوالات
        self.load_remittances()

        # اختيار الحوالة الجديدة
        for row in range(self.remittances_table.rowCount()):
            if self.current_remittances[row][0] == remittance_id:
                self.remittances_table.selectRow(row)
                break

    def edit_remittance(self):
        """تعديل الحوالة المحددة"""
        if self.selected_remittance_id:
            QMessageBox.information(self, "قريباً", "نافذة تعديل الحوالة قيد التطوير")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حوالة للتعديل")

    def delete_remittance(self):
        """حذف الحوالة المحددة"""
        if self.selected_remittance_id:
            reply = QMessageBox.question(self, "تأكيد الحذف",
                                       "هل أنت متأكد من حذف هذه الحوالة؟",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                QMessageBox.information(self, "قريباً", "وظيفة الحذف قيد التطوير")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حوالة للحذف")

    def print_remittance(self):
        """طباعة الحوالة"""
        if self.selected_remittance_id:
            QMessageBox.information(self, "قريباً", "وظيفة الطباعة قيد التطوير")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حوالة للطباعة")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "قريباً", "وظيفة التصدير قيد التطوير")

    def show_reports(self):
        """عرض التقارير"""
        QMessageBox.information(self, "قريباً", "نافذة التقارير قيد التطوير")

    def show_settings(self):
        """عرض الإعدادات"""
        QMessageBox.information(self, "قريباً", "نافذة الإعدادات قيد التطوير")

    def load_status_history(self, remittance_id):
        """تحميل تاريخ حالات الحوالة"""
        # ستكتمل لاحقاً
        pass

    def load_documents(self, remittance_id):
        """تحميل مستندات الحوالة"""
        # ستكتمل لاحقاً
        pass

    def upload_document(self):
        """رفع مستند جديد"""
        QMessageBox.information(self, "قريباً", "وظيفة رفع المستندات قيد التطوير")

    def download_document(self):
        """تحميل مستند"""
        QMessageBox.information(self, "قريباً", "وظيفة تحميل المستندات قيد التطوير")

    def delete_document(self):
        """حذف مستند"""
        QMessageBox.information(self, "قريباً", "وظيفة حذف المستندات قيد التطوير")
