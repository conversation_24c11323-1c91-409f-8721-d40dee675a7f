# -*- coding: utf-8 -*-
"""
إضافة حقل العملة لمندوبي المشتريات
Add Currency Field to Purchase Representatives Migration
"""

import sqlite3
import os

def add_currency_field():
    """إضافة حقل العملة لجدول مندوبي المشتريات"""
    
    # مسار قاعدة البيانات
    db_path = 'data/proshipment.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 فحص وجود حقل العملة...")
        
        # فحص بنية الجدول
        cursor.execute("PRAGMA table_info(purchase_representatives)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'currency' not in column_names:
            print("🔨 إضافة حقل العملة...")
            
            # إضافة حقل العملة
            cursor.execute("""
                ALTER TABLE purchase_representatives 
                ADD COLUMN currency VARCHAR(50) DEFAULT 'ريال سعودي'
            """)
            
            # تحديث القيم الموجودة
            cursor.execute("""
                UPDATE purchase_representatives 
                SET currency = 'ريال سعودي' 
                WHERE currency IS NULL
            """)
            
            print("✅ تم إضافة حقل العملة بنجاح!")
        else:
            print("ℹ️ حقل العملة موجود بالفعل")
        
        # حفظ التغييرات
        conn.commit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة حقل العملة: {str(e)}")
        return False
        
    finally:
        if conn:
            conn.close()

def test_currency_field():
    """اختبار حقل العملة"""
    try:
        db_path = 'data/proshipment.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🧪 اختبار حقل العملة...")
        
        # عرض بعض المندوبين مع العملة
        cursor.execute("""
            SELECT name, currency 
            FROM purchase_representatives 
            WHERE is_active = 1 
            LIMIT 3
        """)
        
        representatives = cursor.fetchall()
        print("👥 المندوبين مع العملة:")
        for rep in representatives:
            print(f"   - {rep[0]}: {rep[1]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إضافة حقل العملة لمندوبي المشتريات")
    print("=" * 50)
    
    if add_currency_field():
        print("\n" + "=" * 50)
        test_currency_field()
        print("\n🎉 تم إكمال العملية بنجاح!")
    else:
        print("\n❌ فشل في إضافة حقل العملة")
