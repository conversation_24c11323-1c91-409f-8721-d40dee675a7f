/* ستايل حديث ومتطور لنظام إدارة الشحنات */

/* النافذة الرئيسية */
QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #f8f9fa, stop:0.5 #e9ecef, stop:1 #dee2e6);
    font-family: "Segoe UI", "Arial", sans-serif;
}

/* شريط القوائم */
QMenuBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #2c3e50, stop:1 #34495e);
    color: white;
    border: none;
    padding: 8px;
    font-size: 13px;
    font-weight: bold;
}

QMenuBar::item {
    background: transparent;
    padding: 10px 18px;
    border-radius: 6px;
    margin: 2px;
    transition: all 0.3s ease;
}

QMenuBar::item:selected {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

QMenuBar::item:pressed {
    background: rgba(255, 255, 255, 0.1);
}

/* القوائم المنسدلة */
QMenu {
    background: white;
    border: 2px solid #bdc3c7;
    border-radius: 10px;
    padding: 8px;
    font-size: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

QMenu::item {
    padding: 10px 25px;
    border-radius: 6px;
    margin: 2px;
    transition: all 0.2s ease;
}

QMenu::item:selected {
    background: #3498db;
    color: white;
}

QMenu::separator {
    height: 1px;
    background: #ecf0f1;
    margin: 5px 10px;
}

/* شريط الأدوات */
QToolBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #34495e, stop:1 #2c3e50);
    border: none;
    spacing: 8px;
    padding: 8px;
}

QToolBar QToolButton {
    background: transparent;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
}

QToolBar QToolButton:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

QToolBar QToolButton:pressed {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(0px);
}

/* شريط الحالة */
QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ecf0f1, stop:1 #bdc3c7);
    border-top: 2px solid #95a5a6;
    color: #2c3e50;
    font-size: 12px;
    font-weight: bold;
    padding: 5px;
}

QStatusBar QLabel {
    padding: 5px 10px;
    border-radius: 4px;
    background: transparent;
}

/* الأزرار العامة */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3498db, stop:1 #2980b9);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #2980b9, stop:1 #3498db);
}

QPushButton:pressed {
    background: #21618c;
}

QPushButton:disabled {
    background: #bdc3c7;
    color: #7f8c8d;
}

/* حقول الإدخال */
QLineEdit, QTextEdit, QPlainTextEdit {
    background: white;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    padding: 10px;
    font-size: 12px;
    selection-background-color: #3498db;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #3498db;
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
}

/* القوائم المنسدلة */
QComboBox {
    background: white;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 12px;
    min-width: 120px;
}

QComboBox:focus {
    border-color: #3498db;
}

QComboBox::drop-down {
    border: none;
    width: 30px;
}

QComboBox::down-arrow {
    image: url(down_arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background: white;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    selection-background-color: #3498db;
    padding: 5px;
}

/* الجداول */
QTableWidget, QTableView {
    background: white;
    border: 1px solid #ecf0f1;
    border-radius: 8px;
    gridline-color: #ecf0f1;
    font-size: 11px;
}

QTableWidget::item, QTableView::item {
    padding: 8px;
    border-bottom: 1px solid #ecf0f1;
}

QTableWidget::item:selected, QTableView::item:selected {
    background: #3498db;
    color: white;
}

QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #34495e, stop:1 #2c3e50);
    color: white;
    padding: 10px;
    border: none;
    font-weight: bold;
    font-size: 11px;
}

/* أشرطة التمرير */
QScrollBar:vertical {
    background: #ecf0f1;
    width: 14px;
    border-radius: 7px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #3498db, stop:1 #2980b9);
    border-radius: 7px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #2980b9, stop:1 #3498db);
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background: #ecf0f1;
    height: 14px;
    border-radius: 7px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3498db, stop:1 #2980b9);
    border-radius: 7px;
    min-width: 30px;
    margin: 2px;
}

/* التبويبات */
QTabWidget::pane {
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    background: white;
}

QTabBar::tab {
    background: #ecf0f1;
    color: #2c3e50;
    padding: 10px 20px;
    margin: 2px;
    border-radius: 6px 6px 0 0;
    font-weight: bold;
    font-size: 11px;
}

QTabBar::tab:selected {
    background: #3498db;
    color: white;
}

QTabBar::tab:hover {
    background: #bdc3c7;
}

/* مربعات الاختيار */
QCheckBox {
    font-size: 12px;
    color: #2c3e50;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #bdc3c7;
    border-radius: 4px;
    background: white;
}

QCheckBox::indicator:checked {
    background: #3498db;
    border-color: #3498db;
    image: url(check.png);
}

/* أزرار الراديو */
QRadioButton {
    font-size: 12px;
    color: #2c3e50;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #bdc3c7;
    border-radius: 9px;
    background: white;
}

QRadioButton::indicator:checked {
    background: #3498db;
    border-color: #3498db;
}

/* شريط التقدم */
QProgressBar {
    background: #ecf0f1;
    border: none;
    border-radius: 8px;
    height: 16px;
    text-align: center;
    font-size: 11px;
    font-weight: bold;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #3498db, stop:1 #2980b9);
    border-radius: 8px;
}

/* الإطارات */
QFrame {
    border-radius: 8px;
}

QFrame[frameShape="1"] { /* StyledPanel */
    border: 1px solid #ecf0f1;
    background: white;
}

/* التسميات */
QLabel {
    color: #2c3e50;
    font-size: 12px;
}

/* نصائح الأدوات */
QToolTip {
    background: #2c3e50;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px;
    font-size: 11px;
    opacity: 230;
}
