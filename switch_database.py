#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تبديل قاعدة البيانات
Database Switching Tool
"""

import json
import sys
from pathlib import Path

def load_config(config_file):
    """تحميل ملف التكوين"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ خطأ في تحميل ملف التكوين: {e}")
        return None

def save_config(config_file, config):
    """حفظ ملف التكوين"""
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"❌ خطأ في حفظ ملف التكوين: {e}")
        return False

def switch_to_sqlite(config_file):
    """التبديل إلى SQLite"""
    print("🔄 التبديل إلى SQLite...")
    
    config = load_config(config_file)
    if config is None:
        return False
    
    config["type"] = "sqlite"
    
    if save_config(config_file, config):
        print("✅ تم التبديل إلى SQLite بنجاح!")
        print("📁 ملف قاعدة البيانات: data/proshipment.db")
        return True
    
    return False

def switch_to_oracle(config_file):
    """التبديل إلى Oracle"""
    print("🔄 التبديل إلى Oracle...")
    
    config = load_config(config_file)
    if config is None:
        return False
    
    config["type"] = "oracle"
    
    if save_config(config_file, config):
        print("✅ تم التبديل إلى Oracle بنجاح!")
        oracle_config = config.get("oracle_config", {})
        print(f"🖥️ الخادم: {oracle_config.get('host', 'localhost')}:{oracle_config.get('port', 1521)}")
        print(f"🗄️ قاعدة البيانات: {oracle_config.get('service_name', 'orcl')}")
        print(f"👤 المستخدم: {oracle_config.get('username', 'proshipment')}")
        return True
    
    return False

def show_current_config(config_file):
    """عرض التكوين الحالي"""
    print("📊 التكوين الحالي:")
    print("-" * 30)
    
    config = load_config(config_file)
    if config is None:
        return
    
    db_type = config.get("type", "غير محدد")
    print(f"نوع قاعدة البيانات: {db_type}")
    
    if db_type == "sqlite":
        sqlite_config = config.get("sqlite_config", {})
        print(f"مسار الملف: {sqlite_config.get('path', 'data/proshipment.db')}")
        print(f"مهلة الاتصال: {sqlite_config.get('timeout', 30)} ثانية")
        
    elif db_type == "oracle":
        oracle_config = config.get("oracle_config", {})
        print(f"الخادم: {oracle_config.get('host', 'localhost')}")
        print(f"المنفذ: {oracle_config.get('port', 1521)}")
        print(f"اسم الخدمة: {oracle_config.get('service_name', 'orcl')}")
        print(f"المستخدم: {oracle_config.get('username', 'proshipment')}")
        print(f"نوع الاتصال: {oracle_config.get('connection_type', 'service_name')}")

def show_help():
    """عرض المساعدة"""
    print("🔧 أداة تبديل قاعدة البيانات")
    print("=" * 40)
    print("الاستخدام:")
    print("  python switch_database.py [الأمر]")
    print()
    print("الأوامر المتاحة:")
    print("  sqlite    - التبديل إلى SQLite")
    print("  oracle    - التبديل إلى Oracle")
    print("  status    - عرض التكوين الحالي")
    print("  help      - عرض هذه المساعدة")
    print()
    print("أمثلة:")
    print("  python switch_database.py sqlite")
    print("  python switch_database.py oracle")
    print("  python switch_database.py status")

def main():
    """الدالة الرئيسية"""
    config_file = "src/database/config/database.json"
    
    if not Path(config_file).exists():
        print(f"❌ ملف التكوين غير موجود: {config_file}")
        return 1
    
    if len(sys.argv) < 2:
        show_help()
        return 0
    
    command = sys.argv[1].lower()
    
    if command == "sqlite":
        success = switch_to_sqlite(config_file)
        if success:
            print("\n💡 نصيحة: أعد تشغيل التطبيق لتطبيق التغييرات")
        return 0 if success else 1
        
    elif command == "oracle":
        success = switch_to_oracle(config_file)
        if success:
            print("\n💡 نصيحة: تأكد من تشغيل خادم Oracle وأعد تشغيل التطبيق")
        return 0 if success else 1
        
    elif command == "status":
        show_current_config(config_file)
        return 0
        
    elif command == "help":
        show_help()
        return 0
        
    else:
        print(f"❌ أمر غير معروف: {command}")
        print("استخدم 'help' لعرض الأوامر المتاحة")
        return 1

if __name__ == "__main__":
    sys.exit(main())
