# 🗺️ نافذة مسارات الشحن التفاعلية - وضع ملء الشاشة

## نظرة عامة
نافذة مسارات الشحن التفاعلية هي ميزة متقدمة تتيح عرض وتتبع مسارات الشحنات على خريطة تفاعلية مع إمكانيات تحليل وإحصائيات شاملة. **تفتح النافذة تلقائياً في وضع ملء الشاشة** للاستفادة القصوى من المساحة المتاحة وتوفير تجربة مستخدم محسنة.

## الملفات المتضمنة
- `shipping_routes_window.py` - النافذة الرئيسية لمسارات الشحن
- `advanced_shipment_tracking_window.py` - تم تحديثها لدعم زر عرض المسارات

## الميزات الرئيسية

### 🖥️ وضع ملء الشاشة المحسن (جديد!)
- **فتح تلقائي في ملء الشاشة**: النافذة تفتح تلقائياً في وضع ملء الشاشة للاستفادة القصوى من المساحة
- **تبديل أوضاع العرض**: زر مخصص أو مفتاح F11 للتبديل بين ملء الشاشة والوضع العادي
- **إخفاء/إظهار الشريط الجانبي**: زر لإخفاء الشريط الجانبي وإعطاء مساحة كاملة للخريطة
- **شريط حالة محسن**: عرض معلومات مفصلة عن:
  - مستوى التكبير الحالي
  - الإحداثيات الحية للماوس
  - الشحنة المحددة حالياً
  - مفاتيح الاختصار المتاحة
- **مفاتيح اختصار شاملة**: دعم كامل لجميع العمليات عبر لوحة المفاتيح

### 🌍 خريطة تفاعلية مطورة
- **عرض مرئي للمسارات**: رسم مسارات الشحن بين الموانئ المختلفة
- **تفاعل مع الشحنات**: النقر على الشحنات لعرض تفاصيلها
- **تكبير وتصغير**: إمكانية التكبير والتصغير باستخدام عجلة الماوس أو الأزرار
- **ألوان مميزة**: ألوان مختلفة للمسارات حسب حالة الشحنة
  - أزرق: مسارات نشطة
  - أخضر: مسارات مكتملة
  - أحمر: مسارات متأخرة

### 📋 إدارة الشحنات
- **جدول شامل**: عرض جميع الشحنات مع معلومات أساسية
- **فلترة متقدمة**: فلترة حسب الحالة وشركة الشحن
- **تفاصيل مفصلة**: عرض تفاصيل كاملة لكل شحنة
- **تتبع مباشر**: تركيز الخريطة على الشحنة المحددة

### 📊 إحصائيات وتحليلات
- **إحصائيات عامة**: عدد الشحنات النشطة والمكتملة والمتأخرة
- **متوسط وقت النقل**: حساب متوسط أوقات النقل
- **إحصائيات الموانئ**: أكثر الموانئ استخداماً
- **تحليل الأداء**: مؤشرات الأداء الرئيسية

### 🚨 نظام التنبيهات
- **تنبيهات التأخير**: تنبيهات للشحنات المتأخرة
- **تنبيهات التتبع**: تنبيهات للشحنات بدون رقم تتبع
- **إعدادات قابلة للتخصيص**: تخصيص حدود التأخير
- **تحديث تلقائي**: تحديث التنبيهات تلقائياً

## مفاتيح الاختصار الجديدة

### التحكم في النافذة
- **F11**: تبديل بين وضع ملء الشاشة والوضع العادي
- **Ctrl+H**: إخفاء/إظهار الشريط الجانبي
- **Escape**: الخروج من وضع ملء الشاشة أو إغلاق النافذة

### التحكم في الخريطة
- **Ctrl++**: تكبير الخريطة
- **Ctrl+-**: تصغير الخريطة
- **Ctrl+0**: إعادة تعيين مستوى التكبير
- **مفاتيح الأسهم**: تحريك الخريطة في جميع الاتجاهات

### العمليات العامة
- **Ctrl+R**: تحديث البيانات
- **Ctrl+E**: تصدير البيانات

## كيفية الاستخدام

### 1. فتح نافذة المسارات (وضع ملء الشاشة)
```python
# من نافذة التتبع المتقدم
# انقر على زر "🗺️ عرض المسارات" في تبويب الخريطة والمواقع
# ستفتح النافذة تلقائياً في وضع ملء الشاشة للاستفادة القصوى من المساحة
```

### 2. التنقل في الخريطة المحسنة
- **تكبير ذكي**: استخدم عجلة الماوس للتكبير حول موقع المؤشر مع عرض النسبة في شريط الحالة
- **سحب محسن**: اسحب الخريطة بالماوس مع تتبع الإحداثيات الحية في شريط الحالة
- **اختيار الشحنات**: انقر على الشحنة في الخريطة أو الجدول مع عرض التفاصيل في شريط الحالة
- **تحكم بالأسهم**: استخدم مفاتيح الأسهم للتنقل السلس في الخريطة
- **إدارة المساحة**: اضغط Ctrl+H لإخفاء الشريط الجانبي والحصول على مساحة كاملة للخريطة
- **تبديل العرض**: اضغط F11 للتبديل بين وضع ملء الشاشة والوضع العادي
- **إعادة تعيين العرض**: زر "🎯 إعادة تعيين" أو Ctrl+0 لإعادة الخريطة للوضع الافتراضي

### 3. الفلترة والبحث
- **فلتر الحالة**: اختر من القائمة المنسدلة (جميع الشحنات، النشطة، في الطريق، إلخ)
- **فلتر شركة الشحن**: اختر شركة شحن محددة
- **تحديث البيانات**: زر "🔄 تحديث" لتحديث البيانات

### 4. عرض التفاصيل
- **تفاصيل سريعة**: تظهر في التبويب الجانبي عند اختيار شحنة
- **تفاصيل كاملة**: زر "📄 تفاصيل كاملة" لنافذة منفصلة
- **مسار الشحنة**: عرض الجدول الزمني للمسار

## المكونات التقنية

### RouteMapWidget
- **الوظيفة**: ويدجت مخصص لرسم الخريطة والمسارات
- **الميزات**: رسم الموانئ، المسارات، الشحنات، والتفاعل
- **التحكم**: تكبير، تصغير، تحريك، اختيار

### ShippingRoutesWindow
- **الوظيفة**: النافذة الرئيسية لإدارة المسارات
- **التبويبات**: قائمة الشحنات، التفاصيل، الإحصائيات، التنبيهات
- **الأدوات**: فلترة، تصدير، إعدادات، مساعدة

## قاعدة البيانات المستخدمة

### الحقول المهمة
- `port_of_loading` - ميناء التحميل
- `port_of_discharge` - ميناء التفريغ  
- `port_of_arrival` - ميناء الوصول
- `final_destination` - الوجهة النهائية
- `shipment_status` - حالة الشحنة
- `estimated_departure_date` - تاريخ المغادرة المتوقع
- `estimated_arrival_date` - تاريخ الوصول المتوقع
- `shipping_company` - شركة الشحن
- `vessel_name` - اسم السفينة
- `tracking_number` - رقم التتبع

## الموانئ المدعومة
النظام يدعم الموانئ الرئيسية التالية مع إحداثياتها:
- جدة (السعودية)
- الدمام (السعودية)
- دبي (الإمارات)
- شنغهاي (الصين)
- سنغافورة
- هونغ كونغ
- روتردام (هولندا)
- هامبورغ (ألمانيا)
- لوس أنجلوس (أمريكا)
- نيويورك (أمريكا)
- طوكيو (اليابان)
- بوسان (كوريا الجنوبية)

## التصدير والتقارير
- **تصدير CSV**: تصدير بيانات المسارات إلى ملف CSV
- **تقارير مفصلة**: تقارير شاملة للإحصائيات
- **طباعة**: إمكانية طباعة التقارير (قيد التطوير)

## الإعدادات القابلة للتخصيص
- **حد التأخير**: تخصيص عدد الأيام لاعتبار الشحنة متأخرة
- **التحديث التلقائي**: تفعيل/إلغاء التحديث التلقائي
- **ألوان الخريطة**: تخصيص ألوان المسارات والموانئ

## المتطلبات التقنية
- Python 3.8+
- PySide6
- SQLAlchemy
- قاعدة بيانات مع جدول الشحنات

## الاستكشاف والإصلاح
- **مشكلة عدم ظهور الخريطة**: تأكد من وجود بيانات الشحنات
- **مشكلة الألوان**: تحقق من حالات الشحنات في قاعدة البيانات
- **مشكلة التتبع**: تأكد من وجود أرقام التتبع والموانئ

## التحسينات الجديدة في وضع ملء الشاشة ✨

### 🚀 تحسينات الأداء
- **فتح سريع**: النافذة تفتح مباشرة في وضع ملء الشاشة دون تأخير
- **تكبير محسن**: تكبير/تصغير حول موقع الماوس لتجربة أكثر سلاسة
- **سحب متقدم**: سحب الخريطة مع تتبع الإحداثيات في الوقت الفعلي
- **استجابة سريعة**: تحديث فوري لشريط الحالة مع كل تفاعل

### 🎛️ تحسينات واجهة المستخدم
- **شريط حالة ذكي**: عرض معلومات شاملة (تكبير، إحداثيات، شحنة محددة، اختصارات)
- **أزرار محسنة**: أزرار مخصصة لإدارة وضع العرض والشريط الجانبي
- **مفاتيح اختصار شاملة**: دعم كامل لجميع العمليات عبر لوحة المفاتيح
- **تخطيط محسن**: توزيع مثالي للمساحة (70% خريطة، 30% شريط جانبي)

### 🔧 تحسينات تقنية
- **إدارة الذاكرة**: تحسين استخدام الذاكرة في وضع ملء الشاشة
- **معالجة الأحداث**: معالجة محسنة لأحداث الماوس ولوحة المفاتيح
- **تحديث الحالة**: تحديث تلقائي لجميع عناصر الواجهة مع التفاعل
- **مرونة العرض**: تكيف تلقائي مع أحجام الشاشات المختلفة

## التطوير المستقبلي
- دعم خرائط حقيقية (Google Maps, OpenStreetMap)
- تتبع GPS مباشر للشحنات
- تنبيهات فورية عبر البريد الإلكتروني/SMS
- تحليلات متقدمة بالذكاء الاصطناعي
- دعم المزيد من الموانئ العالمية
- **تحسينات إضافية لوضع ملء الشاشة**: دعم شاشات متعددة وحفظ تفضيلات العرض
