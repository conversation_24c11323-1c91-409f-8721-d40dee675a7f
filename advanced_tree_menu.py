#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شجرة القوائم المتقدمة لنظام SHIPMENT ERP
Advanced Tree Menu for SHIPMENT ERP System
"""

from PySide6.QtWidgets import (
    QFrame, QVBoxLayout, QLabel, QTreeWidget, QTreeWidgetItem,
    QHBoxLayout, QPushButton, QLineEdit, QMenu, QMessageBox
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QIcon, QAction

class AdvancedSystemTreeWidget(QFrame):
    """شجرة أنظمة التطبيق المتقدمة"""
    
    # إشارات مخصصة
    item_selected = Signal(str, str)  # النظام، الوظيفة
    system_expanded = Signal(str)     # النظام المفتوح
    
    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumWidth(280)
        self.setMinimumWidth(250)
        
        # متغيرات الحالة
        self.current_selection = None
        self.expanded_systems = set()
        
        self.setup_ui(title)
        self.setup_tree_items()
        self.setup_connections()
    
    def setup_ui(self, title):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        
        # عنوان القائمة
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A90E2, stop:1 #357ABD);
                color: white;
                padding: 10px;
                border-radius: 6px;
                margin-bottom: 5px;
            }
        """)
        layout.addWidget(title_label)
        
        # شريط البحث
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 البحث في الأنظمة...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 6px 10px;
                border: 2px solid #E0E0E0;
                border-radius: 4px;
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #4A90E2;
            }
        """)
        search_layout.addWidget(self.search_input)
        
        # زر مسح البحث
        clear_btn = QPushButton("✖")
        clear_btn.setMaximumWidth(30)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #F0F0F0;
                border: 1px solid #D0D0D0;
                border-radius: 4px;
                padding: 4px;
            }
            QPushButton:hover {
                background-color: #E0E0E0;
            }
        """)
        clear_btn.clicked.connect(self.clear_search)
        search_layout.addWidget(clear_btn)
        
        layout.addLayout(search_layout)
        
        # إنشاء شجرة الأنظمة
        self.tree = QTreeWidget()
        self.tree.setHeaderHidden(True)
        self.tree.setRootIsDecorated(True)
        self.tree.setIndentation(25)
        self.tree.setAlternatingRowColors(True)
        
        # تطبيق الأنماط على الشجرة
        self.tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 2px solid #E0E0E0;
                border-radius: 6px;
                font-size: 11px;
                selection-background-color: #E3F2FD;
            }
            QTreeWidget::item {
                padding: 8px 5px;
                border-bottom: 1px solid #F5F5F5;
                min-height: 25px;
            }
            QTreeWidget::item:hover {
                background-color: #F0F8FF;
                color: #2E86AB;
                border-left: 3px solid #4A90E2;
            }
            QTreeWidget::item:selected {
                background-color: #E3F2FD;
                color: #1976D2;
                font-weight: bold;
                border-left: 4px solid #2196F3;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: none;
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: none;
            }
            QTreeWidget::branch:has-children:closed {
                background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYgNEwxMCA4TDYgMTJWNFoiIGZpbGw9IiM2NjY2NjYiLz4KPC9zdmc+) center center no-repeat;
            }
            QTreeWidget::branch:has-children:open {
                background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDZINFoiIGZpbGw9IiM2NjY2NjYiLz4KPC9zdmc+) center center no-repeat;
            }
        """)
        
        layout.addWidget(self.tree)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        self.status_label = QLabel("جاهز")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 10px;
                padding: 4px;
            }
        """)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        # زر توسيع/طي الكل
        self.expand_btn = QPushButton("توسيع الكل")
        self.expand_btn.setMaximumWidth(80)
        self.expand_btn.setStyleSheet("""
            QPushButton {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 3px;
                padding: 3px 6px;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: #E9ECEF;
            }
        """)
        status_layout.addWidget(self.expand_btn)
        
        layout.addLayout(status_layout)
    
    def setup_tree_items(self):
        """إعداد عناصر الشجرة"""
        
        # 1. نظام إدارة الشحنات الرئيسي
        shipment_system = QTreeWidgetItem(self.tree, ["📦 نظام إدارة الشحنات"])
        shipment_system.setExpanded(True)
        shipment_system.setData(0, Qt.UserRole, "shipment_system")
        
        # فروع نظام الشحنات
        shipment_items = [
            ("📋 إنشاء شحنة جديدة", "create_shipment"),
            ("🔍 البحث عن الشحنات", "search_shipments"),
            ("📊 تتبع الشحنات", "track_shipments"),
            ("📝 تحديث حالة الشحنة", "update_status"),
            ("🏷️ طباعة ملصقات الشحن", "print_labels"),
            ("📈 تقارير الشحنات", "shipment_reports"),
            ("🚚 إدارة المركبات", "vehicle_management"),
            ("📍 تتبع المواقع", "location_tracking")
        ]
        
        for text, data in shipment_items:
            item = QTreeWidgetItem(shipment_system, [text])
            item.setData(0, Qt.UserRole, data)
        
        # 2. نظام إدارة العملاء
        customer_system = QTreeWidgetItem(self.tree, ["👥 نظام إدارة العملاء"])
        customer_system.setExpanded(False)
        customer_system.setData(0, Qt.UserRole, "customer_system")
        
        customer_items = [
            ("➕ إضافة عميل جديد", "add_customer"),
            ("✏️ تعديل بيانات العميل", "edit_customer"),
            ("🔍 البحث عن العملاء", "search_customers"),
            ("📋 قائمة العملاء", "customer_list"),
            ("💳 إدارة الحسابات", "account_management"),
            ("📊 تقارير العملاء", "customer_reports"),
            ("📞 سجل الاتصالات", "contact_history"),
            ("⭐ تقييم العملاء", "customer_rating")
        ]
        
        for text, data in customer_items:
            item = QTreeWidgetItem(customer_system, [text])
            item.setData(0, Qt.UserRole, data)
        
        # 3. نظام إدارة المخازن والمستودعات
        warehouse_system = QTreeWidgetItem(self.tree, ["🏪 نظام إدارة المخازن"])
        warehouse_system.setExpanded(False)
        warehouse_system.setData(0, Qt.UserRole, "warehouse_system")
        
        warehouse_items = [
            ("📦 إدارة المخزون", "inventory_management"),
            ("📍 مواقع التخزين", "storage_locations"),
            ("🔄 حركة البضائع", "goods_movement"),
            ("📊 تقارير المخزون", "inventory_reports"),
            ("⚠️ تنبيهات المخزون", "inventory_alerts"),
            ("📋 جرد المخزون", "stock_counting"),
            ("🚛 استلام البضائع", "goods_receiving"),
            ("📤 شحن البضائع", "goods_shipping")
        ]
        
        for text, data in warehouse_items:
            item = QTreeWidgetItem(warehouse_system, [text])
            item.setData(0, Qt.UserRole, data)
        
        # 4. النظام المالي والمحاسبي
        financial_system = QTreeWidgetItem(self.tree, ["💰 النظام المالي"])
        financial_system.setExpanded(False)
        financial_system.setData(0, Qt.UserRole, "financial_system")
        
        financial_items = [
            ("💵 إدارة الفواتير", "invoice_management"),
            ("💳 المدفوعات والتحصيل", "payments"),
            ("📊 التقارير المالية", "financial_reports"),
            ("💹 الأرباح والخسائر", "profit_loss"),
            ("🧾 إدارة الضرائب", "tax_management"),
            ("💰 التدفق النقدي", "cash_flow"),
            ("📈 الميزانية العمومية", "balance_sheet"),
            ("🔍 مراجعة الحسابات", "account_audit")
        ]
        
        for text, data in financial_items:
            item = QTreeWidgetItem(financial_system, [text])
            item.setData(0, Qt.UserRole, data)
        
        # 5. نظام التقارير والإحصائيات
        reports_system = QTreeWidgetItem(self.tree, ["📈 نظام التقارير"])
        reports_system.setExpanded(False)
        reports_system.setData(0, Qt.UserRole, "reports_system")
        
        reports_items = [
            ("📊 التقارير اليومية", "daily_reports"),
            ("📅 التقارير الشهرية", "monthly_reports"),
            ("📆 التقارير السنوية", "yearly_reports"),
            ("📋 تقارير مخصصة", "custom_reports"),
            ("📈 الرسوم البيانية", "charts"),
            ("📉 تحليل الأداء", "performance_analysis"),
            ("📊 إحصائيات متقدمة", "advanced_statistics"),
            ("📱 تقارير الهاتف المحمول", "mobile_reports")
        ]
        
        for text, data in reports_items:
            item = QTreeWidgetItem(reports_system, [text])
            item.setData(0, Qt.UserRole, data)
        
        # 6. نظام الإعدادات والإدارة
        admin_system = QTreeWidgetItem(self.tree, ["⚙️ نظام الإدارة"])
        admin_system.setExpanded(False)
        admin_system.setData(0, Qt.UserRole, "admin_system")
        
        admin_items = [
            ("👤 إدارة المستخدمين", "user_management"),
            ("🔐 الصلاحيات والأذونات", "permissions"),
            ("🗄️ النسخ الاحتياطية", "backup"),
            ("🔧 إعدادات النظام", "system_settings"),
            ("📝 سجل العمليات", "activity_log"),
            ("🔄 تحديثات النظام", "system_updates"),
            ("🛡️ الأمان والحماية", "security"),
            ("🌐 إعدادات الشبكة", "network_settings")
        ]
        
        for text, data in admin_items:
            item = QTreeWidgetItem(admin_system, [text])
            item.setData(0, Qt.UserRole, data)
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.tree.itemClicked.connect(self.on_item_clicked)
        self.tree.itemExpanded.connect(self.on_item_expanded)
        self.tree.itemCollapsed.connect(self.on_item_collapsed)
        self.search_input.textChanged.connect(self.on_search_changed)
        self.expand_btn.clicked.connect(self.toggle_expand_all)
        
        # إعداد القائمة المنبثقة
        self.tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree.customContextMenuRequested.connect(self.show_context_menu)
    
    def on_item_clicked(self, item, column):
        """معالج النقر على عنصر في الشجرة"""
        item_text = item.text(0)
        item_data = item.data(0, Qt.UserRole)
        
        self.current_selection = item_data
        
        # تحديث شريط الحالة
        if item.parent():
            parent_text = item.parent().text(0)
            self.status_label.setText(f"محدد: {item_text}")
            self.item_selected.emit(parent_text, item_text)
        else:
            self.status_label.setText(f"نظام: {item_text}")
            self.system_expanded.emit(item_text)
        
        print(f"تم النقر على: {item_text} (البيانات: {item_data})")
    
    def on_item_expanded(self, item):
        """معالج توسيع عنصر"""
        item_text = item.text(0)
        self.expanded_systems.add(item_text)
        print(f"تم توسيع: {item_text}")
    
    def on_item_collapsed(self, item):
        """معالج طي عنصر"""
        item_text = item.text(0)
        self.expanded_systems.discard(item_text)
        print(f"تم طي: {item_text}")
    
    def on_search_changed(self, text):
        """معالج تغيير نص البحث"""
        if text:
            self.filter_tree_items(text)
            self.status_label.setText(f"البحث: {text}")
        else:
            self.show_all_items()
            self.status_label.setText("جاهز")
    
    def filter_tree_items(self, search_text):
        """تصفية عناصر الشجرة حسب النص"""
        search_text = search_text.lower()
        
        for i in range(self.tree.topLevelItemCount()):
            parent_item = self.tree.topLevelItem(i)
            parent_visible = False
            
            # فحص العنصر الرئيسي
            if search_text in parent_item.text(0).lower():
                parent_visible = True
            
            # فحص العناصر الفرعية
            for j in range(parent_item.childCount()):
                child_item = parent_item.child(j)
                if search_text in child_item.text(0).lower():
                    child_item.setHidden(False)
                    parent_visible = True
                else:
                    child_item.setHidden(True)
            
            parent_item.setHidden(not parent_visible)
            if parent_visible:
                parent_item.setExpanded(True)
    
    def show_all_items(self):
        """إظهار جميع العناصر"""
        for i in range(self.tree.topLevelItemCount()):
            parent_item = self.tree.topLevelItem(i)
            parent_item.setHidden(False)
            
            for j in range(parent_item.childCount()):
                child_item = parent_item.child(j)
                child_item.setHidden(False)
    
    def clear_search(self):
        """مسح نص البحث"""
        self.search_input.clear()
    
    def toggle_expand_all(self):
        """توسيع/طي جميع العناصر"""
        if self.expand_btn.text() == "توسيع الكل":
            self.tree.expandAll()
            self.expand_btn.setText("طي الكل")
        else:
            self.tree.collapseAll()
            self.expand_btn.setText("توسيع الكل")
    
    def show_context_menu(self, position):
        """إظهار القائمة المنبثقة"""
        item = self.tree.itemAt(position)
        if item:
            menu = QMenu(self)
            
            if item.parent():  # عنصر فرعي
                open_action = QAction("🚀 فتح الوظيفة", self)
                open_action.triggered.connect(lambda: self.open_function(item))
                menu.addAction(open_action)
                
                info_action = QAction("ℹ️ معلومات", self)
                info_action.triggered.connect(lambda: self.show_item_info(item))
                menu.addAction(info_action)
            else:  # عنصر رئيسي
                expand_action = QAction("📂 توسيع النظام", self)
                expand_action.triggered.connect(lambda: item.setExpanded(True))
                menu.addAction(expand_action)
                
                collapse_action = QAction("📁 طي النظام", self)
                collapse_action.triggered.connect(lambda: item.setExpanded(False))
                menu.addAction(collapse_action)
            
            menu.exec(self.tree.mapToGlobal(position))
    
    def open_function(self, item):
        """فتح الوظيفة المحددة"""
        function_name = item.text(0)
        QMessageBox.information(
            self, "فتح الوظيفة", 
            f"سيتم فتح: {function_name}\n\n(قيد التطوير)"
        )
    
    def show_item_info(self, item):
        """إظهار معلومات العنصر"""
        item_text = item.text(0)
        parent_text = item.parent().text(0) if item.parent() else "لا يوجد"
        
        QMessageBox.information(
            self, "معلومات العنصر",
            f"الوظيفة: {item_text}\n"
            f"النظام: {parent_text}\n"
            f"الحالة: متاح\n"
            f"الإصدار: 1.0.0"
        )
