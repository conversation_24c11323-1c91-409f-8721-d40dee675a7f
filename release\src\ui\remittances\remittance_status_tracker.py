# -*- coding: utf-8 -*-
"""
نافذة تتبع حالة الحوالات
Remittance Status Tracker Window
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QGroupBox, QTextEdit,
                               QTableWidget, QTableWidgetItem, QHeaderView,
                               QFrame, QScrollArea, QWidget, QComboBox,
                               QDateTimeEdit, QMessageBox, QProgressBar,
                               QAbstractItemView, QSplitter)
from PySide6.QtCore import Qt, QDateTime, Signal, QTimer
from PySide6.QtGui import QFont, QPixmap, QIcon, QColor, QPalette

import sqlite3
from pathlib import Path
from datetime import datetime
import json

from ...utils.arabic_support import reshape_arabic_text

class RemittanceStatusTracker(QDialog):
    """نافذة تتبع حالة الحوالات"""
    
    status_updated = Signal(int, str)  # إشارة تحديث الحالة
    
    def __init__(self, remittance_id, parent=None):
        super().__init__(parent)
        self.remittance_id = remittance_id
        self.remittance_data = None
        self.status_history = []
        
        self.setWindowTitle("تتبع حالة الحوالة - ProShipment")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        self.setModal(True)
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        
        # تحميل البيانات
        self.load_remittance_data()
        self.load_status_history()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        self.create_header_section(layout)
        
        # المحتوى الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - معلومات الحوالة
        left_panel = self.create_remittance_info_panel()
        splitter.addWidget(left_panel)
        
        # الجانب الأيمن - تتبع الحالة
        right_panel = self.create_status_tracking_panel()
        splitter.addWidget(right_panel)
        
        # تعيين النسب
        splitter.setSizes([400, 600])
        layout.addWidget(splitter)
        
        # أزرار الإجراءات
        self.create_action_buttons(layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3b82f6, stop:1 #1e40af);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة التتبع
        icon_label = QLabel("📍")
        icon_label.setStyleSheet("font-size: 32px; color: white;")
        header_layout.addWidget(icon_label)
        
        # معلومات الحوالة
        info_layout = QVBoxLayout()
        
        self.title_label = QLabel("تتبع حالة الحوالة")
        self.title_label.setFont(QFont("Arial", 18, QFont.Bold))
        self.title_label.setStyleSheet("color: white;")
        
        self.remittance_number_label = QLabel("رقم الحوالة: جاري التحميل...")
        self.remittance_number_label.setFont(QFont("Arial", 12))
        self.remittance_number_label.setStyleSheet("color: #e0e7ff;")
        
        info_layout.addWidget(self.title_label)
        info_layout.addWidget(self.remittance_number_label)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        # الحالة الحالية
        self.current_status_frame = QFrame()
        self.current_status_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        status_layout = QVBoxLayout(self.current_status_frame)
        
        current_label = QLabel("الحالة الحالية:")
        current_label.setStyleSheet("color: white; font-size: 12px;")
        
        self.current_status_label = QLabel("جاري التحميل...")
        self.current_status_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.current_status_label.setStyleSheet("color: #fbbf24;")
        
        status_layout.addWidget(current_label)
        status_layout.addWidget(self.current_status_label)
        
        header_layout.addWidget(self.current_status_frame)
        
        layout.addWidget(header_frame)
        
    def create_remittance_info_panel(self):
        """إنشاء لوحة معلومات الحوالة"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # معلومات أساسية
        basic_group = QGroupBox("معلومات الحوالة")
        basic_layout = QGridLayout(basic_group)
        
        self.amount_label = QLabel("-")
        self.currency_label = QLabel("-")
        self.sender_label = QLabel("-")
        self.receiver_label = QLabel("-")
        self.supplier_label = QLabel("-")
        self.date_label = QLabel("-")
        
        basic_layout.addWidget(QLabel("المبلغ:"), 0, 0)
        basic_layout.addWidget(self.amount_label, 0, 1)
        basic_layout.addWidget(QLabel("العملة:"), 1, 0)
        basic_layout.addWidget(self.currency_label, 1, 1)
        basic_layout.addWidget(QLabel("المرسل:"), 2, 0)
        basic_layout.addWidget(self.sender_label, 2, 1)
        basic_layout.addWidget(QLabel("المستقبل:"), 3, 0)
        basic_layout.addWidget(self.receiver_label, 3, 1)
        basic_layout.addWidget(QLabel("المورد:"), 4, 0)
        basic_layout.addWidget(self.supplier_label, 4, 1)
        basic_layout.addWidget(QLabel("التاريخ:"), 5, 0)
        basic_layout.addWidget(self.date_label, 5, 1)
        
        layout.addWidget(basic_group)
        
        # تحديث الحالة
        update_group = QGroupBox("تحديث الحالة")
        update_layout = QVBoxLayout(update_group)
        
        # اختيار الحالة الجديدة
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("الحالة الجديدة:"))
        
        self.new_status_combo = QComboBox()
        self.new_status_combo.setMinimumWidth(200)
        status_layout.addWidget(self.new_status_combo)
        
        update_layout.addLayout(status_layout)
        
        # ملاحظات التحديث
        update_layout.addWidget(QLabel("ملاحظات التحديث:"))
        self.update_notes_input = QTextEdit()
        self.update_notes_input.setMaximumHeight(100)
        self.update_notes_input.setPlaceholderText("أدخل ملاحظات حول تحديث الحالة...")
        update_layout.addWidget(self.update_notes_input)
        
        # زر التحديث
        self.update_status_btn = QPushButton("🔄 تحديث الحالة")
        self.update_status_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        update_layout.addWidget(self.update_status_btn)
        
        layout.addWidget(update_group)
        layout.addStretch()
        
        return panel
        
    def create_status_tracking_panel(self):
        """إنشاء لوحة تتبع الحالة"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # عنوان القسم
        title_label = QLabel("تاريخ تتبع الحالة")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #1e293b; padding: 10px;")
        layout.addWidget(title_label)
        
        # جدول تاريخ الحالات
        self.status_table = QTableWidget()
        self.setup_status_table()
        layout.addWidget(self.status_table)
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.StyledPanel)
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        
        stats_layout = QHBoxLayout(stats_frame)
        
        self.total_updates_label = QLabel("إجمالي التحديثات: 0")
        self.duration_label = QLabel("المدة الإجمالية: -")
        self.last_update_label = QLabel("آخر تحديث: -")
        
        for label in [self.total_updates_label, self.duration_label, self.last_update_label]:
            label.setStyleSheet("font-weight: bold; color: #374151;")
            stats_layout.addWidget(label)
        
        stats_layout.addStretch()
        layout.addWidget(stats_frame)
        
        return panel
        
    def setup_status_table(self):
        """إعداد جدول تاريخ الحالات"""
        headers = ["التاريخ والوقت", "الحالة السابقة", "الحالة الجديدة", "المستخدم", "الملاحظات"]
        
        self.status_table.setColumnCount(len(headers))
        self.status_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.status_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.status_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.status_table.setAlternatingRowColors(True)
        self.status_table.setSortingEnabled(True)
        
        # تعديل عرض الأعمدة
        header = self.status_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الحالة السابقة
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الحالة الجديدة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # المستخدم
        
        # تنسيق الجدول
        self.status_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e2e8f0;
                background-color: white;
                alternate-background-color: #f8fafc;
                selection-background-color: #3b82f6;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e2e8f0;
            }
            QHeaderView::section {
                background-color: #f1f5f9;
                color: #374151;
                padding: 10px;
                border: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)
        
    def create_action_buttons(self, layout):
        """إنشاء أزرار الإجراءات"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        buttons_layout.addWidget(self.progress_bar)
        
        buttons_layout.addStretch()
        
        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث البيانات")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_data)
        buttons_layout.addWidget(refresh_btn)
        
        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة التقرير")
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        print_btn.clicked.connect(self.print_report)
        buttons_layout.addWidget(print_btn)
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #dc2626;
            }
        """)
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.update_status_btn.clicked.connect(self.update_remittance_status)
        self.new_status_combo.currentTextChanged.connect(self.validate_status_update)

    def load_remittance_data(self):
        """تحميل بيانات الحوالة"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # استعلام بيانات الحوالة
            query = """
                SELECT
                    r.remittance_number, r.amount, r.current_status, r.remittance_date,
                    r.sender_name, r.receiver_name, s.name as supplier_name,
                    c.code as currency_code, c.name as currency_name
                FROM remittances r
                LEFT JOIN suppliers s ON r.supplier_id = s.id
                LEFT JOIN currencies c ON r.currency_id = c.id
                WHERE r.id = ?
            """

            cursor.execute(query, (self.remittance_id,))
            self.remittance_data = cursor.fetchone()

            if self.remittance_data:
                self.update_remittance_info_display()

            # تحميل حالات الحوالات المتاحة
            cursor.execute("""
                SELECT name FROM remittance_statuses
                WHERE is_active = 1
                ORDER BY order_index
            """)
            statuses = cursor.fetchall()

            self.new_status_combo.clear()
            self.new_status_combo.addItem("اختر الحالة الجديدة...", None)
            for status in statuses:
                self.new_status_combo.addItem(status[0], status[0])

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الحوالة:\n{str(e)}")

    def update_remittance_info_display(self):
        """تحديث عرض معلومات الحوالة"""
        if not self.remittance_data:
            return

        # تحديث العنوان
        self.remittance_number_label.setText(f"رقم الحوالة: {self.remittance_data[0]}")
        self.current_status_label.setText(self.remittance_data[2] or "غير محدد")

        # تحديث المعلومات الأساسية
        self.amount_label.setText(f"{self.remittance_data[1]:,.2f}" if self.remittance_data[1] else "0.00")
        self.currency_label.setText(f"{self.remittance_data[7]} - {self.remittance_data[8]}" if self.remittance_data[7] else "-")
        self.sender_label.setText(self.remittance_data[4] or "-")
        self.receiver_label.setText(self.remittance_data[5] or "-")
        self.supplier_label.setText(self.remittance_data[6] or "-")

        # تحديث التاريخ
        if self.remittance_data[3]:
            try:
                date_obj = datetime.fromisoformat(self.remittance_data[3].replace('Z', '+00:00'))
                self.date_label.setText(date_obj.strftime('%Y-%m-%d %H:%M'))
            except:
                self.date_label.setText(str(self.remittance_data[3]))
        else:
            self.date_label.setText("-")

        # تلوين الحالة الحالية
        status = self.remittance_data[2]
        if status == "مكتملة":
            self.current_status_label.setStyleSheet("color: #10b981; font-weight: bold;")
        elif status == "معلقة":
            self.current_status_label.setStyleSheet("color: #f59e0b; font-weight: bold;")
        elif status == "ملغية":
            self.current_status_label.setStyleSheet("color: #ef4444; font-weight: bold;")
        else:
            self.current_status_label.setStyleSheet("color: #6b7280; font-weight: bold;")

    def load_status_history(self):
        """تحميل تاريخ حالات الحوالة"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # استعلام تاريخ الحالات
            query = """
                SELECT
                    rsh.changed_at, rsh.old_status, rsh.new_status,
                    rsh.notes, u.full_name as user_name
                FROM remittance_status_history rsh
                LEFT JOIN users u ON rsh.changed_by = u.id
                WHERE rsh.remittance_id = ?
                ORDER BY rsh.changed_at DESC
            """

            cursor.execute(query, (self.remittance_id,))
            self.status_history = cursor.fetchall()

            self.update_status_table()
            self.update_statistics()

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل تاريخ الحالات:\n{str(e)}")

    def update_status_table(self):
        """تحديث جدول تاريخ الحالات"""
        self.status_table.setRowCount(len(self.status_history))

        for row, history in enumerate(self.status_history):
            # التاريخ والوقت
            date_str = ""
            if history[0]:
                try:
                    date_obj = datetime.fromisoformat(history[0].replace('Z', '+00:00'))
                    date_str = date_obj.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    date_str = str(history[0])
            self.status_table.setItem(row, 0, QTableWidgetItem(date_str))

            # الحالة السابقة
            old_status_item = QTableWidgetItem(history[1] or "جديد")
            if history[1]:
                old_status_item.setBackground(QColor("#fee2e2"))
            self.status_table.setItem(row, 1, old_status_item)

            # الحالة الجديدة
            new_status_item = QTableWidgetItem(history[2] or "")
            if history[2] == "مكتملة":
                new_status_item.setBackground(QColor("#dcfce7"))
            elif history[2] == "معلقة":
                new_status_item.setBackground(QColor("#fef3c7"))
            elif history[2] == "ملغية":
                new_status_item.setBackground(QColor("#fee2e2"))
            else:
                new_status_item.setBackground(QColor("#f3f4f6"))
            self.status_table.setItem(row, 2, new_status_item)

            # المستخدم
            self.status_table.setItem(row, 3, QTableWidgetItem(history[4] or "غير محدد"))

            # الملاحظات
            self.status_table.setItem(row, 4, QTableWidgetItem(history[3] or ""))

    def update_statistics(self):
        """تحديث الإحصائيات"""
        total_updates = len(self.status_history)
        self.total_updates_label.setText(f"إجمالي التحديثات: {total_updates}")

        if self.status_history:
            # حساب المدة الإجمالية
            try:
                first_date = datetime.fromisoformat(self.status_history[-1][0].replace('Z', '+00:00'))
                last_date = datetime.fromisoformat(self.status_history[0][0].replace('Z', '+00:00'))
                duration = last_date - first_date

                days = duration.days
                hours = duration.seconds // 3600

                if days > 0:
                    duration_str = f"{days} يوم، {hours} ساعة"
                else:
                    duration_str = f"{hours} ساعة"

                self.duration_label.setText(f"المدة الإجمالية: {duration_str}")

                # آخر تحديث
                last_update_str = last_date.strftime('%Y-%m-%d %H:%M')
                self.last_update_label.setText(f"آخر تحديث: {last_update_str}")

            except:
                self.duration_label.setText("المدة الإجمالية: غير محدد")
                self.last_update_label.setText("آخر تحديث: غير محدد")
        else:
            self.duration_label.setText("المدة الإجمالية: -")
            self.last_update_label.setText("آخر تحديث: -")

    def validate_status_update(self):
        """التحقق من صحة تحديث الحالة"""
        new_status = self.new_status_combo.currentData()
        current_status = self.remittance_data[2] if self.remittance_data else None

        # تفعيل/تعطيل زر التحديث
        is_valid = (new_status is not None and
                   new_status != current_status and
                   new_status != "اختر الحالة الجديدة...")

        self.update_status_btn.setEnabled(is_valid)

    def update_remittance_status(self):
        """تحديث حالة الحوالة"""
        new_status = self.new_status_combo.currentData()
        notes = self.update_notes_input.toPlainText().strip()

        if not new_status:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار الحالة الجديدة")
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.update_status_btn.setEnabled(False)

            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحديث حالة الحوالة
            cursor.execute("""
                UPDATE remittances
                SET current_status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (new_status, self.remittance_id))

            # إضافة سجل في تاريخ الحالات
            old_status = self.remittance_data[2] if self.remittance_data else None
            cursor.execute("""
                INSERT INTO remittance_status_history (
                    remittance_id, old_status, new_status, notes,
                    changed_by, changed_at
                ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (self.remittance_id, old_status, new_status, notes, 1))

            conn.commit()
            conn.close()

            # تحديث البيانات المعروضة
            if self.remittance_data:
                self.remittance_data = list(self.remittance_data)
                self.remittance_data[2] = new_status
                self.remittance_data = tuple(self.remittance_data)

            self.update_remittance_info_display()
            self.load_status_history()

            # مسح النموذج
            self.new_status_combo.setCurrentIndex(0)
            self.update_notes_input.clear()

            # إرسال إشارة التحديث
            self.status_updated.emit(self.remittance_id, new_status)

            QMessageBox.information(self, "نجح التحديث", "تم تحديث حالة الحوالة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث الحالة:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.update_status_btn.setEnabled(True)

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_remittance_data()
        self.load_status_history()

    def print_report(self):
        """طباعة تقرير التتبع"""
        QMessageBox.information(self, "طباعة", "تم إرسال تقرير التتبع للطباعة")
