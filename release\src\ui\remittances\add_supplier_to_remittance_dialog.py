# -*- coding: utf-8 -*-
"""
نافذة إضافة مورد للحوالة
Add Supplier to Remittance Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QTextEdit,
                               QPushButton, QGroupBox, QDoubleSpinBox,
                               QMessageBox)
from PySide6.QtCore import Qt
from PySide6.QtGui import QDoubleValidator

import sqlite3
from pathlib import Path

class AddSupplierToRemittanceDialog(QDialog):
    """نافذة إضافة مورد للحوالة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إضافة مورد للحوالة")
        self.setMinimumSize(500, 400)
        self.setModal(True)
        
        # متغيرات النافذة
        self.suppliers_data = []
        self.currencies_data = []
        
        # إعداد الواجهة
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # معلومات المورد
        supplier_group = QGroupBox("معلومات المورد")
        supplier_layout = QGridLayout(supplier_group)
        
        # اختيار المورد
        supplier_layout.addWidget(QLabel("المورد:"), 0, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.setEditable(True)
        supplier_layout.addWidget(self.supplier_combo, 0, 1)
        
        # المبلغ
        supplier_layout.addWidget(QLabel("المبلغ:"), 1, 0)
        self.amount_input = QLineEdit()
        self.amount_input.setPlaceholderText("0.00")
        validator = QDoubleValidator(0.0, 999999999.99, 2)
        self.amount_input.setValidator(validator)
        supplier_layout.addWidget(self.amount_input, 1, 1)
        
        # العملة
        supplier_layout.addWidget(QLabel("العملة:"), 2, 0)
        self.currency_combo = QComboBox()
        supplier_layout.addWidget(self.currency_combo, 2, 1)
        
        # الوصف
        supplier_layout.addWidget(QLabel("الوصف:"), 3, 0)
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("وصف المعاملة...")
        supplier_layout.addWidget(self.description_input, 3, 1)
        
        layout.addWidget(supplier_group)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        self.add_btn = QPushButton("إضافة")
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #059669; }
        """)
        self.add_btn.clicked.connect(self.accept_supplier)
        buttons_layout.addWidget(self.add_btn)
        
        layout.addLayout(buttons_layout)
        
    def load_data(self):
        """تحميل البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # تحميل الموردين
            cursor.execute("SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name")
            self.suppliers_data = cursor.fetchall()
            
            self.supplier_combo.clear()
            for supplier in self.suppliers_data:
                self.supplier_combo.addItem(supplier[1], supplier[0])
                
            # تحميل العملات
            cursor.execute("SELECT id, code, name FROM currencies WHERE is_active = 1 ORDER BY code")
            self.currencies_data = cursor.fetchall()
            
            self.currency_combo.clear()
            for currency in self.currencies_data:
                self.currency_combo.addItem(f"{currency[1]} - {currency[2]}", currency[0])
                
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات:\n{str(e)}")
            
    def accept_supplier(self):
        """قبول إضافة المورد"""
        if not self.validate_data():
            return
            
        self.accept()
        
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.supplier_combo.currentText().strip():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المورد")
            return False
            
        if not self.amount_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال المبلغ")
            return False
            
        try:
            amount = float(self.amount_input.text())
            if amount <= 0:
                QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ أكبر من صفر")
                return False
        except ValueError:
            QMessageBox.warning(self, "تحذير", "المبلغ غير صحيح")
            return False
            
        if not self.currency_combo.currentText():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار العملة")
            return False
            
        return True
        
    def get_supplier_data(self):
        """الحصول على بيانات المورد"""
        return {
            'name': self.supplier_combo.currentText(),
            'supplier_id': self.supplier_combo.currentData(),
            'amount': float(self.amount_input.text()),
            'currency': self.currency_combo.currentText().split(' - ')[0],
            'currency_id': self.currency_combo.currentData(),
            'description': self.description_input.toPlainText().strip()
        }
