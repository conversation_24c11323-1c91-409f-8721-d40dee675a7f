# دليل الاستخدام - نظام CnX ERP

## 🎯 مقدمة

مرحباً بك في نظام CnX ERP - النموذج التجريبي للواجهة الرئيسية. هذا الدليل سيساعدك على فهم واستخدام جميع ميزات النظام.

## 🚀 البدء السريع

### 1. التثبيت والإعداد

```bash
# تثبيت المتطلبات
pip install PySide6

# تشغيل النموذج التجريبي
python run_prototype.py

# أو استخدام الملف المبسط
python start_cnx_erp.py
```

### 2. أول تشغيل

عند تشغيل النظام لأول مرة:
1. ستظهر رسالة ترحيب
2. ستفتح النافذة الرئيسية مع لوحة المعلومات
3. يمكنك التنقل بين التبويبات المختلفة

## 🖥️ واجهة المستخدم

### شريط القوائم العلوي

#### قائمة "ملف"
- **جديد**: إنشاء مستند جديد
- **فتح**: فتح مستند موجود
- **حفظ**: حفظ المستند الحالي
- **خروج**: إغلاق النظام

#### قائمة "عرض"
- **لوحة المعلومات**: العرض الرئيسي
- **الشحنات**: إدارة الشحنات (قيد التطوير)
- **العملاء**: إدارة العملاء (قيد التطوير)
- **التقارير**: عرض التقارير (قيد التطوير)

#### قائمة "أدوات"
- **إعدادات**: تخصيص إعدادات النظام
- **نسخ احتياطي**: إنشاء نسخة احتياطية
- **استعادة البيانات**: استعادة البيانات

#### قائمة "مساعدة"
- **دليل المستخدم**: هذا الدليل
- **حول البرنامج**: معلومات النظام

### شريط الأدوات

يحتوي على أزرار سريعة للوصول للوظائف الأساسية:
- 🏠 **الرئيسية**: العودة للوحة المعلومات
- 📦 **الشحنات**: إدارة الشحنات
- 👥 **العملاء**: إدارة العملاء
- 📊 **التقارير**: عرض التقارير
- ⚙️ **الإعدادات**: إعدادات النظام
- ❓ **المساعدة**: الحصول على المساعدة

## 📊 لوحة المعلومات

### بطاقات المعلومات
تعرض إحصائيات مهمة:
- **إجمالي الشحنات**: العدد الكلي للشحنات
- **الشحنات المعلقة**: الشحنات قيد المعالجة
- **الشحنات المكتملة**: الشحنات المنجزة
- **العملاء النشطين**: عدد العملاء الفعالين
- **إجمالي الإيرادات**: المبلغ الإجمالي
- **متوسط وقت التسليم**: الوقت المتوسط للتسليم

### الإجراءات السريعة
أزرار للوصول السريع:
- **إنشاء شحنة جديدة**
- **البحث عن شحنة**
- **تقرير يومي**
- **إدارة العملاء**
- **إعدادات النظام**
- **النسخ الاحتياطي**

### الأنشطة الحديثة
جدول يعرض:
- **الوقت**: وقت النشاط
- **النشاط**: وصف العملية
- **المستخدم**: من قام بالعملية

### حالة النظام
معلومات حالة النظام:
- **حالة قاعدة البيانات**: متصل/غير متصل
- **حالة الخادم**: يعمل/متوقف
- **استخدام الذاكرة**: نسبة الاستخدام
- **مساحة القرص**: المساحة المستخدمة
- **آخر نسخة احتياطية**: تاريخ آخر نسخة

## 🎨 تبويب الشعار

يعرض النموذج الأصلي المطابق للصورة:
- **القائمة اليسرى**: خيارات النظام الأساسية
- **المنطقة المركزية**: شعار CnX ERP مع خلفية فنية
- **القائمة اليمنى**: إعدادات النظام المتقدمة

## ⚙️ الإعدادات

### نافذة الإعدادات
تحتوي على تبويبات:

#### تبويب "عام"
- **اللغة**: العربية (افتراضي)
- **المنطقة الزمنية**: GMT+3

#### تبويب "قاعدة البيانات"
- **نوع قاعدة البيانات**: Oracle
- **حالة الاتصال**: متصل/غير متصل

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### النظام لا يبدأ
1. تأكد من تثبيت Python 3.8+
2. تأكد من تثبيت PySide6
3. تحقق من وجود جميع الملفات المطلوبة

#### واجهة المستخدم لا تظهر بشكل صحيح
1. تأكد من دعم النظام للخطوط العربية
2. تحقق من إعدادات العرض
3. أعد تشغيل النظام

#### رسائل خطأ في وحدة التحكم
1. اقرأ رسالة الخطأ بعناية
2. تأكد من صحة مسارات الملفات
3. تحقق من الأذونات

### الحصول على المساعدة
- استخدم قائمة "مساعدة" في النظام
- راجع ملف README للمعلومات التقنية
- تحقق من رسائل الخطأ في وحدة التحكم

## 📝 ملاحظات مهمة

### الميزات المتاحة حالياً
- ✅ واجهة المستخدم الأساسية
- ✅ لوحة المعلومات التفاعلية
- ✅ نوافذ الإعدادات والحوار
- ✅ التنقل بين التبويبات

### الميزات قيد التطوير
- 🔄 ربط قاعدة البيانات Oracle
- 🔄 إدارة الشحنات الكاملة
- 🔄 إدارة العملاء
- 🔄 نظام التقارير المتقدم
- 🔄 نظام الأمان والصلاحيات

## 🎯 نصائح للاستخدام الأمثل

1. **استخدم الاختصارات**: تعلم اختصارات لوحة المفاتيح
2. **راقب شريط الحالة**: يعرض معلومات مهمة
3. **احفظ عملك بانتظام**: استخدم خيار الحفظ
4. **راجع الأنشطة الحديثة**: لمتابعة العمليات
5. **تحقق من حالة النظام**: للتأكد من سلامة العمل

## 📞 الدعم الفني

في حالة مواجهة أي مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من رسائل الخطأ
3. أعد تشغيل النظام
4. تواصل مع فريق الدعم الفني

---

**تم إعداد هذا الدليل بواسطة فريق CnX Solutions**
**الإصدار 1.0.0 - 2024**
