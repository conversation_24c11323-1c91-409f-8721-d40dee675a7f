# -*- coding: utf-8 -*-
"""
تحسينات الوضوح والجودة البصرية
Clarity and Visual Quality Improvements
"""

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QFontDatabase

class ClarityManager:
    """مدير تحسينات الوضوح"""
    
    def __init__(self):
        self.app = QApplication.instance()
        
    def apply_clarity_improvements(self):
        """تطبيق تحسينات الوضوح"""
        if not self.app:
            return
            
        # تحسين جودة الرسم
        self.setup_high_quality_rendering()
        
        # تحسين الخطوط
        self.setup_optimized_fonts()
        
        # تحسين الألوان والتباين
        self.setup_high_contrast_colors()
        
        print("✅ تم تطبيق تحسينات الوضوح")
        
    def setup_high_quality_rendering(self):
        """إعداد الرسم عالي الجودة"""
        # تفعيل تحسينات DPI
        self.app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        self.app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # تحسين جودة النصوص
        self.app.setAttribute(Qt.AA_SynthesizeMouseForUnhandledTouchEvents, False)
        
        # تحسين الرسم
        self.app.setAttribute(Qt.AA_CompressHighFrequencyEvents, True)
        
    def setup_optimized_fonts(self):
        """إعداد الخطوط المحسنة"""
        # البحث عن أفضل خط متاح
        font_db = QFontDatabase()
        available_fonts = font_db.families()
        
        # قائمة الخطوط المفضلة للوضوح
        preferred_fonts = [
            "Segoe UI",
            "Calibri", 
            "Tahoma",
            "Arial",
            "Microsoft Sans Serif"
        ]
        
        selected_font = "Segoe UI"  # الافتراضي
        
        for font_name in preferred_fonts:
            if font_name in available_fonts:
                selected_font = font_name
                break
        
        # إنشاء خط محسن
        font = QFont(selected_font)
        font.setPointSize(11)
        font.setWeight(QFont.Normal)
        font.setHintingPreference(QFont.PreferFullHinting)
        font.setStyleStrategy(QFont.PreferAntialias | QFont.PreferQuality)
        
        # تطبيق الخط على التطبيق
        self.app.setFont(font)
        
        print(f"تم تعيين الخط المحسن: {selected_font}")
        
    def setup_high_contrast_colors(self):
        """إعداد ألوان عالية التباين"""
        high_contrast_style = """
        /* تحسينات الوضوح والتباين */
        QWidget {
            font-family: "Segoe UI", "Calibri", "Tahoma", sans-serif;
            font-size: 11px;
            color: #1a1a1a;
        }
        
        QLabel {
            color: #1a1a1a;
            font-weight: 500;
        }
        
        QPushButton {
            color: white;
            font-weight: 600;
            border: 1px solid rgba(0,0,0,0.1);
        }
        
        QLineEdit, QTextEdit, QPlainTextEdit {
            color: #1a1a1a;
            background: white;
            border: 1px solid #999999;
            font-weight: 400;
        }
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border: 2px solid #0078d4;
            background: #ffffff;
        }
        
        QComboBox {
            color: #1a1a1a;
            background: white;
            border: 1px solid #999999;
        }
        
        QTableWidget {
            color: #1a1a1a;
            background: white;
            gridline-color: #d0d0d0;
        }
        
        QTableWidget::item {
            color: #1a1a1a;
            padding: 6px;
        }
        
        QTableWidget::item:selected {
            background: #0078d4;
            color: white;
        }
        
        QHeaderView::section {
            color: #1a1a1a;
            background: #f0f0f0;
            border: 1px solid #d0d0d0;
            font-weight: 600;
        }
        
        QGroupBox {
            color: #1a1a1a;
            font-weight: 600;
            border: 1px solid #999999;
        }
        
        QGroupBox::title {
            color: #1a1a1a;
            font-weight: 600;
        }
        
        QTabWidget::pane {
            border: 1px solid #999999;
            background: white;
        }
        
        QTabBar::tab {
            color: #1a1a1a;
            background: #f0f0f0;
            border: 1px solid #999999;
            padding: 8px 16px;
            font-weight: 500;
        }
        
        QTabBar::tab:selected {
            background: white;
            border-bottom: 2px solid #0078d4;
            color: #0078d4;
            font-weight: 600;
        }
        
        QMenuBar {
            color: white;
            font-weight: 600;
        }
        
        QMenu {
            color: #1a1a1a;
            background: white;
            border: 1px solid #999999;
        }
        
        QMenu::item:selected {
            background: #0078d4;
            color: white;
        }
        
        QStatusBar {
            color: #1a1a1a;
            font-weight: 500;
        }
        """
        
        # تطبيق التحسينات
        current_style = self.app.styleSheet()
        enhanced_style = current_style + high_contrast_style
        self.app.setStyleSheet(enhanced_style)
        
    def get_optimized_font(self, size=11, weight=QFont.Normal):
        """الحصول على خط محسن"""
        font = QFont("Segoe UI", size)
        font.setWeight(weight)
        font.setHintingPreference(QFont.PreferFullHinting)
        font.setStyleStrategy(QFont.PreferAntialias | QFont.PreferQuality)
        return font
        
    def apply_widget_clarity(self, widget):
        """تطبيق تحسينات الوضوح على ويدجت محدد"""
        if not widget:
            return
            
        # تطبيق خط محسن
        font = self.get_optimized_font()
        widget.setFont(font)
        
        # تحسين الرسم
        widget.setAttribute(Qt.WA_OpaquePaintEvent, False)
        widget.setAttribute(Qt.WA_NoSystemBackground, False)

# إنشاء مثيل عام
clarity_manager = ClarityManager()

def apply_clarity_improvements():
    """دالة سريعة لتطبيق تحسينات الوضوح"""
    clarity_manager.apply_clarity_improvements()

def get_clear_font(size=11, weight=QFont.Normal):
    """الحصول على خط واضح"""
    return clarity_manager.get_optimized_font(size, weight)
