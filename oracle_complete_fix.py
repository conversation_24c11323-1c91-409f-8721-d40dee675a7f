#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لقاعدة بيانات Oracle
Complete Oracle Database Fix
"""

import sys
import os
from pathlib import Path
import cx_Oracle
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def get_oracle_connection():
    """الحصول على اتصال Oracle مباشر"""
    try:
        # معلومات الاتصال
        username = "proshipment"
        password = "ys123"
        host = "localhost"
        port = 1521
        sid = "orcl"
        
        # إنشاء DSN
        dsn = cx_Oracle.makedsn(host, port, sid=sid)
        
        # الاتصال
        connection = cx_Oracle.connect(username, password, dsn)
        print(f"✅ تم الاتصال بـ Oracle بنجاح")
        print(f"   المستخدم: {username}")
        print(f"   الخادم: {host}:{port}")
        print(f"   SID: {sid}")
        
        return connection
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ Oracle: {e}")
        return None

def create_sequences_and_triggers(connection):
    """إنشاء Sequences و Triggers"""
    
    print("\n🔧 إنشاء Sequences و Triggers...")
    
    cursor = connection.cursor()
    
    # قائمة الجداول التي تحتاج sequences
    tables = [
        'units_of_measure',
        'item_groups', 
        'items',
        'currencies',
        'system_settings',
        'companies',
        'fiscal_years',
        'customers',
        'suppliers',
        'warehouses',
        'shipments',
        'shipment_items'
    ]
    
    try:
        for table in tables:
            seq_name = f"{table}_seq"
            trigger_name = f"{table}_trigger"
            
            # إنشاء Sequence
            try:
                cursor.execute(f"""
                    CREATE SEQUENCE {seq_name}
                    START WITH 1
                    INCREMENT BY 1
                    NOCACHE
                    NOCYCLE
                """)
                print(f"   ✅ تم إنشاء sequence: {seq_name}")
            except cx_Oracle.DatabaseError as e:
                if "name is already used" in str(e) or "already exists" in str(e):
                    print(f"   ⚠️ Sequence موجود: {seq_name}")
                else:
                    print(f"   ❌ خطأ في إنشاء sequence {seq_name}: {e}")
            
            # إنشاء Trigger
            try:
                cursor.execute(f"""
                    CREATE OR REPLACE TRIGGER {trigger_name}
                    BEFORE INSERT ON {table}
                    FOR EACH ROW
                    BEGIN
                        IF :NEW.id IS NULL THEN
                            :NEW.id := {seq_name}.NEXTVAL;
                        END IF;
                    END;
                """)
                print(f"   ✅ تم إنشاء trigger: {trigger_name}")
            except cx_Oracle.DatabaseError as e:
                print(f"   ❌ خطأ في إنشاء trigger {trigger_name}: {e}")
        
        connection.commit()
        print("✅ تم إنشاء جميع Sequences و Triggers بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في إنشاء Sequences: {e}")
        connection.rollback()
        return False
    finally:
        cursor.close()

def insert_default_data(connection):
    """إدراج البيانات الافتراضية"""
    
    print("\n📦 إدراج البيانات الافتراضية...")
    
    cursor = connection.cursor()
    
    try:
        # فحص البيانات الموجودة
        cursor.execute("SELECT COUNT(*) FROM units_of_measure")
        units_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM item_groups")
        groups_count = cursor.fetchone()[0]
        
        print(f"   وحدات القياس الموجودة: {units_count}")
        print(f"   مجموعات الأصناف الموجودة: {groups_count}")
        
        # إدراج وحدات القياس
        if units_count == 0:
            print("   إدراج وحدات القياس...")
            units_data = [
                ('كيلوجرام', 'Kilogram', 'كجم', 'kg', 'وحدة قياس الوزن الأساسية'),
                ('جرام', 'Gram', 'جم', 'g', 'وحدة قياس الوزن الصغيرة'),
                ('طن', 'Ton', 'طن', 't', 'وحدة قياس الوزن الكبيرة'),
                ('متر', 'Meter', 'م', 'm', 'وحدة قياس الطول الأساسية'),
                ('سنتيمتر', 'Centimeter', 'سم', 'cm', 'وحدة قياس الطول الصغيرة'),
                ('مليمتر', 'Millimeter', 'مم', 'mm', 'وحدة قياس الطول الدقيقة'),
                ('متر مربع', 'Square Meter', 'م²', 'm²', 'وحدة قياس المساحة'),
                ('متر مكعب', 'Cubic Meter', 'م³', 'm³', 'وحدة قياس الحجم'),
                ('لتر', 'Liter', 'لتر', 'L', 'وحدة قياس السوائل'),
                ('قطعة', 'Piece', 'قطعة', 'pcs', 'وحدة العد الأساسية'),
                ('عبوة', 'Package', 'عبوة', 'pkg', 'وحدة التعبئة'),
                ('صندوق', 'Box', 'صندوق', 'box', 'وحدة التعبئة الكبيرة'),
                ('كرتون', 'Carton', 'كرتون', 'ctn', 'وحدة التعبئة المتوسطة'),
                ('زوج', 'Pair', 'زوج', 'pair', 'وحدة للأشياء المزدوجة'),
                ('دستة', 'Dozen', 'دستة', 'dz', '12 قطعة')
            ]
            
            for unit in units_data:
                cursor.execute("""
                    INSERT INTO units_of_measure 
                    (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
                    VALUES (:1, :2, :3, :4, :5, 1, SYSDATE, SYSDATE)
                """, unit)
                print(f"     ✅ تم إدراج: {unit[0]}")
        
        # إدراج مجموعات الأصناف
        if groups_count == 0:
            print("   إدراج مجموعات الأصناف...")
            groups_data = [
                ('إلكترونيات', 'Electronics', 'الأجهزة والمعدات الإلكترونية'),
                ('ملابس', 'Clothing', 'الملابس والأزياء'),
                ('أغذية', 'Food', 'المواد الغذائية والمشروبات'),
                ('أدوات منزلية', 'Home Appliances', 'الأدوات والأجهزة المنزلية'),
                ('مواد بناء', 'Construction Materials', 'مواد ومعدات البناء'),
                ('قطع غيار', 'Spare Parts', 'قطع الغيار والمكونات'),
                ('مستحضرات تجميل', 'Cosmetics', 'مستحضرات التجميل والعناية'),
                ('كتب ومطبوعات', 'Books & Publications', 'الكتب والمطبوعات والقرطاسية')
            ]
            
            for group in groups_data:
                cursor.execute("""
                    INSERT INTO item_groups 
                    (name, name_en, description, is_active, created_at, updated_at)
                    VALUES (:1, :2, :3, 1, SYSDATE, SYSDATE)
                """, group)
                print(f"     ✅ تم إدراج: {group[0]}")
        
        # إدراج العملات
        cursor.execute("SELECT COUNT(*) FROM currencies")
        currencies_count = cursor.fetchone()[0]
        
        if currencies_count == 0:
            print("   إدراج العملات...")
            currencies_data = [
                ('SAR', 'ريال سعودي', 'Saudi Riyal', 'ر.س', 1.0, 1),
                ('USD', 'دولار أمريكي', 'US Dollar', '$', 3.75, 0),
                ('EUR', 'يورو', 'Euro', '€', 4.10, 0)
            ]
            
            for currency in currencies_data:
                cursor.execute("""
                    INSERT INTO currencies 
                    (code, name, name_en, symbol, exchange_rate, is_base, is_active, created_at, updated_at)
                    VALUES (:1, :2, :3, :4, :5, :6, 1, SYSDATE, SYSDATE)
                """, currency)
                print(f"     ✅ تم إدراج عملة: {currency[0]}")
        
        # إدراج إعدادات النظام
        cursor.execute("SELECT COUNT(*) FROM system_settings")
        settings_count = cursor.fetchone()[0]
        
        if settings_count == 0:
            print("   إدراج إعدادات النظام...")
            settings_data = [
                ('app_name', 'نظام إدارة الشحنات', 'اسم التطبيق', 'general', 'string', 0),
                ('app_version', '2.0.0', 'إصدار التطبيق', 'general', 'string', 0),
                ('database_type', 'ORACLE', 'نوع قاعدة البيانات', 'system', 'string', 1),
                ('default_currency', 'SAR', 'العملة الافتراضية', 'financial', 'string', 0)
            ]
            
            for setting in settings_data:
                cursor.execute("""
                    INSERT INTO system_settings 
                    (key, value, description, category, data_type, is_system, created_at, updated_at)
                    VALUES (:1, :2, :3, :4, :5, :6, SYSDATE, SYSDATE)
                """, setting)
                print(f"     ✅ تم إدراج إعداد: {setting[0]}")
        
        # إدراج بيانات الشركة
        cursor.execute("SELECT COUNT(*) FROM companies")
        companies_count = cursor.fetchone()[0]
        
        if companies_count == 0:
            print("   إدراج بيانات الشركة...")
            cursor.execute("""
                INSERT INTO companies 
                (name, name_en, address, phone, email, is_active, created_at, updated_at)
                VALUES ('شركة الشحنات المتقدمة', 'Advanced Shipping Company', 
                        'الرياض، المملكة العربية السعودية', '+966-11-1234567', 
                        '<EMAIL>', 1, SYSDATE, SYSDATE)
            """)
            print("     ✅ تم إدراج بيانات الشركة")
        
        # إدراج السنة المالية
        cursor.execute("SELECT COUNT(*) FROM fiscal_years")
        fiscal_count = cursor.fetchone()[0]
        
        if fiscal_count == 0:
            print("   إدراج السنة المالية...")
            current_year = datetime.now().year
            cursor.execute("""
                INSERT INTO fiscal_years 
                (year, start_date, end_date, is_current, created_at, updated_at)
                VALUES (:1, TO_DATE('01/01/' || :1, 'DD/MM/YYYY'), 
                        TO_DATE('31/12/' || :1, 'DD/MM/YYYY'), 1, SYSDATE, SYSDATE)
            """, (current_year,))
            print(f"     ✅ تم إدراج السنة المالية: {current_year}")
        
        connection.commit()
        
        # فحص النتائج النهائية
        cursor.execute("SELECT COUNT(*) FROM units_of_measure")
        final_units = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM item_groups")
        final_groups = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM currencies")
        final_currencies = cursor.fetchone()[0]
        
        print(f"\n📊 النتائج النهائية:")
        print(f"   وحدات القياس: {final_units}")
        print(f"   مجموعات الأصناف: {final_groups}")
        print(f"   العملات: {final_currencies}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إدراج البيانات: {e}")
        connection.rollback()
        return False
    finally:
        cursor.close()

def test_sqlalchemy_connection():
    """اختبار اتصال SQLAlchemy"""
    
    print("\n🧪 اختبار اتصال SQLAlchemy...")
    
    try:
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        if db_manager.test_connection():
            print("✅ اتصال SQLAlchemy ناجح")
            
            # اختبار إنشاء session
            with db_manager.get_session() as session:
                from src.database.models import UnitOfMeasure, ItemGroup
                
                units_count = session.query(UnitOfMeasure).count()
                groups_count = session.query(ItemGroup).count()
                
                print(f"   وحدات القياس (SQLAlchemy): {units_count}")
                print(f"   مجموعات الأصناف (SQLAlchemy): {groups_count}")
                
                if units_count > 0 and groups_count > 0:
                    print("✅ البيانات متاحة عبر SQLAlchemy")
                    return True
                else:
                    print("⚠️ البيانات غير متاحة عبر SQLAlchemy")
                    return False
        else:
            print("❌ فشل اتصال SQLAlchemy")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار SQLAlchemy: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 إصلاح شامل لقاعدة بيانات Oracle")
    print("=" * 50)
    
    # الاتصال بـ Oracle
    connection = get_oracle_connection()
    if not connection:
        print("❌ فشل في الاتصال بـ Oracle")
        return False
    
    try:
        # إنشاء Sequences و Triggers
        if not create_sequences_and_triggers(connection):
            print("❌ فشل في إنشاء Sequences")
            return False
        
        # إدراج البيانات الافتراضية
        if not insert_default_data(connection):
            print("❌ فشل في إدراج البيانات")
            return False
        
        # اختبار SQLAlchemy
        if not test_sqlalchemy_connection():
            print("❌ فشل في اختبار SQLAlchemy")
            return False
        
        print("\n🎉 تم إصلاح Oracle بنجاح!")
        print("✅ جميع البيانات متاحة")
        print("✅ SQLAlchemy يعمل بشكل صحيح")
        print("✅ التطبيق جاهز للعمل مع Oracle")
        
        return True
        
    finally:
        connection.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
