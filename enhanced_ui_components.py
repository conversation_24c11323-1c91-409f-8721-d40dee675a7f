#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكونات واجهة المستخدم المحسنة
Enhanced UI Components for the Main Window
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QScrollArea, QGridLayout, QGroupBox, QLineEdit,
    QComboBox, QSpinBox, QDateEdit, QTextEdit, QTableWidget,
    QTableWidgetItem, QHeaderView, QSplitter
)
from PySide6.QtCore import Qt, QTimer, QDateTime, Signal
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QIcon

class AnimatedButton(QPushButton):
    """زر متحرك مع تأثيرات بصرية"""
    
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setMinimumHeight(40)
        self.setup_style()
    
    def setup_style(self):
        """إعداد نمط الزر"""
        self.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                border: 2px solid #2196F3;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
                color: #1976D2;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #BBDEFB, stop:1 #90CAF9);
                border-color: #1976D2;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #90CAF9, stop:1 #64B5F6);
            }
        """)

class InfoCard(QFrame):
    """بطاقة معلومات مع تصميم جذاب"""
    
    def __init__(self, title, value, icon=None, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMinimumSize(200, 120)
        self.setup_ui(title, value, icon)
    
    def setup_ui(self, title, value, icon):
        """إعداد واجهة البطاقة"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # القيمة
        value_label = QLabel(str(value))
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("color: #2E86AB;")
        layout.addWidget(value_label)
        
        # تطبيق النمط
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFFFFF, stop:1 #F8F9FA);
                border: 1px solid #E0E0E0;
                border-radius: 12px;
                padding: 10px;
            }
            QFrame:hover {
                border-color: #2E86AB;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }
        """)

class DashboardWidget(QWidget):
    """لوحة المعلومات الرئيسية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_timer()
    
    def setup_ui(self):
        """إعداد واجهة لوحة المعلومات"""
        layout = QVBoxLayout(self)
        
        # عنوان اللوحة
        title = QLabel("لوحة معلومات SHIPMENT ERP")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #2E86AB;
                color: white;
                padding: 12px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # شبكة البطاقات
        cards_layout = QGridLayout()
        
        # بطاقات المعلومات
        cards_data = [
            ("إجمالي الشحنات", "1,234"),
            ("الشحنات المعلقة", "56"),
            ("الشحنات المكتملة", "1,178"),
            ("العملاء النشطين", "89"),
            ("إجمالي الإيرادات", "$125,430"),
            ("متوسط وقت التسليم", "3.2 أيام")
        ]
        
        row, col = 0, 0
        for title, value in cards_data:
            card = InfoCard(title, value)
            cards_layout.addWidget(card, row, col)
            col += 1
            if col > 2:
                col = 0
                row += 1
        
        layout.addLayout(cards_layout)
        
        # منطقة الرسوم البيانية
        charts_group = QGroupBox("الرسوم البيانية")
        charts_layout = QHBoxLayout(charts_group)
        
        # رسم بياني وهمي
        chart_placeholder = QLabel("منطقة الرسوم البيانية\n(سيتم إضافة الرسوم البيانية هنا)")
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_placeholder.setMinimumHeight(200)
        chart_placeholder.setStyleSheet("""
            QLabel {
                background-color: #F8F9FA;
                border: 2px dashed #DEE2E6;
                border-radius: 8px;
                color: #6C757D;
                font-size: 14px;
            }
        """)
        charts_layout.addWidget(chart_placeholder)
        
        layout.addWidget(charts_group)
    
    def setup_timer(self):
        """إعداد مؤقت لتحديث البيانات"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_data)
        self.timer.start(30000)  # تحديث كل 30 ثانية
    
    def update_data(self):
        """تحديث البيانات"""
        # هنا يمكن إضافة كود تحديث البيانات من قاعدة البيانات
        pass

class QuickActionsWidget(QWidget):
    """ويدجت الإجراءات السريعة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة الإجراءات السريعة"""
        layout = QVBoxLayout(self)
        
        # عنوان القسم
        title = QLabel("الإجراءات السريعة")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #28A745;
                color: white;
                padding: 10px;
                border-radius: 6px;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title)
        
        # أزرار الإجراءات
        actions = [
            "إنشاء شحنة جديدة",
            "البحث عن شحنة",
            "تقرير يومي",
            "إدارة العملاء",
            "إعدادات النظام",
            "النسخ الاحتياطي"
        ]
        
        for action in actions:
            btn = AnimatedButton(action)
            layout.addWidget(btn)
        
        layout.addStretch()

class RecentActivitiesWidget(QWidget):
    """ويدجت الأنشطة الحديثة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة الأنشطة الحديثة"""
        layout = QVBoxLayout(self)
        
        # عنوان القسم
        title = QLabel("الأنشطة الحديثة")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #FFC107;
                color: #212529;
                padding: 10px;
                border-radius: 6px;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title)
        
        # جدول الأنشطة
        table = QTableWidget(5, 3)
        table.setHorizontalHeaderLabels(["الوقت", "النشاط", "المستخدم"])
        
        # بيانات وهمية
        activities = [
            ("10:30", "إنشاء شحنة جديدة #1234", "أحمد محمد"),
            ("10:15", "تحديث بيانات العميل", "فاطمة علي"),
            ("09:45", "طباعة تقرير شهري", "محمد حسن"),
            ("09:30", "تسجيل دخول النظام", "سارة أحمد"),
            ("09:00", "نسخ احتياطي للبيانات", "النظام")
        ]
        
        for row, (time, activity, user) in enumerate(activities):
            table.setItem(row, 0, QTableWidgetItem(time))
            table.setItem(row, 1, QTableWidgetItem(activity))
            table.setItem(row, 2, QTableWidgetItem(user))
        
        # تنسيق الجدول
        table.horizontalHeader().setStretchLastSection(True)
        table.verticalHeader().setVisible(False)
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(table)

class SystemStatusWidget(QWidget):
    """ويدجت حالة النظام"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة حالة النظام"""
        layout = QVBoxLayout(self)
        
        # عنوان القسم
        title = QLabel("حالة النظام")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #17A2B8;
                color: white;
                padding: 10px;
                border-radius: 6px;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات النظام
        info_layout = QVBoxLayout()
        
        status_items = [
            ("حالة قاعدة البيانات:", "متصل", "green"),
            ("حالة الخادم:", "يعمل بشكل طبيعي", "green"),
            ("استخدام الذاكرة:", "65%", "orange"),
            ("مساحة القرص:", "78% مستخدمة", "orange"),
            ("آخر نسخة احتياطية:", "اليوم 02:00", "green")
        ]
        
        for label, value, color in status_items:
            item_layout = QHBoxLayout()
            
            label_widget = QLabel(label)
            label_widget.setFont(QFont("Arial", 10, QFont.Bold))
            
            value_widget = QLabel(value)
            value_widget.setStyleSheet(f"color: {color}; font-weight: bold;")
            
            item_layout.addWidget(label_widget)
            item_layout.addWidget(value_widget)
            item_layout.addStretch()
            
            info_layout.addLayout(item_layout)
        
        layout.addLayout(info_layout)
        layout.addStretch()

class EnhancedMainContent(QWidget):
    """المحتوى الرئيسي المحسن"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد المحتوى الرئيسي"""
        layout = QHBoxLayout(self)
        
        # المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        
        # العمود الأيسر - لوحة المعلومات
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.addWidget(DashboardWidget())
        splitter.addWidget(left_widget)
        
        # العمود الأيمن - الإجراءات والأنشطة
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.addWidget(QuickActionsWidget())
        right_layout.addWidget(RecentActivitiesWidget())
        right_layout.addWidget(SystemStatusWidget())
        splitter.addWidget(right_widget)
        
        # تحديد النسب
        splitter.setSizes([800, 400])
        
        layout.addWidget(splitter)
