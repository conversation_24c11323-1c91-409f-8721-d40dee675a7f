# -*- coding: utf-8 -*-
"""
نافذة إضافة رابط تشعبي للمستندات
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLineEdit, QTextEdit, QPushButton, QLabel,
                               QMessageBox, QComboBox)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
import re

class AddLinkDialog(QDialog):
    """نافذة إضافة رابط تشعبي"""
    
    def __init__(self, parent=None, link_type="", existing_url="", existing_description=""):
        super().__init__(parent)
        self.link_type = link_type
        self.existing_url = existing_url
        self.existing_description = existing_description
        self.setup_ui()
        self.setup_connections()
        
        # إذا كان هناك رابط موجود، املأ الحقول
        if existing_url:
            self.url_edit.setText(existing_url)
        if existing_description:
            self.description_edit.setPlainText(existing_description)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = f"إضافة رابط - {self.link_type}" if self.link_type else "إضافة رابط"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 400)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # عنوان النافذة
        title_label = QLabel(title)
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("QLabel { color: #2c3e50; margin: 10px; }")
        main_layout.addWidget(title_label)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # نوع المستند
        self.document_type_combo = QComboBox()
        self.document_type_combo.addItems([
            "Google Drive", "OneDrive", "Dropbox", "SharePoint",
            "موقع ويب", "خدمة سحابية", "خادم محلي", "أخرى"
        ])
        form_layout.addRow("نوع المصدر:", self.document_type_combo)
        
        # الرابط
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("https://example.com/document")
        self.url_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        form_layout.addRow("الرابط:", self.url_edit)
        
        # اسم المستند/الوصف
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("وصف المستند أو ملاحظات إضافية...")
        self.description_edit.setStyleSheet("""
            QTextEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 11px;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        form_layout.addRow("الوصف:", self.description_edit)
        
        main_layout.addLayout(form_layout)
        
        # معلومات إضافية
        info_label = QLabel("💡 نصائح:")
        info_label.setStyleSheet("QLabel { color: #7f8c8d; font-weight: bold; margin-top: 10px; }")
        main_layout.addWidget(info_label)
        
        tips_label = QLabel("""
• تأكد من أن الرابط صحيح ويمكن الوصول إليه
• يمكنك إضافة روابط من Google Drive أو OneDrive أو أي خدمة سحابية
• استخدم وصفاً واضحاً لتسهيل التعرف على المستند لاحقاً
• يمكنك تعديل الرابط لاحقاً من خلال الضغط على زر التعديل
        """)
        tips_label.setStyleSheet("QLabel { color: #7f8c8d; font-size: 10px; margin-left: 20px; }")
        tips_label.setWordWrap(True)
        main_layout.addWidget(tips_label)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.test_link_button = QPushButton("اختبار الرابط")
        self.test_link_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addWidget(self.test_link_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_button.clicked.connect(self.save_link)
        self.cancel_button.clicked.connect(self.reject)
        self.test_link_button.clicked.connect(self.test_link)
        self.url_edit.textChanged.connect(self.validate_url)
    
    def validate_url(self):
        """التحقق من صحة الرابط"""
        url = self.url_edit.text().strip()
        
        if not url:
            self.save_button.setEnabled(False)
            return
        
        # التحقق من صيغة الرابط
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        is_valid = url_pattern.match(url) is not None
        self.save_button.setEnabled(is_valid)
        
        # تغيير لون الحقل حسب الصحة
        if url and not is_valid:
            self.url_edit.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #e74c3c;
                    border-radius: 5px;
                    font-size: 11px;
                    background-color: #fdf2f2;
                }
            """)
        else:
            self.url_edit.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #27ae60;
                    border-radius: 5px;
                    font-size: 11px;
                    background-color: #f8fff8;
                }
            """)
    
    def test_link(self):
        """اختبار الرابط"""
        url = self.url_edit.text().strip()
        if not url:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الرابط أولاً")
            return
        
        try:
            import webbrowser
            webbrowser.open(url)
            QMessageBox.information(self, "نجح", "تم فتح الرابط في المتصفح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الرابط: {str(e)}")
    
    def save_link(self):
        """حفظ الرابط"""
        url = self.url_edit.text().strip()
        description = self.description_edit.toPlainText().strip()
        
        if not url:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الرابط")
            self.url_edit.setFocus()
            return
        
        # التحقق من صحة الرابط
        self.validate_url()
        if not self.save_button.isEnabled():
            QMessageBox.warning(self, "تحذير", "الرابط غير صحيح، يرجى التحقق من الصيغة")
            self.url_edit.setFocus()
            return
        
        self.accept()
    
    def get_link_data(self):
        """الحصول على بيانات الرابط"""
        return {
            'url': self.url_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'source_type': self.document_type_combo.currentText()
        }
