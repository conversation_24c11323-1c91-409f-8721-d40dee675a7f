# -*- coding: utf-8 -*-
"""
تبويب إعدادات بيانات الشركة
Company Settings Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTextEdit,
                               QGroupBox, QMessageBox, QFileDialog)
from PySide6.QtCore import Qt

from ...database.database_manager import DatabaseManager
from ...database.models import Company

class CompanySettingsWidget(QWidget):
    """ويدجت إعدادات بيانات الشركة"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_company_id = None
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_layout = QFormLayout(basic_group)
        
        self.name_edit = QLineEdit()
        basic_layout.addRow("اسم الشركة:", self.name_edit)
        
        self.name_en_edit = QLineEdit()
        basic_layout.addRow("الاسم بالإنجليزية:", self.name_en_edit)
        
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        basic_layout.addRow("العنوان:", self.address_edit)

        self.address_en_edit = QTextEdit()
        self.address_en_edit.setMaximumHeight(80)
        basic_layout.addRow("العنوان بالإنجليزية:", self.address_en_edit)

        main_layout.addWidget(basic_group)
        
        # مجموعة بيانات الاتصال
        contact_group = QGroupBox("بيانات الاتصال")
        contact_layout = QFormLayout(contact_group)
        
        self.phone_edit = QLineEdit()
        contact_layout.addRow("الهاتف:", self.phone_edit)

        self.fax_edit = QLineEdit()
        contact_layout.addRow("الفاكس:", self.fax_edit)

        self.email_edit = QLineEdit()
        contact_layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        main_layout.addWidget(contact_group)
        
        # مجموعة البيانات القانونية
        legal_group = QGroupBox("البيانات القانونية")
        legal_layout = QFormLayout(legal_group)
        
        self.tax_number_edit = QLineEdit()
        legal_layout.addRow("الرقم الضريبي:", self.tax_number_edit)
        
        self.commercial_register_edit = QLineEdit()
        legal_layout.addRow("السجل التجاري:", self.commercial_register_edit)
        
        main_layout.addWidget(legal_group)
        
        # مجموعة الشعار
        logo_group = QGroupBox("شعار الشركة")
        logo_layout = QVBoxLayout(logo_group)
        
        logo_buttons_layout = QHBoxLayout()
        
        self.logo_path_label = QLabel("لم يتم اختيار شعار")
        logo_buttons_layout.addWidget(self.logo_path_label)
        
        choose_logo_button = QPushButton("اختيار شعار")
        choose_logo_button.clicked.connect(self.choose_logo)
        logo_buttons_layout.addWidget(choose_logo_button)
        
        remove_logo_button = QPushButton("إزالة الشعار")
        remove_logo_button.clicked.connect(self.remove_logo)
        logo_buttons_layout.addWidget(remove_logo_button)
        
        logo_layout.addLayout(logo_buttons_layout)
        main_layout.addWidget(logo_group)
        
        # أزرار الحفظ
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        save_button = QPushButton("حفظ البيانات")
        save_button.clicked.connect(self.save_data)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        reset_button = QPushButton("إعادة تعيين")
        reset_button.clicked.connect(self.load_data)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(reset_button)
        
        main_layout.addLayout(buttons_layout)
        main_layout.addStretch()
    
    def load_data(self):
        """تحميل بيانات الشركة"""
        session = self.db_manager.get_session()
        try:
            # البحث عن الشركة الأولى (يمكن تطوير هذا لدعم عدة شركات)
            company = session.query(Company).filter_by(is_active=True).first()
            
            if company:
                self.current_company_id = company.id
                self.name_edit.setText(company.name or "")
                self.name_en_edit.setText(company.name_en or "")
                self.address_edit.setPlainText(company.address or "")
                self.address_en_edit.setPlainText(company.address_en or "")
                self.phone_edit.setText(company.phone or "")
                self.fax_edit.setText(company.fax or "")
                self.email_edit.setText(company.email or "")
                self.tax_number_edit.setText(company.tax_number or "")
                self.commercial_register_edit.setText(company.commercial_register or "")
                
                if company.logo_path:
                    self.logo_path_label.setText(company.logo_path)
                else:
                    self.logo_path_label.setText("لم يتم اختيار شعار")
            else:
                # إنشاء شركة افتراضية
                self.current_company_id = None
                self.clear_form()
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل بيانات الشركة:\n{str(e)}"
            )
        finally:
            session.close()
    
    def save_data(self):
        """حفظ بيانات الشركة"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم الشركة")
            return
        
        session = self.db_manager.get_session()
        try:
            if self.current_company_id:
                # تحديث الشركة الموجودة
                company = session.query(Company).get(self.current_company_id)
            else:
                # إنشاء شركة جديدة
                company = Company()
                session.add(company)
            
            # تحديث البيانات
            company.name = self.name_edit.text()
            company.name_en = self.name_en_edit.text()
            company.address = self.address_edit.toPlainText()
            company.address_en = self.address_en_edit.toPlainText()
            company.phone = self.phone_edit.text()
            company.fax = self.fax_edit.text()
            company.email = self.email_edit.text()
            company.tax_number = self.tax_number_edit.text()
            company.commercial_register = self.commercial_register_edit.text()
            
            if self.logo_path_label.text() != "لم يتم اختيار شعار":
                company.logo_path = self.logo_path_label.text()
            
            session.commit()
            
            if not self.current_company_id:
                self.current_company_id = company.id
            
            QMessageBox.information(self, "تم الحفظ", "تم حفظ بيانات الشركة بنجاح")
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(
                self,
                "خطأ في الحفظ",
                f"حدث خطأ أثناء حفظ بيانات الشركة:\n{str(e)}"
            )
        finally:
            session.close()
    
    def choose_logo(self):
        """اختيار شعار الشركة"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار شعار الشركة",
            "",
            "ملفات الصور (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        
        if file_path:
            self.logo_path_label.setText(file_path)
    
    def remove_logo(self):
        """إزالة شعار الشركة"""
        self.logo_path_label.setText("لم يتم اختيار شعار")
    
    def clear_form(self):
        """مسح النموذج"""
        self.name_edit.clear()
        self.name_en_edit.clear()
        self.address_edit.clear()
        self.address_en_edit.clear()
        self.phone_edit.clear()
        self.fax_edit.clear()
        self.email_edit.clear()
        self.tax_number_edit.clear()
        self.commercial_register_edit.clear()
        self.logo_path_label.setText("لم يتم اختيار شعار")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        self.save_data()
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        self.clear_form()
        # تعيين قيم افتراضية
        self.name_edit.setText("شركة الشحنات المتقدمة")
        self.name_en_edit.setText("Advanced Shipping Company")
        self.address_edit.setPlainText("الرياض، المملكة العربية السعودية")
        self.phone_edit.setText("+966-11-1234567")
        self.email_edit.setText("<EMAIL>")
