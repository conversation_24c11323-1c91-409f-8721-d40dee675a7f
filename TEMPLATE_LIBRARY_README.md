# مكتبة القوالب - SHIPMENT ERP Template Library

## 🎯 نظرة عامة

تم تطوير **مكتبة القوالب** لتوفير نظام شامل ومرن لإدارة واستدعاء جميع قوالب النماذج في مشروع SHIPMENT ERP. المكتبة تتيح إنشاء واستخدام القوالب بطريقة سهلة ومنظمة مع دعم ملء الشاشة والتخصيص المتقدم.

## ✨ الميزات الرئيسية

### 🏛️ **إدارة شاملة للقوالب:**
- **سجل مركزي** لجميع القوالب
- **تصنيف وتنظيم** القوالب حسب الفئات
- **نظام بحث متقدم** في القوالب
- **معلومات تفصيلية** لكل قالب

### 🖥️ **دعم ملء الشاشة:**
- **وضع ملء الشاشة** افتراضي
- **التبديل السريع** بين الأوضاع (F11)
- **العرض العادي** عند الحاجة
- **حفظ تفضيلات العرض**

### ⚡ **اختصارات سريعة:**
- **أسماء مخصصة** لكل قالب
- **دوال مباشرة** للاستدعاء
- **واجهة برمجية بسيطة**
- **استخدام سهل ومرن**

## 📋 القوالب المتاحة

### 📝 **نموذج إدخال أساسي**
- **الاسم المرجعي**: `نموذج_ادخال_اساسي`
- **الاسم المعروض**: نموذج إدخال أساسي
- **الوصف**: نموذج أساسي لإدخال بيانات الشحنات والعملاء
- **الفئة**: نماذج_البيانات
- **الإصدار**: 1.0.0
- **المطور**: فريق SHIPMENT Solutions

## 🚀 طرق الاستخدام

### 1. **الطريقة الأساسية:**
```python
from template_library import Templates

# إنشاء قالب بملء الشاشة
template = Templates.create("نموذج_ادخال_اساسي", fullscreen=True)

# إنشاء قالب بالوضع العادي
template = Templates.create("نموذج_ادخال_اساسي", fullscreen=False)
```

### 2. **الطريقة المباشرة:**
```python
from template_library import Templates

# استخدام الطريقة المباشرة للقالب
template = Templates.نموذج_ادخال_اساسي(fullscreen=True)
```

### 3. **الاختصارات السريعة:**
```python
from template_library import create_template, نموذج_ادخال_اساسي

# الدالة المختصرة العامة
template1 = create_template("نموذج_ادخال_اساسي", fullscreen=True)

# الدالة المختصرة المخصصة
template2 = نموذج_ادخال_اساسي(fullscreen=False)
```

### 4. **الاستخدام المتقدم:**
```python
from template_library import TemplateLibrary

# إعداد التطبيق
app = QApplication(sys.argv)
TemplateLibrary.setup_app(app)

# إنشاء قالب مع معاملات مخصصة
template = TemplateLibrary.create(
    "نموذج_ادخال_اساسي",
    fullscreen=True,
    # معاملات إضافية...
)

# الحصول على المتغيرات
variables = template.get_form_variables()

# تخصيص البيانات
variables['document_number'].setText("SHP-001")
variables['customer_name'].setText("اسم العميل")

# جمع البيانات
data = template.collect_form_data()
```

## 🔧 إدارة القوالب

### **عرض القوالب المتاحة:**
```python
from template_library import Templates

# قائمة جميع القوالب
templates = Templates.list_all()
print(f"القوالب المتاحة: {templates}")

# معلومات قالب محدد
Templates.info("نموذج_ادخال_اساسي")
```

### **البحث في القوالب:**
```python
# البحث بكلمة مفتاحية
results = Templates.search("أساسي")
print(f"نتائج البحث: {len(results)} قالب")
```

### **إدارة القوالب النشطة:**
```python
# الحصول على مدير القوالب
manager = Templates.get_manager()

# عرض القوالب النشطة
active_templates = manager.get_active_templates()
print(f"القوالب النشطة: {len(active_templates)}")

# إغلاق جميع القوالب
manager.close_all_templates()
```

## 🖥️ وظائف ملء الشاشة

### **التحكم في وضع العرض:**
```python
# إنشاء قالب بملء الشاشة
template = Templates.نموذج_ادخال_اساسي(fullscreen=True)

# التبديل لملء الشاشة
template.maximize_view()

# التبديل للعرض العادي
template.normal_view()

# التبديل التلقائي
template.toggle_fullscreen()
```

### **اختصارات لوحة المفاتيح:**
- **F11**: تبديل ملء الشاشة
- **Escape**: العرض العادي (من ملء الشاشة)

## 📁 الملفات المتضمنة

### **الملفات الأساسية:**
- **`template_library.py`** - المكتبة الرئيسية
- **`form_template.py`** - القالب الأساسي المحدث
- **`run_template_library.py`** - نافذة اختيار القوالب
- **`template_examples.py`** - أمثلة شاملة للاستخدام
- **`test_template_library.py`** - اختبارات شاملة

### **ملفات التوثيق:**
- **`TEMPLATE_LIBRARY_README.md`** - هذا الملف
- **`دليل_قالب_النموذج.md`** - دليل القالب الأساسي
- **`ملخص_قالب_النموذج.md`** - ملخص المشروع

## 🧪 الاختبار والتشغيل

### **اختبار المكتبة:**
```bash
python test_template_library.py
```

### **تشغيل نافذة اختيار القوالب:**
```bash
python run_template_library.py
```

### **تشغيل الأمثلة:**
```bash
# تشغيل جميع الأمثلة
python template_examples.py

# العرض التفاعلي
python template_examples.py --interactive
```

## 🔧 إضافة قوالب جديدة

### **تسجيل قالب جديد:**
```python
from template_library import Templates
from my_custom_template import MyCustomTemplate

# تسجيل القالب الجديد
Templates.register_new(
    name="قالب_مخصص",
    display_name="قالب مخصص",
    description="وصف القالب المخصص",
    template_class=MyCustomTemplate,
    category="قوالب_مخصصة",
    version="1.0.0",
    author="اسم المطور",
    tags=["مخصص", "جديد"]
)

# استخدام القالب الجديد
template = Templates.create("قالب_مخصص")
```

## 📊 هيكل المكتبة

### **الكلاسات الرئيسية:**

#### **TemplateRegistry:**
- إدارة سجل القوالب
- تسجيل وحفظ معلومات القوالب
- البحث والتصفية

#### **TemplateManager:**
- إنشاء وإدارة القوالب النشطة
- معالجة الأحداث والإشارات
- إدارة دورة حياة القوالب

#### **TemplateLibrary:**
- الواجهة الرئيسية للمكتبة
- توفير طرق سهلة للاستخدام
- إدارة التطبيق والإعدادات

## 🎨 التخصيص والتطوير

### **تخصيص القوالب:**
```python
# إنشاء قالب مخصص
class CustomFormTemplate(FormTemplate):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.customize_template()
    
    def customize_template(self):
        # تخصيصات إضافية
        self.setWindowTitle("قالب مخصص")
        # إضافة حقول جديدة
        # تعديل الأنماط
        # إضافة وظائف جديدة

# تسجيل القالب المخصص
Templates.register_new(
    name="قالب_مخصص_جديد",
    display_name="قالب مخصص جديد",
    description="قالب مخصص للاحتياجات الخاصة",
    template_class=CustomFormTemplate
)
```

## 🔗 التكامل مع المشروع

### **الاستخدام في النظام الرئيسي:**
```python
# في الملف الرئيسي للتطبيق
from template_library import Templates

class MainApplication:
    def __init__(self):
        # إعداد المكتبة
        Templates.setup_app(self.app)
    
    def open_data_entry_form(self):
        """فتح نموذج إدخال البيانات"""
        template = Templates.نموذج_ادخال_اساسي(fullscreen=True)
        return template
    
    def create_custom_form(self, form_type):
        """إنشاء نموذج مخصص"""
        template = Templates.create(form_type, fullscreen=True)
        return template
```

## 📈 الإحصائيات

### **نتائج الاختبار:**
```
✅ Python 3.13.5
✅ PySide6 6.9.1
✅ جميع الملفات متوفرة
✅ جميع المكونات تعمل
✅ 1 قالب مسجل
✅ 30 متغير متاح
✅ جميع الاختصارات تعمل
✅ وظائف ملء الشاشة تعمل
🎉 جميع الاختبارات نجحت!
```

## 🎯 الميزات المستقبلية

### **التطوير المخطط:**
- **قوالب إضافية** للنماذج المختلفة
- **نظام ثيمات** للقوالب
- **حفظ التفضيلات** للمستخدمين
- **قوالب ديناميكية** قابلة للتخصيص
- **تصدير واستيراد** القوالب
- **نظام إضافات** للقوالب

## 📞 الدعم والمساعدة

### **للحصول على المساعدة:**
1. راجع التوثيق الشامل
2. شغل ملفات الاختبار
3. جرب الأمثلة المتوفرة
4. تحقق من ملفات التوثيق

### **الإبلاغ عن المشاكل:**
- تأكد من تثبيت جميع المتطلبات
- شغل ملف الاختبار للتحقق
- راجع رسائل الخطأ في وحدة التحكم

---

## ✅ النتيجة النهائية

🎉 **مكتبة قوالب شاملة ومتطورة جاهزة للاستخدام!**

### **المنجز:**
- ✅ **تطوير القالب** ليفتح في وضع ملء الشاشة
- ✅ **إنشاء مكتبة شاملة** لإدارة القوالب
- ✅ **نظام تسمية مخصص** لكل قالب
- ✅ **نموذج إدخال أساسي** جاهز للاستخدام
- ✅ **اختصارات سريعة** ومرنة
- ✅ **توثيق شامل** وأمثلة عملية
- ✅ **اختبارات شاملة** ومكتملة

**📅 تاريخ الإنجاز**: 2024  
**👨‍💻 المطور**: فريق SHIPMENT Solutions  
**🎯 الحالة**: مكتمل وجاهز للاستخدام ✅
