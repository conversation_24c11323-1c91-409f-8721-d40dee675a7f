# إعداد قاعدة البيانات - Database Setup

## 🗄️ أنواع قواعد البيانات المدعومة

يدعم التطبيق نوعين من قواعد البيانات:

### 1. SQLite (للتطوير والاختبار)
- قاعدة بيانات محلية خفيفة
- لا تحتاج إلى خادم منفصل
- مناسبة للتطوير والاختبار

### 2. Oracle Database (للإنتاج)
- قاعدة بيانات مؤسسية قوية
- تدعم المعاملات المعقدة والأداء العالي
- مناسبة للبيئات الإنتاجية

## ⚙️ التكوين

### ملف التكوين الرئيسي
```
src/database/config/database.json
```

### تكوين SQLite
```json
{
  "type": "sqlite",
  "sqlite_config": {
    "path": "data/proshipment.db",
    "timeout": 30,
    "check_same_thread": false
  }
}
```

### تكوين Oracle
```json
{
  "type": "oracle",
  "oracle_config": {
    "host": "localhost",
    "port": 1521,
    "service_name": "orcl",
    "sid": "orcl",
    "username": "proshipment",
    "password": "ys123",
    "connection_type": "sid",
    "pool_size": 20,
    "max_overflow": 30,
    "pool_timeout": 30,
    "pool_recycle": 3600,
    "pool_pre_ping": true,
    "use_ssl": false
  }
}
```

## 🔄 التبديل بين قواعد البيانات

### للتبديل إلى SQLite:
1. افتح ملف `src/database/config/database.json`
2. غير `"type": "oracle"` إلى `"type": "sqlite"`
3. احفظ الملف وأعد تشغيل التطبيق

### للتبديل إلى Oracle:
1. تأكد من تثبيت Oracle Database
2. افتح ملف `src/database/config/database.json`
3. غير `"type": "sqlite"` إلى `"type": "oracle"`
4. حدث إعدادات Oracle في `oracle_config`
5. احفظ الملف وأعد تشغيل التطبيق

## 🧪 اختبار الاتصال

### تشغيل اختبار الاتصال:
```bash
python test_oracle_connection.py
```

هذا السكريبت سيقوم بـ:
- تحميل إعدادات قاعدة البيانات
- اختبار الاتصال
- تهيئة الجداول
- تنفيذ استعلام تجريبي

## 📋 متطلبات Oracle

### المكتبات المطلوبة:
```
cx_Oracle>=8.3.0
SQLAlchemy>=1.4.0
```

### إعداد Oracle Database:
1. تثبيت Oracle Database (XE أو Standard)
2. إنشاء مستخدم جديد:
```sql
CREATE USER proshipment IDENTIFIED BY ys123;
GRANT CONNECT, RESOURCE, DBA TO proshipment;
GRANT UNLIMITED TABLESPACE TO proshipment;
```

3. تحديث إعدادات الاتصال في ملف التكوين

## 🔧 استكشاف الأخطاء

### خطأ في الاتصال بـ Oracle:
- تأكد من تشغيل خدمة Oracle
- تحقق من صحة اسم المستخدم وكلمة المرور
- تأكد من صحة اسم الخدمة أو SID
- تحقق من إعدادات الشبكة والمنفذ

### خطأ في تثبيت cx_Oracle:
```bash
pip install cx_Oracle
```

أو إذا كنت تستخدم Oracle Instant Client:
```bash
pip install cx_Oracle --upgrade
```

## 📊 مراقبة الأداء

التطبيق يدعم مراقبة الأداء مع الإعدادات التالية:
- `pool_size`: عدد الاتصالات في المجموعة
- `max_overflow`: الحد الأقصى للاتصالات الإضافية
- `pool_timeout`: مهلة انتظار الاتصال
- `pool_recycle`: إعادة تدوير الاتصالات
- `pool_pre_ping`: اختبار الاتصال قبل الاستخدام

## 🔐 الأمان

### لبيئة الإنتاج:
- استخدم كلمات مرور قوية
- فعل SSL إذا كان متاحاً
- استخدم متغيرات البيئة لكلمات المرور
- قم بتشفير ملفات التكوين

### متغيرات البيئة:
```bash
export DATABASE_TYPE=oracle
export ORACLE_HOST=localhost
export ORACLE_PORT=1521
export ORACLE_SERVICE_NAME=orcl
export ORACLE_USERNAME=proshipment
export ORACLE_PASSWORD=ys123
```

## 📝 ملاحظات مهمة

1. **النسخ الاحتياطي**: تأكد من إجراء نسخ احتياطية منتظمة
2. **الأداء**: Oracle أسرع للبيانات الكبيرة
3. **التطوير**: SQLite أسهل للتطوير والاختبار
4. **الترقية**: يمكن ترقية البيانات من SQLite إلى Oracle
5. **التوافق**: جميع الميزات متوافقة مع كلا النوعين
