# ميزة الحوالات متعددة الموردين
## Multi-Supplier Remittance Feature

### 📋 نظرة عامة

تم تطوير ميزة جديدة في نظام إدارة الحوالات تسمح بإنشاء حوالة واحدة يمكن توزيعها على عدة موردين مختلفين. هذه الميزة مفيدة جداً في الحالات التالية:

- **دفع مستحقات متعددة**: عندما تريد دفع مستحقات لعدة موردين في حوالة واحدة
- **توزيع المبالغ**: توزيع مبلغ كبير على عدة موردين حسب نسب محددة
- **تسوية حسابات جماعية**: تسوية حسابات عدة موردين في معاملة واحدة
- **توفير الرسوم**: تقليل رسوم التحويل عبر دمج عدة تحويلات في حوالة واحدة

### 🚀 المميزات الجديدة

#### 1. **واجهة متقدمة لإدارة الموردين المتعددين**
- ✅ جدول تفاعلي لإضافة وإدارة الموردين
- ✅ حساب تلقائي للنسب المئوية والمبالغ
- ✅ توزيع متساوي بنقرة واحدة
- ✅ تحديث فوري للملخص المالي

#### 2. **نظام توزيع ذكي**
- ✅ توزيع المبلغ بالتساوي على جميع الموردين
- ✅ حساب النسب المئوية تلقائياً
- ✅ تحديث المبالغ عند تغيير النسب
- ✅ التحقق من صحة التوزيع قبل الحفظ

#### 3. **معلومات مفصلة لكل مورد**
- ✅ مبلغ منفصل لكل مورد
- ✅ نسبة مئوية من إجمالي الحوالة
- ✅ غرض منفصل لكل مورد
- ✅ ملاحظات خاصة بكل مورد
- ✅ بنك وحساب مستقبل منفصل لكل مورد

#### 4. **ملخص مالي شامل**
- ✅ عدد الموردين المشاركين
- ✅ إجمالي المبلغ الموزع
- ✅ المبلغ المتبقي
- ✅ إجمالي النسب المئوية
- ✅ مؤشر صحة التوزيع

### 🛠️ كيفية الاستخدام

#### الخطوة 1: فتح نافذة الحوالة متعددة الموردين
1. افتح نظام إدارة الحوالات
2. انقر على زر **"👥 حوالة متعددة"**
3. ستفتح نافذة جديدة مخصصة للحوالات متعددة الموردين

#### الخطوة 2: إدخال المعلومات العامة
```
📋 المعلومات المطلوبة:
- رقم الحوالة: يتم إنشاؤه تلقائياً
- العملة: اختر العملة المناسبة
- إجمالي المبلغ: المبلغ الكلي للحوالة
- سعر الصرف: إذا كانت العملة غير أساسية
- تاريخ الحوالة: تاريخ إجراء الحوالة
- البنك المرسل: البنك الذي سيرسل الحوالة
- حساب المرسل: الحساب المرسل منه
- الغرض العام: الغرض العام من الحوالة
- ملاحظات عامة: أي ملاحظات إضافية
```

#### الخطوة 3: إضافة الموردين
1. انقر على **"➕ إضافة مورد"**
2. اختر المورد من القائمة المنبثقة
3. كرر العملية لإضافة جميع الموردين المطلوبين

#### الخطوة 4: توزيع المبالغ
يمكنك توزيع المبالغ بطرق مختلفة:

**أ) التوزيع اليدوي:**
- أدخل المبلغ لكل مورد يدوياً
- ستحسب النسب المئوية تلقائياً

**ب) التوزيع بالنسب:**
- أدخل النسبة المئوية لكل مورد
- ستحسب المبالغ تلقائياً

**ج) التوزيع المتساوي:**
- انقر على **"⚖️ توزيع متساوي"**
- سيتم توزيع المبلغ بالتساوي على جميع الموردين

**د) حساب النسب:**
- انقر على **"📊 حساب النسب"**
- ستحسب النسب بناءً على المبالغ المدخلة

#### الخطوة 5: تحديد تفاصيل كل مورد
لكل مورد يمكنك تحديد:
- **البنك المستقبل**: البنك الذي سيستقبل المبلغ
- **حساب المستقبل**: الحساب المحدد للمورد
- **الغرض**: الغرض من التحويل لهذا المورد
- **ملاحظات**: أي ملاحظات خاصة بالمورد

#### الخطوة 6: مراجعة الملخص
تحقق من الملخص المالي:
- ✅ **عدد الموردين**: يجب أن يكون أكبر من 0
- ✅ **إجمالي المبلغ الموزع**: يجب أن يساوي إجمالي المبلغ
- ✅ **المبلغ المتبقي**: يجب أن يكون 0.000
- ✅ **إجمالي النسب**: يجب أن يكون 100.00%

#### الخطوة 7: حفظ الحوالة
- عندما يظهر **"✅ التوزيع صحيح ومتوازن"**
- انقر على **"💾 حفظ الحوالة"**
- ستحفظ الحوالة مع جميع تفاصيل الموردين

### 🗄️ هيكل قاعدة البيانات

#### جدول `remittances` (الحوالة الرئيسية)
```sql
- id: معرف الحوالة
- remittance_number: رقم الحوالة
- currency_id: العملة
- amount: إجمالي المبلغ
- exchange_rate: سعر الصرف
- sender_bank_id: البنك المرسل
- sender_account_id: حساب المرسل
- remittance_date: تاريخ الحوالة
- purpose: الغرض العام
- notes: ملاحظات عامة
- status: حالة الحوالة
```

#### جدول `remittance_details` (تفاصيل الموردين)
```sql
- id: معرف التفصيل
- remittance_id: معرف الحوالة الرئيسية
- supplier_id: معرف المورد
- amount: المبلغ للمورد
- percentage: النسبة المئوية
- purpose: الغرض للمورد
- notes: ملاحظات للمورد
- receiver_bank_id: البنك المستقبل للمورد
- receiver_account_id: حساب المستقبل للمورد
```

### 📊 التقارير والإحصائيات

#### تقارير جديدة متاحة:
1. **تقرير الحوالات متعددة الموردين**
2. **تقرير توزيع المبالغ حسب المورد**
3. **إحصائيات الحوالات المجمعة**
4. **تحليل كفاءة التوزيع**

### 🔧 الدعم الفني

#### متطلبات النظام:
- Python 3.8+
- PySide6
- SQLAlchemy
- قاعدة بيانات محدثة بالجداول الجديدة

#### ملفات النظام الجديدة:
```
src/ui/suppliers/multi_supplier_remittance_dialog.py
src/database/models.py (محدث)
src/database/update_database_remittances.py (محدث)
```

#### كيفية التحديث:
```bash
# تشغيل سكريبت تحديث قاعدة البيانات
python src/database/update_database_remittances.py

# تشغيل النظام
python main.py
```

### 🎯 الفوائد

1. **توفير الوقت**: إنشاء حوالة واحدة بدلاً من عدة حوالات منفصلة
2. **تقليل التكاليف**: رسوم تحويل أقل
3. **دقة أكبر**: حسابات تلقائية تقلل الأخطاء
4. **مرونة عالية**: توزيع حسب النسب أو المبالغ
5. **تتبع أفضل**: ربط جميع التحويلات برقم حوالة واحد
6. **تقارير شاملة**: إحصائيات مفصلة لكل حوالة

### 🔮 التطويرات المستقبلية

- [ ] دعم العملات المختلطة في الحوالة الواحدة
- [ ] قوالب توزيع محفوظة
- [ ] تكامل مع أنظمة البنوك الإلكترونية
- [ ] إشعارات تلقائية للموردين
- [ ] تتبع حالة كل مورد منفصلاً

---

**تم تطوير هذه الميزة بواسطة:** فريق تطوير نظام إدارة الشحنات  
**تاريخ الإصدار:** ديسمبر 2024  
**الإصدار:** 1.0.0
