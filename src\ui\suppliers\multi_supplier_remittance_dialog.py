# -*- coding: utf-8 -*-
"""
نافذة حوار إنشاء حوالة متعددة الموردين
Multi-Supplier Remittance Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QLineEdit, QComboBox, QDateEdit,
                               QDoubleSpinBox, QTableWidget, QTableWidgetItem, QHeaderView,
                               QAbstractItemView, QGroupBox, QFormLayout, QMessageBox,
                               QDialogButtonBox, QFrame, QTextEdit, QCheckBox, QSpinBox,
                               QSplitter, QScrollArea, QWidget)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QFont, QIcon, QPalette, QColor

from ...database.database_manager import DatabaseManager
from ...database.models import (Remittance, RemittanceDetail, Supplier, Currency, Bank, 
                                BankAccount, RemittanceStatus)


class MultiSupplierRemittanceDialog(QDialog):
    """نافذة حوار إنشاء حوالة متعددة الموردين"""
    
    # الإشارات
    remittance_created = Signal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.suppliers_data = []  # قائمة بيانات الموردين
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إنشاء حوالة متعددة الموردين")
        self.setModal(True)
        self.resize(1000, 700)
        
        # تطبيق تخطيط RTL
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel("💰 إنشاء حوالة متعددة الموردين")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ecf0f1, stop:1 #bdc3c7);
                border: 2px solid #95a5a6;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # المحتوى الرئيسي
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # الجزء العلوي - معلومات الحوالة العامة
        self.create_general_info_section()
        splitter.addWidget(self.general_info_group)
        
        # الجزء الأوسط - جدول الموردين
        self.create_suppliers_section()
        splitter.addWidget(self.suppliers_group)
        
        # الجزء السفلي - ملخص الحوالة
        self.create_summary_section()
        splitter.addWidget(self.summary_group)
        
        # تعيين النسب
        splitter.setSizes([200, 350, 150])
        
        # أزرار الحوار
        self.create_dialog_buttons()
        layout.addWidget(self.buttons_frame)
        
    def create_general_info_section(self):
        """إنشاء قسم المعلومات العامة للحوالة"""
        self.general_info_group = QGroupBox("📋 المعلومات العامة للحوالة")
        layout = QGridLayout(self.general_info_group)
        layout.setSpacing(10)
        
        # رقم الحوالة
        layout.addWidget(QLabel("رقم الحوالة:"), 0, 0)
        self.remittance_number_edit = QLineEdit()
        self.remittance_number_edit.setReadOnly(True)
        self.remittance_number_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً")
        layout.addWidget(self.remittance_number_edit, 0, 1)
        
        # العملة
        layout.addWidget(QLabel("العملة:"), 0, 2)
        self.currency_combo = QComboBox()
        layout.addWidget(self.currency_combo, 0, 3)
        
        # إجمالي المبلغ
        layout.addWidget(QLabel("إجمالي المبلغ:"), 1, 0)
        self.total_amount_spin = QDoubleSpinBox()
        self.total_amount_spin.setRange(0.001, 999999999.999)
        self.total_amount_spin.setDecimals(3)
        self.total_amount_spin.setSuffix(" ")
        layout.addWidget(self.total_amount_spin, 1, 1)
        
        # سعر الصرف
        layout.addWidget(QLabel("سعر الصرف:"), 1, 2)
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.000001, 9999.999999)
        self.exchange_rate_spin.setDecimals(6)
        self.exchange_rate_spin.setValue(1.0)
        layout.addWidget(self.exchange_rate_spin, 1, 3)
        
        # تاريخ الحوالة
        layout.addWidget(QLabel("تاريخ الحوالة:"), 2, 0)
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        layout.addWidget(self.date_edit, 2, 1)
        
        # البنك المرسل
        layout.addWidget(QLabel("البنك المرسل:"), 2, 2)
        self.sender_bank_combo = QComboBox()
        layout.addWidget(self.sender_bank_combo, 2, 3)
        
        # حساب المرسل
        layout.addWidget(QLabel("حساب المرسل:"), 3, 0)
        self.sender_account_combo = QComboBox()
        layout.addWidget(self.sender_account_combo, 3, 1)
        
        # الغرض العام
        layout.addWidget(QLabel("الغرض العام:"), 3, 2)
        self.general_purpose_edit = QLineEdit()
        self.general_purpose_edit.setPlaceholderText("الغرض العام من الحوالة...")
        layout.addWidget(self.general_purpose_edit, 3, 3)
        
        # ملاحظات عامة
        layout.addWidget(QLabel("ملاحظات عامة:"), 4, 0)
        self.general_notes_edit = QTextEdit()
        self.general_notes_edit.setMaximumHeight(60)
        self.general_notes_edit.setPlaceholderText("ملاحظات عامة للحوالة...")
        layout.addWidget(self.general_notes_edit, 4, 1, 1, 3)
        
    def create_suppliers_section(self):
        """إنشاء قسم الموردين"""
        self.suppliers_group = QGroupBox("👥 توزيع الحوالة على الموردين")
        layout = QVBoxLayout(self.suppliers_group)
        
        # شريط أدوات الموردين
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        self.add_supplier_btn = QPushButton("➕ إضافة مورد")
        self.add_supplier_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #2ecc71);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2ecc71, stop:1 #27ae60);
            }
        """)
        
        self.remove_supplier_btn = QPushButton("➖ حذف مورد")
        self.remove_supplier_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
        """)
        
        self.distribute_equally_btn = QPushButton("⚖️ توزيع متساوي")
        self.distribute_equally_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
        """)
        
        self.calculate_percentages_btn = QPushButton("📊 حساب النسب")
        self.calculate_percentages_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f39c12, stop:1 #e67e22);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e67e22, stop:1 #d35400);
            }
        """)
        
        toolbar_layout.addWidget(self.add_supplier_btn)
        toolbar_layout.addWidget(self.remove_supplier_btn)
        toolbar_layout.addWidget(self.distribute_equally_btn)
        toolbar_layout.addWidget(self.calculate_percentages_btn)
        toolbar_layout.addStretch()
        
        layout.addWidget(toolbar_frame)
        
        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(8)
        self.suppliers_table.setHorizontalHeaderLabels([
            "المورد", "المبلغ", "النسبة %", "البنك المستقبل", 
            "حساب المستقبل", "الغرض", "ملاحظات", "حذف"
        ])
        
        # إعداد الجدول
        header = self.suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)

        # إعداد ارتفاع الصفوف
        vertical_header = self.suppliers_table.verticalHeader()
        vertical_header.setDefaultSectionSize(38)  # ارتفاع الصف 38 بكسل

        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setMinimumHeight(200)
        
        layout.addWidget(self.suppliers_table)
        
    def create_summary_section(self):
        """إنشاء قسم الملخص"""
        self.summary_group = QGroupBox("📊 ملخص الحوالة")
        layout = QGridLayout(self.summary_group)
        
        # إجمالي الموردين
        layout.addWidget(QLabel("عدد الموردين:"), 0, 0)
        self.total_suppliers_label = QLabel("0")
        self.total_suppliers_label.setStyleSheet("font-weight: bold; color: #2980b9;")
        layout.addWidget(self.total_suppliers_label, 0, 1)
        
        # إجمالي المبلغ الموزع
        layout.addWidget(QLabel("إجمالي المبلغ الموزع:"), 0, 2)
        self.distributed_amount_label = QLabel("0.000")
        self.distributed_amount_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        layout.addWidget(self.distributed_amount_label, 0, 3)
        
        # المبلغ المتبقي
        layout.addWidget(QLabel("المبلغ المتبقي:"), 1, 0)
        self.remaining_amount_label = QLabel("0.000")
        self.remaining_amount_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        layout.addWidget(self.remaining_amount_label, 1, 1)
        
        # إجمالي النسب
        layout.addWidget(QLabel("إجمالي النسب:"), 1, 2)
        self.total_percentage_label = QLabel("0.00%")
        self.total_percentage_label.setStyleSheet("font-weight: bold; color: #8e44ad;")
        layout.addWidget(self.total_percentage_label, 1, 3)
        
        # مؤشر صحة التوزيع
        self.distribution_status_label = QLabel("⚠️ يرجى إضافة موردين وتوزيع المبلغ")
        self.distribution_status_label.setAlignment(Qt.AlignCenter)
        self.distribution_status_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                border-radius: 6px;
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                color: #856404;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.distribution_status_label, 2, 0, 1, 4)
        
    def create_dialog_buttons(self):
        """إنشاء أزرار الحوار"""
        self.buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(self.buttons_frame)
        
        # زر الحفظ
        self.save_btn = QPushButton("💾 حفظ الحوالة")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #2ecc71);
                border: none;
                border-radius: 8px;
                color: white;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2ecc71, stop:1 #27ae60);
            }
            QPushButton:disabled {
                background: #95a5a6;
                color: #ecf0f1;
            }
        """)
        self.save_btn.setEnabled(False)
        
        # زر الإلغاء
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #95a5a6, stop:1 #7f8c8d);
                border: none;
                border-radius: 8px;
                color: white;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7f8c8d, stop:1 #6c7b7d);
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)

    def setup_connections(self):
        """إعداد الاتصالات"""
        # أزرار الموردين
        self.add_supplier_btn.clicked.connect(self.add_supplier_row)
        self.remove_supplier_btn.clicked.connect(self.remove_supplier_row)
        self.distribute_equally_btn.clicked.connect(self.distribute_equally)
        self.calculate_percentages_btn.clicked.connect(self.calculate_percentages)

        # تغيير إجمالي المبلغ
        self.total_amount_spin.valueChanged.connect(self.update_summary)

        # تغيير العملة
        self.currency_combo.currentTextChanged.connect(self.update_currency_suffix)

        # تغيير البنك المرسل
        self.sender_bank_combo.currentTextChanged.connect(self.load_sender_accounts)

        # أزرار الحوار
        self.save_btn.clicked.connect(self.save_remittance)
        self.cancel_btn.clicked.connect(self.reject)

    def load_data(self):
        """تحميل البيانات"""
        try:
            self.load_currencies()
            self.load_banks()
            self.generate_remittance_number()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}")

    def load_currencies(self):
        """تحميل قائمة العملات"""
        try:
            session = self.db_manager.get_session()
            currencies = session.query(Currency).filter(Currency.is_active == True).all()

            self.currency_combo.clear()
            for currency in currencies:
                self.currency_combo.addItem(f"{currency.code} - {currency.name}", currency.id)

            # تعيين الريال السعودي كافتراضي
            sar_index = self.currency_combo.findText("SAR", Qt.MatchContains)
            if sar_index >= 0:
                self.currency_combo.setCurrentIndex(sar_index)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل العملات:\n{str(e)}")

    def load_banks(self):
        """تحميل قائمة البنوك"""
        try:
            session = self.db_manager.get_session()
            banks = session.query(Bank).filter(Bank.is_active == True).all()

            self.sender_bank_combo.clear()
            self.sender_bank_combo.addItem("اختر البنك المرسل...", None)

            for bank in banks:
                self.sender_bank_combo.addItem(f"{bank.code} - {bank.name}", bank.id)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل البنوك:\n{str(e)}")

    def load_sender_accounts(self):
        """تحميل حسابات البنك المرسل"""
        try:
            bank_id = self.sender_bank_combo.currentData()

            self.sender_account_combo.clear()
            self.sender_account_combo.addItem("اختر الحساب...", None)

            if bank_id:
                session = self.db_manager.get_session()
                accounts = session.query(BankAccount).filter(
                    BankAccount.bank_id == bank_id,
                    BankAccount.is_active == True
                ).all()

                for account in accounts:
                    display_text = f"{account.account_number} - {account.account_name}"
                    if account.currency:
                        display_text += f" ({account.currency.code})"
                    self.sender_account_combo.addItem(display_text, account.id)

                session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل حسابات البنك:\n{str(e)}")

    def generate_remittance_number(self):
        """إنشاء رقم الحوالة التلقائي"""
        try:
            session = self.db_manager.get_session()

            from datetime import datetime
            current_year = datetime.now().year

            # البحث عن آخر رقم حوالة في السنة الحالية
            last_remittance = session.query(Remittance).filter(
                Remittance.remittance_number.like(f"REM-{current_year}-%")
            ).order_by(Remittance.id.desc()).first()

            if last_remittance and last_remittance.remittance_number:
                # استخراج الرقم التسلسلي
                parts = last_remittance.remittance_number.split('-')
                if len(parts) >= 3:
                    try:
                        last_number = int(parts[2])
                        new_number = last_number + 1
                    except ValueError:
                        new_number = 1
                else:
                    new_number = 1
            else:
                new_number = 1

            # إنشاء رقم الحوالة الجديد
            remittance_number = f"REM-{current_year}-{new_number:06d}"
            self.remittance_number_edit.setText(remittance_number)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إنشاء رقم الحوالة:\n{str(e)}")
            self.remittance_number_edit.setText(f"REM-{datetime.now().year}-000001")

    def update_currency_suffix(self):
        """تحديث لاحقة العملة"""
        currency_text = self.currency_combo.currentText()
        if currency_text and " - " in currency_text:
            currency_code = currency_text.split(" - ")[0]
            self.total_amount_spin.setSuffix(f" {currency_code}")
        else:
            self.total_amount_spin.setSuffix(" ")

    def add_supplier_row(self):
        """إضافة صف مورد جديد"""
        try:
            # إنشاء نافذة اختيار المورد
            supplier_dialog = SupplierSelectionDialog(self)
            if supplier_dialog.exec() == QDialog.Accepted:
                supplier_data = supplier_dialog.get_selected_supplier()
                if supplier_data:
                    self.add_supplier_to_table(supplier_data)
                    self.update_summary()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة المورد:\n{str(e)}")

    def add_supplier_to_table(self, supplier_data):
        """إضافة مورد إلى الجدول"""
        row = self.suppliers_table.rowCount()
        self.suppliers_table.insertRow(row)

        # المورد
        supplier_item = QTableWidgetItem(f"{supplier_data['code']} - {supplier_data['name']}")
        supplier_item.setData(Qt.UserRole, supplier_data['id'])
        self.suppliers_table.setItem(row, 0, supplier_item)

        # المبلغ
        amount_spin = QDoubleSpinBox()
        amount_spin.setRange(0.001, 999999999.999)
        amount_spin.setDecimals(3)
        amount_spin.valueChanged.connect(self.update_summary)
        self.suppliers_table.setCellWidget(row, 1, amount_spin)

        # النسبة
        percentage_spin = QDoubleSpinBox()
        percentage_spin.setRange(0.01, 100.00)
        percentage_spin.setDecimals(2)
        percentage_spin.setSuffix("%")
        percentage_spin.valueChanged.connect(self.update_amount_from_percentage)
        self.suppliers_table.setCellWidget(row, 2, percentage_spin)

        # البنك المستقبل
        bank_combo = QComboBox()
        self.load_banks_for_combo(bank_combo)
        bank_combo.currentTextChanged.connect(lambda: self.load_accounts_for_supplier(row))
        self.suppliers_table.setCellWidget(row, 3, bank_combo)

        # حساب المستقبل
        account_combo = QComboBox()
        account_combo.addItem("اختر الحساب...", None)
        self.suppliers_table.setCellWidget(row, 4, account_combo)

        # الغرض
        purpose_edit = QLineEdit()
        purpose_edit.setPlaceholderText("الغرض من التحويل...")
        self.suppliers_table.setItem(row, 5, QTableWidgetItem(""))
        self.suppliers_table.setCellWidget(row, 5, purpose_edit)

        # ملاحظات
        notes_edit = QLineEdit()
        notes_edit.setPlaceholderText("ملاحظات...")
        self.suppliers_table.setItem(row, 6, QTableWidgetItem(""))
        self.suppliers_table.setCellWidget(row, 6, notes_edit)

        # زر الحذف
        delete_btn = QPushButton("🗑️")
        delete_btn.setStyleSheet("""
            QPushButton {
                background: #e74c3c;
                border: none;
                border-radius: 4px;
                color: white;
                padding: 4px;
            }
            QPushButton:hover {
                background: #c0392b;
            }
        """)
        delete_btn.clicked.connect(lambda: self.remove_supplier_row_by_button(row))
        self.suppliers_table.setCellWidget(row, 7, delete_btn)

    def load_banks_for_combo(self, combo):
        """تحميل البنوك في كومبو بوكس"""
        try:
            session = self.db_manager.get_session()
            banks = session.query(Bank).filter(Bank.is_active == True).all()

            combo.clear()
            combo.addItem("اختر البنك...", None)

            for bank in banks:
                combo.addItem(f"{bank.code} - {bank.name}", bank.id)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل البنوك:\n{str(e)}")

    def load_accounts_for_supplier(self, row):
        """تحميل حسابات البنك للمورد"""
        try:
            bank_combo = self.suppliers_table.cellWidget(row, 3)
            account_combo = self.suppliers_table.cellWidget(row, 4)

            if not bank_combo or not account_combo:
                return

            bank_id = bank_combo.currentData()

            account_combo.clear()
            account_combo.addItem("اختر الحساب...", None)

            if bank_id:
                session = self.db_manager.get_session()
                accounts = session.query(BankAccount).filter(
                    BankAccount.bank_id == bank_id,
                    BankAccount.is_active == True
                ).all()

                for account in accounts:
                    display_text = f"{account.account_number} - {account.account_name}"
                    if account.currency:
                        display_text += f" ({account.currency.code})"
                    account_combo.addItem(display_text, account.id)

                session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل حسابات البنك:\n{str(e)}")

    def remove_supplier_row(self):
        """حذف صف المورد المحدد"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            self.suppliers_table.removeRow(current_row)
            self.update_summary()

    def remove_supplier_row_by_button(self, row):
        """حذف صف المورد بواسطة الزر"""
        if row < self.suppliers_table.rowCount():
            self.suppliers_table.removeRow(row)
            self.update_summary()

    def distribute_equally(self):
        """توزيع المبلغ بالتساوي على جميع الموردين"""
        total_amount = self.total_amount_spin.value()
        row_count = self.suppliers_table.rowCount()

        if row_count == 0:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة موردين أولاً")
            return

        if total_amount <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال إجمالي المبلغ أولاً")
            return

        amount_per_supplier = total_amount / row_count
        percentage_per_supplier = 100.0 / row_count

        for row in range(row_count):
            # تحديث المبلغ
            amount_spin = self.suppliers_table.cellWidget(row, 1)
            if amount_spin:
                amount_spin.setValue(amount_per_supplier)

            # تحديث النسبة
            percentage_spin = self.suppliers_table.cellWidget(row, 2)
            if percentage_spin:
                percentage_spin.setValue(percentage_per_supplier)

        self.update_summary()

    def calculate_percentages(self):
        """حساب النسب المئوية بناءً على المبالغ"""
        total_amount = self.total_amount_spin.value()

        if total_amount <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال إجمالي المبلغ أولاً")
            return

        for row in range(self.suppliers_table.rowCount()):
            amount_spin = self.suppliers_table.cellWidget(row, 1)
            percentage_spin = self.suppliers_table.cellWidget(row, 2)

            if amount_spin and percentage_spin:
                amount = amount_spin.value()
                percentage = (amount / total_amount) * 100 if total_amount > 0 else 0
                percentage_spin.setValue(percentage)

        self.update_summary()

    def update_amount_from_percentage(self):
        """تحديث المبلغ بناءً على النسبة المئوية"""
        total_amount = self.total_amount_spin.value()

        if total_amount <= 0:
            return

        sender = self.sender()
        if not isinstance(sender, QDoubleSpinBox):
            return

        # العثور على الصف الذي يحتوي على هذا العنصر
        for row in range(self.suppliers_table.rowCount()):
            percentage_spin = self.suppliers_table.cellWidget(row, 2)
            if percentage_spin == sender:
                amount_spin = self.suppliers_table.cellWidget(row, 1)
                if amount_spin:
                    percentage = percentage_spin.value()
                    amount = (percentage / 100) * total_amount
                    amount_spin.blockSignals(True)
                    amount_spin.setValue(amount)
                    amount_spin.blockSignals(False)
                break

        self.update_summary()

    def update_summary(self):
        """تحديث ملخص الحوالة"""
        try:
            total_suppliers = self.suppliers_table.rowCount()
            distributed_amount = 0.0
            total_percentage = 0.0

            # حساب المبلغ الموزع والنسب
            for row in range(total_suppliers):
                amount_spin = self.suppliers_table.cellWidget(row, 1)
                percentage_spin = self.suppliers_table.cellWidget(row, 2)

                if amount_spin:
                    distributed_amount += amount_spin.value()

                if percentage_spin:
                    total_percentage += percentage_spin.value()

            # تحديث التسميات
            self.total_suppliers_label.setText(str(total_suppliers))
            self.distributed_amount_label.setText(f"{distributed_amount:,.3f}")

            total_amount = self.total_amount_spin.value()
            remaining_amount = total_amount - distributed_amount
            self.remaining_amount_label.setText(f"{remaining_amount:,.3f}")

            self.total_percentage_label.setText(f"{total_percentage:.2f}%")

            # تحديث مؤشر صحة التوزيع
            if total_suppliers == 0:
                self.distribution_status_label.setText("⚠️ يرجى إضافة موردين وتوزيع المبلغ")
                self.distribution_status_label.setStyleSheet("""
                    QLabel {
                        padding: 10px; border-radius: 6px; background: #fff3cd;
                        border: 1px solid #ffeaa7; color: #856404; font-weight: bold;
                    }
                """)
                self.save_btn.setEnabled(False)
            elif abs(remaining_amount) < 0.001 and abs(total_percentage - 100.0) < 0.01:
                self.distribution_status_label.setText("✅ التوزيع صحيح ومتوازن")
                self.distribution_status_label.setStyleSheet("""
                    QLabel {
                        padding: 10px; border-radius: 6px; background: #d4edda;
                        border: 1px solid #c3e6cb; color: #155724; font-weight: bold;
                    }
                """)
                self.save_btn.setEnabled(True)
            else:
                self.distribution_status_label.setText("⚠️ التوزيع غير متوازن - يرجى المراجعة")
                self.distribution_status_label.setStyleSheet("""
                    QLabel {
                        padding: 10px; border-radius: 6px; background: #f8d7da;
                        border: 1px solid #f5c6cb; color: #721c24; font-weight: bold;
                    }
                """)
                self.save_btn.setEnabled(False)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحديث الملخص:\n{str(e)}")

    def save_remittance(self):
        """حفظ الحوالة"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_data():
                return

            session = self.db_manager.get_session()

            # إنشاء الحوالة الرئيسية
            remittance = Remittance(
                remittance_number=self.remittance_number_edit.text(),
                currency_id=self.currency_combo.currentData(),
                amount=self.total_amount_spin.value(),
                exchange_rate=self.exchange_rate_spin.value(),
                amount_in_base_currency=self.total_amount_spin.value() * self.exchange_rate_spin.value(),
                sender_bank_id=self.sender_bank_combo.currentData(),
                sender_account_id=self.sender_account_combo.currentData(),
                remittance_date=self.date_edit.date().toPython(),
                purpose=self.general_purpose_edit.text(),
                notes=self.general_notes_edit.toPlainText(),
                status=RemittanceStatus.DRAFT,
                created_by=1  # TODO: استخدام المستخدم الحالي
            )

            session.add(remittance)
            session.flush()  # للحصول على ID الحوالة

            # إضافة تفاصيل الموردين
            for row in range(self.suppliers_table.rowCount()):
                supplier_item = self.suppliers_table.item(row, 0)
                amount_spin = self.suppliers_table.cellWidget(row, 1)
                percentage_spin = self.suppliers_table.cellWidget(row, 2)
                bank_combo = self.suppliers_table.cellWidget(row, 3)
                account_combo = self.suppliers_table.cellWidget(row, 4)
                purpose_edit = self.suppliers_table.cellWidget(row, 5)
                notes_edit = self.suppliers_table.cellWidget(row, 6)

                if supplier_item and amount_spin:
                    detail = RemittanceDetail(
                        remittance_id=remittance.id,
                        supplier_id=supplier_item.data(Qt.UserRole),
                        amount=amount_spin.value(),
                        percentage=percentage_spin.value() if percentage_spin else 0,
                        purpose=purpose_edit.text() if purpose_edit else "",
                        notes=notes_edit.text() if notes_edit else "",
                        receiver_bank_id=bank_combo.currentData() if bank_combo else None,
                        receiver_account_id=account_combo.currentData() if account_combo else None,
                        created_by=1  # TODO: استخدام المستخدم الحالي
                    )
                    session.add(detail)

            session.commit()

            # إرسال إشارة النجاح
            self.remittance_created.emit({
                'id': remittance.id,
                'remittance_number': remittance.remittance_number,
                'amount': float(remittance.amount),
                'suppliers_count': self.suppliers_table.rowCount()
            })

            QMessageBox.information(self, "نجح", "تم إنشاء الحوالة متعددة الموردين بنجاح!")
            self.accept()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الحوالة:\n{str(e)}")
        finally:
            session.close()

    def validate_data(self):
        """التحقق من صحة البيانات"""
        # التحقق من وجود موردين
        if self.suppliers_table.rowCount() == 0:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة مورد واحد على الأقل")
            return False

        # التحقق من إجمالي المبلغ
        if self.total_amount_spin.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال إجمالي المبلغ")
            return False

        # التحقق من العملة
        if not self.currency_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار العملة")
            return False

        # التحقق من البنك المرسل
        if not self.sender_bank_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار البنك المرسل")
            return False

        # التحقق من حساب المرسل
        if not self.sender_account_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حساب المرسل")
            return False

        # التحقق من توزيع المبلغ
        total_amount = self.total_amount_spin.value()
        distributed_amount = 0.0

        for row in range(self.suppliers_table.rowCount()):
            amount_spin = self.suppliers_table.cellWidget(row, 1)
            if amount_spin:
                distributed_amount += amount_spin.value()

        if abs(total_amount - distributed_amount) > 0.001:
            QMessageBox.warning(self, "تحذير", "إجمالي المبلغ الموزع لا يساوي إجمالي المبلغ")
            return False

        return True


class SupplierSelectionDialog(QDialog):
    """نافذة اختيار المورد"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.selected_supplier = None
        self.setup_ui()
        self.load_suppliers()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختيار المورد")
        self.setModal(True)
        self.resize(600, 400)

        layout = QVBoxLayout(self)

        # عنوان
        title_label = QLabel("👥 اختيار المورد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;")
        layout.addWidget(title_label)

        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(4)
        self.suppliers_table.setHorizontalHeaderLabels(["الكود", "الاسم", "الهاتف", "البريد الإلكتروني"])

        header = self.suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)

        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.doubleClicked.connect(self.accept)

        layout.addWidget(self.suppliers_table)

        # أزرار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            session = self.db_manager.get_session()
            suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()

            self.suppliers_table.setRowCount(len(suppliers))

            for row, supplier in enumerate(suppliers):
                self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier.code or ""))
                self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier.name or ""))
                self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier.phone or ""))
                self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier.email or ""))

                # حفظ بيانات المورد
                self.suppliers_table.item(row, 0).setData(Qt.UserRole, {
                    'id': supplier.id,
                    'code': supplier.code,
                    'name': supplier.name,
                    'phone': supplier.phone,
                    'email': supplier.email
                })

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الموردين:\n{str(e)}")

    def get_selected_supplier(self):
        """الحصول على المورد المحدد"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            item = self.suppliers_table.item(current_row, 0)
            if item:
                return item.data(Qt.UserRole)
        return None
