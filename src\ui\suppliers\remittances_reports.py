# -*- coding: utf-8 -*-
"""
ويدجت التقارير المالية للحوالات
Remittances Financial Reports Widget
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QLineEdit, QComboBox, QDateEdit,
                               QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
                               QGroupBox, QFormLayout, QMessageBox, QDialog, QDialogButtonBox,
                               QSplitter, QFrame, QTextEdit, QTabWidget, QProgressBar,
                               QFileDialog, QCheckBox)
from PySide6.QtCore import Qt, Signal, QDate, QThread
from PySide6.QtGui import QFont, QIcon, QPalette, QColor, QPainter
from PySide6.QtCharts import QChart, QChartView, QPieSeries, QBarSeries, QBarSet, QLineSeries

from ...database.database_manager import DatabaseManager
from ...database.models import (Remittance, SupplierTransaction, SupplierAccount, 
                                Supplier, Currency, RemittanceStatus, TransactionType)


class RemittancesReportsWidget(QWidget):
    """ويدجت التقارير المالية للحوالات"""
    
    # الإشارات
    report_generated = Signal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        self.create_toolbar()
        layout.addWidget(self.toolbar_frame)
        
        # تبويبات التقارير
        self.reports_tab = QTabWidget()
        layout.addWidget(self.reports_tab)
        
        # تبويب التقارير السريعة
        self.create_quick_reports_tab()
        self.reports_tab.addTab(self.quick_reports_widget, "📊 التقارير السريعة")
        
        # تبويب التقارير المفصلة
        self.create_detailed_reports_tab()
        self.reports_tab.addTab(self.detailed_reports_widget, "📋 التقارير المفصلة")
        
        # تبويب الرسوم البيانية
        self.create_charts_tab()
        self.reports_tab.addTab(self.charts_widget, "📈 الرسوم البيانية")
        
        # تبويب التقارير المخصصة
        self.create_custom_reports_tab()
        self.reports_tab.addTab(self.custom_reports_widget, "⚙️ التقارير المخصصة")
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar_frame = QFrame()
        self.toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_layout = QHBoxLayout(self.toolbar_frame)
        
        # أزرار التقارير السريعة
        self.daily_report_btn = QPushButton("📅 تقرير يومي")
        self.daily_report_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e7e34, stop:1 #155724);
            }
        """)
        
        self.monthly_report_btn = QPushButton("📆 تقرير شهري")
        self.monthly_report_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0056b3, stop:1 #004085);
            }
        """)
        
        self.supplier_report_btn = QPushButton("👥 تقرير الموردين")
        self.supplier_report_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #17a2b8, stop:1 #138496);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #138496, stop:1 #117a8b);
            }
        """)
        
        self.export_btn = QPushButton("📤 تصدير")
        self.export_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffc107, stop:1 #e0a800);
                border: none;
                border-radius: 6px;
                color: #212529;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e0a800, stop:1 #d39e00);
            }
        """)
        
        # إضافة الأزرار
        toolbar_layout.addWidget(self.daily_report_btn)
        toolbar_layout.addWidget(self.monthly_report_btn)
        toolbar_layout.addWidget(self.supplier_report_btn)
        toolbar_layout.addWidget(self.export_btn)
        toolbar_layout.addStretch()
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        toolbar_layout.addWidget(self.progress_bar)
        
    def create_quick_reports_tab(self):
        """إنشاء تبويب التقارير السريعة"""
        self.quick_reports_widget = QWidget()
        layout = QVBoxLayout(self.quick_reports_widget)
        
        # إحصائيات سريعة
        stats_group = QGroupBox("📊 الإحصائيات السريعة")
        stats_layout = QGridLayout(stats_group)
        
        # إحصائيات اليوم
        today_group = QGroupBox("📅 إحصائيات اليوم")
        today_layout = QGridLayout(today_group)
        
        self.today_remittances_label = QLabel("الحوالات: 0")
        self.today_amount_label = QLabel("المبلغ: 0.000")
        self.today_completed_label = QLabel("المكتملة: 0")
        self.today_pending_label = QLabel("في الانتظار: 0")
        
        today_layout.addWidget(self.today_remittances_label, 0, 0)
        today_layout.addWidget(self.today_amount_label, 0, 1)
        today_layout.addWidget(self.today_completed_label, 1, 0)
        today_layout.addWidget(self.today_pending_label, 1, 1)
        
        # إحصائيات الشهر
        month_group = QGroupBox("📆 إحصائيات الشهر")
        month_layout = QGridLayout(month_group)
        
        self.month_remittances_label = QLabel("الحوالات: 0")
        self.month_amount_label = QLabel("المبلغ: 0.000")
        self.month_suppliers_label = QLabel("الموردين: 0")
        self.month_avg_label = QLabel("المتوسط: 0.000")
        
        month_layout.addWidget(self.month_remittances_label, 0, 0)
        month_layout.addWidget(self.month_amount_label, 0, 1)
        month_layout.addWidget(self.month_suppliers_label, 1, 0)
        month_layout.addWidget(self.month_avg_label, 1, 1)
        
        # إحصائيات السنة
        year_group = QGroupBox("📈 إحصائيات السنة")
        year_layout = QGridLayout(year_group)
        
        self.year_remittances_label = QLabel("الحوالات: 0")
        self.year_amount_label = QLabel("المبلغ: 0.000")
        self.year_growth_label = QLabel("النمو: 0%")
        self.year_best_month_label = QLabel("أفضل شهر: -")
        
        year_layout.addWidget(self.year_remittances_label, 0, 0)
        year_layout.addWidget(self.year_amount_label, 0, 1)
        year_layout.addWidget(self.year_growth_label, 1, 0)
        year_layout.addWidget(self.year_best_month_label, 1, 1)
        
        stats_layout.addWidget(today_group, 0, 0)
        stats_layout.addWidget(month_group, 0, 1)
        stats_layout.addWidget(year_group, 1, 0, 1, 2)
        
        layout.addWidget(stats_group)
        
        # أهم الموردين
        top_suppliers_group = QGroupBox("🏆 أهم الموردين")
        top_suppliers_layout = QVBoxLayout(top_suppliers_group)
        
        self.top_suppliers_table = QTableWidget()
        self.top_suppliers_table.setColumnCount(4)
        self.top_suppliers_table.setHorizontalHeaderLabels([
            "المورد", "عدد الحوالات", "إجمالي المبلغ", "متوسط الحوالة"
        ])
        
        header = self.top_suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.top_suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.top_suppliers_table.setAlternatingRowColors(True)
        self.top_suppliers_table.setMaximumHeight(200)
        
        top_suppliers_layout.addWidget(self.top_suppliers_table)
        layout.addWidget(top_suppliers_group)
        
    def create_detailed_reports_tab(self):
        """إنشاء تبويب التقارير المفصلة"""
        self.detailed_reports_widget = QWidget()
        layout = QVBoxLayout(self.detailed_reports_widget)
        
        # معايير التقرير
        criteria_group = QGroupBox("🔍 معايير التقرير")
        criteria_layout = QFormLayout(criteria_group)
        
        # نوع التقرير
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "تقرير الحوالات", "تقرير المعاملات", "تقرير الأرصدة",
            "تقرير الموردين", "تقرير العملات", "تقرير الحالات"
        ])
        criteria_layout.addRow("نوع التقرير:", self.report_type_combo)
        
        # الفترة الزمنية
        self.date_from_edit = QDateEdit()
        self.date_from_edit.setDate(QDate.currentDate().addDays(-30))
        self.date_from_edit.setCalendarPopup(True)
        criteria_layout.addRow("من تاريخ:", self.date_from_edit)
        
        self.date_to_edit = QDateEdit()
        self.date_to_edit.setDate(QDate.currentDate())
        self.date_to_edit.setCalendarPopup(True)
        criteria_layout.addRow("إلى تاريخ:", self.date_to_edit)
        
        # المورد
        self.supplier_filter_combo = QComboBox()
        self.supplier_filter_combo.addItem("جميع الموردين", None)
        criteria_layout.addRow("المورد:", self.supplier_filter_combo)
        
        # العملة
        self.currency_filter_combo = QComboBox()
        self.currency_filter_combo.addItem("جميع العملات", None)
        criteria_layout.addRow("العملة:", self.currency_filter_combo)
        
        # الحالة
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItem("جميع الحالات", None)
        for status in RemittanceStatus:
            self.status_filter_combo.addItem(status.value, status.name)
        criteria_layout.addRow("الحالة:", self.status_filter_combo)
        
        # زر إنشاء التقرير
        self.generate_report_btn = QPushButton("📊 إنشاء التقرير")
        self.generate_report_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e7e34, stop:1 #155724);
            }
        """)
        criteria_layout.addRow("", self.generate_report_btn)
        
        layout.addWidget(criteria_group)
        
        # نتائج التقرير
        results_group = QGroupBox("📋 نتائج التقرير")
        results_layout = QVBoxLayout(results_group)
        
        # ملخص التقرير
        summary_frame = QFrame()
        summary_layout = QHBoxLayout(summary_frame)
        
        self.report_summary_label = QLabel("لم يتم إنشاء تقرير بعد")
        self.report_summary_label.setStyleSheet("font-weight: bold; color: #6c757d;")
        summary_layout.addWidget(self.report_summary_label)
        summary_layout.addStretch()
        
        results_layout.addWidget(summary_frame)
        
        # جدول النتائج
        self.results_table = QTableWidget()
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSortingEnabled(True)
        
        header = self.results_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        results_layout.addWidget(self.results_table)
        layout.addWidget(results_group)
        
    def create_charts_tab(self):
        """إنشاء تبويب الرسوم البيانية"""
        self.charts_widget = QWidget()
        layout = QVBoxLayout(self.charts_widget)
        
        # أزرار الرسوم البيانية
        charts_toolbar = QFrame()
        charts_toolbar_layout = QHBoxLayout(charts_toolbar)
        
        self.pie_chart_btn = QPushButton("🥧 رسم دائري")
        self.bar_chart_btn = QPushButton("📊 رسم بياني")
        self.line_chart_btn = QPushButton("📈 رسم خطي")
        self.refresh_charts_btn = QPushButton("🔄 تحديث")
        
        charts_toolbar_layout.addWidget(self.pie_chart_btn)
        charts_toolbar_layout.addWidget(self.bar_chart_btn)
        charts_toolbar_layout.addWidget(self.line_chart_btn)
        charts_toolbar_layout.addWidget(self.refresh_charts_btn)
        charts_toolbar_layout.addStretch()
        
        layout.addWidget(charts_toolbar)
        
        # منطقة الرسوم البيانية
        self.chart_view = QChartView()
        self.chart_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(self.chart_view)
        
    def create_custom_reports_tab(self):
        """إنشاء تبويب التقارير المخصصة"""
        self.custom_reports_widget = QWidget()
        layout = QVBoxLayout(self.custom_reports_widget)
        
        # إعدادات التقرير المخصص
        custom_group = QGroupBox("⚙️ إعدادات التقرير المخصص")
        custom_layout = QFormLayout(custom_group)
        
        # اسم التقرير
        self.custom_name_edit = QLineEdit()
        self.custom_name_edit.setPlaceholderText("اسم التقرير المخصص...")
        custom_layout.addRow("اسم التقرير:", self.custom_name_edit)
        
        # الحقول المطلوبة
        fields_group = QGroupBox("الحقول المطلوبة")
        fields_layout = QGridLayout(fields_group)
        
        self.include_remittance_number = QCheckBox("رقم الحوالة")
        self.include_supplier = QCheckBox("المورد")
        self.include_amount = QCheckBox("المبلغ")
        self.include_currency = QCheckBox("العملة")
        self.include_date = QCheckBox("التاريخ")
        self.include_status = QCheckBox("الحالة")
        self.include_banks = QCheckBox("البنوك")
        self.include_purpose = QCheckBox("الغرض")
        
        # تحديد الحقول الافتراضية
        self.include_remittance_number.setChecked(True)
        self.include_supplier.setChecked(True)
        self.include_amount.setChecked(True)
        self.include_date.setChecked(True)
        self.include_status.setChecked(True)
        
        fields_layout.addWidget(self.include_remittance_number, 0, 0)
        fields_layout.addWidget(self.include_supplier, 0, 1)
        fields_layout.addWidget(self.include_amount, 0, 2)
        fields_layout.addWidget(self.include_currency, 0, 3)
        fields_layout.addWidget(self.include_date, 1, 0)
        fields_layout.addWidget(self.include_status, 1, 1)
        fields_layout.addWidget(self.include_banks, 1, 2)
        fields_layout.addWidget(self.include_purpose, 1, 3)
        
        custom_layout.addRow(fields_group)
        
        # أزرار التقرير المخصص
        custom_buttons_frame = QFrame()
        custom_buttons_layout = QHBoxLayout(custom_buttons_frame)
        
        self.save_template_btn = QPushButton("💾 حفظ القالب")
        self.load_template_btn = QPushButton("📂 تحميل قالب")
        self.generate_custom_btn = QPushButton("📊 إنشاء التقرير")
        
        custom_buttons_layout.addWidget(self.save_template_btn)
        custom_buttons_layout.addWidget(self.load_template_btn)
        custom_buttons_layout.addWidget(self.generate_custom_btn)
        custom_buttons_layout.addStretch()
        
        custom_layout.addRow(custom_buttons_frame)
        
        layout.addWidget(custom_group)
        layout.addStretch()
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        # أزرار التقارير السريعة
        self.daily_report_btn.clicked.connect(self.generate_daily_report)
        self.monthly_report_btn.clicked.connect(self.generate_monthly_report)
        self.supplier_report_btn.clicked.connect(self.generate_supplier_report)
        self.export_btn.clicked.connect(self.export_report)
        
        # التقارير المفصلة
        self.generate_report_btn.clicked.connect(self.generate_detailed_report)
        
        # الرسوم البيانية
        self.pie_chart_btn.clicked.connect(self.show_pie_chart)
        self.bar_chart_btn.clicked.connect(self.show_bar_chart)
        self.line_chart_btn.clicked.connect(self.show_line_chart)
        self.refresh_charts_btn.clicked.connect(self.refresh_charts)
        
        # التقارير المخصصة
        self.save_template_btn.clicked.connect(self.save_template)
        self.load_template_btn.clicked.connect(self.load_template)
        self.generate_custom_btn.clicked.connect(self.generate_custom_report)
        
    def load_data(self):
        """تحميل البيانات"""
        try:
            self.load_suppliers()
            self.load_currencies()
            self.load_quick_statistics()
            self.load_top_suppliers()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            session = self.db_manager.get_session()
            suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()

            self.supplier_filter_combo.clear()
            self.supplier_filter_combo.addItem("جميع الموردين", None)

            for supplier in suppliers:
                self.supplier_filter_combo.addItem(f"{supplier.code} - {supplier.name}", supplier.id)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الموردين:\n{str(e)}")

    def load_currencies(self):
        """تحميل قائمة العملات"""
        try:
            session = self.db_manager.get_session()
            currencies = session.query(Currency).filter(Currency.is_active == True).all()

            self.currency_filter_combo.clear()
            self.currency_filter_combo.addItem("جميع العملات", None)

            for currency in currencies:
                self.currency_filter_combo.addItem(f"{currency.code} - {currency.name}", currency.id)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل العملات:\n{str(e)}")

    def load_quick_statistics(self):
        """تحميل الإحصائيات السريعة"""
        try:
            session = self.db_manager.get_session()

            from datetime import datetime, timedelta
            now = datetime.now()

            # إحصائيات اليوم
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            today_remittances = session.query(Remittance).filter(
                Remittance.remittance_date >= today_start.date()
            ).all()

            today_count = len(today_remittances)
            today_amount = sum([float(r.amount or 0) for r in today_remittances])
            today_completed = len([r for r in today_remittances if r.status == RemittanceStatus.COMPLETED])
            today_pending = len([r for r in today_remittances if r.status == RemittanceStatus.PENDING])

            self.today_remittances_label.setText(f"الحوالات: {today_count}")
            self.today_amount_label.setText(f"المبلغ: {today_amount:,.3f}")
            self.today_completed_label.setText(f"المكتملة: {today_completed}")
            self.today_pending_label.setText(f"في الانتظار: {today_pending}")

            # إحصائيات الشهر
            month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            month_remittances = session.query(Remittance).filter(
                Remittance.remittance_date >= month_start.date()
            ).all()

            month_count = len(month_remittances)
            month_amount = sum([float(r.amount or 0) for r in month_remittances])
            month_suppliers = len(set([r.supplier_id for r in month_remittances if r.supplier_id]))
            month_avg = month_amount / month_count if month_count > 0 else 0

            self.month_remittances_label.setText(f"الحوالات: {month_count}")
            self.month_amount_label.setText(f"المبلغ: {month_amount:,.3f}")
            self.month_suppliers_label.setText(f"الموردين: {month_suppliers}")
            self.month_avg_label.setText(f"المتوسط: {month_avg:,.3f}")

            # إحصائيات السنة
            year_start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            year_remittances = session.query(Remittance).filter(
                Remittance.remittance_date >= year_start.date()
            ).all()

            year_count = len(year_remittances)
            year_amount = sum([float(r.amount or 0) for r in year_remittances])

            # حساب النمو مقارنة بالعام السابق
            last_year_start = year_start.replace(year=year_start.year - 1)
            last_year_end = year_start
            last_year_remittances = session.query(Remittance).filter(
                Remittance.remittance_date >= last_year_start.date(),
                Remittance.remittance_date < last_year_end.date()
            ).all()

            last_year_amount = sum([float(r.amount or 0) for r in last_year_remittances])
            growth_rate = ((year_amount - last_year_amount) / last_year_amount * 100) if last_year_amount > 0 else 0

            # أفضل شهر
            monthly_amounts = {}
            for r in year_remittances:
                if r.remittance_date:
                    month_key = r.remittance_date.strftime("%Y-%m")
                    monthly_amounts[month_key] = monthly_amounts.get(month_key, 0) + float(r.amount or 0)

            best_month = max(monthly_amounts.items(), key=lambda x: x[1])[0] if monthly_amounts else "-"

            self.year_remittances_label.setText(f"الحوالات: {year_count}")
            self.year_amount_label.setText(f"المبلغ: {year_amount:,.3f}")
            self.year_growth_label.setText(f"النمو: {growth_rate:+.1f}%")
            self.year_best_month_label.setText(f"أفضل شهر: {best_month}")

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الإحصائيات:\n{str(e)}")

    def load_top_suppliers(self):
        """تحميل أهم الموردين"""
        try:
            session = self.db_manager.get_session()

            # حساب إحصائيات الموردين
            suppliers_stats = {}
            remittances = session.query(Remittance).filter(
                Remittance.status != RemittanceStatus.CANCELLED
            ).all()

            for remittance in remittances:
                if remittance.supplier_id:
                    supplier_id = remittance.supplier_id
                    if supplier_id not in suppliers_stats:
                        suppliers_stats[supplier_id] = {
                            'name': remittance.supplier.name if remittance.supplier else 'غير محدد',
                            'count': 0,
                            'total_amount': 0
                        }

                    suppliers_stats[supplier_id]['count'] += 1
                    suppliers_stats[supplier_id]['total_amount'] += float(remittance.amount or 0)

            # ترتيب الموردين حسب إجمالي المبلغ
            sorted_suppliers = sorted(suppliers_stats.items(),
                                    key=lambda x: x[1]['total_amount'],
                                    reverse=True)[:10]  # أفضل 10 موردين

            self.top_suppliers_table.setRowCount(len(sorted_suppliers))

            for row, (supplier_id, stats) in enumerate(sorted_suppliers):
                # اسم المورد
                self.top_suppliers_table.setItem(row, 0, QTableWidgetItem(stats['name']))

                # عدد الحوالات
                count_item = QTableWidgetItem(str(stats['count']))
                count_item.setTextAlignment(Qt.AlignCenter)
                self.top_suppliers_table.setItem(row, 1, count_item)

                # إجمالي المبلغ
                total_item = QTableWidgetItem(f"{stats['total_amount']:,.3f}")
                total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.top_suppliers_table.setItem(row, 2, total_item)

                # متوسط الحوالة
                avg_amount = stats['total_amount'] / stats['count'] if stats['count'] > 0 else 0
                avg_item = QTableWidgetItem(f"{avg_amount:,.3f}")
                avg_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.top_suppliers_table.setItem(row, 3, avg_item)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل أهم الموردين:\n{str(e)}")

    def generate_daily_report(self):
        """إنشاء تقرير يومي"""
        try:
            self.show_progress(True, 0)

            session = self.db_manager.get_session()

            from datetime import datetime
            today = datetime.now().date()

            remittances = session.query(Remittance).filter(
                Remittance.remittance_date == today
            ).all()

            # إعداد الجدول
            self.results_table.setColumnCount(6)
            self.results_table.setHorizontalHeaderLabels([
                "رقم الحوالة", "المورد", "المبلغ", "العملة", "الحالة", "البنك المرسل"
            ])

            self.results_table.setRowCount(len(remittances))

            total_amount = 0

            for row, remittance in enumerate(remittances):
                self.results_table.setItem(row, 0, QTableWidgetItem(remittance.remittance_number or ""))
                self.results_table.setItem(row, 1, QTableWidgetItem(remittance.supplier.name if remittance.supplier else ""))

                amount_item = QTableWidgetItem(f"{remittance.amount:.3f}")
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.results_table.setItem(row, 2, amount_item)

                self.results_table.setItem(row, 3, QTableWidgetItem(remittance.currency.code if remittance.currency else ""))
                self.results_table.setItem(row, 4, QTableWidgetItem(remittance.status.value if remittance.status else ""))
                self.results_table.setItem(row, 5, QTableWidgetItem(remittance.sender_bank.name if remittance.sender_bank else ""))

                total_amount += float(remittance.amount or 0)

                self.show_progress(True, int((row + 1) / len(remittances) * 100))

            # تحديث الملخص
            self.report_summary_label.setText(
                f"تقرير يومي - {today} | "
                f"عدد الحوالات: {len(remittances)} | "
                f"إجمالي المبلغ: {total_amount:,.3f}"
            )

            # التبديل إلى تبويب التقارير المفصلة
            self.reports_tab.setCurrentIndex(1)

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقرير اليومي:\n{str(e)}")
        finally:
            self.show_progress(False)

    def generate_monthly_report(self):
        """إنشاء تقرير شهري"""
        try:
            self.show_progress(True, 0)

            session = self.db_manager.get_session()

            from datetime import datetime
            now = datetime.now()
            month_start = now.replace(day=1).date()

            remittances = session.query(Remittance).filter(
                Remittance.remittance_date >= month_start
            ).all()

            # إعداد الجدول
            self.results_table.setColumnCount(7)
            self.results_table.setHorizontalHeaderLabels([
                "رقم الحوالة", "المورد", "المبلغ", "العملة", "التاريخ", "الحالة", "البنك المرسل"
            ])

            self.results_table.setRowCount(len(remittances))

            total_amount = 0

            for row, remittance in enumerate(remittances):
                self.results_table.setItem(row, 0, QTableWidgetItem(remittance.remittance_number or ""))
                self.results_table.setItem(row, 1, QTableWidgetItem(remittance.supplier.name if remittance.supplier else ""))

                amount_item = QTableWidgetItem(f"{remittance.amount:.3f}")
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.results_table.setItem(row, 2, amount_item)

                self.results_table.setItem(row, 3, QTableWidgetItem(remittance.currency.code if remittance.currency else ""))

                date_str = remittance.remittance_date.strftime("%Y-%m-%d") if remittance.remittance_date else ""
                self.results_table.setItem(row, 4, QTableWidgetItem(date_str))

                self.results_table.setItem(row, 5, QTableWidgetItem(remittance.status.value if remittance.status else ""))
                self.results_table.setItem(row, 6, QTableWidgetItem(remittance.sender_bank.name if remittance.sender_bank else ""))

                total_amount += float(remittance.amount or 0)

                self.show_progress(True, int((row + 1) / len(remittances) * 100))

            # تحديث الملخص
            month_name = now.strftime("%Y-%m")
            self.report_summary_label.setText(
                f"تقرير شهري - {month_name} | "
                f"عدد الحوالات: {len(remittances)} | "
                f"إجمالي المبلغ: {total_amount:,.3f}"
            )

            # التبديل إلى تبويب التقارير المفصلة
            self.reports_tab.setCurrentIndex(1)

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقرير الشهري:\n{str(e)}")
        finally:
            self.show_progress(False)

    def generate_supplier_report(self):
        """إنشاء تقرير الموردين"""
        try:
            self.show_progress(True, 0)

            session = self.db_manager.get_session()

            # حساب إحصائيات الموردين
            suppliers_stats = {}
            remittances = session.query(Remittance).filter(
                Remittance.status != RemittanceStatus.CANCELLED
            ).all()

            for i, remittance in enumerate(remittances):
                if remittance.supplier_id:
                    supplier_id = remittance.supplier_id
                    if supplier_id not in suppliers_stats:
                        suppliers_stats[supplier_id] = {
                            'name': remittance.supplier.name if remittance.supplier else 'غير محدد',
                            'code': remittance.supplier.code if remittance.supplier else '',
                            'count': 0,
                            'total_amount': 0,
                            'completed': 0,
                            'pending': 0
                        }

                    suppliers_stats[supplier_id]['count'] += 1
                    suppliers_stats[supplier_id]['total_amount'] += float(remittance.amount or 0)

                    if remittance.status == RemittanceStatus.COMPLETED:
                        suppliers_stats[supplier_id]['completed'] += 1
                    elif remittance.status == RemittanceStatus.PENDING:
                        suppliers_stats[supplier_id]['pending'] += 1

                self.show_progress(True, int((i + 1) / len(remittances) * 50))

            # إعداد الجدول
            self.results_table.setColumnCount(7)
            self.results_table.setHorizontalHeaderLabels([
                "كود المورد", "اسم المورد", "عدد الحوالات", "إجمالي المبلغ",
                "المكتملة", "في الانتظار", "متوسط الحوالة"
            ])

            sorted_suppliers = sorted(suppliers_stats.items(),
                                    key=lambda x: x[1]['total_amount'],
                                    reverse=True)

            self.results_table.setRowCount(len(sorted_suppliers))

            for row, (supplier_id, stats) in enumerate(sorted_suppliers):
                self.results_table.setItem(row, 0, QTableWidgetItem(stats['code']))
                self.results_table.setItem(row, 1, QTableWidgetItem(stats['name']))

                count_item = QTableWidgetItem(str(stats['count']))
                count_item.setTextAlignment(Qt.AlignCenter)
                self.results_table.setItem(row, 2, count_item)

                total_item = QTableWidgetItem(f"{stats['total_amount']:,.3f}")
                total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.results_table.setItem(row, 3, total_item)

                completed_item = QTableWidgetItem(str(stats['completed']))
                completed_item.setTextAlignment(Qt.AlignCenter)
                self.results_table.setItem(row, 4, completed_item)

                pending_item = QTableWidgetItem(str(stats['pending']))
                pending_item.setTextAlignment(Qt.AlignCenter)
                self.results_table.setItem(row, 5, pending_item)

                avg_amount = stats['total_amount'] / stats['count'] if stats['count'] > 0 else 0
                avg_item = QTableWidgetItem(f"{avg_amount:,.3f}")
                avg_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.results_table.setItem(row, 6, avg_item)

                self.show_progress(True, 50 + int((row + 1) / len(sorted_suppliers) * 50))

            # تحديث الملخص
            total_suppliers = len(sorted_suppliers)
            total_remittances = sum([stats['count'] for _, stats in sorted_suppliers])
            total_amount = sum([stats['total_amount'] for _, stats in sorted_suppliers])

            self.report_summary_label.setText(
                f"تقرير الموردين | "
                f"عدد الموردين: {total_suppliers} | "
                f"إجمالي الحوالات: {total_remittances} | "
                f"إجمالي المبلغ: {total_amount:,.3f}"
            )

            # التبديل إلى تبويب التقارير المفصلة
            self.reports_tab.setCurrentIndex(1)

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير الموردين:\n{str(e)}")
        finally:
            self.show_progress(False)

    def generate_detailed_report(self):
        """إنشاء تقرير مفصل حسب المعايير المحددة"""
        try:
            self.show_progress(True, 0)

            session = self.db_manager.get_session()

            # بناء الاستعلام حسب المعايير
            query = session.query(Remittance)

            # تصفية التاريخ
            date_from = self.date_from_edit.date().toPython()
            date_to = self.date_to_edit.date().toPython()
            query = query.filter(Remittance.remittance_date >= date_from)
            query = query.filter(Remittance.remittance_date <= date_to)

            # تصفية المورد
            supplier_id = self.supplier_filter_combo.currentData()
            if supplier_id:
                query = query.filter(Remittance.supplier_id == supplier_id)

            # تصفية العملة
            currency_id = self.currency_filter_combo.currentData()
            if currency_id:
                query = query.filter(Remittance.currency_id == currency_id)

            # تصفية الحالة
            status_filter = self.status_filter_combo.currentData()
            if status_filter:
                status_enum = getattr(RemittanceStatus, status_filter)
                query = query.filter(Remittance.status == status_enum)

            remittances = query.order_by(Remittance.created_at.desc()).all()

            # إعداد الجدول حسب نوع التقرير
            report_type = self.report_type_combo.currentText()

            if report_type == "تقرير الحوالات":
                self.setup_remittances_report_table(remittances)
            elif report_type == "تقرير المعاملات":
                self.setup_transactions_report_table(date_from, date_to, supplier_id)
            elif report_type == "تقرير الأرصدة":
                self.setup_balances_report_table(supplier_id, currency_id)
            else:
                self.setup_remittances_report_table(remittances)

            # تحديث الملخص
            total_amount = sum([float(r.amount or 0) for r in remittances])
            self.report_summary_label.setText(
                f"{report_type} | "
                f"الفترة: {date_from} إلى {date_to} | "
                f"عدد السجلات: {len(remittances)} | "
                f"إجمالي المبلغ: {total_amount:,.3f}"
            )

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقرير المفصل:\n{str(e)}")
        finally:
            self.show_progress(False)

    def setup_remittances_report_table(self, remittances):
        """إعداد جدول تقرير الحوالات"""
        self.results_table.setColumnCount(8)
        self.results_table.setHorizontalHeaderLabels([
            "رقم الحوالة", "المورد", "المبلغ", "العملة", "التاريخ",
            "الحالة", "البنك المرسل", "البنك المستقبل"
        ])

        self.results_table.setRowCount(len(remittances))

        for row, remittance in enumerate(remittances):
            self.results_table.setItem(row, 0, QTableWidgetItem(remittance.remittance_number or ""))
            self.results_table.setItem(row, 1, QTableWidgetItem(remittance.supplier.name if remittance.supplier else ""))

            amount_item = QTableWidgetItem(f"{remittance.amount:.3f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.results_table.setItem(row, 2, amount_item)

            self.results_table.setItem(row, 3, QTableWidgetItem(remittance.currency.code if remittance.currency else ""))

            date_str = remittance.remittance_date.strftime("%Y-%m-%d") if remittance.remittance_date else ""
            self.results_table.setItem(row, 4, QTableWidgetItem(date_str))

            self.results_table.setItem(row, 5, QTableWidgetItem(remittance.status.value if remittance.status else ""))
            self.results_table.setItem(row, 6, QTableWidgetItem(remittance.sender_bank.name if remittance.sender_bank else ""))
            self.results_table.setItem(row, 7, QTableWidgetItem(remittance.receiver_bank.name if remittance.receiver_bank else ""))

            self.show_progress(True, int((row + 1) / len(remittances) * 100))

    def setup_transactions_report_table(self, date_from, date_to, supplier_id):
        """إعداد جدول تقرير المعاملات"""
        session = self.db_manager.get_session()

        query = session.query(SupplierTransaction)
        query = query.filter(SupplierTransaction.transaction_date >= date_from)
        query = query.filter(SupplierTransaction.transaction_date <= date_to)

        if supplier_id:
            # الحصول على حسابات المورد
            accounts = session.query(SupplierAccount).filter(SupplierAccount.supplier_id == supplier_id).all()
            account_ids = [acc.id for acc in accounts]
            if account_ids:
                query = query.filter(SupplierTransaction.supplier_account_id.in_(account_ids))

        transactions = query.order_by(SupplierTransaction.created_at.desc()).all()

        self.results_table.setColumnCount(8)
        self.results_table.setHorizontalHeaderLabels([
            "رقم المعاملة", "المورد", "النوع", "المبلغ", "الرصيد قبل",
            "الرصيد بعد", "التاريخ", "الوصف"
        ])

        self.results_table.setRowCount(len(transactions))

        for row, transaction in enumerate(transactions):
            self.results_table.setItem(row, 0, QTableWidgetItem(transaction.transaction_number or ""))

            supplier_name = transaction.supplier_account.supplier.name if transaction.supplier_account and transaction.supplier_account.supplier else ""
            self.results_table.setItem(row, 1, QTableWidgetItem(supplier_name))

            self.results_table.setItem(row, 2, QTableWidgetItem(transaction.transaction_type.value if transaction.transaction_type else ""))

            amount_item = QTableWidgetItem(f"{transaction.amount:.3f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.results_table.setItem(row, 3, amount_item)

            balance_before_item = QTableWidgetItem(f"{transaction.balance_before:.3f}" if transaction.balance_before else "0.000")
            balance_before_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.results_table.setItem(row, 4, balance_before_item)

            balance_after_item = QTableWidgetItem(f"{transaction.balance_after:.3f}" if transaction.balance_after else "0.000")
            balance_after_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.results_table.setItem(row, 5, balance_after_item)

            date_str = transaction.transaction_date.strftime("%Y-%m-%d") if transaction.transaction_date else ""
            self.results_table.setItem(row, 6, QTableWidgetItem(date_str))

            self.results_table.setItem(row, 7, QTableWidgetItem(transaction.description or ""))

            self.show_progress(True, int((row + 1) / len(transactions) * 100))

        session.close()

    def setup_balances_report_table(self, supplier_id, currency_id):
        """إعداد جدول تقرير الأرصدة"""
        session = self.db_manager.get_session()

        query = session.query(SupplierAccount)

        if supplier_id:
            query = query.filter(SupplierAccount.supplier_id == supplier_id)

        if currency_id:
            query = query.filter(SupplierAccount.currency_id == currency_id)

        accounts = query.all()

        self.results_table.setColumnCount(7)
        self.results_table.setHorizontalHeaderLabels([
            "المورد", "العملة", "رقم الحساب", "الرصيد الحالي",
            "حد الائتمان", "الائتمان المتاح", "آخر معاملة"
        ])

        self.results_table.setRowCount(len(accounts))

        for row, account in enumerate(accounts):
            self.results_table.setItem(row, 0, QTableWidgetItem(account.supplier.name if account.supplier else ""))
            self.results_table.setItem(row, 1, QTableWidgetItem(account.currency.code if account.currency else ""))
            self.results_table.setItem(row, 2, QTableWidgetItem(account.account_number or ""))

            balance_item = QTableWidgetItem(f"{account.balance:.3f}" if account.balance else "0.000")
            balance_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.results_table.setItem(row, 3, balance_item)

            credit_item = QTableWidgetItem(f"{account.credit_limit:.3f}" if account.credit_limit else "0.000")
            credit_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.results_table.setItem(row, 4, credit_item)

            available_item = QTableWidgetItem(f"{account.available_credit:.3f}" if account.available_credit else "0.000")
            available_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.results_table.setItem(row, 5, available_item)

            last_transaction = account.last_transaction_date.strftime("%Y-%m-%d") if account.last_transaction_date else "-"
            self.results_table.setItem(row, 6, QTableWidgetItem(last_transaction))

            self.show_progress(True, int((row + 1) / len(accounts) * 100))

        session.close()

    def show_pie_chart(self):
        """عرض الرسم الدائري"""
        try:
            # إنشاء بيانات الرسم الدائري لحالات الحوالات
            session = self.db_manager.get_session()

            status_counts = {}
            remittances = session.query(Remittance).all()

            for remittance in remittances:
                status = remittance.status.value if remittance.status else "غير محدد"
                status_counts[status] = status_counts.get(status, 0) + 1

            # إنشاء الرسم الدائري
            series = QPieSeries()

            for status, count in status_counts.items():
                series.append(status, count)

            chart = QChart()
            chart.addSeries(series)
            chart.setTitle("توزيع الحوالات حسب الحالة")
            chart.legend().setAlignment(Qt.AlignBottom)

            self.chart_view.setChart(chart)

            # التبديل إلى تبويب الرسوم البيانية
            self.reports_tab.setCurrentIndex(2)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إنشاء الرسم الدائري:\n{str(e)}")

    def show_bar_chart(self):
        """عرض الرسم البياني"""
        try:
            # إنشاء بيانات الرسم البياني للمبالغ الشهرية
            session = self.db_manager.get_session()

            from datetime import datetime, timedelta
            now = datetime.now()

            monthly_amounts = {}

            # آخر 12 شهر
            for i in range(12):
                month_date = now.replace(day=1) - timedelta(days=30 * i)
                month_key = month_date.strftime("%Y-%m")
                monthly_amounts[month_key] = 0

            remittances = session.query(Remittance).filter(
                Remittance.remittance_date >= (now - timedelta(days=365)).date()
            ).all()

            for remittance in remittances:
                if remittance.remittance_date:
                    month_key = remittance.remittance_date.strftime("%Y-%m")
                    if month_key in monthly_amounts:
                        monthly_amounts[month_key] += float(remittance.amount or 0)

            # إنشاء الرسم البياني
            series = QBarSeries()
            bar_set = QBarSet("المبلغ")

            sorted_months = sorted(monthly_amounts.keys())
            for month in sorted_months:
                bar_set.append(monthly_amounts[month])

            series.append(bar_set)

            chart = QChart()
            chart.addSeries(series)
            chart.setTitle("المبالغ الشهرية للحوالات")
            chart.legend().setAlignment(Qt.AlignBottom)

            self.chart_view.setChart(chart)

            # التبديل إلى تبويب الرسوم البيانية
            self.reports_tab.setCurrentIndex(2)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إنشاء الرسم البياني:\n{str(e)}")

    def show_line_chart(self):
        """عرض الرسم الخطي"""
        try:
            # إنشاء بيانات الرسم الخطي لاتجاه الحوالات
            session = self.db_manager.get_session()

            from datetime import datetime, timedelta
            now = datetime.now()

            daily_counts = {}

            # آخر 30 يوم
            for i in range(30):
                day_date = (now - timedelta(days=i)).date()
                daily_counts[day_date] = 0

            remittances = session.query(Remittance).filter(
                Remittance.remittance_date >= (now - timedelta(days=30)).date()
            ).all()

            for remittance in remittances:
                if remittance.remittance_date and remittance.remittance_date in daily_counts:
                    daily_counts[remittance.remittance_date] += 1

            # إنشاء الرسم الخطي
            series = QLineSeries()

            sorted_dates = sorted(daily_counts.keys())
            for i, date in enumerate(sorted_dates):
                series.append(i, daily_counts[date])

            chart = QChart()
            chart.addSeries(series)
            chart.setTitle("اتجاه عدد الحوالات اليومية")
            chart.legend().setAlignment(Qt.AlignBottom)

            self.chart_view.setChart(chart)

            # التبديل إلى تبويب الرسوم البيانية
            self.reports_tab.setCurrentIndex(2)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إنشاء الرسم الخطي:\n{str(e)}")

    def refresh_charts(self):
        """تحديث الرسوم البيانية"""
        # إعادة تحميل البيانات وتحديث الرسوم
        self.load_quick_statistics()
        self.load_top_suppliers()

    def export_report(self):
        """تصدير التقرير"""
        try:
            # اختيار مسار الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "تصدير التقرير",
                f"تقرير_الحوالات_{QDate.currentDate().toString('yyyy-MM-dd')}.csv",
                "CSV Files (*.csv);;Excel Files (*.xlsx)"
            )

            if file_path:
                # تصدير بيانات الجدول
                self.export_table_data(file_path)
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير:\n{str(e)}")

    def export_table_data(self, file_path):
        """تصدير بيانات الجدول إلى ملف"""
        import csv

        with open(file_path, 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)

            # كتابة العناوين
            headers = []
            for col in range(self.results_table.columnCount()):
                headers.append(self.results_table.horizontalHeaderItem(col).text())
            writer.writerow(headers)

            # كتابة البيانات
            for row in range(self.results_table.rowCount()):
                row_data = []
                for col in range(self.results_table.columnCount()):
                    item = self.results_table.item(row, col)
                    row_data.append(item.text() if item else "")
                writer.writerow(row_data)

    def generate_custom_report(self):
        """إنشاء تقرير مخصص"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة التقارير المخصصة قيد التطوير")

    def save_template(self):
        """حفظ قالب التقرير"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حفظ القوالب قيد التطوير")

    def load_template(self):
        """تحميل قالب التقرير"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تحميل القوالب قيد التطوير")

    def show_progress(self, show=True, value=0):
        """إظهار أو إخفاء شريط التقدم"""
        self.progress_bar.setVisible(show)
        if show:
            self.progress_bar.setValue(value)

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
