# -*- coding: utf-8 -*-
"""
نافذة تتبع الشحنات
Shipment Tracking Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QMessageBox, QHeaderView, QAbstractItemView, QComboBox,
                               QGroupBox, QFormLayout, QDateEdit, QToolBar, QStatusBar,
                               QProgressBar, QFrame, QGridLayout, QTextEdit, QSizePolicy)
from PySide6.QtCore import Qt, QDate, QTimer, Signal
from PySide6.QtGui import QAction, QIcon, QFont, QColor

from ...database.database_manager import DatabaseManager
from ...database.models import Shipment, Supplier, Container, ShipmentItem
from ...utils.formatters import format_date, format_datetime, format_number, format_currency

class ShipmentTrackingWindow(QMainWindow):
    """نافذة تتبع الشحنات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.setup_toolbar()
        self.setup_connections()
        self.load_tracking_data()
        
        # تحديث تلقائي كل 30 ثانية
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_tracking_data)
        self.refresh_timer.start(30000)  # 30 ثانية
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تتبع الشحنات")
        # تحديد حجم مناسب للنافذة
        self.resize(1400, 900)  # حجم مناسب
        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)  # مسافات مناسبة بين العناصر
        main_layout.setContentsMargins(10, 10, 10, 10)  # هوامش مناسبة

        # إحصائيات سريعة - إعطاء مساحة ثابتة
        stats_frame = self.create_stats_frame()
        main_layout.addWidget(stats_frame, 0)  # stretch factor = 0 (مساحة ثابتة)

        # منطقة البحث والفلترة المحسنة
        search_group = QGroupBox("البحث والفلترة")
        search_group.setMinimumHeight(80)   # ارتفاع أقل للصف الواحد
        search_group.setMaximumHeight(100)  # ارتفاع أقصى مناسب
        search_layout = QHBoxLayout(search_group)  # تغيير إلى HBoxLayout للصف الواحد
        search_layout.setSpacing(15)
        search_layout.setContentsMargins(20, 15, 20, 15)

        # حقل البحث الفوري
        search_label = QLabel("البحث:")
        search_label.setMinimumWidth(50)
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث في جميع الأعمدة...")
        self.search_edit.setMinimumHeight(35)
        self.search_edit.setMinimumWidth(300)

        # إعداد البحث الفوري
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_instant_search)
        self.search_edit.textChanged.connect(self.on_search_text_changed)

        # فلترة بحالة الشحنة
        status_label = QLabel("حالة الشحنة:")
        status_label.setMinimumWidth(80)
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.setMinimumHeight(35)
        self.status_filter_combo.setMinimumWidth(150)
        self.status_filter_combo.addItem("جميع الحالات", "")
        self.status_filter_combo.addItems([
            "تحت الطلب", "مؤكدة", "تم الشحن", "في الطريق",
            "وصلت الميناء", "في الجمارك", "تم التسليم", "ملغية", "متاخرة"
        ])
        self.status_filter_combo.currentTextChanged.connect(self.perform_instant_search)

        # فلترة بحالة الإفراج الجديدة
        clearance_label = QLabel("حالة الإفراج:")
        clearance_label.setMinimumWidth(80)
        self.clearance_filter_combo = QComboBox()
        self.clearance_filter_combo.setMinimumHeight(35)
        self.clearance_filter_combo.setMinimumWidth(150)
        self.clearance_filter_combo.addItem("جميع حالات الإفراج", "")
        self.clearance_filter_combo.addItems([
            "بدون الافراج", "مع الافراج"
        ])
        self.clearance_filter_combo.currentTextChanged.connect(self.perform_instant_search)

        # زر مسح البحث
        self.clear_search_button = QPushButton("مسح")
        self.clear_search_button.setMinimumHeight(35)
        self.clear_search_button.setMinimumWidth(80)
        self.clear_search_button.clicked.connect(self.clear_search)

        # ترتيب العناصر في صف واحد
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(status_label)
        search_layout.addWidget(self.status_filter_combo)
        search_layout.addWidget(clearance_label)
        search_layout.addWidget(self.clearance_filter_combo)
        search_layout.addWidget(self.clear_search_button)
        search_layout.addStretch()  # مساحة مرنة في النهاية
        
        main_layout.addWidget(search_group, 0)  # stretch factor = 0 (مساحة ثابتة)
        
        # جدول تتبع الشحنات
        self.tracking_table = QTableWidget()
        self.tracking_table.setColumnCount(22)  # مطابقة النافذة الرئيسية
        self.tracking_table.setHorizontalHeaderLabels([
            "رقم الشحنة", "التاريخ", "المورد", "تفاصيل الأصناف", "الكمية", "عدد الحاويات", "رقم الحاوية",
            "حالة الشحنة", "حالة الإفراج", "بوليصة الشحن", "رقم التتبع", "رقم DHL", "شركة الشحن", "طريقة الشحن",
            "ميناء التحميل", "ميناء التفريغ", "ميناء الوصول", "الوجهة النهائية", "اسم السفينة", "رقم الرحلة",
            "تاريخ المغادرة", "تاريخ الوصول المتوقع"
        ])
        
        # إعداد الجدول - مطابقة النافذة الرئيسية
        header = self.tracking_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الشحنة
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # المورد - توسيع لإظهار الاسم كاملاً
        header.resizeSection(2, 200)  # توسيع عمود المورد إلى 200 بكسل
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # تفاصيل الأصناف (قابل للتمدد)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الكمية
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # عدد الحاويات
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # رقم الحاوية
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # حالة الشحنة
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # حالة الإفراج
        header.setSectionResizeMode(9, QHeaderView.ResizeToContents)  # بوليصة الشحن
        header.setSectionResizeMode(10, QHeaderView.ResizeToContents)  # رقم التتبع
        header.setSectionResizeMode(11, QHeaderView.ResizeToContents)  # رقم DHL
        header.setSectionResizeMode(12, QHeaderView.ResizeToContents)  # شركة الشحن
        header.setSectionResizeMode(13, QHeaderView.ResizeToContents)  # طريقة الشحن
        header.setSectionResizeMode(14, QHeaderView.ResizeToContents)  # ميناء التحميل
        header.setSectionResizeMode(15, QHeaderView.ResizeToContents)  # ميناء التفريغ
        header.setSectionResizeMode(16, QHeaderView.ResizeToContents)  # ميناء الوصول
        header.setSectionResizeMode(17, QHeaderView.ResizeToContents)  # الوجهة النهائية
        header.setSectionResizeMode(18, QHeaderView.ResizeToContents)  # اسم السفينة
        header.setSectionResizeMode(19, QHeaderView.ResizeToContents)  # رقم الرحلة
        header.setSectionResizeMode(20, QHeaderView.ResizeToContents)  # تاريخ المغادرة
        header.setSectionResizeMode(21, QHeaderView.ResizeToContents)  # تاريخ الوصول المتوقع
        
        self.tracking_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.tracking_table.setAlternatingRowColors(True)
        # جعل الجدول للقراءة فقط (غير قابل للتعديل)
        self.tracking_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        # تحسين مظهر الجدول
        self.tracking_table.setMinimumHeight(300)  # ارتفاع أدنى مناسب
        self.tracking_table.verticalHeader().setDefaultSectionSize(35)  # ارتفاع الصفوف
        
        # إعطاء الجدول المساحة الأكبر مع الحفاظ على توازن الشاشة
        main_layout.addWidget(self.tracking_table, 4)  # stretch factor = 4 (حوالي 50-60% من المساحة)

        # منطقة تفاصيل الشحنة المحددة
        details_group = QGroupBox("تفاصيل الشحنة")
        details_group.setMinimumHeight(120)  # ارتفاع أدنى مناسب
        details_group.setMaximumHeight(180)  # ارتفاع أقصى مناسب
        details_layout = QVBoxLayout(details_group)

        self.details_text = QTextEdit()
        self.details_text.setMinimumHeight(100)  # ارتفاع أدنى مناسب
        self.details_text.setMaximumHeight(150)  # ارتفاع أقصى مناسب
        self.details_text.setReadOnly(True)
        self.details_text.setFont(QFont("Arial", 10))  # خط مقروء
        details_layout.addWidget(self.details_text)

        main_layout.addWidget(details_group, 0)  # stretch factor = 0 (مساحة ثابتة)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
    def create_stats_frame(self):
        """إنشاء قسم الإحصائيات - توزيع متوازن لكامل المساحة"""
        # إطار رئيسي بسيط
        stats_frame = QFrame()
        stats_frame.setFixedHeight(300)
        stats_frame.setMinimumWidth(1000)  # عرض أكبر لاستغلال المساحة
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 8px;
            }
        """)

        # توزيع البطاقات بشكل متوازن عبر كامل العرض
        # الصف الأول - موزع من اليسار لليمين
        self.total_shipments_label = self.create_fixed_card(stats_frame, "إجمالي الشحنات", "0", "#2196F3", 80, 40)
        self.confirmed_label = self.create_fixed_card(stats_frame, "مؤكدة", "0", "#00BCD4", 320, 40)
        self.shipped_label = self.create_fixed_card(stats_frame, "تم الشحن", "0", "#3F51B5", 560, 40)

        # الصف الثاني - نفس التوزيع
        self.in_transit_label = self.create_fixed_card(stats_frame, "في الطريق", "0", "#FF9800", 80, 120)
        self.at_port_label = self.create_fixed_card(stats_frame, "في الميناء", "0", "#9C27B0", 320, 120)
        self.in_customs_label = self.create_fixed_card(stats_frame, "في الجمارك", "0", "#607D8B", 560, 120)

        # الصف الثالث - نفس التوزيع
        self.delivered_label = self.create_fixed_card(stats_frame, "تم التسليم", "0", "#4CAF50", 80, 200)
        self.delayed_label = self.create_fixed_card(stats_frame, "متأخرة", "0", "#F44336", 320, 200)
        self.cancelled_label = self.create_fixed_card(stats_frame, "ملغية", "0", "#795548", 560, 200)

        return stats_frame

    def create_fixed_card(self, parent, title, value, color, x, y):
        """إنشاء بطاقة بموضع ثابت مضمون"""
        card = QFrame(parent)
        card.setGeometry(x, y, 200, 70)  # موضع وحجم ثابت مطلقاً
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 3px solid {color};
                border-radius: 10px;
            }}
        """)

        # عنوان البطاقة
        title_label = QLabel(title, card)
        title_label.setGeometry(10, 10, 180, 20)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #333333;
                font-weight: bold;
                background: transparent;
                border: none;
            }
        """)

        # قيمة البطاقة
        value_label = QLabel(value, card)
        value_label.setGeometry(10, 35, 180, 25)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {color};
                background: transparent;
                border: none;
            }}
        """)

        # حفظ مرجع القيمة للتحديث
        card.value_label = value_label

        return card


        
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = QToolBar("أدوات التتبع")
        self.addToolBar(toolbar)
        
        # تحديث
        refresh_action = QAction("تحديث", self)
        refresh_action.setStatusTip("تحديث بيانات التتبع")
        refresh_action.triggered.connect(self.load_tracking_data)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # تصدير
        export_action = QAction("تصدير", self)
        export_action.setStatusTip("تصدير بيانات التتبع")
        export_action.triggered.connect(self.export_tracking_data)
        toolbar.addAction(export_action)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        # عرض التفاصيل عند تحديد شحنة
        self.tracking_table.itemSelectionChanged.connect(self.show_shipment_details)

    def on_search_text_changed(self):
        """استدعاء البحث الفوري عند تغيير النص"""
        self.search_timer.stop()
        self.search_timer.start(300)  # تأخير 300 مللي ثانية

    def perform_instant_search(self):
        """تنفيذ البحث الفوري في جميع الأعمدة"""
        search_text = self.search_edit.text().strip().lower()
        status_filter = self.status_filter_combo.currentText() if self.status_filter_combo.currentText() != "جميع الحالات" else ""
        clearance_filter = self.clearance_filter_combo.currentText() if self.clearance_filter_combo.currentText() != "جميع حالات الإفراج" else ""

        # إخفاء جميع الصفوف أولاً
        for row in range(self.tracking_table.rowCount()):
            self.tracking_table.setRowHidden(row, True)

        # إظهار الصفوف التي تطابق معايير البحث
        for row in range(self.tracking_table.rowCount()):
            show_row = True

            # فلترة بحالة الشحنة
            if status_filter:
                status_item = self.tracking_table.item(row, 7)  # عمود حالة الشحنة
                if not status_item or status_item.text() != status_filter:
                    show_row = False

            # فلترة بحالة الإفراج
            if clearance_filter and show_row:
                clearance_item = self.tracking_table.item(row, 8)  # عمود حالة الإفراج
                if not clearance_item or clearance_item.text() != clearance_filter:
                    show_row = False

            # البحث النصي في جميع الأعمدة
            if search_text and show_row:
                row_matches = False
                for col in range(self.tracking_table.columnCount()):
                    item = self.tracking_table.item(row, col)
                    if item and search_text in item.text().lower():
                        row_matches = True
                        break
                if not row_matches:
                    show_row = False

            self.tracking_table.setRowHidden(row, not show_row)

        # تحديث شريط الحالة
        visible_rows = sum(1 for row in range(self.tracking_table.rowCount()) if not self.tracking_table.isRowHidden(row))
        total_rows = self.tracking_table.rowCount()
        self.status_bar.showMessage(f"عرض {visible_rows} من أصل {total_rows} شحنة")

    def clear_search(self):
        """مسح البحث وإظهار جميع الشحنات"""
        self.search_edit.clear()
        self.status_filter_combo.setCurrentIndex(0)
        self.clearance_filter_combo.setCurrentIndex(0)

        # إظهار جميع الصفوف
        for row in range(self.tracking_table.rowCount()):
            self.tracking_table.setRowHidden(row, False)

        # تحديث شريط الحالة
        total_rows = self.tracking_table.rowCount()
        self.status_bar.showMessage(f"عرض جميع الشحنات ({total_rows})")

    def load_tracking_data(self):
        """تحميل بيانات التتبع"""
        session = self.db_manager.get_session()
        try:
            shipments = session.query(Shipment).join(Supplier).filter(
                Shipment.is_active == True
            ).order_by(Shipment.created_at.desc()).all()

            # تحميل بيانات الحاويات والأصناف لكل شحنة
            for shipment in shipments:
                shipment.containers = session.query(Container).filter(
                    Container.shipment_id == shipment.id
                ).all()
                shipment.items = session.query(ShipmentItem).filter(
                    ShipmentItem.shipment_id == shipment.id
                ).all()
            
            self.populate_tracking_table(shipments)
            self.update_statistics(shipments)
            self.status_bar.showMessage(f"تم تحديث بيانات {len(shipments)} شحنة - آخر تحديث: {QDate.currentDate().toString('yyyy-MM-dd')}")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات التتبع: {str(e)}")
        finally:
            session.close()
            
    def populate_tracking_table(self, shipments):
        """ملء جدول التتبع"""
        self.tracking_table.setRowCount(len(shipments))

        for row, shipment in enumerate(shipments):
            # رقم الشحنة
            shipment_number_item = QTableWidgetItem(shipment.shipment_number or "")
            shipment_number_item.setData(Qt.UserRole, shipment.id)
            self.tracking_table.setItem(row, 0, shipment_number_item)

            # التاريخ (تاريخ الشحنة)
            from ...utils.formatters import format_date
            date_text = format_date(shipment.shipment_date, 'short') if shipment.shipment_date else ""
            date_item = QTableWidgetItem(date_text)
            self.tracking_table.setItem(row, 1, date_item)

            # المورد (اسم كامل)
            supplier_name = shipment.supplier.name if shipment.supplier else ""
            supplier_item = QTableWidgetItem(supplier_name)
            self.tracking_table.setItem(row, 2, supplier_item)

            # تفاصيل الأصناف (أسماء الأصناف)
            item_names = []
            if hasattr(shipment, 'items') and shipment.items:
                for shipment_item in shipment.items:
                    if shipment_item.item and shipment_item.item.name:
                        item_names.append(shipment_item.item.name)
            items_text = ", ".join(item_names[:3])  # أول 3 أصناف فقط لتوفير المساحة
            if len(item_names) > 3:
                items_text += f" (+{len(item_names) - 3} أخرى)"
            items_item = QTableWidgetItem(items_text)
            items_item.setToolTip(", ".join(item_names))  # عرض جميع الأصناف في tooltip
            self.tracking_table.setItem(row, 3, items_item)

            # الكمية (إجمالي الكميات من الأصناف)
            total_quantity = 0
            if hasattr(shipment, 'items') and shipment.items:
                total_quantity = sum(item.quantity for item in shipment.items)
            quantity_item = QTableWidgetItem(f"{total_quantity:.2f}")
            self.tracking_table.setItem(row, 4, quantity_item)

            # عدد الحاويات
            containers_count = len(shipment.containers) if shipment.containers else 0
            containers_count_item = QTableWidgetItem(str(containers_count))
            self.tracking_table.setItem(row, 5, containers_count_item)

            # رقم الحاوية (من جدول الحاويات)
            container_numbers = []
            if hasattr(shipment, 'containers') and shipment.containers:
                container_numbers = [container.container_number for container in shipment.containers if container.container_number]
            container_text = ", ".join(container_numbers) if container_numbers else ""
            container_item = QTableWidgetItem(container_text)
            self.tracking_table.setItem(row, 6, container_item)

            # حالة الشحنة مع لون
            status_item = QTableWidgetItem(shipment.shipment_status or "")
            status_item.setBackground(self.get_status_color(shipment.shipment_status))
            self.tracking_table.setItem(row, 7, status_item)

            # حالة الإفراج
            clearance_item = QTableWidgetItem(shipment.clearance_status or "")
            self.tracking_table.setItem(row, 8, clearance_item)

            # بوليصة الشحن
            bl_item = QTableWidgetItem(shipment.bill_of_lading or "")
            self.tracking_table.setItem(row, 9, bl_item)

            # رقم التتبع
            tracking_item = QTableWidgetItem(shipment.tracking_number or "")
            self.tracking_table.setItem(row, 10, tracking_item)

            # رقم DHL
            dhl_item = QTableWidgetItem(shipment.dhl_number or "")
            self.tracking_table.setItem(row, 11, dhl_item)

            # شركة الشحن
            shipping_company_item = QTableWidgetItem(shipment.shipping_company or "")
            self.tracking_table.setItem(row, 12, shipping_company_item)

            # طريقة الشحن
            shipping_method_item = QTableWidgetItem(shipment.shipping_method or "")
            self.tracking_table.setItem(row, 13, shipping_method_item)

            # ميناء التحميل
            loading_port_item = QTableWidgetItem(shipment.port_of_loading or "")
            self.tracking_table.setItem(row, 14, loading_port_item)

            # ميناء التفريغ
            discharge_port_item = QTableWidgetItem(shipment.port_of_discharge or "")
            self.tracking_table.setItem(row, 15, discharge_port_item)

            # ميناء الوصول
            arrival_port_item = QTableWidgetItem(shipment.port_of_arrival or "")
            self.tracking_table.setItem(row, 16, arrival_port_item)

            # الوجهة النهائية
            destination_item = QTableWidgetItem(shipment.final_destination or "")
            self.tracking_table.setItem(row, 17, destination_item)

            # اسم السفينة
            vessel_item = QTableWidgetItem(shipment.vessel_name or "")
            self.tracking_table.setItem(row, 18, vessel_item)

            # رقم الرحلة
            voyage_item = QTableWidgetItem(shipment.voyage_number or "")
            self.tracking_table.setItem(row, 19, voyage_item)

            # تاريخ المغادرة - بتنسيق الويندوز
            departure_date = format_date(shipment.actual_departure_date, 'short') if shipment.actual_departure_date else ""
            departure_item = QTableWidgetItem(departure_date)
            self.tracking_table.setItem(row, 20, departure_item)

            # تاريخ الوصول المتوقع - بتنسيق الويندوز
            est_arrival = format_date(shipment.estimated_arrival_date, 'short') if shipment.estimated_arrival_date else ""
            est_arrival_item = QTableWidgetItem(est_arrival)
            self.tracking_table.setItem(row, 21, est_arrival_item)
            
    def get_status_color(self, status):
        """الحصول على لون الحالة"""
        colors = {
            "تحت الطلب": QColor("#FFF3E0"),
            "مؤكدة": QColor("#E3F2FD"),
            "تم الشحن": QColor("#E8F5E8"),
            "في الطريق": QColor("#FFF8E1"),
            "وصلت الميناء": QColor("#F3E5F5"),
            "في الجمارك": QColor("#ECEFF1"),
            "تم التسليم": QColor("#E8F5E8"),
            "ملغية": QColor("#FFEBEE"),
            "متاخرة": QColor("#FFCDD2")
        }
        return colors.get(status, QColor("#FFFFFF"))
        
    def calculate_progress(self, status):
        """حساب نسبة التقدم"""
        progress_map = {
            "تحت الطلب": 10,
            "مؤكدة": 20,
            "تم الشحن": 40,
            "في الطريق": 60,
            "وصلت الميناء": 80,
            "في الجمارك": 90,
            "تم التسليم": 100,
            "ملغية": 0,
            "متاخرة": 30
        }
        return progress_map.get(status, 0)
        
    def update_statistics(self, shipments):
        """تحديث الإحصائيات المحسنة - قسم 300 بكسل وبطاقات 70 بكسل"""
        total = len(shipments)
        confirmed = len([s for s in shipments if s.shipment_status == "مؤكدة"])
        shipped = len([s for s in shipments if s.shipment_status == "تم الشحن"])
        in_transit = len([s for s in shipments if s.shipment_status == "في الطريق"])
        at_port = len([s for s in shipments if s.shipment_status == "وصلت الميناء"])
        in_customs = len([s for s in shipments if s.shipment_status == "في الجمارك"])
        delivered = len([s for s in shipments if s.shipment_status == "تم التسليم"])
        delayed = len([s for s in shipments if s.shipment_status == "متاخرة"])
        cancelled = len([s for s in shipments if s.shipment_status == "ملغية"])

        # تحديث البطاقات الجديدة (3 صفوف × 3 أعمدة)
        self.total_shipments_label.value_label.setText(str(total))
        self.confirmed_label.value_label.setText(str(confirmed))
        self.shipped_label.value_label.setText(str(shipped))

        self.in_transit_label.value_label.setText(str(in_transit))
        self.at_port_label.value_label.setText(str(at_port))
        self.in_customs_label.value_label.setText(str(in_customs))

        self.delivered_label.value_label.setText(str(delivered))
        self.delayed_label.value_label.setText(str(delayed))
        self.cancelled_label.value_label.setText(str(cancelled))
        
    def show_shipment_details(self):
        """عرض تفاصيل الشحنة المحددة"""
        current_row = self.tracking_table.currentRow()
        if current_row >= 0:
            shipment_id = self.tracking_table.item(current_row, 0).data(Qt.UserRole)
            
            session = self.db_manager.get_session()
            try:
                shipment = session.query(Shipment).filter(Shipment.id == shipment_id).first()
                if shipment:
                    details = f"""
رقم الشحنة: {shipment.shipment_number}
المورد: {shipment.supplier.name if shipment.supplier else 'غير محدد'}
رقم فاتورة المورد: {shipment.supplier_invoice_number or 'غير محدد'}
حالة الشحنة: {shipment.shipment_status}
حالة الإفراج: {shipment.clearance_status}
رقم التتبع: {shipment.tracking_number or 'غير محدد'}
شركة الشحن: {shipment.shipping_company or 'غير محدد'}
اسم السفينة: {shipment.vessel_name or 'غير محدد'}
رقم الرحلة: {shipment.voyage_number or 'غير محدد'}
ميناء التحميل: {shipment.port_of_loading or 'غير محدد'}
ميناء التفريغ: {shipment.port_of_discharge or 'غير محدد'}
ملاحظات: {shipment.notes or 'لا توجد ملاحظات'}
                    """
                    self.details_text.setPlainText(details.strip())
            except Exception as e:
                self.details_text.setPlainText(f"خطأ في تحميل التفاصيل: {str(e)}")
            finally:
                session.close()
        else:
            self.details_text.clear()
            

        
    def export_tracking_data(self):
        """تصدير بيانات التتبع"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ ميزة التصدير لاحقاً")
        
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        self.refresh_timer.stop()
        event.accept()
