# -*- coding: utf-8 -*-
"""
نافذة إدارة حوالات الموردين الشاملة والمتقدمة
Comprehensive Supplier Remittances Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QGroupBox, QLabel, QPushButton, QTableWidget, QTableWidgetItem,
                               QComboBox, QDoubleSpinBox, QLineEdit, QTextEdit, QDateEdit,
                               QCheckBox, QMessageBox, QHeaderView, QSplitter, QFormLayout,
                               QFrame, QSpacerItem, QSizePolicy, QTabWidget, QTreeWidget,
                               QTreeWidgetItem, QProgressBar, QDialog, QDialogButtonBox)
from PySide6.QtCore import Qt, Signal, QDate, QTimer
from PySide6.QtGui import QFont, QIcon, QColor

from ...database.database_manager import DatabaseManager
from ...database.models import (Supplier, Currency, SupplierRemittance, 
                               SupplierRemittanceItem, SupplierAccount)
from datetime import datetime, date


class SupplierRemittancesWidget(QWidget):
    """ويدجت إدارة حوالات الموردين"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_remittance_id = None
        self.setup_ui()
        self.load_initial_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        title_label = QLabel("إدارة حوالات الموردين الشاملة والمتقدمة")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e8f5e8, stop:1 #d4edda);
                border: 1px solid #28a745;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء تبويبات رئيسية
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # تبويب إدارة الحوالات
        self.setup_remittances_tab()
        
        # تبويب حسابات الموردين
        self.setup_accounts_tab()
        
        # تبويب التقارير
        self.setup_reports_tab()
        
    def setup_remittances_tab(self):
        """إعداد تبويب إدارة الحوالات"""
        remittances_widget = QWidget()
        layout = QVBoxLayout(remittances_widget)
        
        # إنشاء Splitter للتقسيم
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # الجانب الأيسر - قائمة الحوالات
        self.setup_remittances_list(splitter)
        
        # الجانب الأيمن - تفاصيل الحوالة
        self.setup_remittance_details(splitter)
        
        # تعيين نسب التقسيم
        splitter.setSizes([600, 800])
        
        self.tab_widget.addTab(remittances_widget, "إدارة الحوالات")
        
    def setup_remittances_list(self, parent):
        """إعداد قائمة الحوالات"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # أزرار العمليات السريعة
        buttons_layout = QHBoxLayout()
        
        self.new_remittance_btn = QPushButton("حوالة جديدة")
        self.new_remittance_btn.setMinimumHeight(40)
        self.new_remittance_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #20c997);
                color: white;
                font-weight: bold;
                border-radius: 6px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #20c997, stop:1 #28a745);
            }
        """)
        self.new_remittance_btn.clicked.connect(self.create_new_remittance)
        buttons_layout.addWidget(self.new_remittance_btn)
        
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.setMinimumHeight(40)
        self.refresh_btn.clicked.connect(self.load_remittances)
        buttons_layout.addWidget(self.refresh_btn)
        
        buttons_layout.addStretch()
        left_layout.addLayout(buttons_layout)
        
        # مرشحات البحث
        filters_group = QGroupBox("مرشحات البحث")
        filters_layout = QFormLayout(filters_group)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "جميع الحالات", "مرسلة", "في الطريق", "وصلت للبنك", "مؤكدة", "مرحلة", "ملغاة"
        ])
        self.status_filter.currentTextChanged.connect(self.filter_remittances)
        filters_layout.addRow("الحالة:", self.status_filter)
        
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        filters_layout.addRow("من تاريخ:", self.date_from)
        
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        filters_layout.addRow("إلى تاريخ:", self.date_to)
        
        left_layout.addWidget(filters_group)
        
        # جدول الحوالات
        remittances_group = QGroupBox("قائمة الحوالات")
        remittances_layout = QVBoxLayout(remittances_group)
        
        self.remittances_table = QTableWidget()
        self.remittances_table.setColumnCount(6)
        self.remittances_table.setHorizontalHeaderLabels([
            "رقم الحوالة", "التاريخ", "المبلغ", "العملة", "الحالة", "عدد الموردين"
        ])
        
        # تعيين عرض الأعمدة
        header = self.remittances_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.Stretch)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        
        self.remittances_table.setAlternatingRowColors(True)
        self.remittances_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.remittances_table.itemSelectionChanged.connect(self.on_remittance_selected)
        
        remittances_layout.addWidget(self.remittances_table)
        left_layout.addWidget(remittances_group)
        
        parent.addWidget(left_widget)
        
    def setup_remittance_details(self, parent):
        """إعداد تفاصيل الحوالة"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # معلومات الحوالة الأساسية
        basic_info_group = QGroupBox("معلومات الحوالة الأساسية")
        basic_info_layout = QFormLayout(basic_info_group)
        
        self.remittance_number_edit = QLineEdit()
        self.remittance_number_edit.setReadOnly(True)
        basic_info_layout.addRow("رقم الحوالة:", self.remittance_number_edit)
        
        self.remittance_date_edit = QDateEdit()
        self.remittance_date_edit.setDate(QDate.currentDate())
        self.remittance_date_edit.setCalendarPopup(True)
        basic_info_layout.addRow("تاريخ الحوالة:", self.remittance_date_edit)
        
        self.total_amount_edit = QDoubleSpinBox()
        self.total_amount_edit.setMaximum(*********.99)
        self.total_amount_edit.setDecimals(2)
        self.total_amount_edit.setReadOnly(True)
        basic_info_layout.addRow("المبلغ الإجمالي:", self.total_amount_edit)
        
        self.currency_combo = QComboBox()
        basic_info_layout.addRow("العملة:", self.currency_combo)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "مرسلة", "في الطريق", "وصلت للبنك", "مؤكدة", "مرحلة", "ملغاة"
        ])
        self.status_combo.currentTextChanged.connect(self.on_status_changed)
        basic_info_layout.addRow("الحالة:", self.status_combo)
        
        right_layout.addWidget(basic_info_group)
        
        # معلومات البنوك
        banks_group = QGroupBox("معلومات البنوك")
        banks_layout = QFormLayout(banks_group)
        
        self.sender_bank_edit = QLineEdit()
        banks_layout.addRow("البنك المرسل:", self.sender_bank_edit)
        
        self.receiver_bank_edit = QLineEdit()
        banks_layout.addRow("البنك المستقبل:", self.receiver_bank_edit)
        
        self.swift_code_edit = QLineEdit()
        banks_layout.addRow("كود SWIFT:", self.swift_code_edit)
        
        right_layout.addWidget(banks_group)
        
        # تفاصيل الموردين في الحوالة
        suppliers_group = QGroupBox("الموردين في الحوالة")
        suppliers_layout = QVBoxLayout(suppliers_group)
        
        # أزرار إدارة الموردين
        suppliers_buttons_layout = QHBoxLayout()
        
        self.add_supplier_btn = QPushButton("إضافة مورد")
        self.add_supplier_btn.clicked.connect(self.add_supplier_to_remittance)
        suppliers_buttons_layout.addWidget(self.add_supplier_btn)
        
        self.remove_supplier_btn = QPushButton("إزالة مورد")
        self.remove_supplier_btn.clicked.connect(self.remove_supplier_from_remittance)
        self.remove_supplier_btn.setEnabled(False)
        suppliers_buttons_layout.addWidget(self.remove_supplier_btn)
        
        suppliers_buttons_layout.addStretch()
        suppliers_layout.addLayout(suppliers_buttons_layout)
        
        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(5)
        self.suppliers_table.setHorizontalHeaderLabels([
            "المورد", "المبلغ", "العملة", "الحالة", "تاريخ التأكيد"
        ])
        
        # تعيين عرض الأعمدة
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.itemSelectionChanged.connect(self.on_supplier_item_selected)
        
        suppliers_layout.addWidget(self.suppliers_table)
        right_layout.addWidget(suppliers_group)
        
        # أزرار العمليات
        operations_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setMinimumHeight(40)
        self.save_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                color: white;
                font-weight: bold;
                border-radius: 6px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0056b3, stop:1 #007bff);
            }
        """)
        self.save_btn.clicked.connect(self.save_remittance)
        operations_layout.addWidget(self.save_btn)
        
        self.confirm_btn = QPushButton("تأكيد الوصول")
        self.confirm_btn.setMinimumHeight(40)
        self.confirm_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #20c997);
                color: white;
                font-weight: bold;
                border-radius: 6px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #20c997, stop:1 #28a745);
            }
        """)
        self.confirm_btn.clicked.connect(self.confirm_remittance_receipt)
        self.confirm_btn.setEnabled(False)
        operations_layout.addWidget(self.confirm_btn)
        
        self.post_btn = QPushButton("ترحيل للحسابات")
        self.post_btn.setMinimumHeight(40)
        self.post_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #17a2b8, stop:1 #138496);
                color: white;
                font-weight: bold;
                border-radius: 6px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #138496, stop:1 #17a2b8);
            }
        """)
        self.post_btn.clicked.connect(self.post_remittance_to_accounts)
        self.post_btn.setEnabled(False)
        operations_layout.addWidget(self.post_btn)
        
        operations_layout.addStretch()
        right_layout.addLayout(operations_layout)
        
        parent.addWidget(right_widget)
        
    def setup_accounts_tab(self):
        """إعداد تبويب حسابات الموردين"""
        accounts_widget = QWidget()
        layout = QVBoxLayout(accounts_widget)
        
        # عنوان التبويب
        title_label = QLabel("حسابات الموردين")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # جدول حسابات الموردين
        self.accounts_table = QTableWidget()
        self.accounts_table.setColumnCount(8)
        self.accounts_table.setHorizontalHeaderLabels([
            "المورد", "العملة", "الرصيد الافتتاحي", "الرصيد الحالي", 
            "إجمالي المشتريات", "إجمالي الحوالات", "حد الائتمان", "آخر معاملة"
        ])
        
        # تعيين عرض الأعمدة
        header = self.accounts_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        for i in range(1, 8):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)
        
        self.accounts_table.setAlternatingRowColors(True)
        layout.addWidget(self.accounts_table)
        
        self.tab_widget.addTab(accounts_widget, "حسابات الموردين")
        
    def setup_reports_tab(self):
        """إعداد تبويب التقارير"""
        reports_widget = QWidget()
        layout = QVBoxLayout(reports_widget)
        
        # عنوان التبويب
        title_label = QLabel("تقارير الحوالات")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # مجموعة التقارير
        reports_group = QGroupBox("التقارير المتاحة")
        reports_layout = QVBoxLayout(reports_group)
        
        # أزرار التقارير
        reports_buttons_layout = QHBoxLayout()
        
        daily_report_btn = QPushButton("تقرير يومي")
        daily_report_btn.clicked.connect(self.generate_daily_report)
        reports_buttons_layout.addWidget(daily_report_btn)
        
        monthly_report_btn = QPushButton("تقرير شهري")
        monthly_report_btn.clicked.connect(self.generate_monthly_report)
        reports_buttons_layout.addWidget(monthly_report_btn)
        
        supplier_report_btn = QPushButton("تقرير حسب المورد")
        supplier_report_btn.clicked.connect(self.generate_supplier_report)
        reports_buttons_layout.addWidget(supplier_report_btn)
        
        reports_buttons_layout.addStretch()
        reports_layout.addLayout(reports_buttons_layout)
        
        # منطقة عرض التقارير
        self.reports_text = QTextEdit()
        self.reports_text.setReadOnly(True)
        reports_layout.addWidget(self.reports_text)
        
        layout.addWidget(reports_group)
        
        self.tab_widget.addTab(reports_widget, "التقارير")

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        self.load_currencies()
        self.load_remittances()
        self.load_accounts()

    def load_currencies(self):
        """تحميل قائمة العملات"""
        session = self.db_manager.get_session()
        try:
            currencies = session.query(Currency).filter_by(is_active=True).order_by(Currency.name).all()

            self.currency_combo.clear()
            for currency in currencies:
                display_text = f"{currency.name} ({currency.code})"
                self.currency_combo.addItem(display_text, currency.id)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في تحميل العملات:\n{str(e)}")
        finally:
            session.close()

    def load_remittances(self):
        """تحميل قائمة الحوالات"""
        session = self.db_manager.get_session()
        try:
            # تطبيق المرشحات
            query = session.query(SupplierRemittance).join(Currency)

            # مرشح الحالة
            status_filter = self.status_filter.currentText()
            if status_filter != "جميع الحالات":
                query = query.filter(SupplierRemittance.status == status_filter)

            # مرشح التاريخ
            date_from = self.date_from.date().toPython()
            date_to = self.date_to.date().toPython()
            query = query.filter(
                SupplierRemittance.remittance_date >= date_from,
                SupplierRemittance.remittance_date <= date_to
            )

            remittances = query.order_by(SupplierRemittance.remittance_date.desc()).all()

            self.remittances_table.setRowCount(len(remittances))

            for row, remittance in enumerate(remittances):
                # رقم الحوالة
                number_item = QTableWidgetItem(remittance.remittance_number)
                number_item.setData(Qt.UserRole, remittance.id)
                self.remittances_table.setItem(row, 0, number_item)

                # التاريخ
                date_text = remittance.remittance_date.strftime("%Y-%m-%d")
                self.remittances_table.setItem(row, 1, QTableWidgetItem(date_text))

                # المبلغ
                amount_text = f"{remittance.total_amount:,.2f}"
                self.remittances_table.setItem(row, 2, QTableWidgetItem(amount_text))

                # العملة
                self.remittances_table.setItem(row, 3, QTableWidgetItem(remittance.currency.code))

                # الحالة
                status_item = QTableWidgetItem(remittance.status)
                if remittance.status == "مؤكدة":
                    status_item.setBackground(QColor("#d4edda"))
                elif remittance.status == "مرحلة":
                    status_item.setBackground(QColor("#cce5ff"))
                elif remittance.status == "ملغاة":
                    status_item.setBackground(QColor("#f8d7da"))
                self.remittances_table.setItem(row, 4, status_item)

                # عدد الموردين
                suppliers_count = len(remittance.items)
                self.remittances_table.setItem(row, 5, QTableWidgetItem(str(suppliers_count)))

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في تحميل الحوالات:\n{str(e)}")
        finally:
            session.close()

    def load_accounts(self):
        """تحميل حسابات الموردين"""
        session = self.db_manager.get_session()
        try:
            accounts = session.query(SupplierAccount).join(Supplier).join(Currency).filter(
                SupplierAccount.is_active == True
            ).order_by(Supplier.name, Currency.name).all()

            self.accounts_table.setRowCount(len(accounts))

            for row, account in enumerate(accounts):
                # المورد
                self.accounts_table.setItem(row, 0, QTableWidgetItem(account.supplier.name))

                # العملة
                self.accounts_table.setItem(row, 1, QTableWidgetItem(account.currency.code))

                # الرصيد الافتتاحي
                opening_text = f"{account.opening_balance:,.2f}"
                self.accounts_table.setItem(row, 2, QTableWidgetItem(opening_text))

                # الرصيد الحالي
                current_text = f"{account.current_balance:,.2f}"
                current_item = QTableWidgetItem(current_text)
                if account.current_balance < 0:
                    current_item.setBackground(QColor("#f8d7da"))
                elif account.current_balance > 0:
                    current_item.setBackground(QColor("#d4edda"))
                self.accounts_table.setItem(row, 3, current_item)

                # إجمالي المشتريات
                purchases_text = f"{account.total_purchases:,.2f}"
                self.accounts_table.setItem(row, 4, QTableWidgetItem(purchases_text))

                # إجمالي الحوالات
                remittances_text = f"{account.total_remittances:,.2f}"
                self.accounts_table.setItem(row, 5, QTableWidgetItem(remittances_text))

                # حد الائتمان
                credit_text = f"{account.credit_limit:,.2f}"
                self.accounts_table.setItem(row, 6, QTableWidgetItem(credit_text))

                # آخر معاملة
                last_transaction = account.last_transaction_date.strftime("%Y-%m-%d") if account.last_transaction_date else "لا يوجد"
                self.accounts_table.setItem(row, 7, QTableWidgetItem(last_transaction))

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في تحميل الحسابات:\n{str(e)}")
        finally:
            session.close()

    def filter_remittances(self):
        """تطبيق مرشحات الحوالات"""
        self.load_remittances()

    def on_remittance_selected(self):
        """عند تحديد حوالة من القائمة"""
        current_row = self.remittances_table.currentRow()
        if current_row >= 0:
            remittance_id = self.remittances_table.item(current_row, 0).data(Qt.UserRole)
            self.load_remittance_details(remittance_id)

    def load_remittance_details(self, remittance_id):
        """تحميل تفاصيل الحوالة"""
        self.current_remittance_id = remittance_id

        session = self.db_manager.get_session()
        try:
            remittance = session.query(SupplierRemittance).get(remittance_id)
            if not remittance:
                return

            # تعبئة البيانات الأساسية
            self.remittance_number_edit.setText(remittance.remittance_number)
            self.remittance_date_edit.setDate(QDate.fromString(remittance.remittance_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
            self.total_amount_edit.setValue(remittance.total_amount)

            # تحديد العملة
            for i in range(self.currency_combo.count()):
                if self.currency_combo.itemData(i) == remittance.currency_id:
                    self.currency_combo.setCurrentIndex(i)
                    break

            # تحديد الحالة
            self.status_combo.setCurrentText(remittance.status)

            # معلومات البنوك
            self.sender_bank_edit.setText(remittance.sender_bank_name or "")
            self.receiver_bank_edit.setText(remittance.receiver_bank_name or "")
            self.swift_code_edit.setText(remittance.swift_code or "")

            # تحميل تفاصيل الموردين
            self.load_remittance_suppliers(remittance_id)

            # تحديث حالة الأزرار
            self.update_buttons_state(remittance.status)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في تحميل تفاصيل الحوالة:\n{str(e)}")
        finally:
            session.close()

    def load_remittance_suppliers(self, remittance_id):
        """تحميل الموردين في الحوالة"""
        session = self.db_manager.get_session()
        try:
            items = session.query(SupplierRemittanceItem).join(Supplier).join(Currency).filter(
                SupplierRemittanceItem.remittance_id == remittance_id
            ).order_by(Supplier.name).all()

            self.suppliers_table.setRowCount(len(items))

            for row, item in enumerate(items):
                # المورد
                supplier_item = QTableWidgetItem(item.supplier.name)
                supplier_item.setData(Qt.UserRole, item.id)
                self.suppliers_table.setItem(row, 0, supplier_item)

                # المبلغ
                amount_text = f"{item.amount:,.2f}"
                self.suppliers_table.setItem(row, 1, QTableWidgetItem(amount_text))

                # العملة
                self.suppliers_table.setItem(row, 2, QTableWidgetItem(item.currency.code))

                # الحالة
                status_item = QTableWidgetItem(item.status)
                if item.status == "مؤكد":
                    status_item.setBackground(QColor("#d4edda"))
                elif item.status == "مرحل":
                    status_item.setBackground(QColor("#cce5ff"))
                self.suppliers_table.setItem(row, 3, status_item)

                # تاريخ التأكيد
                confirmed_date = item.supplier_confirmed_date.strftime("%Y-%m-%d") if item.supplier_confirmed_date else "غير مؤكد"
                self.suppliers_table.setItem(row, 4, QTableWidgetItem(confirmed_date))

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في تحميل موردي الحوالة:\n{str(e)}")
        finally:
            session.close()

    def update_buttons_state(self, status):
        """تحديث حالة الأزرار حسب حالة الحوالة"""
        self.confirm_btn.setEnabled(status in ["مرسلة", "في الطريق", "وصلت للبنك"])
        self.post_btn.setEnabled(status == "مؤكدة")

    def on_status_changed(self):
        """عند تغيير حالة الحوالة"""
        if self.current_remittance_id:
            new_status = self.status_combo.currentText()
            self.update_buttons_state(new_status)

    def on_supplier_item_selected(self):
        """عند تحديد مورد من جدول الموردين"""
        selected_items = self.suppliers_table.selectedItems()
        self.remove_supplier_btn.setEnabled(len(selected_items) > 0)

    def create_new_remittance(self):
        """إنشاء حوالة جديدة"""
        dialog = NewRemittanceDialog(self)
        if dialog.exec() == QDialog.Accepted:
            self.load_remittances()

    def add_supplier_to_remittance(self):
        """إضافة مورد للحوالة"""
        if not self.current_remittance_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حوالة أولاً")
            return

        dialog = AddSupplierToRemittanceDialog(self.current_remittance_id, self)
        if dialog.exec() == QDialog.Accepted:
            self.load_remittance_suppliers(self.current_remittance_id)
            self.update_remittance_total()

    def remove_supplier_from_remittance(self):
        """إزالة مورد من الحوالة"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            return

        item_id = self.suppliers_table.item(current_row, 0).data(Qt.UserRole)
        supplier_name = self.suppliers_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من إزالة المورد '{supplier_name}' من الحوالة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                item = session.query(SupplierRemittanceItem).get(item_id)
                if item:
                    session.delete(item)
                    session.commit()

                    QMessageBox.information(self, "تم الحذف", f"تم إزالة المورد '{supplier_name}' من الحوالة")
                    self.load_remittance_suppliers(self.current_remittance_id)
                    self.update_remittance_total()

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء إزالة المورد:\n{str(e)}")
            finally:
                session.close()

    def update_remittance_total(self):
        """تحديث المبلغ الإجمالي للحوالة"""
        if not self.current_remittance_id:
            return

        session = self.db_manager.get_session()
        try:
            # حساب المجموع من تفاصيل الموردين
            total = session.query(SupplierRemittanceItem).filter_by(
                remittance_id=self.current_remittance_id
            ).with_entities(
                session.query(SupplierRemittanceItem.amount).filter_by(
                    remittance_id=self.current_remittance_id
                ).subquery().c.amount.sum()
            ).scalar() or 0.0

            # تحديث الحوالة
            remittance = session.query(SupplierRemittance).get(self.current_remittance_id)
            if remittance:
                remittance.total_amount = total
                session.commit()

                # تحديث الواجهة
                self.total_amount_edit.setValue(total)

        except Exception as e:
            session.rollback()
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في تحديث المجموع:\n{str(e)}")
        finally:
            session.close()

    def save_remittance(self):
        """حفظ بيانات الحوالة"""
        if not self.current_remittance_id:
            QMessageBox.warning(self, "تحذير", "لا توجد حوالة محددة للحفظ")
            return

        session = self.db_manager.get_session()
        try:
            remittance = session.query(SupplierRemittance).get(self.current_remittance_id)
            if not remittance:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الحوالة")
                return

            # تحديث البيانات
            remittance.remittance_date = self.remittance_date_edit.date().toPython()
            remittance.currency_id = self.currency_combo.currentData()
            remittance.status = self.status_combo.currentText()
            remittance.sender_bank_name = self.sender_bank_edit.text()
            remittance.receiver_bank_name = self.receiver_bank_edit.text()
            remittance.swift_code = self.swift_code_edit.text()
            remittance.updated_at = datetime.now()

            session.commit()
            QMessageBox.information(self, "تم الحفظ", "تم حفظ بيانات الحوالة بنجاح")

            # تحديث القائمة
            self.load_remittances()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في الحفظ", f"حدث خطأ أثناء حفظ الحوالة:\n{str(e)}")
        finally:
            session.close()

    def confirm_remittance_receipt(self):
        """تأكيد وصول الحوالة للبنك الخارجي"""
        if not self.current_remittance_id:
            return

        reply = QMessageBox.question(
            self, "تأكيد الوصول",
            "هل تم تأكيد وصول الحوالة للبنك الخارجي؟\nسيتم تحديث حالة الحوالة إلى 'مؤكدة'",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                remittance = session.query(SupplierRemittance).get(self.current_remittance_id)
                if remittance:
                    remittance.status = "مؤكدة"
                    remittance.confirmed_date = datetime.now()
                    remittance.received_date = datetime.now()

                    # تحديث حالة جميع الموردين في الحوالة
                    for item in remittance.items:
                        item.status = "مؤكد"
                        item.supplier_confirmed_date = datetime.now()

                    session.commit()

                    QMessageBox.information(self, "تم التأكيد", "تم تأكيد وصول الحوالة بنجاح")

                    # تحديث الواجهة
                    self.status_combo.setCurrentText("مؤكدة")
                    self.update_buttons_state("مؤكدة")
                    self.load_remittance_suppliers(self.current_remittance_id)
                    self.load_remittances()

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ في التأكيد", f"حدث خطأ أثناء تأكيد الحوالة:\n{str(e)}")
            finally:
                session.close()

    def post_remittance_to_accounts(self):
        """ترحيل الحوالة لحسابات الموردين"""
        if not self.current_remittance_id:
            return

        reply = QMessageBox.question(
            self, "تأكيد الترحيل",
            "هل تريد ترحيل الحوالة لحسابات الموردين؟\nسيتم إضافة المبالغ لحسابات الموردين المعنيين",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                remittance = session.query(SupplierRemittance).get(self.current_remittance_id)
                if not remittance or remittance.status != "مؤكدة":
                    QMessageBox.warning(self, "تحذير", "يجب أن تكون الحوالة مؤكدة أولاً")
                    return

                # ترحيل كل مورد في الحوالة
                for item in remittance.items:
                    # البحث عن حساب المورد أو إنشاؤه
                    account = session.query(SupplierAccount).filter_by(
                        supplier_id=item.supplier_id,
                        currency_id=item.currency_id
                    ).first()

                    if not account:
                        # إنشاء حساب جديد
                        account = SupplierAccount(
                            supplier_id=item.supplier_id,
                            currency_id=item.currency_id,
                            opening_balance=0.0,
                            current_balance=0.0,
                            credit_limit=100000.0
                        )
                        session.add(account)

                    # إضافة المبلغ للحساب
                    account.current_balance += item.amount
                    account.total_remittances += item.amount
                    account.last_remittance_date = datetime.now()
                    account.last_transaction_date = datetime.now()

                    # تحديث حالة المورد في الحوالة
                    item.status = "مرحل"
                    item.posted_to_supplier_date = datetime.now()

                # تحديث حالة الحوالة
                remittance.status = "مرحلة"
                remittance.posted_date = datetime.now()

                session.commit()

                QMessageBox.information(self, "تم الترحيل", "تم ترحيل الحوالة لحسابات الموردين بنجاح")

                # تحديث الواجهة
                self.status_combo.setCurrentText("مرحلة")
                self.update_buttons_state("مرحلة")
                self.load_remittance_suppliers(self.current_remittance_id)
                self.load_remittances()
                self.load_accounts()

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ في الترحيل", f"حدث خطأ أثناء ترحيل الحوالة:\n{str(e)}")
            finally:
                session.close()

    def generate_daily_report(self):
        """إنشاء تقرير يومي"""
        today = date.today()
        session = self.db_manager.get_session()
        try:
            remittances = session.query(SupplierRemittance).filter(
                SupplierRemittance.remittance_date == today
            ).all()

            report = f"تقرير الحوالات اليومي - {today.strftime('%Y-%m-%d')}\n"
            report += "=" * 50 + "\n\n"

            if not remittances:
                report += "لا توجد حوالات لهذا اليوم\n"
            else:
                total_amount = 0
                for remittance in remittances:
                    report += f"رقم الحوالة: {remittance.remittance_number}\n"
                    report += f"المبلغ: {remittance.total_amount:,.2f} {remittance.currency.code}\n"
                    report += f"الحالة: {remittance.status}\n"
                    report += f"عدد الموردين: {len(remittance.items)}\n"
                    report += "-" * 30 + "\n"
                    total_amount += remittance.total_amount

                report += f"\nإجمالي المبلغ: {total_amount:,.2f}\n"
                report += f"عدد الحوالات: {len(remittances)}\n"

            self.reports_text.setText(report)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")
        finally:
            session.close()

    def generate_monthly_report(self):
        """إنشاء تقرير شهري"""
        today = date.today()
        first_day = today.replace(day=1)

        session = self.db_manager.get_session()
        try:
            remittances = session.query(SupplierRemittance).filter(
                SupplierRemittance.remittance_date >= first_day,
                SupplierRemittance.remittance_date <= today
            ).all()

            report = f"تقرير الحوالات الشهري - {today.strftime('%Y-%m')}\n"
            report += "=" * 50 + "\n\n"

            if not remittances:
                report += "لا توجد حوالات لهذا الشهر\n"
            else:
                # تجميع حسب الحالة
                status_summary = {}
                total_amount = 0

                for remittance in remittances:
                    status = remittance.status
                    if status not in status_summary:
                        status_summary[status] = {'count': 0, 'amount': 0}

                    status_summary[status]['count'] += 1
                    status_summary[status]['amount'] += remittance.total_amount
                    total_amount += remittance.total_amount

                report += "ملخص حسب الحالة:\n"
                for status, data in status_summary.items():
                    report += f"{status}: {data['count']} حوالة بمبلغ {data['amount']:,.2f}\n"

                report += f"\nإجمالي المبلغ: {total_amount:,.2f}\n"
                report += f"إجمالي عدد الحوالات: {len(remittances)}\n"

            self.reports_text.setText(report)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")
        finally:
            session.close()

    def generate_supplier_report(self):
        """إنشاء تقرير حسب المورد"""
        session = self.db_manager.get_session()
        try:
            # تجميع البيانات حسب المورد
            suppliers_data = {}

            items = session.query(SupplierRemittanceItem).join(Supplier).join(SupplierRemittance).all()

            for item in items:
                supplier_name = item.supplier.name
                if supplier_name not in suppliers_data:
                    suppliers_data[supplier_name] = {
                        'total_amount': 0,
                        'remittances_count': 0,
                        'confirmed_amount': 0,
                        'posted_amount': 0
                    }

                suppliers_data[supplier_name]['total_amount'] += item.amount
                suppliers_data[supplier_name]['remittances_count'] += 1

                if item.status == "مؤكد":
                    suppliers_data[supplier_name]['confirmed_amount'] += item.amount
                elif item.status == "مرحل":
                    suppliers_data[supplier_name]['posted_amount'] += item.amount

            report = "تقرير الحوالات حسب المورد\n"
            report += "=" * 50 + "\n\n"

            if not suppliers_data:
                report += "لا توجد بيانات حوالات للموردين\n"
            else:
                for supplier, data in suppliers_data.items():
                    report += f"المورد: {supplier}\n"
                    report += f"إجمالي المبلغ: {data['total_amount']:,.2f}\n"
                    report += f"عدد الحوالات: {data['remittances_count']}\n"
                    report += f"المبلغ المؤكد: {data['confirmed_amount']:,.2f}\n"
                    report += f"المبلغ المرحل: {data['posted_amount']:,.2f}\n"
                    report += "-" * 30 + "\n"

            self.reports_text.setText(report)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")
        finally:
            session.close()


class NewRemittanceDialog(QDialog):
    """نافذة حوار إنشاء حوالة جديدة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.load_currencies()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("حوالة جديدة")
        self.setMinimumSize(500, 400)

        layout = QVBoxLayout(self)

        # معلومات الحوالة
        form_layout = QFormLayout()

        self.remittance_number_edit = QLineEdit()
        self.remittance_number_edit.setText(self.generate_remittance_number())
        form_layout.addRow("رقم الحوالة:", self.remittance_number_edit)

        self.remittance_date_edit = QDateEdit()
        self.remittance_date_edit.setDate(QDate.currentDate())
        self.remittance_date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ الحوالة:", self.remittance_date_edit)

        self.currency_combo = QComboBox()
        form_layout.addRow("العملة:", self.currency_combo)

        self.sender_bank_edit = QLineEdit()
        form_layout.addRow("البنك المرسل:", self.sender_bank_edit)

        self.receiver_bank_edit = QLineEdit()
        form_layout.addRow("البنك المستقبل:", self.receiver_bank_edit)

        self.purpose_edit = QTextEdit()
        self.purpose_edit.setMaximumHeight(80)
        form_layout.addRow("الغرض:", self.purpose_edit)

        layout.addLayout(form_layout)

        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def load_currencies(self):
        """تحميل العملات"""
        session = self.db_manager.get_session()
        try:
            currencies = session.query(Currency).filter_by(is_active=True).order_by(Currency.name).all()

            for currency in currencies:
                display_text = f"{currency.name} ({currency.code})"
                self.currency_combo.addItem(display_text, currency.id)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في تحميل العملات:\n{str(e)}")
        finally:
            session.close()

    def generate_remittance_number(self):
        """إنشاء رقم حوالة تلقائي"""
        today = date.today()
        return f"REM-{today.strftime('%Y%m%d')}-{datetime.now().strftime('%H%M%S')}"

    def accept(self):
        """قبول الحوار وإنشاء الحوالة"""
        if not self.remittance_number_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الحوالة")
            return

        if not self.currency_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار العملة")
            return

        session = self.db_manager.get_session()
        try:
            # إنشاء الحوالة الجديدة
            remittance = SupplierRemittance(
                remittance_number=self.remittance_number_edit.text().strip(),
                remittance_date=self.remittance_date_edit.date().toPython(),
                total_amount=0.0,
                currency_id=self.currency_combo.currentData(),
                sender_bank_name=self.sender_bank_edit.text().strip(),
                receiver_bank_name=self.receiver_bank_edit.text().strip(),
                purpose=self.purpose_edit.toPlainText().strip(),
                status="مرسلة"
            )

            session.add(remittance)
            session.commit()

            QMessageBox.information(self, "تم الإنشاء", "تم إنشاء الحوالة الجديدة بنجاح")
            super().accept()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في الإنشاء", f"حدث خطأ أثناء إنشاء الحوالة:\n{str(e)}")
        finally:
            session.close()


class AddSupplierToRemittanceDialog(QDialog):
    """نافذة حوار إضافة مورد للحوالة"""

    def __init__(self, remittance_id, parent=None):
        super().__init__(parent)
        self.remittance_id = remittance_id
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة مورد للحوالة")
        self.setMinimumSize(600, 500)

        layout = QVBoxLayout(self)

        # معلومات المورد
        form_layout = QFormLayout()

        self.supplier_combo = QComboBox()
        form_layout.addRow("المورد:", self.supplier_combo)

        self.amount_edit = QDoubleSpinBox()
        self.amount_edit.setMaximum(*********.99)
        self.amount_edit.setDecimals(2)
        self.amount_edit.setMinimum(0.01)
        form_layout.addRow("المبلغ:", self.amount_edit)

        self.currency_combo = QComboBox()
        form_layout.addRow("العملة:", self.currency_combo)

        self.supplier_bank_edit = QLineEdit()
        form_layout.addRow("بنك المورد:", self.supplier_bank_edit)

        self.account_number_edit = QLineEdit()
        form_layout.addRow("رقم الحساب:", self.account_number_edit)

        self.account_name_edit = QLineEdit()
        form_layout.addRow("اسم الحساب:", self.account_name_edit)

        self.swift_code_edit = QLineEdit()
        form_layout.addRow("كود SWIFT:", self.swift_code_edit)

        self.purpose_edit = QTextEdit()
        self.purpose_edit.setMaximumHeight(80)
        form_layout.addRow("الغرض:", self.purpose_edit)

        self.invoice_numbers_edit = QLineEdit()
        form_layout.addRow("أرقام الفواتير:", self.invoice_numbers_edit)

        layout.addLayout(form_layout)

        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def load_data(self):
        """تحميل البيانات"""
        session = self.db_manager.get_session()
        try:
            # تحميل الموردين
            suppliers = session.query(Supplier).filter_by(is_active=True).order_by(Supplier.name).all()

            for supplier in suppliers:
                display_text = f"{supplier.name} ({supplier.code})"
                self.supplier_combo.addItem(display_text, supplier.id)

            # تحميل العملات
            currencies = session.query(Currency).filter_by(is_active=True).order_by(Currency.name).all()

            for currency in currencies:
                display_text = f"{currency.name} ({currency.code})"
                self.currency_combo.addItem(display_text, currency.id)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في تحميل البيانات:\n{str(e)}")
        finally:
            session.close()

    def accept(self):
        """قبول الحوار وإضافة المورد"""
        if not self.supplier_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد")
            return

        if self.amount_edit.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
            return

        if not self.currency_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة")
            return

        session = self.db_manager.get_session()
        try:
            # التحقق من عدم وجود المورد مسبقاً في الحوالة
            existing_item = session.query(SupplierRemittanceItem).filter_by(
                remittance_id=self.remittance_id,
                supplier_id=self.supplier_combo.currentData()
            ).first()

            if existing_item:
                QMessageBox.warning(self, "تحذير", "هذا المورد موجود مسبقاً في الحوالة")
                return

            # إنشاء عنصر جديد
            item = SupplierRemittanceItem(
                remittance_id=self.remittance_id,
                supplier_id=self.supplier_combo.currentData(),
                amount=self.amount_edit.value(),
                currency_id=self.currency_combo.currentData(),
                supplier_bank_name=self.supplier_bank_edit.text().strip(),
                supplier_account_number=self.account_number_edit.text().strip(),
                supplier_account_name=self.account_name_edit.text().strip(),
                supplier_swift_code=self.swift_code_edit.text().strip(),
                purpose=self.purpose_edit.toPlainText().strip(),
                invoice_numbers=self.invoice_numbers_edit.text().strip(),
                status="مرسل"
            )

            session.add(item)
            session.commit()

            QMessageBox.information(self, "تم الإضافة", "تم إضافة المورد للحوالة بنجاح")
            super().accept()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في الإضافة", f"حدث خطأ أثناء إضافة المورد:\n{str(e)}")
        finally:
            session.close()


class SupplierRemittancesWindow(QMainWindow):
    """نافذة إدارة حوالات الموردين الرئيسية"""

    def __init__(self):
        super().__init__()
        self.setup_window()
        self.setup_ui()

    def setup_window(self):
        """إعداد النافذة"""
        self.setWindowTitle("إدارة حوالات الموردين - ProShipment")
        self.setMinimumSize(1400, 900)

        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()

        # تطبيق الخط العربي
        font = QFont("Segoe UI", 10)
        self.setFont(font)

        # تطبيق تخطيط RTL
        self.setLayoutDirection(Qt.RightToLeft)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء الويدجت الرئيسي
        self.remittances_widget = SupplierRemittancesWidget()
        self.setCentralWidget(self.remittances_widget)

        # إعداد شريط الحالة
        self.statusBar().showMessage("جاهز - إدارة حوالات الموردين الشاملة والمتقدمة")
