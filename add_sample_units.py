#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية لوحدات القياس
Add Sample Units of Measurement Data
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.universal_database_manager import UniversalDatabaseManager
from src.database.oracle_config import DatabaseConfigManager
from src.database.models import UnitOfMeasure

def add_sample_units():
    """إضافة وحدات قياس تجريبية"""
    
    print("🔧 إضافة وحدات قياس تجريبية...")
    print("=" * 50)
    
    try:
        # تحميل التكوين
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        # وحدات قياس إضافية متخصصة
        additional_units = [
            # وحدات الوقت
            {
                "name": "ثانية",
                "name_en": "Second",
                "symbol": "ث",
                "symbol_en": "s",
                "description": "وحدة قياس الوقت الأساسية"
            },
            {
                "name": "دقيقة",
                "name_en": "Minute",
                "symbol": "د",
                "symbol_en": "min",
                "description": "وحدة قياس الوقت - 60 ثانية"
            },
            {
                "name": "ساعة",
                "name_en": "Hour",
                "symbol": "س",
                "symbol_en": "h",
                "description": "وحدة قياس الوقت - 60 دقيقة"
            },
            {
                "name": "يوم",
                "name_en": "Day",
                "symbol": "يوم",
                "symbol_en": "day",
                "description": "وحدة قياس الوقت - 24 ساعة"
            },
            # وحدات الطاقة
            {
                "name": "واط",
                "name_en": "Watt",
                "symbol": "واط",
                "symbol_en": "W",
                "description": "وحدة قياس القدرة الكهربائية"
            },
            {
                "name": "كيلوواط",
                "name_en": "Kilowatt",
                "symbol": "ك.واط",
                "symbol_en": "kW",
                "description": "وحدة قياس القدرة الكهربائية - 1000 واط"
            },
            # وحدات درجة الحرارة
            {
                "name": "درجة مئوية",
                "name_en": "Celsius",
                "symbol": "°م",
                "symbol_en": "°C",
                "description": "وحدة قياس درجة الحرارة"
            },
            {
                "name": "فهرنهايت",
                "name_en": "Fahrenheit",
                "symbol": "°ف",
                "symbol_en": "°F",
                "description": "وحدة قياس درجة الحرارة"
            },
            # وحدات السرعة
            {
                "name": "كيلومتر/ساعة",
                "name_en": "Kilometer per Hour",
                "symbol": "كم/س",
                "symbol_en": "km/h",
                "description": "وحدة قياس السرعة"
            },
            {
                "name": "متر/ثانية",
                "name_en": "Meter per Second",
                "symbol": "م/ث",
                "symbol_en": "m/s",
                "description": "وحدة قياس السرعة"
            },
            # وحدات خاصة بالتجارة
            {
                "name": "بالة",
                "name_en": "Bale",
                "symbol": "بالة",
                "symbol_en": "bale",
                "description": "وحدة تعبئة للمواد النسيجية"
            },
            {
                "name": "رزمة",
                "name_en": "Bundle",
                "symbol": "رزمة",
                "symbol_en": "bundle",
                "description": "وحدة تعبئة للمواد المختلفة"
            },
            {
                "name": "لفة",
                "name_en": "Roll",
                "symbol": "لفة",
                "symbol_en": "roll",
                "description": "وحدة تعبئة للمواد الملفوفة"
            },
            {
                "name": "طقم",
                "name_en": "Set",
                "symbol": "طقم",
                "symbol_en": "set",
                "description": "مجموعة من القطع المترابطة"
            },
            # وحدات الأدوية والمواد الكيميائية
            {
                "name": "مليجرام",
                "name_en": "Milligram",
                "symbol": "مجم",
                "symbol_en": "mg",
                "description": "وحدة قياس الوزن الدقيقة للأدوية"
            },
            {
                "name": "مليلتر",
                "name_en": "Milliliter",
                "symbol": "مل",
                "symbol_en": "ml",
                "description": "وحدة قياس الحجم للسوائل"
            }
        ]
        
        with db_manager.get_session() as session:
            added_count = 0
            skipped_count = 0
            
            for unit_data in additional_units:
                # التحقق من عدم وجود الوحدة مسبقاً
                existing = session.query(UnitOfMeasure).filter_by(name=unit_data["name"]).first()
                
                if existing:
                    print(f"   ⚠️ الوحدة '{unit_data['name']}' موجودة مسبقاً - تم التخطي")
                    skipped_count += 1
                    continue
                
                # إنشاء الوحدة الجديدة
                new_unit = UnitOfMeasure(
                    name=unit_data["name"],
                    name_en=unit_data["name_en"],
                    symbol=unit_data["symbol"],
                    symbol_en=unit_data["symbol_en"],
                    description=unit_data["description"],
                    is_active=True
                )
                
                session.add(new_unit)
                print(f"   ✅ تم إضافة الوحدة: {unit_data['name']} ({unit_data['symbol']})")
                added_count += 1
            
            print(f"\n📊 ملخص العملية:")
            print(f"   ✅ تم إضافة: {added_count} وحدة")
            print(f"   ⚠️ تم تخطي: {skipped_count} وحدة (موجودة مسبقاً)")
            
            if added_count > 0:
                print(f"\n🎉 تم إضافة {added_count} وحدة قياس جديدة بنجاح!")
            else:
                print(f"\n💡 جميع الوحدات موجودة مسبقاً.")
                
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def list_all_units():
    """عرض جميع وحدات القياس الموجودة"""
    print("\n📋 وحدات القياس الموجودة:")
    print("-" * 60)
    
    try:
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        with db_manager.get_session() as session:
            units = session.query(UnitOfMeasure).order_by(UnitOfMeasure.name).all()
            
            if not units:
                print("   لا توجد وحدات قياس مسجلة")
                return
            
            print(f"{'الرقم':<5} {'الاسم':<15} {'الرمز':<10} {'الاسم بالإنجليزية':<20} {'الحالة':<10}")
            print("-" * 60)
            
            for i, unit in enumerate(units, 1):
                status = "نشطة" if unit.is_active else "غير نشطة"
                print(f"{i:<5} {unit.name:<15} {unit.symbol:<10} {unit.name_en:<20} {status:<10}")
            
            print(f"\nإجمالي الوحدات: {len(units)}")
            active_count = sum(1 for unit in units if unit.is_active)
            print(f"الوحدات النشطة: {active_count}")
            print(f"الوحدات غير النشطة: {len(units) - active_count}")
            
    except Exception as e:
        print(f"❌ خطأ في عرض البيانات: {e}")

if __name__ == "__main__":
    print("🚀 إدارة وحدات القياس")
    print("=" * 50)
    
    # إضافة البيانات التجريبية
    success = add_sample_units()
    
    if success:
        # عرض جميع الوحدات
        list_all_units()
        print("\n✅ تمت العملية بنجاح!")
    else:
        print("\n❌ فشلت العملية!")
        sys.exit(1)
