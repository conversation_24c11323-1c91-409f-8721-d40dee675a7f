#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثال عملي لاستخدام قالب النموذج
Practical Example of Using Form Template
"""

import sys
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt, QTimer
from form_template import FormTemplate

class FormTemplateExample(FormTemplate):
    """مثال عملي لاستخدام القالب"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("مثال عملي - قالب النموذج")
        
        # إعداد البيانات التجريبية
        self.setup_sample_data()
        
        # إعداد الاتصالات الإضافية
        self.setup_additional_connections()
        
        # إعداد مؤقت لتحديث الوقت
        self.setup_timer()
    
    def setup_sample_data(self):
        """إعداد بيانات تجريبية"""
        # ملء البيانات التجريبية
        self.document_number.setText("SHP-2024-001")
        self.document_date.setText("2024-07-11")
        self.customer_name.setText("شركة النقل السريع المحدودة")
        self.customer_number.setText("CUST-001")
        self.customer_address.setPlainText("الرياض - حي الملك فهد - شارع الملك عبدالعزيز\nص.ب: 12345 - الرمز البريدي: 11564")
        
        # تحديد نوع وحالة الشحنة
        self.shipment_type.setCurrentText("شحنة سريعة")
        self.shipment_status.setCurrentText("جاهزة للشحن")
        
        # بيانات الوزن والقيمة
        self.weight.setText("25.5")
        self.value.setText("1500.00")
        
        # المدن
        self.sender_city.setCurrentText("الرياض")
        self.receiver_city.setCurrentText("جدة")
        
        # ملاحظات
        self.notes.setPlainText("شحنة عاجلة - يرجى التسليم خلال 24 ساعة\nيحتوي على مواد قابلة للكسر - يرجى التعامل بحذر")
        
        # تحديث شريط الحالة
        self.statusBar().showMessage("تم تحميل البيانات التجريبية")
    
    def setup_additional_connections(self):
        """إعداد اتصالات إضافية للمثال"""
        # ربط أزرار إضافية
        self.save_btn.clicked.connect(self.save_with_confirmation)
        self.export_btn.clicked.connect(self.export_example_data)
        self.import_btn.clicked.connect(self.import_example_data)
        
        # ربط تغيير نوع الشحنة
        self.shipment_type.currentTextChanged.connect(self.on_shipment_type_changed)
    
    def setup_timer(self):
        """إعداد مؤقت لتحديث الوقت"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # تحديث كل ثانية
        self.update_time()
    
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        from datetime import datetime
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"الوقت: {current_time}")
    
    def save_with_confirmation(self):
        """حفظ مع رسالة تأكيد"""
        if self.validate_form_data():
            data = self.collect_form_data()
            
            # إظهار ملخص البيانات
            summary = self.create_data_summary(data)
            
            reply = QMessageBox.question(
                self, "تأكيد الحفظ",
                f"هل تريد حفظ البيانات التالية؟\n\n{summary}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                # محاكاة عملية الحفظ
                self.simulate_save_process()
                QMessageBox.information(self, "نجح الحفظ", "تم حفظ البيانات بنجاح!")
                self.statusBar().showMessage("تم حفظ البيانات بنجاح")
                
                # تحديث عدد السجلات
                current_count = int(self.record_count_label.text().split(": ")[1])
                self.record_count_label.setText(f"عدد السجلات: {current_count + 1}")
        else:
            QMessageBox.warning(self, "خطأ في البيانات", "يرجى تصحيح الأخطاء قبل الحفظ")
    
    def create_data_summary(self, data):
        """إنشاء ملخص البيانات"""
        summary = f"""رقم المستند: {data['document_number']}
التاريخ: {data['document_date']}
العميل: {data['customer_name']}
نوع الشحنة: {data['shipment_type']}
الوزن: {data['weight']} كجم
القيمة: {data['value']} ريال
من: {data['sender_city']} إلى: {data['receiver_city']}"""
        return summary
    
    def simulate_save_process(self):
        """محاكاة عملية الحفظ"""
        import time
        
        # محاكاة تأخير الحفظ
        self.statusBar().showMessage("جاري الحفظ...")
        QApplication.processEvents()
        time.sleep(1)
        
        # محاكاة إنشاء ملف
        self.statusBar().showMessage("إنشاء الملف...")
        QApplication.processEvents()
        time.sleep(0.5)
        
        # محاكاة حفظ قاعدة البيانات
        self.statusBar().showMessage("حفظ في قاعدة البيانات...")
        QApplication.processEvents()
        time.sleep(0.5)
    
    def export_example_data(self):
        """تصدير البيانات التجريبية"""
        data = self.collect_form_data()
        
        # محاكاة تصدير البيانات
        export_text = f"""# بيانات الشحنة المصدرة
رقم المستند: {data['document_number']}
تاريخ المستند: {data['document_date']}
اسم العميل: {data['customer_name']}
رقم العميل: {data['customer_number']}
عنوان العميل: {data['customer_address']}
نوع الشحنة: {data['shipment_type']}
حالة الشحنة: {data['shipment_status']}
الوزن: {data['weight']} كجم
القيمة: {data['value']} ريال
مدينة المرسل: {data['sender_city']}
مدينة المستقبل: {data['receiver_city']}
الملاحظات: {data['notes']}

# خيارات الطباعة
طباعة الرأسية: {'نعم' if data['print_options']['header'] else 'لا'}
طباعة التذييل: {'نعم' if data['print_options']['footer'] else 'لا'}
طباعة الشعار: {'نعم' if data['print_options']['logo'] else 'لا'}
"""
        
        QMessageBox.information(
            self, "تصدير البيانات",
            f"تم تصدير البيانات:\n\n{export_text[:200]}..."
        )
        
        self.statusBar().showMessage("تم تصدير البيانات")
    
    def import_example_data(self):
        """استيراد بيانات تجريبية"""
        # بيانات تجريبية أخرى
        sample_data = {
            "document_number": "SHP-2024-002",
            "document_date": "2024-07-12",
            "customer_name": "مؤسسة التجارة الدولية",
            "customer_number": "CUST-002",
            "customer_address": "جدة - حي الروضة - طريق الملك عبدالله\nص.ب: 54321 - الرمز البريدي: 21564",
            "shipment_type": "شحنة مبردة",
            "weight": "15.0",
            "value": "2500.00",
            "sender_city": "جدة",
            "receiver_city": "الدمام",
            "notes": "شحنة مبردة - درجة حرارة من 2-8 مئوية\nيجب التسليم خلال 12 ساعة"
        }
        
        # تطبيق البيانات المستوردة
        self.document_number.setText(sample_data["document_number"])
        self.document_date.setText(sample_data["document_date"])
        self.customer_name.setText(sample_data["customer_name"])
        self.customer_number.setText(sample_data["customer_number"])
        self.customer_address.setPlainText(sample_data["customer_address"])
        self.shipment_type.setCurrentText(sample_data["shipment_type"])
        self.weight.setText(sample_data["weight"])
        self.value.setText(sample_data["value"])
        self.sender_city.setCurrentText(sample_data["sender_city"])
        self.receiver_city.setCurrentText(sample_data["receiver_city"])
        self.notes.setPlainText(sample_data["notes"])
        
        QMessageBox.information(self, "استيراد البيانات", "تم استيراد البيانات التجريبية بنجاح!")
        self.statusBar().showMessage("تم استيراد البيانات")
    
    def on_shipment_type_changed(self, shipment_type):
        """معالج تغيير نوع الشحنة"""
        # تحديث الحالة حسب النوع
        if "سريعة" in shipment_type:
            self.shipment_status.setCurrentText("جاهزة للشحن")
        elif "مبردة" in shipment_type:
            self.shipment_status.setCurrentText("قيد المعالجة")
        elif "خاصة" in shipment_type:
            self.shipment_status.setCurrentText("جديدة")
        
        self.statusBar().showMessage(f"تم تغيير نوع الشحنة إلى: {shipment_type}")
    
    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        reply = QMessageBox.question(
            self, "تأكيد الإغلاق",
            "هل تريد إغلاق النموذج؟\nسيتم فقدان البيانات غير المحفوظة.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if hasattr(self, 'timer'):
                self.timer.stop()
            event.accept()
        else:
            event.ignore()

def main():
    """الدالة الرئيسية للمثال"""
    print("=" * 60)
    print("🚀 مثال عملي لاستخدام قالب النموذج")
    print("=" * 60)
    print("📋 يحتوي على:")
    print("   • بيانات تجريبية محملة مسبقاً")
    print("   • وظائف حفظ وتصدير واستيراد")
    print("   • تحديث الوقت في الوقت الفعلي")
    print("   • رسائل تأكيد وتحقق")
    print("   • معالجة الأحداث المتقدمة")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النموذج التجريبي
    example_form = FormTemplateExample()
    example_form.show()
    
    # رسالة ترحيب
    QMessageBox.information(
        example_form, "مرحباً",
        "مرحباً بك في المثال العملي لقالب النموذج!\n\n"
        "تم تحميل بيانات تجريبية مسبقاً.\n"
        "يمكنك تجربة جميع الوظائف والميزات.\n\n"
        "جرب:\n"
        "• تعديل البيانات\n"
        "• حفظ النموذج\n"
        "• تصدير البيانات\n"
        "• استيراد بيانات جديدة"
    )
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
