# -*- coding: utf-8 -*-
"""
نافذة إعدادات الشركة
Company Settings Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QMessageBox, QMenuBar, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QAction

from .company_settings import CompanySettingsWidget

class CompanySettingsWindow(QMainWindow):
    """نافذة إعدادات الشركة"""
    
    settings_saved = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إعدادات الشركة - نظام إدارة الشحنات")
        self.setFixedSize(896, 896)  # تغيير حجم النافذة إلى 896x896 بكسل
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        title_label = QLabel("إعدادات بيانات الشركة")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # ويدجت إعدادات الشركة
        self.company_settings_widget = CompanySettingsWidget()
        main_layout.addWidget(self.company_settings_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        save_button = QPushButton("حفظ الإعدادات")
        save_button.clicked.connect(self.save_settings)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        apply_button = QPushButton("تطبيق")
        apply_button.clicked.connect(self.apply_settings)
        apply_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.close)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(apply_button)
        buttons_layout.addWidget(cancel_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_settings)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة تحرير
        edit_menu = menubar.addMenu("تحرير")
        reset_action = QAction("إعادة تعيين", self)
        reset_action.triggered.connect(self.reset_settings)
        edit_menu.addAction(reset_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        save_action = QAction("حفظ", self)
        save_action.triggered.connect(self.save_settings)
        toolbar.addAction(save_action)
        
        apply_action = QAction("تطبيق", self)
        apply_action.triggered.connect(self.apply_settings)
        toolbar.addAction(apply_action)
        
        toolbar.addSeparator()
        
        close_action = QAction("إغلاق", self)
        close_action.triggered.connect(self.close)
        toolbar.addAction(close_action)
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            if hasattr(self.company_settings_widget, 'save_settings'):
                self.company_settings_widget.save_settings()
            
            QMessageBox.information(self, "تم الحفظ", "تم حفظ إعدادات الشركة بنجاح")
            self.settings_saved.emit()
            self.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"حدث خطأ أثناء حفظ إعدادات الشركة:\n{str(e)}")
    
    def apply_settings(self):
        """تطبيق الإعدادات"""
        try:
            if hasattr(self.company_settings_widget, 'save_settings'):
                self.company_settings_widget.save_settings()
            
            QMessageBox.information(self, "تم التطبيق", "تم تطبيق إعدادات الشركة بنجاح")
            self.settings_saved.emit()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التطبيق", f"حدث خطأ أثناء تطبيق إعدادات الشركة:\n{str(e)}")
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self, "إعادة تعيين الإعدادات",
            "هل تريد إعادة تعيين جميع إعدادات الشركة إلى القيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                if hasattr(self.company_settings_widget, 'reset_settings'):
                    self.company_settings_widget.reset_settings()
                QMessageBox.information(self, "تم إعادة التعيين", "تم إعادة تعيين إعدادات الشركة إلى القيم الافتراضية")
            except Exception as e:
                QMessageBox.critical(self, "خطأ في إعادة التعيين", f"حدث خطأ أثناء إعادة تعيين إعدادات الشركة:\n{str(e)}")
    
    def refresh_data(self):
        """تحديث البيانات"""
        if hasattr(self.company_settings_widget, 'load_settings'):
            self.company_settings_widget.load_settings()
