# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للبرنامج
Main Application Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QMenuBar, QMenu, QToolBar, QStatusBar,
                               QLabel, QPushButton, QFrame, QGridLayout, QMessageBox,
                               QScrollArea, QGraphicsDropShadowEffect, QSizePolicy, QTreeWidget,
                               QTreeWidgetItem, QTextEdit, QSplitter, QApplication, QDialog)
from PySide6.QtCore import Qt, QSize, Signal, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QIcon, QPixmap, QFont, QAction, QLinearGradient, QColor, QPainter, QPalette, QBrush, QPen

# محاولة تحميل QSvgRenderer مع معالجة الأخطاء
try:
    from PySide6.QtSvg import QSvgRenderer
    SVG_AVAILABLE = True
except ImportError:
    print("⚠️ مكتبة QtSvg غير متاحة، سيتم استخدام صورة PNG بديلة")
    SVG_AVAILABLE = False
    QSvgRenderer = None

from ..utils.arabic_support import reshape_arabic_text
from .settings.settings_window import SettingsWindow
from .items.items_window import ItemsWindow
from .suppliers.suppliers_window import SuppliersWindow
from .shipments.shipments_window import ShipmentsWindow
from .shipments.live_tracking_window import LiveTrackingWindow
from .shipments.shipment_maps_window import ShipmentMapsWindow
from .shipments.real_map_window import RealMapWindow
from .shipments.simple_real_map_window import SimpleRealMapWindow
from .dialogs.shipping_data_enhancement_dialog import ShippingDataEnhancementDialog
from .styles.style_manager import style_manager
import os

class WatermarkWidget(QWidget):
    """ويدجت مخصص مع علامة مائية في الخلفية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.watermark_svg = None
        self.watermark_pixmap = None
        self.load_watermark()

    def load_watermark(self):
        """تحميل العلامة المائية من ملف SVG أو PNG"""
        try:
            # مسار الصورة
            current_dir = os.path.dirname(os.path.abspath(__file__))

            # محاولة تحميل SVG أولاً
            if SVG_AVAILABLE:
                svg_path = os.path.join(current_dir, "..", "resources", "images", "company_logo.svg")
                svg_path = os.path.normpath(svg_path)

                if os.path.exists(svg_path):
                    self.watermark_svg = QSvgRenderer(svg_path)
                    if self.watermark_svg.isValid():
                        print("✅ تم تحميل العلامة المائية SVG بنجاح")
                        return

            # إنشاء صورة PNG بديلة إذا لم يتوفر SVG
            self.create_fallback_watermark()

        except Exception as e:
            print(f"❌ خطأ في تحميل العلامة المائية: {e}")
            self.create_fallback_watermark()

    def create_fallback_watermark(self):
        """إنشاء علامة مائية بديلة باستخدام النص"""
        try:
            # إنشاء صورة بديلة بالنص
            pixmap = QPixmap(400, 400)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # رسم دائرة خارجية
            painter.setPen(QPen(QColor(100, 100, 100), 8))
            painter.drawEllipse(50, 50, 300, 300)

            # رسم شعار مبسط
            painter.setPen(QPen(QColor(200, 0, 0), 6))
            painter.setBrush(QBrush(QColor(255, 0, 0)))
            painter.drawEllipse(150, 150, 100, 100)

            # إضافة النص
            painter.setPen(QPen(QColor(50, 50, 50)))
            font = QFont("Arial", 24, QFont.Bold)
            painter.setFont(font)
            painter.drawText(QRect(120, 180, 160, 40), Qt.AlignCenter, "FTC")

            # النص العربي
            font.setPointSize(12)
            painter.setFont(font)
            painter.drawText(QRect(100, 280, 200, 30), Qt.AlignCenter, "الشركة التجارية")

            painter.end()
            self.watermark_pixmap = pixmap
            print("✅ تم إنشاء علامة مائية بديلة بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء العلامة المائية البديلة: {e}")

    def paintEvent(self, event):
        """رسم العلامة المائية في الخلفية"""
        super().paintEvent(event)

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # حساب حجم وموقع العلامة المائية
        widget_rect = self.rect()
        watermark_size = min(widget_rect.width(), widget_rect.height()) // 3

        # موقع العلامة المائية في المنتصف
        x = (widget_rect.width() - watermark_size) // 2
        y = (widget_rect.height() - watermark_size) // 2

        # تعيين الشفافية للعلامة المائية
        painter.setOpacity(0.08)  # شفافية عالية

        watermark_rect = QRect(x, y, watermark_size, watermark_size)

        # رسم العلامة المائية حسب النوع المتاح
        if self.watermark_svg and self.watermark_svg.isValid():
            # رسم SVG
            self.watermark_svg.render(painter, watermark_rect)
        elif self.watermark_pixmap:
            # رسم PNG
            scaled_pixmap = self.watermark_pixmap.scaled(
                watermark_size, watermark_size,
                Qt.KeepAspectRatio, Qt.SmoothTransformation
            )
            painter.drawPixmap(watermark_rect.topLeft(), scaled_pixmap)

        painter.end()

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام إدارة الشحنات المتكامل - ProShipment")
        self.setMinimumSize(1200, 800)

        # تعيين النافذة لتفتح في وضع ملء الشاشة
        self.showMaximized()

        # تحميل الثيم الحديث
        style_manager.load_theme("modern")

        # إعداد الستايل العام
        self.setup_global_style()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()

        # إعداد الرسوم المتحركة
        self.setup_animations()

        # متغيرات النوافذ المنفصلة
        self.settings_window = None
        self.items_window = None
        self.suppliers_window = None
        self.shipments_window = None
        self.live_tracking_window = None
        self.shipment_maps_window = None
        self.real_map_window = None
        self.simple_map_window = None
        self.remittances_window = None
        self.database_settings_window = None
        self.banks_management_window = None
        self.supplier_accounts_management_window = None

    def center_window(self, window):
        """توسيط النافذة وسط الشاشة"""
        try:
            # الحصول على حجم الشاشة
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()

            # الحصول على حجم النافذة
            window_geometry = window.frameGeometry()

            # حساب الموقع المركزي
            center_point = screen_geometry.center()
            window_geometry.moveCenter(center_point)

            # تطبيق الموقع الجديد
            window.move(window_geometry.topLeft())

        except Exception as e:
            print(f"خطأ في توسيط النافذة: {e}")

    def setup_global_style(self):
        """إعداد الستايل الأساسي للتطبيق"""
        # إزالة جميع الأنماط المخصصة
        self.setStyleSheet("")

    def setup_animations(self):
        """إعداد الرسوم المتحركة"""
        # مؤقت لتحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # تحديث كل ثانية

    def setup_ui(self):
        """إعداد واجهة المستخدم"""

        # الويدجت المركزي مع منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        # إزالة أنماط منطقة التمرير

        # استخدام الويدجت المخصص مع العلامة المائية
        central_widget = WatermarkWidget()
        central_widget.setObjectName("centralWidget")

        scroll_area.setWidget(central_widget)
        self.setCentralWidget(scroll_area)

        # التخطيط الرئيسي العمودي مثل ERP التقليدي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # الشريط العلوي الأحمر
        self.create_top_header_bar(main_layout)

        # المحتوى الرئيسي
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(15)
        content_layout.setContentsMargins(20, 20, 20, 20)

        # شبكة الأيقونات الرئيسية مثل الصورة
        self.create_erp_style_grid(content_layout)

        # إزالة أنماط content_widget

        main_layout.addWidget(content_widget)

    def create_top_header_bar(self, layout):
        """إنشاء الشريط العلوي الأحمر مثل الصورة"""
        header_bar = QFrame()
        header_bar.setFixedHeight(80)
        # إزالة أنماط الشريط العلوي

        header_layout = QHBoxLayout(header_bar)
        header_layout.setContentsMargins(20, 10, 20, 10)
        header_layout.setSpacing(20)

        # الجانب الأيسر - اسم النظام
        left_section = QVBoxLayout()
        left_section.setSpacing(5)

        # اسم النظام الرئيسي
        system_name = QLabel("ProShipment Plus ERP")
        system_name.setAlignment(Qt.AlignRight)
        name_font = QFont()
        name_font.setPointSize(18)
        name_font.setBold(True)
        name_font.setFamily("Arial")
        system_name.setFont(name_font)
        # إزالة أنماط اسم النظام

        # وصف النظام
        system_desc = QLabel("نظام إدارة الشحنات والتوريد المتكامل - الإصدار المتقدم")
        system_desc.setAlignment(Qt.AlignRight)
        desc_font = QFont()
        desc_font.setPointSize(11)
        desc_font.setWeight(QFont.Medium)
        system_desc.setFont(desc_font)
        # إزالة أنماط وصف النظام

        left_section.addWidget(system_name)
        left_section.addWidget(system_desc)

        # الجانب الأيمن - معلومات المستخدم والتاريخ
        right_section = QVBoxLayout()
        right_section.setSpacing(5)
        right_section.setAlignment(Qt.AlignLeft)

        # معلومات المستخدم
        user_info = QLabel("المستخدم: المدير العام")
        user_info.setAlignment(Qt.AlignLeft)
        user_font = QFont()
        user_font.setPointSize(12)
        user_font.setBold(True)
        user_info.setFont(user_font)
        # إزالة أنماط معلومات المستخدم

        # التاريخ والوقت
        from datetime import datetime
        current_time = datetime.now()
        date_time_info = QLabel(f"التاريخ: {current_time.strftime('%Y/%m/%d')} - الوقت: {current_time.strftime('%H:%M')}")
        date_time_info.setAlignment(Qt.AlignLeft)
        datetime_font = QFont()
        datetime_font.setPointSize(10)
        date_time_info.setFont(datetime_font)
        # إزالة أنماط معلومات التاريخ والوقت

        right_section.addWidget(user_info)
        right_section.addWidget(date_time_info)

        # الوسط - شعار الشركة
        center_section = QVBoxLayout()
        center_section.setAlignment(Qt.AlignCenter)

        logo_label = QLabel("🚢")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_font = QFont()
        logo_font.setPointSize(32)
        logo_label.setFont(logo_font)
        # إزالة أنماط الشعار

        center_section.addWidget(logo_label)

        header_layout.addLayout(left_section, 3)
        header_layout.addLayout(center_section, 1)
        header_layout.addLayout(right_section, 2)

        layout.addWidget(header_bar)

    def create_erp_style_grid(self, layout):
        """إنشاء التخطيط الجديد مع القائمة القابلة للطي ومنطقة المحتوى المحسنة"""
        # إنشاء QSplitter للتحكم في حجم الأقسام
        splitter = QSplitter(Qt.Horizontal)

        # إنشاء القائمة الرئيسية القابلة للطي (الجانب الأيسر)
        main_menu = self.create_collapsible_menu()
        splitter.addWidget(main_menu)

        # إنشاء منطقة المحتوى المحسنة (الجانب الأيمن)
        content_area = self.create_enhanced_content_area()
        splitter.addWidget(content_area)

        # تعيين النسب الأولية (25% للقائمة، 75% للمحتوى)
        splitter.setSizes([300, 900])
        splitter.setCollapsible(0, False)  # منع إخفاء القائمة تماماً

        layout.addWidget(splitter)

    def create_enhanced_content_area(self):
        """إنشاء منطقة المحتوى المحسنة بدلاً من البطاقات"""
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
            }
        """)

        content_layout = QVBoxLayout(content_frame)
        content_layout.setSpacing(20)
        content_layout.setContentsMargins(30, 30, 30, 30)

        # عنوان المنطقة الرئيسية
        welcome_label = QLabel("مرحباً بك في نظام إدارة الشحنات المتكامل")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_font = QFont()
        welcome_font.setPointSize(24)
        welcome_font.setBold(True)
        welcome_label.setFont(welcome_font)
        welcome_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                padding: 20px;
                background-color: white;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
            }
        """)
        content_layout.addWidget(welcome_label)

        # منطقة الإحصائيات السريعة
        stats_area = self.create_quick_stats_area()
        content_layout.addWidget(stats_area)

        # منطقة الأخبار والتحديثات
        news_area = self.create_news_updates_area()
        content_layout.addWidget(news_area)

        # إضافة مساحة مرنة في النهاية
        content_layout.addStretch()

        return content_frame

    def create_collapsible_menu(self):
        """إنشاء القائمة الرئيسية القابلة للطي مع الأنظمة الفرعية"""
        menu_frame = QFrame()
        menu_frame.setMinimumWidth(280)
        menu_frame.setMaximumWidth(400)
        menu_frame.setStyleSheet("""
            QFrame {
                background-color: #1e293b;
                border: none;
                border-radius: 0px;
            }
        """)

        menu_layout = QVBoxLayout(menu_frame)
        menu_layout.setSpacing(0)
        menu_layout.setContentsMargins(0, 0, 0, 0)

        # عنوان القائمة
        title_label = QLabel("القائمة الرئيسية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFixedHeight(60)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #0f172a;
                color: white;
                font-size: 16px;
                font-weight: bold;
                border-bottom: 2px solid #334155;
                padding: 15px;
            }
        """)
        menu_layout.addWidget(title_label)

        # إنشاء QTreeWidget للقائمة القابلة للطي
        self.menu_tree = QTreeWidget()
        self.menu_tree.setHeaderHidden(True)
        self.menu_tree.setRootIsDecorated(True)
        self.menu_tree.setIndentation(20)
        self.menu_tree.setStyleSheet("""
            QTreeWidget {
                background-color: #1e293b;
                color: white;
                border: none;
                outline: none;
                font-size: 13px;
            }
            QTreeWidget::item {
                padding: 8px 15px;
                border: none;
                min-height: 35px;
            }
            QTreeWidget::item:hover {
                background-color: #334155;
            }
            QTreeWidget::item:selected {
                background-color: #3b82f6;
                color: white;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: url(none);
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: url(none);
            }
        """)

        # إضافة الأنظمة الرئيسية والفرعية
        self.populate_menu_tree()

        # ربط إشارة النقر
        self.menu_tree.itemClicked.connect(self.handle_tree_item_click)

        menu_layout.addWidget(self.menu_tree)

        return menu_frame

    def populate_menu_tree(self):
        """ملء القائمة الشجرية بالأنظمة والشاشات الفرعية"""
        # الأنظمة الأساسية
        basic_systems = QTreeWidgetItem(self.menu_tree, ["الأنظمة الأساسية"])
        basic_systems.setExpanded(True)

        # إدارة الشحنات
        shipments_item = QTreeWidgetItem(basic_systems, ["إدارة الشحنات"])
        QTreeWidgetItem(shipments_item, ["قائمة الشحنات"])
        QTreeWidgetItem(shipments_item, ["إضافة شحنة جديدة"])
        QTreeWidgetItem(shipments_item, ["تتبع الشحنات"])
        QTreeWidgetItem(shipments_item, ["تقارير الشحنات"])

        # إدارة الأصناف
        items_item = QTreeWidgetItem(basic_systems, ["إدارة الأصناف"])
        QTreeWidgetItem(items_item, ["قائمة الأصناف"])
        QTreeWidgetItem(items_item, ["إضافة صنف جديد"])
        QTreeWidgetItem(items_item, ["مجموعات الأصناف"])
        QTreeWidgetItem(items_item, ["وحدات القياس"])

        # إدارة الموردين
        suppliers_item = QTreeWidgetItem(basic_systems, ["إدارة الموردين"])
        QTreeWidgetItem(suppliers_item, ["قائمة الموردين"])
        QTreeWidgetItem(suppliers_item, ["إضافة مورد جديد"])
        QTreeWidgetItem(suppliers_item, ["تقييم الموردين"])
        QTreeWidgetItem(suppliers_item, ["تقارير الموردين"])

        # إدارة الحوالات المتقدمة
        remittances_item = QTreeWidgetItem(basic_systems, ["إدارة الحوالات"])
        QTreeWidgetItem(remittances_item, ["قائمة الحوالات"])
        QTreeWidgetItem(remittances_item, ["طلب حوالة"])
        QTreeWidgetItem(remittances_item, ["إنشاء حوالة جديدة"])
        QTreeWidgetItem(remittances_item, ["تتبع الحوالات"])
        QTreeWidgetItem(remittances_item, ["حسابات الموردين"])
        QTreeWidgetItem(remittances_item, ["إدارة البنوك"])

        # إدارة قاعدة البيانات
        database_item = QTreeWidgetItem(basic_systems, ["إدارة قاعدة البيانات"])
        QTreeWidgetItem(database_item, ["إعدادات قاعدة البيانات"])
        QTreeWidgetItem(database_item, ["صيانة وتحسين"])
        QTreeWidgetItem(database_item, ["النسخ الاحتياطية"])
        QTreeWidgetItem(database_item, ["مراقبة الأداء"])
        QTreeWidgetItem(database_item, ["الأمان والحماية"])

        # الإعدادات
        settings_item = QTreeWidgetItem(basic_systems, ["الإعدادات"])
        QTreeWidgetItem(settings_item, ["إعدادات عامة"])
        QTreeWidgetItem(settings_item, ["إعدادات قاعدة البيانات"])
        QTreeWidgetItem(settings_item, ["إعدادات التقارير"])

        # الأنظمة المالية
        financial_systems = QTreeWidgetItem(self.menu_tree, ["الأنظمة المالية"])

        costs_item = QTreeWidgetItem(financial_systems, ["إدارة التكاليف"])
        QTreeWidgetItem(costs_item, ["تكاليف الشحن"])
        QTreeWidgetItem(costs_item, ["تكاليف إضافية"])
        QTreeWidgetItem(costs_item, ["تقارير التكاليف"])

        customs_item = QTreeWidgetItem(financial_systems, ["الجمارك والرسوم"])
        QTreeWidgetItem(customs_item, ["الرسوم الجمركية"])
        QTreeWidgetItem(customs_item, ["الإقرارات الجمركية"])
        QTreeWidgetItem(customs_item, ["تقارير جمركية"])

        # التقارير والإحصائيات
        reports_systems = QTreeWidgetItem(self.menu_tree, ["التقارير والإحصائيات"])

        reports_item = QTreeWidgetItem(reports_systems, ["التقارير العامة"])
        QTreeWidgetItem(reports_item, ["تقارير يومية"])
        QTreeWidgetItem(reports_item, ["تقارير شهرية"])
        QTreeWidgetItem(reports_item, ["تقارير سنوية"])

        stats_item = QTreeWidgetItem(reports_systems, ["الإحصائيات"])
        QTreeWidgetItem(stats_item, ["إحصائيات الشحنات"])
        QTreeWidgetItem(stats_item, ["إحصائيات الموردين"])
        QTreeWidgetItem(stats_item, ["إحصائيات مالية"])

        # الإدارة والأمان
        admin_systems = QTreeWidgetItem(self.menu_tree, ["الإدارة والأمان"])

        users_item = QTreeWidgetItem(admin_systems, ["إدارة المستخدمين"])
        QTreeWidgetItem(users_item, ["قائمة المستخدمين"])
        QTreeWidgetItem(users_item, ["الأدوار والصلاحيات"])
        QTreeWidgetItem(users_item, ["سجل النشاطات"])

        backup_item = QTreeWidgetItem(admin_systems, ["النسخ الاحتياطي"])
        QTreeWidgetItem(backup_item, ["إنشاء نسخة احتياطية"])
        QTreeWidgetItem(backup_item, ["استعادة البيانات"])
        QTreeWidgetItem(backup_item, ["جدولة النسخ"])

        # الأدوات المساعدة
        tools_systems = QTreeWidgetItem(self.menu_tree, ["الأدوات المساعدة"])

        tracking_item = QTreeWidgetItem(tools_systems, ["التتبع والخرائط"])
        QTreeWidgetItem(tracking_item, ["تتبع مباشر"])
        QTreeWidgetItem(tracking_item, ["خرائط الشحنات"])
        QTreeWidgetItem(tracking_item, ["🌍 خرائط حقيقية"])
        QTreeWidgetItem(tracking_item, ["🔧 اختبار الخرائط"])
        QTreeWidgetItem(tracking_item, ["التنبيهات"])

        help_item = QTreeWidgetItem(tools_systems, ["المساعدة والدعم"])
        QTreeWidgetItem(help_item, ["دليل المستخدم"])
        QTreeWidgetItem(help_item, ["الدعم الفني"])
        QTreeWidgetItem(help_item, ["حول البرنامج"])

    def handle_tree_item_click(self, item, column):
        """معالجة النقر على عناصر القائمة الشجرية"""
        item_text = item.text(0)
        parent_text = item.parent().text(0) if item.parent() else None

        # الأنظمة الأساسية
        if parent_text == "إدارة الشحنات":
            if item_text == "قائمة الشحنات" or item_text == "إدارة الشحنات":
                self.open_shipments_window()
            elif item_text == "إضافة شحنة جديدة":
                self.open_shipments_window()  # يمكن تمرير معامل لفتح نافذة الإضافة
            elif item_text == "تتبع الشحنات":
                self.open_shipments_window()  # يمكن تمرير معامل لفتح نافذة التتبع
            elif item_text == "تقارير الشحنات":
                QMessageBox.information(self, "تقارير الشحنات", "نظام تقارير الشحنات قيد التطوير")

        elif parent_text == "إدارة الأصناف":
            if item_text == "قائمة الأصناف" or item_text == "إدارة الأصناف":
                self.open_items_window()
            elif item_text == "إضافة صنف جديد":
                self.open_items_window()
            elif item_text == "مجموعات الأصناف":
                QMessageBox.information(self, "مجموعات الأصناف", "إدارة مجموعات الأصناف قيد التطوير")
            elif item_text == "وحدات القياس":
                QMessageBox.information(self, "وحدات القياس", "إدارة وحدات القياس قيد التطوير")

        elif parent_text == "إدارة الموردين":
            if item_text == "قائمة الموردين" or item_text == "إدارة الموردين":
                self.open_suppliers_window()
            elif item_text == "إضافة مورد جديد":
                self.open_suppliers_window()
            elif item_text == "تقييم الموردين":
                QMessageBox.information(self, "تقييم الموردين", "نظام تقييم الموردين قيد التطوير")
            elif item_text == "تقارير الموردين":
                QMessageBox.information(self, "تقارير الموردين", "تقارير الموردين قيد التطوير")

        elif parent_text == "إدارة الحوالات":
            if item_text == "قائمة الحوالات" or item_text == "إدارة الحوالات":
                self.open_remittances_window()
            elif item_text == "طلب حوالة":
                self.open_remittance_request_window()
            elif item_text == "إنشاء حوالة جديدة":
                self.open_new_remittance_dialog()
            elif item_text == "تتبع الحوالات":
                self.open_remittances_window()  # يمكن تمرير معامل لفتح تبويب التتبع
            elif item_text == "حسابات الموردين":
                self.open_supplier_accounts_management_window()
            elif item_text == "إدارة البنوك":
                self.open_banks_management_window()

        elif parent_text == "إدارة قاعدة البيانات":
            if item_text == "إعدادات قاعدة البيانات" or item_text == "إدارة قاعدة البيانات":
                self.open_database_settings_window()
            elif item_text == "صيانة وتحسين":
                self.open_database_settings_window()  # فتح تبويب الصيانة
            elif item_text == "النسخ الاحتياطية":
                self.open_database_settings_window()  # فتح تبويب النسخ الاحتياطية
            elif item_text == "مراقبة الأداء":
                self.open_database_settings_window()  # فتح تبويب الأداء
            elif item_text == "الأمان والحماية":
                self.open_database_settings_window()  # فتح تبويب الأمان

        elif parent_text == "الإعدادات":
            if item_text == "إعدادات عامة" or item_text == "الإعدادات":
                self.open_settings_window()
            elif item_text == "إعدادات قاعدة البيانات":
                self.open_database_settings_dialog()
            elif item_text == "إعدادات التقارير":
                QMessageBox.information(self, "إعدادات التقارير", "إعدادات التقارير قيد التطوير")

        # الأدوات المساعدة
        elif parent_text == "التتبع والخرائط":
            if item_text == "تتبع مباشر":
                self.open_live_tracking_window()
            elif item_text == "خرائط الشحنات":
                self.open_shipment_maps_window()
            elif item_text == "🌍 خرائط حقيقية":
                self.open_real_map_window()
            elif item_text == "🔧 اختبار الخرائط":
                self.open_simple_map_window()
            elif item_text == "التنبيهات":
                QMessageBox.information(self, "التنبيهات", "نظام التنبيهات قيد التطوير")

        # الأنظمة الأخرى (قيد التطوير)
        else:
            if item.parent() and not item.childCount():  # عنصر فرعي وليس له أطفال
                QMessageBox.information(self, item_text, f"{item_text} قيد التطوير")

    def create_quick_stats_area(self):
        """إنشاء منطقة الإحصائيات السريعة"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
            }
        """)

        stats_layout = QVBoxLayout(stats_frame)
        stats_layout.setSpacing(15)
        stats_layout.setContentsMargins(20, 20, 20, 20)

        # عنوان القسم
        stats_title = QLabel("الإحصائيات السريعة")
        stats_title.setAlignment(Qt.AlignCenter)
        stats_font = QFont()
        stats_font.setPointSize(16)
        stats_font.setBold(True)
        stats_title.setFont(stats_font)
        stats_title.setStyleSheet("color: #1e293b; padding: 10px;")
        stats_layout.addWidget(stats_title)

        # شبكة الإحصائيات
        stats_grid_layout = QGridLayout()
        stats_grid_layout.setSpacing(15)

        # بيانات الإحصائيات
        stats_data = [
            ("الشحنات النشطة", "210", "#3b82f6", 0, 0),
            ("الموردين المسجلين", "45", "#10b981", 0, 1),
            ("الأصناف المتاحة", "1,250", "#f59e0b", 0, 2),
            ("الشحنات المكتملة", "1,890", "#8b5cf6", 1, 0),
            ("القيمة الإجمالية", "$2.5M", "#ef4444", 1, 1),
            ("متوسط وقت التسليم", "12 يوم", "#06b6d4", 1, 2)
        ]

        for title, value, color, row, col in stats_data:
            stat_card = self.create_stat_card(title, value, color)
            stats_grid_layout.addWidget(stat_card, row, col)

        stats_layout.addLayout(stats_grid_layout)
        return stats_frame

    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية صغيرة"""
        card = QFrame()
        card.setFixedHeight(80)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 6px;
                border: none;
            }}
        """)

        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(5)
        card_layout.setContentsMargins(15, 10, 15, 10)

        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_font = QFont()
        value_font.setPointSize(18)
        value_font.setBold(True)
        value_label.setFont(value_font)
        value_label.setStyleSheet("color: white;")

        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(10)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: white;")

        card_layout.addWidget(value_label)
        card_layout.addWidget(title_label)

        return card

    def create_news_updates_area(self):
        """إنشاء منطقة الأخبار والتحديثات"""
        news_frame = QFrame()
        news_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
            }
        """)

        news_layout = QVBoxLayout(news_frame)
        news_layout.setSpacing(15)
        news_layout.setContentsMargins(20, 20, 20, 20)

        # عنوان القسم
        news_title = QLabel("آخر التحديثات والأخبار")
        news_title.setAlignment(Qt.AlignCenter)
        news_font = QFont()
        news_font.setPointSize(16)
        news_font.setBold(True)
        news_title.setFont(news_font)
        news_title.setStyleSheet("color: #1e293b; padding: 10px;")
        news_layout.addWidget(news_title)

        # منطقة النص
        news_text = QTextEdit()
        news_text.setMaximumHeight(200)
        news_text.setReadOnly(True)
        news_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 15px;
                background-color: #f8fafc;
                font-size: 13px;
                line-height: 1.5;
            }
        """)

        # محتوى الأخبار
        news_content = """
        <div style="text-align: right; direction: rtl;">
        <h3 style="color: #1e293b;">🎉 مرحباً بك في نظام ProShipment Plus ERP</h3>

        <p><strong>✨ الميزات الجديدة:</strong></p>
        <ul>
        <li>نظام تتبع الشحنات المتقدم مع الخرائط التفاعلية</li>
        <li>إدارة شاملة للموردين مع نظام التقييم</li>
        <li>إدارة متطورة للأصناف والمخزون</li>
        <li>تقارير وإحصائيات تفصيلية</li>
        </ul>

        <p><strong>🔧 قيد التطوير:</strong></p>
        <ul>
        <li>نظام إدارة التكاليف والمصروفات</li>
        <li>نظام الجمارك والرسوم الحكومية</li>
        <li>التطبيق المحمول للمتابعة</li>
        <li>نظام الإشعارات والتنبيهات</li>
        </ul>

        <p style="color: #3b82f6;"><strong>💡 نصيحة:</strong> استخدم القائمة الجانبية للوصول السريع لجميع الأنظمة والشاشات الفرعية.</p>
        </div>
        """

        news_text.setHtml(news_content)
        news_layout.addWidget(news_text)

        return news_frame

    def handle_menu_action(self, action):
        """معالجة إجراءات القائمة المحسنة"""
        # الأنظمة الأساسية المتاحة
        if action == "shipments":
            self.open_shipments_window()
        elif action == "items":
            self.open_items_window()
        elif action == "suppliers":
            self.open_suppliers_window()
        elif action == "settings":
            self.open_settings_window()

        # الأنظمة المالية
        elif action == "costs":
            QMessageBox.information(self, "إدارة التكاليف", "نظام إدارة التكاليف والمصروفات قيد التطوير")
        elif action == "customs":
            QMessageBox.information(self, "الجمارك والرسوم", "نظام الجمارك والرسوم الحكومية قيد التطوير")
        elif action == "invoices":
            QMessageBox.information(self, "الفواتير", "نظام إدارة الفواتير والمدفوعات قيد التطوير")
        elif action == "financial_analysis":
            QMessageBox.information(self, "التحليل المالي", "نظام التحليل المالي والتنبؤات قيد التطوير")

        # التقارير والإحصائيات
        elif action == "reports":
            QMessageBox.information(self, "التقارير العامة", "نظام التقارير الشاملة قيد التطوير")
        elif action == "statistics":
            QMessageBox.information(self, "الإحصائيات", "نظام الإحصائيات والتحليلات قيد التطوير")
        elif action == "shipment_reports":
            QMessageBox.information(self, "تقارير الشحنات", "تقارير مفصلة عن الشحنات قيد التطوير")
        elif action == "financial_reports":
            QMessageBox.information(self, "التقارير المالية", "التقارير المالية والمحاسبية قيد التطوير")

        # الإدارة والأمان
        elif action == "users":
            QMessageBox.information(self, "إدارة المستخدمين", "نظام إدارة المستخدمين والصلاحيات قيد التطوير")
        elif action == "security":
            QMessageBox.information(self, "الأمان والصلاحيات", "نظام الأمان وإدارة الصلاحيات قيد التطوير")
        elif action == "backup":
            QMessageBox.information(self, "النسخ الاحتياطي", "نظام النسخ الاحتياطي واستعادة البيانات قيد التطوير")
        elif action == "maintenance":
            QMessageBox.information(self, "صيانة النظام", "أدوات صيانة وتحسين أداء النظام قيد التطوير")

        # الأدوات المساعدة
        elif action == "mobile_app":
            QMessageBox.information(self, "التطبيق المحمول", "تطبيق الهاتف المحمول للمتابعة قيد التطوير")
        elif action == "tracking_maps":
            QMessageBox.information(self, "الخرائط والتتبع", "نظام تتبع الشحنات على الخرائط قيد التطوير")
        elif action == "notifications":
            QMessageBox.information(self, "الإشعارات", "نظام الإشعارات والتنبيهات قيد التطوير")
        elif action == "help":
            QMessageBox.information(self, "المساعدة والدعم", "مركز المساعدة والدعم الفني قيد التطوير")







    def create_sidebar_item(self, icon, text, callback):
        """إنشاء عنصر في الشريط الجانبي"""
        item_frame = QFrame()
        item_frame.setCursor(Qt.PointingHandCursor)
        # إزالة أنماط عنصر الشريط الجانبي

        item_layout = QHBoxLayout(item_frame)
        item_layout.setSpacing(15)
        item_layout.setContentsMargins(15, 10, 15, 10)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_font = QFont()
        icon_font.setPointSize(16)
        icon_label.setFont(icon_font)
        # إزالة أنماط أيقونة البطاقة
        icon_label.setMinimumWidth(25)

        # النص
        text_label = QLabel(text)
        text_font = QFont()
        text_font.setPointSize(13)
        text_font.setWeight(QFont.Medium)
        text_label.setFont(text_font)
        text_label.setStyleSheet("""
            color: white;
            background: transparent;
            letter-spacing: 0.5px;
        """)

        item_layout.addWidget(icon_label)
        item_layout.addWidget(text_label)
        item_layout.addStretch()

        # ربط النقر بالوظيفة
        def on_click():
            if callback:
                callback()

        item_frame.mousePressEvent = lambda event: on_click()

        return item_frame

    def create_user_info_section(self):
        """إنشاء قسم معلومات المستخدم"""
        user_frame = QFrame()
        user_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding: 15px;
            }
        """)

        user_layout = QVBoxLayout(user_frame)
        user_layout.setSpacing(8)
        user_layout.setContentsMargins(15, 12, 15, 12)

        # اسم المستخدم
        username_label = QLabel("👤 المدير العام")
        username_font = QFont()
        username_font.setPointSize(12)
        username_font.setBold(True)
        username_label.setFont(username_font)
        username_label.setStyleSheet("color: white; background: transparent;")

        # حالة الاتصال
        status_label = QLabel("🟢 متصل")
        status_font = QFont()
        status_font.setPointSize(10)
        status_label.setFont(status_font)
        status_label.setStyleSheet("color: rgba(255, 255, 255, 0.8); background: transparent;")

        user_layout.addWidget(username_label)
        user_layout.addWidget(status_label)

        return user_frame

    def create_enhanced_header(self, layout):
        """إنشاء الهيدر الرئيسي المتقدم"""
        header_frame = QFrame()
        header_frame.setMinimumHeight(180)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(102, 126, 234, 0.95),
                    stop:0.3 rgba(118, 75, 162, 0.95),
                    stop:0.7 rgba(240, 147, 251, 0.95),
                    stop:1 rgba(245, 87, 108, 0.95));
                border-radius: 25px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(20px);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(15)
        header_layout.setContentsMargins(40, 30, 40, 30)

        # العنوان الرئيسي المطور
        title_label = QLabel("🚢 ProShipment Pro • الجيل الثاني")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(28)
        title_font.setBold(True)
        title_font.setFamily("Arial")
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                background: transparent;
                padding: 20px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
                letter-spacing: 2px;
            }
        """)
        header_layout.addWidget(title_label)

        # العنوان الفرعي المحسن
        subtitle_label = QLabel("نظام إدارة الشحنات والتوريد المتطور • واجهة عصرية • أداء استثنائي")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_font = QFont()
        subtitle_font.setPointSize(14)
        subtitle_font.setWeight(QFont.Medium)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.95);
                background: transparent;
                padding: 12px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
                letter-spacing: 0.8px;
            }
        """)
        header_layout.addWidget(subtitle_label)

        # إضافة معلومات إضافية
        info_label = QLabel("✨ تصميم متطور • تفاعل سلس • إدارة ذكية")
        info_label.setAlignment(Qt.AlignCenter)
        info_font = QFont()
        info_font.setPointSize(11)
        info_label.setFont(info_font)
        info_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                background: transparent;
                padding: 8px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            }
        """)
        header_layout.addWidget(info_label)

        # إضافة خط فاصل مع تأثير متدرج
        separator = QFrame()
        separator.setFixedHeight(2)
        separator.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:0.5 rgba(255, 255, 255, 0.6), stop:1 transparent);
                border: none;
                margin: 10px 50px;
            }
        """)
        header_layout.addWidget(separator)

        layout.addWidget(header_frame)

    def create_stats_section(self, layout):
        """إنشاء قسم الإحصائيات السريعة المتقدم"""
        stats_frame = QFrame()
        stats_frame.setMinimumHeight(140)
        stats_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
        """)

        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setSpacing(25)
        stats_layout.setContentsMargins(30, 25, 30, 25)

        # بيانات الإحصائيات المحدثة
        stats_data = [
            ("📦", "إجمالي الشحنات", "1,234", "#667eea", "#764ba2"),
            ("🏭", "الموردين النشطين", "89", "#f093fb", "#f5576c"),
            ("📋", "الأصناف المسجلة", "5,678", "#4facfe", "#00f2fe"),
            ("⏱️", "الشحنات المعلقة", "23", "#43e97b", "#38f9d7")
        ]

        for icon, title, value, color1, color2 in stats_data:
            stat_widget = self.create_stat_widget_advanced(icon, title, value, color1, color2)
            stats_layout.addWidget(stat_widget)

        layout.addWidget(stats_frame)

    def create_stat_widget_advanced(self, icon, title, value, color1, color2):
        """إنشاء ويدجت إحصائية متقدمة"""
        widget = QFrame()
        widget.setMinimumHeight(100)
        widget.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                border-radius: 16px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            }}
            QFrame:hover {{
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            }}
        """)

        layout = QVBoxLayout(widget)
        layout.setSpacing(10)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(20, 15, 20, 15)

        # الأيقونة مع تأثيرات
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_font = QFont()
        icon_font.setPointSize(28)
        icon_label.setFont(icon_font)
        icon_label.setStyleSheet("""
            color: white;
            background: transparent;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        """)
        layout.addWidget(icon_label)

        # القيمة مع تأثيرات متقدمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_font = QFont()
        value_font.setPointSize(24)
        value_font.setBold(True)
        value_font.setFamily("Arial")
        value_label.setFont(value_font)
        value_label.setStyleSheet("""
            color: white;
            background: transparent;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
            font-weight: 700;
        """)
        layout.addWidget(value_label)

        # العنوان مع تأثيرات
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        title_font = QFont()
        title_font.setPointSize(11)
        title_font.setWeight(QFont.Medium)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.95);
            background: transparent;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.5px;
        """)
        layout.addWidget(title_label)

        return widget

    def darken_color(self, color):
        """تغميق اللون للتأثير عند التمرير"""
        color_map = {
            "#3498db": "#2980b9",
            "#2ecc71": "#27ae60",
            "#e74c3c": "#c0392b",
            "#f39c12": "#e67e22"
        }
        return color_map.get(color, color)

    def create_main_buttons_section(self, layout):
        """إنشاء قسم الأزرار الرئيسية المتقدم"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
        """)

        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(25)
        buttons_layout.setContentsMargins(30, 25, 30, 25)

        # عنوان القسم مع تأثيرات متقدمة
        section_title = QLabel("🎛️ الأنظمة الرئيسية")
        section_title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(22)
        title_font.setBold(True)
        title_font.setFamily("Arial")
        section_title.setFont(title_font)
        section_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: transparent;
                padding: 15px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
                letter-spacing: 1px;
            }
        """)
        buttons_layout.addWidget(section_title)

        # خط فاصل متدرج
        separator = QFrame()
        separator.setFixedHeight(2)
        separator.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:0.5 rgba(44, 62, 80, 0.3), stop:1 transparent);
                border: none;
                margin: 5px 40px;
            }
        """)
        buttons_layout.addWidget(separator)

        # شبكة الأزرار مع مسافات محسنة
        grid_layout = QGridLayout()
        grid_layout.setSpacing(30)
        grid_layout.setContentsMargins(15, 15, 15, 15)

        # إنشاء أزرار الأنظمة الرئيسية
        self.create_system_buttons(grid_layout)

        buttons_layout.addLayout(grid_layout)
        layout.addWidget(buttons_frame)

    def create_quick_info_section(self, layout):
        """إنشاء قسم المعلومات السريعة المحسن"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.12);
            }
        """)

        info_layout = QVBoxLayout(info_frame)
        info_layout.setSpacing(25)
        info_layout.setContentsMargins(35, 30, 35, 30)

        # عنوان القسم مع تصميم متطور
        section_title = QLabel("⚡ لوحة المعلومات السريعة")
        section_title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(22)
        title_font.setBold(True)
        title_font.setFamily("Arial")
        section_title.setFont(title_font)
        section_title.setStyleSheet("""
            QLabel {
                color: #1a202c;
                background: transparent;
                padding: 20px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
                letter-spacing: 1px;
            }
        """)
        info_layout.addWidget(section_title)

        # خط فاصل متدرج محسن
        separator = QFrame()
        separator.setFixedHeight(3)
        separator.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent,
                    stop:0.2 rgba(102, 126, 234, 0.4),
                    stop:0.5 rgba(118, 75, 162, 0.6),
                    stop:0.8 rgba(240, 147, 251, 0.4),
                    stop:1 transparent);
                border: none;
                border-radius: 2px;
                margin: 10px 40px;
            }
        """)
        info_layout.addWidget(separator)

        # شبكة المعلومات السريعة
        info_grid_layout = QGridLayout()
        info_grid_layout.setSpacing(20)
        info_grid_layout.setContentsMargins(10, 10, 10, 10)

        # بيانات المعلومات السريعة
        quick_info_data = [
            ("🏢", "معلومات الشركة", "عرض تفاصيل الشركة والفروع", "#667eea", "#764ba2", 0, 0),
            ("📊", "التقارير السريعة", "الوصول السريع للتقارير المهمة", "#f093fb", "#f5576c", 0, 1),
            ("⚙️", "الإعدادات السريعة", "تخصيص إعدادات النظام", "#43e97b", "#38f9d7", 1, 0),
            ("🔔", "الإشعارات", "عرض الإشعارات والتنبيهات", "#4facfe", "#00f2fe", 1, 1),
            ("📈", "الإحصائيات المتقدمة", "تحليلات مفصلة للأداء", "#ffecd2", "#fcb69f", 2, 0),
            ("🎯", "المهام السريعة", "الوصول للمهام الأكثر استخداماً", "#a8edea", "#fed6e3", 2, 1)
        ]

        for icon, title, description, color1, color2, row, col in quick_info_data:
            info_card = self.create_quick_info_card(icon, title, description, color1, color2)
            info_grid_layout.addWidget(info_card, row, col)

        info_layout.addLayout(info_grid_layout)
        layout.addWidget(info_frame)

    def create_quick_info_card(self, icon, title, description, color1, color2):
        """إنشاء بطاقة معلومات سريعة"""
        card_frame = QFrame()
        card_frame.setMinimumSize(280, 120)
        card_frame.setCursor(Qt.PointingHandCursor)
        card_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                border-radius: 18px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color2}, stop:1 {color1});
                transform: translateY(-3px);
                box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
                border-color: rgba(255, 255, 255, 0.4);
            }}
        """)

        card_layout = QVBoxLayout(card_frame)
        card_layout.setSpacing(12)
        card_layout.setContentsMargins(20, 15, 20, 15)
        card_layout.setAlignment(Qt.AlignCenter)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_font = QFont()
        icon_font.setPointSize(24)
        icon_label.setFont(icon_font)
        icon_label.setStyleSheet("""
            background: transparent;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        """)

        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        title_font = QFont()
        title_font.setPointSize(13)
        title_font.setBold(True)
        title_font.setFamily("Arial")
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            background: transparent;
            color: white;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.5px;
        """)

        # الوصف
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        desc_font = QFont()
        desc_font.setPointSize(10)
        desc_font.setWeight(QFont.Medium)
        desc_label.setFont(desc_font)
        desc_label.setStyleSheet("""
            background: transparent;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        """)

        card_layout.addWidget(icon_label)
        card_layout.addWidget(title_label)
        card_layout.addWidget(desc_label)

        return card_frame

    # دوال الشريط الجانبي للوصول السريع
    def show_dashboard(self):
        """عرض لوحة التحكم الرئيسية"""
        print("عرض لوحة التحكم الرئيسية")

    def open_customers(self):
        """فتح نافذة إدارة العملاء"""
        print("فتح نافذة إدارة العملاء")

    def open_reports(self):
        """فتح نافذة التقارير"""
        print("فتح نافذة التقارير")

    def show_help(self):
        """عرض نافذة المساعدة والدعم"""
        print("عرض نافذة المساعدة والدعم")

    def logout(self):
        """تسجيل الخروج من النظام"""
        reply = QMessageBox.question(self, 'تأكيد الخروج',
                                   'هل تريد تسجيل الخروج من النظام؟',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.close()

    def create_system_buttons(self, layout):
        """إنشاء أزرار الأنظمة الرئيسية"""

        # بيانات الأزرار مع الألوان المتدرجة المتقدمة
        buttons_data = [
            ("⚙️", "إعدادات", "الإعدادات العامة للنظام", "إدارة إعدادات النظام والشركة والمستخدمين", self.open_settings, 0, 0, "#667eea", "#764ba2"),
            ("📦", "أصناف", "إدارة الأصناف والمخزون", "إدارة الأصناف ووحدات القياس والمجموعات", self.open_items, 0, 1, "#4facfe", "#00f2fe"),
            ("🏭", "موردين", "إدارة الموردين والعملاء", "إدارة بيانات الموردين والعمليات والتقارير", self.open_suppliers, 0, 2, "#43e97b", "#38f9d7"),
            ("🚢", "شحنات", "إدارة الشحنات والتتبع", "نظام إدارة وتتبع الشحنات المتكامل", self.open_shipments, 1, 0, "#f093fb", "#f5576c"),
            ("🏛️", "جمارك", "الإدخالات الجمركية", "نظام الإدخالات الجمركية (قيد التطوير)", self.show_under_development, 1, 1, "#ffecd2", "#fcb69f"),
            ("💰", "تكاليف", "إدارة التكاليف والمصروفات", "نظام إدارة التكاليف (قيد التطوير)", self.show_under_development, 1, 2, "#a8edea", "#fed6e3"),
        ]

        for icon, title, subtitle, tooltip, callback, row, col, color1, color2 in buttons_data:
            button = self.create_main_button_advanced(icon, title, subtitle, tooltip, callback, color1, color2)
            layout.addWidget(button, row, col)
    
    def create_main_button_advanced(self, icon, title, subtitle, tooltip, callback, color1, color2):
        """إنشاء زر رئيسي متقدم"""
        button = QPushButton()
        button.setToolTip(tooltip)
        button.setMinimumSize(350, 160)
        button.clicked.connect(callback)
        button.setCursor(Qt.PointingHandCursor)

        # تطبيق الستايل المتقدم
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 20px;
                color: white;
                font-weight: bold;
                text-align: center;
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            }}
            QPushButton:hover {{
                transform: translateY(-3px);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
                border-color: rgba(255, 255, 255, 0.4);
            }}
            QPushButton:pressed {{
                transform: translateY(-1px);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }}
        """)

        # إنشاء التخطيط الداخلي للزر
        button_layout = QVBoxLayout(button)
        button_layout.setSpacing(15)
        button_layout.setAlignment(Qt.AlignCenter)
        button_layout.setContentsMargins(25, 20, 25, 20)

        # الأيقونة مع تأثيرات متقدمة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_font = QFont()
        icon_font.setPointSize(32)
        icon_label.setFont(icon_font)
        icon_label.setStyleSheet("""
            background: transparent;
            color: white;
            padding: 8px;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
        """)

        # العنوان الرئيسي
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_font.setFamily("Arial")
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            background: transparent;
            color: white;
            padding: 5px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
        """)

        # العنوان الفرعي
        subtitle_label = QLabel(subtitle)
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setWordWrap(True)
        subtitle_font = QFont()
        subtitle_font.setPointSize(11)
        subtitle_font.setWeight(QFont.Medium)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("""
            background: transparent;
            color: rgba(255, 255, 255, 0.9);
            padding: 3px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        """)

        # إضافة العناصر للتخطيط
        button_layout.addWidget(icon_label)
        button_layout.addWidget(title_label)
        button_layout.addWidget(subtitle_label)

        return button
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        # إجراءات قائمة ملف
        new_action = QAction("جديد", self)
        new_action.setShortcut("Ctrl+N")
        file_menu.addAction(new_action)
        
        open_action = QAction("فتح", self)
        open_action.setShortcut("Ctrl+O")
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        backup_action = QAction("نسخة احتياطية", self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة الأنظمة
        systems_menu = menubar.addMenu("الأنظمة")
        
        settings_action = QAction("الإعدادات العامة", self)
        settings_action.triggered.connect(self.open_settings)
        systems_menu.addAction(settings_action)
        
        items_action = QAction("إدارة الأصناف", self)
        items_action.triggered.connect(self.open_items)
        systems_menu.addAction(items_action)
        
        suppliers_action = QAction("إدارة الموردين", self)
        suppliers_action.triggered.connect(self.open_suppliers)
        systems_menu.addAction(suppliers_action)

        shipments_action = QAction("إدارة الشحنات", self)
        shipments_action.triggered.connect(self.open_shipments)
        systems_menu.addAction(shipments_action)

        # قائمة الأدوات
        tools_menu = menubar.addMenu("أدوات")

        shipping_enhancement_action = QAction("🔧 تحسين بيانات شركات الشحن", self)
        shipping_enhancement_action.setToolTip("تحليل وتصحيح أسماء شركات الشحن في قاعدة البيانات")
        shipping_enhancement_action.triggered.connect(self.open_shipping_enhancement)
        tools_menu.addAction(shipping_enhancement_action)

        # تم حذف نظام تعبئة البيانات المفقودة لتجنب التداخل مع نظام الشحنات
        # data_filler_action = QAction("📝 تعبئة البيانات المفقودة", self)
        # data_filler_action.setToolTip("البحث وتعبئة الحقول الفارغة في الشحنات بناءً على البيانات المشابهة")
        # data_filler_action.triggered.connect(self.open_data_filler)
        # tools_menu.addAction(data_filler_action)

        # قائمة العرض والثيمات
        view_menu = menubar.addMenu("العرض")

        # قائمة فرعية للثيمات
        themes_menu = view_menu.addMenu("الثيمات")

        modern_theme_action = QAction("الثيم الحديث", self)
        modern_theme_action.triggered.connect(lambda: style_manager.load_theme("modern"))
        themes_menu.addAction(modern_theme_action)

        dark_theme_action = QAction("الثيم المظلم", self)
        dark_theme_action.triggered.connect(style_manager.apply_dark_theme)
        themes_menu.addAction(dark_theme_action)

        light_theme_action = QAction("الثيم الفاتح", self)
        light_theme_action.triggered.connect(style_manager.apply_light_theme)
        themes_menu.addAction(light_theme_action)

        themes_menu.addSeparator()

        reset_theme_action = QAction("إعادة تعيين الثيم", self)
        reset_theme_action.triggered.connect(style_manager.reset_style)
        themes_menu.addAction(reset_theme_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")

        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # أزرار شريط الأدوات
        settings_action = QAction("الإعدادات", self)
        settings_action.triggered.connect(self.open_settings)
        toolbar.addAction(settings_action)
        
        items_action = QAction("الأصناف", self)
        items_action.triggered.connect(self.open_items)
        toolbar.addAction(items_action)
        
        suppliers_action = QAction("الموردين", self)
        suppliers_action.triggered.connect(self.open_suppliers)
        toolbar.addAction(suppliers_action)

        shipments_action = QAction("الشحنات", self)
        shipments_action.triggered.connect(self.open_shipments)
        toolbar.addAction(shipments_action)
        
        toolbar.addSeparator()
        
        backup_action = QAction("نسخة احتياطية", self)
        backup_action.triggered.connect(self.create_backup)
        toolbar.addAction(backup_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة المحسن"""
        status_bar = self.statusBar()

        # معلومات الحالة مع أيقونة
        self.status_label = QLabel("🟢 النظام جاهز")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                font-weight: bold;
                padding: 5px 10px;
            }
        """)
        status_bar.addWidget(self.status_label)

        # معلومات المستخدم مع أيقونة
        user_label = QLabel("👤 المستخدم: مدير النظام")
        user_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px 10px;
            }
        """)
        status_bar.addPermanentWidget(user_label)

        # التاريخ والوقت مع أيقونة
        from datetime import datetime
        self.date_label = QLabel(f"📅 {datetime.now().strftime('%Y/%m/%d - %H:%M:%S')}")
        self.date_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px 10px;
            }
        """)
        status_bar.addPermanentWidget(self.date_label)

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        from datetime import datetime
        current_time = datetime.now().strftime('%Y/%m/%d - %H:%M:%S')
        self.date_label.setText(f"📅 {current_time}")
    
    def open_settings_window(self):
        """فتح نافذة الإعدادات"""
        if self.settings_window is None or not self.settings_window.isVisible():
            self.settings_window = SettingsWindow(self)

        self.settings_window.show()
        self.settings_window.raise_()
        self.settings_window.activateWindow()

    def open_settings(self):
        """فتح نافذة الإعدادات - للتوافق مع الكود القديم"""
        self.open_settings_window()
    
    def open_items_window(self):
        """فتح نافذة إدارة الأصناف"""
        # التحقق من أن النافذة موجودة وصالحة
        try:
            if self.items_window is None or not self.items_window.isVisible():
                self.items_window = ItemsWindow(self)
                # ربط إشارة الإغلاق لتنظيف المرجع
                self.items_window.destroyed.connect(lambda: setattr(self, 'items_window', None))
        except RuntimeError:
            # النافذة محذوفة، إنشاء نافذة جديدة
            self.items_window = ItemsWindow(self)
            self.items_window.destroyed.connect(lambda: setattr(self, 'items_window', None))

        self.items_window.show()
        self.items_window.raise_()
        self.items_window.activateWindow()

    def open_items(self):
        """فتح نافذة إدارة الأصناف - للتوافق مع الكود القديم"""
        self.open_items_window()
    
    def open_suppliers_window(self):
        """فتح نافذة إدارة الموردين"""
        # التحقق من أن النافذة موجودة وصالحة
        try:
            if self.suppliers_window is None or not self.suppliers_window.isVisible():
                self.suppliers_window = SuppliersWindow(self)
                # ربط إشارة الإغلاق لتنظيف المرجع
                self.suppliers_window.destroyed.connect(lambda: setattr(self, 'suppliers_window', None))
        except RuntimeError:
            # النافذة محذوفة، إنشاء نافذة جديدة
            self.suppliers_window = SuppliersWindow(self)
            self.suppliers_window.destroyed.connect(lambda: setattr(self, 'suppliers_window', None))

        self.suppliers_window.show()
        self.suppliers_window.raise_()
        self.suppliers_window.activateWindow()

    def open_suppliers(self):
        """فتح نافذة إدارة الموردين - للتوافق مع الكود القديم"""
        self.open_suppliers_window()

    def open_shipments_window(self):
        """فتح نافذة إدارة الشحنات"""
        # التحقق من أن النافذة موجودة وصالحة
        try:
            if self.shipments_window is None or not self.shipments_window.isVisible():
                self.shipments_window = ShipmentsWindow(self)
                # ربط إشارة الإغلاق لتنظيف المرجع
                self.shipments_window.destroyed.connect(lambda: setattr(self, 'shipments_window', None))
        except RuntimeError:
            # النافذة محذوفة، إنشاء نافذة جديدة
            self.shipments_window = ShipmentsWindow(self)
            self.shipments_window.destroyed.connect(lambda: setattr(self, 'shipments_window', None))

        self.shipments_window.show()
        self.shipments_window.raise_()
        self.shipments_window.activateWindow()

    def open_shipments(self):
        """فتح نافذة إدارة الشحنات - للتوافق مع الكود القديم"""
        self.open_shipments_window()

    def open_shipping_enhancement(self):
        """فتح نافذة تحسين بيانات شركات الشحن"""
        try:
            dialog = ShippingDataEnhancementDialog(self)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة تحسين البيانات:\n{str(e)}"
            )

    # تم حذف دالة open_data_filler لتجنب التداخل مع نظام الشحنات
    # def open_data_filler(self):
    #     """فتح نافذة تعبئة البيانات المفقودة"""
    #     try:
    #         from .dialogs.shipment_data_filler_dialog import ShipmentDataFillerDialog
    #         dialog = ShipmentDataFillerDialog(parent=self)
    #         dialog.exec()
    #     except Exception as e:
    #         QMessageBox.critical(
    #             self,
    #             "خطأ",
    #             f"خطأ في فتح نافذة تعبئة البيانات المفقودة:\n{str(e)}"
    #         )

    def show_under_development(self):
        """عرض رسالة قيد التطوير"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "هذه الميزة قيد التطوير وستكون متاحة في الإصدارات القادمة."
        )
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        from ..database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        if db_manager.backup_database():
            QMessageBox.information(
                self,
                "نسخة احتياطية",
                "تم إنشاء النسخة الاحتياطية بنجاح"
            )
        else:
            QMessageBox.warning(
                self,
                "خطأ",
                "فشل في إنشاء النسخة الاحتياطية"
            )
    
    def open_live_tracking_window(self):
        """فتح نافذة التتبع المباشر"""
        try:
            if self.live_tracking_window is None:
                self.live_tracking_window = LiveTrackingWindow()
                # ربط إشارة الإغلاق لتنظيف المرجع
                self.live_tracking_window.destroyed.connect(
                    lambda: setattr(self, 'live_tracking_window', None)
                )

            self.live_tracking_window.show()
            self.live_tracking_window.raise_()
            self.live_tracking_window.activateWindow()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة التتبع المباشر:\n{str(e)}"
            )

    def open_shipment_maps_window(self):
        """فتح نافذة خرائط الشحنات"""
        try:
            if self.shipment_maps_window is None:
                self.shipment_maps_window = ShipmentMapsWindow()
                # ربط إشارة الإغلاق لتنظيف المرجع
                self.shipment_maps_window.destroyed.connect(
                    lambda: setattr(self, 'shipment_maps_window', None)
                )

            self.shipment_maps_window.show()
            self.shipment_maps_window.raise_()
            self.shipment_maps_window.activateWindow()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة خرائط الشحنات:\n{str(e)}"
            )

    def open_real_map_window(self):
        """فتح نافذة الخرائط الحقيقية"""
        try:
            print("محاولة فتح نافذة الخرائط الحقيقية...")

            if self.real_map_window is None:
                print("إنشاء نافذة جديدة...")
                self.real_map_window = RealMapWindow()
                print("تم إنشاء النافذة بنجاح")

                # ربط إشارة الإغلاق لتنظيف المرجع
                self.real_map_window.destroyed.connect(
                    lambda: setattr(self, 'real_map_window', None)
                )

            print("عرض النافذة في المقدمة...")
            # إظهار النافذة في المقدمة
            self.real_map_window.show()
            self.real_map_window.raise_()
            self.real_map_window.activateWindow()

            # التأكد من ظهور النافذة في المقدمة
            self.real_map_window.setWindowState(
                self.real_map_window.windowState() & ~Qt.WindowMinimized | Qt.WindowActive
            )

            # رفع النافذة للمقدمة مرة أخرى
            self.real_map_window.raise_()
            self.real_map_window.activateWindow()

            print("تم عرض النافذة في المقدمة بنجاح")

        except Exception as e:
            print(f"خطأ في فتح النافذة: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة الخرائط الحقيقية:\n{str(e)}"
            )

    def open_simple_map_window(self):
        """فتح نافذة اختبار الخرائط المبسطة"""
        try:
            print("محاولة فتح نافذة اختبار الخرائط...")

            if self.simple_map_window is None:
                print("إنشاء نافذة اختبار جديدة...")
                self.simple_map_window = SimpleRealMapWindow()
                print("تم إنشاء نافذة الاختبار بنجاح")

                # ربط إشارة الإغلاق لتنظيف المرجع
                self.simple_map_window.destroyed.connect(
                    lambda: setattr(self, 'simple_map_window', None)
                )

            print("عرض نافذة الاختبار في المقدمة...")
            # إظهار النافذة في المقدمة
            self.simple_map_window.show()
            self.simple_map_window.raise_()
            self.simple_map_window.activateWindow()

            # التأكد من ظهور النافذة في المقدمة
            self.simple_map_window.setWindowState(
                self.simple_map_window.windowState() & ~Qt.WindowMinimized | Qt.WindowActive
            )

            # رفع النافذة للمقدمة مرة أخرى
            self.simple_map_window.raise_()
            self.simple_map_window.activateWindow()

            print("تم عرض نافذة الاختبار في المقدمة بنجاح")

        except Exception as e:
            print(f"خطأ في فتح نافذة الاختبار: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة اختبار الخرائط:\n{str(e)}"
            )

    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(
            self,
            "حول البرنامج",
            """
            <h3>نظام إدارة الشحنات المتكامل</h3>
            <p><b>الإصدار:</b> 1.0.0</p>
            <p><b>الوصف:</b> نظام شامل لإدارة الشحنات والموردين والأصناف</p>
            <p><b>المطور:</b> فريق ProShipment</p>
            <p><b>حقوق النشر:</b> © 2024 جميع الحقوق محفوظة</p>
            """
        )

    def open_remittances_window(self):
        """فتح نافذة إدارة الحوالات"""
        try:
            from .remittances.remittances_window import RemittancesWindow

            # إغلاق النافذة السابقة إذا كانت موجودة
            if hasattr(self, 'remittances_window') and self.remittances_window:
                self.remittances_window.close()

            self.remittances_window = RemittancesWindow()

            # إعدادات النافذة لضمان ظهورها في المقدمة
            self.remittances_window.setWindowFlags(
                self.remittances_window.windowFlags() |
                Qt.WindowStaysOnTopHint
            )

            # توسيط النافذة وسط الشاشة
            self.center_window(self.remittances_window)

            # إحضار النافذة للمقدمة بطرق متعددة
            self.remittances_window.show()
            self.remittances_window.raise_()
            self.remittances_window.activateWindow()

            # إزالة خاصية البقاء في المقدمة بعد ثانية واحدة
            QTimer.singleShot(1000, lambda: self.remittances_window.setWindowFlags(
                self.remittances_window.windowFlags() & ~Qt.WindowStaysOnTopHint
            ))
            QTimer.singleShot(1001, lambda: self.remittances_window.show())

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إدارة الحوالات:\n{str(e)}")

    def open_remittance_request_window(self):
        """فتح نافذة طلب حوالة"""
        try:
            from .remittances.remittance_request_window import RemittanceRequestWindow

            # إغلاق النافذة السابقة إذا كانت موجودة
            if hasattr(self, 'remittance_request_window') and self.remittance_request_window:
                self.remittance_request_window.close()

            # إنشاء نافذة جديدة
            self.remittance_request_window = RemittanceRequestWindow(self)

            # ربط الإشارات
            self.remittance_request_window.remittance_request_created.connect(self.on_remittance_request_created)
            self.remittance_request_window.send_to_create_remittance.connect(self.on_send_to_create_remittance)

            # عرض النافذة
            self.remittance_request_window.show()

            # رفع النافذة للمقدمة
            self.remittance_request_window.raise_()
            self.remittance_request_window.activateWindow()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة طلب الحوالة:\n{str(e)}")

    def on_remittance_request_created(self, request_data):
        """معالج إنشاء طلب حوالة جديد"""
        try:
            # عرض رسالة تأكيد
            QMessageBox.information(
                self,
                "طلب حوالة جديد",
                f"تم إنشاء طلب الحوالة بنجاح!\n\n"
                f"رقم الطلب: {request_data['request_number']}\n"
                f"المرسل: {request_data['sender_name']}\n"
                f"المستقبل: {request_data['receiver_name']}\n"
                f"المبلغ: {request_data['amount']} {request_data['source_currency']}"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في معالجة طلب الحوالة:\n{str(e)}")

    def on_send_to_create_remittance(self, request_data):
        """معالج إرسال طلب الحوالة لنافذة إنشاء الحوالة"""
        try:
            # فتح نافذة إنشاء الحوالة مع البيانات
            from .remittances.create_remittance_dialog import CreateRemittanceDialog

            dialog = CreateRemittanceDialog(request_data, self)
            dialog.remittance_created.connect(self.on_remittance_created_from_request)
            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إرسال البيانات لنافذة إنشاء الحوالة:\n{str(e)}")

    def on_remittance_created_from_request(self, remittance_data):
        """معالج إنشاء حوالة من طلب"""
        try:
            QMessageBox.information(
                self,
                "تم إنشاء الحوالة",
                f"تم إنشاء الحوالة من الطلب بنجاح!\n\n"
                f"رقم الحوالة: {remittance_data['remittance_number']}\n"
                f"المرسل: {remittance_data['sender_name']}\n"
                f"المستقبل: {remittance_data['receiver_name']}\n"
                f"المبلغ الإجمالي: {remittance_data['total_amount']} {remittance_data['currency']}"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في معالجة الحوالة:\n{str(e)}")

    def open_new_remittance_dialog(self):
        """فتح نافذة إنشاء حوالة جديدة"""
        try:
            from .remittances.new_remittance_dialog import NewRemittanceDialog
            dialog = NewRemittanceDialog(self)

            # توسيط النافذة وسط الشاشة
            self.center_window(dialog)

            # إحضار النافذة للمقدمة
            dialog.raise_()
            dialog.activateWindow()
            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إنشاء حوالة جديدة:\n{str(e)}")

    def open_database_settings_window(self):
        """فتح نافذة إعدادات قاعدة البيانات"""
        try:
            from .database.database_settings_window import DatabaseSettingsWindow
            self.database_settings_window = DatabaseSettingsWindow()

            # توسيط النافذة وسط الشاشة
            self.center_window(self.database_settings_window)

            # إحضار النافذة للمقدمة
            self.database_settings_window.show()
            self.database_settings_window.raise_()
            self.database_settings_window.activateWindow()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إعدادات قاعدة البيانات:\n{str(e)}")

    def open_banks_management_window(self):
        """فتح نافذة إدارة البنوك والصرافين"""
        try:
            from .remittances.banks_management_window import BanksManagementWindow

            # إغلاق النافذة السابقة إذا كانت موجودة
            if hasattr(self, 'banks_management_window') and self.banks_management_window:
                self.banks_management_window.close()

            self.banks_management_window = BanksManagementWindow()

            # إعدادات النافذة لضمان ظهورها في المقدمة
            self.banks_management_window.setWindowFlags(
                self.banks_management_window.windowFlags() |
                Qt.WindowStaysOnTopHint
            )

            # توسيط النافذة وسط الشاشة
            self.center_window(self.banks_management_window)

            # إحضار النافذة للمقدمة بطرق متعددة
            self.banks_management_window.show()
            self.banks_management_window.raise_()
            self.banks_management_window.activateWindow()

            # إزالة خاصية البقاء في المقدمة بعد ثانية واحدة
            QTimer.singleShot(1000, lambda: self.banks_management_window.setWindowFlags(
                self.banks_management_window.windowFlags() & ~Qt.WindowStaysOnTopHint
            ))
            QTimer.singleShot(1001, lambda: self.banks_management_window.show())

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إدارة البنوك:\n{str(e)}")

    def open_supplier_accounts_management_window(self):
        """فتح نافذة إدارة حسابات الموردين"""
        try:
            from .remittances.supplier_accounts_management_window import SupplierAccountsManagementWindow

            # إغلاق النافذة السابقة إذا كانت موجودة
            if hasattr(self, 'supplier_accounts_management_window') and self.supplier_accounts_management_window:
                self.supplier_accounts_management_window.close()

            self.supplier_accounts_management_window = SupplierAccountsManagementWindow()

            # إعدادات النافذة لضمان ظهورها في المقدمة
            self.supplier_accounts_management_window.setWindowFlags(
                self.supplier_accounts_management_window.windowFlags() |
                Qt.WindowStaysOnTopHint
            )

            # توسيط النافذة وسط الشاشة
            self.center_window(self.supplier_accounts_management_window)

            # إحضار النافذة للمقدمة بطرق متعددة
            self.supplier_accounts_management_window.show()
            self.supplier_accounts_management_window.raise_()
            self.supplier_accounts_management_window.activateWindow()

            # إزالة خاصية البقاء في المقدمة بعد ثانية واحدة
            QTimer.singleShot(1000, lambda: self.supplier_accounts_management_window.setWindowFlags(
                self.supplier_accounts_management_window.windowFlags() & ~Qt.WindowStaysOnTopHint
            ))
            QTimer.singleShot(1001, lambda: self.supplier_accounts_management_window.show())

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إدارة حسابات الموردين:\n{str(e)}")

    def open_database_settings_dialog(self):
        """فتح حوار إعدادات قاعدة البيانات"""
        try:
            from src.ui.dialogs.database_settings_dialog import DatabaseSettingsDialog

            dialog = DatabaseSettingsDialog(self)
            if dialog.exec() == QDialog.Accepted:
                # إعادة تحميل التطبيق مع الإعدادات الجديدة
                QMessageBox.information(
                    self,
                    "تم التحديث",
                    "تم تحديث إعدادات قاعدة البيانات.\nقد تحتاج لإعادة تشغيل التطبيق لتطبيق التغييرات."
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"خطأ في فتح إعدادات قاعدة البيانات:\n{str(e)}"
            )

    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            "تأكيد الخروج",
            "هل تريد إغلاق البرنامج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إغلاق النوافذ المنفصلة
            if self.settings_window:
                self.settings_window.close()
            if self.items_window:
                self.items_window.close()
            if self.suppliers_window:
                self.suppliers_window.close()
            if self.shipments_window:
                self.shipments_window.close()
            if self.live_tracking_window:
                self.live_tracking_window.close()
            if self.remittances_window:
                self.remittances_window.close()
            if self.database_settings_window:
                self.database_settings_window.close()
            if self.banks_management_window:
                self.banks_management_window.close()
            if self.supplier_accounts_management_window:
                self.supplier_accounts_management_window.close()
            
            event.accept()
        else:
            event.ignore()
