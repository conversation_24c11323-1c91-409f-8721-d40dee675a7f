# -*- coding: utf-8 -*-
"""
إضافة حقول طريقة احتساب العمولة
Add Commission Calculation Fields Migration
"""

import sqlite3
import os

def add_commission_fields():
    """إضافة حقول طريقة احتساب العمولة لجدول ربط الموردين بالمندوبين"""
    
    # مسار قاعدة البيانات
    db_path = 'data/proshipment.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 فحص وجود حقول العمولة...")
        
        # فحص بنية الجدول
        cursor.execute("PRAGMA table_info(supplier_representatives)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        fields_to_add = [
            ('commission_type', 'VARCHAR(50)', 'نسبة'),
            ('commission_factor', 'REAL', '1.0'),
            ('commission_percentage', 'REAL', '0.0')
        ]
        
        for field_name, field_type, default_value in fields_to_add:
            if field_name not in column_names:
                print(f"🔨 إضافة حقل {field_name}...")
                
                # إضافة الحقل
                cursor.execute(f"""
                    ALTER TABLE supplier_representatives 
                    ADD COLUMN {field_name} {field_type} DEFAULT '{default_value}'
                """)
                
                # تحديث القيم الموجودة
                cursor.execute(f"""
                    UPDATE supplier_representatives 
                    SET {field_name} = '{default_value}' 
                    WHERE {field_name} IS NULL
                """)
                
                print(f"✅ تم إضافة حقل {field_name} بنجاح!")
            else:
                print(f"ℹ️ حقل {field_name} موجود بالفعل")
        
        # حفظ التغييرات
        conn.commit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة حقول العمولة: {str(e)}")
        return False
        
    finally:
        if conn:
            conn.close()

def test_commission_fields():
    """اختبار حقول العمولة"""
    try:
        db_path = 'data/proshipment.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🧪 اختبار حقول العمولة...")
        
        # عرض بعض التعيينات مع حقول العمولة
        cursor.execute("""
            SELECT sr.id, s.name as supplier_name, pr.name as rep_name, 
                   sr.commission_type, sr.commission_factor, sr.commission_percentage
            FROM supplier_representatives sr
            JOIN suppliers s ON sr.supplier_id = s.id
            JOIN purchase_representatives pr ON sr.representative_id = pr.id
            WHERE sr.is_active = 1 
            LIMIT 3
        """)
        
        assignments = cursor.fetchall()
        print("🤝 التعيينات مع حقول العمولة:")
        for assignment in assignments:
            print(f"   - {assignment[1]} → {assignment[2]}")
            print(f"     نوع العمولة: {assignment[3]}, معامل: {assignment[4]}, نسبة: {assignment[5]}%")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إضافة حقول طريقة احتساب العمولة")
    print("=" * 50)
    
    if add_commission_fields():
        print("\n" + "=" * 50)
        test_commission_fields()
        print("\n🎉 تم إكمال العملية بنجاح!")
    else:
        print("\n❌ فشل في إضافة حقول العمولة")
