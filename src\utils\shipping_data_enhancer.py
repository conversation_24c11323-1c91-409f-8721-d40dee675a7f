"""
نظام تحسين وتصحيح بيانات شركات الشحن في قاعدة البيانات
يقوم بمراجعة البيانات الموجودة وتصحيحها تلقائياً
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.database_manager import DatabaseManager
from database.models import Shipment
from utils.shipping_company_validator import ShippingCompanyValidator
from sqlalchemy import text
from typing import List, Dict, Tuple

class ShippingDataEnhancer:
    """فئة لتحسين وتصحيح بيانات شركات الشحن"""
    
    def __init__(self):
        """تهيئة النظام"""
        self.db_manager = DatabaseManager()
        self.validator = ShippingCompanyValidator()
        self.enhancement_log = []
    
    def analyze_shipping_companies(self) -> Dict:
        """تحليل أسماء شركات الشحن الموجودة في قاعدة البيانات"""
        session = self.db_manager.get_session()
        analysis_result = {
            "total_shipments": 0,
            "unique_companies": [],
            "invalid_companies": [],
            "correctable_companies": [],
            "valid_companies": [],
            "empty_companies": 0
        }
        
        try:
            # الحصول على جميع الشحنات
            shipments = session.query(Shipment).all()
            analysis_result["total_shipments"] = len(shipments)
            
            # جمع أسماء الشركات الفريدة
            company_names = set()
            for shipment in shipments:
                if shipment.shipping_company:
                    company_names.add(shipment.shipping_company.strip())
                else:
                    analysis_result["empty_companies"] += 1
            
            analysis_result["unique_companies"] = list(company_names)
            
            # تحليل كل شركة
            for company_name in company_names:
                validation_result = self.validator.validate_and_correct(company_name)
                
                if validation_result["is_valid"]:
                    analysis_result["valid_companies"].append({
                        "original": company_name,
                        "status": "valid",
                        "company_info": validation_result["company_info"]
                    })
                elif validation_result["suggestions"]:
                    analysis_result["correctable_companies"].append({
                        "original": company_name,
                        "status": "correctable",
                        "suggestions": validation_result["suggestions"],
                        "best_match": validation_result["suggestions"][0] if validation_result["suggestions"] else None
                    })
                else:
                    analysis_result["invalid_companies"].append({
                        "original": company_name,
                        "status": "invalid"
                    })
            
            return analysis_result
            
        except Exception as e:
            print(f"خطأ في تحليل شركات الشحن: {e}")
            return analysis_result
        finally:
            session.close()
    
    def enhance_shipping_companies(self, auto_correct: bool = False, confidence_threshold: float = 0.8) -> Dict:
        """تحسين أسماء شركات الشحن في قاعدة البيانات"""
        session = self.db_manager.get_session()
        enhancement_result = {
            "processed_shipments": 0,
            "corrected_companies": 0,
            "skipped_companies": 0,
            "errors": 0,
            "corrections": []
        }
        
        try:
            # الحصول على جميع الشحنات التي تحتوي على شركات شحن
            shipments = session.query(Shipment).filter(
                Shipment.shipping_company.isnot(None),
                Shipment.shipping_company != ""
            ).all()
            
            for shipment in shipments:
                enhancement_result["processed_shipments"] += 1
                original_name = shipment.shipping_company.strip()
                
                # التحقق من صحة الاسم
                validation_result = self.validator.validate_and_correct(original_name)
                
                if validation_result["is_valid"] and validation_result["confidence"] == 1.0:
                    # الاسم صحيح بالفعل
                    continue
                
                elif validation_result["suggestions"]:
                    best_suggestion = validation_result["suggestions"][0]
                    
                    if best_suggestion["similarity"] >= confidence_threshold:
                        if auto_correct:
                            # تصحيح تلقائي
                            corrected_name = best_suggestion["suggested_name"]
                            shipment.shipping_company = corrected_name
                            
                            enhancement_result["corrected_companies"] += 1
                            enhancement_result["corrections"].append({
                                "shipment_id": shipment.id,
                                "shipment_number": shipment.shipment_number,
                                "original": original_name,
                                "corrected": corrected_name,
                                "confidence": best_suggestion["similarity"],
                                "company_info": best_suggestion["company_data"]
                            })
                            
                            # إضافة إلى سجل التحسينات
                            self.enhancement_log.append({
                                "action": "auto_correct",
                                "shipment_id": shipment.id,
                                "original": original_name,
                                "corrected": corrected_name,
                                "confidence": best_suggestion["similarity"]
                            })
                        else:
                            # إضافة للمراجعة اليدوية
                            enhancement_result["corrections"].append({
                                "shipment_id": shipment.id,
                                "shipment_number": shipment.shipment_number,
                                "original": original_name,
                                "suggested": best_suggestion["suggested_name"],
                                "confidence": best_suggestion["similarity"],
                                "company_info": best_suggestion["company_data"],
                                "requires_manual_review": True
                            })
                    else:
                        enhancement_result["skipped_companies"] += 1
                else:
                    enhancement_result["skipped_companies"] += 1
            
            if auto_correct:
                # حفظ التغييرات
                session.commit()
                print(f"✅ تم تصحيح {enhancement_result['corrected_companies']} شركة شحن تلقائياً")
            
            return enhancement_result
            
        except Exception as e:
            session.rollback()
            enhancement_result["errors"] += 1
            print(f"خطأ في تحسين شركات الشحن: {e}")
            return enhancement_result
        finally:
            session.close()
    
    def get_correction_suggestions(self, company_name: str) -> List[Dict]:
        """الحصول على اقتراحات التصحيح لشركة معينة"""
        return self.validator.suggest_corrections(company_name, max_suggestions=5)
    
    def apply_manual_correction(self, shipment_id: int, corrected_name: str) -> bool:
        """تطبيق تصحيح يدوي لشحنة معينة"""
        session = self.db_manager.get_session()
        
        try:
            shipment = session.query(Shipment).filter(Shipment.id == shipment_id).first()
            if shipment:
                original_name = shipment.shipping_company
                shipment.shipping_company = corrected_name
                session.commit()
                
                # إضافة إلى سجل التحسينات
                self.enhancement_log.append({
                    "action": "manual_correct",
                    "shipment_id": shipment_id,
                    "original": original_name,
                    "corrected": corrected_name
                })
                
                return True
            return False
            
        except Exception as e:
            session.rollback()
            print(f"خطأ في التصحيح اليدوي: {e}")
            return False
        finally:
            session.close()
    
    def generate_enhancement_report(self) -> str:
        """إنشاء تقرير تحسين البيانات"""
        analysis = self.analyze_shipping_companies()
        
        report = "📊 تقرير تحليل وتحسين بيانات شركات الشحن\n"
        report += "=" * 50 + "\n\n"
        
        report += f"📈 إحصائيات عامة:\n"
        report += f"   • إجمالي الشحنات: {analysis['total_shipments']}\n"
        report += f"   • شركات فريدة: {len(analysis['unique_companies'])}\n"
        report += f"   • شحنات بدون شركة: {analysis['empty_companies']}\n\n"
        
        report += f"✅ شركات صحيحة: {len(analysis['valid_companies'])}\n"
        for company in analysis['valid_companies']:
            report += f"   • {company['original']} ✓\n"
        
        report += f"\n💡 شركات قابلة للتصحيح: {len(analysis['correctable_companies'])}\n"
        for company in analysis['correctable_companies']:
            if company['best_match']:
                confidence = int(company['best_match']['similarity'] * 100)
                report += f"   • {company['original']} → {company['best_match']['suggested_name']} ({confidence}%)\n"
        
        report += f"\n❌ شركات غير معروفة: {len(analysis['invalid_companies'])}\n"
        for company in analysis['invalid_companies']:
            report += f"   • {company['original']}\n"
        
        return report
    
    def backup_shipping_data(self) -> str:
        """إنشاء نسخة احتياطية من بيانات شركات الشحن"""
        session = self.db_manager.get_session()
        backup_file = f"shipping_companies_backup_{self.db_manager.get_current_timestamp()}.sql"
        
        try:
            # إنشاء استعلام النسخ الاحتياطي
            backup_query = """
            SELECT id, shipment_number, shipping_company 
            FROM shipments 
            WHERE shipping_company IS NOT NULL AND shipping_company != ''
            """
            
            result = session.execute(text(backup_query))
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write("-- نسخة احتياطية من بيانات شركات الشحن\n")
                f.write(f"-- تاريخ الإنشاء: {self.db_manager.get_current_timestamp()}\n\n")
                
                for row in result:
                    f.write(f"UPDATE shipments SET shipping_company = '{row.shipping_company}' WHERE id = {row.id};\n")
            
            return backup_file
            
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return ""
        finally:
            session.close()
    
    def get_enhancement_log(self) -> List[Dict]:
        """الحصول على سجل التحسينات"""
        return self.enhancement_log.copy()
    
    def clear_enhancement_log(self):
        """مسح سجل التحسينات"""
        self.enhancement_log.clear()

def main():
    """دالة رئيسية لاختبار النظام"""
    enhancer = ShippingDataEnhancer()
    
    print("🔍 تحليل بيانات شركات الشحن...")
    analysis = enhancer.analyze_shipping_companies()
    
    print(f"📊 النتائج:")
    print(f"   إجمالي الشحنات: {analysis['total_shipments']}")
    print(f"   شركات فريدة: {len(analysis['unique_companies'])}")
    print(f"   شركات صحيحة: {len(analysis['valid_companies'])}")
    print(f"   شركات قابلة للتصحيح: {len(analysis['correctable_companies'])}")
    print(f"   شركات غير معروفة: {len(analysis['invalid_companies'])}")
    
    # إنشاء تقرير مفصل
    report = enhancer.generate_enhancement_report()
    print("\n" + report)

if __name__ == "__main__":
    main()
