# -*- coding: utf-8 -*-
"""
تبويب بيانات الموردين
Suppliers Data Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QTextEdit, QCheckBox,
                               QComboBox, QDateEdit, QSpinBox, QAbstractItemView, QScrollArea,
                               QFileDialog, QDialog, QRadioButton, QDialogButtonBox, QMainWindow,
                               QMenuBar, QMenu, QToolBar, QStatusBar, QTabWidget)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont, QAction, QIcon
import pandas as pd
from datetime import datetime

from ...database.database_manager import DatabaseManager
from ...database.models import Supplier
from .supplier_currencies_tab import SupplierCurrenciesTab
from .purchase_representatives_tab import PurchaseRepresentativesTab
from .supplier_representatives_tab import SupplierRepresentativesTab

class SuppliersDataWidget(QWidget):
    """ويدجت بيانات الموردين"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_supplier_id = None
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""

        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # الجانب الأيسر - نموذج الإدخال مع منطقة تمرير
        form_scroll = QScrollArea()
        form_scroll.setMaximumWidth(600)
        form_scroll.setMinimumWidth(600)
        form_scroll.setWidgetResizable(True)
        form_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        form_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        form_group = QGroupBox("بيانات المورد")
        form_group.setMinimumWidth(580)

        # استخدام VBoxLayout بدلاً من FormLayout لتجنب التداخل
        form_main_layout = QVBoxLayout(form_group)
        form_main_layout.setSpacing(20)
        form_main_layout.setContentsMargins(20, 20, 20, 20)

        # البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(12)
        basic_layout.setContentsMargins(15, 15, 15, 15)
        basic_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)

        # رقم المورد (مطلوب)
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("أدخل رقم المورد (مطلوب)")
        self.code_edit.setMinimumHeight(35)
        self.code_edit.setMinimumWidth(300)
        self.code_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #e74c3c;
                border-radius: 6px;
                font-size: 12px;
                background-color: #fdf2f2;
            }
            QLineEdit:focus {
                border-color: #c0392b;
                background-color: white;
            }
        """)
        basic_layout.addRow("رقم المورد *:", self.code_edit)

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("اسم المورد أو الشركة")
        self.name_edit.setMinimumHeight(35)
        self.name_edit.setMinimumWidth(300)
        self.name_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        basic_layout.addRow("اسم المورد:", self.name_edit)

        self.name_en_edit = QLineEdit()
        self.name_en_edit.setPlaceholderText("Supplier Name in English")
        self.name_en_edit.setMinimumHeight(35)
        self.name_en_edit.setMinimumWidth(300)
        self.name_en_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        basic_layout.addRow("الاسم بالإنجليزية:", self.name_en_edit)

        self.supplier_type_combo = QComboBox()
        self.supplier_type_combo.addItems(["شركة", "فرد", "مؤسسة", "شركة شحن"])
        self.supplier_type_combo.setMinimumHeight(35)
        self.supplier_type_combo.setMinimumWidth(300)
        self.supplier_type_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        basic_layout.addRow("نوع المورد:", self.supplier_type_combo)

        self.tax_number_edit = QLineEdit()
        self.tax_number_edit.setPlaceholderText("الرقم الضريبي")
        self.tax_number_edit.setMinimumHeight(35)
        self.tax_number_edit.setMinimumWidth(300)
        self.tax_number_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        basic_layout.addRow("الرقم الضريبي:", self.tax_number_edit)

        self.commercial_register_edit = QLineEdit()
        self.commercial_register_edit.setPlaceholderText("رقم السجل التجاري")
        self.commercial_register_edit.setMinimumHeight(35)
        self.commercial_register_edit.setMinimumWidth(300)
        self.commercial_register_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        basic_layout.addRow("السجل التجاري:", self.commercial_register_edit)

        # تحسين تنسيق التسميات في المجموعة الأساسية
        basic_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
            }
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #34495e;
                min-width: 120px;
            }
        """)

        form_main_layout.addWidget(basic_group)
        
        # بيانات الاتصال
        contact_group = QGroupBox("بيانات الاتصال")
        contact_layout = QFormLayout(contact_group)
        contact_layout.setSpacing(12)
        contact_layout.setContentsMargins(15, 15, 15, 15)
        contact_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)

        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("+966 50 123 4567")
        self.phone_edit.setMinimumHeight(35)
        self.phone_edit.setMinimumWidth(300)
        self.phone_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        contact_layout.addRow("الهاتف:", self.phone_edit)

        self.mobile_edit = QLineEdit()
        self.mobile_edit.setPlaceholderText("+966 55 123 4567")
        self.mobile_edit.setMinimumHeight(35)
        self.mobile_edit.setMinimumWidth(300)
        self.mobile_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        contact_layout.addRow("الجوال:", self.mobile_edit)

        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("<EMAIL>")
        self.email_edit.setMinimumHeight(35)
        self.email_edit.setMinimumWidth(300)
        self.email_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        contact_layout.addRow("البريد الإلكتروني:", self.email_edit)

        self.website_edit = QLineEdit()
        self.website_edit.setPlaceholderText("www.supplier.com")
        self.website_edit.setMinimumHeight(35)
        self.website_edit.setMinimumWidth(300)
        self.website_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        contact_layout.addRow("الموقع الإلكتروني:", self.website_edit)

        # تحسين تنسيق مجموعة بيانات الاتصال
        contact_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
            }
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #34495e;
                min-width: 120px;
            }
        """)

        form_main_layout.addWidget(contact_group)
        
        # العنوان
        address_group = QGroupBox("العنوان")
        address_layout = QFormLayout(address_group)
        address_layout.setSpacing(12)
        address_layout.setContentsMargins(15, 15, 15, 15)
        address_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)

        self.country_edit = QLineEdit()
        self.country_edit.setText("المملكة العربية السعودية")
        self.country_edit.setMinimumHeight(35)
        self.country_edit.setMinimumWidth(300)
        self.country_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        address_layout.addRow("الدولة:", self.country_edit)

        self.city_edit = QLineEdit()
        self.city_edit.setPlaceholderText("الرياض")
        self.city_edit.setMinimumHeight(35)
        self.city_edit.setMinimumWidth(300)
        self.city_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        address_layout.addRow("المدينة:", self.city_edit)

        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(90)
        self.address_edit.setMinimumHeight(90)
        self.address_edit.setMinimumWidth(300)
        self.address_edit.setPlaceholderText("العنوان التفصيلي")
        self.address_edit.setStyleSheet("""
            QTextEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        address_layout.addRow("العنوان:", self.address_edit)

        self.postal_code_edit = QLineEdit()
        self.postal_code_edit.setPlaceholderText("12345")
        self.postal_code_edit.setMinimumHeight(35)
        self.postal_code_edit.setMinimumWidth(300)
        self.postal_code_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        address_layout.addRow("الرمز البريدي:", self.postal_code_edit)

        # تحسين تنسيق مجموعة العنوان
        address_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
            }
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #34495e;
                min-width: 120px;
            }
        """)

        form_main_layout.addWidget(address_group)
        
        # إعدادات إضافية
        settings_group = QGroupBox("إعدادات إضافية")
        settings_layout = QFormLayout(settings_group)
        settings_layout.setSpacing(12)
        settings_layout.setContentsMargins(15, 15, 15, 15)
        settings_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)

        self.credit_limit_edit = QSpinBox()
        self.credit_limit_edit.setMaximum(999999999)
        self.credit_limit_edit.setSuffix(" ريال")
        self.credit_limit_edit.setMinimumHeight(35)
        self.credit_limit_edit.setMinimumWidth(300)
        self.credit_limit_edit.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QSpinBox:focus {
                border-color: #3498db;
            }
        """)
        settings_layout.addRow("حد الائتمان:", self.credit_limit_edit)

        self.payment_terms_edit = QSpinBox()
        self.payment_terms_edit.setMaximum(365)
        self.payment_terms_edit.setSuffix(" يوم")
        self.payment_terms_edit.setMinimumHeight(35)
        self.payment_terms_edit.setMinimumWidth(300)
        self.payment_terms_edit.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QSpinBox:focus {
                border-color: #3498db;
            }
        """)
        settings_layout.addRow("مدة السداد:", self.payment_terms_edit)

        self.is_active_check = QCheckBox("نشط")
        self.is_active_check.setChecked(True)
        self.is_active_check.setMinimumHeight(35)
        self.is_active_check.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        settings_layout.addRow("الحالة:", self.is_active_check)

        # تحسين تنسيق مجموعة الإعدادات الإضافية
        settings_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
            }
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #34495e;
                min-width: 120px;
            }
        """)

        form_main_layout.addWidget(settings_group)
        
        # أزرار التحكم
        buttons_group = QGroupBox()
        buttons_layout = QHBoxLayout(buttons_group)
        buttons_layout.setSpacing(10)
        buttons_layout.setContentsMargins(10, 10, 10, 10)

        self.add_button = QPushButton("إضافة")
        self.add_button.clicked.connect(self.add_supplier)
        self.add_button.setMinimumHeight(35)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        self.update_button = QPushButton("تحديث")
        self.update_button.clicked.connect(self.update_supplier)
        self.update_button.setEnabled(False)
        self.update_button.setMinimumHeight(35)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)

        self.clear_button = QPushButton("مسح")
        self.clear_button.clicked.connect(self.clear_form)
        self.clear_button.setMinimumHeight(35)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        self.import_button = QPushButton("📥 استيراد من إكسيل")
        self.import_button.clicked.connect(self.import_suppliers)
        self.import_button.setMinimumHeight(35)
        self.import_button.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)

        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.clear_button)
        buttons_layout.addWidget(self.import_button)
        buttons_layout.addStretch()

        form_main_layout.addWidget(buttons_group)

        # إضافة النموذج إلى منطقة التمرير
        form_scroll.setWidget(form_group)
        main_layout.addWidget(form_scroll)
        
        # الجانب الأيمن - جدول الموردين
        table_group = QGroupBox("الموردين المسجلين")
        table_layout = QVBoxLayout(table_group)
        table_layout.setSpacing(10)
        table_layout.setContentsMargins(15, 15, 15, 15)

        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(7)
        self.suppliers_table.setHorizontalHeaderLabels([
            "رقم المورد", "اسم المورد", "النوع", "الهاتف", "البريد الإلكتروني", "المدينة", "الحالة"
        ])

        # تنسيق الجدول
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # اسم المورد
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # النوع
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الهاتف
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # البريد الإلكتروني
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # المدينة
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحالة

        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.setSelectionMode(QTableWidget.SingleSelection)
        self.suppliers_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.suppliers_table.setMinimumHeight(300)

        # تنسيق الجدول بـ CSS
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        table_layout.addWidget(self.suppliers_table)
        
        # أزرار إدارة الجدول
        table_buttons_layout = QHBoxLayout()
        table_buttons_layout.setSpacing(10)

        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.clicked.connect(self.load_data)
        refresh_button.setMinimumHeight(35)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)

        edit_button = QPushButton("✏️ تعديل المحدد")
        edit_button.clicked.connect(self.edit_selected)
        edit_button.setMinimumHeight(35)
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)

        delete_button = QPushButton("🗑️ حذف المحدد")
        delete_button.clicked.connect(self.delete_selected)
        delete_button.setMinimumHeight(35)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)

        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(edit_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addStretch()

        table_layout.addLayout(table_buttons_layout)
        main_layout.addWidget(table_group)
    
    def load_data(self):
        """تحميل بيانات الموردين"""
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter_by(is_active=True).all()
            
            self.suppliers_table.setRowCount(len(suppliers))
            
            for row, supplier in enumerate(suppliers):
                # رقم المورد
                self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier.code or ""))

                # اسم المورد
                self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier.name or ""))

                # النوع
                self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier.supplier_type or ""))

                # الهاتف
                self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier.phone or ""))

                # البريد الإلكتروني
                self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier.email or ""))

                # المدينة
                self.suppliers_table.setItem(row, 5, QTableWidgetItem(supplier.city or ""))

                # الحالة
                status_text = "نشط" if supplier.is_active else "غير نشط"
                status_item = QTableWidgetItem(status_text)
                if supplier.is_active:
                    status_item.setBackground(Qt.green)
                else:
                    status_item.setBackground(Qt.red)
                self.suppliers_table.setItem(row, 6, status_item)

                # حفظ ID في البيانات
                self.suppliers_table.item(row, 0).setData(Qt.UserRole, supplier.id)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل بيانات الموردين:\n{str(e)}"
            )
        finally:
            session.close()
    
    def add_supplier(self):
        """إضافة مورد جديد"""
        if not self.validate_form():
            return

        session = self.db_manager.get_session()
        try:
            # التحقق من عدم وجود المورد مسبقاً
            existing = session.query(Supplier).filter_by(name=self.name_edit.text()).first()
            if existing:
                QMessageBox.warning(self, "مورد موجود", "هذا المورد موجود بالفعل")
                return

            # إنشاء المورد الجديد
            new_supplier = Supplier(
                code=self.code_edit.text().strip(),
                name=self.name_edit.text(),
                name_en=self.name_en_edit.text(),
                supplier_type=self.supplier_type_combo.currentText(),
                tax_number=self.tax_number_edit.text(),
                commercial_register=self.commercial_register_edit.text(),
                phone=self.phone_edit.text(),
                mobile=self.mobile_edit.text(),
                email=self.email_edit.text(),
                website=self.website_edit.text(),
                country=self.country_edit.text(),
                city=self.city_edit.text(),
                address=self.address_edit.toPlainText(),
                postal_code=self.postal_code_edit.text(),
                credit_limit=self.credit_limit_edit.value(),
                payment_terms=self.payment_terms_edit.value(),
                is_active=self.is_active_check.isChecked()
            )
            
            session.add(new_supplier)
            session.commit()
            
            QMessageBox.information(self, "تم الإضافة", "تم إضافة المورد بنجاح")
            
            self.clear_form()
            self.load_data()
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في الإضافة", f"حدث خطأ أثناء إضافة المورد:\n{str(e)}")
        finally:
            session.close()
    
    def update_supplier(self):
        """تحديث بيانات المورد"""
        if not self.current_supplier_id or not self.validate_form():
            return
        
        session = self.db_manager.get_session()
        try:
            supplier = session.query(Supplier).get(self.current_supplier_id)
            if supplier:
                supplier.code = self.code_edit.text().strip()
                supplier.name = self.name_edit.text()
                supplier.name_en = self.name_en_edit.text()
                supplier.supplier_type = self.supplier_type_combo.currentText()
                supplier.tax_number = self.tax_number_edit.text()
                supplier.commercial_register = self.commercial_register_edit.text()
                supplier.phone = self.phone_edit.text()
                supplier.mobile = self.mobile_edit.text()
                supplier.email = self.email_edit.text()
                supplier.website = self.website_edit.text()
                supplier.country = self.country_edit.text()
                supplier.city = self.city_edit.text()
                supplier.address = self.address_edit.toPlainText()
                supplier.postal_code = self.postal_code_edit.text()
                supplier.credit_limit = self.credit_limit_edit.value()
                supplier.payment_terms = self.payment_terms_edit.value()
                supplier.is_active = self.is_active_check.isChecked()
                
                session.commit()
                
                QMessageBox.information(self, "تم التحديث", "تم تحديث بيانات المورد بنجاح")
                
                self.clear_form()
                self.load_data()
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في التحديث", f"حدث خطأ أثناء تحديث بيانات المورد:\n{str(e)}")
        finally:
            session.close()
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.code_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم المورد")
            return False

        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المورد")
            return False

        # التحقق من عدم تكرار رقم المورد
        session = self.db_manager.get_session()
        try:
            existing_supplier = session.query(Supplier).filter_by(code=self.code_edit.text().strip()).first()
            if existing_supplier and (not self.current_supplier_id or existing_supplier.id != self.current_supplier_id):
                QMessageBox.warning(self, "خطأ", f"رقم المورد '{self.code_edit.text().strip()}' موجود بالفعل")
                return False
        finally:
            session.close()

        if self.email_edit.text() and "@" not in self.email_edit.text():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال بريد إلكتروني صحيح")
            return False

        return True
    
    def clear_form(self):
        """مسح النموذج"""
        self.current_supplier_id = None
        self.code_edit.clear()
        self.name_edit.clear()
        self.name_en_edit.clear()
        self.supplier_type_combo.setCurrentIndex(0)
        self.tax_number_edit.clear()
        self.commercial_register_edit.clear()
        self.phone_edit.clear()
        self.mobile_edit.clear()
        self.email_edit.clear()
        self.website_edit.clear()
        self.country_edit.setText("المملكة العربية السعودية")
        self.city_edit.clear()
        self.address_edit.clear()
        self.postal_code_edit.clear()
        self.credit_limit_edit.setValue(0)
        self.payment_terms_edit.setValue(0)
        self.is_active_check.setChecked(True)
        
        self.add_button.setEnabled(True)
        self.update_button.setEnabled(False)
    
    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            self.edit_selected()
    
    def edit_selected(self):
        """تعديل المورد المحدد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            return
        
        supplier_id = self.suppliers_table.item(current_row, 0).data(Qt.UserRole)
        
        session = self.db_manager.get_session()
        try:
            supplier = session.query(Supplier).get(supplier_id)
            if supplier:
                self.current_supplier_id = supplier.id
                self.code_edit.setText(supplier.code or "")
                self.name_edit.setText(supplier.name or "")
                self.name_en_edit.setText(supplier.name_en or "")
                
                # تعيين نوع المورد
                type_index = self.supplier_type_combo.findText(supplier.supplier_type or "شركة")
                if type_index >= 0:
                    self.supplier_type_combo.setCurrentIndex(type_index)
                
                self.tax_number_edit.setText(supplier.tax_number or "")
                self.commercial_register_edit.setText(supplier.commercial_register or "")
                self.phone_edit.setText(supplier.phone or "")
                self.mobile_edit.setText(supplier.mobile or "")
                self.email_edit.setText(supplier.email or "")
                self.website_edit.setText(supplier.website or "")
                self.country_edit.setText(supplier.country or "")
                self.city_edit.setText(supplier.city or "")
                self.address_edit.setPlainText(supplier.address or "")
                self.postal_code_edit.setText(supplier.postal_code or "")
                self.credit_limit_edit.setValue(supplier.credit_limit or 0)
                self.payment_terms_edit.setValue(supplier.payment_terms or 0)
                self.is_active_check.setChecked(supplier.is_active)
                
                self.add_button.setEnabled(False)
                self.update_button.setEnabled(True)
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات المورد:\n{str(e)}")
        finally:
            session.close()
    
    def delete_selected(self):
        """حذف المورد المحدد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "لا يوجد تحديد", "يرجى تحديد مورد للحذف")
            return
        
        supplier_id = self.suppliers_table.item(current_row, 0).data(Qt.UserRole)
        supplier_name = self.suppliers_table.item(current_row, 0).text()
        
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف المورد '{supplier_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                supplier = session.query(Supplier).get(supplier_id)
                if supplier:
                    supplier.is_active = False  # حذف منطقي
                    session.commit()
                    
                    QMessageBox.information(self, "تم الحذف", f"تم حذف المورد '{supplier_name}' بنجاح")
                    self.load_data()
                    self.clear_form()
                    
            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف المورد:\n{str(e)}")
            finally:
                session.close()
    
    def add_new_supplier(self):
        """إضافة مورد جديد - يستدعى من النافذة الرئيسية"""
        self.clear_form()
        self.name_edit.setFocus()

    def import_suppliers(self):
        """استيراد الموردين من ملف إكسيل"""
        # نافذة خيارات الاستيراد
        options_dialog = QDialog(self)
        options_dialog.setWindowTitle("خيارات استيراد الموردين")
        options_dialog.setModal(True)
        options_dialog.resize(450, 250)

        layout = QVBoxLayout(options_dialog)

        # عنوان
        title_label = QLabel("ماذا تريد أن تفعل مع الموردين الموجودين؟")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50;")
        layout.addWidget(title_label)

        # خيارات
        update_existing_radio = QRadioButton("تحديث الموردين الموجودين")
        update_existing_radio.setChecked(True)  # الخيار الافتراضي
        update_existing_radio.setStyleSheet("font-size: 12px; padding: 5px;")

        skip_existing_radio = QRadioButton("تجاهل الموردين الموجودين")
        skip_existing_radio.setStyleSheet("font-size: 12px; padding: 5px;")

        layout.addWidget(update_existing_radio)
        layout.addWidget(skip_existing_radio)

        # معلومات إضافية
        info_label = QLabel("""
        📋 الأعمدة المطلوبة في ملف الإكسيل:
        • رقم المورد (مطلوب)
        • اسم المورد (مطلوب)
        • الاسم الإنجليزي (اختياري)
        • نوع المورد (اختياري)
        • الرقم الضريبي (اختياري)
        • السجل التجاري (اختياري)
        • الهاتف (اختياري)
        • الجوال (اختياري)
        • البريد الإلكتروني (اختياري)
        • الموقع الإلكتروني (اختياري)
        • الدولة (اختياري)
        • المدينة (اختياري)
        • العنوان (اختياري)
        • الرمز البريدي (اختياري)
        • حد الائتمان (اختياري)
        • مدة السداد (اختياري)
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-size: 11px;
                color: #495057;
            }
        """)
        layout.addWidget(info_label)

        # أزرار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(options_dialog.accept)
        buttons.rejected.connect(options_dialog.reject)
        layout.addWidget(buttons)

        if options_dialog.exec() != QDialog.Accepted:
            return

        # اختيار الملف
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار ملف الإكسيل", "",
            "ملفات الإكسيل (*.xlsx *.xls)"
        )

        if file_path:
            update_existing = update_existing_radio.isChecked()
            success_count, error_count, errors = self.import_from_excel(file_path, update_existing)

            # عرض النتائج
            if error_count == 0:
                QMessageBox.information(
                    self, "تم الاستيراد بنجاح",
                    f"تم استيراد {success_count} مورد بنجاح"
                )
            else:
                error_details = "\n".join(errors[:10])  # عرض أول 10 أخطاء فقط
                if len(errors) > 10:
                    error_details += f"\n... و {len(errors) - 10} أخطاء أخرى"

                QMessageBox.warning(
                    self, "تم الاستيراد مع أخطاء",
                    f"تم استيراد {success_count} مورد بنجاح\n"
                    f"فشل في استيراد {error_count} مورد\n\n"
                    f"الأخطاء:\n{error_details}"
                )

            # تحديث الجدول
            self.load_data()

    def import_from_excel(self, file_path, update_existing=True):
        """استيراد البيانات من ملف إكسيل"""
        success_count = 0
        error_count = 0
        errors = []

        try:
            # قراءة ملف الإكسيل
            df = pd.read_excel(file_path)

            # التحقق من وجود العمود المطلوب
            required_columns = ['رقم المورد', 'اسم المورد']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                errors.append(f"الأعمدة المطلوبة مفقودة: {', '.join(missing_columns)}")
                return 0, 1, errors

            session = self.db_manager.get_session()
            try:
                # تتبع الأسماء المستخدمة في الملف لتجنب التكرار
                used_names_in_file = set()

                # معالجة كل صف
                for index, row in df.iterrows():
                    try:
                        # التحقق من البيانات الأساسية
                        supplier_code = str(row['رقم المورد']).strip()
                        supplier_name = str(row['اسم المورد']).strip()

                        if not supplier_code or supplier_code == 'nan':
                            errors.append(f"الصف {index + 2}: رقم المورد مطلوب")
                            error_count += 1
                            continue

                        if not supplier_name or supplier_name == 'nan':
                            errors.append(f"الصف {index + 2}: اسم المورد مطلوب")
                            error_count += 1
                            continue

                        # التحقق من عدم تكرار رقم المورد في الملف نفسه
                        if supplier_code in used_names_in_file:
                            errors.append(f"الصف {index + 2}: رقم المورد '{supplier_code}' مكرر في الملف")
                            error_count += 1
                            continue

                        used_names_in_file.add(supplier_code)

                        # التحقق من وجود المورد في قاعدة البيانات
                        existing_supplier = session.query(Supplier).filter_by(code=supplier_code).first()

                        if existing_supplier:
                            if update_existing:
                                try:
                                    # تحديث المورد الموجود
                                    existing_supplier.name = supplier_name
                                    self.update_supplier_from_row(existing_supplier, row)
                                    existing_supplier.updated_at = datetime.now()
                                    success_count += 1
                                    continue
                                except Exception as update_error:
                                    errors.append(f"الصف {index + 2}: فشل في تحديث المورد '{supplier_code}' - {str(update_error)}")
                                    error_count += 1
                                    continue
                            else:
                                # تجاهل المورد الموجود
                                errors.append(f"الصف {index + 2}: المورد '{supplier_code}' موجود بالفعل - تم التجاهل")
                                error_count += 1
                                continue

                        # إنشاء مورد جديد
                        new_supplier = Supplier(
                            code=supplier_code,
                            name=supplier_name,
                            created_at=datetime.now(),
                            updated_at=datetime.now()
                        )

                        # تحديث البيانات من الصف
                        self.update_supplier_from_row(new_supplier, row)

                        session.add(new_supplier)
                        success_count += 1

                    except Exception as e:
                        errors.append(f"الصف {index + 2}: خطأ في المعالجة - {str(e)}")
                        error_count += 1
                        continue

                # حفظ التغييرات
                if success_count > 0:
                    session.commit()

            except Exception as e:
                session.rollback()
                errors.append(f"خطأ في قاعدة البيانات: {str(e)}")
                error_count += len(df)
                success_count = 0
            finally:
                session.close()

        except Exception as e:
            errors.append(f"خطأ في قراءة الملف: {str(e)}")
            error_count = 1
            success_count = 0

        return success_count, error_count, errors

    def update_supplier_from_row(self, supplier, row):
        """تحديث بيانات المورد من صف الإكسيل"""
        # استخراج البيانات الاختيارية مع التحقق من وجودها
        def get_safe_value(column_name, default=''):
            if column_name in row.index:
                value = str(row[column_name]).strip()
                return value if value != 'nan' else default
            return default

        def get_safe_numeric_value(column_name, default=0):
            if column_name in row.index:
                try:
                    value = row[column_name]
                    if pd.isna(value) or value == 'nan':
                        return default
                    return float(value)
                except (ValueError, TypeError):
                    return default
            return default

        def get_safe_int_value(column_name, default=0):
            if column_name in row.index:
                try:
                    value = row[column_name]
                    if pd.isna(value) or value == 'nan':
                        return default
                    return int(float(value))
                except (ValueError, TypeError):
                    return default
            return default

        # تحديث البيانات
        supplier.name_en = get_safe_value('الاسم الإنجليزي')
        supplier.supplier_type = get_safe_value('نوع المورد')
        supplier.tax_number = get_safe_value('الرقم الضريبي')
        supplier.commercial_register = get_safe_value('السجل التجاري')
        supplier.phone = get_safe_value('الهاتف')
        supplier.mobile = get_safe_value('الجوال')
        supplier.email = get_safe_value('البريد الإلكتروني')
        supplier.website = get_safe_value('الموقع الإلكتروني')
        supplier.country = get_safe_value('الدولة')
        supplier.city = get_safe_value('المدينة')
        supplier.address = get_safe_value('العنوان')
        supplier.postal_code = get_safe_value('الرمز البريدي')
        supplier.credit_limit = get_safe_numeric_value('حد الائتمان', 0.0)
        supplier.payment_terms = get_safe_int_value('مدة السداد', 0)

        # تعيين الحالة النشطة افتراضياً
        supplier.is_active = True

        # تعيين معلومات الاتصال إذا لم تكن موجودة
        if not supplier.contact_person:
            supplier.contact_person = get_safe_value('شخص الاتصال')


class SuppliersDataWindow(QMainWindow):
    """نافذة بيانات الموردين المنفصلة"""

    def __init__(self):
        super().__init__()
        self.setup_window()
        self.setup_ui()

    def setup_window(self):
        """إعداد النافذة"""
        self.setWindowTitle("إدارة بيانات الموردين - ProShipment")
        self.setMinimumSize(1400, 800)

        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()

        # تطبيق الخط العربي
        font = QFont("Segoe UI", 10)
        self.setFont(font)

        # تطبيق تخطيط RTL
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QMenuBar {
                background-color: #2c3e50;
                color: white;
                border: none;
                padding: 5px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 15px;
                margin: 2px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #34495e;
            }
            QToolBar {
                background-color: #34495e;
                border: none;
                spacing: 5px;
                padding: 5px;
            }
            QStatusBar {
                background-color: #2c3e50;
                color: white;
                border: none;
            }
        """)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #ddd;
                border-bottom: none;
                padding: 10px 20px;
                margin-right: 2px;
                font-size: 12px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background-color: #e9ecef;
            }
        """)

        # تبويب بيانات الموردين
        self.suppliers_widget = SuppliersDataWidget()
        self.tab_widget.addTab(self.suppliers_widget, "📋 بيانات الموردين")

        # تبويب ربط الموردين بالعملات
        self.supplier_currencies_tab = SupplierCurrenciesTab()
        self.tab_widget.addTab(self.supplier_currencies_tab, "💰 ربط الموردين بالعملات")

        # تبويب مندوبي المشتريات
        self.purchase_representatives_tab = PurchaseRepresentativesTab()
        self.tab_widget.addTab(self.purchase_representatives_tab, "👥 مندوبي المشتريات")

        # تبويب ربط الموردين بمندوبي المشتريات
        self.supplier_representatives_tab = SupplierRepresentativesTab()
        self.tab_widget.addTab(self.supplier_representatives_tab, "🤝 ربط الموردين بالمندوبين")

        self.setCentralWidget(self.tab_widget)

        # إعداد شريط القوائم
        self.setup_menu_bar()

        # إعداد شريط الأدوات
        self.setup_toolbar()

        # إعداد شريط الحالة
        self.setup_status_bar()

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu("ملف")

        new_action = QAction("مورد جديد", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.suppliers_widget.add_new_supplier)
        file_menu.addAction(new_action)

        file_menu.addSeparator()

        import_action = QAction("استيراد من إكسيل", self)
        import_action.setShortcut("Ctrl+I")
        import_action.triggered.connect(self.suppliers_widget.import_suppliers)
        file_menu.addAction(import_action)

        file_menu.addSeparator()

        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)

        # قائمة التحرير
        edit_menu = menubar.addMenu("تحرير")

        refresh_action = QAction("تحديث البيانات", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.suppliers_widget.load_data)
        edit_menu.addAction(refresh_action)

        edit_menu.addSeparator()

        clear_action = QAction("مسح النموذج", self)
        clear_action.setShortcut("Ctrl+R")
        clear_action.triggered.connect(self.suppliers_widget.clear_form)
        edit_menu.addAction(clear_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")

        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setMovable(False)

        # زر مورد جديد
        new_action = QAction("مورد جديد", self)
        new_action.setToolTip("إضافة مورد جديد (Ctrl+N)")
        new_action.triggered.connect(self.suppliers_widget.add_new_supplier)
        toolbar.addAction(new_action)

        toolbar.addSeparator()

        # زر تحديث
        refresh_action = QAction("تحديث", self)
        refresh_action.setToolTip("تحديث البيانات (F5)")
        refresh_action.triggered.connect(self.suppliers_widget.load_data)
        toolbar.addAction(refresh_action)

        # زر مسح النموذج
        clear_action = QAction("مسح", self)
        clear_action.setToolTip("مسح النموذج (Ctrl+R)")
        clear_action.triggered.connect(self.suppliers_widget.clear_form)
        toolbar.addAction(clear_action)

        toolbar.addSeparator()

        # زر استيراد
        import_action = QAction("استيراد", self)
        import_action.setToolTip("استيراد من إكسيل (Ctrl+I)")
        import_action.triggered.connect(self.suppliers_widget.import_suppliers)
        toolbar.addAction(import_action)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.showMessage("جاهز - إدارة بيانات الموردين")

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(
            self,
            "حول البرنامج",
            "نظام إدارة بيانات الموردين\n"
            "جزء من نظام ProShipment المتكامل\n"
            "الإصدار 1.0"
        )
