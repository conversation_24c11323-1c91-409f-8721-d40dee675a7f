#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة أصناف تجريبية مع وحدات القياس
Add Sample Items with Units of Measurement
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.universal_database_manager import UniversalDatabaseManager
from src.database.oracle_config import DatabaseConfigManager
from src.database.models import Item, ItemGroup, UnitOfMeasure

def add_sample_items():
    """إضافة أصناف تجريبية"""
    
    print("📦 إضافة أصناف تجريبية...")
    print("=" * 50)
    
    try:
        # تحميل التكوين
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        with db_manager.get_session() as session:
            # الحصول على المجموعات ووحدات القياس
            electronics_group = session.query(ItemGroup).filter_by(name="إلكترونيات").first()
            clothing_group = session.query(ItemGroup).filter_by(name="ملابس").first()
            food_group = session.query(ItemGroup).filter_by(name="أغذية").first()
            home_group = session.query(ItemGroup).filter_by(name="أدوات منزلية").first()
            
            # وحدات القياس
            piece_unit = session.query(UnitOfMeasure).filter_by(name="قطعة").first()
            kg_unit = session.query(UnitOfMeasure).filter_by(name="كيلوجرام").first()
            box_unit = session.query(UnitOfMeasure).filter_by(name="صندوق").first()
            pair_unit = session.query(UnitOfMeasure).filter_by(name="زوج").first()
            liter_unit = session.query(UnitOfMeasure).filter_by(name="لتر").first()
            
            # التحقق من وجود البيانات المطلوبة
            if not all([electronics_group, clothing_group, food_group, home_group]):
                print("❌ لم يتم العثور على جميع مجموعات الأصناف المطلوبة")
                return False
            
            if not all([piece_unit, kg_unit, box_unit, pair_unit]):
                print("❌ لم يتم العثور على جميع وحدات القياس المطلوبة")
                return False
            
            # الأصناف التجريبية
            sample_items = [
                # إلكترونيات
                {
                    "code": "ELC001",
                    "name": "لابتوب ديل XPS 13",
                    "name_en": "Dell XPS 13 Laptop",
                    "description": "لابتوب محمول عالي الأداء مع معالج Intel Core i7",
                    "group_id": electronics_group.id,
                    "unit_id": piece_unit.id,
                    "cost_price": 3500.00,
                    "selling_price": 4200.00,
                    "weight": 1.2,
                    "dimensions": "30.4 x 19.9 x 1.4 سم"
                },
                {
                    "code": "ELC002",
                    "name": "هاتف ذكي سامسونج Galaxy S23",
                    "name_en": "Samsung Galaxy S23 Smartphone",
                    "description": "هاتف ذكي متطور مع كاميرا عالية الدقة",
                    "group_id": electronics_group.id,
                    "unit_id": piece_unit.id,
                    "cost_price": 2800.00,
                    "selling_price": 3200.00,
                    "weight": 0.168,
                    "dimensions": "14.6 x 7.1 x 0.76 سم"
                },
                {
                    "code": "ELC003",
                    "name": "سماعات بلوتوث AirPods Pro",
                    "name_en": "AirPods Pro Bluetooth Headphones",
                    "description": "سماعات لاسلكية مع خاصية إلغاء الضوضاء",
                    "group_id": electronics_group.id,
                    "unit_id": pair_unit.id,
                    "cost_price": 850.00,
                    "selling_price": 999.00,
                    "weight": 0.056,
                    "dimensions": "4.5 x 6.1 x 2.1 سم"
                },
                
                # ملابس
                {
                    "code": "CLT001",
                    "name": "قميص قطني رجالي",
                    "name_en": "Men's Cotton Shirt",
                    "description": "قميص قطني عالي الجودة للرجال - مقاسات متعددة",
                    "group_id": clothing_group.id,
                    "unit_id": piece_unit.id,
                    "cost_price": 45.00,
                    "selling_price": 75.00,
                    "weight": 0.3,
                    "dimensions": "متوسط: 72 x 52 سم"
                },
                {
                    "code": "CLT002",
                    "name": "حذاء رياضي نايكي",
                    "name_en": "Nike Sports Shoes",
                    "description": "حذاء رياضي مريح للجري والأنشطة الرياضية",
                    "group_id": clothing_group.id,
                    "unit_id": pair_unit.id,
                    "cost_price": 280.00,
                    "selling_price": 350.00,
                    "weight": 0.8,
                    "dimensions": "مقاس 42: 28 x 10 x 8 سم"
                },
                
                # أغذية
                {
                    "code": "FOD001",
                    "name": "أرز بسمتي هندي",
                    "name_en": "Indian Basmati Rice",
                    "description": "أرز بسمتي عالي الجودة من الهند - كيس 5 كيلو",
                    "group_id": food_group.id,
                    "unit_id": kg_unit.id,
                    "cost_price": 18.00,
                    "selling_price": 25.00,
                    "weight": 5.0,
                    "dimensions": "35 x 25 x 8 سم"
                },
                {
                    "code": "FOD002",
                    "name": "زيت زيتون بكر ممتاز",
                    "name_en": "Extra Virgin Olive Oil",
                    "description": "زيت زيتون بكر ممتاز من إسبانيا - زجاجة 500 مل",
                    "group_id": food_group.id,
                    "unit_id": liter_unit.id if liter_unit else piece_unit.id,
                    "cost_price": 35.00,
                    "selling_price": 45.00,
                    "weight": 0.5,
                    "dimensions": "25 x 6 x 6 سم"
                },
                
                # أدوات منزلية
                {
                    "code": "HOM001",
                    "name": "مكنسة كهربائية دايسون",
                    "name_en": "Dyson Vacuum Cleaner",
                    "description": "مكنسة كهربائية لاسلكية عالية الأداء",
                    "group_id": home_group.id,
                    "unit_id": piece_unit.id,
                    "cost_price": 1200.00,
                    "selling_price": 1500.00,
                    "weight": 2.5,
                    "dimensions": "25 x 25 x 125 سم"
                },
                {
                    "code": "HOM002",
                    "name": "طقم أواني طبخ ستانلس ستيل",
                    "name_en": "Stainless Steel Cookware Set",
                    "description": "طقم أواني طبخ من الستانلس ستيل - 12 قطعة",
                    "group_id": home_group.id,
                    "unit_id": box_unit.id,
                    "cost_price": 450.00,
                    "selling_price": 650.00,
                    "weight": 8.5,
                    "dimensions": "45 x 35 x 25 سم"
                }
            ]
            
            added_count = 0
            skipped_count = 0
            
            for item_data in sample_items:
                # التحقق من عدم وجود الصنف مسبقاً
                existing = session.query(Item).filter_by(code=item_data["code"]).first()
                
                if existing:
                    print(f"   ⚠️ الصنف '{item_data['name']}' موجود مسبقاً - تم التخطي")
                    skipped_count += 1
                    continue
                
                # إنشاء الصنف الجديد
                new_item = Item(
                    code=item_data["code"],
                    name=item_data["name"],
                    name_en=item_data["name_en"],
                    description=item_data["description"],
                    group_id=item_data["group_id"],
                    unit_id=item_data["unit_id"],
                    cost_price=item_data["cost_price"],
                    selling_price=item_data["selling_price"],
                    weight=item_data["weight"],
                    dimensions=item_data["dimensions"],
                    is_active=True
                )
                
                session.add(new_item)
                print(f"   ✅ تم إضافة الصنف: {item_data['name']} ({item_data['code']})")
                added_count += 1
            
            print(f"\n📊 ملخص العملية:")
            print(f"   ✅ تم إضافة: {added_count} صنف")
            print(f"   ⚠️ تم تخطي: {skipped_count} صنف (موجود مسبقاً)")
            
            if added_count > 0:
                print(f"\n🎉 تم إضافة {added_count} صنف جديد بنجاح!")
            else:
                print(f"\n💡 جميع الأصناف موجودة مسبقاً.")
                
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def list_items_with_units():
    """عرض الأصناف مع وحدات القياس"""
    print("\n📋 الأصناف مع وحدات القياس:")
    print("-" * 80)
    
    try:
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        with db_manager.get_session() as session:
            items = session.query(Item).join(UnitOfMeasure).join(ItemGroup).all()
            
            if not items:
                print("   لا توجد أصناف مسجلة")
                return
            
            print(f"{'الكود':<8} {'الاسم':<25} {'المجموعة':<15} {'الوحدة':<10} {'السعر':<8}")
            print("-" * 80)
            
            for item in items:
                unit_name = item.unit.name if item.unit else "غير محدد"
                group_name = item.group.name if item.group else "غير محدد"
                print(f"{item.code:<8} {item.name[:24]:<25} {group_name:<15} {unit_name:<10} {item.selling_price:<8.2f}")
            
            print(f"\nإجمالي الأصناف: {len(items)}")
            
    except Exception as e:
        print(f"❌ خطأ في عرض البيانات: {e}")

if __name__ == "__main__":
    print("🚀 إدارة الأصناف مع وحدات القياس")
    print("=" * 50)
    
    # إضافة البيانات التجريبية
    success = add_sample_items()
    
    if success:
        # عرض الأصناف مع وحدات القياس
        list_items_with_units()
        print("\n✅ تمت العملية بنجاح!")
    else:
        print("\n❌ فشلت العملية!")
        sys.exit(1)
