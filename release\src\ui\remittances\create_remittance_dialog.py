# -*- coding: utf-8 -*-
"""
نافذة إنشاء حوالة جديدة
Create Remittance Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QPushButton, QLineEdit, QComboBox, 
                               QTextEdit, QDateEdit, QDoubleSpinBox, QFrame,
                               QGroupBox, QGridLayout, QMessageBox, QProgressBar,
                               QSpinBox, QCheckBox)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QFont, QPixmap

import sqlite3
from pathlib import Path
from datetime import datetime
import json

class CreateRemittanceDialog(QDialog):
    """نافذة إنشاء حوالة جديدة"""
    
    remittance_created = Signal(dict)  # إشارة عند إنشاء الحوالة
    
    def __init__(self, transfer_request_data=None, parent=None):
        super().__init__(parent)
        self.transfer_request_data = transfer_request_data
        self.setWindowTitle("إنشاء حوالة جديدة - ProShipment")
        self.setMinimumSize(900, 800)
        self.resize(1000, 850)
        self.setModal(True)

        # توسيط النافذة
        self.center_window()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.load_data()

        # ملء البيانات من طلب التحويل إذا كان متوفراً
        if self.transfer_request_data:
            self.fill_from_transfer_request()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # العنوان الرئيسي
        title_label = QLabel("💸 إنشاء حوالة جديدة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                margin-bottom: 20px;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #e74c3c, stop:1 #c0392b);
                border-radius: 10px;
            }
        """)
        layout.addWidget(title_label)

        # إنشاء التخطيط الرئيسي
        main_frame = QFrame()
        main_layout = QHBoxLayout(main_frame)
        main_layout.setSpacing(20)

        # الجانب الأيسر - معلومات المرسل والمستقبل
        left_layout = QVBoxLayout()
        
        sender_group = self.create_sender_section()
        left_layout.addWidget(sender_group)
        
        receiver_group = self.create_receiver_section()
        left_layout.addWidget(receiver_group)
        
        main_layout.addLayout(left_layout)

        # الجانب الأيمن - تفاصيل الحوالة
        right_layout = QVBoxLayout()
        
        remittance_group = self.create_remittance_section()
        right_layout.addWidget(remittance_group)
        
        fees_group = self.create_fees_section()
        right_layout.addWidget(fees_group)
        
        main_layout.addLayout(right_layout)

        layout.addWidget(main_frame)

        # قسم الملاحظات والمرفقات
        notes_group = self.create_notes_section()
        layout.addWidget(notes_group)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #e74c3c;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # أزرار الإجراءات
        buttons_frame = self.create_buttons_section()
        layout.addWidget(buttons_frame)

    def create_sender_section(self):
        """إنشاء قسم معلومات المرسل"""
        group = QGroupBox("📤 معلومات المرسل")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        
        layout = QFormLayout(group)
        layout.setSpacing(12)
        layout.setContentsMargins(15, 20, 15, 15)

        # اسم المرسل
        self.sender_name_input = QLineEdit()
        self.sender_name_input.setPlaceholderText("أدخل اسم المرسل...")
        layout.addRow("👤 الاسم:", self.sender_name_input)

        # هاتف المرسل
        self.sender_phone_input = QLineEdit()
        self.sender_phone_input.setPlaceholderText("أدخل رقم الهاتف...")
        layout.addRow("📱 الهاتف:", self.sender_phone_input)

        # هوية المرسل
        self.sender_id_input = QLineEdit()
        self.sender_id_input.setPlaceholderText("أدخل رقم الهوية...")
        layout.addRow("🆔 الهوية:", self.sender_id_input)

        # عنوان المرسل
        self.sender_address_input = QLineEdit()
        self.sender_address_input.setPlaceholderText("أدخل العنوان...")
        layout.addRow("📍 العنوان:", self.sender_address_input)

        return group

    def create_receiver_section(self):
        """إنشاء قسم معلومات المستقبل"""
        group = QGroupBox("📥 معلومات المستقبل")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        
        layout = QFormLayout(group)
        layout.setSpacing(12)
        layout.setContentsMargins(15, 20, 15, 15)

        # اسم المستقبل
        self.receiver_name_input = QLineEdit()
        self.receiver_name_input.setPlaceholderText("أدخل اسم المستقبل...")
        layout.addRow("👤 الاسم:", self.receiver_name_input)

        # هاتف المستقبل
        self.receiver_phone_input = QLineEdit()
        self.receiver_phone_input.setPlaceholderText("أدخل رقم الهاتف...")
        layout.addRow("📱 الهاتف:", self.receiver_phone_input)

        # البلد
        self.receiver_country_combo = QComboBox()
        self.receiver_country_combo.setEditable(True)
        layout.addRow("🌍 البلد:", self.receiver_country_combo)

        # المدينة
        self.receiver_city_input = QLineEdit()
        self.receiver_city_input.setPlaceholderText("أدخل المدينة...")
        layout.addRow("🏙️ المدينة:", self.receiver_city_input)

        return group

    def create_remittance_section(self):
        """إنشاء قسم تفاصيل الحوالة"""
        group = QGroupBox("💰 تفاصيل الحوالة")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #f39c12;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        
        layout = QFormLayout(group)
        layout.setSpacing(12)
        layout.setContentsMargins(15, 20, 15, 15)

        # رقم الحوالة
        self.remittance_number_input = QLineEdit()
        self.remittance_number_input.setPlaceholderText("سيتم إنشاؤه تلقائياً...")
        self.remittance_number_input.setReadOnly(True)
        layout.addRow("🔢 رقم الحوالة:", self.remittance_number_input)

        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0.01, 999999999.99)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" ريال")
        layout.addRow("💵 المبلغ:", self.amount_input)

        # العملة
        self.currency_combo = QComboBox()
        layout.addRow("💱 العملة:", self.currency_combo)

        # سعر الصرف
        self.exchange_rate_input = QDoubleSpinBox()
        self.exchange_rate_input.setRange(0.0001, 9999.9999)
        self.exchange_rate_input.setDecimals(4)
        self.exchange_rate_input.setValue(1.0000)
        layout.addRow("📈 سعر الصرف:", self.exchange_rate_input)

        # البنك/الصراف المرسل
        self.sender_bank_combo = QComboBox()
        layout.addRow("🏦 البنك المرسل:", self.sender_bank_combo)

        # البنك/الصراف المستقبل
        self.receiver_bank_combo = QComboBox()
        layout.addRow("🏪 البنك المستقبل:", self.receiver_bank_combo)

        # تاريخ الحوالة
        self.remittance_date_input = QDateEdit()
        self.remittance_date_input.setDate(QDate.currentDate())
        self.remittance_date_input.setCalendarPopup(True)
        layout.addRow("📅 تاريخ الحوالة:", self.remittance_date_input)

        return group

    def create_fees_section(self):
        """إنشاء قسم الرسوم والعمولات"""
        group = QGroupBox("💳 الرسوم والعمولات")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #9b59b6;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        
        layout = QFormLayout(group)
        layout.setSpacing(12)
        layout.setContentsMargins(15, 20, 15, 15)

        # رسوم التحويل
        self.transfer_fee_input = QDoubleSpinBox()
        self.transfer_fee_input.setRange(0.00, 99999.99)
        self.transfer_fee_input.setDecimals(2)
        self.transfer_fee_input.setSuffix(" ريال")
        layout.addRow("💰 رسوم التحويل:", self.transfer_fee_input)

        # العمولة
        self.commission_input = QDoubleSpinBox()
        self.commission_input.setRange(0.00, 99999.99)
        self.commission_input.setDecimals(2)
        self.commission_input.setSuffix(" ريال")
        layout.addRow("📊 العمولة:", self.commission_input)

        # المبلغ الإجمالي
        self.total_amount_input = QDoubleSpinBox()
        self.total_amount_input.setRange(0.00, 999999999.99)
        self.total_amount_input.setDecimals(2)
        self.total_amount_input.setSuffix(" ريال")
        self.total_amount_input.setReadOnly(True)
        layout.addRow("💯 المبلغ الإجمالي:", self.total_amount_input)

        return group

    def create_notes_section(self):
        """إنشاء قسم الملاحظات"""
        group = QGroupBox("📝 ملاحظات ومرفقات")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #1abc9c;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        
        layout = QVBoxLayout(group)
        layout.setContentsMargins(15, 20, 15, 15)

        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية حول الحوالة...")
        self.notes_input.setMaximumHeight(80)
        layout.addWidget(self.notes_input)

        return group

    def create_buttons_section(self):
        """إنشاء قسم الأزرار"""
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.addStretch()

        # زر إنشاء الحوالة
        self.create_btn = QPushButton("💸 إنشاء الحوالة")
        self.create_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 180px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        layout.addWidget(self.create_btn)

        # زر حفظ كمسودة
        self.save_draft_btn = QPushButton("💾 حفظ كمسودة")
        self.save_draft_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
        """)
        layout.addWidget(self.save_draft_btn)

        # زر إلغاء
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        layout.addWidget(self.cancel_btn)

        return frame

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.create_btn.clicked.connect(self.create_remittance)
        self.save_draft_btn.clicked.connect(self.save_as_draft)
        self.cancel_btn.clicked.connect(self.reject)
        
        # تحديث المبلغ الإجمالي عند تغيير المبلغ أو الرسوم
        self.amount_input.valueChanged.connect(self.calculate_total)
        self.transfer_fee_input.valueChanged.connect(self.calculate_total)
        self.commission_input.valueChanged.connect(self.calculate_total)

    def load_data(self):
        """تحميل البيانات الأساسية"""
        self.load_currencies()
        self.load_banks_and_exchanges()
        self.load_countries()
        self.generate_remittance_number()

    def load_currencies(self):
        """تحميل العملات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT code, name FROM currencies WHERE is_active = 1 ORDER BY name")
            currencies = cursor.fetchall()

            self.currency_combo.clear()
            self.currency_combo.addItem("اختر العملة...", None)

            for currency in currencies:
                self.currency_combo.addItem(f"{currency[1]} ({currency[0]})", currency[0])

            # تعيين الريال اليمني كافتراضي
            for i in range(self.currency_combo.count()):
                if self.currency_combo.itemData(i) == "YER":
                    self.currency_combo.setCurrentIndex(i)
                    break

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل العملات: {e}")

    def load_banks_and_exchanges(self):
        """تحميل البنوك والصرافات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل البنوك
            cursor.execute("SELECT id, name FROM banks WHERE is_active = 1 ORDER BY name")
            banks = cursor.fetchall()

            # تحميل الصرافات
            cursor.execute("SELECT id, name FROM exchanges WHERE is_active = 1 ORDER BY name")
            exchanges = cursor.fetchall()

            # ملء قائمة البنوك/الصرافات المرسلة
            self.sender_bank_combo.clear()
            self.sender_bank_combo.addItem("اختر البنك/الصراف المرسل...", None)

            for bank in banks:
                self.sender_bank_combo.addItem(f"🏦 {bank[1]}", f"bank_{bank[0]}")

            for exchange in exchanges:
                self.sender_bank_combo.addItem(f"💱 {exchange[1]}", f"exchange_{exchange[0]}")

            # ملء قائمة البنوك/الصرافات المستقبلة
            self.receiver_bank_combo.clear()
            self.receiver_bank_combo.addItem("اختر البنك/الصراف المستقبل...", None)

            for bank in banks:
                self.receiver_bank_combo.addItem(f"🏦 {bank[1]}", f"bank_{bank[0]}")

            for exchange in exchanges:
                self.receiver_bank_combo.addItem(f"💱 {exchange[1]}", f"exchange_{exchange[0]}")

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل البنوك والصرافات: {e}")

    def load_countries(self):
        """تحميل قائمة البلدان"""
        countries = [
            "اليمن", "السعودية", "الإمارات", "قطر", "الكويت", "البحرين", "عمان",
            "الأردن", "لبنان", "سوريا", "العراق", "مصر", "السودان", "ليبيا",
            "المغرب", "الجزائر", "تونس", "موريتانيا", "الصومال", "جيبوتي",
            "أمريكا", "بريطانيا", "ألمانيا", "فرنسا", "إيطاليا", "هولندا",
            "السويد", "النرويج", "الدنمارك", "كندا", "أستراليا", "ماليزيا",
            "إندونيسيا", "تركيا", "الهند", "باكستان", "بنغلاديش", "الفلبين"
        ]

        self.receiver_country_combo.clear()
        self.receiver_country_combo.addItem("اختر البلد...")
        self.receiver_country_combo.addItems(countries)

    def generate_remittance_number(self):
        """إنشاء رقم حوالة جديد"""
        try:
            # إنشاء رقم حوالة بناءً على التاريخ والوقت
            now = datetime.now()
            remittance_number = f"REM{now.strftime('%Y%m%d%H%M%S')}"
            self.remittance_number_input.setText(remittance_number)
        except Exception as e:
            print(f"خطأ في إنشاء رقم الحوالة: {e}")

    def calculate_total(self):
        """حساب المبلغ الإجمالي"""
        try:
            amount = self.amount_input.value()
            transfer_fee = self.transfer_fee_input.value()
            commission = self.commission_input.value()

            total = amount + transfer_fee + commission
            self.total_amount_input.setValue(total)
        except Exception as e:
            print(f"خطأ في حساب المبلغ الإجمالي: {e}")

    def fill_from_transfer_request(self):
        """ملء البيانات من طلب التحويل أو طلب الحوالة"""
        if not self.transfer_request_data:
            return

        try:
            data = self.transfer_request_data

            # معلومات المرسل
            self.sender_name_input.setText(data.get('sender_name', ''))
            self.sender_phone_input.setText(data.get('sender_phone', ''))
            self.sender_id_input.setText(data.get('sender_id', ''))
            self.sender_address_input.setText(data.get('sender_address', ''))

            # معلومات المستقبل
            self.receiver_name_input.setText(data.get('receiver_name', ''))
            self.receiver_phone_input.setText(data.get('receiver_phone', ''))
            self.receiver_city_input.setText(data.get('receiver_city', ''))

            # تعيين البلد
            country = data.get('receiver_country', '')
            if country and country != "اختر البلد...":
                index = self.receiver_country_combo.findText(country)
                if index >= 0:
                    self.receiver_country_combo.setCurrentIndex(index)

            # المبلغ والعملة
            amount = data.get('amount', 0.0)
            if amount > 0:
                self.amount_input.setValue(amount)

            # العملة المرسلة (source_currency من طلب التحويل/الحوالة)
            source_currency = data.get('source_currency', '')
            if source_currency:
                for i in range(self.currency_combo.count()):
                    if self.currency_combo.itemData(i) == source_currency:
                        self.currency_combo.setCurrentIndex(i)
                        break

            # سعر الصرف
            exchange_rate = data.get('exchange_rate', 1.0)
            if exchange_rate > 0:
                self.exchange_rate_input.setValue(exchange_rate)

            # البنوك
            sender_bank = data.get('sender_bank', '')
            if sender_bank:
                for i in range(self.sender_bank_combo.count()):
                    if self.sender_bank_combo.itemData(i) == sender_bank:
                        self.sender_bank_combo.setCurrentIndex(i)
                        break

            receiver_bank = data.get('receiver_bank', '')
            if receiver_bank:
                for i in range(self.receiver_bank_combo.count()):
                    if self.receiver_bank_combo.itemData(i) == receiver_bank:
                        self.receiver_bank_combo.setCurrentIndex(i)
                        break

            # تاريخ التحويل
            transfer_date = data.get('transfer_date', '')
            if transfer_date:
                try:
                    date = QDate.fromString(transfer_date, "yyyy-MM-dd")
                    if date.isValid():
                        self.remittance_date_input.setDate(date)
                except:
                    pass

            # الملاحظات
            notes = data.get('notes', '')
            request_id = data.get('request_id', '') or data.get('request_number', '')
            priority = data.get('priority', '')

            if notes or request_id or priority:
                combined_notes = ""

                # تحديد نوع المصدر
                if 'request_number' in data:
                    combined_notes += f"من طلب الحوالة رقم: {request_id}\n"
                elif 'request_id' in data:
                    combined_notes += f"من طلب التحويل رقم: {request_id[:8]}\n"

                if notes:
                    combined_notes += f"ملاحظات الطلب: {notes}\n"

                if priority:
                    combined_notes += f"الأولوية: {priority}\n"

                # إضافة معلومات إضافية حسب نوع الطلب
                if 'transfer_type' in data:
                    combined_notes += f"نوع التحويل: {data.get('transfer_type', '')}\n"

                if 'delivery_method' in data:
                    combined_notes += f"طريقة الاستلام: {data.get('delivery_method', '')}\n"

                # معلومات العملة المستهدفة
                target_currency = data.get('target_currency', '')
                if target_currency and target_currency != source_currency:
                    combined_notes += f"العملة المستهدفة: {target_currency}\n"

                self.notes_input.setPlainText(combined_notes.strip())

            # تحديث المبلغ الإجمالي
            self.calculate_total()

        except Exception as e:
            print(f"خطأ في ملء البيانات من الطلب: {e}")
            QMessageBox.warning(self, "تحذير", f"حدث خطأ في ملء بعض البيانات من الطلب:\n{str(e)}")



    def validate_form(self):
        """التحقق من صحة النموذج"""
        if not self.sender_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المرسل")
            self.sender_name_input.setFocus()
            return False

        if not self.receiver_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المستقبل")
            self.receiver_name_input.setFocus()
            return False

        if self.amount_input.value() <= 0:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال مبلغ الحوالة")
            self.amount_input.setFocus()
            return False

        if self.currency_combo.currentData() is None:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى اختيار العملة")
            self.currency_combo.setFocus()
            return False

        return True

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        return {
            'remittance_number': self.remittance_number_input.text().strip(),
            'sender_name': self.sender_name_input.text().strip(),
            'sender_phone': self.sender_phone_input.text().strip(),
            'sender_id': self.sender_id_input.text().strip(),
            'sender_address': self.sender_address_input.text().strip(),
            'receiver_name': self.receiver_name_input.text().strip(),
            'receiver_phone': self.receiver_phone_input.text().strip(),
            'receiver_country': self.receiver_country_combo.currentText(),
            'receiver_city': self.receiver_city_input.text().strip(),
            'amount': self.amount_input.value(),
            'currency': self.currency_combo.currentData(),
            'exchange_rate': self.exchange_rate_input.value(),
            'sender_bank': self.sender_bank_combo.currentData(),
            'receiver_bank': self.receiver_bank_combo.currentData(),
            'transfer_fee': self.transfer_fee_input.value(),
            'commission': self.commission_input.value(),
            'total_amount': self.total_amount_input.value(),
            'remittance_date': self.remittance_date_input.date().toString("yyyy-MM-dd"),
            'notes': self.notes_input.toPlainText().strip(),
            'created_at': datetime.now().isoformat(),
            'status': 'active'
        }

    def create_remittance(self):
        """إنشاء الحوالة"""
        if not self.validate_form():
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.create_btn.setEnabled(False)

            # جمع البيانات
            remittance_data = self.collect_form_data()

            # حفظ الحوالة في قاعدة البيانات
            remittance_id = self.save_remittance_to_database(remittance_data)

            if remittance_id:
                # إضافة معرف الحوالة للبيانات
                remittance_data['id'] = remittance_id

                QMessageBox.information(self, "نجح الإنشاء",
                                      f"تم إنشاء الحوالة بنجاح\nرقم الحوالة: {remittance_data['remittance_number']}")

                # إرسال إشارة مع بيانات الحوالة
                self.remittance_created.emit(remittance_data)
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إنشاء الحوالة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء الحوالة:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.create_btn.setEnabled(True)

    def save_as_draft(self):
        """حفظ الحوالة كمسودة"""
        try:
            # جمع البيانات
            remittance_data = self.collect_form_data()
            remittance_data['status'] = 'draft'

            # حفظ المسودة
            draft_id = self.save_remittance_to_database(remittance_data)

            if draft_id:
                QMessageBox.information(self, "تم الحفظ",
                                      f"تم حفظ مسودة الحوالة بنجاح\nرقم المسودة: {draft_id}")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ مسودة الحوالة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ المسودة:\n{str(e)}")

    def save_remittance_to_database(self, data):
        """حفظ الحوالة في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء جدول الحوالات إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS remittances (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    remittance_number TEXT UNIQUE NOT NULL,
                    sender_name TEXT NOT NULL,
                    sender_phone TEXT,
                    sender_id TEXT,
                    sender_address TEXT,
                    receiver_name TEXT NOT NULL,
                    receiver_phone TEXT,
                    receiver_country TEXT,
                    receiver_city TEXT,
                    amount DECIMAL(15,2) NOT NULL,
                    currency TEXT NOT NULL,
                    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
                    sender_bank TEXT,
                    receiver_bank TEXT,
                    transfer_fee DECIMAL(10,2) DEFAULT 0.00,
                    commission DECIMAL(10,2) DEFAULT 0.00,
                    total_amount DECIMAL(15,2) NOT NULL,
                    remittance_date DATE,
                    notes TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # إدراج الحوالة
            cursor.execute("""
                INSERT INTO remittances (
                    remittance_number, sender_name, sender_phone, sender_id, sender_address,
                    receiver_name, receiver_phone, receiver_country, receiver_city,
                    amount, currency, exchange_rate, sender_bank, receiver_bank,
                    transfer_fee, commission, total_amount, remittance_date, notes, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['remittance_number'], data['sender_name'], data['sender_phone'],
                data['sender_id'], data['sender_address'], data['receiver_name'],
                data['receiver_phone'], data['receiver_country'], data['receiver_city'],
                data['amount'], data['currency'], data['exchange_rate'],
                data['sender_bank'], data['receiver_bank'], data['transfer_fee'],
                data['commission'], data['total_amount'], data['remittance_date'],
                data['notes'], data['status'], data['created_at']
            ))

            remittance_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return remittance_id

        except Exception as e:
            print(f"خطأ في حفظ الحوالة: {e}")
            return None
