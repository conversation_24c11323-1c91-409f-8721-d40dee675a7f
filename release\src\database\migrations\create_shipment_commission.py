# -*- coding: utf-8 -*-
"""
إنشاء جدول عمولة الشحنات
Create Shipment Commission Table Migration
"""

import sqlite3
import os

def create_shipment_commission_table():
    """إنشاء جدول عمولة الشحنات"""
    
    # مسار قاعدة البيانات
    db_path = 'data/proshipment.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 فحص وجود جدول عمولة الشحنات...")
        
        # فحص وجود الجدول
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='shipment_commissions'
        """)
        
        if cursor.fetchone():
            print("ℹ️ جدول عمولة الشحنات موجود بالفعل")
            return True
        
        print("🔨 إنشاء جدول عمولة الشحنات...")
        
        # إنشاء الجدول
        cursor.execute("""
            CREATE TABLE shipment_commissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_representative_id INTEGER NOT NULL,
                calculation_type VARCHAR(50) DEFAULT 'quantity',
                min_quantity REAL DEFAULT 0,
                max_quantity REAL,
                quantity_commission_rate REAL DEFAULT 0,
                min_value REAL DEFAULT 0,
                max_value REAL,
                value_commission_percentage REAL DEFAULT 0,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_representative_id) REFERENCES supplier_representatives (id)
            )
        """)
        
        # إنشاء فهارس
        cursor.execute("""
            CREATE INDEX idx_shipment_commissions_supplier_rep 
            ON shipment_commissions(supplier_representative_id)
        """)
        
        cursor.execute("""
            CREATE INDEX idx_shipment_commissions_type 
            ON shipment_commissions(calculation_type)
        """)
        
        cursor.execute("""
            CREATE INDEX idx_shipment_commissions_active 
            ON shipment_commissions(is_active)
        """)
        
        print("✅ تم إنشاء جدول عمولة الشحنات بنجاح!")
        
        # حفظ التغييرات
        conn.commit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول عمولة الشحنات: {str(e)}")
        return False
        
    finally:
        if conn:
            conn.close()

def remove_old_commission_fields():
    """حذف حقول العمولة القديمة من جدول supplier_representatives"""
    
    db_path = 'data/proshipment.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🧹 حذف حقول العمولة القديمة...")
        
        # فحص وجود الحقول القديمة
        cursor.execute("PRAGMA table_info(supplier_representatives)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        old_fields = ['commission_type', 'commission_factor', 'commission_percentage']
        fields_to_remove = [field for field in old_fields if field in column_names]
        
        if not fields_to_remove:
            print("ℹ️ لا توجد حقول عمولة قديمة للحذف")
            return True
        
        # إنشاء جدول مؤقت بدون الحقول القديمة
        cursor.execute("""
            CREATE TABLE supplier_representatives_temp AS
            SELECT id, supplier_id, representative_id, is_primary, assigned_date, notes, is_active, created_at, updated_at
            FROM supplier_representatives
        """)
        
        # حذف الجدول الأصلي
        cursor.execute("DROP TABLE supplier_representatives")
        
        # إعادة تسمية الجدول المؤقت
        cursor.execute("ALTER TABLE supplier_representatives_temp RENAME TO supplier_representatives")
        
        # إعادة إنشاء الفهارس
        cursor.execute("""
            CREATE INDEX idx_supplier_representative_unique 
            ON supplier_representatives(supplier_id, representative_id)
        """)
        
        print("✅ تم حذف حقول العمولة القديمة بنجاح!")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حذف حقول العمولة القديمة: {str(e)}")
        return False
        
    finally:
        if conn:
            conn.close()

def test_shipment_commission():
    """اختبار جدول عمولة الشحنات"""
    try:
        db_path = 'data/proshipment.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🧪 اختبار جدول عمولة الشحنات...")
        
        # إدراج بيانات تجريبية
        cursor.execute("""
            INSERT INTO shipment_commissions 
            (supplier_representative_id, calculation_type, min_quantity, quantity_commission_rate, description)
            VALUES (1, 'quantity', 100, 5.0, 'عمولة حسب الكمية - 5 ريال لكل وحدة من 100 وحدة فأكثر')
        """)
        
        cursor.execute("""
            INSERT INTO shipment_commissions 
            (supplier_representative_id, calculation_type, min_value, value_commission_percentage, description)
            VALUES (1, 'value', 10000, 2.5, 'عمولة حسب القيمة - 2.5% من قيمة البضاعة من 10000 ريال فأكثر')
        """)
        
        # عرض البيانات
        cursor.execute("""
            SELECT id, calculation_type, min_quantity, quantity_commission_rate, 
                   min_value, value_commission_percentage, description
            FROM shipment_commissions
            WHERE is_active = 1
        """)
        
        commissions = cursor.fetchall()
        print("📋 قواعد العمولة:")
        for commission in commissions:
            if commission[1] == 'quantity':
                print(f"   - حسب الكمية: من {commission[2]} وحدة → {commission[3]} ريال/وحدة")
            else:
                print(f"   - حسب القيمة: من {commission[4]} ريال → {commission[5]}%")
            print(f"     الوصف: {commission[6]}")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إنشاء نظام عمولة الشحنات")
    print("=" * 50)
    
    if create_shipment_commission_table():
        if remove_old_commission_fields():
            print("\n" + "=" * 50)
            test_shipment_commission()
            print("\n🎉 تم إكمال العملية بنجاح!")
        else:
            print("\n❌ فشل في حذف حقول العمولة القديمة")
    else:
        print("\n❌ فشل في إنشاء جدول عمولة الشحنات")
