/* ملف الرسوم المتحركة والتأثيرات المتقدمة */
/* Advanced Animations and Effects */

/* الرسوم المتحركة الأساسية */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes zoomOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.3);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes flip {
    0% {
        transform: perspective(400px) rotateY(0);
    }
    40% {
        transform: perspective(400px) translateZ(150px) rotateY(170deg);
    }
    50% {
        transform: perspective(400px) translateZ(150px) rotateY(190deg) scale(1);
    }
    80% {
        transform: perspective(400px) rotateY(360deg) scale(0.95);
    }
    100% {
        transform: perspective(400px) scale(1);
    }
}

/* تأثير الموجة عند النقر */
@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

/* تأثيرات التحميل */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes spinPulse {
    0% {
        transform: rotate(0deg) scale(1);
    }
    50% {
        transform: rotate(180deg) scale(0.8);
    }
    100% {
        transform: rotate(360deg) scale(1);
    }
}

.loading-spinner {
    display: inline-block;
    position: relative;
    width: 40px;
    height: 40px;
}

.spinner-ring {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 32px;
    height: 32px;
    margin: 4px;
    border: 3px solid transparent;
    border-top-color: #667eea;
    border-radius: 50%;
    animation: spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
}

.spinner-ring:nth-child(1) {
    animation-delay: -0.45s;
}

.spinner-ring:nth-child(2) {
    animation-delay: -0.3s;
}

.spinner-ring:nth-child(3) {
    animation-delay: -0.15s;
}

/* تأثيرات الإشعارات */
@keyframes slideInFromTop {
    from {
        transform: translateY(-100px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideOutToTop {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(-100px);
        opacity: 0;
    }
}

.advanced-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    max-width: 500px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    transform: translateY(-100px);
    opacity: 0;
    transition: all 0.3s ease;
}

.advanced-notification.show {
    transform: translateY(0);
    opacity: 1;
    animation: slideInFromTop 0.3s ease;
}

.advanced-notification.hide {
    animation: slideOutToTop 0.3s ease;
}

.notification-content {
    display: flex;
    align-items: center;
    padding: 15px 20px;
}

.notification-icon {
    font-size: 20px;
    margin-left: 10px;
}

.notification-message {
    flex: 1;
    margin: 0 15px;
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #7f8c8d;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    color: #e74c3c;
}

/* أنواع الإشعارات */
.advanced-notification.success {
    border-left: 4px solid #2ecc71;
}

.advanced-notification.error {
    border-left: 4px solid #e74c3c;
}

.advanced-notification.warning {
    border-left: 4px solid #f39c12;
}

.advanced-notification.info {
    border-left: 4px solid #3498db;
}

/* تأثيرات التمرير */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: all 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

/* تأثيرات الظهور التدريجي */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.5s ease-out;
}

.zoom-in {
    animation: zoomIn 0.5s ease-out;
}

.bounce-in {
    animation: bounce 1s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

.rotate-animation {
    animation: rotate 2s linear infinite;
}

.flip-animation {
    animation: flip 1s ease-in-out;
}

/* تأثيرات التأخير للعناصر المتعددة */
.delay-100 {
    animation-delay: 0.1s;
}

.delay-200 {
    animation-delay: 0.2s;
}

.delay-300 {
    animation-delay: 0.3s;
}

.delay-400 {
    animation-delay: 0.4s;
}

.delay-500 {
    animation-delay: 0.5s;
}

/* تأثيرات السرعة */
.duration-fast {
    animation-duration: 0.3s;
}

.duration-normal {
    animation-duration: 0.6s;
}

.duration-slow {
    animation-duration: 1s;
}

/* تأثيرات التكرار */
.infinite {
    animation-iteration-count: infinite;
}

.repeat-2 {
    animation-iteration-count: 2;
}

.repeat-3 {
    animation-iteration-count: 3;
}

/* تأثيرات الإيقاف المؤقت */
.paused {
    animation-play-state: paused;
}

.running {
    animation-play-state: running;
}
