#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق الآن
Run Application Now
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_app():
    """تشغيل التطبيق"""
    
    print("🚀 تشغيل التطبيق...")
    
    try:
        # فحص الاستيرادات الأساسية
        print("📋 فحص الاستيرادات...")
        
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        print("   ✅ PySide6")
        
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        print("   ✅ Database modules")
        
        from src.ui.main_window import MainWindow
        print("   ✅ Main window")
        
        from src.utils.arabic_support import setup_arabic_support
        print("   ✅ Arabic support")
        
        # إعداد قاعدة البيانات
        print("\n🔧 إعداد قاعدة البيانات...")
        
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        print(f"   📊 نوع قاعدة البيانات: {config.type.value}")
        
        db_manager = UniversalDatabaseManager(config)
        print("   ✅ تم إنشاء مدير قاعدة البيانات")
        
        # اختبار الاتصال
        print("   🔗 اختبار الاتصال...")
        if db_manager.test_connection():
            print("   ✅ الاتصال ناجح")
        else:
            print("   ❌ فشل الاتصال")
            return False
        
        # تهيئة قاعدة البيانات
        print("   🏗️ تهيئة قاعدة البيانات...")
        if db_manager.initialize_database():
            print("   ✅ تم تهيئة قاعدة البيانات")
        else:
            print("   ⚠️ مشكلة في التهيئة، لكن سنحاول المتابعة")
        
        # إنشاء التطبيق
        print("\n🖥️ إنشاء واجهة التطبيق...")
        
        app = QApplication(sys.argv)
        print("   ✅ تم إنشاء QApplication")
        
        # إعداد دعم اللغة العربية
        setup_arabic_support(app)
        print("   ✅ تم إعداد دعم اللغة العربية")
        
        # إنشاء النافذة الرئيسية
        print("   🏠 إنشاء النافذة الرئيسية...")
        window = MainWindow()
        print("   ✅ تم إنشاء النافذة الرئيسية")
        
        # عرض النافذة
        window.show()
        print("   ✅ تم عرض النافذة")
        
        print("\n🎉 تم تشغيل التطبيق بنجاح!")
        print("💡 يمكنك الآن استخدام التطبيق")
        print("📋 للوصول لإدارة وحدات القياس:")
        print("   1. اذهب إلى قائمة 'إدارة الأصناف'")
        print("   2. اختر 'وحدات القياس'")
        
        # تشغيل حلقة الأحداث
        print("\n⚡ بدء حلقة الأحداث...")
        return app.exec()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت جميع المكتبات المطلوبة:")
        print("   pip install PySide6 cx_Oracle SQLAlchemy")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        print("🔍 تفاصيل الخطأ:")
        traceback.print_exc()
        return False

def check_requirements():
    """فحص المتطلبات"""
    
    print("🔍 فحص المتطلبات...")
    
    requirements = [
        ("PySide6", "pip install PySide6"),
        ("cx_Oracle", "pip install cx_Oracle"),
        ("SQLAlchemy", "pip install SQLAlchemy")
    ]
    
    missing = []
    
    for module, install_cmd in requirements:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module} - غير مثبت")
            missing.append((module, install_cmd))
    
    if missing:
        print("\n💡 المكتبات المفقودة:")
        for module, cmd in missing:
            print(f"   {cmd}")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def main():
    """الدالة الرئيسية"""
    
    print("🚀 تشغيل تطبيق إدارة الشحنات")
    print("=" * 40)
    
    # فحص المتطلبات
    if not check_requirements():
        print("❌ يرجى تثبيت المكتبات المفقودة أولاً")
        return False
    
    # تشغيل التطبيق
    result = run_app()
    
    if result:
        print("\n✅ تم إغلاق التطبيق بنجاح")
    else:
        print("\n❌ حدث خطأ في التطبيق")
    
    return result

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
