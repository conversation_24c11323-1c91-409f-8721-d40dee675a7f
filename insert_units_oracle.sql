-- إدراج وحدات القياس الأساسية في Oracle
-- Insert Basic Units of Measurement in Oracle

-- التحقق من وجود الجداول أولاً
-- Check if tables exist first

-- إدراج وحدات القياس
-- Insert Units of Measurement
INSERT INTO units_of_measure (id, name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at)
SELECT * FROM (
    SELECT 1 as id, 'كيلوجرام' as name, 'Kilogram' as name_en, 'كجم' as symbol, 'kg' as symbol_en, 'وحدة قياس الوزن الأساسية' as description, 1 as is_active, SYSDATE as created_at, SYSDATE as updated_at FROM dual
    UNION ALL
    SELECT 2, 'جرام', 'Gram', 'جم', 'g', 'وحدة قياس الوزن الصغيرة', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 3, 'طن', 'Ton', 'طن', 't', 'وحدة قياس الوزن الكبيرة', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 4, 'متر', 'Meter', 'م', 'm', 'وحدة قياس الطول الأساسية', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 5, 'سنتيمتر', 'Centimeter', 'سم', 'cm', 'وحدة قياس الطول الصغيرة', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 6, 'مليمتر', 'Millimeter', 'مم', 'mm', 'وحدة قياس الطول الدقيقة', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 7, 'متر مربع', 'Square Meter', 'م²', 'm²', 'وحدة قياس المساحة', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 8, 'متر مكعب', 'Cubic Meter', 'م³', 'm³', 'وحدة قياس الحجم', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 9, 'لتر', 'Liter', 'لتر', 'L', 'وحدة قياس السوائل', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 10, 'قطعة', 'Piece', 'قطعة', 'pcs', 'وحدة العد الأساسية', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 11, 'عبوة', 'Package', 'عبوة', 'pkg', 'وحدة التعبئة', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 12, 'صندوق', 'Box', 'صندوق', 'box', 'وحدة التعبئة الكبيرة', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 13, 'كرتون', 'Carton', 'كرتون', 'ctn', 'وحدة التعبئة المتوسطة', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 14, 'زوج', 'Pair', 'زوج', 'pair', 'وحدة للأشياء المزدوجة', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 15, 'دستة', 'Dozen', 'دستة', 'dz', '12 قطعة', 1, SYSDATE, SYSDATE FROM dual
) new_units
WHERE NOT EXISTS (
    SELECT 1 FROM units_of_measure WHERE name = new_units.name
);

-- إدراج مجموعات الأصناف
-- Insert Item Groups
INSERT INTO item_groups (id, name, name_en, description, is_active, created_at, updated_at)
SELECT * FROM (
    SELECT 1 as id, 'إلكترونيات' as name, 'Electronics' as name_en, 'الأجهزة والمعدات الإلكترونية' as description, 1 as is_active, SYSDATE as created_at, SYSDATE as updated_at FROM dual
    UNION ALL
    SELECT 2, 'ملابس', 'Clothing', 'الملابس والأزياء', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 3, 'أغذية', 'Food', 'المواد الغذائية والمشروبات', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 4, 'أدوات منزلية', 'Home Appliances', 'الأدوات والأجهزة المنزلية', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 5, 'مواد بناء', 'Construction Materials', 'مواد ومعدات البناء', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 6, 'قطع غيار', 'Spare Parts', 'قطع الغيار والمكونات', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 7, 'مستحضرات تجميل', 'Cosmetics', 'مستحضرات التجميل والعناية', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 8, 'كتب ومطبوعات', 'Books & Publications', 'الكتب والمطبوعات والقرطاسية', 1, SYSDATE, SYSDATE FROM dual
) new_groups
WHERE NOT EXISTS (
    SELECT 1 FROM item_groups WHERE name = new_groups.name
);

-- إدراج العملات الأساسية
-- Insert Basic Currencies
INSERT INTO currencies (id, code, name, name_en, symbol, exchange_rate, is_base, is_active, created_at, updated_at)
SELECT * FROM (
    SELECT 1 as id, 'SAR' as code, 'ريال سعودي' as name, 'Saudi Riyal' as name_en, 'ر.س' as symbol, 1.0 as exchange_rate, 1 as is_base, 1 as is_active, SYSDATE as created_at, SYSDATE as updated_at FROM dual
    UNION ALL
    SELECT 2, 'USD', 'دولار أمريكي', 'US Dollar', '$', 3.75, 0, 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 3, 'EUR', 'يورو', 'Euro', '€', 4.10, 0, 1, SYSDATE, SYSDATE FROM dual
) new_currencies
WHERE NOT EXISTS (
    SELECT 1 FROM currencies WHERE code = new_currencies.code
);

-- إدراج إعدادات النظام الأساسية
-- Insert Basic System Settings
INSERT INTO system_settings (id, key, value, description, category, data_type, is_system, created_at, updated_at)
SELECT * FROM (
    SELECT 1 as id, 'app_name' as key, 'نظام إدارة الشحنات' as value, 'اسم التطبيق' as description, 'general' as category, 'string' as data_type, 0 as is_system, SYSDATE as created_at, SYSDATE as updated_at FROM dual
    UNION ALL
    SELECT 2, 'app_version', '2.0.0', 'إصدار التطبيق', 'general', 'string', 0, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 3, 'database_type', 'ORACLE', 'نوع قاعدة البيانات', 'system', 'string', 1, SYSDATE, SYSDATE FROM dual
    UNION ALL
    SELECT 4, 'default_currency', 'SAR', 'العملة الافتراضية', 'financial', 'string', 0, SYSDATE, SYSDATE FROM dual
) new_settings
WHERE NOT EXISTS (
    SELECT 1 FROM system_settings WHERE key = new_settings.key
);

-- إدراج بيانات الشركة الافتراضية
-- Insert Default Company Data
INSERT INTO companies (id, name, name_en, address, phone, email, is_active, created_at, updated_at)
SELECT * FROM (
    SELECT 1 as id, 'شركة الشحنات المتقدمة' as name, 'Advanced Shipping Company' as name_en, 'الرياض، المملكة العربية السعودية' as address, '+966-11-1234567' as phone, '<EMAIL>' as email, 1 as is_active, SYSDATE as created_at, SYSDATE as updated_at FROM dual
) new_company
WHERE NOT EXISTS (
    SELECT 1 FROM companies WHERE name = new_company.name
);

-- إدراج السنة المالية الحالية
-- Insert Current Fiscal Year
INSERT INTO fiscal_years (id, year, start_date, end_date, is_current, created_at, updated_at)
SELECT * FROM (
    SELECT 1 as id, EXTRACT(YEAR FROM SYSDATE) as year, 
           TO_DATE('01/01/' || EXTRACT(YEAR FROM SYSDATE), 'DD/MM/YYYY') as start_date,
           TO_DATE('31/12/' || EXTRACT(YEAR FROM SYSDATE), 'DD/MM/YYYY') as end_date,
           1 as is_current, SYSDATE as created_at, SYSDATE as updated_at FROM dual
) new_fiscal_year
WHERE NOT EXISTS (
    SELECT 1 FROM fiscal_years WHERE year = EXTRACT(YEAR FROM SYSDATE)
);

-- تأكيد التغييرات
COMMIT;

-- عرض النتائج
SELECT 'Units of Measure' as table_name, COUNT(*) as record_count FROM units_of_measure
UNION ALL
SELECT 'Item Groups', COUNT(*) FROM item_groups
UNION ALL
SELECT 'Currencies', COUNT(*) FROM currencies
UNION ALL
SELECT 'System Settings', COUNT(*) FROM system_settings
UNION ALL
SELECT 'Companies', COUNT(*) FROM companies
UNION ALL
SELECT 'Fiscal Years', COUNT(*) FROM fiscal_years;

-- عرض وحدات القياس المدرجة
SELECT 'وحدات القياس المدرجة:' as info FROM dual;
SELECT name, symbol, name_en, symbol_en FROM units_of_measure ORDER BY id;

-- عرض مجموعات الأصناف المدرجة
SELECT 'مجموعات الأصناف المدرجة:' as info FROM dual;
SELECT name, name_en FROM item_groups ORDER BY id;
