#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة شحنة جديدة نهائية مع أزرار التحكم العاملة
هذه النافذة تحل محل النافذة الأصلية المعقدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                               QTabWidget, QWidget, QGroupBox, QFormLayout,
                               QLineEdit, QComboBox, QTextEdit, QDateEdit,
                               QSpinBox, QTableWidget, QMessageBox, QLabel,
                               QFrame, QToolBar, QDialogButtonBox, QApplication,
                               QHeaderView, QAbstractItem<PERSON>iew, QGridLayout,
                               QDoubleSpinBox, QTableWidgetItem, QProgressDialog,
                               QCheckBox, QRadioButton, QButtonGroup)
from ..widgets.flexible_date_edit import FlexibleDateEdit
from ..widgets.smart_shipping_company_widget import SmartShippingCompanyWidget
from PySide6.QtCore import Signal, QDate, Qt, QSize, QLocale, QThread, QObject, QTimer
from PySide6.QtGui import QFont, QAction, QKeySequence, QShortcut

# استيراد النماذج المطلوبة لحفظ البيانات
from ...database.database_manager import DatabaseManager
from ...database.models import Shipment, ShipmentItem, Container, ShipmentDocument, PurchaseOrder, PurchaseOrderItem
from ...utils.formatters import format_date, format_datetime, format_number, format_currency
from ..dialogs.auto_fill_dialog import AutoFillDialog


class ShipmentSaveWorker(QObject):
    """Worker class لحفظ الشحنة في الخلفية - معطلة لصالح الحفظ المباشر"""

    # إشارات للتواصل مع الواجهة الرئيسية
    progress_updated = Signal(int, str)  # النسبة المئوية والرسالة
    save_completed = Signal(bool, str, object)  # نجح/فشل، رسالة، بيانات الشحنة
    error_occurred = Signal(str)

    def __init__(self):
        super().__init__()
        self.db_manager = None  # معطل
        self._should_stop = True  # دائماً متوقف

    def stop(self):
        """إيقاف العملية"""
        self._should_stop = True

    def save_shipment_async(self, shipment_data, shipment_items, containers_data, documents_data, is_edit_mode, current_shipment_id):
        """حفظ الشحنة بشكل غير متزامن - معطلة"""
        print("⚠️ تم استدعاء save_shipment_async المعطلة")
        self.error_occurred.emit("Worker class معطل - يجب استخدام الحفظ المباشر")
        return




class NewShipmentWindow(QDialog):
    """نافذة شحنة جديدة نهائية مع أزرار التحكم العاملة"""

    shipment_saved = Signal(int)
    shipment_deleted = Signal(int)
    
    def __init__(self, parent=None, shipment_id=None):
        super().__init__(parent)
        self.current_shipment_id = shipment_id  # معرف الشحنة للتعديل
        self.db_manager = DatabaseManager()
        self.selected_supplier_id = None  # معرف المورد المختار
        self.is_edit_mode = shipment_id is not None  # وضع التعديل
        self._is_saving = False  # متغير لتجنب الحفظ المتداخل

        # إعداد نظام الحفظ غير المتزامن
        self.save_thread = None
        self.save_worker = None
        self.progress_dialog = None

        self.setup_ui()
        self.setup_connections()

        # إعداد التعبئة التلقائية حسب الوضع
        self.configure_auto_fill_mode()

        # إذا كان في وضع التعديل، التحقق من حالة النظام أولاً
        if self.is_edit_mode:
            self.check_system_state_and_load_data()

    def configure_auto_fill_mode(self):
        """إعداد وضع التعبئة التلقائية - معطلة افتراضياً في جميع الأوضاع"""
        try:
            if hasattr(self, 'smart_shipping_company_widget'):
                # التعبئة التلقائية معطلة افتراضياً في جميع الأوضاع
                self.smart_shipping_company_widget.set_auto_fill_enabled(False)
                # تحديث حالة الزر
                self.smart_shipping_company_widget.auto_fill_toggle_button.setChecked(False)
                self.smart_shipping_company_widget.auto_fill_toggle_button.setText("🔒 تعطيل")
                print("🔒 التعبئة التلقائية معطلة افتراضياً - يمكن تفعيلها يدوياً")
        except Exception as e:
            print(f"تحذير في إعداد وضع التعبئة التلقائية: {e}")

    def check_system_state_and_load_data(self):
        """التحقق من حالة النظام قبل تحميل البيانات في وضع التعديل"""
        try:
            # التحقق من وجود عمليات تعبئة بيانات نشطة
            import threading
            active_threads = threading.active_count()

            if active_threads > 3:  # عتبة آمنة للخيوط النشطة
                reply = QMessageBox.question(
                    self, "تحذير",
                    "يبدو أن هناك عمليات أخرى نشطة في النظام.\n"
                    "هل تريد المتابعة؟ (قد يؤثر على الأداء)",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply != QMessageBox.Yes:
                    self.reject()
                    return

            # تأخير قصير للسماح للعمليات الأخرى بالانتهاء
            from PySide6.QtCore import QTimer
            QTimer.singleShot(500, self.load_shipment_data)

        except Exception as e:
            print(f"تحذير في فحص حالة النظام: {e}")
            # في حالة الخطأ، تحميل البيانات مباشرة
            self.load_shipment_data()

    def load_from_purchase_order(self, purchase_order_id):
        """تحميل بيانات من طلب شراء"""
        try:
            session = self.db_manager.get_session()

            # تحميل طلب الشراء
            purchase_order = session.query(PurchaseOrder).filter(
                PurchaseOrder.id == purchase_order_id
            ).first()

            if not purchase_order:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على طلب الشراء")
                return

            # تعبئة بيانات المورد
            if purchase_order.supplier:
                self.selected_supplier_id = purchase_order.supplier_id
                supplier_text = f"{purchase_order.supplier.name} ({purchase_order.supplier.code})"
                self.supplier_edit.setText(supplier_text)
                self.supplier_edit.setProperty("supplier_id", purchase_order.supplier_id)

            # تعبئة العملة
            if purchase_order.currency_id:
                currency_index = -1
                for i in range(self.currency_combo.count()):
                    if self.currency_combo.itemData(i) == purchase_order.currency_id:
                        currency_index = i
                        break
                if currency_index >= 0:
                    self.currency_combo.setCurrentIndex(currency_index)

            # تعبئة سعر الصرف
            if hasattr(self, 'exchange_rate_edit') and purchase_order.exchange_rate:
                self.exchange_rate_edit.setText(str(purchase_order.exchange_rate))

            # تعبئة الملاحظات
            if purchase_order.notes:
                self.notes_edit.setPlainText(f"من طلب الشراء {purchase_order.order_number}:\n{purchase_order.notes}")

            # تحميل أصناف طلب الشراء
            order_items = session.query(PurchaseOrderItem).filter(
                PurchaseOrderItem.purchase_order_id == purchase_order_id
            ).all()

            # مسح الجدول الحالي
            self.items_table.setRowCount(0)

            # إضافة الأصناف
            for order_item in order_items:
                if order_item.item:
                    row = self.items_table.rowCount()
                    self.items_table.insertRow(row)

                    # كود الصنف
                    self.items_table.setItem(row, 0, QTableWidgetItem(order_item.item.code))

                    # اسم الصنف
                    self.items_table.setItem(row, 1, QTableWidgetItem(order_item.item.name))

                    # الكمية - استخدام الكمية المتبقية
                    remaining_qty = order_item.quantity - order_item.delivered_quantity
                    self.items_table.setItem(row, 2, QTableWidgetItem(f"{remaining_qty:.2f}"))

                    # سعر الوحدة
                    self.items_table.setItem(row, 3, QTableWidgetItem(f"{order_item.unit_price:.2f}"))

                    # تاريخ الإنتاج - من طلب الشراء إذا متوفر
                    production_date = ""
                    if hasattr(order_item, 'production_date') and order_item.production_date:
                        production_date = order_item.production_date.strftime("%Y-%m-%d")
                    self.items_table.setItem(row, 4, QTableWidgetItem(production_date))

                    # تاريخ الانتهاء - من طلب الشراء إذا متوفر
                    expiry_date = ""
                    if hasattr(order_item, 'expiry_date') and order_item.expiry_date:
                        expiry_date = order_item.expiry_date.strftime("%Y-%m-%d")
                    self.items_table.setItem(row, 5, QTableWidgetItem(expiry_date))

                    # الإجمالي
                    total = remaining_qty * order_item.unit_price
                    self.items_table.setItem(row, 6, QTableWidgetItem(f"{total:.2f}"))

                    # الوزن - من بيانات الصنف
                    weight = order_item.item.weight if order_item.item.weight else 0.0
                    self.items_table.setItem(row, 7, QTableWidgetItem(f"{weight:.2f}"))

                    # ملاحظات
                    notes = f"من طلب الشراء {purchase_order.order_number}"
                    if order_item.notes:
                        notes += f" - {order_item.notes}"
                    self.items_table.setItem(row, 8, QTableWidgetItem(notes))

                    # حفظ معرف الصنف ومعرف صنف طلب الشراء
                    self.items_table.item(row, 0).setData(Qt.UserRole, order_item.item_id)
                    self.items_table.item(row, 0).setData(Qt.UserRole + 1, order_item.id)  # معرف صنف طلب الشراء

            # تحديث الإجماليات
            self.calculate_totals()

            session.close()

            QMessageBox.information(
                self, "نجح",
                f"تم تحميل بيانات طلب الشراء {purchase_order.order_number} بنجاح"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات طلب الشراء: {str(e)}")
            if 'session' in locals():
                session.close()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        if self.is_edit_mode:
            self.setWindowTitle("✏️ تعديل الشحنة - مع أزرار التحكم")
        else:
            self.setWindowTitle("🚢 شحنة جديدة - مع أزرار التحكم")
        self.setModal(True)
        self.resize(1440, 990)  # الحجم المطلوب
        self.setMinimumSize(1000, 500)  # حد أدنى للحجم

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)  # تقليل المسافات
        main_layout.setContentsMargins(15, 15, 15, 15)  # تقليل الهوامش
        
        # عنوان النافذة
        self.create_title_header(main_layout)
        
        # شريط أزرار التحكم
        self.create_control_toolbar(main_layout)
        
        # التبويبات
        self.create_tabs(main_layout)
        
        # أزرار الحفظ والإلغاء
        self.create_dialog_buttons(main_layout)
        
    def create_title_header(self, main_layout):
        """إنشاء عنوان النافذة"""
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 8px;
                padding: 8px;
            }
        """)
        
        title_layout = QHBoxLayout(title_frame)
        
        title_label = QLabel("🚢 نظام إدارة الشحنات - شحنة جديدة")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 5px;
            }
        """)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        main_layout.addWidget(title_frame)
        
    def create_control_toolbar(self, main_layout):
        """إنشاء شريط أزرار التحكم"""
        # إطار الأزرار
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel | QFrame.Raised)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 2px solid #3498db;
                border-radius: 8px;
                padding: 8px;
                margin: 3px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setSpacing(15)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)

        # زر إضافة
        self.new_button = QPushButton("🆕 إضافة")
        self.new_button.setFixedSize(120, 40)
        self.new_button.setStyleSheet(self.get_button_style("#3498db", "#2980b9"))

        # زر حفظ
        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setFixedSize(120, 40)
        self.save_button.setStyleSheet(self.get_button_style("#27ae60", "#229954"))

        # زر تعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setFixedSize(120, 40)
        self.edit_button.setStyleSheet(self.get_button_style("#f39c12", "#e67e22"))
        self.edit_button.setEnabled(False)

        # زر حذف
        self.delete_button = QPushButton("🗑️ حذف")
        self.delete_button.setFixedSize(120, 40)
        self.delete_button.setStyleSheet(self.get_button_style("#dc3545", "#c82333"))
        self.delete_button.setEnabled(False)  # يتم تفعيله في وضع التعديل فقط

        # زر استيراد من الإكسيل
        self.import_excel_button = QPushButton("📊 استيراد إكسيل")
        self.import_excel_button.setFixedSize(140, 40)
        self.import_excel_button.setStyleSheet(self.get_button_style("#17a2b8", "#138496"))
        self.import_excel_button.setToolTip("استيراد بيانات الشحنة من ملف إكسيل")

        # زر خروج
        self.exit_button = QPushButton("🚪 خروج")
        self.exit_button.setFixedSize(120, 40)
        self.exit_button.setStyleSheet(self.get_button_style("#e74c3c", "#c0392b"))

        # ترتيب الأزرار
        toolbar_layout.addWidget(self.new_button)
        toolbar_layout.addWidget(self.save_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addWidget(self.delete_button)
        toolbar_layout.addWidget(self.import_excel_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.exit_button)
        
        main_layout.addWidget(toolbar_frame)
        
    def get_button_style(self, color1, color2):
        """الحصول على نمط الزر"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                color: white;
                font-weight: bold;
                font-size: 12px;
                border: 2px solid {color2};
                border-radius: 8px;
            }}
            QPushButton:hover {{
                background: {color2};
                border: 2px solid {color1};
            }}
            QPushButton:pressed {{
                background: {color1};
            }}
            QPushButton:disabled {{
                background: #bdc3c7;
                color: #7f8c8d;
                border: 2px solid #95a5a6;
            }}
        """
        
    def create_tabs(self, main_layout):
        """إنشاء التبويبات"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                padding: 5px;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 8px 15px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
        """)
        
        # إنشاء التبويبات
        self.create_basic_tab()
        self.create_items_tab()
        self.create_financial_tab()
        self.create_shipping_tab()
        self.create_containers_tab()
        self.create_documents_tab()
        
        main_layout.addWidget(self.tab_widget)
        
    def create_basic_tab(self):
        """إنشاء التبويب الأساسي"""
        basic_tab = QWidget()
        self.tab_widget.addTab(basic_tab, "📋 البيانات الأساسية")
        
        layout = QVBoxLayout(basic_tab)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #2c3e50;
                background-color: white;
            }
        """)
        
        basic_layout = QGridLayout(basic_group)
        basic_layout.setSpacing(8)

        # الصف الأول: التاريخ + رقم الشحنة + المورد
        basic_layout.addWidget(QLabel("التاريخ:"), 0, 0)
        self.shipment_date_edit = FlexibleDateEdit()
        self.shipment_date_edit.setDate(QDate.currentDate())
        self.shipment_date_edit.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.shipment_date_edit, 0, 1)

        basic_layout.addWidget(QLabel("رقم الشحنة:"), 0, 2)

        # تخطيط رقم الشحنة مع زر التوليد
        shipment_number_layout = QHBoxLayout()
        self.shipment_number_edit = QLineEdit()
        self.shipment_number_edit.setReadOnly(False)  # السماح بالتعديل
        unique_number = self.generate_unique_shipment_number()
        self.shipment_number_edit.setText(unique_number)
        self.shipment_number_edit.setStyleSheet(self.get_input_style())
        shipment_number_layout.addWidget(self.shipment_number_edit)

        # زر توليد رقم جديد
        generate_number_btn = QPushButton("🔄")
        generate_number_btn.setToolTip("توليد رقم شحنة جديد")
        generate_number_btn.setFixedSize(30, 30)
        generate_number_btn.clicked.connect(self.generate_new_shipment_number)
        generate_number_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 15px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        shipment_number_layout.addWidget(generate_number_btn)

        basic_layout.addLayout(shipment_number_layout, 0, 3)

        basic_layout.addWidget(QLabel("المورد:"), 0, 4)
        supplier_layout = QHBoxLayout()
        self.supplier_edit = QLineEdit()
        self.supplier_edit.setPlaceholderText("اكتب اسم المورد أو اضغط F9 للبحث")
        self.supplier_edit.setStyleSheet(self.get_input_style())
        self.supplier_search_button = QPushButton("F9")
        self.supplier_search_button.setMaximumWidth(35)
        self.supplier_search_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 6px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        supplier_layout.addWidget(self.supplier_edit)
        supplier_layout.addWidget(self.supplier_search_button)
        supplier_widget = QWidget()
        supplier_widget.setLayout(supplier_layout)
        basic_layout.addWidget(supplier_widget, 0, 5)

        # الصف الثاني: فاتورة المورد + حالة الشحنة
        basic_layout.addWidget(QLabel("فاتورة المورد:"), 1, 0)
        self.supplier_invoice_edit = QLineEdit()
        self.supplier_invoice_edit.setPlaceholderText("رقم فاتورة المورد...")
        self.supplier_invoice_edit.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.supplier_invoice_edit, 1, 1)

        basic_layout.addWidget(QLabel("حالة الشحنة:"), 1, 2)
        self.shipment_status_combo = QComboBox()
        self.shipment_status_combo.addItems([
            "تحت الطلب", "مؤكدة", "تم الشحن", "في الطريق",
            "وصلت الميناء", "في الجمارك", "تم التسليم", "ملغية", "متاخرة"
        ])
        self.shipment_status_combo.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.shipment_status_combo, 1, 3)

        # الصف الثالث: حالة الإفراج
        basic_layout.addWidget(QLabel("حالة الإفراج:"), 2, 0)
        self.clearance_status_combo = QComboBox()
        self.clearance_status_combo.addItems(["بدون الافراج", "مع الافراج"])
        self.clearance_status_combo.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.clearance_status_combo, 2, 1)

        # الصف الرابع: بوليصة الشحن + ملاحظات
        basic_layout.addWidget(QLabel("بوليصة الشحن:"), 3, 0)
        self.bill_of_lading_edit = QLineEdit()
        self.bill_of_lading_edit.setPlaceholderText("رقم بوليصة الشحن...")
        self.bill_of_lading_edit.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.bill_of_lading_edit, 3, 1)

        basic_layout.addWidget(QLabel("ملاحظات:"), 3, 2)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(50)
        self.notes_edit.setPlaceholderText("أدخل ملاحظات...")
        self.notes_edit.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.notes_edit, 3, 3)

        layout.addWidget(basic_group)
        layout.addStretch()
        
    def get_input_style(self, readonly=False):
        """الحصول على نمط الحقول"""
        bg_color = "#f8f9fa" if readonly else "white"
        return f"""
            QLineEdit, QComboBox, QTextEdit, QDateEdit {{
                background-color: {bg_color};
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                min-height: 20px;
            }}
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus, QDateEdit:focus {{
                border: 2px solid #3498db;
                background-color: #f8f9fa;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 30px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border: 2px solid #3498db;
                width: 10px;
                height: 10px;
            }}
        """
        
    def create_items_tab(self):
        """إنشاء تبويب الأصناف"""
        items_tab = QWidget()
        self.tab_widget.addTab(items_tab, "📦 الأصناف")

        layout = QVBoxLayout(items_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # أزرار إدارة الأصناف
        buttons_layout = QHBoxLayout()

        self.add_item_button = QPushButton("إضافة صنف (F9)")
        self.add_item_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        self.remove_item_button = QPushButton("حذف صنف")
        self.remove_item_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        self.remove_item_button.setEnabled(False)

        self.edit_item_button = QPushButton("تعديل صنف")
        self.edit_item_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e68900;
            }
        """)
        self.edit_item_button.setEnabled(False)

        buttons_layout.addWidget(self.add_item_button)
        buttons_layout.addWidget(self.remove_item_button)
        buttons_layout.addWidget(self.edit_item_button)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(9)
        self.items_table.setHorizontalHeaderLabels([
            "كود الصنف", "اسم الصنف", "الكمية", "سعر الوحدة",
            "تاريخ الإنتاج", "تاريخ الانتهاء", "السعر الإجمالي", "الوزن", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # كود الصنف
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الصنف
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الكمية
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # سعر الوحدة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # تاريخ الإنتاج
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # تاريخ الانتهاء
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # السعر الإجمالي
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # الوزن
        header.setSectionResizeMode(8, QHeaderView.Stretch)  # ملاحظات

        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.items_table)

        # إجمالي الأصناف
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.total_items_label = QLabel("إجمالي الأصناف: 0")
        self.total_items_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)

        self.total_value_label = QLabel("إجمالي القيمة: 0.00")
        self.total_value_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 16px;
                color: #27ae60;
                padding: 10px;
                background-color: #d5f4e6;
                border-radius: 5px;
            }
        """)

        totals_layout.addWidget(self.total_items_label)
        totals_layout.addWidget(self.total_value_label)

        layout.addLayout(totals_layout)
        
    def create_financial_tab(self):
        """إنشاء تبويب المالية"""
        financial_tab = QWidget()
        self.tab_widget.addTab(financial_tab, "💰 البيانات المالية")

        layout = QVBoxLayout(financial_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # مجموعة البيانات المالية
        financial_group = QGroupBox("البيانات المالية")
        financial_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #f39c12;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #f39c12;
                background-color: white;
            }
        """)
        financial_layout = QGridLayout(financial_group)
        financial_layout.setSpacing(12)
        financial_layout.setHorizontalSpacing(8)
        financial_layout.setVerticalSpacing(12)

        # ضبط عرض الأعمدة لجعل التسميات أقرب للحقول (مع إضافة عمودين جديدين)
        financial_layout.setColumnMinimumWidth(0, 120)  # عمود التسميات الأول
        financial_layout.setColumnMinimumWidth(1, 150)  # عمود الحقول الأول
        financial_layout.setColumnMinimumWidth(2, 120)  # عمود التسميات الثاني
        financial_layout.setColumnMinimumWidth(3, 150)  # عمود الحقول الثاني
        financial_layout.setColumnMinimumWidth(4, 140)  # عمود التسميات الثالث
        financial_layout.setColumnMinimumWidth(5, 150)  # عمود الحقول الثالث
        financial_layout.setColumnMinimumWidth(6, 160)  # عمود التسميات الرابع (قيمة البضاعة بالدولار)
        financial_layout.setColumnMinimumWidth(7, 150)  # عمود الحقول الرابع
        financial_layout.setColumnStretch(0, 0)
        financial_layout.setColumnStretch(1, 1)
        financial_layout.setColumnStretch(2, 0)
        financial_layout.setColumnStretch(3, 1)
        financial_layout.setColumnStretch(4, 0)
        financial_layout.setColumnStretch(5, 1)
        financial_layout.setColumnStretch(6, 0)
        financial_layout.setColumnStretch(7, 1)

        # الصف الأول: العملة + سعر الصرف + قيمة البضاعة
        currency_label = QLabel("العملة:")
        currency_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        currency_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(currency_label, 0, 0)
        self.currency_combo = QComboBox()
        self.load_currencies()  # تحميل العملات من قاعدة البيانات
        self.currency_combo.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.currency_combo, 0, 1)

        exchange_rate_label = QLabel("سعر صرف الدولار:")
        exchange_rate_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        exchange_rate_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(exchange_rate_label, 0, 2)
        self.exchange_rate_edit = QLineEdit()
        self.exchange_rate_edit.setPlaceholderText("1.0000")
        self.exchange_rate_edit.setText("1.0000")
        self.exchange_rate_edit.setStyleSheet(self.get_input_style())
        # ربط تغيير سعر الصرف بحساب قيمة البضاعة بالدولار
        self.exchange_rate_edit.textChanged.connect(self.calculate_goods_value_usd)
        financial_layout.addWidget(self.exchange_rate_edit, 0, 3)

        goods_value_label = QLabel("قيمة البضاعة:")
        goods_value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        goods_value_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(goods_value_label, 0, 4)
        self.goods_value_edit = QLineEdit()
        self.goods_value_edit.setPlaceholderText("0.00")
        self.goods_value_edit.setText("0.00")
        self.goods_value_edit.setReadOnly(True)
        self.goods_value_edit.setStyleSheet(self.get_input_style(readonly=True))
        # ربط تغيير قيمة البضاعة بحساب قيمة البضاعة بالدولار
        self.goods_value_edit.textChanged.connect(self.calculate_goods_value_usd)
        financial_layout.addWidget(self.goods_value_edit, 0, 5)

        # إضافة حقل قيمة البضاعة بالدولار في نفس الصف
        goods_value_usd_label = QLabel("قيمة البضاعة بالدولار:")
        goods_value_usd_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        goods_value_usd_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(goods_value_usd_label, 0, 6)
        self.goods_value_usd_edit = QLineEdit()
        self.goods_value_usd_edit.setPlaceholderText("0.00")
        self.goods_value_usd_edit.setText("0.00")
        self.goods_value_usd_edit.setReadOnly(True)
        self.goods_value_usd_edit.setStyleSheet(self.get_input_style(readonly=True))
        financial_layout.addWidget(self.goods_value_usd_edit, 0, 7)



        layout.addWidget(financial_group)

        # مجموعة أجور الشحن
        shipping_fees_group = QGroupBox("أجور الشحن")
        shipping_fees_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #e74c3c;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #e74c3c;
                background-color: white;
            }
        """)
        shipping_fees_layout = QGridLayout(shipping_fees_group)
        shipping_fees_layout.setSpacing(12)
        shipping_fees_layout.setHorizontalSpacing(8)
        shipping_fees_layout.setVerticalSpacing(12)

        # الصف الأول: خيارات القيد (Radio Buttons)
        # إنشاء مجموعة أزرار للتأكد من اختيار واحد فقط
        self.shipping_fees_button_group = QButtonGroup()

        self.supplier_entry_radio = QRadioButton("القيد لحساب المورد")
        self.supplier_entry_radio.setStyleSheet("""
            QRadioButton {
                font-weight: bold;
                color: #2c3e50;
                spacing: 10px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
            QRadioButton::indicator:unchecked {
                border: 2px solid #bdc3c7;
                background-color: white;
                border-radius: 9px;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #e74c3c;
                background-color: #e74c3c;
                border-radius: 9px;
            }
        """)
        self.supplier_entry_radio.toggled.connect(self.toggle_supplier_shipping_fields)
        self.shipping_fees_button_group.addButton(self.supplier_entry_radio)
        shipping_fees_layout.addWidget(self.supplier_entry_radio, 0, 0, 1, 2)

        self.shipping_company_entry_radio = QRadioButton("القيد لحساب شركة الشحن")
        self.shipping_company_entry_radio.setStyleSheet("""
            QRadioButton {
                font-weight: bold;
                color: #2c3e50;
                spacing: 10px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
            QRadioButton::indicator:unchecked {
                border: 2px solid #bdc3c7;
                background-color: white;
                border-radius: 9px;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #e74c3c;
                background-color: #e74c3c;
                border-radius: 9px;
            }
        """)
        self.shipping_company_entry_radio.toggled.connect(self.toggle_shipping_company_fields)
        self.shipping_fees_button_group.addButton(self.shipping_company_entry_radio)
        shipping_fees_layout.addWidget(self.shipping_company_entry_radio, 0, 2, 1, 2)

        # الصف الثاني: حقول المورد (مخفية افتراضياً)
        self.supplier_shipping_label1 = QLabel("أجور الشحن:")
        self.supplier_shipping_label1.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.supplier_shipping_label1.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        self.supplier_shipping_label1.setVisible(False)
        shipping_fees_layout.addWidget(self.supplier_shipping_label1, 1, 0)

        self.supplier_shipping_fees_edit = QLineEdit()
        self.supplier_shipping_fees_edit.setPlaceholderText("0.00")
        self.supplier_shipping_fees_edit.setText("0.00")
        self.supplier_shipping_fees_edit.setStyleSheet(self.get_input_style())
        self.supplier_shipping_fees_edit.setVisible(False)
        shipping_fees_layout.addWidget(self.supplier_shipping_fees_edit, 1, 1)

        self.supplier_currency_label = QLabel("العملة:")
        self.supplier_currency_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.supplier_currency_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        self.supplier_currency_label.setVisible(False)
        shipping_fees_layout.addWidget(self.supplier_currency_label, 1, 2)

        self.supplier_currency_combo = QComboBox()
        self.load_currencies_for_supplier()
        self.supplier_currency_combo.setStyleSheet(self.get_input_style())
        self.supplier_currency_combo.setVisible(False)
        shipping_fees_layout.addWidget(self.supplier_currency_combo, 1, 3)

        self.supplier_local_fees_label = QLabel("أجور الشحن بعملة المورد:")
        self.supplier_local_fees_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.supplier_local_fees_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        self.supplier_local_fees_label.setVisible(False)
        shipping_fees_layout.addWidget(self.supplier_local_fees_label, 1, 4)

        self.supplier_local_fees_edit = QLineEdit()
        self.supplier_local_fees_edit.setPlaceholderText("0.00")
        self.supplier_local_fees_edit.setText("0.00")
        self.supplier_local_fees_edit.setReadOnly(True)
        self.supplier_local_fees_edit.setStyleSheet(self.get_input_style(readonly=True))
        self.supplier_local_fees_edit.setVisible(False)
        shipping_fees_layout.addWidget(self.supplier_local_fees_edit, 1, 5)

        # الصف الثالث: حقول شركة الشحن (مخفية افتراضياً)
        self.shipping_company_label = QLabel("شركة الشحن:")
        self.shipping_company_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.shipping_company_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        self.shipping_company_label.setVisible(False)
        shipping_fees_layout.addWidget(self.shipping_company_label, 2, 0)

        self.shipping_company_combo = QComboBox()
        self.load_shipping_companies()
        self.shipping_company_combo.setStyleSheet(self.get_input_style())
        self.shipping_company_combo.setVisible(False)
        shipping_fees_layout.addWidget(self.shipping_company_combo, 2, 1)

        self.shipping_company_fees_label = QLabel("أجور الشحن:")
        self.shipping_company_fees_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.shipping_company_fees_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        self.shipping_company_fees_label.setVisible(False)
        shipping_fees_layout.addWidget(self.shipping_company_fees_label, 2, 2)

        self.shipping_company_fees_edit = QLineEdit()
        self.shipping_company_fees_edit.setPlaceholderText("0.00")
        self.shipping_company_fees_edit.setText("0.00")
        self.shipping_company_fees_edit.setStyleSheet(self.get_input_style())
        self.shipping_company_fees_edit.setVisible(False)
        shipping_fees_layout.addWidget(self.shipping_company_fees_edit, 2, 3)

        layout.addWidget(shipping_fees_group)

        # مجموعة تكاليف مرتبطة بالشحنة
        shipment_costs_group = QGroupBox("تكاليف مرتبطة بالشحنة")
        shipment_costs_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #9b59b6;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #9b59b6;
                background-color: white;
            }
        """)
        shipment_costs_layout = QGridLayout(shipment_costs_group)
        shipment_costs_layout.setSpacing(12)
        shipment_costs_layout.setHorizontalSpacing(8)
        shipment_costs_layout.setVerticalSpacing(12)

        # ضبط عرض الأعمدة
        shipment_costs_layout.setColumnMinimumWidth(0, 120)  # عمود التسميات الأول
        shipment_costs_layout.setColumnMinimumWidth(1, 150)  # عمود الحقول الأول
        shipment_costs_layout.setColumnMinimumWidth(2, 120)  # عمود التسميات الثاني
        shipment_costs_layout.setColumnMinimumWidth(3, 150)  # عمود الحقول الثاني
        shipment_costs_layout.setColumnMinimumWidth(4, 140)  # عمود التسميات الثالث
        shipment_costs_layout.setColumnMinimumWidth(5, 150)  # عمود الحقول الثالث
        shipment_costs_layout.setColumnStretch(0, 0)
        shipment_costs_layout.setColumnStretch(1, 1)
        shipment_costs_layout.setColumnStretch(2, 0)
        shipment_costs_layout.setColumnStretch(3, 1)
        shipment_costs_layout.setColumnStretch(4, 0)
        shipment_costs_layout.setColumnStretch(5, 1)

        # الصف الأول: تكلفة الشحن + التأمين + رسوم الجمارك
        shipping_cost_label = QLabel("تكلفة الشحن:")
        shipping_cost_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        shipping_cost_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        shipment_costs_layout.addWidget(shipping_cost_label, 0, 0)
        self.shipping_cost_edit = QLineEdit()
        self.shipping_cost_edit.setPlaceholderText("0.00")
        self.shipping_cost_edit.setText("0.00")
        self.shipping_cost_edit.setStyleSheet(self.get_input_style())
        shipment_costs_layout.addWidget(self.shipping_cost_edit, 0, 1)

        insurance_cost_label = QLabel("تكلفة التأمين:")
        insurance_cost_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        insurance_cost_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        shipment_costs_layout.addWidget(insurance_cost_label, 0, 2)
        self.insurance_cost_edit = QLineEdit()
        self.insurance_cost_edit.setPlaceholderText("0.00")
        self.insurance_cost_edit.setText("0.00")
        self.insurance_cost_edit.setStyleSheet(self.get_input_style())
        shipment_costs_layout.addWidget(self.insurance_cost_edit, 0, 3)

        customs_fees_label = QLabel("رسوم الجمارك:")
        customs_fees_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        customs_fees_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        shipment_costs_layout.addWidget(customs_fees_label, 0, 4)
        self.customs_fees_edit = QLineEdit()
        self.customs_fees_edit.setPlaceholderText("0.00")
        self.customs_fees_edit.setText("0.00")
        self.customs_fees_edit.setStyleSheet(self.get_input_style())
        shipment_costs_layout.addWidget(self.customs_fees_edit, 0, 5)

        # الصف الثاني: رسوم أخرى + إجمالي التكاليف + إجمالي بالعملة المحلية
        other_fees_label = QLabel("رسوم أخرى:")
        other_fees_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        other_fees_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        shipment_costs_layout.addWidget(other_fees_label, 1, 0)
        self.other_fees_edit = QLineEdit()
        self.other_fees_edit.setPlaceholderText("0.00")
        self.other_fees_edit.setText("0.00")
        self.other_fees_edit.setStyleSheet(self.get_input_style())
        shipment_costs_layout.addWidget(self.other_fees_edit, 1, 1)

        total_costs_label = QLabel("إجمالي التكاليف:")
        total_costs_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        total_costs_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        shipment_costs_layout.addWidget(total_costs_label, 1, 2)
        self.total_costs_edit = QLineEdit()
        self.total_costs_edit.setPlaceholderText("0.00")
        self.total_costs_edit.setText("0.00")
        self.total_costs_edit.setReadOnly(True)
        self.total_costs_edit.setStyleSheet(self.get_input_style(readonly=True))
        shipment_costs_layout.addWidget(self.total_costs_edit, 1, 3)

        total_local_label = QLabel("الإجمالي بالعملة المحلية:")
        total_local_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        total_local_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        shipment_costs_layout.addWidget(total_local_label, 1, 4)
        self.total_local_currency_edit = QLineEdit()
        self.total_local_currency_edit.setPlaceholderText("0.00")
        self.total_local_currency_edit.setText("0.00")
        self.total_local_currency_edit.setReadOnly(True)
        self.total_local_currency_edit.setStyleSheet(self.get_input_style(readonly=True))
        shipment_costs_layout.addWidget(self.total_local_currency_edit, 1, 5)

        layout.addWidget(shipment_costs_group)

        # مجموعة الدفع
        payment_group = QGroupBox("معلومات الدفع")
        payment_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #27ae60;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #27ae60;
                background-color: white;
            }
        """)
        payment_layout = QGridLayout(payment_group)
        payment_layout.setSpacing(12)
        payment_layout.setHorizontalSpacing(8)
        payment_layout.setVerticalSpacing(12)

        # ضبط عرض الأعمدة لجعل التسميات أقرب للحقول
        payment_layout.setColumnMinimumWidth(0, 120)  # عمود التسميات الأول
        payment_layout.setColumnMinimumWidth(1, 150)  # عمود الحقول الأول
        payment_layout.setColumnMinimumWidth(2, 120)  # عمود التسميات الثاني
        payment_layout.setColumnMinimumWidth(3, 150)  # عمود الحقول الثاني
        payment_layout.setColumnMinimumWidth(4, 120)  # عمود التسميات الثالث
        payment_layout.setColumnMinimumWidth(5, 150)  # عمود الحقول الثالث
        payment_layout.setColumnStretch(0, 0)
        payment_layout.setColumnStretch(1, 1)
        payment_layout.setColumnStretch(2, 0)
        payment_layout.setColumnStretch(3, 1)
        payment_layout.setColumnStretch(4, 0)
        payment_layout.setColumnStretch(5, 1)

        # الصف الأول: حالة الدفع + المبلغ المدفوع + المبلغ المتبقي
        payment_status_label = QLabel("حالة الدفع:")
        payment_status_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        payment_status_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        payment_layout.addWidget(payment_status_label, 0, 0)
        self.payment_status_combo = QComboBox()
        self.payment_status_combo.addItems(["غير مدفوع", "مدفوع جزئياً", "مدفوع بالكامل", "مسترد"])
        self.payment_status_combo.setStyleSheet(self.get_input_style())
        payment_layout.addWidget(self.payment_status_combo, 0, 1)

        paid_amount_label = QLabel("المبلغ المدفوع:")
        paid_amount_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        paid_amount_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        payment_layout.addWidget(paid_amount_label, 0, 2)
        self.paid_amount_edit = QLineEdit()
        self.paid_amount_edit.setPlaceholderText("0.00")
        self.paid_amount_edit.setText("0.00")
        self.paid_amount_edit.setStyleSheet(self.get_input_style())
        payment_layout.addWidget(self.paid_amount_edit, 0, 3)

        remaining_amount_label = QLabel("المبلغ المتبقي:")
        remaining_amount_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        remaining_amount_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        payment_layout.addWidget(remaining_amount_label, 0, 4)
        self.remaining_amount_edit = QLineEdit()
        self.remaining_amount_edit.setPlaceholderText("0.00")
        self.remaining_amount_edit.setText("0.00")
        self.remaining_amount_edit.setReadOnly(True)
        self.remaining_amount_edit.setStyleSheet(self.get_input_style(readonly=True))
        payment_layout.addWidget(self.remaining_amount_edit, 0, 5)

        # الصف الثاني: تاريخ الدفع + طريقة الدفع
        payment_date_label = QLabel("تاريخ الدفع:")
        payment_date_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        payment_date_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        payment_layout.addWidget(payment_date_label, 1, 0)
        self.payment_date_edit = FlexibleDateEdit()
        self.payment_date_edit.setDate(QDate.currentDate())
        self.payment_date_edit.setStyleSheet(self.get_input_style())
        payment_layout.addWidget(self.payment_date_edit, 1, 1)

        payment_method_label = QLabel("طريقة الدفع:")
        payment_method_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        payment_method_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        payment_layout.addWidget(payment_method_label, 1, 2)
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقداً", "شيك", "تحويل بنكي", "بطاقة ائتمان", "أخرى"])
        self.payment_method_combo.setStyleSheet(self.get_input_style())
        payment_layout.addWidget(self.payment_method_combo, 1, 3)

        layout.addWidget(payment_group)
        layout.addStretch()

    def create_shipping_tab(self):
        """إنشاء تبويب الشحن"""
        shipping_tab = QWidget()
        self.tab_widget.addTab(shipping_tab, "🚢 الشحن")

        layout = QVBoxLayout(shipping_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # مجموعة بيانات الشحن
        shipping_group = QGroupBox("بيانات الشحن")
        shipping_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #3498db;
                background-color: white;
            }
        """)
        shipping_layout = QGridLayout(shipping_group)
        shipping_layout.setSpacing(8)

        # الصف الأول: شركة الشحن الذكية + نوع الشحن + طريقة الشحن
        shipping_layout.addWidget(QLabel("شركة الشحن:"), 0, 0)
        self.smart_shipping_company_widget = SmartShippingCompanyWidget()
        self.smart_shipping_company_widget.company_selected.connect(self.on_shipping_company_selected)
        self.smart_shipping_company_widget.company_validated.connect(self.on_shipping_company_validated)
        shipping_layout.addWidget(self.smart_shipping_company_widget, 0, 1)

        shipping_layout.addWidget(QLabel("نوع الشحن:"), 0, 2)
        self.shipping_type_combo = QComboBox()
        self.shipping_type_combo.addItems(["بحري", "جوي", "بري", "مختلط"])
        self.shipping_type_combo.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.shipping_type_combo, 0, 3)

        shipping_layout.addWidget(QLabel("طريقة الشحن:"), 0, 4)
        self.shipping_method_combo = QComboBox()
        self.shipping_method_combo.addItems(["FCL", "LCL", "Air Freight", "Express"])
        self.shipping_method_combo.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.shipping_method_combo, 0, 5)

        # الصف الثاني: شروط التسليم + ميناء الشحن + ميناء الوصول
        shipping_layout.addWidget(QLabel("شروط التسليم:"), 1, 0)
        self.incoterms_combo = QComboBox()
        self.incoterms_combo.addItems([
            "EXW", "FCA", "CPT", "CIP", "DAP", "DPU", "DDP",
            "FAS", "FOB", "CFR", "CIF"
        ])
        self.incoterms_combo.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.incoterms_combo, 1, 1)

        shipping_layout.addWidget(QLabel("ميناء الشحن:"), 1, 2)
        self.port_of_loading_edit = QLineEdit()
        self.port_of_loading_edit.setPlaceholderText("ميناء الشحن...")
        self.port_of_loading_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.port_of_loading_edit, 1, 3)

        shipping_layout.addWidget(QLabel("ميناء الوصول:"), 1, 4)
        self.port_of_discharge_edit = QLineEdit()
        self.port_of_discharge_edit.setPlaceholderText("ميناء الوصول...")
        self.port_of_discharge_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.port_of_discharge_edit, 1, 5)

        # الصف الثالث: الوجهة النهائية + اسم السفينة + رقم الرحلة
        shipping_layout.addWidget(QLabel("الوجهة النهائية:"), 2, 0)
        self.final_destination_edit = QLineEdit()
        self.final_destination_edit.setPlaceholderText("الوجهة النهائية...")
        self.final_destination_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.final_destination_edit, 2, 1)

        shipping_layout.addWidget(QLabel("اسم السفينة:"), 2, 2)
        self.vessel_name_edit = QLineEdit()
        self.vessel_name_edit.setPlaceholderText("اسم السفينة...")
        self.vessel_name_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.vessel_name_edit, 2, 3)

        shipping_layout.addWidget(QLabel("رقم الرحلة:"), 2, 4)
        self.voyage_number_edit = QLineEdit()
        self.voyage_number_edit.setPlaceholderText("رقم الرحلة...")
        self.voyage_number_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.voyage_number_edit, 2, 5)

        # الصف الرابع: رقم DHL + رقم التتبع
        shipping_layout.addWidget(QLabel("رقم DHL:"), 3, 0)
        self.dhl_number_edit = QLineEdit()
        self.dhl_number_edit.setPlaceholderText("رقم DHL...")
        self.dhl_number_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.dhl_number_edit, 3, 1)

        shipping_layout.addWidget(QLabel("رقم التتبع:"), 3, 2)
        self.tracking_number_edit = QLineEdit()
        self.tracking_number_edit.setPlaceholderText("رقم التتبع...")
        self.tracking_number_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.tracking_number_edit, 3, 3)

        # ملاحظة: تم حذف حقلي "رقم بوليصة الشحن" و "رقم الحاوية الرئيسي"
        # لأنهما موجودان في تبويبات أخرى (تبويب البيانات الأساسية وتبويب الحاويات)

        layout.addWidget(shipping_group)

        # مجموعة التواريخ
        dates_group = QGroupBox("التواريخ")
        dates_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #27ae60;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #27ae60;
                background-color: white;
            }
        """)
        dates_layout = QGridLayout(dates_group)
        dates_layout.setSpacing(8)

        # الصف الأول: تاريخ المغادرة المتوقع + الفعلي
        dates_layout.addWidget(QLabel("تاريخ المغادرة المتوقع:"), 0, 0)
        self.estimated_departure_date_edit = FlexibleDateEdit()
        # إلغاء الإدخال التلقائي للتاريخ - يبقى فارغاً
        self.estimated_departure_date_edit.setStyleSheet(self.get_input_style())
        dates_layout.addWidget(self.estimated_departure_date_edit, 0, 1)

        dates_layout.addWidget(QLabel("تاريخ المغادرة الفعلي:"), 0, 2)
        self.actual_departure_date_edit = FlexibleDateEdit()
        # إلغاء الإدخال التلقائي للتاريخ - يبقى فارغاً
        self.actual_departure_date_edit.setStyleSheet(self.get_input_style())
        dates_layout.addWidget(self.actual_departure_date_edit, 0, 3)

        # الصف الثاني: تاريخ الوصول المتوقع + الفعلي
        dates_layout.addWidget(QLabel("تاريخ الوصول المتوقع:"), 1, 0)
        self.estimated_arrival_date_edit = FlexibleDateEdit()
        # إلغاء الإدخال التلقائي للتاريخ - يبقى فارغاً
        self.estimated_arrival_date_edit.setStyleSheet(self.get_input_style())
        dates_layout.addWidget(self.estimated_arrival_date_edit, 1, 1)

        dates_layout.addWidget(QLabel("تاريخ الوصول الفعلي:"), 1, 2)
        self.actual_arrival_date_edit = FlexibleDateEdit()
        # إلغاء الإدخال التلقائي للتاريخ - يبقى فارغاً
        self.actual_arrival_date_edit.setStyleSheet(self.get_input_style())
        dates_layout.addWidget(self.actual_arrival_date_edit, 1, 3)

        layout.addWidget(dates_group)
        layout.addStretch()

    def create_containers_tab(self):
        """إنشاء تبويب الحاويات"""
        containers_tab = QWidget()
        self.tab_widget.addTab(containers_tab, "📦 الحاويات")

        layout = QVBoxLayout(containers_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # أزرار إدارة الحاويات
        buttons_layout = QHBoxLayout()

        self.add_container_button = QPushButton("إضافة حاوية")
        self.add_container_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        self.remove_container_button = QPushButton("حذف حاوية")
        self.remove_container_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        self.remove_container_button.setEnabled(False)

        self.edit_container_button = QPushButton("تعديل حاوية")
        self.edit_container_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e68900;
            }
        """)
        self.edit_container_button.setEnabled(False)

        buttons_layout.addWidget(self.add_container_button)
        buttons_layout.addWidget(self.remove_container_button)
        buttons_layout.addWidget(self.edit_container_button)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول الحاويات
        self.containers_table = QTableWidget()
        self.containers_table.setColumnCount(8)
        self.containers_table.setHorizontalHeaderLabels([
            "رقم الحاوية", "نوع الحاوية", "الحجم", "الوزن الفارغ",
            "الوزن المحمل", "الحالة", "تاريخ التحميل", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.containers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الحاوية
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # نوع الحاوية
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الحجم
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الوزن الفارغ
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الوزن المحمل
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحالة
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # تاريخ التحميل
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # ملاحظات

        self.containers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.containers_table.setAlternatingRowColors(True)
        self.containers_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.containers_table)

        # معلومات إضافية عن الحاويات
        info_group = QGroupBox("معلومات إضافية")
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #3498db;
                background-color: white;
            }
        """)
        info_layout = QFormLayout(info_group)
        info_layout.setSpacing(12)

        # إجمالي الوزن
        self.total_weight_spin = QDoubleSpinBox()
        self.total_weight_spin.setRange(0.00, 999999.99)
        self.total_weight_spin.setDecimals(2)
        self.total_weight_spin.setSuffix(" كجم")
        self.total_weight_spin.setStyleSheet(self.get_input_style())
        # تطبيق التنسيق العربي للأرقام
        self.total_weight_spin.setLocale(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
        info_layout.addRow("إجمالي الوزن:", self.total_weight_spin)

        # إجمالي الحجم
        self.total_volume_spin = QDoubleSpinBox()
        self.total_volume_spin.setRange(0.00, 999999.99)
        self.total_volume_spin.setDecimals(2)
        self.total_volume_spin.setSuffix(" م³")
        self.total_volume_spin.setStyleSheet(self.get_input_style())
        # تطبيق التنسيق العربي للأرقام
        self.total_volume_spin.setLocale(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
        info_layout.addRow("إجمالي الحجم:", self.total_volume_spin)

        # عدد الطرود
        self.packages_count_spin = QSpinBox()
        self.packages_count_spin.setRange(0, 999999)
        self.packages_count_spin.setStyleSheet(self.get_input_style())
        # تطبيق التنسيق العربي للأرقام
        self.packages_count_spin.setLocale(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
        info_layout.addRow("عدد الطرود:", self.packages_count_spin)

        # نوع التعبئة
        self.packaging_type_combo = QComboBox()
        self.packaging_type_combo.addItems([
            "صناديق", "أكياس", "براميل", "لفائف", "منصات", "أخرى"
        ])
        self.packaging_type_combo.setStyleSheet(self.get_input_style())
        info_layout.addRow("نوع التعبئة:", self.packaging_type_combo)

        # ملاحظات الشحن
        self.shipping_notes_edit = QTextEdit()
        self.shipping_notes_edit.setMaximumHeight(80)
        self.shipping_notes_edit.setPlaceholderText("ملاحظات خاصة بالشحن...")
        self.shipping_notes_edit.setStyleSheet(self.get_input_style())
        info_layout.addRow("ملاحظات الشحن:", self.shipping_notes_edit)

        layout.addWidget(info_group)

        # إجمالي الحاويات
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.total_containers_label = QLabel("إجمالي الحاويات: 0")
        self.total_containers_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)

        totals_layout.addWidget(self.total_containers_label)

        layout.addLayout(totals_layout)

    def create_documents_tab(self):
        """إنشاء تبويب المستندات"""
        documents_tab = QWidget()
        self.tab_widget.addTab(documents_tab, "📄 المستندات")

        layout = QVBoxLayout(documents_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # قسم روابط المستندات المحددة
        documents_links_group = QGroupBox("روابط المستندات")
        documents_links_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #9b59b6;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #9b59b6;
                background-color: white;
            }
        """)
        documents_links_layout = QGridLayout(documents_links_group)
        documents_links_layout.setSpacing(8)

        # المستندات الأولية
        self.initial_docs_label = QLabel("المستندات الأولية:")
        self.initial_docs_edit = QLineEdit()
        self.initial_docs_edit.setPlaceholderText("رابط المستندات الأولية")
        self.initial_docs_edit.setReadOnly(True)
        self.initial_docs_edit.setStyleSheet(self.get_input_style())
        self.initial_docs_button = QPushButton("رابط")
        self.initial_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 4px 8px; border-radius: 3px; font-size: 11px; }")
        self.initial_docs_attach_button = QPushButton("مرفق")
        self.initial_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 4px 8px; border-radius: 3px; font-size: 11px; }")

        documents_links_layout.addWidget(self.initial_docs_label, 0, 0)
        documents_links_layout.addWidget(self.initial_docs_edit, 0, 1)
        documents_links_layout.addWidget(self.initial_docs_button, 0, 2)
        documents_links_layout.addWidget(self.initial_docs_attach_button, 0, 3)

        # مستندات DN
        self.dn_docs_label = QLabel("المستندات (DN):")
        self.dn_docs_edit = QLineEdit()
        self.dn_docs_edit.setPlaceholderText("رابط مستندات DN")
        self.dn_docs_edit.setReadOnly(True)
        self.dn_docs_edit.setStyleSheet(self.get_input_style())
        self.dn_docs_button = QPushButton("إضافة رابط")
        self.dn_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.dn_docs_attach_button = QPushButton("إضافة مرفق")
        self.dn_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.dn_docs_label, 1, 0)
        documents_links_layout.addWidget(self.dn_docs_edit, 1, 1)
        documents_links_layout.addWidget(self.dn_docs_button, 1, 2)
        documents_links_layout.addWidget(self.dn_docs_attach_button, 1, 3)

        # المستندات المرسلة للجمارك
        self.customs_docs_label = QLabel("المستندات المرسلة للجمارك:")
        self.customs_docs_edit = QLineEdit()
        self.customs_docs_edit.setPlaceholderText("رابط المستندات المرسلة للجمارك")
        self.customs_docs_edit.setReadOnly(True)
        self.customs_docs_edit.setStyleSheet(self.get_input_style())
        self.customs_docs_button = QPushButton("إضافة رابط")
        self.customs_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.customs_docs_attach_button = QPushButton("إضافة مرفق")
        self.customs_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.customs_docs_label, 2, 0)
        documents_links_layout.addWidget(self.customs_docs_edit, 2, 1)
        documents_links_layout.addWidget(self.customs_docs_button, 2, 2)
        documents_links_layout.addWidget(self.customs_docs_attach_button, 2, 3)

        # بوليصة الشحن
        self.bill_lading_label = QLabel("بوليصة الشحن:")
        self.bill_lading_edit = QLineEdit()
        self.bill_lading_edit.setPlaceholderText("رابط بوليصة الشحن")
        self.bill_lading_edit.setReadOnly(True)
        self.bill_lading_edit.setStyleSheet(self.get_input_style())
        self.bill_lading_button = QPushButton("إضافة رابط")
        self.bill_lading_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.bill_lading_attach_button = QPushButton("إضافة مرفق")
        self.bill_lading_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.bill_lading_label, 3, 0)
        documents_links_layout.addWidget(self.bill_lading_edit, 3, 1)
        documents_links_layout.addWidget(self.bill_lading_button, 3, 2)
        documents_links_layout.addWidget(self.bill_lading_attach_button, 3, 3)

        # صور الأصناف
        self.items_images_label = QLabel("صور الأصناف:")
        self.items_images_edit = QLineEdit()
        self.items_images_edit.setPlaceholderText("رابط صور الأصناف")
        self.items_images_edit.setReadOnly(True)
        self.items_images_edit.setStyleSheet(self.get_input_style())
        self.items_images_button = QPushButton("إضافة رابط")
        self.items_images_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.items_images_attach_button = QPushButton("إضافة مرفق")
        self.items_images_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.items_images_label, 4, 0)
        documents_links_layout.addWidget(self.items_images_edit, 4, 1)
        documents_links_layout.addWidget(self.items_images_button, 4, 2)
        documents_links_layout.addWidget(self.items_images_attach_button, 4, 3)

        # مستندات أخرى
        self.other_docs_label = QLabel("مستندات أخرى:")
        self.other_docs_edit = QLineEdit()
        self.other_docs_edit.setPlaceholderText("رابط مستندات أخرى")
        self.other_docs_edit.setReadOnly(True)
        self.other_docs_edit.setStyleSheet(self.get_input_style())
        self.other_docs_button = QPushButton("إضافة رابط")
        self.other_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.other_docs_attach_button = QPushButton("إضافة مرفق")
        self.other_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.other_docs_label, 5, 0)
        documents_links_layout.addWidget(self.other_docs_edit, 5, 1)
        documents_links_layout.addWidget(self.other_docs_button, 5, 2)
        documents_links_layout.addWidget(self.other_docs_attach_button, 5, 3)

        layout.addWidget(documents_links_group)

        # قسم المستندات الإضافية (الجدول القديم)
        additional_docs_group = QGroupBox("مستندات إضافية")
        additional_docs_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #e74c3c;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #e74c3c;
                background-color: white;
            }
        """)
        additional_docs_layout = QVBoxLayout(additional_docs_group)

        # أزرار إدارة المستندات الإضافية
        buttons_layout = QHBoxLayout()

        self.add_document_button = QPushButton("إضافة مستند")
        self.add_document_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        self.add_link_button = QPushButton("إضافة رابط")
        self.add_link_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        self.remove_document_button = QPushButton("حذف")
        self.remove_document_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")
        self.remove_document_button.setEnabled(False)

        self.open_document_button = QPushButton("فتح")
        self.open_document_button.setStyleSheet("QPushButton { background-color: #ff9800; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")
        self.open_document_button.setEnabled(False)

        self.add_attachment_button = QPushButton("إضافة مرفق")
        self.add_attachment_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        buttons_layout.addWidget(self.add_document_button)
        buttons_layout.addWidget(self.add_link_button)
        buttons_layout.addWidget(self.add_attachment_button)
        buttons_layout.addWidget(self.remove_document_button)
        buttons_layout.addWidget(self.open_document_button)
        buttons_layout.addStretch()

        additional_docs_layout.addLayout(buttons_layout)

        # جدول المستندات
        self.documents_table = QTableWidget()
        self.documents_table.setColumnCount(5)
        self.documents_table.setHorizontalHeaderLabels([
            "اسم المستند", "النوع", "المسار/الرابط", "تاريخ الإضافة", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.documents_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # اسم المستند
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # النوع
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # المسار/الرابط
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # تاريخ الإضافة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # ملاحظات

        self.documents_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.documents_table.setAlternatingRowColors(True)
        self.documents_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        additional_docs_layout.addWidget(self.documents_table)

        # إجمالي المستندات
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.total_documents_label = QLabel("إجمالي المستندات: 0")
        self.total_documents_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)

        totals_layout.addWidget(self.total_documents_label)

        additional_docs_layout.addLayout(totals_layout)

        layout.addWidget(additional_docs_group)

    def create_dialog_buttons(self, main_layout):
        """إنشاء أزرار الحوار"""
        button_box = QDialogButtonBox()
        button_box.setStyleSheet("""
            QDialogButtonBox {
                background-color: #f8f9fa;
                border-top: 2px solid #dee2e6;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton {
                background-color: #6c757d;
                color: white;
                font-weight: bold;
                padding: 8px 20px;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        
        # زر حفظ وإغلاق
        save_close_btn = button_box.addButton("حفظ وإغلاق", QDialogButtonBox.AcceptRole)
        save_close_btn.clicked.connect(self.save_and_close)
        
        # زر إلغاء
        cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        cancel_btn.clicked.connect(self.reject)
        
        main_layout.addWidget(button_box)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.new_button.clicked.connect(self.new_shipment)
        self.save_button.clicked.connect(self.save_shipment)
        self.edit_button.clicked.connect(self.edit_shipment)
        self.delete_button.clicked.connect(self.delete_shipment)
        self.import_excel_button.clicked.connect(self.import_from_excel)
        self.exit_button.clicked.connect(self.reject)

        # اتصال زر البحث عن المورد F9
        self.supplier_search_button.clicked.connect(self.search_supplier)

        # اتصالات أزرار الأصناف
        self.add_item_button.clicked.connect(self.add_item)
        self.remove_item_button.clicked.connect(self.remove_item)
        self.edit_item_button.clicked.connect(self.edit_item)

        # اتصالات جدول الأصناف
        self.items_table.itemSelectionChanged.connect(self.on_item_selection_changed)

        # اتصالات أزرار الحاويات
        self.add_container_button.clicked.connect(self.add_container)
        self.remove_container_button.clicked.connect(self.remove_container)
        self.edit_container_button.clicked.connect(self.edit_container)

        # اتصالات جدول الحاويات
        self.containers_table.itemSelectionChanged.connect(self.on_container_selection_changed)

        # ربط حدث تغيير محتوى الخلية (تم إلغاء البحث الذكي)
        # self.containers_table.itemChanged.connect(self.on_container_item_changed)

        # اتصالات الحقول المالية لإعادة حساب الإجماليات
        if hasattr(self, 'shipping_cost_edit'):
            self.shipping_cost_edit.textChanged.connect(self.calculate_totals)
        if hasattr(self, 'insurance_cost_edit'):
            self.insurance_cost_edit.textChanged.connect(self.calculate_totals)
        if hasattr(self, 'customs_fees_edit'):
            self.customs_fees_edit.textChanged.connect(self.calculate_totals)
        if hasattr(self, 'other_fees_edit'):
            self.other_fees_edit.textChanged.connect(self.calculate_totals)
        if hasattr(self, 'exchange_rate_edit'):
            self.exchange_rate_edit.textChanged.connect(self.calculate_totals)
        if hasattr(self, 'paid_amount_edit'):
            self.paid_amount_edit.textChanged.connect(self.calculate_remaining_amount)

        # ربط حقول أجور الشحن الجديدة
        if hasattr(self, 'supplier_shipping_fees_edit'):
            self.supplier_shipping_fees_edit.textChanged.connect(self.calculate_totals)
        if hasattr(self, 'shipping_company_fees_edit'):
            self.shipping_company_fees_edit.textChanged.connect(self.calculate_totals)

        # دعم الحقول القديمة أيضاً (SpinBox) للتوافق مع الإصدارات السابقة
        if hasattr(self, 'shipping_cost_spin'):
            self.shipping_cost_spin.valueChanged.connect(self.calculate_totals)
        if hasattr(self, 'insurance_cost_spin'):
            self.insurance_cost_spin.valueChanged.connect(self.calculate_totals)
        if hasattr(self, 'customs_fees_spin'):
            self.customs_fees_spin.valueChanged.connect(self.calculate_totals)
        if hasattr(self, 'other_fees_spin'):
            self.other_fees_spin.valueChanged.connect(self.calculate_totals)
        if hasattr(self, 'exchange_rate_spin'):
            self.exchange_rate_spin.valueChanged.connect(self.calculate_totals)
        if hasattr(self, 'paid_amount_spin'):
            self.paid_amount_spin.valueChanged.connect(self.calculate_remaining_amount)

        # اتصالات أزرار المستندات المحددة (الروابط)
        if hasattr(self, 'initial_docs_button'):
            self.initial_docs_button.clicked.connect(lambda: self.add_specific_link("المستندات الأولية", self.initial_docs_edit))
        if hasattr(self, 'dn_docs_button'):
            self.dn_docs_button.clicked.connect(lambda: self.add_specific_link("المستندات (DN)", self.dn_docs_edit))
        if hasattr(self, 'customs_docs_button'):
            self.customs_docs_button.clicked.connect(lambda: self.add_specific_link("المستندات المرسلة للجمارك", self.customs_docs_edit))
        if hasattr(self, 'bill_lading_button'):
            self.bill_lading_button.clicked.connect(lambda: self.add_specific_link("بوليصة الشحن", self.bill_lading_edit))
        if hasattr(self, 'items_images_button'):
            self.items_images_button.clicked.connect(lambda: self.add_specific_link("صور الأصناف", self.items_images_edit))
        if hasattr(self, 'other_docs_button'):
            self.other_docs_button.clicked.connect(lambda: self.add_specific_link("مستندات أخرى", self.other_docs_edit))

        # اتصالات أزرار المستندات المحددة (المرفقات)
        if hasattr(self, 'initial_docs_attach_button'):
            self.initial_docs_attach_button.clicked.connect(lambda: self.add_specific_attachment("المستندات الأولية"))
        if hasattr(self, 'dn_docs_attach_button'):
            self.dn_docs_attach_button.clicked.connect(lambda: self.add_specific_attachment("المستندات (DN)"))
        if hasattr(self, 'customs_docs_attach_button'):
            self.customs_docs_attach_button.clicked.connect(lambda: self.add_specific_attachment("المستندات المرسلة للجمارك"))
        if hasattr(self, 'bill_lading_attach_button'):
            self.bill_lading_attach_button.clicked.connect(lambda: self.add_specific_attachment("بوليصة الشحن"))
        if hasattr(self, 'items_images_attach_button'):
            self.items_images_attach_button.clicked.connect(lambda: self.add_specific_attachment("صور الأصناف"))
        if hasattr(self, 'other_docs_attach_button'):
            self.other_docs_attach_button.clicked.connect(lambda: self.add_specific_attachment("مستندات أخرى"))

        # اتصالات أزرار المستندات الإضافية
        if hasattr(self, 'add_link_button'):
            self.add_link_button.clicked.connect(self.add_link)
        if hasattr(self, 'add_attachment_button'):
            self.add_attachment_button.clicked.connect(self.add_attachment)
        if hasattr(self, 'add_document_button'):
            self.add_document_button.clicked.connect(self.add_document)
        if hasattr(self, 'remove_document_button'):
            self.remove_document_button.clicked.connect(self.remove_document)
        if hasattr(self, 'open_document_button'):
            self.open_document_button.clicked.connect(self.open_document)

        # إعداد اختصارات لوحة المفاتيح
        self.setup_shortcuts()

    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # اختصار F9 للبحث عن المورد (في تبويب البيانات الأساسية)
        f9_supplier_shortcut = QShortcut(QKeySequence("F9"), self)
        f9_supplier_shortcut.activated.connect(self.handle_f9_shortcut)

        # اختصار Ctrl+N لإضافة صنف جديد
        add_item_shortcut = QShortcut(QKeySequence("Ctrl+N"), self)
        add_item_shortcut.activated.connect(self.add_item)

        # اختصار Delete لحذف الصنف المحدد
        delete_item_shortcut = QShortcut(QKeySequence("Delete"), self)
        delete_item_shortcut.activated.connect(self.remove_item)

    def handle_f9_shortcut(self):
        """التعامل مع اختصار F9 حسب التبويب النشط"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # تبويب البيانات الأساسية
            self.search_supplier()
        elif current_tab == 1:  # تبويب الأصناف
            self.add_item()
        else:
            # في التبويبات الأخرى، افتراضياً البحث عن المورد
            self.search_supplier()

    def on_shipping_company_selected(self, company_info):
        """عند اختيار شركة شحن من الواجهة الذكية"""
        try:
            # تحديث نوع الشحن تلقائياً حسب نوع الشركة
            if hasattr(self, 'shipping_type_combo'):
                shipping_type = company_info.get('type', '')
                if shipping_type:
                    # البحث عن النوع في القائمة المنسدلة
                    for i in range(self.shipping_type_combo.count()):
                        if self.shipping_type_combo.itemText(i) == shipping_type:
                            self.shipping_type_combo.setCurrentIndex(i)
                            break

            # عرض معلومات إضافية في رسالة
            status_msg = f"✅ تم اختيار شركة {company_info['arabic_name']} ({company_info['country']})"
            print(status_msg)  # يمكن استبدالها بشريط حالة لاحقاً

        except Exception as e:
            print(f"خطأ في معالجة اختيار شركة الشحن: {e}")

    def on_shipping_company_validated(self, validation_result):
        """عند التحقق من صحة شركة الشحن"""
        try:
            if validation_result.get('is_valid', False):
                # شركة صحيحة - يمكن إضافة منطق إضافي هنا
                pass
            elif validation_result.get('suggestions'):
                # يوجد اقتراحات - يمكن عرض تنبيه أو معلومات إضافية
                pass
            else:
                # شركة غير معروفة - يمكن عرض تحذير
                pass

        except Exception as e:
            print(f"خطأ في معالجة التحقق من شركة الشحن: {e}")

    def search_supplier(self):
        """البحث عن مورد باستخدام نظام الموردين"""
        try:
            from .supplier_search_dialog import SupplierSearchDialog

            # فتح نافذة البحث عن الموردين
            search_dialog = SupplierSearchDialog(self)

            if search_dialog.exec() == QDialog.Accepted:
                selected_supplier = search_dialog.selected_supplier
                if selected_supplier:
                    # ملء حقل المورد بالاسم والكود
                    supplier_text = f"{selected_supplier.name} ({selected_supplier.code})"
                    self.supplier_edit.setText(supplier_text)

                    # حفظ معرف المورد للاستخدام لاحقاً
                    self.selected_supplier_id = selected_supplier.id
                    # تعيين معرف المورد كخاصية للحقل للتحقق من الصحة
                    self.supplier_edit.setProperty("supplier_id", selected_supplier.id)

                    QMessageBox.information(
                        self,
                        "تم الاختيار",
                        f"✅ تم اختيار المورد: {selected_supplier.name}"
                    )

        except ImportError:
            # في حالة عدم وجود نافذة البحث، استخدم النظام البسيط
            QMessageBox.warning(
                self,
                "تحذير",
                "نافذة البحث عن الموردين غير متوفرة.\nيمكنك كتابة اسم المورد مباشرة في الحقل."
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء البحث عن المورد:\n{str(e)}"
            )

    def add_item(self):
        """إضافة صنف جديد من خلال نافذة البحث المتقدمة"""
        try:
            from .advanced_item_search_dialog import AdvancedItemSearchDialog
            from PySide6.QtWidgets import QDialog

            # فتح نافذة البحث المتقدمة
            dialog = AdvancedItemSearchDialog(self)
            if dialog.exec() == QDialog.Accepted:
                selected_item = dialog.get_selected_item()
                if selected_item:
                    # فتح نافذة إدخال السعر والكمية
                    from .item_price_dialog import ItemPriceDialog
                    price_dialog = ItemPriceDialog(self, selected_item)
                    if price_dialog.exec() == QDialog.Accepted:
                        price_data = price_dialog.get_item_data()

                        # إضافة الصنف إلى الجدول
                        row_count = self.items_table.rowCount()
                        self.items_table.insertRow(row_count)

                        # استخدام البيانات المدخلة من النافذة
                        quantity = price_data['quantity']
                        unit_price = price_data['unit_price']
                        total_price = price_data['total_price']
                        production_date = price_data.get('production_date', '')
                        expiry_date = price_data.get('expiry_date', '')
                        notes = price_data['notes']

                        # ملء البيانات - الترتيب الجديد: "كود الصنف", "اسم الصنف", "الكمية", "سعر الوحدة", "تاريخ الإنتاج", "تاريخ الانتهاء", "السعر الإجمالي", "الوزن", "ملاحظات"
                        self.items_table.setItem(row_count, 0, QTableWidgetItem(selected_item['code']))  # كود الصنف
                        self.items_table.setItem(row_count, 1, QTableWidgetItem(selected_item['name']))  # اسم الصنف
                        self.items_table.setItem(row_count, 2, QTableWidgetItem(str(quantity)))  # الكمية
                        self.items_table.setItem(row_count, 3, QTableWidgetItem(f"{unit_price:.2f}"))  # سعر الوحدة
                        self.items_table.setItem(row_count, 4, QTableWidgetItem(production_date))  # تاريخ الإنتاج
                        self.items_table.setItem(row_count, 5, QTableWidgetItem(expiry_date))  # تاريخ الانتهاء
                        self.items_table.setItem(row_count, 6, QTableWidgetItem(f"{total_price:.2f}"))  # السعر الإجمالي
                        self.items_table.setItem(row_count, 7, QTableWidgetItem(format_number(selected_item['weight'], 2, True)))  # الوزن
                        # دمج الملاحظات من النافذة مع وصف الصنف
                        combined_notes = f"{selected_item['description'] or ''}"
                        if notes:
                            combined_notes += f" | {notes}" if combined_notes else notes
                        self.items_table.setItem(row_count, 8, QTableWidgetItem(combined_notes))  # ملاحظات

                        # حفظ معرف الصنف في البيانات المخفية
                        self.items_table.item(row_count, 0).setData(Qt.UserRole, selected_item['id'])

                        # تحديث المجموع الكلي
                        self.update_items_total()

                        QMessageBox.information(
                            self,
                            "تم بنجاح",
                            f"✅ تم إضافة الصنف: {selected_item['name']}\nالكمية: {quantity}\nالسعر: {unit_price:.2f}\nالمجموع: {total_price:.2f}"
                        )

        except ImportError:
            # في حالة عدم وجود نافذة البحث المتقدمة، استخدم الطريقة القديمة
            self.add_item_simple()
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إضافة الصنف:\n{str(e)}"
            )

    def add_item_simple(self):
        """إضافة صنف بالطريقة البسيطة (احتياطية)"""
        try:
            from PySide6.QtWidgets import QInputDialog

            # الحصول على معلومات الصنف
            item_name, ok = QInputDialog.getText(
                self,
                "إضافة صنف",
                "اسم الصنف:"
            )

            if not ok or not item_name.strip():
                return

            # الحصول على الكمية
            quantity, ok = QInputDialog.getInt(
                self,
                "إضافة صنف",
                "الكمية:",
                1, 1, 999999
            )

            if not ok:
                return

            # الحصول على السعر
            price, ok = QInputDialog.getDouble(
                self,
                "إضافة صنف",
                "السعر:",
                0.0, 0.0, 999999.99, 2
            )

            if not ok:
                return

            # إضافة الصنف إلى الجدول
            row_count = self.items_table.rowCount()
            self.items_table.insertRow(row_count)

            # ملء البيانات
            self.items_table.setItem(row_count, 0, QTableWidgetItem(f"ITEM{row_count + 1:03d}"))  # كود الصنف
            self.items_table.setItem(row_count, 1, QTableWidgetItem(item_name.strip()))  # اسم الصنف
            self.items_table.setItem(row_count, 2, QTableWidgetItem(str(quantity)))  # الكمية
            self.items_table.setItem(row_count, 3, QTableWidgetItem(f"{price:.2f}"))  # سعر الوحدة
            self.items_table.setItem(row_count, 4, QTableWidgetItem(""))  # تاريخ الإنتاج - فارغ
            self.items_table.setItem(row_count, 5, QTableWidgetItem(""))  # تاريخ الانتهاء - فارغ

            total = quantity * price
            self.items_table.setItem(row_count, 6, QTableWidgetItem(f"{total:.2f}"))  # السعر الإجمالي
            self.items_table.setItem(row_count, 7, QTableWidgetItem("0.00"))  # الوزن (افتراضي)
            self.items_table.setItem(row_count, 8, QTableWidgetItem(""))  # ملاحظات

            # تحديث المجموع الكلي
            self.update_items_total()

            QMessageBox.information(
                self,
                "تم بنجاح",
                f"✅ تم إضافة الصنف: {item_name}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إضافة الصنف:\n{str(e)}"
            )

    def remove_item(self):
        """حذف الصنف المحدد"""
        try:
            current_row = self.items_table.currentRow()

            if current_row < 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد صنف للحذف"
                )
                return

            # الحصول على اسم الصنف
            item_name = self.items_table.item(current_row, 1)
            item_name_text = item_name.text() if item_name else "غير محدد"

            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف الصنف:\n{item_name_text}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.items_table.removeRow(current_row)

                # إعادة ترقيم الصفوف
                self.renumber_items_table()

                # تحديث المجموع الكلي
                self.update_items_total()

                QMessageBox.information(
                    self,
                    "تم بنجاح",
                    f"✅ تم حذف الصنف: {item_name_text}"
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء حذف الصنف:\n{str(e)}"
            )

    def edit_item(self):
        """تعديل الصنف المحدد"""
        try:
            current_row = self.items_table.currentRow()

            if current_row < 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد صنف للتعديل"
                )
                return

            # الحصول على البيانات الحالية
            current_name = self.items_table.item(current_row, 1)
            current_quantity = self.items_table.item(current_row, 2)
            current_price = self.items_table.item(current_row, 3)

            current_name_text = current_name.text() if current_name else ""

            # تحويل آمن للكمية
            try:
                current_quantity_value = int(float(current_quantity.text())) if current_quantity and current_quantity.text().strip() else 1
            except (ValueError, AttributeError):
                current_quantity_value = 1

            # تحويل آمن للسعر
            def safe_float(text):
                try:
                    if not text or not text.strip():
                        return 0.0
                    # استبدال الفاصلة العربية بالنقطة الإنجليزية
                    clean_text = text.replace('،', '.').replace(',', '.')
                    return float(clean_text)
                except (ValueError, AttributeError):
                    return 0.0

            current_price_value = safe_float(current_price.text()) if current_price else 0.0

            # تعديل اسم الصنف
            from PySide6.QtWidgets import QInputDialog

            item_name, ok = QInputDialog.getText(
                self,
                "تعديل الصنف",
                "اسم الصنف:",
                text=current_name_text
            )

            if not ok:
                return

            # تعديل الكمية
            quantity, ok = QInputDialog.getInt(
                self,
                "تعديل الصنف",
                "الكمية:",
                current_quantity_value, 1, 999999
            )

            if not ok:
                return

            # تعديل السعر
            price, ok = QInputDialog.getDouble(
                self,
                "تعديل الصنف",
                "السعر:",
                current_price_value, 0.0, 999999.99, 2
            )

            if not ok:
                return

            # تحديث البيانات في الجدول
            self.items_table.setItem(current_row, 1, QTableWidgetItem(item_name.strip()))
            self.items_table.setItem(current_row, 2, QTableWidgetItem(str(quantity)))
            self.items_table.setItem(current_row, 3, QTableWidgetItem(f"{price:.2f}"))

            total = quantity * price
            self.items_table.setItem(current_row, 6, QTableWidgetItem(f"{total:.2f}"))  # السعر الإجمالي في العمود 6

            # تحديث المجموع الكلي
            self.update_items_total()

            QMessageBox.information(
                self,
                "تم بنجاح",
                f"✅ تم تعديل الصنف: {item_name}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء تعديل الصنف:\n{str(e)}"
            )

    def on_item_selection_changed(self):
        """تفعيل/إلغاء تفعيل أزرار الأصناف حسب التحديد"""
        has_selection = self.items_table.currentRow() >= 0
        self.remove_item_button.setEnabled(has_selection)
        self.edit_item_button.setEnabled(has_selection)

    def renumber_items_table(self):
        """إعادة ترقيم جدول الأصناف"""
        for row in range(self.items_table.rowCount()):
            self.items_table.setItem(row, 0, QTableWidgetItem(f"ITEM{row + 1:03d}"))

    def update_items_total(self):
        """تحديث المجموع الكلي للأصناف"""
        try:
            def safe_float(text):
                try:
                    if not text or not text.strip():
                        return 0.0
                    # استبدال الفاصلة العربية بالنقطة الإنجليزية وإزالة الفواصل
                    clean_text = text.replace('،', '.').replace(',', '.')
                    return float(clean_text)
                except (ValueError, AttributeError):
                    return 0.0

            total_value = 0.0
            total_items_count = self.items_table.rowCount()

            # حساب إجمالي القيمة
            for row in range(total_items_count):
                total_item = self.items_table.item(row, 6)  # عمود المجموع - العمود 6 الآن
                if total_item:
                    item_value = safe_float(total_item.text())
                    total_value += item_value


            # تحديث حقول إجمالي الأصناف وإجمالي القيمة في تبويب الأصناف
            if hasattr(self, 'total_items_label'):
                self.total_items_label.setText(f"إجمالي الأصناف: {total_items_count}")

            if hasattr(self, 'total_value_label'):
                self.total_value_label.setText(f"إجمالي القيمة: {total_value:.2f}")

            # تحديث حقل المجموع في تبويب المالية
            if hasattr(self, 'total_amount_edit'):
                self.total_amount_edit.setText(f"{total_value:.2f}")

            # تحديث قيمة البضاعة في التبويب المالي
            if hasattr(self, 'goods_value_edit'):
                self.goods_value_edit.setText(f"{total_value:.2f}")

            # إعادة حساب الإجماليات المالية
            self.calculate_totals()

            # إجبار معالجة الأحداث لضمان التحديث البصري
            from PySide6.QtWidgets import QApplication
            QApplication.processEvents()

            # تحديث النافذة بالكامل
            self.update()
            self.repaint()

        except Exception as e:
            print(f"❌ خطأ في تحديث المجموع: {str(e)}")
            import traceback
            traceback.print_exc()

    def calculate_totals(self):
        """حساب الإجماليات المالية"""
        try:
            def safe_float(text):
                try:
                    if not text or not text.strip():
                        return 0.0
                    # استبدال الفاصلة العربية بالنقطة الإنجليزية
                    clean_text = text.replace('،', '.').replace(',', '.')
                    return float(clean_text)
                except (ValueError, AttributeError):
                    return 0.0

            # الحصول على قيمة البضاعة من الحقل (تم تحديثها بالفعل في update_items_total)
            goods_value = safe_float(self.goods_value_edit.text()) if hasattr(self, 'goods_value_edit') else 0.0

            # حساب إجمالي التكاليف (استخدام نفس دالة safe_float المعرفة أعلاه)
            shipping_cost = safe_float(self.shipping_cost_edit.text()) if hasattr(self, 'shipping_cost_edit') else 0.0
            insurance_cost = safe_float(self.insurance_cost_edit.text()) if hasattr(self, 'insurance_cost_edit') else 0.0
            customs_fees = safe_float(self.customs_fees_edit.text()) if hasattr(self, 'customs_fees_edit') else 0.0
            other_fees = safe_float(self.other_fees_edit.text()) if hasattr(self, 'other_fees_edit') else 0.0

            total_costs = goods_value + shipping_cost + insurance_cost + customs_fees + other_fees

            if hasattr(self, 'total_costs_edit'):
                self.total_costs_edit.setText(f"{total_costs:.2f}")

            # حساب المبلغ بالعملة المحلية
            exchange_rate = safe_float(self.exchange_rate_edit.text()) if hasattr(self, 'exchange_rate_edit') else 1.0
            total_local = total_costs * exchange_rate

            if hasattr(self, 'total_local_currency_edit'):
                self.total_local_currency_edit.setText(f"{total_local:.2f}")

            # حساب قيمة البضاعة بالدولار
            self.calculate_goods_value_usd()

            # حساب المبلغ المتبقي
            self.calculate_remaining_amount()

        except Exception as e:
            print(f"خطأ في حساب الإجماليات: {str(e)}")

    def calculate_remaining_amount(self):
        """حساب المبلغ المتبقي"""
        try:
            if not hasattr(self, 'total_local_currency_spin') or not hasattr(self, 'paid_amount_spin'):
                return

            def safe_float(text):
                try:
                    if not text or not text.strip():
                        return 0.0
                    # استبدال الفاصلة العربية بالنقطة الإنجليزية
                    clean_text = text.replace('،', '.').replace(',', '.')
                    return float(clean_text)
                except (ValueError, AttributeError):
                    return 0.0

            total_amount = safe_float(self.total_local_currency_edit.text()) if hasattr(self, 'total_local_currency_edit') else 0.0
            paid_amount = safe_float(self.paid_amount_edit.text()) if hasattr(self, 'paid_amount_edit') else 0.0
            remaining = max(0, total_amount - paid_amount)

            if hasattr(self, 'remaining_amount_edit'):
                self.remaining_amount_edit.setText(f"{remaining:.2f}")

            # تحديث حالة الدفع تلقائياً
            if hasattr(self, 'payment_status_combo'):
                if remaining == 0 and total_amount > 0:
                    self.payment_status_combo.setCurrentText("تم الدفع بالكامل")
                elif paid_amount > 0 and remaining > 0:
                    self.payment_status_combo.setCurrentText("دفع جزئي")
                elif paid_amount == 0:
                    self.payment_status_combo.setCurrentText("لم يتم الدفع")

        except Exception as e:
            print(f"خطأ في حساب المبلغ المتبقي: {str(e)}")

    # ==================== وظائف إدارة الحاويات ====================

    def add_container(self):
        """إضافة حاوية جديدة"""
        try:
            from .container_dialog import ContainerDialog
            from PySide6.QtWidgets import QDialog

            dialog = ContainerDialog(self)
            if dialog.exec() == QDialog.Accepted:
                container_data = dialog.get_container_data()
                if container_data:
                    self.add_container_to_table(container_data)

        except ImportError:
            # في حالة عدم وجود نافذة الحاوية، استخدم طريقة بسيطة
            self.add_container_simple()
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إضافة الحاوية:\n{str(e)}"
            )

    def add_container_simple(self):
        """إضافة حاوية بالطريقة البسيطة (احتياطية)"""
        try:
            from PySide6.QtWidgets import QInputDialog

            # طلب رقم الحاوية
            container_number, ok = QInputDialog.getText(
                self,
                "إضافة حاوية",
                "رقم الحاوية:",
                text=""
            )

            if not ok or not container_number.strip():
                return

            # التحقق من عدم وجود الحاوية مسبقاً
            for row in range(self.containers_table.rowCount()):
                existing_item = self.containers_table.item(row, 0)
                if existing_item and existing_item.text() == container_number.strip():
                    QMessageBox.warning(self, "تحذير", "هذه الحاوية موجودة بالفعل في الشحنة")
                    return

            # إضافة الحاوية إلى الجدول
            row_count = self.containers_table.rowCount()
            self.containers_table.insertRow(row_count)

            # ملء البيانات الافتراضية
            self.containers_table.setItem(row_count, 0, QTableWidgetItem(container_number.strip()))  # رقم الحاوية
            self.containers_table.setItem(row_count, 1, QTableWidgetItem("20' Standard"))  # نوع الحاوية
            self.containers_table.setItem(row_count, 2, QTableWidgetItem("20 قدم"))  # الحجم
            self.containers_table.setItem(row_count, 3, QTableWidgetItem("2300.00"))  # الوزن الفارغ
            self.containers_table.setItem(row_count, 4, QTableWidgetItem("0.00"))  # الوزن المحمل
            self.containers_table.setItem(row_count, 5, QTableWidgetItem("فارغة"))  # الحالة
            self.containers_table.setItem(row_count, 6, QTableWidgetItem(""))  # تاريخ التحميل
            self.containers_table.setItem(row_count, 7, QTableWidgetItem(""))  # ملاحظات

            QMessageBox.information(
                self,
                "تم بنجاح",
                f"✅ تم إضافة الحاوية: {container_number}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إضافة الحاوية:\n{str(e)}"
            )

    def add_container_to_table(self, container_data):
        """إضافة حاوية إلى الجدول"""
        try:
            # التحقق من عدم وجود الحاوية مسبقاً
            for row in range(self.containers_table.rowCount()):
                existing_number = self.containers_table.item(row, 0).text()
                if existing_number == container_data['container_number']:
                    QMessageBox.warning(self, "تحذير", "هذه الحاوية موجودة بالفعل في الشحنة")
                    return

            # إضافة صف جديد
            row_count = self.containers_table.rowCount()
            self.containers_table.insertRow(row_count)

            # ملء البيانات
            self.containers_table.setItem(row_count, 0, QTableWidgetItem(container_data['container_number']))
            self.containers_table.setItem(row_count, 1, QTableWidgetItem(container_data['container_type']))
            self.containers_table.setItem(row_count, 2, QTableWidgetItem(container_data['size']))
            self.containers_table.setItem(row_count, 3, QTableWidgetItem(f"{container_data['empty_weight']:.2f}"))
            self.containers_table.setItem(row_count, 4, QTableWidgetItem(f"{container_data['loaded_weight']:.2f}"))
            self.containers_table.setItem(row_count, 5, QTableWidgetItem(container_data['status']))
            self.containers_table.setItem(row_count, 6, QTableWidgetItem(""))  # تاريخ التحميل
            self.containers_table.setItem(row_count, 7, QTableWidgetItem(container_data['notes']))

            QMessageBox.information(
                self,
                "تم بنجاح",
                f"✅ تم إضافة الحاوية: {container_data['container_number']}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إضافة الحاوية إلى الجدول:\n{str(e)}"
            )

    def remove_container(self):
        """حذف الحاوية المحددة"""
        try:
            current_row = self.containers_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد حاوية لحذفها"
                )
                return

            # الحصول على رقم الحاوية
            container_number = self.containers_table.item(current_row, 0).text()

            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف الحاوية:\n{container_number}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.containers_table.removeRow(current_row)
                QMessageBox.information(
                    self,
                    "تم بنجاح",
                    f"✅ تم حذف الحاوية: {container_number}"
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء حذف الحاوية:\n{str(e)}"
            )

    def edit_container(self):
        """تعديل الحاوية المحددة"""
        try:
            current_row = self.containers_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد حاوية لتعديلها"
                )
                return

            try:
                from .container_dialog import ContainerDialog

                def safe_float(text):
                    try:
                        if not text or not text.strip():
                            return 0.0
                        # استبدال الفاصلة العربية بالنقطة الإنجليزية
                        clean_text = text.replace('،', '.').replace(',', '.')
                        return float(clean_text)
                    except (ValueError, AttributeError):
                        return 0.0

                # الحصول على البيانات الحالية
                current_data = {
                    'container_number': self.containers_table.item(current_row, 0).text(),
                    'container_type': self.containers_table.item(current_row, 1).text(),
                    'size': self.containers_table.item(current_row, 2).text(),
                    'empty_weight': safe_float(self.containers_table.item(current_row, 3).text()),
                    'loaded_weight': safe_float(self.containers_table.item(current_row, 4).text()),
                    'status': self.containers_table.item(current_row, 5).text(),
                    'notes': self.containers_table.item(current_row, 7).text()
                }

                dialog = ContainerDialog(self, current_data)
                if dialog.exec() == QDialog.Accepted:
                    container_data = dialog.get_container_data()
                    if container_data:
                        # تحديث البيانات في الجدول
                        self.containers_table.item(current_row, 0).setText(container_data['container_number'])
                        self.containers_table.item(current_row, 1).setText(container_data['container_type'])
                        self.containers_table.item(current_row, 2).setText(container_data['size'])
                        self.containers_table.item(current_row, 3).setText(f"{container_data['empty_weight']:.2f}")
                        self.containers_table.item(current_row, 4).setText(f"{container_data['loaded_weight']:.2f}")
                        self.containers_table.item(current_row, 5).setText(container_data['status'])
                        self.containers_table.item(current_row, 7).setText(container_data['notes'])

                        QMessageBox.information(
                            self,
                            "تم بنجاح",
                            f"✅ تم تعديل الحاوية: {container_data['container_number']}"
                        )

            except ImportError:
                # في حالة عدم وجود نافذة الحاوية، استخدم طريقة بسيطة
                self.edit_container_simple(current_row)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء تعديل الحاوية:\n{str(e)}"
            )

    def edit_container_simple(self, current_row):
        """تعديل حاوية بالطريقة البسيطة (احتياطية)"""
        try:
            from PySide6.QtWidgets import QInputDialog

            # الحصول على البيانات الحالية مع التحقق من وجود العناصر
            current_number_item = self.containers_table.item(current_row, 0)
            current_status_item = self.containers_table.item(current_row, 5)

            current_number = current_number_item.text() if current_number_item else ""
            current_status = current_status_item.text() if current_status_item else "فارغة"

            # تعديل رقم الحاوية
            container_number, ok = QInputDialog.getText(
                self,
                "تعديل الحاوية",
                "رقم الحاوية:",
                text=current_number
            )

            if not ok:
                return

            # تعديل الحالة
            status_items = ["فارغة", "محملة", "في الطريق", "وصلت", "تم التفريغ"]
            status, ok = QInputDialog.getItem(
                self,
                "تعديل الحاوية",
                "حالة الحاوية:",
                status_items,
                status_items.index(current_status) if current_status in status_items else 0,
                False
            )

            if not ok:
                return

            # تحديث البيانات في الجدول مع التحقق من وجود العناصر
            number_item = self.containers_table.item(current_row, 0)
            status_item = self.containers_table.item(current_row, 5)

            if number_item:
                number_item.setText(container_number.strip())
            else:
                from PySide6.QtWidgets import QTableWidgetItem
                self.containers_table.setItem(current_row, 0, QTableWidgetItem(container_number.strip()))

            if status_item:
                status_item.setText(status)
            else:
                from PySide6.QtWidgets import QTableWidgetItem
                self.containers_table.setItem(current_row, 5, QTableWidgetItem(status))

            QMessageBox.information(
                self,
                "تم بنجاح",
                f"✅ تم تعديل الحاوية: {container_number}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء تعديل الحاوية:\n{str(e)}"
            )

    def on_container_selection_changed(self):
        """تفعيل/إلغاء تفعيل أزرار الحاويات حسب التحديد"""
        has_selection = self.containers_table.currentRow() >= 0
        self.remove_container_button.setEnabled(has_selection)
        self.edit_container_button.setEnabled(has_selection)

    # تم حذف دالة البحث الذكي للحاويات
    # def on_container_item_changed(self, item):

    # تم حذف دالة التحقق من صحة رقم الحاوية
    # def is_valid_container_number(self, container_number):

    # تم حذف دالة بدء البحث الذكي للحاوية
    # def start_smart_container_search(self, row, container_number):

    # تم حذف دالة تنفيذ البحث الذكي للحاوية
    # def perform_smart_container_search(self, row, container_number, loading_item, original_text):

    # تم حذف دالة تحديد شركة الشحن من رقم الحاوية
    # def detect_carrier_from_container(self, container_number):

    # تم حذف دالة تطبيق نتائج البحث الذكي
    # def apply_smart_search_results(self, row, container_number, search_results, loading_item, original_text):

    # تم حذف جميع دوال البحث الذكي والتعبئة التلقائية للحاويات
    # def apply_fallback_container_data(self, row, container_number, loading_item, original_text):
    # def generate_smart_fallback_data(self, container_number, carrier):
    # def fill_container_data_from_search(self, row, result, loading_item, original_text):
    # def fill_container_data_from_fallback(self, row, data, loading_item, original_text):
    # def fill_shipping_data_from_search(self, result):
    # def fill_shipping_data_from_fallback(self, data):

    # تم حذف دالة توليد البيانات الاحتياطية الذكية
    # def generate_smart_fallback_data(self, container_number, carrier):

    # تم حذف دالة تعبئة بيانات الحاوية من نتائج البحث
    # def fill_container_data_from_search(self, row, result, loading_item, original_text):

    # تم حذف جميع دوال تعبئة البيانات من البحث الذكي والبيانات الاحتياطية
    # def fill_container_data_from_fallback(self, row, data, loading_item, original_text):
    # def fill_shipping_data_from_search(self, result):
    # def fill_shipping_data_from_fallback(self, data):

    def new_shipment(self):
        """إنشاء شحنة جديدة"""
        reply = QMessageBox.question(
            self,
            "شحنة جديدة",
            "هل تريد إنشاء شحنة جديدة؟\nسيتم مسح البيانات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.clear_form()
            self.current_shipment_id = None
            self.is_edit_mode = False  # إعادة تعيين وضع التعديل
            self.edit_button.setEnabled(False)
            self.delete_button.setEnabled(False)

            # التعبئة التلقائية معطلة افتراضياً حتى في الشحنة الجديدة
            self.configure_auto_fill_mode()

            # سؤال المستخدم إذا كان يريد بيانات تجريبية
            test_reply = QMessageBox.question(
                self,
                "بيانات تجريبية",
                "هل تريد ملء النموذج ببيانات تجريبية للاختبار؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if test_reply == QMessageBox.Yes:
                self.fill_test_data()

            QMessageBox.information(self, "شحنة جديدة", "✅ تم إنشاء نموذج شحنة جديد")
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.shipment_number_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الشحنة")
            return False

        if not self.supplier_edit.property("supplier_id"):
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المورد")
            return False

        # التحقق من عدم تكرار رقم الشحنة
        if not self.check_shipment_number_unique():
            return False

        return True

    def check_shipment_number_unique(self):
        """التحقق من أن رقم الشحنة فريد"""
        shipment_number = self.shipment_number_edit.text().strip()

        # إذا كان الحقل فارغ، لا نحتاج للتحقق
        if not shipment_number:
            return True

        session = self.db_manager.get_session()
        try:
            # في وضع التعديل، استثناء الشحنة الحالية من التحقق
            query = session.query(Shipment).filter(
                Shipment.shipment_number == shipment_number
            )

            # في وضع التعديل، استثناء الشحنة الحالية
            if self.is_edit_mode and self.current_shipment_id:
                query = query.filter(Shipment.id != self.current_shipment_id)

            existing_shipment = query.first()

            # إذا وُجدت شحنة أخرى بنفس الرقم، اقتراح رقم جديد
            if existing_shipment:
                new_number = self.generate_unique_shipment_number()

                reply = QMessageBox.question(
                    self,
                    "رقم الشحنة مكرر",
                    f"رقم الشحنة '{shipment_number}' موجود مسبقاً.\n\n"
                    f"هل تريد استخدام الرقم المقترح: '{new_number}'؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    self.shipment_number_edit.setText(new_number)
                    return True
                else:
                    return False

            # إذا لم توجد شحنة أخرى بنفس الرقم، فالرقم فريد
            return True

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في التحقق من رقم الشحنة: {str(e)}")
            return False
        finally:
            session.close()

    def generate_unique_shipment_number(self):
        """توليد رقم شحنة فريد"""
        from datetime import datetime
        import random
        import time

        session = self.db_manager.get_session()
        try:
            # الحصول على السنة الحالية
            current_year = datetime.now().year

            # البحث عن جميع أرقام الشحنات في هذه السنة
            existing_numbers = session.query(Shipment.shipment_number).filter(
                Shipment.shipment_number.like(f'SH-{current_year}-%')
            ).all()

            # استخراج الأرقام التسلسلية الموجودة
            used_numbers = set()
            for (number,) in existing_numbers:
                try:
                    # استخراج الرقم التسلسلي من نهاية رقم الشحنة
                    parts = number.split('-')
                    if len(parts) >= 3:
                        sequential_num = int(parts[-1])
                        used_numbers.add(sequential_num)
                except (ValueError, IndexError):
                    continue

            # العثور على أول رقم متاح
            next_number = 1
            while next_number in used_numbers:
                next_number += 1

            # إضافة عنصر عشوائي لضمان الفرادة في كل استدعاء
            timestamp_suffix = int(time.time() * 1000) % 1000
            next_number = next_number * 1000 + timestamp_suffix

            # توليد رقم الشحنة الجديد
            new_number = f"SH-{current_year}-{next_number:06d}"

            # التأكد النهائي من أن الرقم فريد
            attempt = 0
            while session.query(Shipment).filter(Shipment.shipment_number == new_number).first() and attempt < 100:
                next_number += 1
                new_number = f"SH-{current_year}-{next_number:06d}"
                attempt += 1

            if attempt >= 100:
                # في حالة فشل العثور على رقم فريد، استخدم رقم عشوائي
                random_num = random.randint(100000, 999999)
                new_number = f"SH-{current_year}-{random_num}"

            return new_number

        except Exception as e:
            # في حالة الخطأ، استخدم رقم عشوائي مع الوقت
            timestamp = int(time.time()) % 100000
            return f"SH-{datetime.now().year}-{timestamp:05d}"
        finally:
            session.close()

    def add_specific_link(self, link_type, target_edit):
        """إضافة رابط محدد لنوع مستند معين"""
        try:
            from .add_link_dialog import AddLinkDialog

            existing_url = target_edit.text().strip()
            existing_description = ""

            dialog = AddLinkDialog(self, link_type, existing_url, existing_description)
            if dialog.exec() == QDialog.Accepted:
                link_data = dialog.get_link_data()
                url = link_data['url']
                description = link_data['description']

                # تحديث الحقل
                target_edit.setText(url)
                target_edit.setReadOnly(False)  # السماح بالقراءة للنقر

                # تطبيق تنسيق الرابط التشعبي
                target_edit.setStyleSheet("""
                    QLineEdit {
                        color: #0066cc;
                        text-decoration: underline;
                        background-color: #f0f8ff;
                        border: 2px solid #0066cc;
                        border-radius: 3px;
                        padding: 5px;
                    }
                    QLineEdit:hover {
                        background-color: #e6f3ff;
                    }
                """)

                # إضافة إمكانية النقر لفتح الرابط
                target_edit.mousePressEvent = lambda event: self.open_url(url) if event.button() == Qt.LeftButton else None

                # إضافة tooltip مع الوصف
                if description:
                    target_edit.setToolTip(f"{link_type}\n{description}\nانقر لفتح الرابط")
                else:
                    target_edit.setToolTip(f"{link_type}\nانقر لفتح الرابط")

                # حفظ الرابط في قاعدة البيانات إذا كانت الشحنة محفوظة
                if hasattr(self, 'current_shipment_id') and self.current_shipment_id:
                    self.save_specific_link_to_database(link_type, url, target_edit)

                QMessageBox.information(self, "تم الحفظ", f"تم حفظ رابط {link_type} بنجاح")

        except ImportError:
            QMessageBox.warning(self, "خطأ", "لا يمكن العثور على نافذة إضافة الرابط")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الرابط: {str(e)}")

    def save_specific_link_to_database(self, link_type, url, target_edit):
        """حفظ رابط محدد في قاعدة البيانات"""
        try:
            session = self.db_manager.get_session()

            shipment = session.query(Shipment).filter(
                Shipment.id == self.current_shipment_id
            ).first()

            if shipment:
                # تحديد الحقل المناسب حسب نوع الرابط
                if link_type == "المستندات الأولية":
                    shipment.initial_documents_url = url
                elif link_type == "المستندات (DN)":
                    shipment.dn_documents_url = url
                elif link_type == "المستندات المرسلة للجمارك":
                    shipment.customs_documents_url = url
                elif link_type == "بوليصة الشحن":
                    shipment.bill_of_lading_url = url
                elif link_type == "صور الأصناف":
                    shipment.items_images_url = url
                elif link_type == "مستندات أخرى":
                    shipment.other_documents_url = url

                session.commit()
                print(f"✅ تم حفظ رابط {link_type} في قاعدة البيانات")

            session.close()

        except Exception as e:
            print(f"خطأ في حفظ الرابط في قاعدة البيانات: {str(e)}")

    def add_specific_attachment(self, document_type):
        """إضافة مرفق محدد لنوع مستند معين"""
        try:
            from .attachments_manager_dialog import AttachmentsManagerDialog

            dialog = AttachmentsManagerDialog(self, document_type)
            if dialog.exec() == QDialog.Accepted:
                attachments = dialog.get_attachments()
                if attachments:
                    # حفظ المرفقات في قاعدة البيانات إذا كانت الشحنة محفوظة
                    if hasattr(self, 'current_shipment_id') and self.current_shipment_id:
                        self.save_specific_attachments_to_database(document_type, attachments)

                    # تحديث عداد الزر
                    import json
                    self.update_attachment_button_count(document_type, json.dumps(attachments, ensure_ascii=False))

                    QMessageBox.information(
                        self, "تم الحفظ",
                        f"تم حفظ {len(attachments)} مرفق لـ {document_type}"
                    )

        except ImportError:
            QMessageBox.warning(self, "خطأ", "لا يمكن العثور على نافذة إدارة المرفقات")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المرفق: {str(e)}")

    def save_specific_attachments_to_database(self, document_type, attachments):
        """حفظ مرفقات محددة في قاعدة البيانات"""
        try:
            import json
            session = self.db_manager.get_session()

            shipment = session.query(Shipment).filter(
                Shipment.id == self.current_shipment_id
            ).first()

            if shipment:
                # تحويل قائمة المرفقات إلى JSON
                attachments_json = json.dumps(attachments, ensure_ascii=False)

                # تحديد الحقل المناسب حسب نوع المستند
                if document_type == "المستندات الأولية":
                    shipment.initial_documents_files = attachments_json
                elif document_type == "المستندات (DN)":
                    shipment.dn_documents_files = attachments_json
                elif document_type == "المستندات المرسلة للجمارك":
                    shipment.customs_documents_files = attachments_json
                elif document_type == "بوليصة الشحن":
                    shipment.bill_of_lading_files = attachments_json
                elif document_type == "صور الأصناف":
                    shipment.items_images_files = attachments_json
                elif document_type == "مستندات أخرى":
                    shipment.other_documents_files = attachments_json

                session.commit()
                print(f"✅ تم حفظ مرفقات {document_type} في قاعدة البيانات")

            session.close()

        except Exception as e:
            print(f"خطأ في حفظ المرفقات في قاعدة البيانات: {str(e)}")

    def open_url(self, url):
        """فتح رابط في المتصفح"""
        try:
            import webbrowser
            webbrowser.open(url)
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"لا يمكن فتح الرابط: {str(e)}")

    def add_link(self):
        """إضافة رابط تشعبي للمستندات الإضافية"""
        try:
            from PySide6.QtWidgets import QInputDialog

            # طلب الرابط
            url, ok = QInputDialog.getText(self, "إضافة رابط", "أدخل الرابط:")
            if not ok or not url.strip():
                return

            # طلب اسم الرابط
            name, ok = QInputDialog.getText(self, "اسم الرابط", "أدخل اسم الرابط:")
            if not ok or not name.strip():
                name = url

            self.add_document_to_table(name, "رابط", url, "رابط")

            # حفظ في قاعدة البيانات إذا كانت الشحنة محفوظة
            if hasattr(self, 'current_shipment_id') and self.current_shipment_id:
                self.save_additional_document_to_database(name, "رابط", url, "")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الرابط: {str(e)}")

    def add_attachment(self):
        """إضافة مرفق للمستندات الإضافية"""
        try:
            from .attachments_manager_dialog import AttachmentsManagerDialog

            dialog = AttachmentsManagerDialog(self, "مستندات إضافية")
            if dialog.exec() == QDialog.Accepted:
                attachments = dialog.get_attachments()
                for attachment in attachments:
                    self.add_document_to_table(
                        attachment['name'],
                        "مرفق",
                        attachment['path'],
                        "مرفق"
                    )

                    # حفظ في قاعدة البيانات إذا كانت الشحنة محفوظة
                    if hasattr(self, 'current_shipment_id') and self.current_shipment_id:
                        self.save_additional_document_to_database(
                            attachment['name'], "مرفق", attachment['path'], ""
                        )

        except ImportError:
            QMessageBox.warning(self, "خطأ", "لا يمكن العثور على نافذة إدارة المرفقات")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المرفق: {str(e)}")

    def save_additional_document_to_database(self, name, doc_type, path, description):
        """حفظ مستند إضافي واحد في قاعدة البيانات"""
        try:
            from ...database.models import ShipmentDocument
            session = self.db_manager.get_session()

            document = ShipmentDocument(
                shipment_id=self.current_shipment_id,
                document_name=name,
                document_type=doc_type,
                description=description
            )

            # تحديد ما إذا كان رابط أم ملف
            if path.startswith('http'):
                document.document_url = path
            else:
                document.file_path = path

            session.add(document)
            session.commit()
            session.close()

            print(f"✅ تم حفظ المستند الإضافي: {name}")

        except Exception as e:
            print(f"خطأ في حفظ المستند الإضافي: {str(e)}")

    def add_document(self):
        """إضافة مستند عام"""
        try:
            from PySide6.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار مستند",
                "",
                "جميع الملفات (*.*)"
            )

            if file_path:
                import os
                file_name = os.path.basename(file_path)
                self.add_document_to_table(file_name, "مستند", file_path, "ملف")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المستند: {str(e)}")

    def remove_document(self):
        """حذف مستند من الجدول"""
        try:
            current_row = self.documents_table.currentRow()
            if current_row >= 0:
                reply = QMessageBox.question(
                    self, "تأكيد الحذف",
                    "هل أنت متأكد من حذف هذا المستند؟",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    # الحصول على بيانات المستند قبل الحذف
                    name_item = self.documents_table.item(current_row, 0)
                    path_item = self.documents_table.item(current_row, 2)

                    # حذف من قاعدة البيانات إذا كانت الشحنة محفوظة
                    if (hasattr(self, 'current_shipment_id') and self.current_shipment_id and
                        name_item and path_item):
                        self.remove_additional_document_from_database(
                            name_item.text(), path_item.text()
                        )

                    # حذف من الجدول
                    self.documents_table.removeRow(current_row)

                    # تحديث عداد المستندات
                    self.update_documents_count()
            else:
                QMessageBox.warning(self, "تحذير", "يجب اختيار مستند أولاً")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف المستند: {str(e)}")

    def remove_additional_document_from_database(self, name, path):
        """حذف مستند إضافي من قاعدة البيانات"""
        try:
            from ...database.models import ShipmentDocument
            session = self.db_manager.get_session()

            # البحث عن المستند وحذفه
            query = session.query(ShipmentDocument).filter(
                ShipmentDocument.shipment_id == self.current_shipment_id,
                ShipmentDocument.document_name == name
            )

            # إضافة شرط المسار (رابط أم ملف)
            if path.startswith('http'):
                query = query.filter(ShipmentDocument.document_url == path)
            else:
                query = query.filter(ShipmentDocument.file_path == path)

            document = query.first()
            if document:
                session.delete(document)
                session.commit()
                print(f"✅ تم حذف المستند من قاعدة البيانات: {name}")

            session.close()

        except Exception as e:
            print(f"خطأ في حذف المستند من قاعدة البيانات: {str(e)}")

    def open_document(self):
        """فتح المستند المحدد"""
        try:
            current_row = self.documents_table.currentRow()
            if current_row >= 0:
                path_item = self.documents_table.item(current_row, 2)
                if path_item:
                    path = path_item.text()
                    if path.startswith('http'):
                        self.open_url(path)
                    else:
                        import os
                        os.startfile(path)
            else:
                QMessageBox.warning(self, "تحذير", "يجب اختيار مستند أولاً")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح المستند: {str(e)}")

    def add_document_to_table(self, name, doc_type, path, category):
        """إضافة مستند إلى الجدول"""
        try:
            row = self.documents_table.rowCount()
            self.documents_table.insertRow(row)

            self.documents_table.setItem(row, 0, QTableWidgetItem(name))
            self.documents_table.setItem(row, 1, QTableWidgetItem(doc_type))
            self.documents_table.setItem(row, 2, QTableWidgetItem(path))
            self.documents_table.setItem(row, 3, QTableWidgetItem(category))
            self.documents_table.setItem(row, 4, QTableWidgetItem("نشط"))

            # تحديث عداد المستندات
            self.update_documents_count()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المستند للجدول: {str(e)}")

    def update_documents_count(self):
        """تحديث عداد المستندات الإضافية"""
        try:
            if hasattr(self, 'documents_table') and hasattr(self, 'total_documents_label'):
                count = self.documents_table.rowCount()
                self.total_documents_label.setText(f"إجمالي المستندات: {count}")
        except Exception as e:
            print(f"خطأ في تحديث عداد المستندات: {str(e)}")

    def load_currencies(self):
        """تحميل العملات من قاعدة البيانات"""
        session = self.db_manager.get_session()
        try:
            from ...database.models import Currency
            currencies = session.query(Currency).filter(Currency.is_active == True).all()

            self.currency_combo.clear()

            if currencies:
                for currency in currencies:
                    display_text = f"{currency.name} ({currency.code})"
                    if currency.symbol:
                        display_text = f"{currency.symbol} - {display_text}"
                    self.currency_combo.addItem(display_text, currency.id)

                # تحديد العملة الافتراضية من إعدادات النظام
                default_currency = self.db_manager.get_setting("default_currency", "SAR")
                for i in range(self.currency_combo.count()):
                    if default_currency in self.currency_combo.itemText(i):
                        self.currency_combo.setCurrentIndex(i)
                        break
            else:
                # في حالة عدم وجود عملات، إضافة عملات افتراضية
                default_currencies = [
                    ("الريال السعودي", "SAR", "ر.س"),
                    ("الدولار الأمريكي", "USD", "$"),
                    ("اليورو", "EUR", "€"),
                    ("الدرهم الإماراتي", "AED", "د.إ"),
                    ("الدينار الكويتي", "KWD", "د.ك"),
                    ("الريال القطري", "QAR", "ر.ق"),
                    ("الدينار البحريني", "BHD", "د.ب")
                ]

                for name, code, symbol in default_currencies:
                    display_text = f"{symbol} - {name} ({code})"
                    self.currency_combo.addItem(display_text, code)

                # تحديد الريال السعودي كافتراضي
                for i in range(self.currency_combo.count()):
                    if "SAR" in self.currency_combo.itemText(i):
                        self.currency_combo.setCurrentIndex(i)
                        break

        except Exception as e:
            print(f"خطأ في تحميل العملات: {str(e)}")
            # في حالة الخطأ، إضافة عملات افتراضية
            self.currency_combo.clear()
            self.currency_combo.addItems(["ر.س - الريال السعودي (SAR)", "$ - الدولار الأمريكي (USD)", "€ - اليورو (EUR)"])
        finally:
            session.close()

    def load_currencies_for_supplier(self):
        """تحميل العملات لحقل عملة المورد"""
        session = self.db_manager.get_session()
        try:
            from ...database.models import Currency
            currencies = session.query(Currency).filter(Currency.is_active == True).all()

            self.supplier_currency_combo.clear()

            if currencies:
                for currency in currencies:
                    display_text = f"{currency.name} ({currency.code})"
                    if currency.symbol:
                        display_text = f"{currency.symbol} - {display_text}"
                    self.supplier_currency_combo.addItem(display_text, currency.id)
            else:
                # في حالة عدم وجود عملات، إضافة عملات افتراضية
                default_currencies = [
                    ("الريال السعودي", "SAR", "ر.س"),
                    ("الدولار الأمريكي", "USD", "$"),
                    ("اليورو", "EUR", "€")
                ]
                for name, code, symbol in default_currencies:
                    display_text = f"{symbol} - {name} ({code})"
                    self.supplier_currency_combo.addItem(display_text, code)

        except Exception as e:
            print(f"خطأ في تحميل عملات المورد: {str(e)}")
            self.supplier_currency_combo.clear()
            self.supplier_currency_combo.addItems(["ر.س - الريال السعودي (SAR)", "$ - الدولار الأمريكي (USD)"])
        finally:
            session.close()

    def load_shipping_companies(self):
        """تحميل شركات الشحن من الموردين"""
        session = self.db_manager.get_session()
        try:
            from ...database.models import Supplier
            shipping_companies = session.query(Supplier).filter(
                Supplier.supplier_type == "شركة شحن",
                Supplier.is_active == True
            ).all()

            self.shipping_company_combo.clear()
            self.shipping_company_combo.addItem("-- اختر شركة الشحن --", None)

            if shipping_companies:
                for company in shipping_companies:
                    display_text = company.name
                    if company.name_en:
                        display_text = f"{company.name} ({company.name_en})"
                    self.shipping_company_combo.addItem(display_text, company.id)
            else:
                # في حالة عدم وجود شركات شحن
                self.shipping_company_combo.addItem("لا توجد شركات شحن مسجلة", None)

        except Exception as e:
            print(f"خطأ في تحميل شركات الشحن: {str(e)}")
            self.shipping_company_combo.clear()
            self.shipping_company_combo.addItem("خطأ في تحميل البيانات", None)
        finally:
            session.close()

    def calculate_goods_value_usd(self):
        """حساب قيمة البضاعة بالدولار"""
        try:
            goods_value = float(self.goods_value_edit.text() or "0")
            exchange_rate = float(self.exchange_rate_edit.text() or "1")

            if exchange_rate > 0:
                goods_value_usd = goods_value / exchange_rate
                self.goods_value_usd_edit.setText(f"{goods_value_usd:.2f}")
            else:
                self.goods_value_usd_edit.setText("0.00")
        except (ValueError, ZeroDivisionError):
            self.goods_value_usd_edit.setText("0.00")

    def toggle_supplier_shipping_fields(self, checked):
        """إظهار/إخفاء حقول أجور الشحن للمورد"""
        self.supplier_shipping_label1.setVisible(checked)
        self.supplier_shipping_fees_edit.setVisible(checked)
        self.supplier_currency_label.setVisible(checked)
        self.supplier_currency_combo.setVisible(checked)
        self.supplier_local_fees_label.setVisible(checked)
        self.supplier_local_fees_edit.setVisible(checked)

        # إذا تم تحديد المورد، إخفاء حقول شركة الشحن
        if checked:
            self.shipping_company_label.setVisible(False)
            self.shipping_company_combo.setVisible(False)
            self.shipping_company_fees_label.setVisible(False)
            self.shipping_company_fees_edit.setVisible(False)
            # مسح قيم شركة الشحن
            self.shipping_company_combo.setCurrentIndex(0)
            self.shipping_company_fees_edit.setText("0.00")
        else:
            # إذا تم إلغاء التحديد، امسح قيم المورد
            self.supplier_shipping_fees_edit.setText("0.00")
            self.supplier_local_fees_edit.setText("0.00")

    def toggle_shipping_company_fields(self, checked):
        """إظهار/إخفاء حقول شركة الشحن"""
        self.shipping_company_label.setVisible(checked)
        self.shipping_company_combo.setVisible(checked)
        self.shipping_company_fees_label.setVisible(checked)
        self.shipping_company_fees_edit.setVisible(checked)

        # إذا تم تحديد شركة الشحن، إخفاء حقول المورد
        if checked:
            self.supplier_shipping_label1.setVisible(False)
            self.supplier_shipping_fees_edit.setVisible(False)
            self.supplier_currency_label.setVisible(False)
            self.supplier_currency_combo.setVisible(False)
            self.supplier_local_fees_label.setVisible(False)
            self.supplier_local_fees_edit.setVisible(False)
            # مسح قيم المورد
            self.supplier_shipping_fees_edit.setText("0.00")
            self.supplier_local_fees_edit.setText("0.00")
        else:
            # إذا تم إلغاء التحديد، امسح قيم شركة الشحن
            self.shipping_company_combo.setCurrentIndex(0)
            self.shipping_company_fees_edit.setText("0.00")

    def generate_new_shipment_number(self):
        """توليد رقم شحنة جديد وتحديث الحقل"""
        try:
            new_number = self.generate_unique_shipment_number()
            self.shipment_number_edit.setText(new_number)
            QMessageBox.information(
                self,
                "رقم جديد",
                f"✅ تم توليد رقم شحنة جديد: {new_number}"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"خطأ في توليد رقم الشحنة: {str(e)}"
            )

    def get_shipment_items(self):
        """الحصول على أصناف الشحنة من الجدول"""
        def safe_float(text):
            try:
                if not text or not text.strip():
                    return 0.0
                # استبدال الفاصلة العربية بالنقطة الإنجليزية
                clean_text = text.replace('،', '.').replace(',', '.')
                return float(clean_text)
            except (ValueError, AttributeError):
                return 0.0

        items = []
        try:
            for row in range(self.items_table.rowCount()):
                item_id = self.items_table.item(row, 0).data(Qt.UserRole)
                purchase_order_item_id = self.items_table.item(row, 0).data(Qt.UserRole + 1)  # معرف صنف طلب الشراء
                if item_id:
                    quantity = safe_float(self.items_table.item(row, 2).text())
                    unit_price = safe_float(self.items_table.item(row, 3).text())
                    total_price = safe_float(self.items_table.item(row, 6).text())  # العمود 6 الآن

                    # الحصول على التواريخ
                    production_date_text = self.items_table.item(row, 4).text() if self.items_table.item(row, 4) else ""
                    expiry_date_text = self.items_table.item(row, 5).text() if self.items_table.item(row, 5) else ""

                    # تحويل التواريخ
                    production_date = None
                    expiry_date = None

                    if production_date_text and production_date_text.strip():
                        try:
                            from datetime import datetime
                            production_date = datetime.strptime(production_date_text, "%Y-%m-%d")
                        except:
                            production_date = None

                    if expiry_date_text and expiry_date_text.strip():
                        try:
                            from datetime import datetime
                            expiry_date = datetime.strptime(expiry_date_text, "%Y-%m-%d")
                        except:
                            expiry_date = None

                    items.append({
                        'item_id': item_id,
                        'purchase_order_item_id': purchase_order_item_id,
                        'quantity': quantity,
                        'unit_price': unit_price,
                        'total_price': total_price,
                        'production_date': production_date,
                        'expiry_date': expiry_date
                    })
            return items
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في قراءة أصناف الشحنة: {str(e)}")
            return None

    def update_purchase_order_quantities(self, session, purchase_order_item_id, shipped_quantity):
        """تحديث كميات طلب الشراء عند الشحن"""
        try:
            from src.database.models import PurchaseOrderItem

            # البحث عن صنف طلب الشراء
            po_item = session.query(PurchaseOrderItem).filter(
                PurchaseOrderItem.id == purchase_order_item_id
            ).first()

            if po_item:
                # تحديث الكمية المسلمة
                po_item.delivered_quantity = (po_item.delivered_quantity or 0) + shipped_quantity

                # تحديث الكمية المتبقية
                po_item.remaining_quantity = po_item.quantity - po_item.delivered_quantity

                # التأكد من أن الكمية المتبقية لا تقل عن الصفر
                if po_item.remaining_quantity < 0:
                    po_item.remaining_quantity = 0

                print(f"✅ تم تحديث كميات طلب الشراء:")
                print(f"   - الصنف ID: {purchase_order_item_id}")
                print(f"   - الكمية المشحونة: {shipped_quantity}")
                print(f"   - الكمية المسلمة الإجمالية: {po_item.delivered_quantity}")
                print(f"   - الكمية المتبقية: {po_item.remaining_quantity}")

        except Exception as e:
            print(f"❌ خطأ في تحديث كميات طلب الشراء: {str(e)}")

    def restore_purchase_order_quantities(self, session, shipment_id):
        """استعادة كميات طلب الشراء عند التعديل"""
        try:
            from src.database.models import ShipmentItem, PurchaseOrderItem

            # البحث عن أصناف الشحنة القديمة المرتبطة بطلبات شراء
            old_shipment_items = session.query(ShipmentItem).filter(
                ShipmentItem.shipment_id == shipment_id,
                ShipmentItem.purchase_order_item_id.isnot(None)
            ).all()

            # استعادة الكميات
            for old_item in old_shipment_items:
                po_item = session.query(PurchaseOrderItem).filter(
                    PurchaseOrderItem.id == old_item.purchase_order_item_id
                ).first()

                if po_item:
                    # استعادة الكمية المسلمة
                    po_item.delivered_quantity = (po_item.delivered_quantity or 0) - old_item.quantity

                    # التأكد من أن الكمية المسلمة لا تقل عن الصفر
                    if po_item.delivered_quantity < 0:
                        po_item.delivered_quantity = 0

                    # إعادة حساب الكمية المتبقية
                    po_item.remaining_quantity = po_item.quantity - po_item.delivered_quantity

                    print(f"🔄 تم استعادة كميات طلب الشراء:")
                    print(f"   - الصنف ID: {old_item.purchase_order_item_id}")
                    print(f"   - الكمية المستعادة: {old_item.quantity}")
                    print(f"   - الكمية المسلمة الجديدة: {po_item.delivered_quantity}")
                    print(f"   - الكمية المتبقية الجديدة: {po_item.remaining_quantity}")

        except Exception as e:
            print(f"❌ خطأ في استعادة كميات طلب الشراء: {str(e)}")

    def validate_purchase_order_quantities(self, session):
        """التحقق من صحة كميات طلب الشراء قبل الحفظ"""
        try:
            from src.database.models import PurchaseOrderItem

            validation_errors = []

            for row in range(self.items_table.rowCount()):
                # الحصول على معرف صنف طلب الشراء
                po_item_id = self.items_table.item(row, 0).data(Qt.UserRole + 1)

                if po_item_id:  # إذا كان الصنف مرتبط بطلب شراء
                    # الحصول على الكمية المطلوب شحنها
                    quantity_text = self.items_table.item(row, 2).text()
                    try:
                        ship_quantity = float(quantity_text)
                    except (ValueError, TypeError):
                        ship_quantity = 0.0

                    # الحصول على بيانات طلب الشراء
                    po_item = session.query(PurchaseOrderItem).filter(
                        PurchaseOrderItem.id == po_item_id
                    ).first()

                    if po_item:
                        item_name = self.items_table.item(row, 1).text()
                        available_quantity = po_item.remaining_quantity

                        # التحقق من عدم تجاوز الكمية المتاحة
                        if ship_quantity > available_quantity:
                            validation_errors.append(
                                f"الصنف '{item_name}': "
                                f"الكمية المطلوب شحنها ({ship_quantity}) "
                                f"تتجاوز الكمية المتاحة ({available_quantity})"
                            )

                        print(f"🔍 التحقق من الكمية:")
                        print(f"   - الصنف: {item_name}")
                        print(f"   - الكمية المطلوب شحنها: {ship_quantity}")
                        print(f"   - الكمية المتاحة: {available_quantity}")
                        print(f"   - النتيجة: {'✅ صحيح' if ship_quantity <= available_quantity else '❌ تجاوز الحد'}")

            return validation_errors

        except Exception as e:
            print(f"❌ خطأ في التحقق من كميات طلب الشراء: {str(e)}")
            return [f"خطأ في التحقق من الكميات: {str(e)}"]

    def save_shipment(self):
        """حفظ الشحنة بطريقة مباشرة وبسيطة - بدون threading"""
        # تجنب الحفظ المتداخل
        if self._is_saving:
            print("عملية الحفظ قيد التنفيذ بالفعل، تم تجاهل الطلب")
            return False

        if not self.validate_data():
            return False

        # الحصول على أصناف الشحنة
        shipment_items = self.get_shipment_items()
        if shipment_items is None:
            return False

        self._is_saving = True  # تعيين حالة الحفظ

        # إنشاء progress dialog بسيط
        progress = QProgressDialog("جاري حفظ الشحنة...", None, 0, 100, self)
        progress.setWindowTitle("حفظ الشحنة")
        progress.setModal(True)
        progress.setCancelButton(None)
        progress.setMinimumDuration(0)
        progress.show()

        # معالجة الأحداث لإظهار الـ progress
        QApplication.processEvents()

        try:
            # تحضير البيانات للحفظ
            progress.setValue(20)
            progress.setLabelText("تحضير البيانات...")
            QApplication.processEvents()

            shipment_data = self.prepare_shipment_data()
            containers_data = self.prepare_containers_data()
            documents_data = self.prepare_documents_data()

            # حفظ مباشر بدون threading
            progress.setValue(40)
            progress.setLabelText("حفظ البيانات...")
            QApplication.processEvents()

            success = self.save_shipment_direct(shipment_data, shipment_items, containers_data, documents_data)

            progress.setValue(100)
            progress.close()

            self._is_saving = False

            if success:
                QMessageBox.information(self, "نجح", "تم حفظ الشحنة بنجاح")
                return True
            else:
                return False

        except Exception as e:
            progress.close()
            self._is_saving = False
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الشحنة: {str(e)}")
            return False

    def save_shipment_direct(self, shipment_data, shipment_items, containers_data, documents_data):
        """حفظ الشحنة مباشرة بدون threading"""
        session = None
        try:
            session = self.db_manager.get_session()

            # حفظ أو تحديث الشحنة الأساسية
            if self.is_edit_mode and self.current_shipment_id:
                # وضع التعديل
                shipment = session.get(Shipment, self.current_shipment_id)
                if not shipment:
                    QMessageBox.critical(self, "خطأ", "لم يتم العثور على الشحنة للتعديل")
                    return False

                # تحديث البيانات
                for key, value in shipment_data.items():
                    if hasattr(shipment, key) and key != 'id':
                        setattr(shipment, key, value)
            else:
                # وضع الإنشاء الجديد
                shipment = Shipment(**shipment_data)
                session.add(shipment)

            session.flush()  # للحصول على ID

            # حفظ أصناف الشحنة
            if self.is_edit_mode:
                # حذف الأصناف القديمة
                session.query(ShipmentItem).filter(ShipmentItem.shipment_id == shipment.id).delete()

            for item_data in shipment_items:
                item_data['shipment_id'] = shipment.id
                item = ShipmentItem(**item_data)
                session.add(item)

            # حفظ الحاويات
            if self.is_edit_mode:
                session.query(Container).filter(Container.shipment_id == shipment.id).delete()

            for container_data in containers_data:
                container_data['shipment_id'] = shipment.id
                container = Container(**container_data)
                session.add(container)

            # حفظ المستندات
            if self.is_edit_mode:
                session.query(ShipmentDocument).filter(ShipmentDocument.shipment_id == shipment.id).delete()

            for doc_data in documents_data:
                doc_data['shipment_id'] = shipment.id
                document = ShipmentDocument(**doc_data)
                session.add(document)

            # الحفظ النهائي
            session.commit()

            # تحديث واجهة المستخدم
            if shipment and hasattr(shipment, 'id'):
                self.current_shipment_id = shipment.id
                self.edit_button.setEnabled(True)
                self.delete_button.setEnabled(True)
                self.shipment_saved.emit(shipment.id)

            return True

        except Exception as e:
            if session:
                try:
                    session.rollback()
                except:
                    pass
            QMessageBox.critical(self, "خطأ في الحفظ", f"فشل في حفظ الشحنة: {str(e)}")
            return False
        finally:
            if session:
                try:
                    session.close()
                except:
                    pass

    def prepare_shipment_data(self):
        """تحضير بيانات الشحنة للحفظ"""
        data = {}

        # البيانات الأساسية
        data['shipment_number'] = self.shipment_number_edit.text().strip()
        data['supplier_id'] = self.supplier_edit.property("supplier_id")
        data['shipment_date'] = self.shipment_date_edit.date().toPython()
        data['shipment_status'] = self.shipment_status_combo.currentText()
        data['clearance_status'] = self.clearance_status_combo.currentText()
        data['notes'] = self.notes_edit.toPlainText().strip()

        # بيانات الشحن
        if hasattr(self, 'smart_shipping_company_widget'):
            data['shipping_company'] = self.smart_shipping_company_widget.get_company_name()
        if hasattr(self, 'port_of_loading_edit'):
            data['port_of_loading'] = self.port_of_loading_edit.text().strip()
        if hasattr(self, 'port_of_discharge_edit'):
            data['port_of_discharge'] = self.port_of_discharge_edit.text().strip()
        if hasattr(self, 'tracking_number_edit'):
            data['tracking_number'] = self.tracking_number_edit.text().strip()
        if hasattr(self, 'bill_of_lading_edit'):
            data['bill_of_lading'] = self.bill_of_lading_edit.text().strip()

        # التواريخ
        if hasattr(self, 'departure_date_edit'):
            data['departure_date'] = self.departure_date_edit.get_date()
        if hasattr(self, 'arrival_date_edit'):
            data['arrival_date'] = self.arrival_date_edit.get_date()
        if hasattr(self, 'clearance_date_edit'):
            data['clearance_date'] = self.clearance_date_edit.get_date()
        if hasattr(self, 'delivery_date_edit'):
            data['delivery_date'] = self.delivery_date_edit.get_date()

        return data

    def prepare_containers_data(self):
        """تحضير بيانات الحاويات للحفظ"""
        containers = []

        if hasattr(self, 'containers_table'):
            for row in range(self.containers_table.rowCount()):
                container_data = {
                    'container_number': self.containers_table.item(row, 0).text() if self.containers_table.item(row, 0) else "",
                    'container_type': self.containers_table.item(row, 1).text() if self.containers_table.item(row, 1) else "",
                    'container_size': self.containers_table.item(row, 2).text() if self.containers_table.item(row, 2) else "",
                    'weight_empty': float(self.containers_table.item(row, 3).text()) if self.containers_table.item(row, 3) and self.containers_table.item(row, 3).text() else 0.0,
                    'weight_loaded': float(self.containers_table.item(row, 4).text()) if self.containers_table.item(row, 4) and self.containers_table.item(row, 4).text() else 0.0,
                    'status': self.containers_table.item(row, 5).text() if self.containers_table.item(row, 5) else "",
                    'notes': self.containers_table.item(row, 7).text() if self.containers_table.item(row, 7) else ""
                }
                containers.append(container_data)

        return containers

    def prepare_documents_data(self):
        """تحضير بيانات المستندات للحفظ"""
        documents = []

        if hasattr(self, 'documents_table'):
            for row in range(self.documents_table.rowCount()):
                name_item = self.documents_table.item(row, 0)
                type_item = self.documents_table.item(row, 1)
                path_item = self.documents_table.item(row, 2)
                notes_item = self.documents_table.item(row, 4)

                if name_item and type_item and path_item:
                    doc_data = {
                        'document_name': name_item.text(),
                        'document_type': type_item.text(),
                        'description': notes_item.text() if notes_item else ""
                    }

                    # تحديد ما إذا كان رابط أم ملف
                    path_text = path_item.text()
                    if path_text.startswith('http'):
                        doc_data['document_url'] = path_text
                    else:
                        doc_data['file_path'] = path_text

                    documents.append(doc_data)

        return documents

    def start_async_save(self, shipment_data, shipment_items, containers_data, documents_data):
        """بدء عملية الحفظ غير المتزامنة - تم تعطيلها لصالح الحفظ المباشر"""
        # هذه الدالة لم تعد مستخدمة - تم استبدالها بـ save_shipment_direct
        print("⚠️ تم استدعاء start_async_save المعطلة - يجب استخدام save_shipment_direct")
        pass

    def on_save_progress_updated(self, progress, message):
        """تحديث شريط التقدم - معطلة"""
        pass

    def on_save_completed(self, success, message, shipment_data):
        """معالجة اكتمال عملية الحفظ - معطلة"""
        pass

    def on_save_error(self, error_message):
        """معالجة خطأ في الحفظ - معطلة"""
        pass

    def cancel_save(self):
        """إلغاء عملية الحفظ - معطلة"""
        pass

    def cleanup_save_thread(self):
        """تنظيف الـ thread والـ worker - معطلة"""
        pass

    # تم حذف الكود القديم المعقد واستبداله بنظام الحفظ غير المتزامن الجديد

    def save_documents_data(self, session, shipment):
        """حفظ بيانات المستندات (الروابط والمرفقات)"""
        try:
            # حفظ روابط المستندات المحددة
            if hasattr(self, 'initial_docs_edit'):
                shipment.initial_documents_url = self.initial_docs_edit.text().strip()
            if hasattr(self, 'dn_docs_edit'):
                shipment.dn_documents_url = self.dn_docs_edit.text().strip()
            if hasattr(self, 'customs_docs_edit'):
                shipment.customs_documents_url = self.customs_docs_edit.text().strip()
            if hasattr(self, 'bill_lading_edit'):
                shipment.bill_of_lading_url = self.bill_lading_edit.text().strip()
            if hasattr(self, 'items_images_edit'):
                shipment.items_images_url = self.items_images_edit.text().strip()
            if hasattr(self, 'other_docs_edit'):
                shipment.other_documents_url = self.other_docs_edit.text().strip()

            # حفظ المستندات الإضافية من الجدول
            self.save_additional_documents(session, shipment)

        except Exception as e:
            print(f"خطأ في حفظ بيانات المستندات: {str(e)}")

    def save_additional_documents(self, session, shipment):
        """حفظ المستندات الإضافية من الجدول"""
        try:
            from ...database.models import ShipmentDocument

            # حذف المستندات الإضافية الموجودة أولاً
            session.query(ShipmentDocument).filter(
                ShipmentDocument.shipment_id == shipment.id
            ).delete()

            # حفظ المستندات الجديدة من الجدول
            if hasattr(self, 'documents_table'):
                for row in range(self.documents_table.rowCount()):
                    try:
                        name_item = self.documents_table.item(row, 0)
                        type_item = self.documents_table.item(row, 1)
                        path_item = self.documents_table.item(row, 2)
                        date_item = self.documents_table.item(row, 3)
                        notes_item = self.documents_table.item(row, 4)

                        if name_item and type_item and path_item:
                            document = ShipmentDocument(
                                shipment_id=shipment.id,
                                document_name=name_item.text(),
                                document_type=type_item.text(),
                                description=notes_item.text() if notes_item else ""
                            )

                            # تحديد ما إذا كان رابط أم ملف
                            path_text = path_item.text()
                            if path_text.startswith('http'):
                                document.document_url = path_text
                            else:
                                document.file_path = path_text

                            session.add(document)

                    except Exception as e:
                        print(f"خطأ في حفظ المستند في الصف {row}: {str(e)}")
                        continue

            print(f"✅ تم حفظ المستندات الإضافية للشحنة {shipment.id}")

        except Exception as e:
            print(f"خطأ في حفظ المستندات الإضافية: {str(e)}")

    def save_containers(self, session, shipment_id):
        """حفظ الحاويات"""
        def safe_float(text):
            try:
                if not text or not text.strip():
                    return 0.0
                # استبدال الفاصلة العربية بالنقطة الإنجليزية
                clean_text = text.replace('،', '.').replace(',', '.')
                return float(clean_text)
            except (ValueError, AttributeError):
                return 0.0

        try:
            for row in range(self.containers_table.rowCount()):
                container_number = self.containers_table.item(row, 0).text()
                container_type = self.containers_table.item(row, 1).text()
                container_size = self.containers_table.item(row, 2).text()
                empty_weight = safe_float(self.containers_table.item(row, 3).text())
                loaded_weight = safe_float(self.containers_table.item(row, 4).text())
                status = self.containers_table.item(row, 5).text()
                notes = self.containers_table.item(row, 7).text() if self.containers_table.item(row, 7) else ""

                container = Container(
                    shipment_id=shipment_id,
                    container_number=container_number,
                    container_type=container_type,
                    container_size=container_size,
                    weight_empty=empty_weight,
                    weight_loaded=loaded_weight,
                    status=status,
                    notes=notes
                )
                session.add(container)
        except Exception as e:
            print(f"خطأ في حفظ الحاويات: {str(e)}")
    
    def edit_shipment(self):
        """تعديل الشحنة"""
        if not self.is_edit_mode:
            QMessageBox.warning(self, "تحذير", "لا توجد شحنة محملة للتعديل")
            return

        QMessageBox.information(self, "تعديل", "✏️ يمكنك الآن تعديل بيانات الشحنة وحفظها")

    def delete_shipment(self):
        """حذف الشحنة"""
        if not self.is_edit_mode or not self.current_shipment_id:
            QMessageBox.warning(self, "تحذير", "لا توجد شحنة محملة للحذف")
            return

        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "⚠️ هل أنت متأكد من حذف هذه الشحنة؟\n\nسيتم حذف جميع البيانات المرتبطة بها:\n• الأصناف\n• الحاويات\n• المستندات\n\nهذا الإجراء لا يمكن التراجع عنه!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        session = self.db_manager.get_session()
        try:
            # البحث عن الشحنة
            shipment = session.query(Shipment).filter(
                Shipment.id == self.current_shipment_id
            ).first()

            if not shipment:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الشحنة المطلوبة")
                return

            # استعادة كميات طلبات الشراء قبل الحذف
            self.restore_purchase_order_quantities(session, self.current_shipment_id)

            # حذف البيانات المرتبطة يدوياً
            from ...database.models import ShipmentItem, Container, ShipmentDocument

            # حذف أصناف الشحنة
            session.query(ShipmentItem).filter(
                ShipmentItem.shipment_id == self.current_shipment_id
            ).delete()

            # حذف حاويات الشحنة
            session.query(Container).filter(
                Container.shipment_id == self.current_shipment_id
            ).delete()

            # حذف مستندات الشحنة
            session.query(ShipmentDocument).filter(
                ShipmentDocument.shipment_id == self.current_shipment_id
            ).delete()

            # حذف الشحنة نفسها
            session.delete(shipment)
            session.commit()

            QMessageBox.information(
                self,
                "تم الحذف",
                f"✅ تم حذف الشحنة رقم {shipment.shipment_number} بنجاح"
            )

            # إرسال إشارة الحذف
            self.shipment_deleted.emit(self.current_shipment_id)

            # إغلاق النافذة
            self.accept()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(
                self,
                "خطأ في الحذف",
                f"حدث خطأ أثناء حذف الشحنة:\n{str(e)}"
            )
        finally:
            session.close()

    def load_shipment_data(self):
        """تحميل بيانات الشحنة للتعديل مع حماية شاملة من تداخل الجلسات"""
        if not self.current_shipment_id:
            return

        # التأكد من عدم وجود عمليات حفظ نشطة
        if self._is_saving:
            print("⚠️ عملية حفظ نشطة، تم تأجيل التحميل")
            return

        # إنشاء جلسة منفصلة ومعزولة للتحميل
        session = None
        try:
            # إنشاء مدير قاعدة بيانات منفصل لتجنب التداخل
            isolated_db_manager = DatabaseManager()
            session = isolated_db_manager.get_session()

            # تحميل بيانات الشحنة
            from ...database.models import Supplier
            shipment = session.query(Shipment).filter(
                Shipment.id == self.current_shipment_id
            ).first()

            if not shipment:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الشحنة المطلوبة")
                return

            # ملء البيانات الأساسية
            self.shipment_number_edit.setText(shipment.shipment_number or "")

            # تحميل تاريخ الشحنة - الحفاظ على التاريخ الأصلي إذا كان موجوداً
            if hasattr(self, 'shipment_date_edit'):
                try:
                    # أولوية لتاريخ الشحنة المحفوظ في قاعدة البيانات
                    date_to_use = None
                    if shipment.shipment_date:
                        date_to_use = shipment.shipment_date
                    elif shipment.created_at:
                        date_to_use = shipment.created_at
                    else:
                        # في حالة عدم وجود أي تاريخ، استخدم التاريخ الحالي
                        date_to_use = None

                    if date_to_use:
                        if hasattr(date_to_use, 'year'):
                            # إذا كان التاريخ من نوع datetime
                            self.shipment_date_edit.setDate(QDate(
                                date_to_use.year,
                                date_to_use.month,
                                date_to_use.day
                            ))
                        elif hasattr(date_to_use, 'date'):
                            # إذا كان التاريخ من نوع date
                            date_obj = date_to_use.date() if hasattr(date_to_use, 'date') else date_to_use
                            self.shipment_date_edit.setDate(QDate(
                                date_obj.year,
                                date_obj.month,
                                date_obj.day
                            ))
                        else:
                            self.shipment_date_edit.setDate(QDate.currentDate())
                    else:
                        self.shipment_date_edit.setDate(QDate.currentDate())
                except Exception as e:
                    print(f"خطأ في تحميل التاريخ: {e}")
                    self.shipment_date_edit.setDate(QDate.currentDate())

            # ملء بيانات المورد
            if shipment.supplier:
                self.supplier_edit.setText(shipment.supplier.name)
                self.selected_supplier_id = shipment.supplier_id
                self.supplier_edit.setProperty("supplier_id", shipment.supplier_id)

            # ملء الحقول الأخرى
            if hasattr(self, 'shipment_status_combo'):
                status_index = self.shipment_status_combo.findText(shipment.shipment_status or "تحت الطلب")
                if status_index >= 0:
                    self.shipment_status_combo.setCurrentIndex(status_index)

            if hasattr(self, 'clearance_status_combo'):
                clearance_index = self.clearance_status_combo.findText(shipment.clearance_status or "بدون الافراج")
                if clearance_index >= 0:
                    self.clearance_status_combo.setCurrentIndex(clearance_index)

            if hasattr(self, 'notes_edit'):
                self.notes_edit.setPlainText(shipment.notes or "")

            if hasattr(self, 'tracking_number_edit'):
                self.tracking_number_edit.setText(shipment.tracking_number or "")

            if hasattr(self, 'bill_of_lading_edit'):
                self.bill_of_lading_edit.setText(shipment.bill_of_lading or "")

            if hasattr(self, 'supplier_invoice_edit'):
                self.supplier_invoice_edit.setText(shipment.supplier_invoice_number or "")

            # تحديد العملة المحفوظة
            if hasattr(self, 'currency_combo') and shipment.currency_id:
                for i in range(self.currency_combo.count()):
                    if self.currency_combo.itemData(i) == shipment.currency_id:
                        self.currency_combo.setCurrentIndex(i)
                        break

            # ملء الحقول الإضافية (التي تم إضافتها مؤخراً)
            if hasattr(self, 'shipping_policy_edit'):
                self.shipping_policy_edit.setText(shipment.shipping_policy or "")
            if hasattr(self, 'container_number_edit'):
                self.container_number_edit.setText(shipment.container_number or "")
            if hasattr(self, 'smart_shipping_company_widget'):
                # تعطيل التعبئة التلقائية في وضع التعديل
                self.smart_shipping_company_widget.set_auto_fill_enabled(False)
                # تعيين اسم الشركة بدون تحقق تلقائي
                self.smart_shipping_company_widget.set_company_name(shipment.shipping_company or "", auto_validate=False)
            elif hasattr(self, 'shipping_company_edit'):
                self.shipping_company_edit.setText(shipment.shipping_company or "")

            # ملء الحقول الجديدة
            if hasattr(self, 'dhl_number_edit'):
                self.dhl_number_edit.setText(shipment.dhl_number or "")
            if hasattr(self, 'final_destination_edit'):
                self.final_destination_edit.setText(shipment.final_destination or "")
            if hasattr(self, 'vessel_name_edit'):
                self.vessel_name_edit.setText(shipment.vessel_name or "")
            if hasattr(self, 'voyage_number_edit'):
                self.voyage_number_edit.setText(shipment.voyage_number or "")

            # تحميل الأصناف
            self.load_shipment_items(shipment.id)

            # تحميل الحاويات
            self.load_shipment_containers(shipment.id)

            # تحميل بيانات المستندات
            self.load_documents_data(shipment)

            print(f"✅ تم تحميل بيانات الشحنة: {shipment.shipment_number}")

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في تحميل بيانات الشحنة:\n{str(e)}"
            )
        finally:
            # إغلاق الجلسة بأمان
            if session:
                try:
                    session.close()
                except Exception as close_error:
                    print(f"تحذير: خطأ في إغلاق جلسة قاعدة البيانات: {close_error}")

        # تفعيل أزرار التعديل والحذف بعد تحميل البيانات بنجاح
        if self.current_shipment_id:
            self.edit_button.setEnabled(True)
            self.delete_button.setEnabled(True)

    def load_documents_data(self, shipment):
        """تحميل بيانات المستندات من قاعدة البيانات"""
        try:
            # تحميل روابط المستندات المحددة
            if hasattr(self, 'initial_docs_edit') and shipment.initial_documents_url:
                self.initial_docs_edit.setText(shipment.initial_documents_url)
                self.apply_link_style(self.initial_docs_edit, shipment.initial_documents_url, "المستندات الأولية")

            if hasattr(self, 'dn_docs_edit') and shipment.dn_documents_url:
                self.dn_docs_edit.setText(shipment.dn_documents_url)
                self.apply_link_style(self.dn_docs_edit, shipment.dn_documents_url, "المستندات (DN)")

            if hasattr(self, 'customs_docs_edit') and shipment.customs_documents_url:
                self.customs_docs_edit.setText(shipment.customs_documents_url)
                self.apply_link_style(self.customs_docs_edit, shipment.customs_documents_url, "المستندات المرسلة للجمارك")

            if hasattr(self, 'bill_lading_edit') and shipment.bill_of_lading_url:
                self.bill_lading_edit.setText(shipment.bill_of_lading_url)
                self.apply_link_style(self.bill_lading_edit, shipment.bill_of_lading_url, "بوليصة الشحن")

            if hasattr(self, 'items_images_edit') and shipment.items_images_url:
                self.items_images_edit.setText(shipment.items_images_url)
                self.apply_link_style(self.items_images_edit, shipment.items_images_url, "صور الأصناف")

            if hasattr(self, 'other_docs_edit') and shipment.other_documents_url:
                self.other_docs_edit.setText(shipment.other_documents_url)
                self.apply_link_style(self.other_docs_edit, shipment.other_documents_url, "مستندات أخرى")

            # تحميل بيانات المرفقات وتحديث عدادات الأزرار
            import json

            # تحديث أزرار المرفقات لإظهار عدد الملفات المحفوظة
            self.update_attachment_button_count("المستندات الأولية", shipment.initial_documents_files)
            self.update_attachment_button_count("المستندات (DN)", shipment.dn_documents_files)
            self.update_attachment_button_count("المستندات المرسلة للجمارك", shipment.customs_documents_files)
            self.update_attachment_button_count("بوليصة الشحن", shipment.bill_of_lading_files)
            self.update_attachment_button_count("صور الأصناف", shipment.items_images_files)
            self.update_attachment_button_count("مستندات أخرى", shipment.other_documents_files)

            # تحميل المستندات الإضافية
            self.load_additional_documents(shipment.id)

        except Exception as e:
            print(f"خطأ في تحميل بيانات المستندات: {str(e)}")

    def load_additional_documents(self, shipment_id):
        """تحميل المستندات الإضافية من قاعدة البيانات مع حماية من تداخل الجلسات"""
        session = None
        try:
            from ...database.models import ShipmentDocument
            # إنشاء جلسة منفصلة لتجنب التداخل
            isolated_db_manager = DatabaseManager()
            session = isolated_db_manager.get_session()

            # مسح الجدول أولاً
            if hasattr(self, 'documents_table'):
                self.documents_table.setRowCount(0)

            # تحميل المستندات من قاعدة البيانات
            documents = session.query(ShipmentDocument).filter(
                ShipmentDocument.shipment_id == shipment_id
            ).all()

            for doc in documents:
                # تحديد المسار (رابط أم ملف)
                path = doc.document_url if doc.document_url else doc.file_path

                # إضافة المستند إلى الجدول
                self.add_document_to_table(
                    doc.document_name,
                    doc.document_type or "مستند",
                    path or "",
                    doc.description or ""
                )

            # تحديث عداد المستندات
            if hasattr(self, 'total_documents_label'):
                count = len(documents)
                self.total_documents_label.setText(f"إجمالي المستندات: {count}")

            print(f"✅ تم تحميل {len(documents)} مستند إضافي")

        except Exception as e:
            print(f"خطأ في تحميل المستندات الإضافية: {str(e)}")
        finally:
            # إغلاق الجلسة بأمان
            if session:
                try:
                    session.close()
                except Exception as close_error:
                    print(f"تحذير: خطأ في إغلاق جلسة قاعدة البيانات: {close_error}")

    def update_attachment_button_count(self, document_type, files_json):
        """تحديث عداد المرفقات على الأزرار"""
        try:
            if not files_json:
                return

            import json
            files_list = json.loads(files_json)
            count = len(files_list) if files_list else 0

            if count == 0:
                return

            # تحديد الزر المناسب حسب نوع المستند
            button = None
            if document_type == "المستندات الأولية" and hasattr(self, 'initial_docs_attach_button'):
                button = self.initial_docs_attach_button
            elif document_type == "المستندات (DN)" and hasattr(self, 'dn_docs_attach_button'):
                button = self.dn_docs_attach_button
            elif document_type == "المستندات المرسلة للجمارك" and hasattr(self, 'customs_docs_attach_button'):
                button = self.customs_docs_attach_button
            elif document_type == "بوليصة الشحن" and hasattr(self, 'bill_lading_attach_button'):
                button = self.bill_lading_attach_button
            elif document_type == "صور الأصناف" and hasattr(self, 'items_images_attach_button'):
                button = self.items_images_attach_button
            elif document_type == "مستندات أخرى" and hasattr(self, 'other_docs_attach_button'):
                button = self.other_docs_attach_button

            if button:
                button.setText(f"مرفق ({count})")
                button.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; padding: 4px 8px; border-radius: 3px; font-size: 11px; }")

        except Exception as e:
            print(f"خطأ في تحديث عداد المرفقات: {str(e)}")

    def apply_link_style(self, edit_widget, url, link_type):
        """تطبيق تنسيق الرابط التشعبي على الحقل"""
        try:
            edit_widget.setReadOnly(False)
            edit_widget.setStyleSheet("""
                QLineEdit {
                    color: #0066cc;
                    text-decoration: underline;
                    background-color: #f0f8ff;
                    border: 2px solid #0066cc;
                    border-radius: 3px;
                    padding: 5px;
                }
                QLineEdit:hover {
                    background-color: #e6f3ff;
                }
            """)

            # إضافة إمكانية النقر لفتح الرابط
            edit_widget.mousePressEvent = lambda event: self.open_url(url) if event.button() == Qt.LeftButton else None

            # إضافة tooltip
            edit_widget.setToolTip(f"{link_type}\nانقر لفتح الرابط")

        except Exception as e:
            print(f"خطأ في تطبيق تنسيق الرابط: {str(e)}")

    def load_shipment_items(self, shipment_id):
        """تحميل أصناف الشحنة مع حماية من تداخل الجلسات"""
        session = None
        try:
            # إنشاء جلسة منفصلة لتجنب التداخل
            isolated_db_manager = DatabaseManager()
            session = isolated_db_manager.get_session()
            items = session.query(ShipmentItem).filter(
                ShipmentItem.shipment_id == shipment_id
            ).all()

            self.items_table.setRowCount(len(items))
            for row, item in enumerate(items):
                # ترتيب الأعمدة الجديد: "كود الصنف", "اسم الصنف", "الكمية", "سعر الوحدة", "تاريخ الإنتاج", "تاريخ الانتهاء", "السعر الإجمالي", "الوزن", "ملاحظات"
                self.items_table.setItem(row, 0, QTableWidgetItem(item.item.code if item.item else ""))  # كود الصنف
                self.items_table.setItem(row, 1, QTableWidgetItem(item.item.name if item.item else ""))  # اسم الصنف
                self.items_table.setItem(row, 2, QTableWidgetItem(str(item.quantity)))  # الكمية
                self.items_table.setItem(row, 3, QTableWidgetItem(f"{item.unit_price:.2f}"))  # سعر الوحدة

                # تاريخ الإنتاج - فارغ حالياً (يمكن إضافة حقول في قاعدة البيانات لاحقاً)
                production_date = ""
                if hasattr(item, 'production_date') and item.production_date:
                    production_date = item.production_date.strftime("%Y-%m-%d")
                self.items_table.setItem(row, 4, QTableWidgetItem(production_date))

                # تاريخ الانتهاء - فارغ حالياً (يمكن إضافة حقول في قاعدة البيانات لاحقاً)
                expiry_date = ""
                if hasattr(item, 'expiry_date') and item.expiry_date:
                    expiry_date = item.expiry_date.strftime("%Y-%m-%d")
                self.items_table.setItem(row, 5, QTableWidgetItem(expiry_date))

                self.items_table.setItem(row, 6, QTableWidgetItem(f"{item.total_price:.2f}"))  # السعر الإجمالي
                self.items_table.setItem(row, 7, QTableWidgetItem(f"{item.weight:.2f}" if item.weight else "0.00"))  # الوزن
                self.items_table.setItem(row, 8, QTableWidgetItem(item.notes or ""))  # ملاحظات

                # حفظ معرف الصنف في العمود الأول (كود الصنف)
                self.items_table.item(row, 0).setData(Qt.UserRole, item.item_id)

            self.update_items_total()

        except Exception as e:
            print(f"خطأ في تحميل الأصناف: {str(e)}")
        finally:
            if session:
                try:
                    session.close()
                except:
                    pass

    def load_shipment_containers(self, shipment_id):
        """تحميل حاويات الشحنة مع حماية من تداخل الجلسات"""
        session = None
        try:
            # إنشاء جلسة منفصلة لتجنب التداخل
            isolated_db_manager = DatabaseManager()
            session = isolated_db_manager.get_session()
            containers = session.query(Container).filter(
                Container.shipment_id == shipment_id
            ).all()

            self.containers_table.setRowCount(len(containers))
            for row, container in enumerate(containers):
                self.containers_table.setItem(row, 0, QTableWidgetItem(container.container_number or ""))
                self.containers_table.setItem(row, 1, QTableWidgetItem(container.container_type or ""))
                self.containers_table.setItem(row, 2, QTableWidgetItem(container.container_size or ""))
                self.containers_table.setItem(row, 3, QTableWidgetItem(f"{container.weight_empty:.2f}" if container.weight_empty else "0.00"))
                self.containers_table.setItem(row, 4, QTableWidgetItem(f"{container.weight_loaded:.2f}" if container.weight_loaded else "0.00"))
                self.containers_table.setItem(row, 5, QTableWidgetItem(container.status or ""))
                self.containers_table.setItem(row, 6, QTableWidgetItem(container.notes or ""))

                # حفظ معرف الحاوية
                self.containers_table.item(row, 0).setData(Qt.UserRole, container.id)

        except Exception as e:
            print(f"خطأ في تحميل الحاويات: {str(e)}")
        finally:
            if session:
                try:
                    session.close()
                except:
                    pass

    def fill_test_data(self):
        """ملء النموذج ببيانات تجريبية من واقع النظام"""
        from PySide6.QtCore import QDate

        try:
            # ملء بيانات المورد من النظام
            self.fill_real_supplier_data()

            # البيانات الأساسية - توليد رقم شحنة فريد
            unique_number = self.generate_unique_shipment_number()
            self.shipment_number_edit.setText(unique_number)
            self.supplier_invoice_edit.setText("INV-2025-12345")
            self.shipment_status_combo.setCurrentText("قيد الشحن")
            self.clearance_status_combo.setCurrentText("في الانتظار")
            self.tracking_number_edit.setText("TRK123456789")
            self.bill_of_lading_edit.setText("BOL-2025-001")
            self.notes_edit.setText("شحنة تجريبية للاختبار - تحتوي على مواد إلكترونية")

            # البيانات المالية
            self.currency_combo.setCurrentText("USD")
            self.exchange_rate_edit.setText("3.67")
            self.goods_value_edit.setText("50000.00")
            self.shipping_cost_edit.setText("2500.00")
            if hasattr(self, 'insurance_edit'):
                self.insurance_edit.setText("500.00")
            elif hasattr(self, 'insurance_cost_edit'):
                self.insurance_cost_edit.setText("500.00")
            self.customs_fees_edit.setText("1200.00")
            self.other_fees_edit.setText("300.00")
            self.payment_status_combo.setCurrentText("مدفوع جزئياً")
            self.paid_amount_edit.setText("30000.00")
            self.payment_date_edit.setDate(QDate.currentDate())
            self.payment_method_combo.setCurrentText("تحويل بنكي")

            # بيانات الشحن
            if hasattr(self, 'smart_shipping_company_widget'):
                self.smart_shipping_company_widget.set_company_name("شركة الخليج للشحن")
            elif hasattr(self, 'shipping_company_edit'):
                self.shipping_company_edit.setText("شركة الخليج للشحن")
            self.shipping_type_combo.setCurrentText("بحري")
            self.shipping_method_combo.setCurrentText("FCL")
            self.delivery_terms_combo.setCurrentText("FOB")
            self.departure_port_edit.setText("ميناء جبل علي")
            self.bill_of_lading_number_edit.setText("BL-2024-001234")
            self.main_container_number_edit.setText("MSKU1234567")
            self.final_destination_edit.setText("الرياض - المملكة العربية السعودية")
            if hasattr(self, 'dhl_number_edit'):
                self.dhl_number_edit.setText("DHL123456789")
            self.vessel_name_edit.setText("سفينة الخليج 1")
            self.voyage_number_edit.setText("V2025-001")
            self.bl_number_edit.setText("BL-2025-12345")
            self.departure_date_edit.setDate(QDate.currentDate().addDays(-5))
            self.arrival_date_edit.setDate(QDate.currentDate().addDays(10))
            self.estimated_departure_edit.setDate(QDate.currentDate().addDays(-5))
            self.estimated_arrival_edit.setDate(QDate.currentDate().addDays(10))

            # إضافة بعض الأصناف التجريبية من النظام
            self.add_real_test_items()

            # إضافة حاوية تجريبية
            self.add_test_container()

            # عرض إحصائيات البيانات المستخدمة
            self.show_data_statistics()

            QMessageBox.information(self, "بيانات تجريبية", "✅ تم ملء النموذج ببيانات تجريبية من واقع النظام بنجاح!")

        except Exception as e:
            QMessageBox.warning(
                self,
                "تحذير",
                f"تم ملء البيانات الأساسية، لكن حدث خطأ في ملء بيانات النظام:\n{str(e)}\n\nسيتم استخدام البيانات الافتراضية."
            )
            # في حالة الخطأ، استخدم البيانات الافتراضية
            self.add_test_items()

    def fill_real_supplier_data(self):
        """ملء بيانات مورد حقيقي من النظام"""
        try:
            from src.database.database_manager import DatabaseManager
            from src.database.models import Supplier

            db_manager = DatabaseManager()
            session = db_manager.get_session()

            # الحصول على أول مورد نشط
            supplier = session.query(Supplier).filter(
                Supplier.is_active == True
            ).first()

            if supplier:
                # ملء حقل المورد بالاسم والكود
                supplier_text = f"{supplier.name} ({supplier.code})"
                self.supplier_edit.setText(supplier_text)

                # حفظ معرف المورد
                self.selected_supplier_id = supplier.id
                # تعيين معرف المورد كخاصية للحقل للتحقق من الصحة
                self.supplier_edit.setProperty("supplier_id", supplier.id)

                print(f"✅ تم ملء بيانات المورد: {supplier.name}")
            else:
                # في حالة عدم وجود موردين، استخدم بيانات افتراضية
                self.supplier_edit.setText("شركة الإمارات للتجارة")
                print("⚠️ لم يتم العثور على موردين في النظام، تم استخدام بيانات افتراضية")

        except Exception as e:
            print(f"خطأ في ملء بيانات المورد: {str(e)}")
            # في حالة الخطأ، استخدم بيانات افتراضية
            self.supplier_edit.setText("شركة الإمارات للتجارة")
        finally:
            if 'session' in locals():
                session.close()

    def add_real_test_items(self):
        """إضافة أصناف حقيقية من النظام"""
        try:
            from src.database.database_manager import DatabaseManager
            from src.database.models import Item

            db_manager = DatabaseManager()
            session = db_manager.get_session()

            # الحصول على أول 3 أصناف نشطة من النظام
            items = session.query(Item).filter(
                Item.is_active == True
            ).limit(3).all()

            if items:
                # مسح الجدول أولاً
                self.items_table.setRowCount(0)

                # إضافة الأصناف الحقيقية
                for i, item in enumerate(items):
                    row = self.items_table.rowCount()
                    self.items_table.insertRow(row)

                    # استخدام بيانات الصنف الحقيقية
                    quantity = 10 + (i * 5)  # كميات متنوعة
                    unit_price = item.selling_price if item.selling_price > 0 else (100.0 + (i * 50))
                    total_price = quantity * unit_price
                    weight = item.weight if item.weight else (1.0 + i)

                    # ملء البيانات - الترتيب الجديد: "كود الصنف", "اسم الصنف", "الكمية", "سعر الوحدة", "تاريخ الإنتاج", "تاريخ الانتهاء", "السعر الإجمالي", "الوزن", "ملاحظات"
                    self.items_table.setItem(row, 0, QTableWidgetItem(item.code))  # كود الصنف
                    self.items_table.setItem(row, 1, QTableWidgetItem(item.name))  # اسم الصنف
                    self.items_table.setItem(row, 2, QTableWidgetItem(str(quantity)))  # الكمية
                    self.items_table.setItem(row, 3, QTableWidgetItem(f"{unit_price:.2f}"))  # سعر الوحدة
                    self.items_table.setItem(row, 4, QTableWidgetItem(""))  # تاريخ الإنتاج - فارغ
                    self.items_table.setItem(row, 5, QTableWidgetItem(""))  # تاريخ الانتهاء - فارغ
                    self.items_table.setItem(row, 6, QTableWidgetItem(f"{total_price:.2f}"))  # السعر الإجمالي
                    self.items_table.setItem(row, 7, QTableWidgetItem(f"{weight:.2f}"))  # الوزن
                    self.items_table.setItem(row, 8, QTableWidgetItem(item.description or ""))  # ملاحظات

                # تحديث المجموع الكلي
                self.update_items_total()

                print(f"✅ تم إضافة {len(items)} صنف حقيقي من النظام")
            else:
                # في حالة عدم وجود أصناف، استخدم البيانات الافتراضية
                print("⚠️ لم يتم العثور على أصناف في النظام، سيتم استخدام البيانات الافتراضية")
                self.add_test_items()

        except Exception as e:
            print(f"خطأ في إضافة الأصناف الحقيقية: {str(e)}")
            # في حالة الخطأ، استخدم البيانات الافتراضية
            self.add_test_items()
        finally:
            if 'session' in locals():
                session.close()

    def show_data_statistics(self):
        """عرض إحصائيات البيانات المستخدمة"""
        try:
            from src.database.database_manager import DatabaseManager
            from src.database.models import Item, Supplier

            db_manager = DatabaseManager()
            session = db_manager.get_session()

            # إحصائيات الموردين
            total_suppliers = session.query(Supplier).count()
            active_suppliers = session.query(Supplier).filter(Supplier.is_active == True).count()

            # إحصائيات الأصناف
            total_items = session.query(Item).count()
            active_items = session.query(Item).filter(Item.is_active == True).count()

            # عرض الإحصائيات في وحدة التحكم
            print("📊 إحصائيات البيانات المستخدمة:")
            print("=" * 40)
            print(f"📦 إجمالي الموردين: {total_suppliers}")
            print(f"✅ الموردين النشطين: {active_suppliers}")
            print(f"📋 إجمالي الأصناف: {total_items}")
            print(f"✅ الأصناف النشطة: {active_items}")
            print("=" * 40)

        except Exception as e:
            print(f"خطأ في عرض الإحصائيات: {str(e)}")
        finally:
            if 'session' in locals():
                session.close()

    def add_test_items(self):
        """إضافة أصناف تجريبية"""
        # الترتيب الجديد: "كود الصنف", "اسم الصنف", "الكمية", "سعر الوحدة", "تاريخ الإنتاج", "تاريخ الانتهاء", "السعر الإجمالي", "الوزن", "ملاحظات"
        test_items = [
            ["ELEC001", "لابتوب ديل XPS 13", "50", "800.00", "2024-01-15", "2026-01-15", "40000.00", "1.5", "لابتوبات للمكاتب"],
            ["ELEC002", "شاشة سامسونج 27 بوصة", "25", "300.00", "2024-02-10", "2027-02-10", "7500.00", "8.0", "شاشات عالية الدقة"],
            ["ELEC003", "طابعة HP LaserJet", "10", "250.00", "2024-03-05", "2026-03-05", "2500.00", "12.0", "طابعات ليزر"]
        ]

        self.items_table.setRowCount(len(test_items))
        for row, item in enumerate(test_items):
            for col, value in enumerate(item):
                from PySide6.QtWidgets import QTableWidgetItem
                self.items_table.setItem(row, col, QTableWidgetItem(str(value)))

    def add_test_container(self):
        """إضافة حاوية تجريبية"""
        test_containers = [
            ["CONT001", "20 قدم", "تم التحميل", "MSKU1234567", "15000", "تحتوي على معدات إلكترونية"]
        ]

        self.containers_table.setRowCount(len(test_containers))
        for row, container in enumerate(test_containers):
            for col, value in enumerate(container):
                from PySide6.QtWidgets import QTableWidgetItem
                self.containers_table.setItem(row, col, QTableWidgetItem(str(value)))
    
    def save_and_close(self):
        """حفظ وإغلاق النافذة - نسخة مبسطة"""
        try:
            if self.save_shipment():
                # إغلاق مباشر بدون تأخير
                self.accept()
        except Exception as e:
            print(f"خطأ في حفظ وإغلاق النافذة: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ النافذة: {str(e)}")

    def cleanup_resources(self):
        """تنظيف الموارد"""
        try:
            # إيقاف عمليات الحفظ الجارية
            if self._is_saving:
                self.cancel_save()

            # تنظيف الـ threads
            self.cleanup_save_thread()

            # إغلاق progress dialog
            if self.progress_dialog:
                self.progress_dialog.close()
                self.progress_dialog = None

            # إغلاق جلسات قاعدة البيانات
            if hasattr(self, 'db_manager') and self.db_manager:
                self.db_manager.close_all_sessions()
        except Exception as e:
            print(f"خطأ في تنظيف الموارد: {e}")

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        self.cleanup_resources()
        event.accept()

    def accept(self):
        """قبول النافذة مع تنظيف الموارد"""
        self.cleanup_resources()
        super().accept()

    def reject(self):
        """رفض النافذة مع تنظيف الموارد"""
        self.cleanup_resources()
        super().reject()
    
    def clear_form(self):
        """مسح جميع الحقول"""
        try:
            # تبويب البيانات الأساسية
            if hasattr(self, 'shipment_date_edit'):
                self.shipment_date_edit.setDate(QDate.currentDate())
            self.shipment_number_edit.clear()
            self.supplier_edit.clear()
            self.selected_supplier_id = None  # مسح معرف المورد المختار
            self.supplier_invoice_edit.clear()
            self.shipment_status_combo.setCurrentIndex(0)
            self.clearance_status_combo.setCurrentIndex(0)
            self.tracking_number_edit.clear()
            self.bill_of_lading_edit.clear()
            self.notes_edit.clear()

            # تبويب الأصناف
            self.items_table.setRowCount(0)
            self.total_items_label.setText("إجمالي الأصناف: 0")
            self.total_value_label.setText("إجمالي القيمة: 0.00")

            # تبويب المالية
            self.currency_combo.setCurrentIndex(0)
            self.exchange_rate_edit.setText("1.00")
            self.goods_value_edit.setText("0.00")
            if hasattr(self, 'goods_value_usd_edit'):
                self.goods_value_usd_edit.setText("0.00")
            self.shipping_cost_edit.setText("0.00")
            self.insurance_cost_edit.setText("0.00")
            self.customs_fees_edit.setText("0.00")
            self.other_fees_edit.setText("0.00")
            self.total_costs_edit.setText("0.00")
            self.total_local_currency_edit.setText("0.00")
            self.payment_status_combo.setCurrentIndex(0)
            self.paid_amount_edit.setText("0.00")
            self.remaining_amount_edit.setText("0.00")
            self.payment_date_edit.setDate(QDate.currentDate())
            self.payment_method_combo.setCurrentIndex(0)

            # قسم أجور الشحن الجديد (Radio Buttons)
            if hasattr(self, 'supplier_entry_radio'):
                self.supplier_entry_radio.setChecked(False)
            if hasattr(self, 'shipping_company_entry_radio'):
                self.shipping_company_entry_radio.setChecked(False)
            if hasattr(self, 'supplier_shipping_fees_edit'):
                self.supplier_shipping_fees_edit.setText("0.00")
            if hasattr(self, 'supplier_currency_combo'):
                self.supplier_currency_combo.setCurrentIndex(0)
            if hasattr(self, 'supplier_local_fees_edit'):
                self.supplier_local_fees_edit.setText("0.00")
            if hasattr(self, 'shipping_company_combo'):
                self.shipping_company_combo.setCurrentIndex(0)
            if hasattr(self, 'shipping_company_fees_edit'):
                self.shipping_company_fees_edit.setText("0.00")

            # تبويب الشحن
            if hasattr(self, 'smart_shipping_company_widget'):
                # التعبئة التلقائية معطلة افتراضياً حتى عند مسح النموذج
                self.smart_shipping_company_widget.set_auto_fill_enabled(False)
                self.smart_shipping_company_widget.set_company_name("", auto_validate=False)
                # تحديث حالة الزر
                self.smart_shipping_company_widget.auto_fill_toggle_button.setChecked(False)
                self.smart_shipping_company_widget.auto_fill_toggle_button.setText("🔒 تعطيل")
            elif hasattr(self, 'shipping_company_edit'):
                self.shipping_company_edit.clear()
            self.shipping_type_combo.setCurrentIndex(0)
            self.shipping_method_combo.setCurrentIndex(0)
            self.incoterms_combo.setCurrentIndex(0)
            self.port_of_loading_edit.clear()
            self.port_of_discharge_edit.clear()
            self.final_destination_edit.clear()
            self.vessel_name_edit.clear()
            self.voyage_number_edit.clear()
            if hasattr(self, 'dhl_number_edit'):
                self.dhl_number_edit.clear()
            self.bill_of_lading_number_edit.clear()
            self.main_container_number_edit.clear()
            self.estimated_departure_date_edit.setDate(QDate.currentDate())
            self.actual_departure_date_edit.setDate(QDate.currentDate())
            self.estimated_arrival_date_edit.setDate(QDate.currentDate())
            self.actual_arrival_date_edit.setDate(QDate.currentDate())

            # تبويب الحاويات
            self.containers_table.setRowCount(0)
            self.total_weight_spin.setValue(0.00)
            self.total_volume_spin.setValue(0.00)
            self.packages_count_spin.setValue(0)
            self.packaging_type_combo.setCurrentIndex(0)
            self.shipping_notes_edit.clear()
            self.total_containers_label.setText("إجمالي الحاويات: 0")

            # تبويب المستندات
            self.initial_docs_edit.clear()
            self.dn_docs_edit.clear()
            self.customs_docs_edit.clear()
            self.bill_lading_edit.clear()
            self.items_images_edit.clear()
            self.other_docs_edit.clear()
            self.documents_table.setRowCount(0)
            self.total_documents_label.setText("إجمالي المستندات: 0")

            print("✅ تم مسح النموذج بالكامل")
        except Exception as e:
            print(f"خطأ في مسح النموذج: {str(e)}")

    def import_from_excel(self):
        """استيراد البيانات من ملف إكسيل"""
        try:
            from PySide6.QtWidgets import QFileDialog, QMessageBox, QProgressDialog, QDialog
            from PySide6.QtCore import Qt
            import pandas as pd
            from datetime import datetime

            # اختيار ملف الإكسيل
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف الإكسيل للاستيراد",
                "",
                "Excel Files (*.xlsx *.xls);;All Files (*)"
            )

            if not file_path:
                return

            # إنشاء شريط التقدم
            progress = QProgressDialog("جاري قراءة ملف الإكسيل...", "إلغاء", 0, 100, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()
            progress.setValue(10)

            # قراءة ملف الإكسيل
            try:
                df = pd.read_excel(file_path)
                progress.setValue(30)

                if df.empty:
                    QMessageBox.warning(self, "تحذير", "الملف فارغ أو لا يحتوي على بيانات")
                    return

                # عرض معلومات الملف وخيارات الاستيراد
                total_rows = len(df)
                progress.close()

                # نافذة اختيار نوع الاستيراد
                import_choice = self.show_import_options_dialog(total_rows, df.columns.tolist())
                if import_choice is None:
                    return

                # بدء عملية الاستيراد
                if import_choice == "single":
                    self.import_single_shipment(df, file_path)
                elif import_choice == "multiple":
                    self.import_multiple_shipments(df, file_path)

            except Exception as e:
                progress.close()
                QMessageBox.critical(
                    self,
                    "خطأ في قراءة الملف",
                    f"حدث خطأ أثناء قراءة ملف الإكسيل:\n{str(e)}\n\n"
                    f"تأكد من أن الملف:\n"
                    f"• بصيغة Excel (.xlsx أو .xls)\n"
                    f"• يحتوي على البيانات في الصف الأول\n"
                    f"• الأعمدة لها عناوين باللغة العربية"
                )

                # استيراد البيانات من الصف الأول
                if len(df) > 0:
                    row = df.iloc[0]  # أخذ الصف الأول

                    # استيراد البيانات الأساسية
                    if 'التاريخ' in df.columns and pd.notna(row['التاريخ']):
                        try:
                            if isinstance(row['التاريخ'], str):
                                date_obj = pd.to_datetime(row['التاريخ']).date()
                            else:
                                date_obj = row['التاريخ'].date() if hasattr(row['التاريخ'], 'date') else row['التاريخ']

                            from PySide6.QtCore import QDate
                            qdate = QDate(date_obj.year, date_obj.month, date_obj.day)
                            self.shipment_date_edit.setDate(qdate)
                        except Exception as e:
                            print(f"خطأ في تحويل التاريخ: {e}")

                    progress.setValue(60)

                    # استيراد المورد
                    if 'المورد' in df.columns and pd.notna(row['المورد']):
                        supplier_name = str(row['المورد']).strip()
                        if supplier_name:
                            # البحث عن المورد في قاعدة البيانات
                            session = self.db_manager.get_session()
                            try:
                                from src.database.models import Supplier
                                supplier = session.query(Supplier).filter(
                                    Supplier.name.ilike(f"%{supplier_name}%")
                                ).first()

                                if supplier:
                                    supplier_text = f"{supplier.name} ({supplier.code})"
                                    self.supplier_edit.setText(supplier_text)
                                    self.supplier_edit.setProperty("supplier_id", supplier.id)
                                    self.selected_supplier_id = supplier.id
                                else:
                                    self.supplier_edit.setText(supplier_name)
                            finally:
                                session.close()

                    progress.setValue(70)

                    # استيراد بوليصة الشحن
                    if 'بوليصة الشحن' in df.columns and pd.notna(row['بوليصة الشحن']):
                        self.bill_of_lading_edit.setText(str(row['بوليصة الشحن']).strip())

                    # استيراد الملاحظات
                    if 'ملاحظات' in df.columns and pd.notna(row['ملاحظات']):
                        self.notes_edit.setPlainText(str(row['ملاحظات']).strip())

                    # استيراد حالة الشحنة
                    if 'حالة الشحنة' in df.columns and pd.notna(row['حالة الشحنة']):
                        status_text = str(row['حالة الشحنة']).strip()
                        if status_text:
                            # البحث عن الحالة في القائمة المنسدلة
                            status_index = self.shipment_status_combo.findText(status_text)
                            if status_index >= 0:
                                self.shipment_status_combo.setCurrentIndex(status_index)
                            else:
                                # إذا لم توجد الحالة، أضفها إلى القائمة
                                self.shipment_status_combo.addItem(status_text)
                                self.shipment_status_combo.setCurrentText(status_text)

                    # استيراد حالة الإفراج
                    if 'حالة الإفراج' in df.columns and pd.notna(row['حالة الإفراج']):
                        clearance_text = str(row['حالة الإفراج']).strip()
                        if clearance_text:
                            # البحث عن الحالة في القائمة المنسدلة
                            clearance_index = self.clearance_status_combo.findText(clearance_text)
                            if clearance_index >= 0:
                                self.clearance_status_combo.setCurrentIndex(clearance_index)
                            else:
                                # إذا لم توجد الحالة، أضفها إلى القائمة
                                self.clearance_status_combo.addItem(clearance_text)
                                self.clearance_status_combo.setCurrentText(clearance_text)

                    progress.setValue(80)

                    # استيراد بيانات الشحن
                    if 'شركة الشحن' in df.columns and pd.notna(row['شركة الشحن']):
                        company_name = str(row['شركة الشحن']).strip()
                        if hasattr(self, 'smart_shipping_company_widget'):
                            self.smart_shipping_company_widget.set_company_name(company_name)
                        elif hasattr(self, 'shipping_company_edit'):
                            self.shipping_company_edit.setText(company_name)

                    if 'رقم DHL' in df.columns and pd.notna(row['رقم DHL']):
                        self.dhl_number_edit.setText(str(row['رقم DHL']).strip())

                    # تم حذف حقل ميناء الوصول المكرر

                    # استيراد تاريخ الوصول المتوقع
                    if 'تاريخ الوصول المتوقع' in df.columns and pd.notna(row['تاريخ الوصول المتوقع']):
                        try:
                            if isinstance(row['تاريخ الوصول المتوقع'], str):
                                date_obj = pd.to_datetime(row['تاريخ الوصول المتوقع']).date()
                            else:
                                date_obj = row['تاريخ الوصول المتوقع'].date() if hasattr(row['تاريخ الوصول المتوقع'], 'date') else row['تاريخ الوصول المتوقع']

                            qdate = QDate(date_obj.year, date_obj.month, date_obj.day)
                            self.estimated_arrival_date_edit.setDate(qdate)
                        except Exception as e:
                            print(f"خطأ في تحويل تاريخ الوصول المتوقع: {e}")

                    progress.setValue(90)

                    # استيراد أرقام الحاويات (دعم الحاويات المتعددة)
                    if 'رقم الحاوية' in df.columns and pd.notna(row['رقم الحاوية']):
                        container_data = str(row['رقم الحاوية']).strip()
                        if container_data:
                            # تحليل الحاويات المتعددة
                            containers = self.parse_multiple_containers(container_data)
                            for container_number in containers:
                                if container_number.strip():
                                    # إضافة كل حاوية على حدة
                                    self.add_container_from_import(container_number.strip())

                    progress.setValue(100)
                    progress.close()

                    # حساب عدد الحاويات المستوردة
                    containers_count = 0
                    if 'رقم الحاوية' in df.columns and pd.notna(row['رقم الحاوية']):
                        container_data = str(row['رقم الحاوية']).strip()
                        if container_data:
                            containers = self.parse_multiple_containers(container_data)
                            containers_count = len([c for c in containers if c.strip()])

                    QMessageBox.information(
                        self,
                        "نجح الاستيراد",
                        f"✅ تم استيراد البيانات بنجاح من الملف:\n{file_path}\n\n"
                        f"البيانات المستوردة:\n"
                        f"• التاريخ: {'✓' if 'التاريخ' in df.columns else '✗'}\n"
                        f"• المورد: {'✓' if 'المورد' in df.columns else '✗'}\n"
                        f"• بوليصة الشحن: {'✓' if 'بوليصة الشحن' in df.columns else '✗'}\n"
                        f"• ملاحظات: {'✓' if 'ملاحظات' in df.columns else '✗'}\n"
                        f"• حالة الشحنة: {'✓' if 'حالة الشحنة' in df.columns else '✗'}\n"
                        f"• حالة الإفراج: {'✓' if 'حالة الإفراج' in df.columns else '✗'}\n"
                        f"• شركة الشحن: {'✓' if 'شركة الشحن' in df.columns else '✗'}\n"
                        f"• رقم DHL: {'✓' if 'رقم DHL' in df.columns else '✗'}\n"
                        f"• ميناء الوصول: {'✓' if 'ميناء الوصول' in df.columns else '✗'}\n"
                        f"• تاريخ الوصول المتوقع: {'✓' if 'تاريخ الوصول المتوقع' in df.columns else '✗'}\n"
                        f"• الحاويات: {'✓' if 'رقم الحاوية' in df.columns else '✗'}"
                        f"{f' ({containers_count} حاوية)' if containers_count > 0 else ''}"
                    )

            except Exception as e:
                progress.close()
                QMessageBox.critical(
                    self,
                    "خطأ في قراءة الملف",
                    f"حدث خطأ أثناء قراءة ملف الإكسيل:\n{str(e)}\n\n"
                    f"تأكد من أن الملف:\n"
                    f"• بصيغة Excel (.xlsx أو .xls)\n"
                    f"• يحتوي على البيانات في الصف الأول\n"
                    f"• الأعمدة لها عناوين باللغة العربية"
                )

        except ImportError:
            QMessageBox.critical(
                self,
                "مكتبة مفقودة",
                "مكتبة pandas غير مثبتة.\n\n"
                "لاستخدام ميزة الاستيراد من الإكسيل، يرجى تثبيت pandas:\n"
                "pip install pandas openpyxl"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ غير متوقع",
                f"حدث خطأ غير متوقع:\n{str(e)}"
            )

    def show_import_options_dialog(self, total_rows, columns):
        """عرض نافذة خيارات الاستيراد"""
        try:
            from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                                         QPushButton, QRadioButton, QTextEdit, QGroupBox,
                                         QScrollArea, QFrame)
            from PySide6.QtCore import Qt
            from PySide6.QtGui import QFont

            dialog = QDialog(self)
            dialog.setWindowTitle("خيارات استيراد البيانات من الإكسيل")
            dialog.setModal(True)
            dialog.resize(600, 500)

            layout = QVBoxLayout(dialog)

            # معلومات الملف
            info_group = QGroupBox("معلومات الملف")
            info_layout = QVBoxLayout(info_group)

            info_label = QLabel(f"📊 عدد الأسطر في الملف: {total_rows}")
            info_label.setFont(QFont("Arial", 10, QFont.Bold))
            info_layout.addWidget(info_label)

            # عرض الأعمدة المتاحة
            columns_label = QLabel("📋 الأعمدة المتاحة في الملف:")
            info_layout.addWidget(columns_label)

            columns_text = QTextEdit()
            columns_text.setMaximumHeight(100)
            columns_text.setPlainText(", ".join(columns))
            columns_text.setReadOnly(True)
            info_layout.addWidget(columns_text)

            layout.addWidget(info_group)

            # خيارات الاستيراد
            options_group = QGroupBox("اختر نوع الاستيراد")
            options_layout = QVBoxLayout(options_group)

            # خيار الاستيراد الفردي
            single_radio = QRadioButton("استيراد الصف الأول فقط (شحنة واحدة)")
            single_radio.setChecked(True)
            single_desc = QLabel("   • يتم استيراد بيانات الصف الأول فقط إلى النافذة الحالية")
            single_desc.setStyleSheet("color: gray; margin-left: 20px;")
            options_layout.addWidget(single_radio)
            options_layout.addWidget(single_desc)

            # خط فاصل
            line = QFrame()
            line.setFrameShape(QFrame.HLine)
            line.setFrameShadow(QFrame.Sunken)
            options_layout.addWidget(line)

            # خيار الاستيراد المتعدد
            multiple_radio = QRadioButton(f"استيراد جميع الأسطر ({total_rows} شحنة)")
            multiple_desc = QLabel(f"   • يتم إنشاء {total_rows} شحنة جديدة في قاعدة البيانات")
            multiple_desc.setStyleSheet("color: gray; margin-left: 20px;")
            multiple_warning = QLabel("   ⚠️ تحذير: هذا سيقوم بإنشاء شحنات متعددة في قاعدة البيانات")
            multiple_warning.setStyleSheet("color: orange; margin-left: 20px; font-weight: bold;")
            options_layout.addWidget(multiple_radio)
            options_layout.addWidget(multiple_desc)
            options_layout.addWidget(multiple_warning)

            layout.addWidget(options_group)

            # أزرار التحكم
            buttons_layout = QHBoxLayout()

            ok_button = QPushButton("متابعة")
            ok_button.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)

            cancel_button = QPushButton("إلغاء")
            cancel_button.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #da190b;
                }
            """)

            buttons_layout.addStretch()
            buttons_layout.addWidget(ok_button)
            buttons_layout.addWidget(cancel_button)

            layout.addLayout(buttons_layout)

            # ربط الأزرار
            def on_ok():
                if single_radio.isChecked():
                    dialog.result_value = "single"
                else:
                    dialog.result_value = "multiple"
                dialog.accept()

            def on_cancel():
                dialog.result_value = None
                dialog.reject()

            ok_button.clicked.connect(on_ok)
            cancel_button.clicked.connect(on_cancel)

            # عرض النافذة
            if dialog.exec() == QDialog.Accepted:
                return dialog.result_value
            else:
                return None

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في عرض خيارات الاستيراد: {str(e)}")
            return None

    def import_single_shipment(self, df, file_path):
        """استيراد شحنة واحدة (الصف الأول فقط)"""
        try:
            from PySide6.QtWidgets import QProgressDialog, QMessageBox
            from PySide6.QtCore import Qt
            from datetime import datetime

            # إنشاء شريط التقدم
            progress = QProgressDialog("جاري استيراد الشحنة...", "إلغاء", 0, 100, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()
            progress.setValue(10)

            # قراءة الصف الأول فقط
            row = df.iloc[0]
            progress.setValue(30)

            # استيراد التاريخ
            if 'التاريخ' in df.columns and pd.notna(row['التاريخ']):
                date_value = row['التاريخ']
                if isinstance(date_value, str):
                    try:
                        parsed_date = datetime.strptime(date_value, "%Y-%m-%d")
                        self.shipment_date_edit.setDate(parsed_date.date())
                    except:
                        try:
                            parsed_date = datetime.strptime(date_value, "%d/%m/%Y")
                            self.shipment_date_edit.setDate(parsed_date.date())
                        except:
                            pass
                else:
                    self.shipment_date_edit.setDate(date_value.date())

            progress.setValue(40)

            # استيراد المورد
            if 'المورد' in df.columns and pd.notna(row['المورد']):
                supplier_name = str(row['المورد']).strip()
                if supplier_name:
                    # البحث عن المورد في قاعدة البيانات
                    from src.models.database import get_session
                    from src.models.supplier import Supplier

                    session = get_session()
                    supplier = session.query(Supplier).filter(
                        Supplier.name.ilike(f"%{supplier_name}%")
                    ).first()

                    if supplier:
                        # العثور على المورد في القائمة المنسدلة
                        for i in range(self.supplier_combo.count()):
                            if self.supplier_combo.itemData(i) == supplier.id:
                                self.supplier_combo.setCurrentIndex(i)
                                break
                    else:
                        # إضافة المورد الجديد
                        self.supplier_combo.addItem(supplier_name, None)
                        self.supplier_combo.setCurrentText(supplier_name)

                    session.close()

            progress.setValue(50)

            # استيراد بوليصة الشحن
            if 'بوليصة الشحن' in df.columns and pd.notna(row['بوليصة الشحن']):
                self.bill_of_lading_edit.setText(str(row['بوليصة الشحن']).strip())

            # استيراد الملاحظات
            if 'ملاحظات' in df.columns and pd.notna(row['ملاحظات']):
                self.notes_edit.setPlainText(str(row['ملاحظات']).strip())

            progress.setValue(60)

            # استيراد حالة الشحنة
            if 'حالة الشحنة' in df.columns and pd.notna(row['حالة الشحنة']):
                status_text = str(row['حالة الشحنة']).strip()
                if status_text:
                    status_index = self.shipment_status_combo.findText(status_text)
                    if status_index >= 0:
                        self.shipment_status_combo.setCurrentIndex(status_index)
                    else:
                        self.shipment_status_combo.addItem(status_text)
                        self.shipment_status_combo.setCurrentText(status_text)

            # استيراد حالة الإفراج
            if 'حالة الإفراج' in df.columns and pd.notna(row['حالة الإفراج']):
                clearance_text = str(row['حالة الإفراج']).strip()
                if clearance_text:
                    clearance_index = self.clearance_status_combo.findText(clearance_text)
                    if clearance_index >= 0:
                        self.clearance_status_combo.setCurrentIndex(clearance_index)
                    else:
                        self.clearance_status_combo.addItem(clearance_text)
                        self.clearance_status_combo.setCurrentText(clearance_text)

            progress.setValue(70)

            # استيراد بيانات الشحن
            if 'شركة الشحن' in df.columns and pd.notna(row['شركة الشحن']):
                company_name = str(row['شركة الشحن']).strip()
                if hasattr(self, 'smart_shipping_company_widget'):
                    self.smart_shipping_company_widget.set_company_name(company_name)
                elif hasattr(self, 'shipping_company_edit'):
                    self.shipping_company_edit.setText(company_name)

            if 'رقم DHL' in df.columns and pd.notna(row['رقم DHL']):
                self.dhl_number_edit.setText(str(row['رقم DHL']).strip())

            # تم حذف حقل ميناء الوصول المكرر

            if 'تاريخ الوصول المتوقع' in df.columns and pd.notna(row['تاريخ الوصول المتوقع']):
                arrival_date = row['تاريخ الوصول المتوقع']
                if isinstance(arrival_date, str):
                    try:
                        parsed_date = datetime.strptime(arrival_date, "%Y-%m-%d")
                        self.expected_arrival_date_edit.setDate(parsed_date.date())
                    except:
                        try:
                            parsed_date = datetime.strptime(arrival_date, "%d/%m/%Y")
                            self.expected_arrival_date_edit.setDate(parsed_date.date())
                        except:
                            pass
                else:
                    self.expected_arrival_date_edit.setDate(arrival_date.date())

            progress.setValue(80)

            # استيراد أرقام الحاويات (دعم الحاويات المتعددة)
            containers_count = 0
            if 'رقم الحاوية' in df.columns and pd.notna(row['رقم الحاوية']):
                container_data = str(row['رقم الحاوية']).strip()
                if container_data:
                    containers = self.parse_multiple_containers(container_data)
                    for container_number in containers:
                        if container_number.strip():
                            self.add_container_from_import(container_number.strip())
                            containers_count += 1

            progress.setValue(100)
            progress.close()

            # رسالة النجاح
            QMessageBox.information(
                self,
                "نجح الاستيراد",
                f"✅ تم استيراد بيانات الشحنة بنجاح من الملف:\n{file_path}\n\n"
                f"البيانات المستوردة:\n"
                f"• التاريخ: {'✓' if 'التاريخ' in df.columns else '✗'}\n"
                f"• المورد: {'✓' if 'المورد' in df.columns else '✗'}\n"
                f"• بوليصة الشحن: {'✓' if 'بوليصة الشحن' in df.columns else '✗'}\n"
                f"• ملاحظات: {'✓' if 'ملاحظات' in df.columns else '✗'}\n"
                f"• حالة الشحنة: {'✓' if 'حالة الشحنة' in df.columns else '✗'}\n"
                f"• حالة الإفراج: {'✓' if 'حالة الإفراج' in df.columns else '✗'}\n"
                f"• شركة الشحن: {'✓' if 'شركة الشحن' in df.columns else '✗'}\n"
                f"• رقم DHL: {'✓' if 'رقم DHL' in df.columns else '✗'}\n"
                f"• ميناء الوصول: {'✓' if 'ميناء الوصول' in df.columns else '✗'}\n"
                f"• تاريخ الوصول المتوقع: {'✓' if 'تاريخ الوصول المتوقع' in df.columns else '✗'}\n"
                f"• الحاويات: {'✓' if 'رقم الحاوية' in df.columns else '✗'}"
                f"{f' ({containers_count} حاوية)' if containers_count > 0 else ''}"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في استيراد الشحنة: {str(e)}")

    def import_multiple_shipments(self, df, file_path):
        """استيراد عدة شحنات (جميع الأسطر)"""
        try:
            from PySide6.QtWidgets import QProgressDialog, QMessageBox
            from PySide6.QtCore import Qt
            from datetime import datetime
            import pandas as pd

            # استيراد النماذج من المسار الصحيح
            from ...database.database_manager import DatabaseManager
            from ...database.models import Shipment, Container, Supplier

            total_rows = len(df)

            # تأكيد العملية
            reply = QMessageBox.question(
                self,
                "تأكيد الاستيراد المتعدد",
                f"هل أنت متأكد من رغبتك في إنشاء {total_rows} شحنة جديدة في قاعدة البيانات؟\n\n"
                f"⚠️ تحذير: هذه العملية لا يمكن التراجع عنها!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # إنشاء شريط التقدم
            progress = QProgressDialog(f"جاري استيراد {total_rows} شحنة...", "إلغاء", 0, total_rows, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # إنشاء جلسة قاعدة البيانات
            db_manager = DatabaseManager()
            session = db_manager.get_session()
            imported_count = 0
            failed_count = 0
            failed_details = []

            try:
                for index, row in df.iterrows():
                    if progress.wasCanceled():
                        break

                    progress.setLabelText(f"جاري استيراد الشحنة {index + 1} من {total_rows}...")
                    progress.setValue(index)

                    try:
                        # إنشاء شحنة جديدة
                        shipment = Shipment()

                        # توليد رقم شحنة تلقائي
                        shipment.shipment_number = self.generate_shipment_number()

                        # استيراد التاريخ
                        if 'التاريخ' in df.columns and pd.notna(row['التاريخ']):
                            date_value = row['التاريخ']
                            if isinstance(date_value, str):
                                try:
                                    shipment.shipment_date = datetime.strptime(date_value, "%Y-%m-%d").date()
                                except:
                                    try:
                                        shipment.shipment_date = datetime.strptime(date_value, "%d/%m/%Y").date()
                                    except:
                                        shipment.shipment_date = datetime.now().date()
                            else:
                                shipment.shipment_date = date_value.date()
                        else:
                            shipment.shipment_date = datetime.now().date()

                        # استيراد المورد
                        if 'المورد' in df.columns and pd.notna(row['المورد']):
                            supplier_name = str(row['المورد']).strip()
                            if supplier_name:
                                supplier = session.query(Supplier).filter(
                                    Supplier.name.ilike(f"%{supplier_name}%")
                                ).first()

                                if supplier:
                                    shipment.supplier_id = supplier.id
                                else:
                                    # إنشاء مورد جديد
                                    new_supplier = Supplier(name=supplier_name)
                                    session.add(new_supplier)
                                    session.flush()
                                    shipment.supplier_id = new_supplier.id

                        # استيراد باقي البيانات الأساسية
                        if 'بوليصة الشحن' in df.columns and pd.notna(row['بوليصة الشحن']):
                            shipment.bill_of_lading = str(row['بوليصة الشحن']).strip()

                        if 'ملاحظات' in df.columns and pd.notna(row['ملاحظات']):
                            shipment.notes = str(row['ملاحظات']).strip()

                        if 'حالة الشحنة' in df.columns and pd.notna(row['حالة الشحنة']):
                            shipment.shipment_status = str(row['حالة الشحنة']).strip()

                        if 'حالة الإفراج' in df.columns and pd.notna(row['حالة الإفراج']):
                            shipment.clearance_status = str(row['حالة الإفراج']).strip()

                        # استيراد بيانات الشحن
                        if 'شركة الشحن' in df.columns and pd.notna(row['شركة الشحن']):
                            shipment.shipping_company = str(row['شركة الشحن']).strip()

                        if 'رقم DHL' in df.columns and pd.notna(row['رقم DHL']):
                            shipment.dhl_number = str(row['رقم DHL']).strip()

                        if 'ميناء الوصول' in df.columns and pd.notna(row['ميناء الوصول']):
                            shipment.arrival_port = str(row['ميناء الوصول']).strip()

                        if 'تاريخ الوصول المتوقع' in df.columns and pd.notna(row['تاريخ الوصول المتوقع']):
                            arrival_date = row['تاريخ الوصول المتوقع']
                            if isinstance(arrival_date, str):
                                try:
                                    shipment.expected_arrival_date = datetime.strptime(arrival_date, "%Y-%m-%d").date()
                                except:
                                    try:
                                        shipment.expected_arrival_date = datetime.strptime(arrival_date, "%d/%m/%Y").date()
                                    except:
                                        pass
                            else:
                                shipment.expected_arrival_date = arrival_date.date()

                        # حفظ الشحنة
                        session.add(shipment)
                        session.flush()

                        # استيراد الحاويات
                        if 'رقم الحاوية' in df.columns and pd.notna(row['رقم الحاوية']):
                            container_data = str(row['رقم الحاوية']).strip()
                            if container_data:
                                containers = self.parse_multiple_containers(container_data)
                                for container_number in containers:
                                    if container_number.strip():
                                        container = Container(
                                            shipment_id=shipment.id,
                                            container_number=container_number.strip(),
                                            container_type="عادي",
                                            container_size="20 قدم",
                                            status="جديد"
                                        )
                                        session.add(container)

                        session.commit()
                        imported_count += 1

                    except Exception as row_error:
                        session.rollback()
                        failed_count += 1
                        failed_details.append(f"الصف {index + 1}: {str(row_error)}")
                        continue

                progress.setValue(total_rows)
                progress.close()

                # رسالة النتيجة
                if imported_count > 0:
                    success_msg = f"✅ تم استيراد {imported_count} شحنة بنجاح!"
                    if failed_count > 0:
                        success_msg += f"\n\n⚠️ فشل في استيراد {failed_count} شحنة:\n"
                        success_msg += "\n".join(failed_details[:5])  # عرض أول 5 أخطاء فقط
                        if len(failed_details) > 5:
                            success_msg += f"\n... و {len(failed_details) - 5} أخطاء أخرى"

                    QMessageBox.information(self, "نتيجة الاستيراد", success_msg)
                else:
                    QMessageBox.critical(
                        self,
                        "فشل الاستيراد",
                        f"❌ فشل في استيراد جميع الشحنات!\n\n"
                        f"الأخطاء:\n" + "\n".join(failed_details[:10])
                    )

            finally:
                session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في الاستيراد المتعدد: {str(e)}")

    def generate_shipment_number(self):
        """توليد رقم شحنة تلقائي"""
        try:
            from src.models.database import get_session
            from src.models.shipment import Shipment
            from datetime import datetime

            session = get_session()

            # الحصول على آخر رقم شحنة
            last_shipment = session.query(Shipment).order_by(Shipment.id.desc()).first()

            if last_shipment and last_shipment.shipment_number:
                try:
                    # استخراج الرقم من آخر شحنة
                    last_number = int(last_shipment.shipment_number.split('-')[-1])
                    new_number = last_number + 1
                except:
                    new_number = 1
            else:
                new_number = 1

            # تنسيق رقم الشحنة
            current_year = datetime.now().year
            shipment_number = f"SH-{current_year}-{new_number:04d}"

            session.close()
            return shipment_number

        except Exception as e:
            print(f"خطأ في توليد رقم الشحنة: {e}")
            from datetime import datetime
            return f"SH-{datetime.now().year}-{datetime.now().microsecond:04d}"

    def add_container_from_import(self, container_number):
        """إضافة حاوية من الاستيراد"""
        try:
            # إضافة صف جديد في جدول الحاويات
            row_count = self.containers_table.rowCount()
            self.containers_table.insertRow(row_count)

            # ملء البيانات الأساسية
            from PySide6.QtWidgets import QTableWidgetItem
            self.containers_table.setItem(row_count, 0, QTableWidgetItem(container_number))  # رقم الحاوية
            self.containers_table.setItem(row_count, 1, QTableWidgetItem("20ft"))  # نوع الحاوية (افتراضي)
            self.containers_table.setItem(row_count, 2, QTableWidgetItem("20"))  # الحجم (افتراضي)
            self.containers_table.setItem(row_count, 3, QTableWidgetItem("2.3"))  # الوزن الفارغ (افتراضي)
            self.containers_table.setItem(row_count, 4, QTableWidgetItem("0"))  # الوزن المحمل
            self.containers_table.setItem(row_count, 5, QTableWidgetItem("فارغة"))  # الحالة (افتراضي)
            self.containers_table.setItem(row_count, 6, QTableWidgetItem(""))  # تاريخ التحميل
            self.containers_table.setItem(row_count, 7, QTableWidgetItem("مستوردة من إكسيل"))  # ملاحظات

            # تحديث عداد الحاويات
            self.update_containers_count()

        except Exception as e:
            print(f"خطأ في إضافة الحاوية من الاستيراد: {e}")

    def parse_multiple_containers(self, container_data):
        """تحليل الحاويات المتعددة من النص"""
        try:
            containers = []

            # قائمة الفواصل المدعومة
            separators = [',', '،', ';', '؛', '\n', '\r\n', '|', '-', '/']

            # تنظيف النص أولاً
            container_data = container_data.strip()

            # البحث عن الفاصل المستخدم
            used_separator = None
            for sep in separators:
                if sep in container_data:
                    used_separator = sep
                    break

            if used_separator:
                # تقسيم النص بناءً على الفاصل المكتشف
                parts = container_data.split(used_separator)
                for part in parts:
                    cleaned_part = part.strip()
                    if cleaned_part:
                        containers.append(cleaned_part)
            else:
                # إذا لم يوجد فاصل، فهي حاوية واحدة
                containers.append(container_data)

            # إزالة التكرارات والحاويات الفارغة
            unique_containers = []
            for container in containers:
                if container and container not in unique_containers:
                    unique_containers.append(container)

            return unique_containers

        except Exception as e:
            print(f"خطأ في تحليل الحاويات المتعددة: {e}")
            # في حالة الخطأ، إرجاع الحاوية كما هي
            return [container_data] if container_data else []

    def update_containers_count(self):
        """تحديث عداد الحاويات"""
        try:
            if hasattr(self, 'containers_table') and hasattr(self, 'total_containers_label'):
                count = self.containers_table.rowCount()
                self.total_containers_label.setText(f"إجمالي الحاويات: {count}")
        except Exception as e:
            print(f"خطأ في تحديث عداد الحاويات: {e}")

def main():
    """اختبار النافذة النهائية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = NewShipmentWindow()
    window.show()
    
    print("✅ تم فتح النافذة النهائية مع أزرار التحكم")
    print("🔍 تحقق من وجود:")
    print("   • عنوان أزرق في الأعلى")
    print("   • شريط أزرار ملون: إضافة (أزرق)، حفظ (أخضر)، تعديل (برتقالي)، خروج (أحمر)")
    print("   • تبويبات: البيانات الأساسية، الأصناف، البيانات المالية")
    print("   • أزرار حفظ وإغلاق/إلغاء في الأسفل")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
