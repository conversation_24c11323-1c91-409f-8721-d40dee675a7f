#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة وتشخيص Oracle - ProShipment V2.0.0
Oracle Monitoring and Diagnostics System
"""

import sys
import os
import time
import psutil
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.universal_database_manager import UniversalDatabaseManager
from src.database.oracle_config import DatabaseConfigManager, DatabaseType

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/oracle_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """مقاييس الأداء"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    active_sessions: int
    response_time: float
    query_count: int
    error_count: int

@dataclass
class ConnectionStatus:
    """حالة الاتصال"""
    is_connected: bool
    response_time: float
    error_message: Optional[str]
    last_check: datetime

class OracleMonitor:
    """مراقب Oracle الشامل"""
    
    def __init__(self):
        self.config_manager = DatabaseConfigManager()
        self.db_manager = None
        self.metrics_history = []
        self.connection_status = None
        self.alerts = []
        
        # إعداد المراقبة
        self.setup_monitoring()
    
    def setup_monitoring(self):
        """إعداد نظام المراقبة"""
        try:
            # تحميل الإعدادات
            config = self.config_manager.load_config()
            
            if config.type == DatabaseType.ORACLE:
                self.db_manager = UniversalDatabaseManager(config)
                logger.info("تم إعداد مراقب Oracle")
            else:
                logger.warning("نوع قاعدة البيانات ليس Oracle")
                
        except Exception as e:
            logger.error(f"خطأ في إعداد المراقبة: {e}")
    
    def check_connection(self) -> ConnectionStatus:
        """فحص حالة الاتصال"""
        start_time = time.time()
        
        try:
            if not self.db_manager:
                return ConnectionStatus(
                    is_connected=False,
                    response_time=0,
                    error_message="مدير قاعدة البيانات غير مهيأ",
                    last_check=datetime.now()
                )
            
            # اختبار الاتصال
            is_connected = self.db_manager.test_connection()
            response_time = time.time() - start_time
            
            status = ConnectionStatus(
                is_connected=is_connected,
                response_time=response_time,
                error_message=None if is_connected else "فشل في الاتصال",
                last_check=datetime.now()
            )
            
            self.connection_status = status
            return status
            
        except Exception as e:
            response_time = time.time() - start_time
            status = ConnectionStatus(
                is_connected=False,
                response_time=response_time,
                error_message=str(e),
                last_check=datetime.now()
            )
            
            self.connection_status = status
            return status
    
    def collect_performance_metrics(self) -> PerformanceMetrics:
        """جمع مقاييس الأداء"""
        try:
            # مقاييس النظام
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # مقاييس قاعدة البيانات
            active_sessions = self.get_active_sessions_count()
            response_time = self.measure_query_response_time()
            query_count = self.get_query_count()
            error_count = self.get_error_count()
            
            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_usage=cpu_usage,
                memory_usage=memory.percent,
                disk_usage=disk.percent,
                active_sessions=active_sessions,
                response_time=response_time,
                query_count=query_count,
                error_count=error_count
            )
            
            # حفظ في التاريخ
            self.metrics_history.append(metrics)
            
            # الاحتفاظ بآخر 1000 قياس فقط
            if len(self.metrics_history) > 1000:
                self.metrics_history = self.metrics_history[-1000:]
            
            return metrics
            
        except Exception as e:
            logger.error(f"خطأ في جمع مقاييس الأداء: {e}")
            return PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_usage=0,
                memory_usage=0,
                disk_usage=0,
                active_sessions=0,
                response_time=0,
                query_count=0,
                error_count=0
            )
    
    def get_active_sessions_count(self) -> int:
        """الحصول على عدد الجلسات النشطة"""
        try:
            if not self.db_manager or not self.db_manager.is_connected:
                return 0
            
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                result = session.execute(text("""
                    SELECT COUNT(*) 
                    FROM v$session 
                    WHERE status = 'ACTIVE' 
                    AND username IS NOT NULL
                """))
                return result.scalar() or 0
                
        except Exception as e:
            logger.debug(f"خطأ في الحصول على عدد الجلسات: {e}")
            return 0
    
    def measure_query_response_time(self) -> float:
        """قياس زمن استجابة الاستعلام"""
        try:
            if not self.db_manager or not self.db_manager.is_connected:
                return 0
            
            start_time = time.time()
            
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                session.execute(text("SELECT 1 FROM DUAL"))
            
            return time.time() - start_time
            
        except Exception as e:
            logger.debug(f"خطأ في قياس زمن الاستجابة: {e}")
            return 0
    
    def get_query_count(self) -> int:
        """الحصول على عدد الاستعلامات"""
        try:
            if not self.db_manager or not self.db_manager.is_connected:
                return 0
            
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                result = session.execute(text("""
                    SELECT value 
                    FROM v$sysstat 
                    WHERE name = 'user calls'
                """))
                return int(result.scalar() or 0)
                
        except Exception as e:
            logger.debug(f"خطأ في الحصول على عدد الاستعلامات: {e}")
            return 0
    
    def get_error_count(self) -> int:
        """الحصول على عدد الأخطاء"""
        try:
            if not self.db_manager or not self.db_manager.is_connected:
                return 0
            
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                result = session.execute(text("""
                    SELECT COUNT(*) 
                    FROM v$diag_alert_ext 
                    WHERE originating_timestamp > SYSDATE - 1/24
                    AND message_type = 1
                """))
                return result.scalar() or 0
                
        except Exception as e:
            logger.debug(f"خطأ في الحصول على عدد الأخطاء: {e}")
            return 0
    
    def check_tablespace_usage(self) -> List[Dict[str, Any]]:
        """فحص استخدام Tablespaces"""
        try:
            if not self.db_manager or not self.db_manager.is_connected:
                return []
            
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                result = session.execute(text("""
                    SELECT 
                        tablespace_name,
                        total_space,
                        used_space,
                        free_space,
                        ROUND((used_space/total_space)*100, 2) as usage_percent
                    FROM (
                        SELECT 
                            df.tablespace_name,
                            df.total_space,
                            NVL(fs.free_space, 0) as free_space,
                            df.total_space - NVL(fs.free_space, 0) as used_space
                        FROM 
                            (SELECT tablespace_name, SUM(bytes)/1024/1024 as total_space
                             FROM dba_data_files GROUP BY tablespace_name) df
                        LEFT JOIN 
                            (SELECT tablespace_name, SUM(bytes)/1024/1024 as free_space
                             FROM dba_free_space GROUP BY tablespace_name) fs
                        ON df.tablespace_name = fs.tablespace_name
                    )
                """))
                
                tablespaces = []
                for row in result:
                    tablespaces.append({
                        'name': row[0],
                        'total_mb': row[1],
                        'used_mb': row[2],
                        'free_mb': row[3],
                        'usage_percent': row[4]
                    })
                
                return tablespaces
                
        except Exception as e:
            logger.debug(f"خطأ في فحص Tablespaces: {e}")
            return []
    
    def check_long_running_queries(self) -> List[Dict[str, Any]]:
        """فحص الاستعلامات طويلة المدى"""
        try:
            if not self.db_manager or not self.db_manager.is_connected:
                return []
            
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                result = session.execute(text("""
                    SELECT 
                        s.sid,
                        s.serial#,
                        s.username,
                        s.program,
                        s.machine,
                        ROUND((SYSDATE - s.logon_time) * 24 * 60, 2) as duration_minutes,
                        sq.sql_text
                    FROM v$session s
                    LEFT JOIN v$sql sq ON s.sql_id = sq.sql_id
                    WHERE s.status = 'ACTIVE'
                    AND s.username IS NOT NULL
                    AND (SYSDATE - s.logon_time) * 24 * 60 > 5
                    ORDER BY duration_minutes DESC
                """))
                
                queries = []
                for row in result:
                    queries.append({
                        'sid': row[0],
                        'serial': row[1],
                        'username': row[2],
                        'program': row[3],
                        'machine': row[4],
                        'duration_minutes': row[5],
                        'sql_text': row[6][:100] if row[6] else None
                    })
                
                return queries
                
        except Exception as e:
            logger.debug(f"خطأ في فحص الاستعلامات طويلة المدى: {e}")
            return []
    
    def generate_health_report(self) -> Dict[str, Any]:
        """إنشاء تقرير صحة النظام"""
        try:
            # فحص الاتصال
            connection_status = self.check_connection()
            
            # جمع مقاييس الأداء
            metrics = self.collect_performance_metrics()
            
            # فحص Tablespaces
            tablespaces = self.check_tablespace_usage()
            
            # فحص الاستعلامات طويلة المدى
            long_queries = self.check_long_running_queries()
            
            # تحليل الحالة العامة
            health_score = self.calculate_health_score(metrics, tablespaces)
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'connection_status': {
                    'connected': connection_status.is_connected,
                    'response_time': connection_status.response_time,
                    'error_message': connection_status.error_message
                },
                'performance_metrics': {
                    'cpu_usage': metrics.cpu_usage,
                    'memory_usage': metrics.memory_usage,
                    'disk_usage': metrics.disk_usage,
                    'active_sessions': metrics.active_sessions,
                    'response_time': metrics.response_time,
                    'query_count': metrics.query_count,
                    'error_count': metrics.error_count
                },
                'tablespaces': tablespaces,
                'long_running_queries': long_queries,
                'health_score': health_score,
                'recommendations': self.generate_recommendations(metrics, tablespaces, long_queries)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير الصحة: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def calculate_health_score(self, metrics: PerformanceMetrics, tablespaces: List[Dict]) -> int:
        """حساب نقاط صحة النظام"""
        score = 100
        
        # خصم نقاط حسب استخدام CPU
        if metrics.cpu_usage > 80:
            score -= 20
        elif metrics.cpu_usage > 60:
            score -= 10
        
        # خصم نقاط حسب استخدام الذاكرة
        if metrics.memory_usage > 90:
            score -= 20
        elif metrics.memory_usage > 70:
            score -= 10
        
        # خصم نقاط حسب زمن الاستجابة
        if metrics.response_time > 2:
            score -= 15
        elif metrics.response_time > 1:
            score -= 5
        
        # خصم نقاط حسب استخدام Tablespaces
        for ts in tablespaces:
            if ts['usage_percent'] > 90:
                score -= 15
            elif ts['usage_percent'] > 80:
                score -= 5
        
        # خصم نقاط حسب عدد الأخطاء
        if metrics.error_count > 10:
            score -= 10
        elif metrics.error_count > 5:
            score -= 5
        
        return max(0, score)
    
    def generate_recommendations(self, metrics: PerformanceMetrics, 
                               tablespaces: List[Dict], 
                               long_queries: List[Dict]) -> List[str]:
        """إنشاء توصيات التحسين"""
        recommendations = []
        
        # توصيات CPU
        if metrics.cpu_usage > 80:
            recommendations.append("استخدام CPU عالي - راجع الاستعلامات المعقدة")
        
        # توصيات الذاكرة
        if metrics.memory_usage > 90:
            recommendations.append("استخدام الذاكرة عالي - فكر في زيادة الذاكرة")
        
        # توصيات زمن الاستجابة
        if metrics.response_time > 2:
            recommendations.append("زمن الاستجابة بطيء - تحقق من الشبكة والفهارس")
        
        # توصيات Tablespaces
        for ts in tablespaces:
            if ts['usage_percent'] > 90:
                recommendations.append(f"Tablespace {ts['name']} ممتلئ تقريباً - أضف مساحة")
        
        # توصيات الاستعلامات طويلة المدى
        if len(long_queries) > 5:
            recommendations.append("عدد كبير من الاستعلامات طويلة المدى - راجع الأداء")
        
        # توصيات الأخطاء
        if metrics.error_count > 5:
            recommendations.append("عدد أخطاء عالي - راجع سجلات Oracle")
        
        if not recommendations:
            recommendations.append("النظام يعمل بشكل جيد")
        
        return recommendations
    
    def start_continuous_monitoring(self, interval_seconds: int = 60):
        """بدء المراقبة المستمرة"""
        logger.info(f"بدء المراقبة المستمرة كل {interval_seconds} ثانية")
        
        try:
            while True:
                # جمع المقاييس
                metrics = self.collect_performance_metrics()
                
                # فحص التنبيهات
                self.check_alerts(metrics)
                
                # انتظار الفترة التالية
                time.sleep(interval_seconds)
                
        except KeyboardInterrupt:
            logger.info("تم إيقاف المراقبة بواسطة المستخدم")
        except Exception as e:
            logger.error(f"خطأ في المراقبة المستمرة: {e}")
    
    def check_alerts(self, metrics: PerformanceMetrics):
        """فحص التنبيهات"""
        alerts = []
        
        # تنبيهات CPU
        if metrics.cpu_usage > 90:
            alerts.append(f"تحذير: استخدام CPU عالي جداً ({metrics.cpu_usage}%)")
        
        # تنبيهات الذاكرة
        if metrics.memory_usage > 95:
            alerts.append(f"تحذير: استخدام الذاكرة عالي جداً ({metrics.memory_usage}%)")
        
        # تنبيهات زمن الاستجابة
        if metrics.response_time > 5:
            alerts.append(f"تحذير: زمن الاستجابة بطيء جداً ({metrics.response_time:.2f}s)")
        
        # حفظ التنبيهات
        for alert in alerts:
            self.alerts.append({
                'timestamp': datetime.now(),
                'message': alert,
                'severity': 'high'
            })
            logger.warning(alert)
        
        # الاحتفاظ بآخر 100 تنبيه فقط
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]

def main():
    """الدالة الرئيسية"""
    print("🔍 نظام مراقبة وتشخيص Oracle - ProShipment V2.0.0")
    print("="*60)
    
    monitor = OracleMonitor()
    
    print("اختر نوع المراقبة:")
    print("1. تقرير صحة فوري")
    print("2. مراقبة مستمرة")
    print("3. فحص الاتصال فقط")
    
    choice = input("اختر رقم (1-3): ").strip()
    
    if choice == "1":
        print("\n📊 إنشاء تقرير صحة النظام...")
        report = monitor.generate_health_report()
        
        print(f"\n🏥 تقرير صحة Oracle")
        print("-" * 40)
        print(f"الوقت: {report['timestamp']}")
        
        if 'connection_status' in report:
            conn = report['connection_status']
            status = "✅ متصل" if conn['connected'] else "❌ غير متصل"
            print(f"حالة الاتصال: {status}")
            print(f"زمن الاستجابة: {conn['response_time']:.3f}s")
            
            if 'performance_metrics' in report:
                perf = report['performance_metrics']
                print(f"استخدام CPU: {perf['cpu_usage']:.1f}%")
                print(f"استخدام الذاكرة: {perf['memory_usage']:.1f}%")
                print(f"الجلسات النشطة: {perf['active_sessions']}")
                
            if 'health_score' in report:
                score = report['health_score']
                print(f"نقاط الصحة: {score}/100")
                
            if 'recommendations' in report:
                print("\n💡 التوصيات:")
                for rec in report['recommendations']:
                    print(f"   • {rec}")
    
    elif choice == "2":
        interval = input("فترة المراقبة بالثواني [60]: ").strip()
        interval = int(interval) if interval else 60
        
        monitor.start_continuous_monitoring(interval)
    
    elif choice == "3":
        print("\n🔌 فحص الاتصال...")
        status = monitor.check_connection()
        
        if status.is_connected:
            print(f"✅ الاتصال نجح في {status.response_time:.3f} ثانية")
        else:
            print(f"❌ فشل الاتصال: {status.error_message}")
    
    else:
        print("❌ خيار غير صحيح")

if __name__ == "__main__":
    main()
