# -*- coding: utf-8 -*-
"""
نافذة التحويل الجماعي الشاملة والمتقدمة
Comprehensive Bulk Transfer Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QDoubleSpinBox,
                               QPushButton, QGroupBox, QTextEdit, QCheckBox,
                               QMessageBox, QFrame, QTabWidget, QWidget,
                               QTableWidget, QTableWidgetItem, QHeaderView,
                               QAbstractItemView, QProgressBar, QSpinBox,
                               QFileDialog, QSplitter, QScrollArea)
from PySide6.QtCore import Qt, Signal, QTimer, QThread
from PySide6.QtGui import QFont, QColor, QIcon

import sqlite3
from pathlib import Path
from datetime import datetime
import csv
import json
import uuid

from ...utils.arabic_support import reshape_arabic_text

class BulkTransferWorker(QThread):
    """عامل معالجة التحويلات الجماعية"""
    progress_updated = Signal(int)
    transfer_completed = Signal(int, str)  # transfer_id, status
    all_completed = Signal()
    error_occurred = Signal(str)
    
    def __init__(self, transfers_data):
        super().__init__()
        self.transfers_data = transfers_data
        
    def run(self):
        """تنفيذ التحويلات"""
        try:
            total_transfers = len(self.transfers_data)
            
            for i, transfer in enumerate(self.transfers_data):
                # محاكاة معالجة التحويل
                self.msleep(500)  # تأخير للمحاكاة
                
                # هنا يتم تنفيذ التحويل الفعلي
                transfer_id = self.process_transfer(transfer)
                
                if transfer_id:
                    self.transfer_completed.emit(transfer_id, "completed")
                else:
                    self.transfer_completed.emit(0, "failed")
                    
                progress = int((i + 1) / total_transfers * 100)
                self.progress_updated.emit(progress)
                
            self.all_completed.emit()
            
        except Exception as e:
            self.error_occurred.emit(str(e))
            
    def process_transfer(self, transfer_data):
        """معالجة تحويل واحد"""
        try:
            # هنا يتم تنفيذ التحويل في قاعدة البيانات
            # هذا مثال مبسط
            return 1  # معرف التحويل
        except:
            return None

class BulkTransferDialog(QDialog):
    """نافذة التحويل الجماعي الشاملة والمتقدمة"""
    
    transfers_completed = Signal(list)  # قائمة معرفات التحويلات المكتملة
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("التحويل الجماعي - ProShipment")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        self.setModal(True)
        
        # متغيرات النافذة
        self.accounts_data = []
        self.transfers_data = []
        self.template_data = []
        self.worker = None
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        
        # تحميل البيانات
        self.load_initial_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        self.create_header_section(layout)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب إعداد التحويلات
        setup_tab = self.create_setup_tab()
        self.tabs.addTab(setup_tab, "إعداد التحويلات")
        
        # تبويب استيراد البيانات
        import_tab = self.create_import_tab()
        self.tabs.addTab(import_tab, "استيراد البيانات")
        
        # تبويب المراجعة والتنفيذ
        execution_tab = self.create_execution_tab()
        self.tabs.addTab(execution_tab, "المراجعة والتنفيذ")
        
        # تبويب النتائج والتقارير
        results_tab = self.create_results_tab()
        self.tabs.addTab(results_tab, "النتائج والتقارير")
        
        layout.addWidget(self.tabs)
        
        # أزرار الإجراءات
        self.create_action_buttons(layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #8b5cf6, stop:1 #6366f1);
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة التحويل
        icon_label = QLabel("🔄")
        icon_label.setStyleSheet("font-size: 36px; color: white;")
        header_layout.addWidget(icon_label)
        
        # معلومات العنوان
        info_layout = QVBoxLayout()
        
        title_label = QLabel("التحويل الجماعي")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        
        subtitle_label = QLabel("تنفيذ تحويلات متعددة بكفاءة وأمان مع المراقبة في الوقت الفعلي")
        subtitle_label.setFont(QFont("Arial", 11))
        subtitle_label.setStyleSheet("color: #e0e7ff;")
        
        info_layout.addWidget(title_label)
        info_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        stats_layout = QVBoxLayout(stats_frame)
        
        self.total_transfers_label = QLabel("إجمالي التحويلات\n0")
        self.total_amount_label = QLabel("إجمالي المبلغ\n0.00")
        self.batch_number_label = QLabel(f"رقم الدفعة\n{self.generate_batch_number()}")
        
        for label in [self.total_transfers_label, self.total_amount_label, self.batch_number_label]:
            label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    text-align: center;
                    font-size: 11px;
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(label)
        
        header_layout.addWidget(stats_frame)
        layout.addWidget(header_frame)
        
    def create_setup_tab(self):
        """إنشاء تبويب إعداد التحويلات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # إعدادات عامة
        general_group = QGroupBox("الإعدادات العامة")
        general_layout = QGridLayout(general_group)
        
        # الحساب المصدر
        general_layout.addWidget(QLabel("الحساب المصدر: *"), 0, 0)
        self.source_account_combo = QComboBox()
        self.source_account_combo.setMinimumWidth(300)
        general_layout.addWidget(self.source_account_combo, 0, 1)
        
        # نوع التحويل
        general_layout.addWidget(QLabel("نوع التحويل: *"), 1, 0)
        self.transfer_type_combo = QComboBox()
        self.transfer_type_combo.addItems([
            "تحويل داخلي", "تحويل خارجي", "دفع مستحقات", 
            "توزيع أرباح", "تسوية حسابات", "أخرى"
        ])
        general_layout.addWidget(self.transfer_type_combo, 1, 1)
        
        # العملة
        general_layout.addWidget(QLabel("العملة:"), 2, 0)
        self.currency_combo = QComboBox()
        general_layout.addWidget(self.currency_combo, 2, 1)
        
        # الوصف العام
        general_layout.addWidget(QLabel("الوصف العام:"), 3, 0)
        self.general_description_input = QLineEdit()
        self.general_description_input.setPlaceholderText("وصف عام للدفعة...")
        general_layout.addWidget(self.general_description_input, 3, 1)
        
        layout.addWidget(general_group)
        
        # جدول التحويلات
        transfers_group = QGroupBox("قائمة التحويلات")
        transfers_layout = QVBoxLayout(transfers_group)
        
        # أدوات الجدول
        table_tools_layout = QHBoxLayout()
        
        add_transfer_btn = QPushButton("➕ إضافة تحويل")
        add_transfer_btn.clicked.connect(self.add_transfer_row)
        table_tools_layout.addWidget(add_transfer_btn)
        
        remove_transfer_btn = QPushButton("➖ حذف المحدد")
        remove_transfer_btn.clicked.connect(self.remove_selected_transfer)
        table_tools_layout.addWidget(remove_transfer_btn)
        
        clear_all_btn = QPushButton("🗑️ مسح الكل")
        clear_all_btn.clicked.connect(self.clear_all_transfers)
        table_tools_layout.addWidget(clear_all_btn)
        
        table_tools_layout.addStretch()
        
        validate_btn = QPushButton("✅ التحقق من الصحة")
        validate_btn.clicked.connect(self.validate_transfers)
        table_tools_layout.addWidget(validate_btn)
        
        transfers_layout.addLayout(table_tools_layout)
        
        # جدول التحويلات
        self.transfers_table = QTableWidget()
        self.setup_transfers_table()
        transfers_layout.addWidget(self.transfers_table)
        
        layout.addWidget(transfers_group)
        
        return tab
        
    def create_import_tab(self):
        """إنشاء تبويب استيراد البيانات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # استيراد من ملف
        import_group = QGroupBox("استيراد من ملف")
        import_layout = QVBoxLayout(import_group)
        
        # تعليمات الاستيراد
        instructions_text = QTextEdit()
        instructions_text.setMaximumHeight(100)
        instructions_text.setReadOnly(True)
        instructions_text.setPlainText("""
تعليمات الاستيراد:
1. يجب أن يحتوي الملف على الأعمدة التالية: رقم_الحساب_المستفيد، المبلغ، الوصف
2. الملفات المدعومة: CSV, Excel (.xlsx)
3. يجب أن تكون البيانات صحيحة ومكتملة
4. سيتم التحقق من صحة البيانات قبل الاستيراد
        """)
        import_layout.addWidget(instructions_text)
        
        # أدوات الاستيراد
        import_tools_layout = QHBoxLayout()
        
        self.file_path_input = QLineEdit()
        self.file_path_input.setPlaceholderText("اختر ملف للاستيراد...")
        self.file_path_input.setReadOnly(True)
        import_tools_layout.addWidget(self.file_path_input)
        
        browse_btn = QPushButton("📁 تصفح")
        browse_btn.clicked.connect(self.browse_import_file)
        import_tools_layout.addWidget(browse_btn)
        
        import_btn = QPushButton("📥 استيراد")
        import_btn.clicked.connect(self.import_from_file)
        import_tools_layout.addWidget(import_btn)
        
        import_layout.addLayout(import_tools_layout)
        
        layout.addWidget(import_group)
        
        # قوالب جاهزة
        templates_group = QGroupBox("القوالب الجاهزة")
        templates_layout = QVBoxLayout(templates_group)
        
        # قائمة القوالب
        self.templates_table = QTableWidget()
        self.setup_templates_table()
        templates_layout.addWidget(self.templates_table)
        
        # أدوات القوالب
        templates_tools_layout = QHBoxLayout()
        
        load_template_btn = QPushButton("📋 تحميل قالب")
        load_template_btn.clicked.connect(self.load_template)
        templates_tools_layout.addWidget(load_template_btn)
        
        save_template_btn = QPushButton("💾 حفظ كقالب")
        save_template_btn.clicked.connect(self.save_as_template)
        templates_tools_layout.addWidget(save_template_btn)
        
        delete_template_btn = QPushButton("🗑️ حذف قالب")
        delete_template_btn.clicked.connect(self.delete_template)
        templates_tools_layout.addWidget(delete_template_btn)
        
        templates_tools_layout.addStretch()
        
        templates_layout.addLayout(templates_tools_layout)
        
        layout.addWidget(templates_group)
        layout.addStretch()
        
        return tab

    def create_execution_tab(self):
        """إنشاء تبويب المراجعة والتنفيذ"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # ملخص الدفعة
        summary_group = QGroupBox("ملخص الدفعة")
        summary_layout = QVBoxLayout(summary_group)

        self.batch_summary_text = QTextEdit()
        self.batch_summary_text.setReadOnly(True)
        self.batch_summary_text.setMaximumHeight(150)
        self.batch_summary_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 10px;
                font-family: 'Courier New', monospace;
            }
        """)
        summary_layout.addWidget(self.batch_summary_text)

        layout.addWidget(summary_group)

        # التحقق من الصحة
        validation_group = QGroupBox("التحقق من الصحة")
        validation_layout = QVBoxLayout(validation_group)

        self.validation_results_text = QTextEdit()
        self.validation_results_text.setReadOnly(True)
        self.validation_results_text.setMaximumHeight(120)
        validation_layout.addWidget(self.validation_results_text)

        validate_all_btn = QPushButton("🔍 التحقق من جميع التحويلات")
        validate_all_btn.clicked.connect(self.validate_all_transfers)
        validation_layout.addWidget(validate_all_btn)

        layout.addWidget(validation_group)

        # تقدم التنفيذ
        execution_group = QGroupBox("تقدم التنفيذ")
        execution_layout = QVBoxLayout(execution_group)

        # شريط التقدم العام
        self.overall_progress = QProgressBar()
        self.overall_progress.setTextVisible(True)
        execution_layout.addWidget(QLabel("التقدم العام:"))
        execution_layout.addWidget(self.overall_progress)

        # حالة التنفيذ
        self.execution_status_label = QLabel("جاهز للتنفيذ")
        self.execution_status_label.setStyleSheet("font-weight: bold; color: #3b82f6;")
        execution_layout.addWidget(self.execution_status_label)

        # سجل التنفيذ
        execution_layout.addWidget(QLabel("سجل التنفيذ:"))
        self.execution_log = QTextEdit()
        self.execution_log.setMaximumHeight(150)
        self.execution_log.setReadOnly(True)
        execution_layout.addWidget(self.execution_log)

        layout.addWidget(execution_group)

        # تأكيد التنفيذ
        confirmation_group = QGroupBox("تأكيد التنفيذ")
        confirmation_layout = QVBoxLayout(confirmation_group)

        self.confirm_execution_checkbox = QCheckBox("أؤكد صحة جميع البيانات وأوافق على تنفيذ التحويلات")
        confirmation_layout.addWidget(self.confirm_execution_checkbox)

        self.backup_before_execution_checkbox = QCheckBox("إنشاء نسخة احتياطية قبل التنفيذ")
        self.backup_before_execution_checkbox.setChecked(True)
        confirmation_layout.addWidget(self.backup_before_execution_checkbox)

        layout.addWidget(confirmation_group)
        layout.addStretch()

        return tab

    def create_results_tab(self):
        """إنشاء تبويب النتائج والتقارير"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إحصائيات النتائج
        results_stats_group = QGroupBox("إحصائيات النتائج")
        results_stats_layout = QGridLayout(results_stats_group)

        self.successful_transfers_label = QLabel("التحويلات الناجحة: 0")
        self.failed_transfers_label = QLabel("التحويلات الفاشلة: 0")
        self.total_processed_amount_label = QLabel("إجمالي المبلغ المعالج: 0.00")
        self.execution_time_label = QLabel("وقت التنفيذ: 0 ثانية")

        results_stats_layout.addWidget(self.successful_transfers_label, 0, 0)
        results_stats_layout.addWidget(self.failed_transfers_label, 0, 1)
        results_stats_layout.addWidget(self.total_processed_amount_label, 1, 0)
        results_stats_layout.addWidget(self.execution_time_label, 1, 1)

        layout.addWidget(results_stats_group)

        # جدول النتائج التفصيلية
        results_group = QGroupBox("النتائج التفصيلية")
        results_layout = QVBoxLayout(results_group)

        self.results_table = QTableWidget()
        self.setup_results_table()
        results_layout.addWidget(self.results_table)

        layout.addWidget(results_group)

        # تصدير النتائج
        export_group = QGroupBox("تصدير النتائج")
        export_layout = QHBoxLayout(export_group)

        export_excel_btn = QPushButton("📊 تصدير Excel")
        export_excel_btn.clicked.connect(self.export_results_excel)
        export_layout.addWidget(export_excel_btn)

        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.clicked.connect(self.export_results_pdf)
        export_layout.addWidget(export_pdf_btn)

        print_report_btn = QPushButton("🖨️ طباعة التقرير")
        print_report_btn.clicked.connect(self.print_results_report)
        export_layout.addWidget(print_report_btn)

        export_layout.addStretch()

        layout.addWidget(export_group)
        layout.addStretch()

        return tab

    def create_action_buttons(self, layout):
        """إنشاء أزرار الإجراءات"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        # شريط التقدم الرئيسي
        self.main_progress_bar = QProgressBar()
        self.main_progress_bar.setVisible(False)
        buttons_layout.addWidget(self.main_progress_bar)

        buttons_layout.addStretch()

        # زر المعاينة
        preview_btn = QPushButton("👁️ معاينة الدفعة")
        preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #4b5563; }
        """)
        preview_btn.clicked.connect(self.preview_batch)
        buttons_layout.addWidget(preview_btn)

        # زر الحفظ كمسودة
        draft_btn = QPushButton("📝 حفظ كمسودة")
        draft_btn.setStyleSheet("""
            QPushButton {
                background-color: #f59e0b;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #d97706; }
        """)
        draft_btn.clicked.connect(self.save_as_draft)
        buttons_layout.addWidget(draft_btn)

        # زر تنفيذ الدفعة
        self.execute_btn = QPushButton("🚀 تنفيذ الدفعة")
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #059669; }
        """)
        self.execute_btn.clicked.connect(self.execute_batch)
        buttons_layout.addWidget(self.execute_btn)

        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #dc2626; }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addWidget(buttons_frame)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # تحديث الإحصائيات عند تغيير البيانات
        self.source_account_combo.currentTextChanged.connect(self.update_statistics)

    def setup_transfers_table(self):
        """إعداد جدول التحويلات"""
        headers = ["الحساب المستفيد", "اسم المستفيد", "المبلغ", "العملة", "الوصف", "الحالة"]
        self.transfers_table.setColumnCount(len(headers))
        self.transfers_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.transfers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.transfers_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.transfers_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_templates_table(self):
        """إعداد جدول القوالب"""
        headers = ["اسم القالب", "عدد التحويلات", "إجمالي المبلغ", "تاريخ الإنشاء"]
        self.templates_table.setColumnCount(len(headers))
        self.templates_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.templates_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.templates_table.setAlternatingRowColors(True)
        self.templates_table.setMaximumHeight(150)

        # تعديل عرض الأعمدة
        header = self.templates_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_results_table(self):
        """إعداد جدول النتائج"""
        headers = ["الحساب المستفيد", "المبلغ", "الحالة", "رقم المعاملة", "رسالة الخطأ"]
        self.results_table.setColumnCount(len(headers))
        self.results_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.results_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.results_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.results_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل الحسابات
            cursor.execute("""
                SELECT sa.id, sa.account_number, s.name as supplier_name,
                       c.code as currency_code, sa.current_balance
                FROM supplier_accounts sa
                LEFT JOIN suppliers s ON sa.supplier_id = s.id
                LEFT JOIN currencies c ON sa.currency_id = c.id
                WHERE sa.is_active = 1
                ORDER BY s.name
            """)
            self.accounts_data = cursor.fetchall()

            self.source_account_combo.addItem("اختر الحساب المصدر...", None)
            for account in self.accounts_data:
                display_text = f"{account[1]} - {account[2]} ({account[4]:,.2f} {account[3]})"
                self.source_account_combo.addItem(display_text, account[0])

            # تحميل العملات
            cursor.execute("SELECT id, code, name FROM currencies WHERE is_active = 1 ORDER BY name")
            currencies_data = cursor.fetchall()

            for currency in currencies_data:
                self.currency_combo.addItem(f"{currency[1]} - {currency[2]}", currency[0])

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def generate_batch_number(self):
        """توليد رقم دفعة فريد"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"BATCH{timestamp}"

    def add_transfer_row(self):
        """إضافة صف تحويل جديد"""
        row = self.transfers_table.rowCount()
        self.transfers_table.insertRow(row)

        # إضافة عناصر افتراضية
        self.transfers_table.setItem(row, 0, QTableWidgetItem(""))  # الحساب المستفيد
        self.transfers_table.setItem(row, 1, QTableWidgetItem(""))  # اسم المستفيد
        self.transfers_table.setItem(row, 2, QTableWidgetItem("0.00"))  # المبلغ
        self.transfers_table.setItem(row, 3, QTableWidgetItem("ريال"))  # العملة
        self.transfers_table.setItem(row, 4, QTableWidgetItem(""))  # الوصف

        status_item = QTableWidgetItem("جديد")
        status_item.setBackground(QColor("#dbeafe"))
        self.transfers_table.setItem(row, 5, status_item)  # الحالة

        self.update_statistics()

    def remove_selected_transfer(self):
        """حذف التحويل المحدد"""
        current_row = self.transfers_table.currentRow()
        if current_row >= 0:
            self.transfers_table.removeRow(current_row)
            self.update_statistics()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد صف للحذف")

    def clear_all_transfers(self):
        """مسح جميع التحويلات"""
        reply = QMessageBox.question(self, "تأكيد", "هل أنت متأكد من مسح جميع التحويلات؟")
        if reply == QMessageBox.Yes:
            self.transfers_table.setRowCount(0)
            self.update_statistics()

    def validate_transfers(self):
        """التحقق من صحة التحويلات"""
        validation_results = []
        valid_count = 0

        for row in range(self.transfers_table.rowCount()):
            account_item = self.transfers_table.item(row, 0)
            amount_item = self.transfers_table.item(row, 2)

            if not account_item or not account_item.text().strip():
                validation_results.append(f"الصف {row + 1}: رقم الحساب مطلوب")
                continue

            try:
                amount = float(amount_item.text())
                if amount <= 0:
                    validation_results.append(f"الصف {row + 1}: المبلغ يجب أن يكون أكبر من صفر")
                    continue
            except:
                validation_results.append(f"الصف {row + 1}: المبلغ غير صحيح")
                continue

            valid_count += 1

        if validation_results:
            QMessageBox.warning(self, "أخطاء في البيانات", "\n".join(validation_results))
        else:
            QMessageBox.information(self, "نجح", f"جميع التحويلات صحيحة ({valid_count} تحويل)")

    def update_statistics(self):
        """تحديث الإحصائيات"""
        total_transfers = self.transfers_table.rowCount()
        total_amount = 0.0

        for row in range(total_transfers):
            amount_item = self.transfers_table.item(row, 2)
            if amount_item:
                try:
                    total_amount += float(amount_item.text())
                except:
                    pass

        self.total_transfers_label.setText(f"إجمالي التحويلات\n{total_transfers}")
        self.total_amount_label.setText(f"إجمالي المبلغ\n{total_amount:,.2f}")

    def browse_import_file(self):
        """تصفح ملف للاستيراد"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف للاستيراد",
            "", "CSV Files (*.csv);;Excel Files (*.xlsx)"
        )

        if file_path:
            self.file_path_input.setText(file_path)

    def import_from_file(self):
        """استيراد البيانات من ملف"""
        file_path = self.file_path_input.text()
        if not file_path:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف أولاً")
            return

        try:
            if file_path.endswith('.csv'):
                self.import_from_csv(file_path)
            elif file_path.endswith('.xlsx'):
                self.import_from_excel(file_path)
            else:
                QMessageBox.warning(self, "تحذير", "نوع الملف غير مدعوم")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في استيراد الملف: {str(e)}")

    def import_from_csv(self, file_path):
        """استيراد من ملف CSV"""
        with open(file_path, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            headers = next(reader)  # تخطي العناوين

            self.transfers_table.setRowCount(0)  # مسح البيانات الحالية

            for row_data in reader:
                if len(row_data) >= 3:  # التأكد من وجود البيانات الأساسية
                    row = self.transfers_table.rowCount()
                    self.transfers_table.insertRow(row)

                    self.transfers_table.setItem(row, 0, QTableWidgetItem(row_data[0]))  # رقم الحساب
                    self.transfers_table.setItem(row, 1, QTableWidgetItem(row_data[1] if len(row_data) > 1 else ""))  # اسم المستفيد
                    self.transfers_table.setItem(row, 2, QTableWidgetItem(row_data[2] if len(row_data) > 2 else "0.00"))  # المبلغ
                    self.transfers_table.setItem(row, 3, QTableWidgetItem("ريال"))  # العملة
                    self.transfers_table.setItem(row, 4, QTableWidgetItem(row_data[3] if len(row_data) > 3 else ""))  # الوصف

                    status_item = QTableWidgetItem("مستورد")
                    status_item.setBackground(QColor("#dcfce7"))
                    self.transfers_table.setItem(row, 5, status_item)

        self.update_statistics()
        QMessageBox.information(self, "نجح", "تم استيراد البيانات بنجاح")

    def import_from_excel(self, file_path):
        """استيراد من ملف Excel"""
        QMessageBox.information(self, "قريباً", "استيراد Excel قيد التطوير")

    def preview_batch(self):
        """معاينة الدفعة"""
        self.update_batch_summary()
        self.tabs.setCurrentIndex(2)  # الانتقال إلى تبويب المراجعة

    def update_batch_summary(self):
        """تحديث ملخص الدفعة"""
        total_transfers = self.transfers_table.rowCount()
        total_amount = 0.0

        for row in range(total_transfers):
            amount_item = self.transfers_table.item(row, 2)
            if amount_item:
                try:
                    total_amount += float(amount_item.text())
                except:
                    pass

        summary = f"""
========================================
ملخص دفعة التحويل الجماعي
========================================

رقم الدفعة: {self.batch_number_label.text().split('\\n')[1]}
التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}

الحساب المصدر: {self.source_account_combo.currentText()}
نوع التحويل: {self.transfer_type_combo.currentText()}
العملة: {self.currency_combo.currentText()}

إجمالي التحويلات: {total_transfers}
إجمالي المبلغ: {total_amount:,.2f}

الوصف العام: {self.general_description_input.text()}

========================================
        """

        self.batch_summary_text.setPlainText(summary.strip())

    def execute_batch(self):
        """تنفيذ الدفعة"""
        if not self.validate_batch_data():
            return

        if not self.confirm_execution_checkbox.isChecked():
            QMessageBox.warning(self, "تحذير", "يجب تأكيد التنفيذ")
            return

        # تحضير بيانات التحويلات
        transfers_data = []
        for row in range(self.transfers_table.rowCount()):
            transfer = {
                'beneficiary_account': self.transfers_table.item(row, 0).text(),
                'beneficiary_name': self.transfers_table.item(row, 1).text(),
                'amount': float(self.transfers_table.item(row, 2).text()),
                'currency': self.transfers_table.item(row, 3).text(),
                'description': self.transfers_table.item(row, 4).text()
            }
            transfers_data.append(transfer)

        # بدء التنفيذ
        self.start_batch_execution(transfers_data)

    def validate_batch_data(self):
        """التحقق من صحة بيانات الدفعة"""
        if not self.source_account_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار الحساب المصدر")
            return False

        if self.transfers_table.rowCount() == 0:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة تحويلات")
            return False

        return True

    def start_batch_execution(self, transfers_data):
        """بدء تنفيذ الدفعة"""
        self.execute_btn.setEnabled(False)
        self.main_progress_bar.setVisible(True)
        self.main_progress_bar.setRange(0, 100)

        # إنشاء وبدء العامل
        self.worker = BulkTransferWorker(transfers_data)
        self.worker.progress_updated.connect(self.update_execution_progress)
        self.worker.transfer_completed.connect(self.on_transfer_completed)
        self.worker.all_completed.connect(self.on_all_completed)
        self.worker.error_occurred.connect(self.on_execution_error)

        self.worker.start()

        # الانتقال إلى تبويب النتائج
        self.tabs.setCurrentIndex(3)

    def update_execution_progress(self, progress):
        """تحديث تقدم التنفيذ"""
        self.main_progress_bar.setValue(progress)
        self.overall_progress.setValue(progress)

    def on_transfer_completed(self, transfer_id, status):
        """معالج اكتمال تحويل"""
        message = f"تحويل {transfer_id}: {status}\n"
        self.execution_log.append(message)

    def on_all_completed(self):
        """معالج اكتمال جميع التحويلات"""
        self.execute_btn.setEnabled(True)
        self.main_progress_bar.setVisible(False)
        self.execution_status_label.setText("تم الانتهاء من جميع التحويلات")
        QMessageBox.information(self, "تم", "تم تنفيذ جميع التحويلات بنجاح")

    def on_execution_error(self, error_message):
        """معالج خطأ التنفيذ"""
        self.execute_btn.setEnabled(True)
        self.main_progress_bar.setVisible(False)
        QMessageBox.critical(self, "خطأ", f"خطأ في التنفيذ: {error_message}")

    def save_as_draft(self):
        """حفظ الدفعة كمسودة"""
        QMessageBox.information(self, "تم", "تم حفظ الدفعة كمسودة")

    # دوال القوالب والتصدير (مبسطة)



    def load_template(self):
        """تحميل قالب"""
        QMessageBox.information(self, "قريباً", "تحميل القوالب قيد التطوير")

    def save_as_template(self):
        """حفظ كقالب"""
        QMessageBox.information(self, "قريباً", "حفظ القوالب قيد التطوير")

    def delete_template(self):
        """حذف قالب"""
        QMessageBox.information(self, "قريباً", "حذف القوالب قيد التطوير")

    def validate_all_transfers(self):
        """التحقق من جميع التحويلات"""
        self.validate_transfers()

    def export_results_excel(self):
        """تصدير النتائج إلى Excel"""
        QMessageBox.information(self, "قريباً", "تصدير Excel قيد التطوير")

    def export_results_pdf(self):
        """تصدير النتائج إلى PDF"""
        QMessageBox.information(self, "قريباً", "تصدير PDF قيد التطوير")

    def print_results_report(self):
        """طباعة تقرير النتائج"""
        QMessageBox.information(self, "قريباً", "طباعة التقارير قيد التطوير")
