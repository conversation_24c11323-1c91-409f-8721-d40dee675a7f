# -*- coding: utf-8 -*-
"""
ويدجت إدارة الأصناف
Items Management Widget
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QTextEdit, QCheckBox,
                               QComboBox, QSpinBox, QDoubleSpinBox, QScrollArea)
from PySide6.QtCore import Qt
import pandas as pd
import os
from datetime import datetime

from ...database.database_manager import DatabaseManager
from ...database.models import Item, ItemGroup, UnitOfMeasure

class ItemsManagementWidget(QWidget):
    """ويدجت إدارة الأصناف"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_item_id = None
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(15)
        
        # الجانب الأيسر - نموذج الإدخال
        form_group = QGroupBox("بيانات الصنف")
        form_group.setMaximumWidth(600)  # زيادة العرض
        form_layout = QVBoxLayout(form_group)

        # إنشاء منطقة التمرير للنموذج
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(15)
        scroll_layout.setContentsMargins(10, 10, 10, 10)
        
        # البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(10)
        basic_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        
        self.item_name_edit = QLineEdit()
        self.item_name_edit.setPlaceholderText("اسم الصنف")
        self.item_name_edit.setMinimumHeight(35)
        basic_layout.addRow("اسم الصنف:", self.item_name_edit)

        self.item_code_edit = QLineEdit()
        self.item_code_edit.setPlaceholderText("كود الصنف")
        self.item_code_edit.setMinimumHeight(35)
        basic_layout.addRow("كود الصنف:", self.item_code_edit)

        self.item_name_en_edit = QLineEdit()
        self.item_name_en_edit.setPlaceholderText("الاسم الإنجليزي")
        self.item_name_en_edit.setMinimumHeight(35)
        basic_layout.addRow("الاسم الإنجليزي:", self.item_name_en_edit)

        self.barcode_edit = QLineEdit()
        self.barcode_edit.setPlaceholderText("الباركود")
        self.barcode_edit.setMinimumHeight(35)
        basic_layout.addRow("الباركود:", self.barcode_edit)

        self.group_combo = QComboBox()
        self.group_combo.setMinimumHeight(35)
        self.load_item_groups()
        basic_layout.addRow("مجموعة الصنف:", self.group_combo)

        self.unit_combo = QComboBox()
        self.unit_combo.setMinimumHeight(35)
        self.load_units()
        basic_layout.addRow("وحدة القياس:", self.unit_combo)
        
        scroll_layout.addWidget(basic_group)
        
        # الأسعار والتكاليف
        pricing_group = QGroupBox("الأسعار والتكاليف")
        pricing_layout = QFormLayout(pricing_group)
        pricing_layout.setSpacing(10)
        pricing_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        
        self.cost_price_edit = QDoubleSpinBox()
        self.cost_price_edit.setMaximum(999999.99)
        self.cost_price_edit.setSuffix(" ريال")
        self.cost_price_edit.setDecimals(2)
        self.cost_price_edit.setMinimumHeight(35)
        pricing_layout.addRow("سعر التكلفة:", self.cost_price_edit)

        self.selling_price_edit = QDoubleSpinBox()
        self.selling_price_edit.setMaximum(999999.99)
        self.selling_price_edit.setSuffix(" ريال")
        self.selling_price_edit.setDecimals(2)
        self.selling_price_edit.setMinimumHeight(35)
        pricing_layout.addRow("سعر البيع:", self.selling_price_edit)

        self.wholesale_price_edit = QDoubleSpinBox()
        self.wholesale_price_edit.setMaximum(999999.99)
        self.wholesale_price_edit.setSuffix(" ريال")
        self.wholesale_price_edit.setDecimals(2)
        self.wholesale_price_edit.setMinimumHeight(35)
        pricing_layout.addRow("سعر الجملة:", self.wholesale_price_edit)
        
        scroll_layout.addWidget(pricing_group)

        # المخزون
        inventory_group = QGroupBox("المخزون")
        inventory_layout = QFormLayout(inventory_group)
        inventory_layout.setSpacing(10)
        inventory_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        
        self.current_stock_edit = QSpinBox()
        self.current_stock_edit.setMaximum(999999)
        self.current_stock_edit.setMinimumHeight(35)
        inventory_layout.addRow("الكمية الحالية:", self.current_stock_edit)

        self.min_stock_edit = QSpinBox()
        self.min_stock_edit.setMaximum(999999)
        self.min_stock_edit.setMinimumHeight(35)
        inventory_layout.addRow("الحد الأدنى:", self.min_stock_edit)

        self.max_stock_edit = QSpinBox()
        self.max_stock_edit.setMaximum(999999)
        self.max_stock_edit.setMinimumHeight(35)
        inventory_layout.addRow("الحد الأقصى:", self.max_stock_edit)
        
        scroll_layout.addWidget(inventory_group)

        # العبوات والأوزان
        packaging_group = QGroupBox("العبوات والأوزان")
        packaging_layout = QFormLayout(packaging_group)
        packaging_layout.setSpacing(10)
        packaging_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        self.total_package_spin = QSpinBox()
        self.total_package_spin.setMaximum(999999)
        self.total_package_spin.setSuffix(" قطعة")
        self.total_package_spin.setMinimumHeight(35)
        packaging_layout.addRow("عبوة الكلي:", self.total_package_spin)

        self.partial_package_spin = QSpinBox()
        self.partial_package_spin.setMaximum(999999)
        self.partial_package_spin.setSuffix(" قطعة")
        self.partial_package_spin.setMinimumHeight(35)
        packaging_layout.addRow("عبوة الجزئي:", self.partial_package_spin)

        self.gram_weight_spin = QDoubleSpinBox()
        self.gram_weight_spin.setMaximum(999999.99)
        self.gram_weight_spin.setSuffix(" جرام")
        self.gram_weight_spin.setDecimals(2)
        self.gram_weight_spin.setMinimumHeight(35)
        packaging_layout.addRow("الجرام:", self.gram_weight_spin)

        self.item_weight_spin = QDoubleSpinBox()
        self.item_weight_spin.setMaximum(999999.99)
        self.item_weight_spin.setSuffix(" كجم")
        self.item_weight_spin.setDecimals(3)
        self.item_weight_spin.setMinimumHeight(35)
        packaging_layout.addRow("الوزن:", self.item_weight_spin)

        scroll_layout.addWidget(packaging_group)

        # معلومات إضافية
        additional_group = QGroupBox("معلومات إضافية")
        additional_layout = QFormLayout(additional_group)
        additional_layout.setSpacing(10)
        additional_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(70)
        self.description_edit.setMinimumHeight(70)
        self.description_edit.setPlaceholderText("وصف الصنف")
        additional_layout.addRow("الوصف:", self.description_edit)

        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(70)
        self.notes_edit.setMinimumHeight(70)
        self.notes_edit.setPlaceholderText("ملاحظات")
        additional_layout.addRow("ملاحظات:", self.notes_edit)

        self.dimensions_edit = QLineEdit()
        self.dimensions_edit.setPlaceholderText("الأبعاد (الطول × العرض × الارتفاع)")
        self.dimensions_edit.setMinimumHeight(35)
        additional_layout.addRow("الأبعاد:", self.dimensions_edit)

        self.is_active_check = QCheckBox("نشط")
        self.is_active_check.setChecked(True)
        additional_layout.addRow("", self.is_active_check)
        
        scroll_layout.addWidget(additional_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        self.add_button = QPushButton("إضافة")
        self.add_button.clicked.connect(self.add_item)
        self.add_button.setMinimumHeight(40)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.update_button = QPushButton("تحديث")
        self.update_button.clicked.connect(self.update_item)
        self.update_button.setEnabled(False)
        self.update_button.setMinimumHeight(40)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        
        self.clear_button = QPushButton("مسح")
        self.clear_button.clicked.connect(self.clear_form)
        self.clear_button.setMinimumHeight(40)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.clear_button)

        scroll_layout.addLayout(buttons_layout)

        # إعداد منطقة التمرير
        scroll_area.setWidget(scroll_widget)
        form_layout.addWidget(scroll_area)

        main_layout.addWidget(form_group)
        
        # الجانب الأيمن - جدول الأصناف
        table_group = QGroupBox("الأصناف المسجلة")
        table_layout = QVBoxLayout(table_group)
        
        # فلاتر البحث
        search_layout = QHBoxLayout()
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في الأصناف...")
        self.search_edit.textChanged.connect(self.filter_items)
        
        self.group_filter_combo = QComboBox()
        self.group_filter_combo.addItem("جميع المجموعات", None)
        self.load_groups_for_filter()
        self.group_filter_combo.currentTextChanged.connect(self.filter_items)
        
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(QLabel("المجموعة:"))
        search_layout.addWidget(self.group_filter_combo)
        
        table_layout.addLayout(search_layout)
        
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(8)
        self.items_table.setHorizontalHeaderLabels([
            "اسم الصنف", "الكود", "المجموعة", "الوحدة", "سعر البيع", 
            "الكمية الحالية", "الحد الأدنى", "الحالة"
        ])
        
        # تنسيق الجدول
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.items_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        table_layout.addWidget(self.items_table)
        
        # أزرار إدارة الجدول
        table_buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_data)
        
        edit_button = QPushButton("تعديل المحدد")
        edit_button.clicked.connect(self.edit_selected)
        
        delete_button = QPushButton("حذف المحدد")
        delete_button.clicked.connect(self.delete_selected)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(edit_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addStretch()
        
        table_layout.addLayout(table_buttons_layout)
        main_layout.addWidget(table_group)

        # تحميل البيانات الأولية
        self.load_data()

    def load_item_groups(self):
        """تحميل مجموعات الأصناف"""
        session = self.db_manager.get_session()
        try:
            groups = session.query(ItemGroup).filter_by(is_active=True).all()
            self.group_combo.clear()
            self.group_combo.addItem("-- اختر المجموعة --", None)
            for group in groups:
                self.group_combo.addItem(group.name, group.id)
        except Exception as e:
            print(f"خطأ في تحميل مجموعات الأصناف: {e}")
        finally:
            session.close()

    def load_units(self):
        """تحميل وحدات القياس"""
        session = self.db_manager.get_session()
        try:
            units = session.query(UnitOfMeasure).filter_by(is_active=True).all()
            self.unit_combo.clear()
            self.unit_combo.addItem("-- اختر الوحدة --", None)
            for unit in units:
                self.unit_combo.addItem(unit.name, unit.id)
        except Exception as e:
            print(f"خطأ في تحميل وحدات القياس: {e}")
        finally:
            session.close()

    def load_data(self):
        """تحميل بيانات الأصناف"""
        session = self.db_manager.get_session()
        try:
            items = session.query(Item).filter_by(is_active=True).all()

            self.items_table.setRowCount(len(items))

            for row, item in enumerate(items):
                self.items_table.setItem(row, 0, QTableWidgetItem(item.name or ""))
                self.items_table.setItem(row, 1, QTableWidgetItem(item.code or ""))

                # مجموعة الصنف
                group_name = item.group.name if item.group else "غير محدد"
                self.items_table.setItem(row, 2, QTableWidgetItem(group_name))

                # وحدة القياس
                unit_name = item.unit.name if item.unit else "غير محدد"
                self.items_table.setItem(row, 3, QTableWidgetItem(unit_name))

                # سعر البيع
                selling_price = f"{item.selling_price:.2f}" if item.selling_price else "0.00"
                self.items_table.setItem(row, 4, QTableWidgetItem(selling_price))

                # الكمية الحالية (افتراضية)
                self.items_table.setItem(row, 5, QTableWidgetItem("0"))

                # الحد الأدنى (افتراضي)
                self.items_table.setItem(row, 6, QTableWidgetItem("0"))

                # الحالة
                status = "نشط" if item.is_active else "غير نشط"
                self.items_table.setItem(row, 7, QTableWidgetItem(status))

                # حفظ معرف الصنف ومعرف المجموعة
                self.items_table.item(row, 0).setData(Qt.UserRole, item.id)
                self.items_table.item(row, 2).setData(Qt.UserRole, item.group_id)

        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل بيانات الأصناف:\n{str(e)}"
            )
        finally:
            session.close()

    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.item_name_edit.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم الصنف")
            return False

        if not self.item_code_edit.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال كود الصنف")
            return False

        if self.group_combo.currentData() is None:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى اختيار مجموعة الصنف")
            return False

        if self.unit_combo.currentData() is None:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى اختيار وحدة القياس")
            return False

        return True

    def add_item(self):
        """إضافة صنف جديد"""
        if not self.validate_form():
            return

        session = self.db_manager.get_session()
        try:
            # التحقق من عدم وجود الكود مسبقاً
            existing = session.query(Item).filter_by(code=self.code_edit.text()).first()
            if existing:
                QMessageBox.warning(self, "كود موجود", "هذا الكود موجود بالفعل")
                return

            # إنشاء الصنف الجديد
            new_item = Item(
                name=self.item_name_edit.text(),
                name_en=self.item_name_en_edit.text(),
                code=self.item_code_edit.text(),
                description=self.description_edit.toPlainText(),
                group_id=self.group_combo.currentData(),
                unit_id=self.unit_combo.currentData(),
                cost_price=self.cost_price_edit.value(),
                selling_price=self.selling_price_edit.value(),
                weight=self.item_weight_spin.value() if self.item_weight_spin.value() > 0 else None,
                dimensions=self.dimensions_edit.text() if self.dimensions_edit.text().strip() else None,
                total_package=self.total_package_spin.value(),
                partial_package=self.partial_package_spin.value(),
                gram_weight=self.gram_weight_spin.value(),
                item_weight=self.item_weight_spin.value(),
                is_active=self.is_active_check.isChecked()
            )

            session.add(new_item)
            session.commit()

            QMessageBox.information(self, "تم الإضافة", "تم إضافة الصنف بنجاح")

            self.clear_form()
            self.load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في الإضافة", f"حدث خطأ أثناء إضافة الصنف:\n{str(e)}")
        finally:
            session.close()

    def update_item(self):
        """تحديث الصنف"""
        if not self.current_item_id or not self.validate_form():
            return

        session = self.db_manager.get_session()
        try:
            item = session.query(Item).get(self.current_item_id)
            if item:
                item.name = self.item_name_edit.text()
                item.name_en = self.item_name_en_edit.text()
                item.code = self.item_code_edit.text()
                item.description = self.description_edit.toPlainText()
                item.group_id = self.group_combo.currentData()
                item.unit_id = self.unit_combo.currentData()
                item.cost_price = self.cost_price_edit.value()
                item.selling_price = self.selling_price_edit.value()
                item.weight = self.item_weight_spin.value() if self.item_weight_spin.value() > 0 else None
                item.dimensions = self.dimensions_edit.text() if self.dimensions_edit.text().strip() else None
                item.total_package = self.total_package_spin.value()
                item.partial_package = self.partial_package_spin.value()
                item.gram_weight = self.gram_weight_spin.value()
                item.item_weight = self.item_weight_spin.value()
                item.is_active = self.is_active_check.isChecked()

                session.commit()

                QMessageBox.information(self, "تم التحديث", "تم تحديث الصنف بنجاح")

                self.clear_form()
                self.load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في التحديث", f"حدث خطأ أثناء تحديث الصنف:\n{str(e)}")
        finally:
            session.close()

    def clear_form(self):
        """مسح النموذج"""
        self.current_item_id = None
        self.item_name_edit.clear()
        self.item_name_en_edit.clear()
        self.item_code_edit.clear()
        self.description_edit.clear()
        self.group_combo.setCurrentIndex(0)
        self.unit_combo.setCurrentIndex(0)
        self.cost_price_edit.setValue(0.0)
        self.selling_price_edit.setValue(0.0)
        self.dimensions_edit.clear()
        self.total_package_spin.setValue(0)
        self.partial_package_spin.setValue(0)
        self.gram_weight_spin.setValue(0.0)
        self.item_weight_spin.setValue(0.0)
        self.is_active_check.setChecked(True)

        self.add_button.setEnabled(True)
        self.update_button.setEnabled(False)

    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            self.edit_selected()

    def edit_selected(self):
        """تعديل الصنف المحدد"""
        current_row = self.items_table.currentRow()
        if current_row < 0:
            return

        item_id = self.items_table.item(current_row, 0).data(Qt.UserRole)

        session = self.db_manager.get_session()
        try:
            item = session.query(Item).get(item_id)
            if item:
                self.current_item_id = item.id
                self.item_name_edit.setText(item.name or "")
                self.item_name_en_edit.setText(item.name_en or "")
                self.item_code_edit.setText(item.code or "")
                self.description_edit.setPlainText(item.description or "")

                # تحديد المجموعة
                for i in range(self.group_combo.count()):
                    if self.group_combo.itemData(i) == item.group_id:
                        self.group_combo.setCurrentIndex(i)
                        break

                # تحديد الوحدة
                for i in range(self.unit_combo.count()):
                    if self.unit_combo.itemData(i) == item.unit_id:
                        self.unit_combo.setCurrentIndex(i)
                        break

                self.cost_price_edit.setValue(item.cost_price or 0.0)
                self.selling_price_edit.setValue(item.selling_price or 0.0)
                self.dimensions_edit.setText(item.dimensions or "")
                self.total_package_spin.setValue(item.total_package or 0)
                self.partial_package_spin.setValue(item.partial_package or 0)
                self.gram_weight_spin.setValue(item.gram_weight or 0.0)
                self.item_weight_spin.setValue(item.item_weight or 0.0)
                self.is_active_check.setChecked(item.is_active)

                self.add_button.setEnabled(False)
                self.update_button.setEnabled(True)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الصنف:\n{str(e)}")
        finally:
            session.close()

    def delete_selected(self):
        """حذف الصنف المحدد"""
        current_row = self.items_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "لا يوجد تحديد", "يرجى تحديد صنف للحذف")
            return

        item_id = self.items_table.item(current_row, 0).data(Qt.UserRole)
        item_name = self.items_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف الصنف '{item_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                item = session.query(Item).get(item_id)
                if item:
                    item.is_active = False  # حذف منطقي
                    session.commit()

                    QMessageBox.information(self, "تم الحذف", f"تم حذف الصنف '{item_name}' بنجاح")
                    self.load_data()
                    self.clear_form()

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف الصنف:\n{str(e)}")
            finally:
                session.close()

    def load_groups_for_filter(self):
        """تحميل المجموعات لتصفية البحث"""
        session = self.db_manager.get_session()
        try:
            groups = session.query(ItemGroup).filter_by(is_active=True).all()
            for group in groups:
                self.group_filter_combo.addItem(group.name, group.id)
        except Exception as e:
            print(f"خطأ في تحميل مجموعات التصفية: {e}")
        finally:
            session.close()

    def filter_items(self):
        """تصفية الأصناف حسب النص والمجموعة"""
        search_text = self.search_edit.text().lower()
        selected_group_id = self.group_filter_combo.currentData()

        for row in range(self.items_table.rowCount()):
            show_row = True

            # تصفية حسب النص
            if search_text:
                item_name = self.items_table.item(row, 0).text().lower()
                item_code = self.items_table.item(row, 1).text().lower()
                if search_text not in item_name and search_text not in item_code:
                    show_row = False

            # تصفية حسب المجموعة
            if selected_group_id is not None and show_row:
                item_group_id = self.items_table.item(row, 2).data(Qt.UserRole)
                if item_group_id != selected_group_id:
                    show_row = False

            self.items_table.setRowHidden(row, not show_row)

    def import_items(self):
        """استيراد الأصناف من ملف إكسيل"""
        from PySide6.QtWidgets import QFileDialog, QDialog, QVBoxLayout, QLabel, QRadioButton, QDialogButtonBox

        # نافذة خيارات الاستيراد
        options_dialog = QDialog(self)
        options_dialog.setWindowTitle("خيارات الاستيراد")
        options_dialog.setModal(True)
        options_dialog.resize(400, 200)

        layout = QVBoxLayout(options_dialog)

        # عنوان
        title_label = QLabel("ماذا تريد أن تفعل مع الأصناف الموجودة؟")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(title_label)

        # خيارات
        update_existing_radio = QRadioButton("تحديث الأصناف الموجودة")
        update_existing_radio.setChecked(True)  # الخيار الافتراضي

        skip_existing_radio = QRadioButton("تجاهل الأصناف الموجودة")

        layout.addWidget(update_existing_radio)
        layout.addWidget(skip_existing_radio)

        # أزرار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(options_dialog.accept)
        buttons.rejected.connect(options_dialog.reject)
        layout.addWidget(buttons)

        if options_dialog.exec() != QDialog.Accepted:
            return

        # اختيار الملف
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار ملف الإكسيل", "",
            "ملفات الإكسيل (*.xlsx *.xls)"
        )

        if file_path:
            update_existing = update_existing_radio.isChecked()
            success_count, error_count, errors = self.import_from_excel(file_path, update_existing)

            # عرض النتائج
            if error_count == 0:
                QMessageBox.information(
                    self, "تم الاستيراد بنجاح",
                    f"تم استيراد {success_count} صنف بنجاح"
                )
            else:
                error_details = "\n".join(errors[:10])  # عرض أول 10 أخطاء فقط
                if len(errors) > 10:
                    error_details += f"\n... و {len(errors) - 10} أخطاء أخرى"

                QMessageBox.warning(
                    self, "تم الاستيراد مع أخطاء",
                    f"تم استيراد {success_count} صنف بنجاح\n"
                    f"فشل في استيراد {error_count} صنف\n\n"
                    f"الأخطاء:\n{error_details}"
                )

            # تحديث الجدول
            self.load_items()

    def import_from_excel(self, file_path, update_existing=True):
        """استيراد البيانات من ملف إكسيل"""
        success_count = 0
        error_count = 0
        errors = []

        try:
            # قراءة ملف الإكسيل
            df = pd.read_excel(file_path)

            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['اسم الصنف', 'كود الصنف']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                errors.append(f"الأعمدة المطلوبة مفقودة: {', '.join(missing_columns)}")
                return 0, 1, errors

            session = self.db_manager.get_session()

            try:
                # تحميل المجموعات ووحدات القياس للمطابقة
                groups_dict = {}
                groups = session.query(ItemGroup).filter_by(is_active=True).all()
                for group in groups:
                    groups_dict[group.name] = group.id
                    if group.code:
                        groups_dict[group.code] = group.id

                units_dict = {}
                units = session.query(UnitOfMeasure).filter_by(is_active=True).all()
                for unit in units:
                    units_dict[unit.name] = unit.id
                    if unit.symbol:
                        units_dict[unit.symbol] = unit.id

                # تتبع الأكواد المستخدمة في الملف لتجنب التكرار
                used_codes_in_file = set()

                # معالجة كل صف
                for index, row in df.iterrows():
                    try:
                        # التحقق من البيانات الأساسية
                        item_name = str(row['اسم الصنف']).strip()
                        item_code = str(row['كود الصنف']).strip()

                        if not item_name or item_name == 'nan':
                            errors.append(f"الصف {index + 2}: اسم الصنف مطلوب")
                            error_count += 1
                            continue

                        if not item_code or item_code == 'nan':
                            errors.append(f"الصف {index + 2}: كود الصنف مطلوب")
                            error_count += 1
                            continue

                        # التحقق من عدم تكرار الكود في الملف نفسه
                        if item_code in used_codes_in_file:
                            errors.append(f"الصف {index + 2}: كود الصنف '{item_code}' مكرر في الملف")
                            error_count += 1
                            continue

                        used_codes_in_file.add(item_code)

                        # التحقق من عدم وجود الكود مسبقاً
                        existing_item = session.query(Item).filter_by(code=item_code).first()
                        if existing_item:
                            if update_existing:
                                # تحديث البيانات الموجودة
                                try:
                                    existing_item.name = item_name
                                    if item_name_en:
                                        existing_item.name_en = item_name_en
                                    if description:
                                        existing_item.description = description
                                    if group_id:
                                        existing_item.group_id = group_id
                                    if unit_id:
                                        existing_item.unit_id = unit_id
                                    existing_item.cost_price = cost_price
                                    existing_item.selling_price = selling_price
                                    existing_item.weight = weight
                                    existing_item.dimensions = dimensions
                                    existing_item.total_package = total_package
                                    existing_item.partial_package = partial_package
                                    existing_item.gram_weight = gram_weight
                                    existing_item.item_weight = item_weight
                                    existing_item.updated_at = datetime.now()

                                    success_count += 1
                                    continue
                                except Exception as update_error:
                                    errors.append(f"الصف {index + 2}: فشل في تحديث الصنف '{item_code}' - {str(update_error)}")
                                    error_count += 1
                                    continue
                            else:
                                # تجاهل الصنف الموجود
                                errors.append(f"الصف {index + 2}: كود الصنف '{item_code}' موجود بالفعل - تم التجاهل")
                                error_count += 1
                                continue

                        # استخراج البيانات الاختيارية
                        item_name_en = str(row.get('الاسم الإنجليزي', '')).strip()
                        if item_name_en == 'nan':
                            item_name_en = ''

                        description = str(row.get('الوصف', '')).strip()
                        if description == 'nan':
                            description = ''

                        # معالجة المجموعة
                        group_id = None
                        group_name = str(row.get('المجموعة', '')).strip()
                        if group_name and group_name != 'nan':
                            group_id = groups_dict.get(group_name)
                            if not group_id:
                                errors.append(f"الصف {index + 2}: المجموعة '{group_name}' غير موجودة")

                        # معالجة وحدة القياس
                        unit_id = None
                        unit_name = str(row.get('وحدة القياس', '')).strip()
                        if unit_name and unit_name != 'nan':
                            unit_id = units_dict.get(unit_name)
                            if not unit_id:
                                errors.append(f"الصف {index + 2}: وحدة القياس '{unit_name}' غير موجودة")

                        # معالجة الأسعار
                        cost_price = 0.0
                        selling_price = 0.0
                        weight = None

                        try:
                            if 'سعر التكلفة' in row and pd.notna(row['سعر التكلفة']):
                                cost_price = float(row['سعر التكلفة'])
                        except (ValueError, TypeError):
                            pass

                        try:
                            if 'سعر البيع' in row and pd.notna(row['سعر البيع']):
                                selling_price = float(row['سعر البيع'])
                        except (ValueError, TypeError):
                            pass

                        try:
                            if 'الوزن' in row and pd.notna(row['الوزن']):
                                weight = float(row['الوزن'])
                        except (ValueError, TypeError):
                            pass

                        # الحقول الجديدة
                        total_package = 0
                        partial_package = 0
                        gram_weight = 0.0
                        item_weight = 0.0

                        try:
                            if 'عبوة الكلي' in row and pd.notna(row['عبوة الكلي']):
                                total_package = int(row['عبوة الكلي'])
                        except (ValueError, TypeError):
                            pass

                        try:
                            if 'عبوة الجزئي' in row and pd.notna(row['عبوة الجزئي']):
                                partial_package = int(row['عبوة الجزئي'])
                        except (ValueError, TypeError):
                            pass

                        try:
                            if 'الجرام' in row and pd.notna(row['الجرام']):
                                gram_weight = float(row['الجرام'])
                        except (ValueError, TypeError):
                            pass

                        try:
                            if 'وزن الصنف' in row and pd.notna(row['وزن الصنف']):
                                item_weight = float(row['وزن الصنف'])
                        except (ValueError, TypeError):
                            pass

                        # الأبعاد
                        dimensions = str(row.get('الأبعاد', '')).strip()
                        if dimensions == 'nan':
                            dimensions = None

                        # إنشاء الصنف الجديد
                        new_item = Item(
                            name=item_name,
                            name_en=item_name_en if item_name_en else None,
                            code=item_code,
                            description=description if description else None,
                            group_id=group_id,
                            unit_id=unit_id,
                            cost_price=cost_price,
                            selling_price=selling_price,
                            weight=weight,
                            dimensions=dimensions,
                            total_package=total_package,
                            partial_package=partial_package,
                            gram_weight=gram_weight,
                            item_weight=item_weight,
                            is_active=True
                        )

                        session.add(new_item)
                        success_count += 1

                    except Exception as e:
                        errors.append(f"الصف {index + 2}: خطأ في المعالجة - {str(e)}")
                        error_count += 1
                        continue

                # حفظ التغييرات
                if success_count > 0:
                    session.commit()

            except Exception as e:
                session.rollback()
                errors.append(f"خطأ في قاعدة البيانات: {str(e)}")
                error_count += len(df)
                success_count = 0
            finally:
                session.close()

        except Exception as e:
            errors.append(f"خطأ في قراءة الملف: {str(e)}")
            error_count = 1
            success_count = 0

        return success_count, error_count, errors
