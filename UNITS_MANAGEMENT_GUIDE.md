# دليل إدارة وحدات القياس والأصناف
# Units of Measurement & Items Management Guide

## 🎯 نظرة عامة

تم تطوير نظام إدارة وحدات القياس ليكون شاملاً ومتطوراً، يدعم جميع أنواع وحدات القياس المستخدمة في التجارة والصناعة.

## 🏗️ الإعداد الأولي

### 1. تشغيل الإعداد الشامل
```bash
python setup_units_and_items.py
```

هذا السكريپت سيقوم بـ:
- ✅ اختبار الاتصال بقاعدة البيانات
- ✅ تهيئة الجداول المطلوبة
- ✅ إدراج وحدات القياس الأساسية
- ✅ إدراج مجموعات الأصناف الافتراضية
- ✅ عرض إحصائيات النظام

### 2. إضافة وحدات قياس إضافية
```bash
python add_sample_units.py
```

### 3. إضافة أصناف تجريبية
```bash
python add_sample_items.py
```

## 📏 وحدات القياس المدعومة

### 🏋️ وحدات الوزن
- **كيلوجرام** (كجم / kg) - الوحدة الأساسية
- **جرام** (جم / g) - للأوزان الصغيرة
- **طن** (طن / t) - للأوزان الكبيرة
- **مليجرام** (مجم / mg) - للأدوية والمواد الدقيقة

### 📐 وحدات الطول
- **متر** (م / m) - الوحدة الأساسية
- **سنتيمتر** (سم / cm) - للقياسات الصغيرة
- **مليمتر** (مم / mm) - للقياسات الدقيقة

### 🧊 وحدات الحجم والسعة
- **متر مكعب** (م³ / m³) - للأحجام الكبيرة
- **لتر** (لتر / L) - للسوائل
- **مليلتر** (مل / ml) - للسوائل الصغيرة

### 📊 وحدات المساحة
- **متر مربع** (م² / m²) - لقياس المساحات

### 🔢 وحدات العد والتعبئة
- **قطعة** (قطعة / pcs) - الوحدة الأساسية للعد
- **عبوة** (عبوة / pkg) - للتعبئة الصغيرة
- **صندوق** (صندوق / box) - للتعبئة الكبيرة
- **كرتون** (كرتون / ctn) - للتعبئة المتوسطة
- **زوج** (زوج / pair) - للأشياء المزدوجة
- **دستة** (دستة / dz) - 12 قطعة
- **بالة** (بالة / bale) - للمواد النسيجية
- **رزمة** (رزمة / bundle) - للمواد المختلفة
- **لفة** (لفة / roll) - للمواد الملفوفة
- **طقم** (طقم / set) - مجموعة مترابطة

### ⏰ وحدات الوقت
- **ثانية** (ث / s)
- **دقيقة** (د / min)
- **ساعة** (س / h)
- **يوم** (يوم / day)

### ⚡ وحدات الطاقة والقدرة
- **واط** (واط / W)
- **كيلوواط** (ك.واط / kW)

### 🌡️ وحدات درجة الحرارة
- **درجة مئوية** (°م / °C)
- **فهرنهايت** (°ف / °F)

### 🏃 وحدات السرعة
- **كيلومتر/ساعة** (كم/س / km/h)
- **متر/ثانية** (م/ث / m/s)

## 📂 مجموعات الأصناف الافتراضية

1. **إلكترونيات** - الأجهزة والمعدات الإلكترونية
2. **ملابس** - الملابس والأزياء
3. **أغذية** - المواد الغذائية والمشروبات
4. **أدوات منزلية** - الأدوات والأجهزة المنزلية
5. **مواد بناء** - مواد ومعدات البناء
6. **قطع غيار** - قطع الغيار والمكونات
7. **مستحضرات تجميل** - مستحضرات التجميل والعناية
8. **كتب ومطبوعات** - الكتب والمطبوعات والقرطاسية

## 🖥️ واجهة إدارة وحدات القياس

### ✨ الميزات الجديدة

#### 🔍 البحث والتصفية
- **البحث النصي**: ابحث في اسم الوحدة أو الرمز
- **التصفية حسب الحالة**: 
  - جميع الوحدات
  - الوحدات النشطة
  - الوحدات غير النشطة

#### 📊 التصدير
- **تصدير إلى Excel**: تصدير جميع البيانات مع التفاصيل الكاملة
- **تنسيق احترافي**: ملفات Excel منسقة وجاهزة للاستخدام

#### 🎨 التصميم المحسن
- **جدول محسن**: عرض أفضل للبيانات مع ألوان مميزة للحالات
- **أزرار تفاعلية**: تصميم عصري وسهل الاستخدام
- **رسائل واضحة**: تأكيدات وتحذيرات واضحة

## 🔧 كيفية الاستخدام

### 1. فتح إدارة وحدات القياس
1. شغل التطبيق: `python main.py`
2. اذهب إلى قائمة "إدارة الأصناف"
3. اختر "وحدات القياس"

### 2. إضافة وحدة قياس جديدة
1. املأ البيانات في النموذج:
   - **اسم الوحدة**: مثل "كيلوجرام"
   - **الاسم بالإنجليزية**: مثل "Kilogram"
   - **الرمز**: مثل "كجم"
   - **الرمز بالإنجليزية**: مثل "kg"
   - **الوصف**: وصف مفصل للوحدة
2. اختر حالة الوحدة (نشطة/غير نشطة)
3. اضغط "إضافة"

### 3. تعديل وحدة قياس
1. اختر الوحدة من الجدول
2. ستظهر بياناتها في النموذج
3. عدل البيانات المطلوبة
4. اضغط "تحديث"

### 4. البحث والتصفية
1. استخدم مربع البحث للبحث في الأسماء والرموز
2. استخدم قائمة التصفية لعرض وحدات معينة
3. الجدول سيتحدث تلقائياً

### 5. تصدير البيانات
1. اضغط "تصدير إلى Excel"
2. اختر مكان الحفظ
3. سيتم إنشاء ملف Excel مع جميع البيانات

## 📦 إدارة الأصناف

### ربط الأصناف بوحدات القياس
عند إضافة صنف جديد:
1. اختر المجموعة المناسبة
2. **اختر وحدة القياس المناسبة** من القائمة المنسدلة
3. املأ باقي البيانات (السعر، الوزن، الأبعاد)

### أمثلة على الربط الصحيح
- **لابتوب** → قطعة
- **أرز** → كيلوجرام
- **حذاء** → زوج
- **زيت الطبخ** → لتر
- **طقم أواني** → صندوق

## 🛠️ أدوات إضافية

### اختبار قاعدة البيانات
```bash
python test_oracle_connection.py
```

### تبديل نوع قاعدة البيانات
```bash
# عرض الحالة الحالية
python switch_database.py status

# التبديل إلى Oracle
python switch_database.py oracle

# التبديل إلى SQLite
python switch_database.py sqlite
```

## 📊 الإحصائيات والتقارير

النظام يوفر إحصائيات شاملة:
- عدد وحدات القياس (النشطة وغير النشطة)
- عدد مجموعات الأصناف
- عدد الأصناف في كل مجموعة
- ربط الأصناف بوحدات القياس

## 🔒 الأمان والنسخ الاحتياطي

- **النسخ الاحتياطي التلقائي**: يتم إنشاء نسخ احتياطية دورية
- **التحقق من البيانات**: فحص صحة البيانات قبل الحفظ
- **سجل العمليات**: تسجيل جميع العمليات للمراجعة

## 🆘 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من إعدادات قاعدة البيانات
   - شغل `python test_oracle_connection.py`

2. **وحدة قياس موجودة مسبقاً**
   - تحقق من الأسماء المكررة
   - استخدم أسماء مختلفة

3. **خطأ في التصدير**
   - تأكد من تثبيت مكتبة pandas
   - `pip install pandas openpyxl`

## 📞 الدعم الفني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. شغل سكريپت الاختبار للتأكد من سلامة النظام
3. تحقق من ملفات السجل للأخطاء التفصيلية

---

**تم تطوير هذا النظام ليكون شاملاً ومرناً لجميع احتياجات إدارة الأصناف ووحدات القياس في البيئات التجارية والصناعية.**
