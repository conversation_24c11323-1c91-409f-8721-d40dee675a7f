#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام وحدات القياس
Test Units System
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        from src.database.universal_database_manager import UniversalDatabaseManager
        print("   ✅ UniversalDatabaseManager")
        
        from src.database.oracle_config import DatabaseConfigManager
        print("   ✅ DatabaseConfigManager")
        
        from src.database.models import UnitOfMeasure, ItemGroup, Item
        print("   ✅ Models")
        
        return True
    except Exception as e:
        print(f"   ❌ خطأ في الاستيراد: {e}")
        return False

def test_config():
    """اختبار التكوين"""
    print("\n📋 اختبار التكوين...")
    
    try:
        from src.database.oracle_config import DatabaseConfigManager
        
        config_file = "src/database/config/database.json"
        if not Path(config_file).exists():
            print(f"   ❌ ملف التكوين غير موجود: {config_file}")
            return False
        
        config_manager = DatabaseConfigManager(config_file)
        config = config_manager.load_config()
        
        print(f"   ✅ نوع قاعدة البيانات: {config.type.value}")
        
        if config.type.value == "oracle":
            oracle_config = config.oracle_config
            print(f"   📊 الخادم: {oracle_config.host}:{oracle_config.port}")
            print(f"   📊 قاعدة البيانات: {oracle_config.service_name}")
            print(f"   📊 المستخدم: {oracle_config.username}")
        
        return True
    except Exception as e:
        print(f"   ❌ خطأ في التكوين: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🔗 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        if db_manager.test_connection():
            print("   ✅ نجح الاتصال بقاعدة البيانات")
            return True
        else:
            print("   ❌ فشل الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
        return False

def test_database_initialization():
    """اختبار تهيئة قاعدة البيانات"""
    print("\n🏗️ اختبار تهيئة قاعدة البيانات...")
    
    try:
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        if db_manager.initialize_database():
            print("   ✅ تم تهيئة قاعدة البيانات بنجاح")
            return True
        else:
            print("   ❌ فشل في تهيئة قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في التهيئة: {e}")
        return False

def test_units_data():
    """اختبار بيانات وحدات القياس"""
    print("\n📏 اختبار بيانات وحدات القياس...")
    
    try:
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        from src.database.models import UnitOfMeasure
        
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        with db_manager.get_session() as session:
            units_count = session.query(UnitOfMeasure).count()
            active_units_count = session.query(UnitOfMeasure).filter_by(is_active=True).count()
            
            print(f"   📊 إجمالي وحدات القياس: {units_count}")
            print(f"   📊 الوحدات النشطة: {active_units_count}")
            
            if units_count > 0:
                print("   ✅ توجد وحدات قياس في النظام")
                
                # عرض أول 5 وحدات
                units = session.query(UnitOfMeasure).limit(5).all()
                print("   📋 أمثلة من وحدات القياس:")
                for unit in units:
                    status = "نشطة" if unit.is_active else "غير نشطة"
                    print(f"      • {unit.name} ({unit.symbol}) - {status}")
                
                return True
            else:
                print("   ⚠️ لا توجد وحدات قياس في النظام")
                return False
                
    except Exception as e:
        print(f"   ❌ خطأ في فحص البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار نظام وحدات القياس")
    print("=" * 50)
    
    tests = [
        ("الاستيرادات", test_imports),
        ("التكوين", test_config),
        ("الاتصال بقاعدة البيانات", test_database_connection),
        ("تهيئة قاعدة البيانات", test_database_initialization),
        ("بيانات وحدات القياس", test_units_data)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
