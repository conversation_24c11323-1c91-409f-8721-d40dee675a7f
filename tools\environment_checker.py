#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة فحص البيئة لـ Oracle - ProShipment V2.0.0
Environment Checker for Oracle Support
"""

import sys
import os
import platform
import subprocess
from pathlib import Path
from typing import List, Dict, Tuple, Optional

class EnvironmentChecker:
    """فاحص البيئة للتأكد من جاهزية Oracle"""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.info = []
        
    def check_all(self) -> bool:
        """فحص شامل للبيئة"""
        print("🔍 فحص البيئة لدعم Oracle...")
        print("="*60)
        
        checks = [
            ("Python Version", self.check_python_version),
            ("Required Packages", self.check_required_packages),
            ("Oracle Client", self.check_oracle_client),
            ("Environment Variables", self.check_environment_variables),
            ("System Architecture", self.check_system_architecture),
            ("Network Connectivity", self.check_network_connectivity),
            ("File Permissions", self.check_file_permissions)
        ]
        
        all_passed = True
        
        for check_name, check_func in checks:
            print(f"\n📋 {check_name}...")
            try:
                result = check_func()
                if not result:
                    all_passed = False
            except Exception as e:
                print(f"   ❌ خطأ في الفحص: {e}")
                self.issues.append(f"خطأ في فحص {check_name}: {e}")
                all_passed = False
        
        self.print_summary()
        return all_passed
    
    def check_python_version(self) -> bool:
        """فحص إصدار Python"""
        version = sys.version_info
        min_version = (3, 8)
        
        if version >= min_version:
            print(f"   ✅ Python {version.major}.{version.minor}.{version.micro}")
            self.info.append(f"Python {version.major}.{version.minor}.{version.micro}")
            return True
        else:
            print(f"   ❌ Python {version.major}.{version.minor} (مطلوب 3.8+)")
            self.issues.append(f"إصدار Python قديم: {version.major}.{version.minor}")
            return False
    
    def check_required_packages(self) -> bool:
        """فحص الحزم المطلوبة"""
        required_packages = [
            ('PySide6', 'PySide6'),
            ('SQLAlchemy', 'sqlalchemy'),
            ('cx_Oracle', 'cx_Oracle'),
            ('ReportLab', 'reportlab'),
            ('Requests', 'requests'),
            ('OpenPyXL', 'openpyxl'),
            ('Pillow', 'PIL')
        ]
        
        missing_packages = []
        available_packages = []
        
        for display_name, import_name in required_packages:
            try:
                __import__(import_name)
                print(f"   ✅ {display_name}")
                available_packages.append(display_name)
            except ImportError:
                print(f"   ❌ {display_name} (مفقود)")
                missing_packages.append(display_name)
        
        if missing_packages:
            self.issues.append(f"حزم مفقودة: {', '.join(missing_packages)}")
            print(f"\n   💡 لتثبيت الحزم المفقودة:")
            print(f"   pip install {' '.join(missing_packages)}")
            return False
        else:
            self.info.append(f"جميع الحزم متاحة: {len(available_packages)}")
            return True
    
    def check_oracle_client(self) -> bool:
        """فحص Oracle Instant Client"""
        print("   🔍 البحث عن Oracle Instant Client...")
        
        # فحص cx_Oracle
        try:
            import cx_Oracle
            print(f"   ✅ cx_Oracle {cx_Oracle.version}")
            
            # محاولة الحصول على معلومات العميل
            try:
                client_version = cx_Oracle.clientversion()
                print(f"   ✅ Oracle Client {'.'.join(map(str, client_version))}")
                self.info.append(f"Oracle Client {'.'.join(map(str, client_version))}")
                return True
            except Exception as e:
                print(f"   ⚠️ cx_Oracle متاح لكن Oracle Client غير مكتمل: {e}")
                self.warnings.append("Oracle Client غير مكتمل")
                return False
                
        except ImportError:
            print("   ❌ cx_Oracle غير متاح")
            self.issues.append("cx_Oracle غير مثبت")
            return False
    
    def check_environment_variables(self) -> bool:
        """فحص متغيرات البيئة"""
        oracle_vars = [
            'ORACLE_HOME',
            'TNS_ADMIN',
            'LD_LIBRARY_PATH',  # Linux
            'PATH'              # Windows
        ]
        
        found_vars = []
        missing_vars = []
        
        for var in oracle_vars:
            value = os.getenv(var)
            if value:
                print(f"   ✅ {var}: {value[:50]}...")
                found_vars.append(var)
            else:
                print(f"   ⚠️ {var}: غير محدد")
                missing_vars.append(var)
        
        if found_vars:
            self.info.append(f"متغيرات البيئة المحددة: {', '.join(found_vars)}")
        
        if missing_vars:
            self.warnings.append(f"متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        
        # فحص PATH للبحث عن Oracle
        path = os.getenv('PATH', '')
        oracle_in_path = any('oracle' in p.lower() for p in path.split(os.pathsep))
        
        if oracle_in_path:
            print("   ✅ Oracle موجود في PATH")
            return True
        else:
            print("   ⚠️ Oracle غير موجود في PATH")
            self.warnings.append("Oracle غير موجود في PATH")
            return len(found_vars) > 0
    
    def check_system_architecture(self) -> bool:
        """فحص معمارية النظام"""
        arch = platform.architecture()
        machine = platform.machine()
        system = platform.system()
        
        print(f"   📊 النظام: {system}")
        print(f"   📊 المعمارية: {arch[0]}")
        print(f"   📊 المعالج: {machine}")
        
        self.info.append(f"النظام: {system} {arch[0]} {machine}")
        
        # تحذيرات خاصة بالمعمارية
        if arch[0] == '32bit':
            self.warnings.append("نظام 32-bit قد يواجه مشاكل مع Oracle")
            print("   ⚠️ نظام 32-bit قد يواجه مشاكل مع Oracle")
            return False
        
        return True
    
    def check_network_connectivity(self) -> bool:
        """فحص الاتصال بالشبكة"""
        # فحص أساسي للشبكة
        try:
            import socket
            
            # فحص DNS
            socket.gethostbyname('www.google.com')
            print("   ✅ اتصال الإنترنت متاح")
            
            # فحص المنفذ المحلي لـ Oracle
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', 1521))
            sock.close()
            
            if result == 0:
                print("   ✅ منفذ Oracle (1521) مفتوح محلياً")
                self.info.append("منفذ Oracle متاح محلياً")
            else:
                print("   ⚠️ منفذ Oracle (1521) غير متاح محلياً")
                self.warnings.append("منفذ Oracle غير متاح محلياً")
            
            return True
            
        except Exception as e:
            print(f"   ❌ مشكلة في الشبكة: {e}")
            self.issues.append(f"مشكلة في الشبكة: {e}")
            return False
    
    def check_file_permissions(self) -> bool:
        """فحص أذونات الملفات"""
        test_dirs = [
            Path("data"),
            Path("config"),
            Path("logs"),
            Path("backups")
        ]
        
        all_writable = True
        
        for test_dir in test_dirs:
            test_dir.mkdir(parents=True, exist_ok=True)
            test_file = test_dir / "test_write.tmp"
            
            try:
                test_file.write_text("test", encoding='utf-8')
                test_file.unlink()
                print(f"   ✅ {test_dir} قابل للكتابة")
            except Exception as e:
                print(f"   ❌ {test_dir} غير قابل للكتابة: {e}")
                self.issues.append(f"مجلد {test_dir} غير قابل للكتابة")
                all_writable = False
        
        return all_writable
    
    def print_summary(self):
        """طباعة ملخص الفحص"""
        print("\n" + "="*60)
        print("📊 ملخص فحص البيئة")
        print("="*60)
        
        print(f"\n🔴 المشاكل الحرجة: {len(self.issues)}")
        for issue in self.issues:
            print(f"   ❌ {issue}")
        
        print(f"\n🟡 التحذيرات: {len(self.warnings)}")
        for warning in self.warnings:
            print(f"   ⚠️ {warning}")
        
        print(f"\n🟢 المعلومات: {len(self.info)}")
        for info in self.info[:5]:  # أول 5 معلومات
            print(f"   ℹ️ {info}")
        
        if len(self.info) > 5:
            print(f"   ... و {len(self.info) - 5} معلومة أخرى")
        
        # تقييم الحالة العامة
        if len(self.issues) == 0:
            if len(self.warnings) == 0:
                print("\n🎉 البيئة جاهزة تماماً لـ Oracle!")
                status = "جاهز"
            elif len(self.warnings) <= 3:
                print("\n✅ البيئة جاهزة مع بعض التحذيرات")
                status = "جاهز مع تحذيرات"
            else:
                print("\n🟡 البيئة تحتاج بعض التحسينات")
                status = "يحتاج تحسينات"
        else:
            if len(self.issues) <= 2:
                print("\n🔴 البيئة تحتاج إصلاحات")
                status = "يحتاج إصلاحات"
            else:
                print("\n🚨 البيئة غير جاهزة لـ Oracle")
                status = "غير جاهز"
        
        print(f"الحالة العامة: {status}")
        
        # اقتراحات الإصلاح
        if self.issues or self.warnings:
            print(f"\n💡 اقتراحات الإصلاح:")
            
            if any("حزم مفقودة" in issue for issue in self.issues):
                print("   📦 شغل: pip install -r requirements.txt")
            
            if any("Oracle Client" in issue for issue in self.issues):
                print("   🔧 حمل وثبت Oracle Instant Client")
                print("   🔗 https://www.oracle.com/database/technologies/instant-client.html")
            
            if any("PATH" in warning for warning in self.warnings):
                print("   ⚙️ أضف مسار Oracle إلى متغير البيئة PATH")
            
            if any("أذونات" in issue for issue in self.issues):
                print("   🔒 شغل التطبيق كمدير (Run as Administrator)")

def main():
    """الدالة الرئيسية"""
    checker = EnvironmentChecker()
    success = checker.check_all()
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
