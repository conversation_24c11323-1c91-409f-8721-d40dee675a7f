# -*- coding: utf-8 -*-
"""
نافذة التقارير المالية للحوالات
Financial Reports Window for Remittances
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTableWidget, QTableWidgetItem, QPushButton, QLabel,
                               QComboBox, QDateEdit, QFrame, QSplitter, QGroupBox,
                               QGridLayout, QMessageBox, QHeaderView, QAbstractItemView,
                               QTabWidget, QTextEdit, QProgressBar, QFileDialog,
                               QCheckBox, QSpinBox, QScrollArea)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QThread
from PySide6.QtGui import QFont, QPixmap, QIcon, QColor, QPalette
# استيراد اختياري للرسوم البيانية
try:
    from PySide6.QtCharts import QChart, QChartView, QPieSeries, QBarSeries, QBarSet, QLineSeries
    CHARTS_AVAILABLE = True
except ImportError:
    CHARTS_AVAILABLE = False
    # إنشاء بديل بسيط
    class QChartView(QWidget):
        def __init__(self):
            super().__init__()
            layout = QVBoxLayout(self)
            label = QLabel("الرسوم البيانية غير متوفرة\nيرجى تثبيت PySide6-Charts")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("color: #6b7280; font-size: 14px;")
            layout.addWidget(label)

import sqlite3
from pathlib import Path
from datetime import datetime, timedelta
import json
import csv
# استيراد اختياري لـ Excel
try:
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

from ...utils.arabic_support import reshape_arabic_text

class FinancialReportsWindow(QMainWindow):
    """نافذة التقارير المالية للحوالات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("التقارير المالية للحوالات - ProShipment")
        self.setMinimumSize(1400, 800)
        self.resize(1600, 900)
        
        # متغيرات النافذة
        self.current_report_data = []
        self.report_type = "daily"
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # عنوان النافذة
        self.create_header_section(main_layout)
        
        # قسم إعدادات التقرير
        self.create_report_settings_section(main_layout)
        
        # التبويبات الرئيسية
        self.tabs = QTabWidget()
        
        # تبويب التقارير الجاهزة
        ready_reports_tab = self.create_ready_reports_tab()
        self.tabs.addTab(ready_reports_tab, "التقارير الجاهزة")
        
        # تبويب التقارير المخصصة
        custom_reports_tab = self.create_custom_reports_tab()
        self.tabs.addTab(custom_reports_tab, "التقارير المخصصة")
        
        # تبويب الرسوم البيانية
        charts_tab = self.create_charts_tab()
        self.tabs.addTab(charts_tab, "الرسوم البيانية")
        
        # تبويب الإحصائيات
        statistics_tab = self.create_statistics_tab()
        self.tabs.addTab(statistics_tab, "الإحصائيات التفصيلية")
        
        main_layout.addWidget(self.tabs)
        
        # شريط الحالة والإجراءات
        self.create_status_actions_section(main_layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1e40af, stop:1 #3b82f6);
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة التقارير
        icon_label = QLabel("📊")
        icon_label.setStyleSheet("font-size: 36px; color: white;")
        header_layout.addWidget(icon_label)
        
        # معلومات التقرير
        info_layout = QVBoxLayout()
        
        title_label = QLabel("التقارير المالية للحوالات")
        title_label.setFont(QFont("Arial", 20, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        
        subtitle_label = QLabel("تقارير شاملة ومفصلة لجميع العمليات المالية")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setStyleSheet("color: #e0e7ff;")
        
        info_layout.addWidget(title_label)
        info_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        stats_layout = QGridLayout(stats_frame)
        
        self.total_remittances_label = QLabel("إجمالي الحوالات\n0")
        self.total_amount_label = QLabel("إجمالي المبلغ\n0.00 ريال")
        self.pending_count_label = QLabel("الحوالات المعلقة\n0")
        self.completed_count_label = QLabel("الحوالات المكتملة\n0")
        
        for i, label in enumerate([self.total_remittances_label, self.total_amount_label, 
                                 self.pending_count_label, self.completed_count_label]):
            label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    text-align: center;
                    font-size: 12px;
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(label, 0, i)
        
        header_layout.addWidget(stats_frame)
        
        layout.addWidget(header_frame)
        
    def create_report_settings_section(self, layout):
        """إنشاء قسم إعدادات التقرير"""
        settings_frame = QFrame()
        settings_frame.setFrameStyle(QFrame.StyledPanel)
        settings_frame.setStyleSheet("""
            QFrame {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        settings_layout = QHBoxLayout(settings_frame)
        settings_layout.setSpacing(20)
        
        # نوع التقرير
        settings_layout.addWidget(QLabel("نوع التقرير:"))
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "تقرير يومي", "تقرير أسبوعي", "تقرير شهري", 
            "تقرير ربع سنوي", "تقرير سنوي", "تقرير مخصص"
        ])
        self.report_type_combo.setMinimumWidth(150)
        settings_layout.addWidget(self.report_type_combo)
        
        # فترة التقرير
        settings_layout.addWidget(QLabel("من تاريخ:"))
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        settings_layout.addWidget(self.date_from)
        
        settings_layout.addWidget(QLabel("إلى تاريخ:"))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        settings_layout.addWidget(self.date_to)
        
        # فلتر المورد
        settings_layout.addWidget(QLabel("المورد:"))
        self.supplier_filter = QComboBox()
        self.supplier_filter.addItem("جميع الموردين", "")
        self.supplier_filter.setMinimumWidth(200)
        settings_layout.addWidget(self.supplier_filter)
        
        # فلتر العملة
        settings_layout.addWidget(QLabel("العملة:"))
        self.currency_filter = QComboBox()
        self.currency_filter.addItem("جميع العملات", "")
        self.currency_filter.setMinimumWidth(150)
        settings_layout.addWidget(self.currency_filter)
        
        # أزرار الإجراءات
        self.generate_report_btn = QPushButton("📊 إنشاء التقرير")
        self.generate_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        settings_layout.addWidget(self.generate_report_btn)
        
        self.export_report_btn = QPushButton("📤 تصدير")
        self.export_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        settings_layout.addWidget(self.export_report_btn)
        
        settings_layout.addStretch()
        layout.addWidget(settings_frame)
        
    def create_ready_reports_tab(self):
        """إنشاء تبويب التقارير الجاهزة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # قائمة التقارير الجاهزة
        reports_frame = QFrame()
        reports_layout = QGridLayout(reports_frame)
        
        # تقرير الحوالات اليومية
        daily_btn = self.create_report_button(
            "📅 تقرير الحوالات اليومية",
            "عرض جميع الحوالات لليوم الحالي",
            "#10b981"
        )
        daily_btn.clicked.connect(lambda: self.generate_predefined_report("daily"))
        reports_layout.addWidget(daily_btn, 0, 0)
        
        # تقرير الحوالات الشهرية
        monthly_btn = self.create_report_button(
            "📊 تقرير الحوالات الشهرية",
            "إحصائيات شاملة للشهر الحالي",
            "#3b82f6"
        )
        monthly_btn.clicked.connect(lambda: self.generate_predefined_report("monthly"))
        reports_layout.addWidget(monthly_btn, 0, 1)
        
        # تقرير حسب المورد
        supplier_btn = self.create_report_button(
            "🏢 تقرير حسب المورد",
            "تفصيل الحوالات لكل مورد",
            "#f59e0b"
        )
        supplier_btn.clicked.connect(lambda: self.generate_predefined_report("supplier"))
        reports_layout.addWidget(supplier_btn, 1, 0)
        
        # تقرير الأرصدة
        balances_btn = self.create_report_button(
            "💰 تقرير أرصدة الموردين",
            "أرصدة جميع حسابات الموردين",
            "#8b5cf6"
        )
        balances_btn.clicked.connect(lambda: self.generate_predefined_report("balances"))
        reports_layout.addWidget(balances_btn, 1, 1)
        
        # تقرير الحوالات المعلقة
        pending_btn = self.create_report_button(
            "⏳ تقرير الحوالات المعلقة",
            "جميع الحوالات في انتظار المعالجة",
            "#ef4444"
        )
        pending_btn.clicked.connect(lambda: self.generate_predefined_report("pending"))
        reports_layout.addWidget(pending_btn, 2, 0)
        
        # تقرير الرسوم والعمولات
        fees_btn = self.create_report_button(
            "💳 تقرير الرسوم والعمولات",
            "تفصيل جميع الرسوم المحصلة",
            "#06b6d4"
        )
        fees_btn.clicked.connect(lambda: self.generate_predefined_report("fees"))
        reports_layout.addWidget(fees_btn, 2, 1)
        
        layout.addWidget(reports_frame)
        
        # جدول النتائج
        self.ready_reports_table = QTableWidget()
        self.setup_reports_table(self.ready_reports_table)
        layout.addWidget(self.ready_reports_table)
        
        return tab
        
    def create_report_button(self, title, description, color):
        """إنشاء زر تقرير"""
        btn = QPushButton()
        btn.setMinimumSize(300, 80)
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                text-align: left;
                padding: 15px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
                transform: translateY(-2px);
            }}
        """)
        
        btn.setText(f"{title}\n{description}")
        return btn

    def create_custom_reports_tab(self):
        """إنشاء تبويب التقارير المخصصة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات التقرير المخصص
        settings_group = QGroupBox("إعدادات التقرير المخصص")
        settings_layout = QGridLayout(settings_group)

        # الحقول المطلوبة
        settings_layout.addWidget(QLabel("الحقول المطلوبة:"), 0, 0)

        fields_frame = QFrame()
        fields_layout = QGridLayout(fields_frame)

        self.field_remittance_number = QCheckBox("رقم الحوالة")
        self.field_remittance_number.setChecked(True)
        fields_layout.addWidget(self.field_remittance_number, 0, 0)

        self.field_date = QCheckBox("التاريخ")
        self.field_date.setChecked(True)
        fields_layout.addWidget(self.field_date, 0, 1)

        self.field_sender = QCheckBox("المرسل")
        fields_layout.addWidget(self.field_sender, 0, 2)

        self.field_receiver = QCheckBox("المستقبل")
        fields_layout.addWidget(self.field_receiver, 1, 0)

        self.field_amount = QCheckBox("المبلغ")
        self.field_amount.setChecked(True)
        fields_layout.addWidget(self.field_amount, 1, 1)

        self.field_currency = QCheckBox("العملة")
        fields_layout.addWidget(self.field_currency, 1, 2)

        self.field_status = QCheckBox("الحالة")
        fields_layout.addWidget(self.field_status, 2, 0)

        self.field_supplier = QCheckBox("المورد")
        fields_layout.addWidget(self.field_supplier, 2, 1)

        self.field_fees = QCheckBox("الرسوم")
        fields_layout.addWidget(self.field_fees, 2, 2)

        settings_layout.addWidget(fields_frame, 0, 1, 1, 2)

        # ترتيب النتائج
        settings_layout.addWidget(QLabel("ترتيب حسب:"), 1, 0)
        self.sort_by_combo = QComboBox()
        self.sort_by_combo.addItems([
            "تاريخ الحوالة", "المبلغ", "المورد", "الحالة", "رقم الحوالة"
        ])
        settings_layout.addWidget(self.sort_by_combo, 1, 1)

        self.sort_order_combo = QComboBox()
        self.sort_order_combo.addItems(["تنازلي", "تصاعدي"])
        settings_layout.addWidget(self.sort_order_combo, 1, 2)

        # حد النتائج
        settings_layout.addWidget(QLabel("حد النتائج:"), 2, 0)
        self.limit_results = QSpinBox()
        self.limit_results.setRange(1, 10000)
        self.limit_results.setValue(1000)
        settings_layout.addWidget(self.limit_results, 2, 1)

        layout.addWidget(settings_group)

        # جدول النتائج المخصصة
        self.custom_reports_table = QTableWidget()
        self.setup_reports_table(self.custom_reports_table)
        layout.addWidget(self.custom_reports_table)

        return tab

    def create_charts_tab(self):
        """إنشاء تبويب الرسوم البيانية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # أزرار اختيار نوع الرسم البياني
        charts_buttons_frame = QFrame()
        charts_buttons_layout = QHBoxLayout(charts_buttons_frame)

        pie_chart_btn = QPushButton("📊 رسم دائري - الحوالات حسب المورد")
        pie_chart_btn.clicked.connect(self.show_pie_chart)
        charts_buttons_layout.addWidget(pie_chart_btn)

        bar_chart_btn = QPushButton("📈 رسم بياني - الحوالات الشهرية")
        bar_chart_btn.clicked.connect(self.show_bar_chart)
        charts_buttons_layout.addWidget(bar_chart_btn)

        line_chart_btn = QPushButton("📉 رسم خطي - اتجاه الحوالات")
        line_chart_btn.clicked.connect(self.show_line_chart)
        charts_buttons_layout.addWidget(line_chart_btn)

        charts_buttons_layout.addStretch()
        layout.addWidget(charts_buttons_frame)

        # منطقة عرض الرسوم البيانية
        self.chart_view = QChartView()
        self.chart_view.setMinimumHeight(400)
        layout.addWidget(self.chart_view)

        return tab

    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات التفصيلية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إحصائيات عامة
        general_stats_group = QGroupBox("الإحصائيات العامة")
        general_stats_layout = QGridLayout(general_stats_group)

        self.stats_total_remittances = QLabel("0")
        self.stats_total_amount = QLabel("0.00")
        self.stats_avg_amount = QLabel("0.00")
        self.stats_max_amount = QLabel("0.00")
        self.stats_min_amount = QLabel("0.00")

        general_stats_layout.addWidget(QLabel("إجمالي الحوالات:"), 0, 0)
        general_stats_layout.addWidget(self.stats_total_remittances, 0, 1)
        general_stats_layout.addWidget(QLabel("إجمالي المبلغ:"), 0, 2)
        general_stats_layout.addWidget(self.stats_total_amount, 0, 3)

        general_stats_layout.addWidget(QLabel("متوسط المبلغ:"), 1, 0)
        general_stats_layout.addWidget(self.stats_avg_amount, 1, 1)
        general_stats_layout.addWidget(QLabel("أعلى مبلغ:"), 1, 2)
        general_stats_layout.addWidget(self.stats_max_amount, 1, 3)

        general_stats_layout.addWidget(QLabel("أقل مبلغ:"), 2, 0)
        general_stats_layout.addWidget(self.stats_min_amount, 2, 1)

        layout.addWidget(general_stats_group)

        # إحصائيات حسب الحالة
        status_stats_group = QGroupBox("إحصائيات حسب الحالة")
        status_stats_layout = QGridLayout(status_stats_group)

        self.stats_pending = QLabel("0")
        self.stats_completed = QLabel("0")
        self.stats_cancelled = QLabel("0")
        self.stats_processing = QLabel("0")

        status_stats_layout.addWidget(QLabel("معلقة:"), 0, 0)
        status_stats_layout.addWidget(self.stats_pending, 0, 1)
        status_stats_layout.addWidget(QLabel("مكتملة:"), 0, 2)
        status_stats_layout.addWidget(self.stats_completed, 0, 3)

        status_stats_layout.addWidget(QLabel("ملغية:"), 1, 0)
        status_stats_layout.addWidget(self.stats_cancelled, 1, 1)
        status_stats_layout.addWidget(QLabel("قيد المعالجة:"), 1, 2)
        status_stats_layout.addWidget(self.stats_processing, 1, 3)

        layout.addWidget(status_stats_group)

        # إحصائيات حسب العملة
        currency_stats_group = QGroupBox("إحصائيات حسب العملة")
        currency_stats_layout = QVBoxLayout(currency_stats_group)

        self.currency_stats_table = QTableWidget()
        self.currency_stats_table.setColumnCount(4)
        self.currency_stats_table.setHorizontalHeaderLabels([
            "العملة", "عدد الحوالات", "إجمالي المبلغ", "النسبة المئوية"
        ])
        currency_stats_layout.addWidget(self.currency_stats_table)

        layout.addWidget(currency_stats_group)

        # إحصائيات حسب المورد
        supplier_stats_group = QGroupBox("إحصائيات حسب المورد")
        supplier_stats_layout = QVBoxLayout(supplier_stats_group)

        self.supplier_stats_table = QTableWidget()
        self.supplier_stats_table.setColumnCount(4)
        self.supplier_stats_table.setHorizontalHeaderLabels([
            "المورد", "عدد الحوالات", "إجمالي المبلغ", "متوسط المبلغ"
        ])
        supplier_stats_layout.addWidget(self.supplier_stats_table)

        layout.addWidget(supplier_stats_group)

        return tab

    def create_status_actions_section(self, layout):
        """إنشاء قسم الحالة والإجراءات"""
        status_frame = QFrame()
        status_layout = QHBoxLayout(status_frame)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)

        # معلومات الحالة
        self.status_label = QLabel("جاهز")
        self.status_label.setStyleSheet("color: #059669; font-weight: bold;")
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()

        # أزرار الإجراءات الإضافية
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.clicked.connect(self.print_report)
        status_layout.addWidget(print_btn)

        save_btn = QPushButton("💾 حفظ التقرير")
        save_btn.clicked.connect(self.save_report)
        status_layout.addWidget(save_btn)

        close_btn = QPushButton("❌ إغلاق")
        close_btn.clicked.connect(self.close)
        status_layout.addWidget(close_btn)

        layout.addWidget(status_frame)

    def setup_reports_table(self, table):
        """إعداد جدول التقارير"""
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setSelectionMode(QAbstractItemView.SingleSelection)
        table.setAlternatingRowColors(True)
        table.setSortingEnabled(True)

        # تنسيق الجدول
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e2e8f0;
                background-color: white;
                alternate-background-color: #f8fafc;
                selection-background-color: #3b82f6;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e2e8f0;
            }
            QHeaderView::section {
                background-color: #f1f5f9;
                color: #374151;
                padding: 10px;
                border: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # إعدادات التقرير
        self.generate_report_btn.clicked.connect(self.generate_custom_report)
        self.export_report_btn.clicked.connect(self.export_current_report)

        # تغيير نوع التقرير
        self.report_type_combo.currentTextChanged.connect(self.on_report_type_changed)

        # تغيير التواريخ
        self.date_from.dateChanged.connect(self.update_quick_stats)
        self.date_to.dateChanged.connect(self.update_quick_stats)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل الموردين
            cursor.execute("SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name")
            suppliers = cursor.fetchall()
            for supplier in suppliers:
                self.supplier_filter.addItem(supplier[1], supplier[0])

            # تحميل العملات
            cursor.execute("SELECT id, code, name FROM currencies WHERE is_active = 1 ORDER BY name")
            currencies = cursor.fetchall()
            for currency in currencies:
                self.currency_filter.addItem(f"{currency[1]} - {currency[2]}", currency[0])

            conn.close()

            # تحديث الإحصائيات السريعة
            self.update_quick_stats()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات الأولية:\n{str(e)}")

    def update_quick_stats(self):
        """تحديث الإحصائيات السريعة"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            date_from = self.date_from.date().toString("yyyy-MM-dd")
            date_to = self.date_to.date().toString("yyyy-MM-dd")

            # إجمالي الحوالات
            cursor.execute("""
                SELECT COUNT(*) FROM remittances
                WHERE DATE(remittance_date) BETWEEN ? AND ? AND is_active = 1
            """, (date_from, date_to))
            total_remittances = cursor.fetchone()[0]

            # إجمالي المبلغ
            cursor.execute("""
                SELECT SUM(amount) FROM remittances
                WHERE DATE(remittance_date) BETWEEN ? AND ? AND is_active = 1
            """, (date_from, date_to))
            total_amount = cursor.fetchone()[0] or 0

            # الحوالات المعلقة
            cursor.execute("""
                SELECT COUNT(*) FROM remittances
                WHERE DATE(remittance_date) BETWEEN ? AND ?
                AND current_status = 'معلقة' AND is_active = 1
            """, (date_from, date_to))
            pending_count = cursor.fetchone()[0]

            # الحوالات المكتملة
            cursor.execute("""
                SELECT COUNT(*) FROM remittances
                WHERE DATE(remittance_date) BETWEEN ? AND ?
                AND current_status = 'مكتملة' AND is_active = 1
            """, (date_from, date_to))
            completed_count = cursor.fetchone()[0]

            # تحديث التسميات
            self.total_remittances_label.setText(f"إجمالي الحوالات\n{total_remittances}")
            self.total_amount_label.setText(f"إجمالي المبلغ\n{total_amount:,.2f} ريال")
            self.pending_count_label.setText(f"الحوالات المعلقة\n{pending_count}")
            self.completed_count_label.setText(f"الحوالات المكتملة\n{completed_count}")

            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات السريعة: {e}")

    def on_report_type_changed(self):
        """معالج تغيير نوع التقرير"""
        report_type = self.report_type_combo.currentText()

        if report_type == "تقرير يومي":
            self.date_from.setDate(QDate.currentDate())
            self.date_to.setDate(QDate.currentDate())
        elif report_type == "تقرير أسبوعي":
            self.date_from.setDate(QDate.currentDate().addDays(-7))
            self.date_to.setDate(QDate.currentDate())
        elif report_type == "تقرير شهري":
            self.date_from.setDate(QDate.currentDate().addDays(-30))
            self.date_to.setDate(QDate.currentDate())
        elif report_type == "تقرير ربع سنوي":
            self.date_from.setDate(QDate.currentDate().addDays(-90))
            self.date_to.setDate(QDate.currentDate())
        elif report_type == "تقرير سنوي":
            self.date_from.setDate(QDate.currentDate().addDays(-365))
            self.date_to.setDate(QDate.currentDate())

        self.update_quick_stats()

    def generate_predefined_report(self, report_type):
        """إنشاء تقرير جاهز"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.status_label.setText("جاري إنشاء التقرير...")

            if report_type == "daily":
                self.generate_daily_report()
            elif report_type == "monthly":
                self.generate_monthly_report()
            elif report_type == "supplier":
                self.generate_supplier_report()
            elif report_type == "balances":
                self.generate_balances_report()
            elif report_type == "pending":
                self.generate_pending_report()
            elif report_type == "fees":
                self.generate_fees_report()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء التقرير:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.status_label.setText("جاهز")

    def generate_daily_report(self):
        """إنشاء تقرير الحوالات اليومية"""
        db_path = Path("data/proshipment.db")
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        today = QDate.currentDate().toString("yyyy-MM-dd")

        query = """
            SELECT
                r.remittance_number, r.remittance_date, r.sender_name,
                r.receiver_name, s.name as supplier_name, r.amount,
                c.code as currency_code, r.current_status
            FROM remittances r
            LEFT JOIN suppliers s ON r.supplier_id = s.id
            LEFT JOIN currencies c ON r.currency_id = c.id
            WHERE DATE(r.remittance_date) = ? AND r.is_active = 1
            ORDER BY r.remittance_date DESC
        """

        cursor.execute(query, (today,))
        data = cursor.fetchall()

        # تحديث الجدول
        self.update_report_table(self.ready_reports_table, data, [
            "رقم الحوالة", "التاريخ", "المرسل", "المستقبل",
            "المورد", "المبلغ", "العملة", "الحالة"
        ])

        conn.close()
        self.status_label.setText(f"تم إنشاء تقرير يومي - {len(data)} حوالة")

    def generate_custom_report(self):
        """إنشاء تقرير مخصص"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.status_label.setText("جاري إنشاء التقرير المخصص...")

            # بناء الاستعلام حسب الحقول المحددة
            selected_fields = []
            field_names = []

            if self.field_remittance_number.isChecked():
                selected_fields.append("r.remittance_number")
                field_names.append("رقم الحوالة")

            if self.field_date.isChecked():
                selected_fields.append("r.remittance_date")
                field_names.append("التاريخ")

            if self.field_sender.isChecked():
                selected_fields.append("r.sender_name")
                field_names.append("المرسل")

            if self.field_receiver.isChecked():
                selected_fields.append("r.receiver_name")
                field_names.append("المستقبل")

            if self.field_amount.isChecked():
                selected_fields.append("r.amount")
                field_names.append("المبلغ")

            if self.field_currency.isChecked():
                selected_fields.append("c.code")
                field_names.append("العملة")

            if self.field_status.isChecked():
                selected_fields.append("r.current_status")
                field_names.append("الحالة")

            if self.field_supplier.isChecked():
                selected_fields.append("s.name")
                field_names.append("المورد")

            if self.field_fees.isChecked():
                selected_fields.append("r.total_charges")
                field_names.append("الرسوم")

            if not selected_fields:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار حقل واحد على الأقل")
                return

            # بناء الاستعلام
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            query = f"""
                SELECT {', '.join(selected_fields)}
                FROM remittances r
                LEFT JOIN suppliers s ON r.supplier_id = s.id
                LEFT JOIN currencies c ON r.currency_id = c.id
                WHERE r.is_active = 1
            """

            params = []

            # إضافة فلاتر
            date_from = self.date_from.date().toString("yyyy-MM-dd")
            date_to = self.date_to.date().toString("yyyy-MM-dd")
            query += " AND DATE(r.remittance_date) BETWEEN ? AND ?"
            params.extend([date_from, date_to])

            supplier_id = self.supplier_filter.currentData()
            if supplier_id:
                query += " AND r.supplier_id = ?"
                params.append(supplier_id)

            currency_id = self.currency_filter.currentData()
            if currency_id:
                query += " AND r.currency_id = ?"
                params.append(currency_id)

            # إضافة ترتيب
            sort_field_map = {
                "تاريخ الحوالة": "r.remittance_date",
                "المبلغ": "r.amount",
                "المورد": "s.name",
                "الحالة": "r.current_status",
                "رقم الحوالة": "r.remittance_number"
            }

            sort_field = sort_field_map.get(self.sort_by_combo.currentText(), "r.remittance_date")
            sort_order = "DESC" if self.sort_order_combo.currentText() == "تنازلي" else "ASC"
            query += f" ORDER BY {sort_field} {sort_order}"

            # إضافة حد النتائج
            query += f" LIMIT {self.limit_results.value()}"

            cursor.execute(query, params)
            data = cursor.fetchall()

            # تحديث الجدول
            self.update_report_table(self.custom_reports_table, data, field_names)

            conn.close()
            self.status_label.setText(f"تم إنشاء التقرير المخصص - {len(data)} سجل")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء التقرير المخصص:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def update_report_table(self, table, data, headers):
        """تحديث جدول التقرير"""
        table.setRowCount(len(data))
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)

        for row, record in enumerate(data):
            for col, value in enumerate(record):
                if isinstance(value, float):
                    item_text = f"{value:,.2f}"
                elif isinstance(value, str) and value:
                    try:
                        # محاولة تنسيق التاريخ
                        date_obj = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        item_text = date_obj.strftime('%Y-%m-%d %H:%M')
                    except:
                        item_text = str(value)
                else:
                    item_text = str(value or "")

                item = QTableWidgetItem(item_text)

                # تلوين الحالات
                if col < len(headers) and headers[col] == "الحالة":
                    if value == "مكتملة":
                        item.setBackground(QColor("#dcfce7"))
                    elif value == "معلقة":
                        item.setBackground(QColor("#fef3c7"))
                    elif value == "ملغية":
                        item.setBackground(QColor("#fee2e2"))

                table.setItem(row, col, item)

        # تعديل عرض الأعمدة
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def export_current_report(self):
        """تصدير التقرير الحالي"""
        current_tab = self.tabs.currentIndex()

        if current_tab == 0:  # التقارير الجاهزة
            table = self.ready_reports_table
        elif current_tab == 1:  # التقارير المخصصة
            table = self.custom_reports_table
        else:
            QMessageBox.information(self, "تنبيه", "لا يوجد تقرير للتصدير")
            return

        if table.rowCount() == 0:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات للتصدير")
            return

        # اختيار نوع التصدير
        file_dialog = QFileDialog()

        # تحديد أنواع الملفات المتاحة
        if EXCEL_AVAILABLE:
            file_types = "Excel Files (*.xlsx);;CSV Files (*.csv)"
        else:
            file_types = "CSV Files (*.csv)"

        file_path, file_type = file_dialog.getSaveFileName(
            self, "تصدير التقرير",
            f"تقرير_الحوالات_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            file_types
        )

        if file_path:
            try:
                if file_type == "Excel Files (*.xlsx)" and EXCEL_AVAILABLE:
                    self.export_to_excel(table, file_path)
                else:
                    self.export_to_csv(table, file_path)

                QMessageBox.information(self, "نجح التصدير", f"تم تصدير التقرير إلى:\n{file_path}")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في تصدير التقرير:\n{str(e)}")

    def export_to_excel(self, table, file_path):
        """تصدير إلى Excel"""
        if not EXCEL_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "مكتبة Excel غير متوفرة. سيتم التصدير كـ CSV بدلاً من ذلك.")
            self.export_to_csv(table, file_path.replace('.xlsx', '.csv'))
            return

        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "تقرير الحوالات"

        # إضافة العناوين
        headers = []
        for col in range(table.columnCount()):
            header = table.horizontalHeaderItem(col)
            headers.append(header.text() if header else f"عمود {col + 1}")

        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # إضافة البيانات
        for row in range(table.rowCount()):
            for col in range(table.columnCount()):
                item = table.item(row, col)
                value = item.text() if item else ""
                worksheet.cell(row=row + 2, column=col + 1, value=value)

        # تعديل عرض الأعمدة
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

        workbook.save(file_path)

    def export_to_csv(self, table, file_path):
        """تصدير إلى CSV"""
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)

            # إضافة العناوين
            headers = []
            for col in range(table.columnCount()):
                header = table.horizontalHeaderItem(col)
                headers.append(header.text() if header else f"عمود {col + 1}")
            writer.writerow(headers)

            # إضافة البيانات
            for row in range(table.rowCount()):
                row_data = []
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    row_data.append(item.text() if item else "")
                writer.writerow(row_data)

    # دوال التقارير الأخرى (مبسطة)
    def generate_monthly_report(self):
        """إنشاء تقرير شهري"""
        QMessageBox.information(self, "قريباً", "تقرير الحوالات الشهرية قيد التطوير")

    def generate_supplier_report(self):
        """إنشاء تقرير حسب المورد"""
        QMessageBox.information(self, "قريباً", "تقرير الحوالات حسب المورد قيد التطوير")

    def generate_balances_report(self):
        """إنشاء تقرير الأرصدة"""
        QMessageBox.information(self, "قريباً", "تقرير أرصدة الموردين قيد التطوير")

    def generate_pending_report(self):
        """إنشاء تقرير الحوالات المعلقة"""
        QMessageBox.information(self, "قريباً", "تقرير الحوالات المعلقة قيد التطوير")

    def generate_fees_report(self):
        """إنشاء تقرير الرسوم"""
        QMessageBox.information(self, "قريباً", "تقرير الرسوم والعمولات قيد التطوير")

    def show_pie_chart(self):
        """عرض الرسم الدائري"""
        QMessageBox.information(self, "قريباً", "الرسوم البيانية الدائرية قيد التطوير")

    def show_bar_chart(self):
        """عرض الرسم البياني العمودي"""
        QMessageBox.information(self, "قريباً", "الرسوم البيانية العمودية قيد التطوير")

    def show_line_chart(self):
        """عرض الرسم الخطي"""
        QMessageBox.information(self, "قريباً", "الرسوم البيانية الخطية قيد التطوير")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قريباً", "وظيفة الطباعة قيد التطوير")

    def save_report(self):
        """حفظ التقرير"""
        QMessageBox.information(self, "قريباً", "وظيفة حفظ التقرير قيد التطوير")
