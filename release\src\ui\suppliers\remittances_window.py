# -*- coding: utf-8 -*-
"""
نافذة إدارة الحوالات الرئيسية
Main Remittances Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTabWidget, QLabel, QPushButton, QFrame, QGridLayout,
                               QMessageBox, QMenuBar, QMenu, QToolBar, QStatusBar,
                               QSplitter, QGroupBox, QFormLayout, QLineEdit,
                               QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox,
                               QTextEdit, QCheckBox, QTableWidget, QTableWidgetItem,
                               QHeaderView, QAbstractItemView, QProgressBar)
from PySide6.QtCore import Qt, Signal, QDate, QTimer
from PySide6.QtGui import QFont, QAction, QIcon, QPalette, QColor

from .remittances_management import RemittancesManagementWidget
from .remittances_tracking import RemittancesTrackingWidget
from .supplier_accounts import SupplierAccountsWidget
from .remittances_reports import RemittancesReportsWidget
from .banks_management import BanksManagementWidget


class RemittancesWindow(QMainWindow):
    """النافذة الرئيسية لإدارة الحوالات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_window()
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_connections()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.setWindowTitle("إدارة الحوالات المتقدمة - ProShipment")
        self.setMinimumSize(1600, 900)
        
        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()
        
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 10)
        self.setFont(font)
        
        # تطبيق تخطيط RTL
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق الستايل الحديث
        self.apply_modern_style()
        
    def apply_modern_style(self):
        """تطبيق الستايل الحديث"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            
            QTabWidget::pane {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background: white;
                margin-top: 5px;
            }
            
            QTabWidget::tab-bar {
                alignment: center;
            }
            
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px solid #dee2e6;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                color: #495057;
                min-width: 120px;
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                color: white;
                border-color: #007bff;
            }
            
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                border-color: #2196f3;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background: white;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #495057;
                font-size: 12px;
            }
            
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0056b3, stop:1 #004085);
            }
            
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #004085, stop:1 #002752);
            }
            
            QPushButton:disabled {
                background: #6c757d;
                color: #adb5bd;
            }
            
            QLineEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox, QTextEdit {
                border: 2px solid #ced4da;
                border-radius: 6px;
                padding: 8px;
                background: white;
                selection-background-color: #007bff;
            }
            
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus, 
            QSpinBox:focus, QDoubleSpinBox:focus, QTextEdit:focus {
                border-color: #007bff;
                outline: none;
            }
            
            QTableWidget {
                gridline-color: #dee2e6;
                background: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            
            QTableWidget::item:selected {
                background: #007bff;
                color: white;
            }
            
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #495057, stop:1 #343a40);
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
            
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 6px;
                text-align: center;
                background: #f8f9fa;
            }
            
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #28a745, stop:1 #20c997);
                border-radius: 4px;
            }
        """)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # عنوان النافذة
        title_label = QLabel("🏦 نظام إدارة الحوالات المتقدم")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #495057;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px solid #dee2e6;
                border-radius: 12px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        main_layout.addWidget(self.tab_widget)
        
        # تبويب إدارة الحوالات
        self.remittances_management_widget = RemittancesManagementWidget()
        self.tab_widget.addTab(self.remittances_management_widget, "💰 إدارة الحوالات")
        
        # تبويب تتبع الحوالات
        self.remittances_tracking_widget = RemittancesTrackingWidget()
        self.tab_widget.addTab(self.remittances_tracking_widget, "📍 تتبع الحوالات")
        
        # تبويب حسابات الموردين
        self.supplier_accounts_widget = SupplierAccountsWidget()
        self.tab_widget.addTab(self.supplier_accounts_widget, "👥 حسابات الموردين")
        
        # تبويب إدارة البنوك
        self.banks_management_widget = BanksManagementWidget()
        self.tab_widget.addTab(self.banks_management_widget, "🏛️ إدارة البنوك")
        
        # تبويب التقارير
        self.remittances_reports_widget = RemittancesReportsWidget()
        self.tab_widget.addTab(self.remittances_reports_widget, "📊 التقارير المالية")
        
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        new_remittance_action = QAction("حوالة جديدة", self)
        new_remittance_action.setShortcut("Ctrl+N")
        new_remittance_action.triggered.connect(self.new_remittance)
        file_menu.addAction(new_remittance_action)
        
        file_menu.addSeparator()
        
        import_action = QAction("استيراد البيانات", self)
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)
        
        export_action = QAction("تصدير البيانات", self)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة التحرير
        edit_menu = menubar.addMenu("تحرير")
        
        refresh_action = QAction("تحديث البيانات", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_data)
        edit_menu.addAction(refresh_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu("التقارير")
        
        daily_report_action = QAction("تقرير يومي", self)
        daily_report_action.triggered.connect(self.generate_daily_report)
        reports_menu.addAction(daily_report_action)
        
        monthly_report_action = QAction("تقرير شهري", self)
        monthly_report_action.triggered.connect(self.generate_monthly_report)
        reports_menu.addAction(monthly_report_action)
        
        supplier_report_action = QAction("تقرير حسب المورد", self)
        supplier_report_action.triggered.connect(self.generate_supplier_report)
        reports_menu.addAction(supplier_report_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setMovable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # زر حوالة جديدة
        new_action = QAction("💰 حوالة جديدة", self)
        new_action.setToolTip("إنشاء حوالة جديدة (Ctrl+N)")
        new_action.triggered.connect(self.new_remittance)
        toolbar.addAction(new_action)

        toolbar.addSeparator()

        # زر تحديث
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setToolTip("تحديث البيانات (F5)")
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)

        # زر البحث
        search_action = QAction("🔍 بحث", self)
        search_action.setToolTip("البحث في الحوالات")
        search_action.triggered.connect(self.search_remittances)
        toolbar.addAction(search_action)

        toolbar.addSeparator()

        # زر التقارير
        reports_action = QAction("📊 تقارير", self)
        reports_action.setToolTip("إنشاء التقارير")
        reports_action.triggered.connect(self.show_reports_tab)
        toolbar.addAction(reports_action)

        # زر الإعدادات
        settings_action = QAction("⚙️ إعدادات", self)
        settings_action.setToolTip("إعدادات النظام")
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()

        # تسمية الحالة
        self.status_label = QLabel("جاهز - نظام إدارة الحوالات")
        status_bar.addWidget(self.status_label)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_bar.addPermanentWidget(self.progress_bar)

        # معلومات المستخدم
        self.user_label = QLabel("المستخدم: مدير النظام")
        status_bar.addPermanentWidget(self.user_label)

        # الوقت الحالي
        self.time_label = QLabel()
        self.update_time()
        status_bar.addPermanentWidget(self.time_label)

        # تحديث الوقت كل ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)

    def setup_connections(self):
        """إعداد الاتصالات بين الويدجتات"""
        # ربط إشارات التبويبات
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

        # ربط إشارات الويدجتات
        if hasattr(self.remittances_management_widget, 'remittance_created'):
            self.remittances_management_widget.remittance_created.connect(self.on_remittance_created)

        if hasattr(self.remittances_tracking_widget, 'status_updated'):
            self.remittances_tracking_widget.status_updated.connect(self.on_status_updated)

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"الوقت: {current_time}")

    def update_status(self, message, timeout=3000):
        """تحديث رسالة الحالة"""
        self.status_label.setText(message)
        if timeout > 0:
            QTimer.singleShot(timeout, lambda: self.status_label.setText("جاهز - نظام إدارة الحوالات"))

    def show_progress(self, show=True, value=0):
        """إظهار أو إخفاء شريط التقدم"""
        self.progress_bar.setVisible(show)
        if show:
            self.progress_bar.setValue(value)

    # ===== وظائف القوائم والأدوات =====

    def new_remittance(self):
        """إنشاء حوالة جديدة"""
        self.tab_widget.setCurrentIndex(0)  # التبديل إلى تبويب إدارة الحوالات
        if hasattr(self.remittances_management_widget, 'new_remittance'):
            self.remittances_management_widget.new_remittance()
        self.update_status("تم فتح نموذج حوالة جديدة")

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.show_progress(True, 0)
        self.update_status("جاري تحديث البيانات...")

        try:
            # تحديث كل تبويب
            widgets = [
                self.remittances_management_widget,
                self.remittances_tracking_widget,
                self.supplier_accounts_widget,
                self.banks_management_widget,
                self.remittances_reports_widget
            ]

            total_widgets = len(widgets)
            for i, widget in enumerate(widgets):
                if hasattr(widget, 'refresh_data'):
                    widget.refresh_data()
                self.show_progress(True, int((i + 1) / total_widgets * 100))

            self.update_status("تم تحديث البيانات بنجاح")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحديث البيانات:\n{str(e)}")
            self.update_status("فشل في تحديث البيانات")

        finally:
            self.show_progress(False)

    def search_remittances(self):
        """البحث في الحوالات"""
        self.tab_widget.setCurrentIndex(1)  # التبديل إلى تبويب التتبع
        if hasattr(self.remittances_tracking_widget, 'show_search'):
            self.remittances_tracking_widget.show_search()
        self.update_status("تم فتح نافذة البحث")

    def show_reports_tab(self):
        """إظهار تبويب التقارير"""
        self.tab_widget.setCurrentIndex(4)  # التبديل إلى تبويب التقارير
        self.update_status("تم فتح تبويب التقارير")

    def show_settings(self):
        """إظهار إعدادات النظام"""
        QMessageBox.information(self, "الإعدادات", "إعدادات نظام الحوالات قيد التطوير")

    def import_data(self):
        """استيراد البيانات"""
        QMessageBox.information(self, "استيراد البيانات", "وظيفة استيراد البيانات قيد التطوير")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "تصدير البيانات", "وظيفة تصدير البيانات قيد التطوير")

    def generate_daily_report(self):
        """إنشاء تقرير يومي"""
        self.show_reports_tab()
        if hasattr(self.remittances_reports_widget, 'generate_daily_report'):
            self.remittances_reports_widget.generate_daily_report()

    def generate_monthly_report(self):
        """إنشاء تقرير شهري"""
        self.show_reports_tab()
        if hasattr(self.remittances_reports_widget, 'generate_monthly_report'):
            self.remittances_reports_widget.generate_monthly_report()

    def generate_supplier_report(self):
        """إنشاء تقرير حسب المورد"""
        self.show_reports_tab()
        if hasattr(self.remittances_reports_widget, 'generate_supplier_report'):
            self.remittances_reports_widget.generate_supplier_report()

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(
            self,
            "حول نظام إدارة الحوالات",
            "نظام إدارة الحوالات المتقدم\n"
            "جزء من نظام ProShipment المتكامل\n\n"
            "المميزات:\n"
            "• إدارة الحوالات بأرقام تلقائية\n"
            "• دعم العملات المتعددة\n"
            "• تتبع حالة الحوالات خطوة بخطوة\n"
            "• إدارة حسابات الموردين\n"
            "• تقارير مالية شاملة\n"
            "• نظام أمان متقدم\n\n"
            "الإصدار 1.0"
        )

    # ===== وظائف الأحداث =====

    def on_tab_changed(self, index):
        """عند تغيير التبويب"""
        tab_names = [
            "إدارة الحوالات",
            "تتبع الحوالات",
            "حسابات الموردين",
            "إدارة البنوك",
            "التقارير المالية"
        ]

        if 0 <= index < len(tab_names):
            self.update_status(f"تم التبديل إلى: {tab_names[index]}")

    def on_remittance_created(self, remittance_data):
        """عند إنشاء حوالة جديدة"""
        self.update_status(f"تم إنشاء حوالة جديدة: {remittance_data.get('number', 'غير محدد')}")
        # تحديث التبويبات الأخرى
        if hasattr(self.remittances_tracking_widget, 'refresh_data'):
            self.remittances_tracking_widget.refresh_data()

    def on_status_updated(self, remittance_number, old_status, new_status):
        """عند تحديث حالة الحوالة"""
        self.update_status(f"تم تحديث حالة الحوالة {remittance_number} من {old_status} إلى {new_status}")

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            "تأكيد الإغلاق",
            "هل أنت متأكد من إغلاق نظام إدارة الحوالات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # حفظ الإعدادات
            self.save_settings()
            event.accept()
        else:
            event.ignore()

    def save_settings(self):
        """حفظ إعدادات النافذة"""
        # يمكن إضافة حفظ إعدادات النافذة هنا
        pass
