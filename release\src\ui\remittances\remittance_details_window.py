#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تفاصيل طلب الحوالة
Remittance Request Details Window
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QFrame, QGridLayout, QScrollArea,
                               QGroupBox, QTextEdit, QWidget)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
import sqlite3
from pathlib import Path
from datetime import datetime

class RemittanceDetailsWindow(QDialog):
    """نافذة عرض تفاصيل طلب الحوالة"""
    
    def __init__(self, request_id, parent=None):
        super().__init__(parent)
        self.request_id = request_id
        self.request_data = None
        self.setup_ui()
        self.load_request_data()
        self.populate_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تفاصيل طلب الحوالة")
        self.setGeometry(100, 100, 900, 700)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # رأس النافذة
        self.create_header(main_layout)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # محتوى التفاصيل
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        # البيانات الأساسية
        self.create_basic_info_section(content_layout)
        
        # معلومات المرسل
        self.create_sender_section(content_layout)
        
        # معلومات المستقبل
        self.create_receiver_section(content_layout)
        
        # تفاصيل إضافية
        self.create_additional_info_section(content_layout)
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
        # أزرار التحكم
        self.create_control_buttons(main_layout)
        
        # تطبيق الأنماط
        self.apply_styles()
    
    def create_header(self, layout):
        """إنشاء رأس النافذة"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة ونص الرأس
        title_label = QLabel("📄 تفاصيل طلب الحوالة")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 20px;
                font-weight: bold;
                background: transparent;
            }
        """)
        
        self.request_id_label = QLabel(f"رقم الطلب: {self.request_id}")
        self.request_id_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                background: transparent;
            }
        """)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.request_id_label)
        
        layout.addWidget(header_frame)
    
    def create_basic_info_section(self, layout):
        """إنشاء قسم البيانات الأساسية"""
        group = QGroupBox("📋 البيانات الأساسية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        grid_layout = QGridLayout(group)
        
        # إنشاء الحقول
        self.request_number_label = self.create_info_label("رقم الطلب:")
        self.request_date_label = self.create_info_label("تاريخ الطلب:")
        self.branch_label = self.create_info_label("الفرع:")
        self.exchanger_label = self.create_info_label("الصراف:")
        self.amount_label = self.create_info_label("مبلغ الحوالة:")
        self.currency_label = self.create_info_label("العملة:")
        self.purpose_label = self.create_info_label("الغرض من التحويل:")
        self.status_label = self.create_info_label("الحالة:")
        
        # ترتيب الحقول
        grid_layout.addWidget(QLabel("📄 رقم الطلب:"), 0, 0)
        grid_layout.addWidget(self.request_number_label, 0, 1)
        grid_layout.addWidget(QLabel("📅 تاريخ الطلب:"), 0, 2)
        grid_layout.addWidget(self.request_date_label, 0, 3)
        
        grid_layout.addWidget(QLabel("🏢 الفرع:"), 1, 0)
        grid_layout.addWidget(self.branch_label, 1, 1)
        grid_layout.addWidget(QLabel("👤 الصراف:"), 1, 2)
        grid_layout.addWidget(self.exchanger_label, 1, 3)
        
        grid_layout.addWidget(QLabel("💰 مبلغ الحوالة:"), 2, 0)
        grid_layout.addWidget(self.amount_label, 2, 1)
        grid_layout.addWidget(QLabel("💱 العملة:"), 2, 2)
        grid_layout.addWidget(self.currency_label, 2, 3)
        
        grid_layout.addWidget(QLabel("🎯 الغرض:"), 3, 0)
        grid_layout.addWidget(self.purpose_label, 3, 1, 1, 2)
        grid_layout.addWidget(QLabel("📊 الحالة:"), 3, 3)
        grid_layout.addWidget(self.status_label, 3, 3)
        
        layout.addWidget(group)
    
    def create_sender_section(self, layout):
        """إنشاء قسم معلومات المرسل"""
        group = QGroupBox("👤 معلومات المرسل")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 2px solid #27ae60;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        grid_layout = QGridLayout(group)
        
        # إنشاء الحقول
        self.sender_name_label = self.create_info_label("الاسم الكامل:")
        self.sender_entity_label = self.create_info_label("الجهة:")
        self.sender_phone_label = self.create_info_label("رقم الهاتف:")
        self.sender_fax_label = self.create_info_label("رقم الفاكس:")
        self.sender_mobile_label = self.create_info_label("رقم الموبايل:")
        self.sender_pobox_label = self.create_info_label("ص.ب:")
        self.sender_email_label = self.create_info_label("البريد الإلكتروني:")
        self.sender_address_label = self.create_info_label("العنوان:")
        
        # ترتيب الحقول
        grid_layout.addWidget(QLabel("👤 الاسم الكامل:"), 0, 0)
        grid_layout.addWidget(self.sender_name_label, 0, 1)
        grid_layout.addWidget(QLabel("🏛️ الجهة:"), 0, 2)
        grid_layout.addWidget(self.sender_entity_label, 0, 3)
        
        grid_layout.addWidget(QLabel("📱 رقم الهاتف:"), 1, 0)
        grid_layout.addWidget(self.sender_phone_label, 1, 1)
        grid_layout.addWidget(QLabel("📠 رقم الفاكس:"), 1, 2)
        grid_layout.addWidget(self.sender_fax_label, 1, 3)
        
        grid_layout.addWidget(QLabel("📲 رقم الموبايل:"), 2, 0)
        grid_layout.addWidget(self.sender_mobile_label, 2, 1)
        grid_layout.addWidget(QLabel("📮 ص.ب:"), 2, 2)
        grid_layout.addWidget(self.sender_pobox_label, 2, 3)
        
        grid_layout.addWidget(QLabel("📧 البريد الإلكتروني:"), 3, 0)
        grid_layout.addWidget(self.sender_email_label, 3, 1, 1, 2)
        
        grid_layout.addWidget(QLabel("📍 العنوان:"), 4, 0)
        grid_layout.addWidget(self.sender_address_label, 4, 1, 1, 3)
        
        layout.addWidget(group)
    
    def create_receiver_section(self, layout):
        """إنشاء قسم معلومات المستقبل"""
        group = QGroupBox("👥 معلومات المستقبل")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        grid_layout = QGridLayout(group)
        
        # إنشاء الحقول
        self.receiver_name_label = self.create_info_label("اسم المستقبل:")
        self.receiver_account_label = self.create_info_label("رقم الحساب:")
        self.receiver_bank_label = self.create_info_label("اسم البنك:")
        self.receiver_branch_label = self.create_info_label("فرع البنك:")
        self.receiver_swift_label = self.create_info_label("السويفت:")
        self.receiver_country_label = self.create_info_label("البلد:")
        self.receiver_address_label = self.create_info_label("العنوان:")
        
        # ترتيب الحقول
        grid_layout.addWidget(QLabel("👤 اسم المستقبل:"), 0, 0)
        grid_layout.addWidget(self.receiver_name_label, 0, 1)
        grid_layout.addWidget(QLabel("💳 رقم الحساب:"), 0, 2)
        grid_layout.addWidget(self.receiver_account_label, 0, 3)
        
        grid_layout.addWidget(QLabel("🏦 اسم البنك:"), 1, 0)
        grid_layout.addWidget(self.receiver_bank_label, 1, 1)
        grid_layout.addWidget(QLabel("🏢 فرع البنك:"), 1, 2)
        grid_layout.addWidget(self.receiver_branch_label, 1, 3)
        
        grid_layout.addWidget(QLabel("🔗 السويفت:"), 2, 0)
        grid_layout.addWidget(self.receiver_swift_label, 2, 1)
        grid_layout.addWidget(QLabel("🌍 البلد:"), 2, 2)
        grid_layout.addWidget(self.receiver_country_label, 2, 3)
        
        grid_layout.addWidget(QLabel("📍 العنوان:"), 3, 0)
        grid_layout.addWidget(self.receiver_address_label, 3, 1, 1, 3)
        
        layout.addWidget(group)
    
    def create_additional_info_section(self, layout):
        """إنشاء قسم التفاصيل الإضافية"""
        group = QGroupBox("📝 تفاصيل إضافية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 2px solid #f39c12;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        grid_layout = QGridLayout(group)
        
        # الملاحظات
        grid_layout.addWidget(QLabel("📝 الملاحظات:"), 0, 0)
        self.notes_text = QTextEdit()
        self.notes_text.setMaximumHeight(100)
        self.notes_text.setReadOnly(True)
        self.notes_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 5px;
                background-color: #f8f9fa;
            }
        """)
        grid_layout.addWidget(self.notes_text, 0, 1, 1, 3)
        
        # تواريخ النظام
        self.created_at_label = self.create_info_label("تاريخ الإنشاء:")
        self.updated_at_label = self.create_info_label("تاريخ التحديث:")
        
        grid_layout.addWidget(QLabel("📅 تاريخ الإنشاء:"), 1, 0)
        grid_layout.addWidget(self.created_at_label, 1, 1)
        grid_layout.addWidget(QLabel("🔄 تاريخ التحديث:"), 1, 2)
        grid_layout.addWidget(self.updated_at_label, 1, 3)
        
        layout.addWidget(group)
    
    def create_info_label(self, text):
        """إنشاء تسمية معلومات"""
        label = QLabel(text)
        label.setStyleSheet("""
            QLabel {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 12px;
            }
        """)
        label.setWordWrap(True)
        return label
    
    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        
        # زر طباعة
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setStyleSheet("""
            QPushButton {
                background: #9b59b6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #8e44ad;
            }
        """)
        print_btn.clicked.connect(self.print_details)
        
        # زر تحديث الحالة
        update_btn = QPushButton("🔄 تحديث الحالة")
        update_btn.setStyleSheet("""
            QPushButton {
                background: #f39c12;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #e67e22;
            }
        """)
        update_btn.clicked.connect(self.update_status)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c0392b;
            }
        """)
        close_btn.clicked.connect(self.close)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(update_btn)
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def apply_styles(self):
        """تطبيق الأنماط العامة"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel {
                font-size: 12px;
            }
        """)
    
    def load_request_data(self):
        """تحميل بيانات الطلب من قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM remittance_requests WHERE id = ?", (self.request_id,))
            result = cursor.fetchone()
            
            if result:
                # الحصول على أسماء الأعمدة
                cursor.execute("PRAGMA table_info(remittance_requests)")
                columns = [column[1] for column in cursor.fetchall()]
                
                # تحويل النتيجة إلى قاموس
                self.request_data = dict(zip(columns, result))
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات الطلب: {e}")
            self.request_data = {}
    
    def populate_data(self):
        """تعبئة البيانات في النافذة"""
        if not self.request_data:
            return
        
        # البيانات الأساسية
        self.request_number_label.setText(self.request_data.get('request_number', 'غير محدد'))
        self.request_date_label.setText(self.request_data.get('request_date', 'غير محدد'))
        self.branch_label.setText(self.request_data.get('branch', 'غير محدد'))
        self.exchanger_label.setText(self.request_data.get('exchanger', 'غير محدد'))
        
        # المبلغ والعملة
        amount = self.request_data.get('remittance_amount') or self.request_data.get('amount', 0)
        currency = self.request_data.get('currency', 'غير محدد')
        self.amount_label.setText(f"{amount} {currency}")
        self.currency_label.setText(currency)
        
        self.purpose_label.setText(self.request_data.get('transfer_purpose', 'غير محدد'))
        self.status_label.setText(self.request_data.get('status', 'غير محدد'))
        
        # معلومات المرسل
        self.sender_name_label.setText(self.request_data.get('sender_name', 'غير محدد'))
        self.sender_entity_label.setText(self.request_data.get('sender_entity', 'غير محدد'))
        self.sender_phone_label.setText(self.request_data.get('sender_phone', 'غير محدد'))
        self.sender_fax_label.setText(self.request_data.get('sender_fax', 'غير محدد'))
        self.sender_mobile_label.setText(self.request_data.get('sender_mobile', 'غير محدد'))
        self.sender_pobox_label.setText(self.request_data.get('sender_pobox', 'غير محدد'))
        self.sender_email_label.setText(self.request_data.get('sender_email', 'غير محدد'))
        self.sender_address_label.setText(self.request_data.get('sender_address', 'غير محدد'))
        
        # معلومات المستقبل
        self.receiver_name_label.setText(self.request_data.get('receiver_name', 'غير محدد'))
        self.receiver_account_label.setText(self.request_data.get('receiver_account', 'غير محدد'))
        self.receiver_bank_label.setText(self.request_data.get('receiver_bank_name', 'غير محدد'))
        self.receiver_branch_label.setText(self.request_data.get('receiver_bank_branch', 'غير محدد'))
        self.receiver_swift_label.setText(self.request_data.get('receiver_swift', 'غير محدد'))
        self.receiver_country_label.setText(self.request_data.get('receiver_country', 'غير محدد'))
        self.receiver_address_label.setText(self.request_data.get('receiver_address', 'غير محدد'))
        
        # التفاصيل الإضافية
        self.notes_text.setPlainText(self.request_data.get('notes', 'لا توجد ملاحظات'))
        
        # التواريخ
        created_at = self.request_data.get('created_at', '')
        if created_at:
            try:
                # تحويل التاريخ إلى تنسيق قابل للقراءة
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                formatted_date = dt.strftime('%Y-%m-%d %H:%M:%S')
                self.created_at_label.setText(formatted_date)
            except:
                self.created_at_label.setText(created_at)
        else:
            self.created_at_label.setText('غير محدد')
        
        updated_at = self.request_data.get('updated_at', 'غير محدد')
        self.updated_at_label.setText(updated_at)
    
    def print_details(self):
        """طباعة تفاصيل الطلب"""
        try:
            # محاولة استخدام النموذج الاحترافي أولاً
            try:
                from .professional_print_template import ProfessionalPrintTemplate
                self.print_window = ProfessionalPrintTemplate(self.request_data)
                self.print_window.show()
            except ImportError:
                # في حالة فشل الاستيراد، استخدم النموذج المتقدم
                try:
                    from .remittance_print_template import RemittancePrintTemplate
                    self.print_window = RemittancePrintTemplate(self.request_data)
                    self.print_window.show()
                except ImportError:
                    # في حالة فشل الاستيراد، استخدم النموذج المبسط
                    from .simple_print_template import SimplePrintTemplate
                    self.print_window = SimplePrintTemplate(self.request_data)
                    self.print_window.show()

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نموذج الطباعة:\n{str(e)}")
    
    def update_status(self):
        """تحديث حالة الطلب"""
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(self, "تحديث الحالة", "ميزة تحديث الحالة قيد التطوير...")

if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # اختبار النافذة مع معرف تجريبي
    window = RemittanceDetailsWindow(1)
    window.show()
    
    sys.exit(app.exec())
