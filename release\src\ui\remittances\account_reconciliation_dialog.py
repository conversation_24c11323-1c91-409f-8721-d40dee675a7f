# -*- coding: utf-8 -*-
"""
نافذة تسوية الحسابات الشاملة والمتقدمة
Comprehensive Account Reconciliation Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QDoubleSpinBox,
                               QPushButton, QGroupBox, QTextEdit, QCheckBox,
                               QMessageBox, QFrame, QTabWidget, QWidget,
                               QTableWidget, QTableWidgetItem, QHeaderView,
                               QAbstractItemView, QProgressBar, QDateEdit,
                               QSplitter, QScrollArea, QCalendarWidget)
from PySide6.QtCore import Qt, Signal, QDate, QTimer
from PySide6.QtGui import QFont, QColor, QIcon

import sqlite3
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal
import json

from ...utils.arabic_support import reshape_arabic_text

class AccountReconciliationDialog(QDialog):
    """نافذة تسوية الحسابات الشاملة والمتقدمة"""
    
    reconciliation_completed = Signal(int)  # معرف التسوية
    
    def __init__(self, parent=None, account_id=None):
        super().__init__(parent)
        self.setWindowTitle("تسوية الحسابات - ProShipment")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        self.setModal(True)
        
        # متغيرات النافذة
        self.account_id = account_id
        self.accounts_data = []
        self.transactions_data = []
        self.reconciliation_items = []
        self.selected_account_data = None
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        
        # تحميل البيانات
        self.load_initial_data()
        
        # تحديد الحساب إذا تم تمريره
        if self.account_id:
            self.select_account_by_id(self.account_id)
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        self.create_header_section(layout)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب اختيار الحساب والفترة
        selection_tab = self.create_selection_tab()
        self.tabs.addTab(selection_tab, "اختيار الحساب والفترة")
        
        # تبويب المعاملات والمطابقة
        matching_tab = self.create_matching_tab()
        self.tabs.addTab(matching_tab, "المعاملات والمطابقة")
        
        # تبويب الفروقات والتعديلات
        adjustments_tab = self.create_adjustments_tab()
        self.tabs.addTab(adjustments_tab, "الفروقات والتعديلات")
        
        # تبويب التقرير النهائي
        report_tab = self.create_report_tab()
        self.tabs.addTab(report_tab, "التقرير النهائي")
        
        layout.addWidget(self.tabs)
        
        # أزرار الإجراءات
        self.create_action_buttons(layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #059669, stop:1 #047857);
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة التسوية
        icon_label = QLabel("⚖️")
        icon_label.setStyleSheet("font-size: 36px; color: white;")
        header_layout.addWidget(icon_label)
        
        # معلومات العنوان
        info_layout = QVBoxLayout()
        
        title_label = QLabel("تسوية الحسابات")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        
        subtitle_label = QLabel("مطابقة وتسوية المعاملات مع كشوفات الحسابات الخارجية")
        subtitle_label.setFont(QFont("Arial", 11))
        subtitle_label.setStyleSheet("color: #d1fae5;")
        
        info_layout.addWidget(title_label)
        info_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        stats_layout = QVBoxLayout(stats_frame)
        
        self.reconciliation_number_label = QLabel(f"رقم التسوية\n{self.generate_reconciliation_number()}")
        self.reconciliation_date_label = QLabel(f"تاريخ التسوية\n{datetime.now().strftime('%Y-%m-%d')}")
        self.status_label = QLabel("الحالة\nجديد")
        
        for label in [self.reconciliation_number_label, self.reconciliation_date_label, self.status_label]:
            label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    text-align: center;
                    font-size: 11px;
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(label)
        
        header_layout.addWidget(stats_frame)
        layout.addWidget(header_frame)
        
    def create_selection_tab(self):
        """إنشاء تبويب اختيار الحساب والفترة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # اختيار الحساب
        account_group = QGroupBox("اختيار الحساب")
        account_layout = QGridLayout(account_group)
        
        # الحساب
        account_layout.addWidget(QLabel("الحساب: *"), 0, 0)
        self.account_combo = QComboBox()
        self.account_combo.setMinimumWidth(400)
        account_layout.addWidget(self.account_combo, 0, 1)
        
        # معلومات الحساب المحدد
        account_layout.addWidget(QLabel("المورد:"), 1, 0)
        self.supplier_info_label = QLabel("-")
        self.supplier_info_label.setStyleSheet("background-color: #f3f4f6; padding: 8px; border-radius: 4px;")
        account_layout.addWidget(self.supplier_info_label, 1, 1)
        
        account_layout.addWidget(QLabel("العملة:"), 2, 0)
        self.currency_info_label = QLabel("-")
        self.currency_info_label.setStyleSheet("background-color: #f3f4f6; padding: 8px; border-radius: 4px;")
        account_layout.addWidget(self.currency_info_label, 2, 1)
        
        account_layout.addWidget(QLabel("الرصيد الحالي:"), 3, 0)
        self.balance_info_label = QLabel("0.00")
        self.balance_info_label.setStyleSheet("background-color: #f3f4f6; padding: 8px; border-radius: 4px; font-weight: bold; color: #059669;")
        account_layout.addWidget(self.balance_info_label, 3, 1)
        
        layout.addWidget(account_group)
        
        # فترة التسوية
        period_group = QGroupBox("فترة التسوية")
        period_layout = QGridLayout(period_group)
        
        # من تاريخ
        period_layout.addWidget(QLabel("من تاريخ: *"), 0, 0)
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        self.from_date.setCalendarPopup(True)
        period_layout.addWidget(self.from_date, 0, 1)
        
        # إلى تاريخ
        period_layout.addWidget(QLabel("إلى تاريخ: *"), 0, 2)
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.setCalendarPopup(True)
        period_layout.addWidget(self.to_date, 0, 3)
        
        # فترات سريعة
        quick_periods_layout = QHBoxLayout()
        
        last_week_btn = QPushButton("الأسبوع الماضي")
        last_week_btn.clicked.connect(lambda: self.set_quick_period(7))
        quick_periods_layout.addWidget(last_week_btn)
        
        last_month_btn = QPushButton("الشهر الماضي")
        last_month_btn.clicked.connect(lambda: self.set_quick_period(30))
        quick_periods_layout.addWidget(last_month_btn)
        
        last_quarter_btn = QPushButton("الربع الماضي")
        last_quarter_btn.clicked.connect(lambda: self.set_quick_period(90))
        quick_periods_layout.addWidget(last_quarter_btn)
        
        quick_periods_layout.addStretch()
        
        period_layout.addLayout(quick_periods_layout, 1, 0, 1, 4)
        
        layout.addWidget(period_group)
        
        # إعدادات التسوية
        settings_group = QGroupBox("إعدادات التسوية")
        settings_layout = QGridLayout(settings_group)
        
        # نوع التسوية
        settings_layout.addWidget(QLabel("نوع التسوية:"), 0, 0)
        self.reconciliation_type_combo = QComboBox()
        self.reconciliation_type_combo.addItems([
            "تسوية شاملة", "تسوية جزئية", "تسوية المعاملات المعلقة",
            "تسوية الفروقات", "تسوية نهاية الفترة"
        ])
        settings_layout.addWidget(self.reconciliation_type_combo, 0, 1)
        
        # مستوى التفاصيل
        settings_layout.addWidget(QLabel("مستوى التفاصيل:"), 1, 0)
        self.detail_level_combo = QComboBox()
        self.detail_level_combo.addItems(["ملخص", "تفصيلي", "شامل"])
        self.detail_level_combo.setCurrentText("تفصيلي")
        settings_layout.addWidget(self.detail_level_combo, 1, 1)
        
        # تضمين المعاملات المعلقة
        self.include_pending_checkbox = QCheckBox("تضمين المعاملات المعلقة")
        self.include_pending_checkbox.setChecked(True)
        settings_layout.addWidget(self.include_pending_checkbox, 2, 0, 1, 2)
        
        # تضمين المعاملات الملغاة
        self.include_cancelled_checkbox = QCheckBox("تضمين المعاملات الملغاة")
        settings_layout.addWidget(self.include_cancelled_checkbox, 3, 0, 1, 2)
        
        layout.addWidget(settings_group)
        
        # زر تحميل البيانات
        load_data_btn = QPushButton("📊 تحميل بيانات الفترة")
        load_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover { background-color: #2563eb; }
        """)
        load_data_btn.clicked.connect(self.load_period_data)
        layout.addWidget(load_data_btn)
        
        layout.addStretch()
        return tab
        
    def create_matching_tab(self):
        """إنشاء تبويب المعاملات والمطابقة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # إحصائيات المعاملات
        stats_group = QGroupBox("إحصائيات المعاملات")
        stats_layout = QGridLayout(stats_group)
        
        self.total_transactions_label = QLabel("إجمالي المعاملات: 0")
        self.matched_transactions_label = QLabel("المعاملات المطابقة: 0")
        self.unmatched_transactions_label = QLabel("المعاملات غير المطابقة: 0")
        self.total_amount_label = QLabel("إجمالي المبلغ: 0.00")
        
        stats_layout.addWidget(self.total_transactions_label, 0, 0)
        stats_layout.addWidget(self.matched_transactions_label, 0, 1)
        stats_layout.addWidget(self.unmatched_transactions_label, 1, 0)
        stats_layout.addWidget(self.total_amount_label, 1, 1)
        
        layout.addWidget(stats_group)
        
        # جدول المعاملات
        transactions_group = QGroupBox("المعاملات")
        transactions_layout = QVBoxLayout(transactions_group)
        
        # أدوات الجدول
        table_tools_layout = QHBoxLayout()
        
        auto_match_btn = QPushButton("🔄 مطابقة تلقائية")
        auto_match_btn.clicked.connect(self.auto_match_transactions)
        table_tools_layout.addWidget(auto_match_btn)
        
        manual_match_btn = QPushButton("✋ مطابقة يدوية")
        manual_match_btn.clicked.connect(self.manual_match_selected)
        table_tools_layout.addWidget(manual_match_btn)
        
        unmatch_btn = QPushButton("❌ إلغاء مطابقة")
        unmatch_btn.clicked.connect(self.unmatch_selected)
        table_tools_layout.addWidget(unmatch_btn)
        
        table_tools_layout.addStretch()
        
        filter_combo = QComboBox()
        filter_combo.addItems(["جميع المعاملات", "المطابقة", "غير المطابقة", "المعلقة"])
        filter_combo.currentTextChanged.connect(self.filter_transactions)
        table_tools_layout.addWidget(QLabel("تصفية:"))
        table_tools_layout.addWidget(filter_combo)
        
        transactions_layout.addLayout(table_tools_layout)
        
        # جدول المعاملات
        self.transactions_table = QTableWidget()
        self.setup_transactions_table()
        transactions_layout.addWidget(self.transactions_table)
        
        layout.addWidget(transactions_group)
        
        return tab

    # دوال مبسطة للنوافذ الأخرى
    def create_adjustments_tab(self):
        """إنشاء تبويب الفروقات والتعديلات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        info_label = QLabel("تبويب الفروقات والتعديلات قيد التطوير")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 16px; color: #6b7280; padding: 50px;")
        layout.addWidget(info_label)

        return tab

    def create_report_tab(self):
        """إنشاء تبويب التقرير النهائي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        info_label = QLabel("تبويب التقرير النهائي قيد التطوير")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 16px; color: #6b7280; padding: 50px;")
        layout.addWidget(info_label)

        return tab

    def create_action_buttons(self, layout):
        """إنشاء أزرار الإجراءات"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        buttons_layout.addWidget(self.progress_bar)

        buttons_layout.addStretch()

        # زر المعاينة
        preview_btn = QPushButton("👁️ معاينة التسوية")
        preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #4b5563; }
        """)
        preview_btn.clicked.connect(self.preview_reconciliation)
        buttons_layout.addWidget(preview_btn)

        # زر الحفظ كمسودة
        draft_btn = QPushButton("📝 حفظ كمسودة")
        draft_btn.setStyleSheet("""
            QPushButton {
                background-color: #f59e0b;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #d97706; }
        """)
        draft_btn.clicked.connect(self.save_as_draft)
        buttons_layout.addWidget(draft_btn)

        # زر إتمام التسوية
        complete_btn = QPushButton("✅ إتمام التسوية")
        complete_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #059669; }
        """)
        complete_btn.clicked.connect(self.complete_reconciliation)
        buttons_layout.addWidget(complete_btn)

        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #dc2626; }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addWidget(buttons_frame)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # تحديث معلومات الحساب عند التغيير
        self.account_combo.currentTextChanged.connect(self.update_account_info)

        # تحديث البيانات عند تغيير التواريخ
        self.from_date.dateChanged.connect(self.update_period_info)
        self.to_date.dateChanged.connect(self.update_period_info)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل الحسابات
            cursor.execute("""
                SELECT sa.id, sa.account_number, s.name as supplier_name,
                       c.code as currency_code, sa.current_balance, sa.available_balance
                FROM supplier_accounts sa
                LEFT JOIN suppliers s ON sa.supplier_id = s.id
                LEFT JOIN currencies c ON sa.currency_id = c.id
                WHERE sa.is_active = 1
                ORDER BY s.name
            """)
            self.accounts_data = cursor.fetchall()

            self.account_combo.addItem("اختر الحساب...", None)
            for account in self.accounts_data:
                display_text = f"{account[1]} - {account[2]} ({account[4]:,.2f} {account[3]})"
                self.account_combo.addItem(display_text, account[0])

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def select_account_by_id(self, account_id):
        """تحديد حساب بواسطة المعرف"""
        for i in range(self.account_combo.count()):
            if self.account_combo.itemData(i) == account_id:
                self.account_combo.setCurrentIndex(i)
                break

    def update_account_info(self):
        """تحديث معلومات الحساب المحددة"""
        account_id = self.account_combo.currentData()
        if account_id:
            # البحث عن بيانات الحساب
            for account in self.accounts_data:
                if account[0] == account_id:
                    self.selected_account_data = account
                    self.supplier_info_label.setText(account[2] or "-")
                    self.currency_info_label.setText(account[3] or "-")
                    self.balance_info_label.setText(f"{account[4]:,.2f}")
                    break
        else:
            self.selected_account_data = None
            self.supplier_info_label.setText("-")
            self.currency_info_label.setText("-")
            self.balance_info_label.setText("0.00")

    def set_quick_period(self, days):
        """تعيين فترة سريعة"""
        end_date = QDate.currentDate()
        start_date = end_date.addDays(-days)

        self.from_date.setDate(start_date)
        self.to_date.setDate(end_date)

    def update_period_info(self):
        """تحديث معلومات الفترة"""
        # يمكن إضافة منطق لتحديث المعلومات عند تغيير الفترة
        pass

    def generate_reconciliation_number(self):
        """توليد رقم تسوية فريد"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"REC{timestamp}"

    def load_period_data(self):
        """تحميل بيانات الفترة"""
        if not self.selected_account_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار الحساب أولاً")
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)

            # تحميل المعاملات للفترة المحددة
            self.load_transactions_for_period()

            # الانتقال إلى تبويب المطابقة
            self.tabs.setCurrentIndex(1)

            QMessageBox.information(self, "تم", "تم تحميل بيانات الفترة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def load_transactions_for_period(self):
        """تحميل المعاملات للفترة المحددة"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            from_date_str = self.from_date.date().toString("yyyy-MM-dd")
            to_date_str = self.to_date.date().toString("yyyy-MM-dd")

            query = """
                SELECT id, transaction_type, amount, balance_after, description,
                       reference_number, status, created_at
                FROM account_transactions
                WHERE account_id = ? AND DATE(created_at) BETWEEN ? AND ?
                ORDER BY created_at
            """

            cursor.execute(query, (self.selected_account_data[0], from_date_str, to_date_str))
            self.transactions_data = cursor.fetchall()

            # تحديث جدول المعاملات
            self.update_transactions_table()

            # تحديث الإحصائيات
            self.update_transaction_statistics()

            conn.close()

        except Exception as e:
            raise e

    def setup_transactions_table(self):
        """إعداد جدول المعاملات"""
        headers = ["المعرف", "النوع", "المبلغ", "الرصيد بعد", "الوصف", "المرجع", "الحالة", "التاريخ", "مطابق"]
        self.transactions_table.setColumnCount(len(headers))
        self.transactions_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.transactions_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.transactions_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.transactions_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def update_transactions_table(self):
        """تحديث جدول المعاملات"""
        self.transactions_table.setRowCount(len(self.transactions_data))

        for row, transaction in enumerate(self.transactions_data):
            # المعرف
            self.transactions_table.setItem(row, 0, QTableWidgetItem(str(transaction[0])))

            # النوع
            self.transactions_table.setItem(row, 1, QTableWidgetItem(transaction[1] or ""))

            # المبلغ
            amount_item = QTableWidgetItem(f"{transaction[2]:,.2f}")
            if transaction[2] < 0:
                amount_item.setForeground(QColor("#ef4444"))
            else:
                amount_item.setForeground(QColor("#059669"))
            self.transactions_table.setItem(row, 2, amount_item)

            # الرصيد بعد
            self.transactions_table.setItem(row, 3, QTableWidgetItem(f"{transaction[3]:,.2f}"))

            # الوصف
            self.transactions_table.setItem(row, 4, QTableWidgetItem(transaction[4] or ""))

            # المرجع
            self.transactions_table.setItem(row, 5, QTableWidgetItem(transaction[5] or ""))

            # الحالة
            status_item = QTableWidgetItem(transaction[6] or "")
            if transaction[6] == "completed":
                status_item.setBackground(QColor("#dcfce7"))
            elif transaction[6] == "pending":
                status_item.setBackground(QColor("#fef3c7"))
            self.transactions_table.setItem(row, 6, status_item)

            # التاريخ
            date_str = transaction[7][:16] if transaction[7] else ""
            self.transactions_table.setItem(row, 7, QTableWidgetItem(date_str))

            # مطابق (افتراضي: لا)
            matched_item = QTableWidgetItem("لا")
            matched_item.setBackground(QColor("#fef2f2"))
            self.transactions_table.setItem(row, 8, matched_item)

    def update_transaction_statistics(self):
        """تحديث إحصائيات المعاملات"""
        total_transactions = len(self.transactions_data)
        total_amount = sum([trans[2] for trans in self.transactions_data])
        matched_count = 0  # سيتم حسابها لاحقاً
        unmatched_count = total_transactions - matched_count

        self.total_transactions_label.setText(f"إجمالي المعاملات: {total_transactions}")
        self.matched_transactions_label.setText(f"المعاملات المطابقة: {matched_count}")
        self.unmatched_transactions_label.setText(f"المعاملات غير المطابقة: {unmatched_count}")
        self.total_amount_label.setText(f"إجمالي المبلغ: {total_amount:,.2f}")

    # دوال مبسطة للإجراءات
    def auto_match_transactions(self):
        """مطابقة تلقائية للمعاملات"""
        QMessageBox.information(self, "قريباً", "المطابقة التلقائية قيد التطوير")

    def manual_match_selected(self):
        """مطابقة يدوية للمعاملة المحددة"""
        QMessageBox.information(self, "قريباً", "المطابقة اليدوية قيد التطوير")

    def unmatch_selected(self):
        """إلغاء مطابقة المعاملة المحددة"""
        QMessageBox.information(self, "قريباً", "إلغاء المطابقة قيد التطوير")

    def filter_transactions(self, filter_type):
        """تصفية المعاملات"""
        QMessageBox.information(self, "قريباً", "تصفية المعاملات قيد التطوير")

    def preview_reconciliation(self):
        """معاينة التسوية"""
        QMessageBox.information(self, "قريباً", "معاينة التسوية قيد التطوير")

    def save_as_draft(self):
        """حفظ التسوية كمسودة"""
        QMessageBox.information(self, "تم", "تم حفظ التسوية كمسودة")

    def complete_reconciliation(self):
        """إتمام التسوية"""
        if not self.selected_account_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار الحساب أولاً")
            return

        reply = QMessageBox.question(self, "تأكيد", "هل أنت متأكد من إتمام التسوية؟")
        if reply == QMessageBox.Yes:
            try:
                # هنا يتم حفظ التسوية في قاعدة البيانات
                reconciliation_id = self.save_reconciliation_to_database()

                if reconciliation_id:
                    self.reconciliation_completed.emit(reconciliation_id)
                    QMessageBox.information(self, "نجح", "تم إتمام التسوية بنجاح")
                    self.accept()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في إتمام التسوية")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في إتمام التسوية: {str(e)}")

    def save_reconciliation_to_database(self):
        """حفظ التسوية في قاعدة البيانات"""
        try:
            # هذا مثال مبسط - يمكن تطويره لاحقاً
            return 1  # معرف التسوية
        except:
            return None
