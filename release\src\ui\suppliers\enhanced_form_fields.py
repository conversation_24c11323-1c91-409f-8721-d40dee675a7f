# -*- coding: utf-8 -*-
"""
حقول النموذج المحسنة لنظام إدارة الحوالات
Enhanced Form Fields for Remittances Management System
"""

import re
from datetime import datetime, date
from PySide6.QtWidgets import (QLineEdit, QDateEdit, QComboBox, QDialog, QVBoxLayout, 
                               QHBoxLayout, QTableWidget, QTableWidgetItem, QHeaderView,
                               QAbstractItemView, QPushButton, QLabel, QMessageBox,
                               QDialogButtonBox, QFrame, QCompleter)
from PySide6.QtCore import Qt, Signal, QDate, QStringListModel
from PySide6.QtGui import QValidator, QFont, QPalette, QColor, QKeySequence

from ...database.database_manager import DatabaseManager
from ...database.models import Supplier


class FlexibleDateEdit(QDateEdit):
    """حقل تاريخ مرن يقبل إدخال التاريخ بصيغ مختلفة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDisplayFormat("yyyy-MM-dd")
        self.setCalendarPopup(True)
        
        # تفعيل الإدخال المرن
        self.lineEdit().textChanged.connect(self.parse_flexible_date)
        
    def parse_flexible_date(self, text):
        """تحليل التاريخ المدخل بصيغ مختلفة"""
        if not text or len(text) < 4:
            return
            
        try:
            # إزالة المسافات والرموز الإضافية
            clean_text = re.sub(r'[^\d/-]', '', text)
            
            # أنماط التاريخ المختلفة
            patterns = [
                r'^(\d{4})-(\d{1,2})-(\d{1,2})$',  # YYYY-MM-DD
                r'^(\d{1,2})/(\d{1,2})/(\d{4})$',  # DD/MM/YYYY
                r'^(\d{1,2})-(\d{1,2})-(\d{4})$',  # DD-MM-YYYY
                r'^(\d{4})/(\d{1,2})/(\d{1,2})$',  # YYYY/MM/DD
                r'^(\d{1,2})(\d{1,2})(\d{4})$',    # DDMMYYYY
                r'^(\d{4})(\d{1,2})(\d{1,2})$',    # YYYYMMDD
            ]
            
            for i, pattern in enumerate(patterns):
                match = re.match(pattern, clean_text)
                if match:
                    if i in [0, 3, 5]:  # YYYY-MM-DD format
                        year, month, day = match.groups()
                    else:  # DD-MM-YYYY format
                        day, month, year = match.groups()
                        
                    try:
                        parsed_date = QDate(int(year), int(month), int(day))
                        if parsed_date.isValid():
                            self.blockSignals(True)
                            self.setDate(parsed_date)
                            self.blockSignals(False)
                            return
                    except ValueError:
                        continue
                        
            # محاولة تحليل التاريخ النسبي
            self.parse_relative_date(text)
            
        except Exception:
            pass
            
    def parse_relative_date(self, text):
        """تحليل التاريخ النسبي مثل 'اليوم', 'أمس', 'غداً'"""
        text = text.strip().lower()
        today = QDate.currentDate()
        
        relative_dates = {
            'اليوم': today,
            'today': today,
            'أمس': today.addDays(-1),
            'yesterday': today.addDays(-1),
            'غداً': today.addDays(1),
            'غدا': today.addDays(1),
            'tomorrow': today.addDays(1),
        }
        
        if text in relative_dates:
            self.blockSignals(True)
            self.setDate(relative_dates[text])
            self.blockSignals(False)


class AmountValidator(QValidator):
    """مدقق المبالغ المالية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def validate(self, input_str, pos):
        """التحقق من صحة المبلغ المدخل"""
        if not input_str:
            return QValidator.Intermediate, input_str, pos
            
        # إزالة المسافات والفواصل
        clean_input = re.sub(r'[,\s]', '', input_str)
        
        # نمط المبلغ المالي (أرقام مع نقطة عشرية اختيارية)
        pattern = r'^\d{1,12}(\.\d{0,3})?$'
        
        if re.match(pattern, clean_input):
            return QValidator.Acceptable, input_str, pos
        elif re.match(r'^\d{0,12}(\.\d{0,3})?$', clean_input):
            return QValidator.Intermediate, input_str, pos
        else:
            return QValidator.Invalid, input_str, pos


class EnhancedAmountEdit(QLineEdit):
    """حقل المبلغ المحسن"""
    
    amount_changed = Signal(float)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setValidator(AmountValidator())
        self.setPlaceholderText("0.000")
        self.textChanged.connect(self.format_amount)
        self.editingFinished.connect(self.emit_amount_changed)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QLineEdit {
                border: 2px solid #28a745;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                background-color: white;
                text-align: right;
            }
            QLineEdit:focus {
                border-color: #1e7e34;
                background-color: #f0fff4;
            }
            QLineEdit:invalid {
                border-color: #dc3545;
                background-color: #fff5f5;
            }
        """)
        
    def format_amount(self, text):
        """تنسيق المبلغ أثناء الكتابة"""
        if not text:
            return
            
        # إزالة التنسيق السابق
        clean_text = re.sub(r'[,\s]', '', text)
        
        try:
            # تحويل إلى رقم وإعادة تنسيق
            if '.' in clean_text:
                parts = clean_text.split('.')
                integer_part = parts[0]
                decimal_part = parts[1][:3]  # أقصى 3 خانات عشرية
                
                # إضافة الفواصل للجزء الصحيح
                formatted_integer = self.add_commas(integer_part)
                formatted_text = f"{formatted_integer}.{decimal_part}"
            else:
                formatted_text = self.add_commas(clean_text)
                
            # تحديث النص إذا تغير
            if formatted_text != text:
                cursor_pos = self.cursorPosition()
                self.blockSignals(True)
                self.setText(formatted_text)
                self.setCursorPosition(min(cursor_pos, len(formatted_text)))
                self.blockSignals(False)
                
        except ValueError:
            pass
            
    def add_commas(self, number_str):
        """إضافة الفواصل للأرقام"""
        if not number_str:
            return ""
        return f"{int(number_str):,}" if number_str.isdigit() else number_str
        
    def emit_amount_changed(self):
        """إرسال إشارة تغيير المبلغ"""
        try:
            amount = self.get_amount()
            self.amount_changed.emit(amount)
        except ValueError:
            pass
            
    def get_amount(self):
        """الحصول على المبلغ كرقم"""
        text = self.text().replace(',', '').replace(' ', '')
        return float(text) if text else 0.0
        
    def set_amount(self, amount):
        """تعيين المبلغ"""
        self.setText(f"{amount:,.3f}")


class ExchangeRateValidator(QValidator):
    """مدقق سعر الصرف"""
    
    def validate(self, input_str, pos):
        """التحقق من صحة سعر الصرف"""
        if not input_str:
            return QValidator.Intermediate, input_str, pos
            
        # نمط سعر الصرف (أرقام مع نقطة عشرية)
        pattern = r'^\d{1,4}(\.\d{0,6})?$'
        
        if re.match(pattern, input_str):
            return QValidator.Acceptable, input_str, pos
        elif re.match(r'^\d{0,4}(\.\d{0,6})?$', input_str):
            return QValidator.Intermediate, input_str, pos
        else:
            return QValidator.Invalid, input_str, pos


class EnhancedExchangeRateEdit(QLineEdit):
    """حقل سعر الصرف المحسن"""
    
    rate_changed = Signal(float)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setValidator(ExchangeRateValidator())
        self.setPlaceholderText("1.000000")
        self.editingFinished.connect(self.emit_rate_changed)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ffc107;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                background-color: white;
                text-align: right;
            }
            QLineEdit:focus {
                border-color: #e0a800;
                background-color: #fffbf0;
            }
            QLineEdit:invalid {
                border-color: #dc3545;
                background-color: #fff5f5;
            }
        """)
        
    def emit_rate_changed(self):
        """إرسال إشارة تغيير سعر الصرف"""
        try:
            rate = self.get_rate()
            self.rate_changed.emit(rate)
        except ValueError:
            pass
            
    def get_rate(self):
        """الحصول على سعر الصرف كرقم"""
        text = self.text()
        return float(text) if text else 1.0
        
    def set_rate(self, rate):
        """تعيين سعر الصرف"""
        self.setText(f"{rate:.6f}")


class AdvancedSupplierCombo(QComboBox):
    """كومبو بوكس الموردين المتقدم مع البحث"""
    
    supplier_selected = Signal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.suppliers_data = []
        
        # تفعيل البحث التلقائي
        self.setEditable(True)
        self.setInsertPolicy(QComboBox.NoInsert)
        
        # إعداد المكمل التلقائي
        self.completer = QCompleter()
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.completer.setFilterMode(Qt.MatchContains)
        self.setCompleter(self.completer)
        
        # تحميل الموردين
        self.load_suppliers()
        
        # الاتصالات
        self.currentTextChanged.connect(self.on_text_changed)
        
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            session = self.db_manager.get_session()
            suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()
            
            self.clear()
            self.suppliers_data.clear()
            supplier_names = []
            
            self.addItem("اختر المورد...", None)
            
            for supplier in suppliers:
                display_text = f"{supplier.code} - {supplier.name}"
                self.addItem(display_text, supplier.id)
                
                # حفظ بيانات المورد
                self.suppliers_data.append({
                    'id': supplier.id,
                    'code': supplier.code,
                    'name': supplier.name,
                    'display_text': display_text
                })
                
                supplier_names.append(display_text)
                
            # تحديث المكمل التلقائي
            model = QStringListModel(supplier_names)
            self.completer.setModel(model)
            
            session.close()
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الموردين:\n{str(e)}")
            
    def on_text_changed(self, text):
        """معالج تغيير النص"""
        # البحث عن المورد المطابق
        for supplier in self.suppliers_data:
            if supplier['display_text'] == text:
                self.supplier_selected.emit(supplier)
                break
                
    def keyPressEvent(self, event):
        """معالج الضغط على المفاتيح"""
        if event.key() == Qt.Key_F9:
            self.show_advanced_search()
        else:
            super().keyPressEvent(event)
            
    def show_advanced_search(self):
        """عرض نافذة البحث المتقدم"""
        dialog = AdvancedSupplierSearchDialog(self)
        if dialog.exec() == QDialog.Accepted:
            selected_supplier = dialog.get_selected_supplier()
            if selected_supplier:
                # تعيين المورد المحدد
                display_text = f"{selected_supplier['code']} - {selected_supplier['name']}"
                index = self.findText(display_text)
                if index >= 0:
                    self.setCurrentIndex(index)
                    self.supplier_selected.emit(selected_supplier)


class AdvancedSupplierSearchDialog(QDialog):
    """نافذة البحث المتقدم للموردين"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.selected_supplier = None
        self.setup_ui()
        self.load_suppliers()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("البحث المتقدم للموردين - F9")
        self.setModal(True)
        self.resize(800, 500)
        
        layout = QVBoxLayout(self)
        
        # عنوان
        title_label = QLabel("🔍 البحث المتقدم للموردين")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ecf0f1, stop:1 #bdc3c7);
                border: 2px solid #95a5a6;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # شريط البحث
        search_frame = QFrame()
        search_layout = QHBoxLayout(search_frame)
        
        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالكود أو الاسم أو الهاتف...")
        self.search_edit.textChanged.connect(self.filter_suppliers)
        search_layout.addWidget(self.search_edit)
        
        layout.addWidget(search_frame)
        
        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(5)
        self.suppliers_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "الهاتف", "البريد الإلكتروني", "المدينة"
        ])
        
        header = self.suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.doubleClicked.connect(self.accept)
        
        layout.addWidget(self.suppliers_table)
        
        # أزرار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        # تركيز البحث
        self.search_edit.setFocus()
        
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            session = self.db_manager.get_session()
            suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()
            
            self.suppliers_table.setRowCount(len(suppliers))
            
            for row, supplier in enumerate(suppliers):
                self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier.code or ""))
                self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier.name or ""))
                self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier.phone or ""))
                self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier.email or ""))
                self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier.city or ""))
                
                # حفظ بيانات المورد
                self.suppliers_table.item(row, 0).setData(Qt.UserRole, {
                    'id': supplier.id,
                    'code': supplier.code,
                    'name': supplier.name,
                    'phone': supplier.phone,
                    'email': supplier.email,
                    'city': supplier.city
                })
                
            session.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الموردين:\n{str(e)}")
            
    def filter_suppliers(self, search_text):
        """تصفية الموردين حسب النص المدخل"""
        search_text = search_text.lower()
        
        for row in range(self.suppliers_table.rowCount()):
            show_row = False
            
            for col in range(self.suppliers_table.columnCount()):
                item = self.suppliers_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
                    
            self.suppliers_table.setRowHidden(row, not show_row)
            
    def get_selected_supplier(self):
        """الحصول على المورد المحدد"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            item = self.suppliers_table.item(current_row, 0)
            if item:
                return item.data(Qt.UserRole)
        return None
