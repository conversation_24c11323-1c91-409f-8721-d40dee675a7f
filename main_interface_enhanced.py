#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية المطورة - SHIPMENT ERP Enhanced Main Interface
واجهة رئيسية متطورة بنفس تخطيط وتصميم main_window_prototype.py
"""

import sys
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QToolBar, QStatusBar, QLabel, QPushButton, QFrame,
    QScrollArea, QSplitter, QTextEdit, QTreeWidget, QTreeWidgetItem,
    QSizePolicy, QSpacerItem, QMessageBox, QProgressBar
)
from PySide6.QtCore import Qt, QSize, QTimer, Signal
from PySide6.QtGui import (
    QPixmap, QPainter, Q<PERSON><PERSON>arGradient, Q<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>, QBrush, <PERSON><PERSON><PERSON>, QAction
)

# استيراد مكتبة القوالب
try:
    from template_library import Templates, نموذج_ادخال_اساسي
    TEMPLATES_AVAILABLE = True
except ImportError:
    TEMPLATES_AVAILABLE = False
    print("⚠️ مكتبة القوالب غير متاحة")

class EnhancedGradientWidget(QWidget):
    """ويدجت مع خلفية متدرجة محسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(800, 600)
        self.animation_offset = 0
        
        # مؤقت للرسوم المتحركة
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(50)  # تحديث كل 50ms
    
    def update_animation(self):
        """تحديث الرسوم المتحركة"""
        self.animation_offset += 1
        if self.animation_offset > 360:
            self.animation_offset = 0
        self.update()
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # إنشاء التدرج اللوني المحسن
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(240, 248, 255))  # أزرق فاتح
        gradient.setColorAt(0.2, QColor(255, 255, 255))  # أبيض
        gradient.setColorAt(0.5, QColor(248, 250, 255))  # أزرق فاتح جداً
        gradient.setColorAt(0.8, QColor(255, 255, 255))  # أبيض
        gradient.setColorAt(1, QColor(255, 248, 250))  # وردي فاتح
        
        painter.fillRect(self.rect(), gradient)
        
        # رسم الخطوط المنحنية المتحركة
        pen = QPen(QColor(100, 150, 255, 30), 3)
        painter.setPen(pen)
        
        # خطوط منحنية متحركة في الأسفل
        for i in range(25):
            x = i * 50 + (self.animation_offset % 100)
            y = self.height() - 120 + (i % 4) * 15
            painter.drawLine(x, y, x + 180, y - 40)
        
        # خطوط منحنية متحركة في الأعلى
        pen.setColor(QColor(255, 100, 150, 25))
        painter.setPen(pen)
        for i in range(20):
            x = self.width() - i * 60 - (self.animation_offset % 120)
            y = 80 + (i % 3) * 25
            painter.drawLine(x, y, x - 160, y + 35)
        
        # إضافة دوائر زخرفية متحركة
        pen.setColor(QColor(100, 200, 255, 20))
        painter.setPen(pen)
        painter.setBrush(QColor(100, 200, 255, 10))
        
        for i in range(8):
            x = 100 + i * 150 + (self.animation_offset % 50)
            y = 200 + (i % 2) * 200
            radius = 30 + (i % 3) * 10
            painter.drawEllipse(x, y, radius, radius)

class EnhancedLogoWidget(QLabel):
    """ويدجت عرض الشعار المحسن"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlignment(Qt.AlignCenter)
        self.setMinimumSize(500, 250)
        
        # إنشاء تخطيط عمودي
        layout = QVBoxLayout(self)
        
        # الشعار الرئيسي
        main_logo = QLabel("SHIPMENT ERP")
        main_logo.setAlignment(Qt.AlignCenter)
        font = QFont("Arial", 52, QFont.Bold)
        main_logo.setFont(font)
        main_logo.setStyleSheet("""
            QLabel {
                color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2E86AB, stop:0.5 #A23B72, stop:1 #F18F01);
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
        """)
        layout.addWidget(main_logo)
        
        # النسخة
        version_label = QLabel("الإصدار 2.0 المتقدم")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setFont(QFont("Arial", 14, QFont.Bold))
        version_label.setStyleSheet("""
            QLabel {
                color: #666;
                background: transparent;
                margin-top: 10px;
            }
        """)
        layout.addWidget(version_label)
        
        # شريط تقدم زخرفي
        progress = QProgressBar()
        progress.setRange(0, 100)
        progress.setValue(85)
        progress.setTextVisible(False)
        progress.setMaximumHeight(8)
        progress.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 4px;
                background-color: rgba(255,255,255,0.3);
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2E86AB, stop:1 #A23B72);
                border-radius: 4px;
            }
        """)
        layout.addWidget(progress)

class EnhancedSystemTreeWidget(QFrame):
    """شجرة أنظمة التطبيق المحسنة"""

    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumWidth(280)
        self.setMinimumWidth(250)
        self.parent_window = parent

        layout = QVBoxLayout(self)

        # عنوان القائمة المحسن
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 13, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E8F4FD, stop:1 #D1E7F0);
                padding: 12px;
                border: 2px solid #B0D4F1;
                border-radius: 8px;
                color: #2E86AB;
                font-weight: bold;
            }
        """)
        layout.addWidget(title_label)

        # إنشاء شجرة الأنظمة المحسنة
        self.tree = QTreeWidget()
        self.tree.setHeaderHidden(True)
        self.tree.setRootIsDecorated(True)
        self.tree.setIndentation(25)
        self.tree.setAnimated(True)

        # تطبيق الأنماط المحسنة على الشجرة
        self.tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                font-size: 12px;
                selection-background-color: #E8F4FD;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F5F5F5;
                margin: 1px;
            }
            QTreeWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #F0F8FF, stop:1 #E8F4FD);
                color: #2E86AB;
                border-radius: 4px;
            }
            QTreeWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #E8F4FD, stop:1 #D1E7F0);
                color: #2E86AB;
                font-weight: bold;
                border-radius: 4px;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: url(none);
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: url(none);
            }
        """)

        self.setup_enhanced_tree_items()
        layout.addWidget(self.tree)

        # ربط الأحداث
        self.tree.itemClicked.connect(self.on_item_clicked)

    def setup_enhanced_tree_items(self):
        """إعداد عناصر الشجرة المحسنة"""

        # 1. نظام إدارة الشحنات
        shipment_system = QTreeWidgetItem(self.tree, ["📦 نظام إدارة الشحنات"])
        shipment_system.setExpanded(True)

        # فروع نظام الشحنات
        new_shipment_item = QTreeWidgetItem(shipment_system, ["📋 إنشاء شحنة جديدة"])
        new_shipment_item.setData(0, Qt.UserRole, "new_shipment")

        search_item = QTreeWidgetItem(shipment_system, ["🔍 البحث عن الشحنات"])
        search_item.setData(0, Qt.UserRole, "search_shipments")

        track_item = QTreeWidgetItem(shipment_system, ["📊 تتبع الشحنات"])
        track_item.setData(0, Qt.UserRole, "track_shipments")

        update_item = QTreeWidgetItem(shipment_system, ["📝 تحديث حالة الشحنة"])
        update_item.setData(0, Qt.UserRole, "update_status")

        print_item = QTreeWidgetItem(shipment_system, ["🏷️ طباعة ملصقات الشحن"])
        print_item.setData(0, Qt.UserRole, "print_labels")

        reports_item = QTreeWidgetItem(shipment_system, ["📈 تقارير الشحنات"])
        reports_item.setData(0, Qt.UserRole, "shipment_reports")

        # 2. مكتبة القوالب المحسنة (إذا كانت متاحة)
        if TEMPLATES_AVAILABLE:
            templates_system = QTreeWidgetItem(self.tree, ["🏛️ مكتبة القوالب المتقدمة"])
            templates_system.setExpanded(True)
            
            # فروع مكتبة القوالب
            basic_template_item = QTreeWidgetItem(templates_system, ["📋 نموذج إدخال أساسي"])
            basic_template_item.setData(0, Qt.UserRole, "new_shipment")
            
            library_item = QTreeWidgetItem(templates_system, ["🏛️ مكتبة القوالب الكاملة"])
            library_item.setData(0, Qt.UserRole, "template_library")
            
            examples_item = QTreeWidgetItem(templates_system, ["📚 أمثلة القوالب"])
            examples_item.setData(0, Qt.UserRole, "template_examples")
            
            QTreeWidgetItem(templates_system, ["📝 قوالب مخصصة"])
            QTreeWidgetItem(templates_system, ["⚙️ إعدادات القوالب"])

        # 3. نظام إدارة العملاء
        customer_system = QTreeWidgetItem(self.tree, ["👥 نظام إدارة العملاء"])
        customer_system.setExpanded(False)

        # فروع نظام العملاء
        QTreeWidgetItem(customer_system, ["➕ إضافة عميل جديد"])
        QTreeWidgetItem(customer_system, ["✏️ تعديل بيانات العميل"])
        QTreeWidgetItem(customer_system, ["🔍 البحث عن العملاء"])
        QTreeWidgetItem(customer_system, ["📋 قائمة العملاء"])
        QTreeWidgetItem(customer_system, ["💳 إدارة الحسابات"])
        QTreeWidgetItem(customer_system, ["📊 تقارير العملاء"])

        # 4. نظام إدارة المخازن
        warehouse_system = QTreeWidgetItem(self.tree, ["🏪 نظام إدارة المخازن"])
        warehouse_system.setExpanded(False)

        # فروع نظام المخازن
        QTreeWidgetItem(warehouse_system, ["📦 إدارة المخزون"])
        QTreeWidgetItem(warehouse_system, ["📥 استلام البضائع"])
        QTreeWidgetItem(warehouse_system, ["📤 شحن البضائع"])
        QTreeWidgetItem(warehouse_system, ["📊 تقارير المخزون"])
        QTreeWidgetItem(warehouse_system, ["⚠️ تنبيهات المخزون"])

        # 5. النظام المالي
        financial_system = QTreeWidgetItem(self.tree, ["💰 النظام المالي"])
        financial_system.setExpanded(False)

        # فروع النظام المالي
        QTreeWidgetItem(financial_system, ["💳 إدارة الفواتير"])
        QTreeWidgetItem(financial_system, ["💵 المدفوعات"])
        QTreeWidgetItem(financial_system, ["📊 التقارير المالية"])
        QTreeWidgetItem(financial_system, ["💹 الأرباح والخسائر"])

        # 6. نظام الإدارة
        admin_system = QTreeWidgetItem(self.tree, ["⚙️ نظام الإدارة"])
        admin_system.setExpanded(False)

        # فروع نظام الإدارة
        QTreeWidgetItem(admin_system, ["👤 إدارة المستخدمين"])
        QTreeWidgetItem(admin_system, ["🔐 الصلاحيات والأذونات"])
        QTreeWidgetItem(admin_system, ["🗄️ النسخ الاحتياطية"])
        QTreeWidgetItem(admin_system, ["🔧 إعدادات النظام"])
        QTreeWidgetItem(admin_system, ["📝 سجل العمليات"])
        QTreeWidgetItem(admin_system, ["🔄 تحديثات النظام"])

    def on_item_clicked(self, item, column):
        """معالج النقر على عنصر في الشجرة"""
        item_text = item.text(0)
        item_data = item.data(0, Qt.UserRole)
        
        print(f"تم النقر على: {item_text}")
        
        # معالجة النقر حسب نوع العنصر
        if item_data == "new_shipment":
            self.open_new_shipment_form()
        elif item_data == "search_shipments":
            self.show_search_dialog()
        elif item_data == "track_shipments":
            self.show_tracking_dialog()
        elif item_data == "template_library":
            self.open_template_library()
        elif item_data == "template_examples":
            self.open_template_examples()
        else:
            # عرض رسالة للعناصر الأخرى
            QMessageBox.information(
                self, "معلومات",
                f"تم النقر على: {item_text}\n\n"
                "هذه الميزة قيد التطوير وستكون متاحة قريباً.\n\n"
                "الميزات المتاحة حالياً:\n"
                "• إنشاء شحنة جديدة\n"
                "• مكتبة القوالب\n"
                "• أمثلة القوالب"
            )

    def open_new_shipment_form(self):
        """فتح نموذج شحنة جديد"""
        if TEMPLATES_AVAILABLE and hasattr(self, 'parent_window') and self.parent_window:
            self.parent_window.open_new_shipment_form()
        elif TEMPLATES_AVAILABLE:
            try:
                form = نموذج_ادخال_اساسي(fullscreen=False)
                if form:
                    form.show()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في فتح النموذج:\n{str(e)}")
        else:
            QMessageBox.information(
                self, "معلومات",
                "مكتبة القوالب غير متاحة.\n"
                "تأكد من وجود ملف template_library.py"
            )

    def open_template_library(self):
        """فتح مكتبة القوالب"""
        if TEMPLATES_AVAILABLE and hasattr(self, 'parent_window') and self.parent_window:
            self.parent_window.open_template_library()
        elif TEMPLATES_AVAILABLE:
            try:
                from run_template_library import TemplateSelector
                selector = TemplateSelector()
                selector.exec()
            except ImportError:
                QMessageBox.information(
                    self, "مكتبة القوالب",
                    "🏛️ مكتبة القوالب المتاحة:\n\n"
                    "• نموذج إدخال أساسي\n"
                    "• قوالب مخصصة (قيد التطوير)\n\n"
                    "استخدم القائمة الجانبية للوصول للنماذج."
                )
        else:
            QMessageBox.warning(self, "تحذير", "مكتبة القوالب غير متاحة")

    def open_template_examples(self):
        """فتح أمثلة القوالب"""
        if TEMPLATES_AVAILABLE:
            try:
                import subprocess
                subprocess.Popen([sys.executable, "template_examples.py"])
                QMessageBox.information(
                    self, "أمثلة القوالب",
                    "تم تشغيل أمثلة القوالب في نافذة منفصلة."
                )
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تشغيل الأمثلة:\n{str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "مكتبة القوالب غير متاحة")

    def show_search_dialog(self):
        """عرض نافذة البحث"""
        QMessageBox.information(
            self, "البحث عن الشحنات",
            "🔍 نافذة البحث المتقدم\n\n"
            "الميزات المتاحة:\n"
            "• البحث برقم الشحنة\n"
            "• البحث باسم العميل\n"
            "• البحث بالتاريخ\n"
            "• البحث بالحالة\n"
            "• فلترة متقدمة\n\n"
            "🚧 قيد التطوير"
        )

    def show_tracking_dialog(self):
        """عرض نافذة التتبع"""
        QMessageBox.information(
            self, "تتبع الشحنات",
            "📊 نظام تتبع الشحنات المتقدم\n\n"
            "الميزات المتاحة:\n"
            "• تتبع في الوقت الفعلي\n"
            "• خريطة المسار التفاعلية\n"
            "• تحديثات الحالة التلقائية\n"
            "• إشعارات فورية\n"
            "• تقارير مفصلة\n\n"
            "🚧 قيد التطوير"
        )

class EnhancedSideMenuWidget(QFrame):
    """قائمة جانبية محسنة"""

    def __init__(self, title, items, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumWidth(220)
        self.setMinimumWidth(200)

        layout = QVBoxLayout(self)

        # عنوان القائمة المحسن
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E8F4FD, stop:1 #D1E7F0);
                padding: 10px;
                border: 2px solid #B0D4F1;
                border-radius: 6px;
                color: #2E86AB;
                font-weight: bold;
            }
        """)
        layout.addWidget(title_label)

        # عناصر القائمة المحسنة
        for item in items:
            btn = QPushButton(item)
            btn.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 10px 15px;
                    border: none;
                    background-color: transparent;
                    color: #333;
                    font-size: 11px;
                    border-radius: 4px;
                    margin: 2px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #F0F8FF, stop:1 #E8F4FD);
                    color: #2E86AB;
                    font-weight: bold;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #E8F4FD, stop:1 #D1E7F0);
                }
            """)
            layout.addWidget(btn)

        layout.addStretch()

class EnhancedMainWindow(QMainWindow):
    """الواجهة الرئيسية المطورة"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام إدارة الشحنات المتقدم - SHIPMENT ERP v2.0 Enhanced")
        self.setMinimumSize(1300, 850)
        self.resize(1500, 950)

        # متغيرات التطبيق
        self.active_forms = []
        self.current_user = "مدير النظام المتقدم"
        self.system_status = "متصل - عالي الأداء"

        # إعداد مكتبة القوالب
        if TEMPLATES_AVAILABLE:
            Templates.setup_app(QApplication.instance())
            self.setup_template_connections()

        self.setup_ui()
        self.setup_enhanced_style()
        self.setup_timer()

        # رسالة ترحيب محسنة
        self.show_enhanced_welcome_message()

    def setup_template_connections(self):
        """إعداد اتصالات مكتبة القوالب"""
        if TEMPLATES_AVAILABLE:
            manager = Templates.get_manager()
            manager.template_created.connect(self.on_form_created)
            manager.template_closed.connect(self.on_form_closed)

    def setup_timer(self):
        """إعداد مؤقت تحديث الوقت"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # تحديث كل ثانية
        self.update_time()

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("🕐 %Y-%m-%d %H:%M:%S")
        if hasattr(self, 'time_label'):
            self.time_label.setText(current_time)

    def setup_ui(self):
        """إعداد واجهة المستخدم المحسنة"""

        # إنشاء القائمة الرئيسية
        self.create_enhanced_menu_bar()

        # إنشاء شريط الأدوات المحسن
        self.create_enhanced_toolbar()

        # إنشاء المحتوى المركزي المحسن
        self.create_enhanced_central_widget()

        # إنشاء شريط الحالة المحسن
        self.create_enhanced_status_bar()

    def create_enhanced_menu_bar(self):
        """إنشاء شريط القوائم المحسن"""
        menubar = self.menuBar()

        # قائمة ملف محسنة
        file_menu = menubar.addMenu("📁 ملف")

        new_action = QAction("📋 جديد", self)
        new_action.setShortcut("Ctrl+N")
        new_action.setStatusTip("إنشاء نموذج شحنة جديد")
        new_action.triggered.connect(self.open_new_shipment_form)
        file_menu.addAction(new_action)

        open_action = QAction("📂 فتح", self)
        open_action.setShortcut("Ctrl+O")
        open_action.setStatusTip("فتح ملف موجود")
        file_menu.addAction(open_action)

        save_action = QAction("💾 حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.setStatusTip("حفظ الملف الحالي")
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        exit_action = QAction("🚪 خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.setStatusTip("إغلاق التطبيق")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة النماذج محسنة
        forms_menu = menubar.addMenu("📋 النماذج")

        basic_form_action = QAction("📝 نموذج إدخال أساسي", self)
        basic_form_action.setStatusTip("فتح نموذج إدخال البيانات الأساسي")
        basic_form_action.triggered.connect(self.open_new_shipment_form)
        forms_menu.addAction(basic_form_action)

        if TEMPLATES_AVAILABLE:
            template_library_action = QAction("🏛️ مكتبة القوالب", self)
            template_library_action.setStatusTip("فتح مكتبة القوالب الكاملة")
            template_library_action.triggered.connect(self.open_template_library)
            forms_menu.addAction(template_library_action)

            examples_action = QAction("📚 أمثلة القوالب", self)
            examples_action.setStatusTip("عرض أمثلة استخدام القوالب")
            examples_action.triggered.connect(self.open_template_examples)
            forms_menu.addAction(examples_action)

        # قائمة عرض محسنة
        view_menu = menubar.addMenu("👁️ عرض")
        view_menu.addAction("🔍 تكبير")
        view_menu.addAction("🔎 تصغير")
        view_menu.addSeparator()
        view_menu.addAction("🖥️ ملء الشاشة")
        view_menu.addAction("🪟 نافذة عادية")

        # قائمة أدوات محسنة
        tools_menu = menubar.addMenu("🔧 أدوات")
        tools_menu.addAction("⚙️ إعدادات")
        tools_menu.addAction("🎨 تخصيص الواجهة")
        tools_menu.addAction("🔌 الإضافات")

        # قائمة مساعدة محسنة
        help_menu = menubar.addMenu("❓ مساعدة")

        about_action = QAction("ℹ️ حول البرنامج", self)
        about_action.setStatusTip("معلومات حول البرنامج")
        about_action.triggered.connect(self.show_enhanced_about)
        help_menu.addAction(about_action)

        help_menu.addAction("📖 دليل المستخدم")
        help_menu.addAction("🆘 الدعم الفني")
        help_menu.addAction("🌐 الموقع الإلكتروني")

    def create_enhanced_toolbar(self):
        """إنشاء شريط الأدوات المحسن"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        toolbar.setMovable(False)

        # الأدوات المحسنة
        actions = [
            ("📋 نموذج جديد", "إنشاء نموذج شحنة جديد", self.open_new_shipment_form),
            ("🏛️ مكتبة القوالب", "فتح مكتبة القوالب", self.open_template_library),
            ("🔍 بحث", "البحث في الشحنات", self.show_search_dialog),
            ("📊 تتبع", "تتبع الشحنات", self.show_tracking_dialog),
            ("💾 حفظ", "حفظ البيانات", None),
            ("🖨️ طباعة", "طباعة التقارير", None),
            ("⚙️ إعدادات", "إعدادات النظام", self.show_settings),
            ("❓ مساعدة", "المساعدة والدعم", self.show_help)
        ]

        for text, tooltip, callback in actions:
            action = toolbar.addAction(text)
            action.setToolTip(tooltip)
            action.setStatusTip(tooltip)
            if callback:
                action.triggered.connect(callback)
            if text in ["💾 حفظ", "⚙️ إعدادات"]:
                toolbar.addSeparator()

    def create_enhanced_central_widget(self):
        """إنشاء المحتوى المركزي المحسن"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(8)

        # إنشاء المقسم الرئيسي المحسن
        splitter = QSplitter(Qt.Horizontal)
        splitter.setChildrenCollapsible(False)
        main_layout.addWidget(splitter)

        # القائمة الجانبية اليسرى - شجرة الأنظمة المحسنة
        left_menu = EnhancedSystemTreeWidget("🏢 أنظمة التطبيق المتقدمة", self)
        splitter.addWidget(left_menu)

        # المنطقة المركزية المحسنة
        center_widget = EnhancedGradientWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setContentsMargins(20, 20, 20, 20)

        # إضافة مساحة فارغة في الأعلى
        center_layout.addStretch(1)

        # إضافة الشعار المحسن
        logo = EnhancedLogoWidget()
        center_layout.addWidget(logo)

        # إضافة النص التوضيحي المحسن
        subtitle = QLabel("نظام إدارة الشحنات المتقدم والمطور")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setFont(QFont("Arial", 16, QFont.Bold))
        subtitle.setStyleSheet("""
            QLabel {
                color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #666, stop:1 #999);
                background: transparent;
                margin-top: 15px;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            }
        """)
        center_layout.addWidget(subtitle)

        # إضافة معلومات إضافية
        info_label = QLabel("✨ واجهة محسنة مع تأثيرات بصرية متقدمة ✨")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setFont(QFont("Arial", 12))
        info_label.setStyleSheet("""
            QLabel {
                color: #888;
                background: transparent;
                margin-top: 10px;
                font-style: italic;
            }
        """)
        center_layout.addWidget(info_label)

        # إضافة مساحة فارغة في الأسفل
        center_layout.addStretch(1)

        splitter.addWidget(center_widget)

        # القائمة الجانبية اليمنى المحسنة
        right_menu_items = [
            "👤 إدارة المستخدمين",
            "🔐 صلاحيات النظام",
            "📝 سجل العمليات",
            "🗄️ النسخ الاحتياطية",
            "🌐 إعدادات الشبكة",
            "🔄 تحديثات النظام",
            "🆘 الدعم الفني",
            "📞 اتصل بنا",
            "ℹ️ حول النظام"
        ]
        right_menu = EnhancedSideMenuWidget("⚙️ إعدادات النظام", right_menu_items)
        splitter.addWidget(right_menu)

        # تحديد أحجام المقاسم المحسنة
        splitter.setSizes([280, 800, 220])

    def create_enhanced_status_bar(self):
        """إنشاء شريط الحالة المحسن"""
        status_bar = self.statusBar()

        # معلومات المستخدم المحسنة
        self.user_label = QLabel(f"👤 المستخدم: {self.current_user}")
        self.user_label.setStyleSheet("color: #2E86AB; font-weight: bold;")
        status_bar.addWidget(self.user_label)

        # فاصل
        status_bar.addWidget(QLabel(" | "))

        # تاريخ ووقت محسن
        self.time_label = QLabel()
        self.time_label.setStyleSheet("color: #666; font-weight: bold;")
        status_bar.addWidget(self.time_label)

        # فاصل
        status_bar.addWidget(QLabel(" | "))

        # حالة الاتصال المحسنة
        self.connection_label = QLabel(f"🌐 {self.system_status}")
        self.connection_label.setStyleSheet("color: green; font-weight: bold;")
        status_bar.addWidget(self.connection_label)

        # فاصل
        status_bar.addWidget(QLabel(" | "))

        # عدد النماذج النشطة
        self.forms_count_label = QLabel("📋 النماذج النشطة: 0")
        self.forms_count_label.setStyleSheet("color: #A23B72; font-weight: bold;")
        status_bar.addWidget(self.forms_count_label)

        # شريط تقدم في شريط الحالة
        self.status_progress = QProgressBar()
        self.status_progress.setMaximumWidth(100)
        self.status_progress.setMaximumHeight(16)
        self.status_progress.setRange(0, 100)
        self.status_progress.setValue(100)
        self.status_progress.setTextVisible(False)
        self.status_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: #f0f0f0;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 2px;
            }
        """)
        status_bar.addPermanentWidget(self.status_progress)

    def setup_enhanced_style(self):
        """إعداد الأنماط المحسنة"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F8F9FA, stop:1 #E9ECEF);
            }
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E8F4FD, stop:1 #D1E7F0);
                border-bottom: 2px solid #B0D4F1;
                padding: 6px;
                font-weight: bold;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 15px;
                border-radius: 6px;
                margin: 2px;
            }
            QMenuBar::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #D1E7DD, stop:1 #C3E6CB);
                color: #155724;
            }
            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F8F9FA, stop:1 #E9ECEF);
                border: 2px solid #DEE2E6;
                spacing: 5px;
                padding: 8px;
                font-weight: bold;
            }
            QToolBar QToolButton {
                padding: 8px 12px;
                border-radius: 6px;
                margin: 2px;
            }
            QToolBar QToolButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                color: #1976D2;
            }
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E9ECEF, stop:1 #DEE2E6);
                border-top: 2px solid #CED4DA;
                padding: 6px;
                font-weight: bold;
            }
        """)

    # الوظائف الأساسية
    def open_new_shipment_form(self):
        """فتح نموذج شحنة جديد"""
        if TEMPLATES_AVAILABLE:
            try:
                form = نموذج_ادخال_اساسي(fullscreen=False)
                if form:
                    form.show()
                    self.statusBar().showMessage("✅ تم فتح نموذج إدخال أساسي جديد", 3000)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في فتح النموذج:\n{str(e)}")
        else:
            QMessageBox.information(
                self, "معلومات",
                "⚠️ مكتبة القوالب غير متاحة.\n"
                "تأكد من وجود ملف template_library.py"
            )

    def open_template_library(self):
        """فتح مكتبة القوالب"""
        if TEMPLATES_AVAILABLE:
            try:
                from run_template_library import TemplateSelector
                selector = TemplateSelector(self)
                selector.exec()
            except ImportError:
                QMessageBox.information(
                    self, "مكتبة القوالب المتقدمة",
                    "🏛️ مكتبة القوالب المتاحة:\n\n"
                    "📋 نموذج إدخال أساسي\n"
                    "📚 أمثلة القوالب\n"
                    "📝 قوالب مخصصة (قيد التطوير)\n"
                    "⚙️ إعدادات القوالب\n\n"
                    "استخدم القائمة الجانبية للوصول للنماذج."
                )
        else:
            QMessageBox.warning(self, "تحذير", "مكتبة القوالب غير متاحة")

    def open_template_examples(self):
        """فتح أمثلة القوالب"""
        if TEMPLATES_AVAILABLE:
            try:
                import subprocess
                subprocess.Popen([sys.executable, "template_examples.py"])
                QMessageBox.information(
                    self, "أمثلة القوالب",
                    "✅ تم تشغيل أمثلة القوالب في نافذة منفصلة.\n\n"
                    "ستجد أمثلة متنوعة لاستخدام القوالب."
                )
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تشغيل الأمثلة:\n{str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "مكتبة القوالب غير متاحة")

    def show_search_dialog(self):
        """عرض نافذة البحث المحسنة"""
        QMessageBox.information(
            self, "البحث المتقدم عن الشحنات",
            "🔍 نافذة البحث المتقدم والذكي\n\n"
            "الميزات المتاحة:\n"
            "🔢 البحث برقم الشحنة\n"
            "👤 البحث باسم العميل\n"
            "📅 البحث بالتاريخ والفترة\n"
            "📊 البحث بالحالة والنوع\n"
            "🌍 البحث بالموقع والوجهة\n"
            "💰 البحث بالقيمة والتكلفة\n"
            "🔍 فلترة متقدمة وذكية\n"
            "📈 تحليلات وإحصائيات\n\n"
            "🚧 قيد التطوير - الإصدار القادم"
        )

    def show_tracking_dialog(self):
        """عرض نافذة التتبع المحسنة"""
        QMessageBox.information(
            self, "تتبع الشحنات المتقدم",
            "📊 نظام تتبع الشحنات المتقدم والذكي\n\n"
            "الميزات المتاحة:\n"
            "🌐 تتبع في الوقت الفعلي\n"
            "🗺️ خريطة المسار التفاعلية\n"
            "🔄 تحديثات الحالة التلقائية\n"
            "📱 إشعارات فورية ومخصصة\n"
            "📊 تقارير مفصلة وتحليلية\n"
            "📈 رسوم بيانية تفاعلية\n"
            "🤖 ذكاء اصطناعي للتنبؤ\n"
            "📧 تنبيهات عبر البريد الإلكتروني\n\n"
            "🚧 قيد التطوير - الإصدار القادم"
        )

    def show_settings(self):
        """عرض نافذة الإعدادات"""
        QMessageBox.information(
            self, "إعدادات النظام المتقدمة",
            "⚙️ إعدادات النظام الشاملة\n\n"
            "الأقسام المتاحة:\n"
            "🎨 تخصيص الواجهة والألوان\n"
            "🌐 إعدادات الشبكة والاتصال\n"
            "🔐 الأمان والصلاحيات\n"
            "📊 إعدادات التقارير\n"
            "🔔 الإشعارات والتنبيهات\n"
            "💾 النسخ الاحتياطية\n"
            "🔄 التحديثات التلقائية\n"
            "📱 التكامل مع التطبيقات\n\n"
            "🚧 قيد التطوير"
        )

    def show_help(self):
        """عرض نافذة المساعدة"""
        QMessageBox.information(
            self, "المساعدة والدعم",
            "❓ مركز المساعدة والدعم\n\n"
            "الخدمات المتاحة:\n"
            "📖 دليل المستخدم التفاعلي\n"
            "🎥 فيديوهات تعليمية\n"
            "💬 الدردشة المباشرة\n"
            "📧 الدعم عبر البريد\n"
            "📞 الدعم الهاتفي\n"
            "🌐 المنتدى والمجتمع\n"
            "📚 قاعدة المعرفة\n"
            "🔧 استكشاف الأخطاء\n\n"
            "للحصول على المساعدة الفورية:\n"
            "📧 <EMAIL>\n"
            "📞 +966-11-123-4567"
        )

    def show_enhanced_about(self):
        """عرض معلومات البرنامج المحسنة"""
        QMessageBox.about(
            self, "حول البرنامج",
            "🚢 SHIPMENT ERP v2.0 Enhanced\n"
            "نظام إدارة الشحنات المتقدم والمطور\n\n"
            "✨ الميزات الجديدة:\n"
            "🎨 واجهة محسنة مع تأثيرات بصرية\n"
            "🏛️ مكتبة قوالب متكاملة ومتطورة\n"
            "📊 تحليلات وتقارير متقدمة\n"
            "🤖 ذكاء اصطناعي مدمج\n"
            "🌐 تكامل سحابي متطور\n"
            "📱 تطبيق موبايل مصاحب\n\n"
            "👨‍💻 فريق التطوير: SHIPMENT Solutions\n"
            "📅 تاريخ الإصدار: 2024\n"
            "🌐 الموقع: www.shipment-erp.com\n"
            "📧 البريد: <EMAIL>\n\n"
            "© جميع الحقوق محفوظة 2024"
        )

    def show_enhanced_welcome_message(self):
        """عرض رسالة ترحيب محسنة"""
        QMessageBox.information(
            self, "مرحباً بك في النظام المتقدم",
            "🎉 مرحباً بك في نظام إدارة الشحنات المتقدم!\n\n"
            "✨ الواجهة الجديدة تحتوي على:\n"
            "🎨 تصميم متطور مع تأثيرات بصرية\n"
            "🏛️ مكتبة قوالب شاملة ومتكاملة\n"
            "🌳 قوائم تنقل ذكية ومنظمة\n"
            "⚙️ إعدادات متقدمة وقابلة للتخصيص\n"
            "📊 تحليلات وتقارير في الوقت الفعلي\n"
            "🔔 نظام إشعارات ذكي\n"
            "🤖 مساعد ذكي مدمج\n\n"
            "🚀 للبدء:\n"
            "📋 انقر على 'إنشاء شحنة جديدة' من القائمة الجانبية\n"
            "🏛️ أو استكشف 'مكتبة القوالب' للمزيد من الخيارات\n"
            "📚 أو تصفح 'أمثلة القوالب' للتعلم\n\n"
            "💡 نصيحة: استخدم Ctrl+N لإنشاء نموذج جديد بسرعة!"
        )

    # وظائف إدارة النماذج
    def on_form_created(self, template_name, template_instance):
        """معالج إنشاء نموذج جديد"""
        self.active_forms.append(template_instance)
        self.update_forms_count()
        self.statusBar().showMessage(f"✅ تم إنشاء نموذج: {template_name}", 3000)

    def on_form_closed(self, template_name):
        """معالج إغلاق نموذج"""
        # تحديث قائمة النماذج النشطة
        self.active_forms = [f for f in self.active_forms if not f.isHidden()]
        self.update_forms_count()
        self.statusBar().showMessage(f"🔒 تم إغلاق نموذج: {template_name}", 3000)

    def update_forms_count(self):
        """تحديث عدد النماذج النشطة"""
        count = len(self.active_forms)
        if hasattr(self, 'forms_count_label'):
            self.forms_count_label.setText(f"📋 النماذج النشطة: {count}")

            # تحديث شريط التقدم حسب عدد النماذج
            if hasattr(self, 'status_progress'):
                progress_value = min(100, count * 20)  # كل نموذج = 20%
                self.status_progress.setValue(progress_value)

    def closeEvent(self, event):
        """معالج إغلاق التطبيق المحسن"""
        if self.active_forms:
            reply = QMessageBox.question(
                self, "تأكيد الإغلاق",
                f"⚠️ يوجد {len(self.active_forms)} نموذج نشط.\n\n"
                "هل تريد إغلاق التطبيق؟\n"
                "سيتم إغلاق جميع النماذج النشطة.\n\n"
                "💡 نصيحة: احفظ عملك قبل الإغلاق!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.No:
                event.ignore()
                return

        # إيقاف المؤقتات
        if hasattr(self, 'timer'):
            self.timer.stop()

        # إغلاق جميع النماذج النشطة
        for form in self.active_forms:
            form.close()

        # رسالة وداع
        self.statusBar().showMessage("👋 شكراً لاستخدام نظام إدارة الشحنات المتقدم!", 2000)

        event.accept()

def main():
    """الدالة الرئيسية المحسنة"""
    print("🚀 تشغيل نظام إدارة الشحنات المتقدم والمطور")
    print("=" * 60)
    print("✨ الواجهة الرئيسية المحسنة")
    print("   🎨 تصميم متطور مع تأثيرات بصرية")
    print("   🏛️ مكتبة قوالب متكاملة ومتطورة")
    print("   📊 تحليلات وتقارير في الوقت الفعلي")
    print("   🤖 ذكاء اصطناعي مدمج")
    print("   🌐 تكامل سحابي متطور")
    print("   📱 واجهة متجاوبة وقابلة للتخصيص")
    print("=" * 60)

    app = QApplication(sys.argv)

    # إعداد الخط والاتجاه
    app.setLayoutDirection(Qt.RightToLeft)

    # إعداد معلومات التطبيق
    app.setApplicationName("SHIPMENT ERP Enhanced")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("SHIPMENT Solutions")

    # إنشاء الواجهة الرئيسية المحسنة
    window = EnhancedMainWindow()
    window.show()

    print("✅ تم تشغيل الواجهة الرئيسية المحسنة بنجاح!")
    print("🎯 الواجهة جاهزة للاستخدام مع جميع الميزات المتقدمة")
    print("💡 استخدم Ctrl+N لإنشاء نموذج جديد")
    print("🏛️ أو استكشف مكتبة القوالب من القائمة الجانبية")

    # تشغيل التطبيق
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
