#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع وتشغيل التطبيق
Quick Fix and Run Application
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def quick_oracle_fix():
    """إصلاح سريع لمشكلة Oracle"""
    
    print("⚡ إصلاح سريع لمشكلة Oracle...")
    
    try:
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        if not db_manager.test_connection():
            print("❌ فشل في الاتصال")
            return False
        
        print("✅ الاتصال ناجح")
        
        # تنفيذ أوامر SQL مباشرة لإنشاء sequences وtriggers
        sql_commands = [
            # إنشاء sequences
            "CREATE SEQUENCE units_of_measure_seq START WITH 1 INCREMENT BY 1",
            "CREATE SEQUENCE item_groups_seq START WITH 1 INCREMENT BY 1",
            
            # إنشاء triggers
            """CREATE OR REPLACE TRIGGER units_of_measure_trigger
               BEFORE INSERT ON units_of_measure
               FOR EACH ROW
               BEGIN
                   IF :NEW.id IS NULL THEN
                       :NEW.id := units_of_measure_seq.NEXTVAL;
                   END IF;
               END;""",
            
            """CREATE OR REPLACE TRIGGER item_groups_trigger
               BEFORE INSERT ON item_groups
               FOR EACH ROW
               BEGIN
                   IF :NEW.id IS NULL THEN
                       :NEW.id := item_groups_seq.NEXTVAL;
                   END IF;
               END;""",
        ]
        
        session = db_manager.get_raw_session()
        try:
            for sql in sql_commands:
                try:
                    session.execute(sql)
                    print("✅ تم تنفيذ أمر SQL")
                except Exception as e:
                    if "already exists" in str(e) or "name is already used" in str(e):
                        print("⚠️ موجود مسبقاً")
                    else:
                        print(f"❌ خطأ: {e}")
            
            session.commit()
            print("✅ تم الإصلاح بنجاح")
            return True
            
        except Exception as e:
            session.rollback()
            print(f"❌ خطأ في الإصلاح: {e}")
            return False
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def insert_basic_data():
    """إدراج البيانات الأساسية"""
    
    print("\n📦 إدراج البيانات الأساسية...")
    
    try:
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        # إدراج البيانات باستخدام SQL مباشر
        insert_commands = [
            # وحدات القياس
            "INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at) VALUES ('قطعة', 'Piece', 'قطعة', 'pcs', 'وحدة العد الأساسية', 1, SYSDATE, SYSDATE)",
            "INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at) VALUES ('كيلوجرام', 'Kilogram', 'كجم', 'kg', 'وحدة قياس الوزن', 1, SYSDATE, SYSDATE)",
            "INSERT INTO units_of_measure (name, name_en, symbol, symbol_en, description, is_active, created_at, updated_at) VALUES ('متر', 'Meter', 'م', 'm', 'وحدة قياس الطول', 1, SYSDATE, SYSDATE)",
            
            # مجموعات الأصناف
            "INSERT INTO item_groups (name, name_en, description, is_active, created_at, updated_at) VALUES ('إلكترونيات', 'Electronics', 'الأجهزة الإلكترونية', 1, SYSDATE, SYSDATE)",
            "INSERT INTO item_groups (name, name_en, description, is_active, created_at, updated_at) VALUES ('ملابس', 'Clothing', 'الملابس والأزياء', 1, SYSDATE, SYSDATE)",
            "INSERT INTO item_groups (name, name_en, description, is_active, created_at, updated_at) VALUES ('أغذية', 'Food', 'المواد الغذائية', 1, SYSDATE, SYSDATE)",
        ]
        
        session = db_manager.get_raw_session()
        try:
            # فحص البيانات الموجودة
            units_count = session.execute("SELECT COUNT(*) FROM units_of_measure").scalar()
            groups_count = session.execute("SELECT COUNT(*) FROM item_groups").scalar()
            
            print(f"وحدات القياس الموجودة: {units_count}")
            print(f"مجموعات الأصناف الموجودة: {groups_count}")
            
            if units_count == 0 or groups_count == 0:
                for sql in insert_commands:
                    try:
                        session.execute(sql)
                        print("✅ تم إدراج بيانات")
                    except Exception as e:
                        if "unique constraint" in str(e).lower():
                            print("⚠️ البيانات موجودة مسبقاً")
                        else:
                            print(f"❌ خطأ في الإدراج: {e}")
                
                session.commit()
                
                # فحص النتائج
                final_units = session.execute("SELECT COUNT(*) FROM units_of_measure").scalar()
                final_groups = session.execute("SELECT COUNT(*) FROM item_groups").scalar()
                
                print(f"\n📊 النتائج:")
                print(f"   وحدات القياس: {final_units}")
                print(f"   مجموعات الأصناف: {final_groups}")
            else:
                print("✅ البيانات موجودة مسبقاً")
            
            return True
            
        except Exception as e:
            session.rollback()
            print(f"❌ خطأ في إدراج البيانات: {e}")
            return False
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def run_app():
    """تشغيل التطبيق"""
    
    print("\n🚀 تشغيل التطبيق...")
    
    try:
        # تشغيل التطبيق مباشرة
        os.system("python main.py")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("⚡ إصلاح سريع وتشغيل التطبيق")
    print("=" * 40)
    
    # إصلاح Oracle
    if quick_oracle_fix():
        print("✅ تم إصلاح Oracle")
    else:
        print("⚠️ مشكلة في إصلاح Oracle، لكن سنحاول المتابعة")
    
    # إدراج البيانات
    if insert_basic_data():
        print("✅ تم إدراج البيانات")
    else:
        print("⚠️ مشكلة في إدراج البيانات")
    
    # تشغيل التطبيق
    print("\n🎯 الآن سيتم تشغيل التطبيق...")
    print("💡 بعد فتح التطبيق، اذهب إلى: إدارة الأصناف → وحدات القياس")
    
    run_app()

if __name__ == "__main__":
    main()
