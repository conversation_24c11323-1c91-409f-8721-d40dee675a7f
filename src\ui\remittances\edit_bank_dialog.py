# -*- coding: utf-8 -*-
"""
نافذة تعديل البنك
Edit Bank Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QTextEdit, QPushButton,
                               QGroupBox, QCheckBox, QMessageBox, QFrame, QProgressBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

import sqlite3
from pathlib import Path
from datetime import datetime

class EditBankDialog(QDialog):
    """نافذة تعديل البنك"""
    
    bank_updated = Signal(int)  # إشارة عند تحديث البنك
    
    def __init__(self, bank_id, parent=None):
        super().__init__(parent)
        self.bank_id = bank_id
        self.setWindowTitle("تعديل البنك - ProShipment")
        self.setMinimumSize(700, 800)
        self.resize(800, 800)
        self.setModal(True)

        # توسيط النافذة
        self.center_window()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.load_bank_data()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel("تعديل بيانات البنك")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # المعلومات الأساسية
        basic_group = QGroupBox("المعلومات الأساسية")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        basic_layout = QGridLayout(basic_group)
        basic_layout.setSpacing(10)
        
        # رمز البنك
        basic_layout.addWidget(QLabel("رمز البنك: *"), 0, 0)
        self.bank_code_input = QLineEdit()
        self.bank_code_input.setPlaceholderText("مثال: BANK001")
        self.bank_code_input.setMaxLength(20)
        self.bank_code_input.setMinimumHeight(35)
        basic_layout.addWidget(self.bank_code_input, 0, 1)

        # اسم البنك
        basic_layout.addWidget(QLabel("اسم البنك: *"), 0, 2)
        self.bank_name_input = QLineEdit()
        self.bank_name_input.setPlaceholderText("أدخل اسم البنك...")
        self.bank_name_input.setMinimumHeight(35)
        basic_layout.addWidget(self.bank_name_input, 0, 3)

        # اسم البنك بالإنجليزية
        basic_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 1, 0)
        self.bank_name_en_input = QLineEdit()
        self.bank_name_en_input.setPlaceholderText("Bank Name in English...")
        self.bank_name_en_input.setMinimumHeight(35)
        basic_layout.addWidget(self.bank_name_en_input, 1, 1, 1, 3)

        # رمز SWIFT
        basic_layout.addWidget(QLabel("رمز SWIFT:"), 2, 0)
        self.swift_code_input = QLineEdit()
        self.swift_code_input.setPlaceholderText("مثال: RIBLSARI")
        self.swift_code_input.setMaxLength(11)
        self.swift_code_input.setMinimumHeight(35)
        basic_layout.addWidget(self.swift_code_input, 2, 1)

        # نوع البنك
        basic_layout.addWidget(QLabel("نوع البنك:"), 2, 2)
        self.bank_type_combo = QComboBox()
        self.bank_type_combo.addItems([
            "بنك تجاري",
            "بنك إسلامي", 
            "بنك استثماري",
            "بنك تنمية",
            "بنك مركزي",
            "أخرى"
        ])
        self.bank_type_combo.setMinimumHeight(35)
        basic_layout.addWidget(self.bank_type_combo, 2, 3)
        
        layout.addWidget(basic_group)
        
        # معلومات الاتصال والموقع
        contact_group = QGroupBox("معلومات الاتصال والموقع")
        contact_group.setStyleSheet(basic_group.styleSheet())
        contact_layout = QGridLayout(contact_group)
        contact_layout.setSpacing(10)
        
        # البلد
        contact_layout.addWidget(QLabel("البلد:"), 0, 0)
        self.country_combo = QComboBox()
        self.country_combo.addItems([
            "السعودية", "الإمارات", "الكويت", "قطر", "البحرين", 
            "عمان", "اليمن", "الأردن", "لبنان", "مصر", "أخرى"
        ])
        self.country_combo.setMinimumHeight(35)
        contact_layout.addWidget(self.country_combo, 0, 1)

        # المدينة
        contact_layout.addWidget(QLabel("المدينة:"), 0, 2)
        self.city_input = QLineEdit()
        self.city_input.setPlaceholderText("أدخل المدينة...")
        self.city_input.setMinimumHeight(35)
        contact_layout.addWidget(self.city_input, 0, 3)

        # العنوان
        contact_layout.addWidget(QLabel("العنوان:"), 1, 0)
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        self.address_input.setMinimumHeight(80)
        self.address_input.setPlaceholderText("أدخل العنوان التفصيلي...")
        contact_layout.addWidget(self.address_input, 1, 1, 1, 3)

        # الهاتف
        contact_layout.addWidget(QLabel("الهاتف:"), 2, 0)
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("+966 11 123 4567")
        self.phone_input.setMinimumHeight(35)
        contact_layout.addWidget(self.phone_input, 2, 1)

        # الفاكس
        contact_layout.addWidget(QLabel("الفاكس:"), 2, 2)
        self.fax_input = QLineEdit()
        self.fax_input.setPlaceholderText("+966 11 123 4568")
        self.fax_input.setMinimumHeight(35)
        contact_layout.addWidget(self.fax_input, 2, 3)

        # البريد الإلكتروني
        contact_layout.addWidget(QLabel("البريد الإلكتروني:"), 3, 0)
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        self.email_input.setMinimumHeight(35)
        contact_layout.addWidget(self.email_input, 3, 1)

        # الموقع الإلكتروني
        contact_layout.addWidget(QLabel("الموقع الإلكتروني:"), 3, 2)
        self.website_input = QLineEdit()
        self.website_input.setPlaceholderText("www.bank.com")
        self.website_input.setMinimumHeight(35)
        contact_layout.addWidget(self.website_input, 3, 3)
        
        layout.addWidget(contact_group)
        
        # معلومات إضافية
        extra_group = QGroupBox("معلومات إضافية")
        extra_group.setStyleSheet(basic_group.styleSheet())
        extra_layout = QGridLayout(extra_group)
        extra_layout.setSpacing(10)
        
        # الملاحظات
        extra_layout.addWidget(QLabel("ملاحظات:"), 0, 0)
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setMinimumHeight(80)
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        extra_layout.addWidget(self.notes_input, 0, 1, 1, 3)

        # حالة البنك
        self.is_active_checkbox = QCheckBox("البنك نشط")
        self.is_active_checkbox.setChecked(True)
        self.is_active_checkbox.setMinimumHeight(30)
        extra_layout.addWidget(self.is_active_checkbox, 1, 0, 1, 2)
        
        layout.addWidget(extra_group)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # أزرار الإجراءات
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.addStretch()
        
        self.save_btn = QPushButton("💾 حفظ التعديلات")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_bank)
        self.cancel_btn.clicked.connect(self.reject)
        
        # تحويل رمز البنك للأحرف الكبيرة
        self.bank_code_input.textChanged.connect(
            lambda: self.bank_code_input.setText(self.bank_code_input.text().upper())
        )
        
        # تحويل رمز SWIFT للأحرف الكبيرة
        self.swift_code_input.textChanged.connect(
            lambda: self.swift_code_input.setText(self.swift_code_input.text().upper())
        )

    def load_bank_data(self):
        """تحميل بيانات البنك للتعديل"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("""
                SELECT code, name, name_en, swift_code, country, city, address,
                       phone, fax, email, website, bank_type, notes, is_active
                FROM banks WHERE id = ?
            """, (self.bank_id,))

            bank_data = cursor.fetchone()
            conn.close()

            if bank_data:
                self.bank_code_input.setText(bank_data[0] or "")
                self.bank_name_input.setText(bank_data[1] or "")
                self.bank_name_en_input.setText(bank_data[2] or "")
                self.swift_code_input.setText(bank_data[3] or "")

                # تعيين البلد
                if bank_data[4]:
                    index = self.country_combo.findText(bank_data[4])
                    if index >= 0:
                        self.country_combo.setCurrentIndex(index)

                self.city_input.setText(bank_data[5] or "")
                self.address_input.setPlainText(bank_data[6] or "")
                self.phone_input.setText(bank_data[7] or "")
                self.fax_input.setText(bank_data[8] or "")
                self.email_input.setText(bank_data[9] or "")
                self.website_input.setText(bank_data[10] or "")

                # تعيين نوع البنك
                if bank_data[11]:
                    index = self.bank_type_combo.findText(bank_data[11])
                    if index >= 0:
                        self.bank_type_combo.setCurrentIndex(index)

                self.notes_input.setPlainText(bank_data[12] or "")
                self.is_active_checkbox.setChecked(bool(bank_data[13]))
            else:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على بيانات البنك")
                self.reject()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات البنك:\n{str(e)}")
            self.reject()

    def validate_form(self):
        """التحقق من صحة النموذج"""
        if not self.bank_code_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال رمز البنك")
            self.bank_code_input.setFocus()
            return False

        if not self.bank_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم البنك")
            self.bank_name_input.setFocus()
            return False

        return True

    def save_bank(self):
        """حفظ تعديلات البنك"""
        if not self.validate_form():
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.save_btn.setEnabled(False)

            # جمع البيانات
            bank_data = {
                'code': self.bank_code_input.text().strip(),
                'name': self.bank_name_input.text().strip(),
                'name_en': self.bank_name_en_input.text().strip() or None,
                'swift_code': self.swift_code_input.text().strip() or None,
                'country': self.country_combo.currentText(),
                'city': self.city_input.text().strip() or None,
                'address': self.address_input.toPlainText().strip() or None,
                'phone': self.phone_input.text().strip() or None,
                'fax': self.fax_input.text().strip() or None,
                'email': self.email_input.text().strip() or None,
                'website': self.website_input.text().strip() or None,
                'bank_type': self.bank_type_combo.currentText(),
                'notes': self.notes_input.toPlainText().strip() or None,
                'is_active': self.is_active_checkbox.isChecked(),
                'updated_at': datetime.now().isoformat()
            }

            # حفظ في قاعدة البيانات
            success = self.update_bank_in_database(bank_data)

            if success:
                QMessageBox.information(self, "نجح الحفظ",
                                      f"تم تحديث البنك '{bank_data['name']}' بنجاح")

                # إرسال إشارة
                self.bank_updated.emit(self.bank_id)
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في تحديث البنك")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ التعديلات:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.save_btn.setEnabled(True)

    def update_bank_in_database(self, data):
        """تحديث البنك في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحديث البنك
            cursor.execute("""
                UPDATE banks SET
                    code = ?, name = ?, name_en = ?, swift_code = ?, country = ?,
                    city = ?, address = ?, phone = ?, fax = ?, email = ?, website = ?,
                    bank_type = ?, notes = ?, is_active = ?, updated_at = ?
                WHERE id = ?
            """, (
                data['code'], data['name'], data['name_en'], data['swift_code'],
                data['country'], data['city'], data['address'], data['phone'],
                data['fax'], data['email'], data['website'], data['bank_type'],
                data['notes'], data['is_active'], data['updated_at'], self.bank_id
            ))

            conn.commit()
            conn.close()

            return True

        except sqlite3.IntegrityError as e:
            if "UNIQUE constraint failed: banks.code" in str(e):
                QMessageBox.warning(self, "خطأ", "رمز البنك موجود بالفعل. يرجى اختيار رمز آخر.")
            else:
                QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {e}")
            return False
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات: {e}")
            return False
