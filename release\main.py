#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة الشحنات المتكامل
ProShipment Management System

نظام شامل لإدارة الشحنات والموردين والأصناف
مع دعم كامل للغة العربية وتخطيط RTL
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale, Qt
from PySide6.QtGui import QFont

from src.ui.main_window import MainWindow
from src.database.universal_database_manager import UniversalDatabaseManager
from src.database.oracle_config import DatabaseConfigManager
from src.utils.arabic_support import setup_arabic_support

def main():
    """دالة تشغيل البرنامج الرئيسية"""

    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)

        # إعداد دعم اللغة العربية
        setup_arabic_support(app)

        # إعداد قاعدة البيانات
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        db_manager.initialize_database()

        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.show()

        # تشغيل التطبيق
        sys.exit(app.exec())

    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
