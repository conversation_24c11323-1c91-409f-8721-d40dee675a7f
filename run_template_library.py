#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مكتبة القوالب - SHIPMENT ERP Template Library Runner
"""

import sys
from PySide6.QtWidgets import QApplication, QMessageBox, QDialog, QVBoxLayout, QHBoxLayout
from PySide6.QtWidgets import QLabel, QPushButton, QListWidget, QTextEdit, QCheckBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# استيراد مكتبة القوالب
from template_library import TemplateLibrary, Templates

class TemplateSelector(QDialog):
    """نافذة اختيار القوالب"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("مكتبة القوالب - SHIPMENT ERP")
        self.setMinimumSize(600, 500)
        self.selected_template = None
        self.setup_ui()
        self.load_templates()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # العنوان
        title_label = QLabel("🏛️ مكتبة القوالب - SHIPMENT ERP")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A90E2, stop:1 #357ABD);
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # التخطيط الأفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # قائمة القوالب
        templates_layout = QVBoxLayout()
        templates_label = QLabel("📋 القوالب المتاحة:")
        templates_label.setFont(QFont("Arial", 12, QFont.Bold))
        templates_layout.addWidget(templates_label)
        
        self.templates_list = QListWidget()
        self.templates_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #E0E0E0;
                border-radius: 5px;
                background-color: white;
                font-size: 11px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F0F0F0;
            }
            QListWidget::item:selected {
                background-color: #E3F2FD;
                color: #1976D2;
            }
            QListWidget::item:hover {
                background-color: #F0F8FF;
            }
        """)
        self.templates_list.currentItemChanged.connect(self.on_template_selected)
        templates_layout.addWidget(self.templates_list)
        
        content_layout.addLayout(templates_layout, 1)
        
        # معلومات القالب
        info_layout = QVBoxLayout()
        info_label = QLabel("ℹ️ معلومات القالب:")
        info_label.setFont(QFont("Arial", 12, QFont.Bold))
        info_layout.addWidget(info_label)
        
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        self.info_text.setMaximumHeight(200)
        self.info_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #E0E0E0;
                border-radius: 5px;
                background-color: #F8F9FA;
                font-size: 10px;
                padding: 10px;
            }
        """)
        info_layout.addWidget(self.info_text)
        
        # خيارات العرض
        options_label = QLabel("⚙️ خيارات العرض:")
        options_label.setFont(QFont("Arial", 12, QFont.Bold))
        info_layout.addWidget(options_label)
        
        self.fullscreen_checkbox = QCheckBox("ملء الشاشة")
        self.fullscreen_checkbox.setChecked(False)
        self.fullscreen_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 11px;
                padding: 5px;
            }
        """)
        info_layout.addWidget(self.fullscreen_checkbox)
        
        content_layout.addLayout(info_layout, 1)
        layout.addLayout(content_layout)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        self.create_btn = QPushButton("🚀 إنشاء القالب")
        self.create_btn.setMinimumHeight(40)
        self.create_btn.setEnabled(False)
        self.create_btn.setStyleSheet("""
            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #357ABD;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """)
        self.create_btn.clicked.connect(self.create_template)
        buttons_layout.addWidget(self.create_btn)
        
        self.info_btn = QPushButton("📋 معلومات تفصيلية")
        self.info_btn.setMinimumHeight(40)
        self.info_btn.setEnabled(False)
        self.info_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """)
        self.info_btn.clicked.connect(self.show_detailed_info)
        buttons_layout.addWidget(self.info_btn)
        
        buttons_layout.addStretch()
        
        self.close_btn = QPushButton("❌ إغلاق")
        self.close_btn.setMinimumHeight(40)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #F44336;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #D32F2F;
            }
        """)
        self.close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_templates(self):
        """تحميل قائمة القوالب"""
        templates = Templates.get_manager().registry.get_all_templates()
        
        for name, info in templates.items():
            display_text = f"📋 {info['display_name']}"
            item_text = f"{display_text}\n   📂 {info['category']} | 🔢 v{info['version']}"
            
            self.templates_list.addItem(item_text)
            # حفظ اسم القالب في البيانات
            item = self.templates_list.item(self.templates_list.count() - 1)
            item.setData(Qt.UserRole, name)
    
    def on_template_selected(self, current, previous):
        """معالج اختيار قالب"""
        if current:
            template_name = current.data(Qt.UserRole)
            self.selected_template = template_name
            
            # تحديث معلومات القالب
            template_info = Templates.get_manager().registry.get_template(template_name)
            if template_info:
                info_text = f"""
📋 الاسم: {template_info['display_name']}
📝 الوصف: {template_info['description']}
📂 الفئة: {template_info['category']}
🔢 الإصدار: {template_info['version']}
👨‍💻 المطور: {template_info['author']}
🏷️ العلامات: {', '.join(template_info['tags'])}
📅 تاريخ التسجيل: {template_info['registered_date']}
                """
                self.info_text.setPlainText(info_text.strip())
            
            # تفعيل الأزرار
            self.create_btn.setEnabled(True)
            self.info_btn.setEnabled(True)
        else:
            self.selected_template = None
            self.info_text.clear()
            self.create_btn.setEnabled(False)
            self.info_btn.setEnabled(False)
    
    def create_template(self):
        """إنشاء القالب المحدد"""
        if self.selected_template:
            fullscreen = self.fullscreen_checkbox.isChecked()
            
            try:
                template = Templates.create(self.selected_template, fullscreen=fullscreen)
                if template:
                    QMessageBox.information(
                        self, "نجح الإنشاء",
                        f"تم إنشاء القالب بنجاح!\n\n"
                        f"القالب: {template.windowTitle()}\n"
                        f"وضع العرض: {'ملء الشاشة' if fullscreen else 'عادي'}"
                    )
                    self.accept()  # إغلاق النافذة
                else:
                    QMessageBox.warning(
                        self, "خطأ",
                        "فشل في إنشاء القالب!\nيرجى المحاولة مرة أخرى."
                    )
            except Exception as e:
                QMessageBox.critical(
                    self, "خطأ",
                    f"حدث خطأ أثناء إنشاء القالب:\n{str(e)}"
                )
    
    def show_detailed_info(self):
        """إظهار معلومات تفصيلية"""
        if self.selected_template:
            Templates.info(self.selected_template)
            
            template_info = Templates.get_manager().registry.get_template(self.selected_template)
            if template_info:
                detailed_info = f"""
📋 معلومات تفصيلية للقالب

🏷️ الاسم المعروض: {template_info['display_name']}
🔖 الاسم المرجعي: {template_info['name']}
📝 الوصف الكامل: {template_info['description']}
📂 الفئة: {template_info['category']}
🔢 رقم الإصدار: {template_info['version']}
👨‍💻 المطور: {template_info['author']}
📅 تاريخ التسجيل: {template_info['registered_date']}
🏷️ العلامات: {', '.join(template_info['tags'])}

🔧 معلومات تقنية:
• كلاس القالب: {template_info['template_class'].__name__}
• المعاملات الإضافية: {template_info['kwargs']}

💡 طرق الاستخدام:
• Templates.create("{template_info['name']}")
• Templates.{template_info['name']}()
• create_template("{template_info['name']}")
                """
                
                QMessageBox.information(
                    self, f"معلومات تفصيلية - {template_info['display_name']}",
                    detailed_info.strip()
                )

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🏛️ مكتبة القوالب - SHIPMENT ERP Template Library")
    print("=" * 70)
    print("📋 القوالب المتاحة:")
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # إعداد مكتبة القوالب
    Templates.setup_app(app)
    
    # عرض القوالب المتاحة
    templates = Templates.list_all()
    for i, template_name in enumerate(templates, 1):
        template_info = Templates.get_manager().registry.get_template(template_name)
        print(f"   {i}. {template_info['display_name']} ({template_name})")
    
    print("=" * 70)
    print("🚀 فتح نافذة اختيار القوالب...")
    print("=" * 70)
    
    # إظهار نافذة اختيار القوالب
    selector = TemplateSelector()
    
    if selector.exec() == QDialog.Accepted:
        print("✅ تم إنشاء القالب بنجاح!")
    else:
        print("❌ تم إلغاء العملية")
    
    # تشغيل التطبيق
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
