#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط لنظام CnX ERP
Simple Launcher for CnX ERP System
"""

import sys
import os

def main():
    """تشغيل النظام"""
    print("🚀 تشغيل نظام CnX ERP...")
    print("=" * 40)
    
    # التحقق من وجود الملفات
    if not os.path.exists("run_prototype.py"):
        print("❌ خطأ: ملف run_prototype.py غير موجود")
        return
    
    # تشغيل النموذج التجريبي
    try:
        print("📱 بدء تشغيل الواجهة الرئيسية...")
        os.system("python run_prototype.py")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")

if __name__ == "__main__":
    main()
