#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
قالب النموذج المطابق للتصميم المرفق
Form Template Matching the Attached Design
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QComboBox, QCheckBox, QPushButton, QTextEdit,
    QFrame, QSplitter, QGroupBox, QScrollArea, QSpacerItem, QSizePolicy,
    QToolBar, QStatusBar, QMenuBar, QTableWidget, QTableWidgetItem
)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QFont, QIcon, QPalette, QColor

class FormTemplate(QMainWindow):
    """قالب النموذج الأساسي"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("نموذج إدخال البيانات - SHIPMENT ERP")
        self.setMinimumSize(900, 600)
        self.resize(1000, 700)
        
        # متغيرات النموذج
        self.form_data = {}
        
        self.setup_ui()
        self.setup_styles()
        self.setup_connections()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # إنشاء القائمة الرئيسية
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء المحتوى المركزي
        self.create_central_widget()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        file_menu.addAction("جديد")
        file_menu.addAction("فتح")
        file_menu.addAction("حفظ")
        file_menu.addAction("حفظ باسم")
        file_menu.addSeparator()
        file_menu.addAction("طباعة")
        file_menu.addAction("معاينة الطباعة")
        file_menu.addSeparator()
        file_menu.addAction("خروج")
        
        # قائمة تحرير
        edit_menu = menubar.addMenu("تحرير")
        edit_menu.addAction("تراجع")
        edit_menu.addAction("إعادة")
        edit_menu.addSeparator()
        edit_menu.addAction("نسخ")
        edit_menu.addAction("لصق")
        edit_menu.addAction("قص")
        
        # قائمة عرض
        view_menu = menubar.addMenu("عرض")
        view_menu.addAction("شريط الأدوات")
        view_menu.addAction("شريط الحالة")
        view_menu.addAction("ملء الشاشة")
        
        # قائمة أدوات
        tools_menu = menubar.addMenu("أدوات")
        tools_menu.addAction("التحقق من البيانات")
        tools_menu.addAction("إعدادات")
        tools_menu.addAction("خيارات")
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        toolbar.setIconSize(QSize(24, 24))
        
        # أزرار شريط الأدوات
        actions = [
            ("📄", "جديد", self.new_form),
            ("📂", "فتح", self.open_form),
            ("💾", "حفظ", self.save_form),
            ("🖨️", "طباعة", self.print_form),
            ("🔍", "بحث", self.search_form),
            ("✅", "تحقق", self.validate_form),
            ("🔄", "تحديث", self.refresh_form),
            ("❓", "مساعدة", self.show_help)
        ]
        
        for icon, text, callback in actions:
            action = toolbar.addAction(f"{icon} {text}")
            action.triggered.connect(callback)
            if text in ["طباعة", "تحديث"]:
                toolbar.addSeparator()
    
    def create_central_widget(self):
        """إنشاء المحتوى المركزي"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # إنشاء المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # الجانب الأيمن - نموذج البيانات الرئيسي
        right_widget = self.create_main_form()
        splitter.addWidget(right_widget)
        
        # الجانب الأيسر - الخيارات والإعدادات
        left_widget = self.create_options_panel()
        splitter.addWidget(left_widget)
        
        # تحديد النسب
        splitter.setSizes([700, 300])
        
        # شريط المعلومات السفلي
        info_widget = self.create_info_panel()
        main_layout.addWidget(info_widget)
    
    def create_main_form(self):
        """إنشاء نموذج البيانات الرئيسي"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # عنوان النموذج
        title_label = QLabel("نموذج إدخال البيانات الأساسية")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #E3F2FD;
                color: #1976D2;
                padding: 10px;
                border-radius: 5px;
                border: 1px solid #BBDEFB;
            }
        """)
        layout.addWidget(title_label)
        
        # منطقة التمرير للنموذج
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # محتوى النموذج
        form_content = QWidget()
        form_layout = QGridLayout(form_content)
        form_layout.setSpacing(10)
        form_layout.setColumnStretch(1, 1)
        form_layout.setColumnStretch(3, 1)
        
        # الحقول الأساسية - الصف الأول
        row = 0
        
        # رقم المستند
        form_layout.addWidget(QLabel("رقم المستند:"), row, 0)
        self.document_number = QLineEdit()
        self.document_number.setPlaceholderText("أدخل رقم المستند")
        form_layout.addWidget(self.document_number, row, 1)
        
        # تاريخ المستند
        form_layout.addWidget(QLabel("تاريخ المستند:"), row, 2)
        self.document_date = QLineEdit()
        self.document_date.setPlaceholderText("YYYY-MM-DD")
        form_layout.addWidget(self.document_date, row, 3)
        
        row += 1
        
        # اسم العميل
        form_layout.addWidget(QLabel("اسم العميل:"), row, 0)
        self.customer_name = QLineEdit()
        self.customer_name.setPlaceholderText("أدخل اسم العميل")
        form_layout.addWidget(self.customer_name, row, 1)
        
        # رقم العميل
        form_layout.addWidget(QLabel("رقم العميل:"), row, 2)
        self.customer_number = QLineEdit()
        self.customer_number.setPlaceholderText("رقم العميل")
        form_layout.addWidget(self.customer_number, row, 3)
        
        row += 1
        
        # عنوان العميل
        form_layout.addWidget(QLabel("عنوان العميل:"), row, 0)
        self.customer_address = QTextEdit()
        self.customer_address.setMaximumHeight(60)
        self.customer_address.setPlaceholderText("أدخل عنوان العميل")
        form_layout.addWidget(self.customer_address, row, 1, 1, 3)
        
        row += 1
        
        # نوع الشحنة
        form_layout.addWidget(QLabel("نوع الشحنة:"), row, 0)
        self.shipment_type = QComboBox()
        self.shipment_type.addItems([
            "اختر نوع الشحنة",
            "شحنة عادية",
            "شحنة سريعة", 
            "شحنة مبردة",
            "شحنة خاصة"
        ])
        form_layout.addWidget(self.shipment_type, row, 1)
        
        # حالة الشحنة
        form_layout.addWidget(QLabel("حالة الشحنة:"), row, 2)
        self.shipment_status = QComboBox()
        self.shipment_status.addItems([
            "جديدة",
            "قيد المعالجة",
            "جاهزة للشحن",
            "تم الشحن",
            "تم التسليم"
        ])
        form_layout.addWidget(self.shipment_status, row, 3)
        
        row += 1
        
        # الوزن
        form_layout.addWidget(QLabel("الوزن (كجم):"), row, 0)
        self.weight = QLineEdit()
        self.weight.setPlaceholderText("0.00")
        form_layout.addWidget(self.weight, row, 1)
        
        # القيمة
        form_layout.addWidget(QLabel("القيمة:"), row, 2)
        self.value = QLineEdit()
        self.value.setPlaceholderText("0.00")
        form_layout.addWidget(self.value, row, 3)
        
        row += 1
        
        # مدينة المرسل
        form_layout.addWidget(QLabel("مدينة المرسل:"), row, 0)
        self.sender_city = QComboBox()
        self.sender_city.setEditable(True)
        self.sender_city.addItems([
            "الرياض", "جدة", "الدمام", "مكة المكرمة", 
            "المدينة المنورة", "الطائف", "تبوك", "أبها"
        ])
        form_layout.addWidget(self.sender_city, row, 1)
        
        # مدينة المستقبل
        form_layout.addWidget(QLabel("مدينة المستقبل:"), row, 2)
        self.receiver_city = QComboBox()
        self.receiver_city.setEditable(True)
        self.receiver_city.addItems([
            "الرياض", "جدة", "الدمام", "مكة المكرمة",
            "المدينة المنورة", "الطائف", "تبوك", "أبها"
        ])
        form_layout.addWidget(self.receiver_city, row, 3)
        
        row += 1
        
        # ملاحظات
        form_layout.addWidget(QLabel("ملاحظات:"), row, 0)
        self.notes = QTextEdit()
        self.notes.setMaximumHeight(80)
        self.notes.setPlaceholderText("أدخل أي ملاحظات إضافية")
        form_layout.addWidget(self.notes, row, 1, 1, 3)
        
        # إضافة مساحة فارغة
        form_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding), row + 1, 0)
        
        scroll_area.setWidget(form_content)
        layout.addWidget(scroll_area)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("💾 حفظ")
        self.save_btn.setMinimumHeight(35)
        self.save_btn.clicked.connect(self.save_form)
        buttons_layout.addWidget(self.save_btn)
        
        self.clear_btn = QPushButton("🗑️ مسح")
        self.clear_btn.setMinimumHeight(35)
        self.clear_btn.clicked.connect(self.clear_form)
        buttons_layout.addWidget(self.clear_btn)
        
        self.print_btn = QPushButton("🖨️ طباعة")
        self.print_btn.setMinimumHeight(35)
        self.print_btn.clicked.connect(self.print_form)
        buttons_layout.addWidget(self.print_btn)
        
        buttons_layout.addStretch()
        
        self.close_btn = QPushButton("❌ إغلاق")
        self.close_btn.setMinimumHeight(35)
        self.close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        
        return widget
    
    def create_options_panel(self):
        """إنشاء لوحة الخيارات والإعدادات"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.StyledPanel)
        widget.setMaximumWidth(280)
        
        layout = QVBoxLayout(widget)
        layout.setSpacing(10)
        
        # عنوان اللوحة
        title_label = QLabel("خيارات وإعدادات")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #FFF3E0;
                color: #F57C00;
                padding: 8px;
                border-radius: 4px;
                border: 1px solid #FFCC02;
            }
        """)
        layout.addWidget(title_label)
        
        # مجموعة خيارات الطباعة
        print_group = QGroupBox("خيارات الطباعة")
        print_layout = QVBoxLayout(print_group)
        
        self.print_header = QCheckBox("طباعة الرأسية")
        self.print_header.setChecked(True)
        print_layout.addWidget(self.print_header)
        
        self.print_footer = QCheckBox("طباعة التذييل")
        self.print_footer.setChecked(True)
        print_layout.addWidget(self.print_footer)
        
        self.print_logo = QCheckBox("طباعة الشعار")
        self.print_logo.setChecked(True)
        print_layout.addWidget(self.print_logo)
        
        layout.addWidget(print_group)
        
        # مجموعة إعدادات التحقق
        validation_group = QGroupBox("إعدادات التحقق")
        validation_layout = QVBoxLayout(validation_group)
        
        self.validate_required = QCheckBox("التحقق من الحقول المطلوبة")
        self.validate_required.setChecked(True)
        validation_layout.addWidget(self.validate_required)
        
        self.validate_format = QCheckBox("التحقق من تنسيق البيانات")
        self.validate_format.setChecked(True)
        validation_layout.addWidget(self.validate_format)
        
        self.auto_save = QCheckBox("الحفظ التلقائي")
        self.auto_save.setChecked(False)
        validation_layout.addWidget(self.auto_save)
        
        layout.addWidget(validation_group)
        
        # مجموعة إعدادات العرض
        display_group = QGroupBox("إعدادات العرض")
        display_layout = QVBoxLayout(display_group)
        
        self.show_tooltips = QCheckBox("إظهار التلميحات")
        self.show_tooltips.setChecked(True)
        display_layout.addWidget(self.show_tooltips)
        
        self.highlight_errors = QCheckBox("تمييز الأخطاء")
        self.highlight_errors.setChecked(True)
        display_layout.addWidget(self.highlight_errors)
        
        layout.addWidget(display_group)
        
        # أزرار سريعة
        quick_actions_group = QGroupBox("إجراءات سريعة")
        quick_layout = QVBoxLayout(quick_actions_group)
        
        self.template_btn = QPushButton("📋 تحميل قالب")
        self.template_btn.clicked.connect(self.load_template)
        quick_layout.addWidget(self.template_btn)
        
        self.export_btn = QPushButton("📤 تصدير البيانات")
        self.export_btn.clicked.connect(self.export_data)
        quick_layout.addWidget(self.export_btn)
        
        self.import_btn = QPushButton("📥 استيراد البيانات")
        self.import_btn.clicked.connect(self.import_data)
        quick_layout.addWidget(self.import_btn)
        
        layout.addWidget(quick_actions_group)
        
        # إضافة مساحة فارغة
        layout.addStretch()
        
        return widget

    def create_info_panel(self):
        """إنشاء لوحة المعلومات السفلية"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.StyledPanel)
        widget.setMaximumHeight(120)

        layout = QHBoxLayout(widget)
        layout.setSpacing(15)

        # جدول المعلومات
        info_table = QTableWidget(3, 4)
        info_table.setHorizontalHeaderLabels([
            "معرف السجل", "تاريخ الإنشاء", "المستخدم", "آخر تعديل"
        ])

        # إضافة بيانات تجريبية
        info_table.setItem(0, 0, QTableWidgetItem("SHP-2024-001"))
        info_table.setItem(0, 1, QTableWidgetItem("2024-01-15"))
        info_table.setItem(0, 2, QTableWidgetItem("admin"))
        info_table.setItem(0, 3, QTableWidgetItem("2024-01-15 14:30"))

        info_table.setItem(1, 0, QTableWidgetItem("SHP-2024-002"))
        info_table.setItem(1, 1, QTableWidgetItem("2024-01-16"))
        info_table.setItem(1, 2, QTableWidgetItem("user1"))
        info_table.setItem(1, 3, QTableWidgetItem("2024-01-16 09:15"))

        info_table.setItem(2, 0, QTableWidgetItem("SHP-2024-003"))
        info_table.setItem(2, 1, QTableWidgetItem("2024-01-17"))
        info_table.setItem(2, 2, QTableWidgetItem("user2"))
        info_table.setItem(2, 3, QTableWidgetItem("2024-01-17 16:45"))

        info_table.resizeColumnsToContents()
        layout.addWidget(info_table)

        return widget

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()

        # رسالة الحالة الرئيسية
        status_bar.showMessage("جاهز - يمكنك إدخال البيانات")

        # معلومات إضافية
        self.user_label = QLabel("المستخدم: admin")
        status_bar.addPermanentWidget(self.user_label)

        self.time_label = QLabel("الوقت: 14:30:25")
        status_bar.addPermanentWidget(self.time_label)

        self.record_count_label = QLabel("عدد السجلات: 0")
        status_bar.addPermanentWidget(self.record_count_label)

    def setup_styles(self):
        """إعداد الأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #F5F5F5;
            }

            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 5px;
            }

            QLabel {
                color: #333333;
                font-weight: bold;
            }

            QLineEdit {
                padding: 6px 10px;
                border: 2px solid #E0E0E0;
                border-radius: 4px;
                font-size: 11px;
                background-color: white;
            }

            QLineEdit:focus {
                border-color: #4A90E2;
                background-color: #F8F9FA;
            }

            QComboBox {
                padding: 6px 10px;
                border: 2px solid #E0E0E0;
                border-radius: 4px;
                font-size: 11px;
                background-color: white;
            }

            QComboBox:focus {
                border-color: #4A90E2;
            }

            QTextEdit {
                border: 2px solid #E0E0E0;
                border-radius: 4px;
                font-size: 11px;
                background-color: white;
                padding: 5px;
            }

            QTextEdit:focus {
                border-color: #4A90E2;
                background-color: #F8F9FA;
            }

            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }

            QPushButton:hover {
                background-color: #357ABD;
            }

            QPushButton:pressed {
                background-color: #2E6DA4;
            }

            QCheckBox {
                spacing: 8px;
                font-size: 11px;
            }

            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }

            QCheckBox::indicator:unchecked {
                border: 2px solid #CCCCCC;
                background-color: white;
                border-radius: 3px;
            }

            QCheckBox::indicator:checked {
                border: 2px solid #4A90E2;
                background-color: #4A90E2;
                border-radius: 3px;
            }

            QGroupBox {
                font-weight: bold;
                border: 2px solid #E0E0E0;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #4A90E2;
            }

            QTableWidget {
                gridline-color: #E0E0E0;
                background-color: white;
                alternate-background-color: #F8F9FA;
                selection-background-color: #E3F2FD;
            }

            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #E0E0E0;
            }

            QHeaderView::section {
                background-color: #F0F0F0;
                padding: 8px;
                border: 1px solid #E0E0E0;
                font-weight: bold;
                color: #333333;
            }

            QScrollArea {
                border: none;
                background-color: transparent;
            }

            QScrollBar:vertical {
                background-color: #F0F0F0;
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background-color: #CCCCCC;
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background-color: #999999;
            }
        """)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # ربط الأحداث
        self.document_number.textChanged.connect(self.on_data_changed)
        self.customer_name.textChanged.connect(self.on_data_changed)
        self.shipment_type.currentTextChanged.connect(self.on_data_changed)
        self.weight.textChanged.connect(self.validate_numeric_input)
        self.value.textChanged.connect(self.validate_numeric_input)

    # وظائف معالجة الأحداث
    def new_form(self):
        """إنشاء نموذج جديد"""
        self.clear_form()
        self.statusBar().showMessage("تم إنشاء نموذج جديد")

    def open_form(self):
        """فتح نموذج موجود"""
        self.statusBar().showMessage("فتح نموذج...")
        # هنا يمكن إضافة منطق فتح الملفات

    def save_form(self):
        """حفظ النموذج"""
        if self.validate_form_data():
            self.collect_form_data()
            self.statusBar().showMessage("تم حفظ النموذج بنجاح")
        else:
            self.statusBar().showMessage("خطأ في البيانات - لم يتم الحفظ")

    def clear_form(self):
        """مسح النموذج"""
        self.document_number.clear()
        self.document_date.clear()
        self.customer_name.clear()
        self.customer_number.clear()
        self.customer_address.clear()
        self.shipment_type.setCurrentIndex(0)
        self.shipment_status.setCurrentIndex(0)
        self.weight.clear()
        self.value.clear()
        self.sender_city.setCurrentIndex(0)
        self.receiver_city.setCurrentIndex(0)
        self.notes.clear()

        self.statusBar().showMessage("تم مسح النموذج")

    def print_form(self):
        """طباعة النموذج"""
        self.statusBar().showMessage("جاري الطباعة...")
        # هنا يمكن إضافة منطق الطباعة

    def search_form(self):
        """البحث في النماذج"""
        self.statusBar().showMessage("البحث...")
        # هنا يمكن إضافة منطق البحث

    def validate_form(self):
        """التحقق من صحة النموذج"""
        if self.validate_form_data():
            self.statusBar().showMessage("البيانات صحيحة ✓")
        else:
            self.statusBar().showMessage("يوجد أخطاء في البيانات ✗")

    def refresh_form(self):
        """تحديث النموذج"""
        self.statusBar().showMessage("تم تحديث النموذج")

    def show_help(self):
        """إظهار المساعدة"""
        self.statusBar().showMessage("عرض المساعدة...")

    def load_template(self):
        """تحميل قالب"""
        self.statusBar().showMessage("تحميل قالب...")

    def export_data(self):
        """تصدير البيانات"""
        self.statusBar().showMessage("تصدير البيانات...")

    def import_data(self):
        """استيراد البيانات"""
        self.statusBar().showMessage("استيراد البيانات...")

    def on_data_changed(self):
        """معالج تغيير البيانات"""
        self.statusBar().showMessage("تم تعديل البيانات")

    def validate_numeric_input(self):
        """التحقق من الإدخال الرقمي"""
        sender = self.sender()
        text = sender.text()

        # التحقق من أن النص رقمي
        try:
            if text:
                float(text)
            sender.setStyleSheet("border-color: #4A90E2;")
        except ValueError:
            sender.setStyleSheet("border-color: #F44336;")

    def validate_form_data(self):
        """التحقق من صحة بيانات النموذج"""
        errors = []

        # التحقق من الحقول المطلوبة
        if not self.document_number.text().strip():
            errors.append("رقم المستند مطلوب")

        if not self.customer_name.text().strip():
            errors.append("اسم العميل مطلوب")

        if self.shipment_type.currentIndex() == 0:
            errors.append("نوع الشحنة مطلوب")

        # التحقق من الحقول الرقمية
        try:
            if self.weight.text():
                weight_val = float(self.weight.text())
                if weight_val <= 0:
                    errors.append("الوزن يجب أن يكون أكبر من صفر")
        except ValueError:
            errors.append("الوزن يجب أن يكون رقماً")

        try:
            if self.value.text():
                value_val = float(self.value.text())
                if value_val < 0:
                    errors.append("القيمة لا يمكن أن تكون سالبة")
        except ValueError:
            errors.append("القيمة يجب أن تكون رقماً")

        if errors:
            error_message = "الأخطاء التالية:\n" + "\n".join(f"• {error}" for error in errors)
            self.statusBar().showMessage(f"يوجد {len(errors)} أخطاء")
            return False

        return True

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        self.form_data = {
            'document_number': self.document_number.text(),
            'document_date': self.document_date.text(),
            'customer_name': self.customer_name.text(),
            'customer_number': self.customer_number.text(),
            'customer_address': self.customer_address.toPlainText(),
            'shipment_type': self.shipment_type.currentText(),
            'shipment_status': self.shipment_status.currentText(),
            'weight': self.weight.text(),
            'value': self.value.text(),
            'sender_city': self.sender_city.currentText(),
            'receiver_city': self.receiver_city.currentText(),
            'notes': self.notes.toPlainText(),
            'print_options': {
                'header': self.print_header.isChecked(),
                'footer': self.print_footer.isChecked(),
                'logo': self.print_logo.isChecked()
            },
            'validation_options': {
                'required': self.validate_required.isChecked(),
                'format': self.validate_format.isChecked(),
                'auto_save': self.auto_save.isChecked()
            }
        }

        return self.form_data

    def get_form_variables(self):
        """الحصول على متغيرات النموذج للاستخدام في التطبيق"""
        return {
            # حقول البيانات الأساسية
            'document_number': self.document_number,
            'document_date': self.document_date,
            'customer_name': self.customer_name,
            'customer_number': self.customer_number,
            'customer_address': self.customer_address,
            'shipment_type': self.shipment_type,
            'shipment_status': self.shipment_status,
            'weight': self.weight,
            'value': self.value,
            'sender_city': self.sender_city,
            'receiver_city': self.receiver_city,
            'notes': self.notes,

            # خيارات الطباعة
            'print_header': self.print_header,
            'print_footer': self.print_footer,
            'print_logo': self.print_logo,

            # خيارات التحقق
            'validate_required': self.validate_required,
            'validate_format': self.validate_format,
            'auto_save': self.auto_save,
            'show_tooltips': self.show_tooltips,
            'highlight_errors': self.highlight_errors,

            # أزرار العمليات
            'save_btn': self.save_btn,
            'clear_btn': self.clear_btn,
            'print_btn': self.print_btn,
            'close_btn': self.close_btn,
            'template_btn': self.template_btn,
            'export_btn': self.export_btn,
            'import_btn': self.import_btn,

            # عناصر شريط الحالة
            'user_label': self.user_label,
            'time_label': self.time_label,
            'record_count_label': self.record_count_label
        }
