# -*- coding: utf-8 -*-
"""
محسن الثيم الفاتح المتطور
Advanced Light Theme Enhancer
"""

from PySide6.QtWidgets import QApplication, QWidget
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QPalette, QColor

class LightThemeEnhancer:
    """محسن الثيم الفاتح"""
    
    def __init__(self):
        self.app = QApplication.instance()
        
    def apply_enhanced_light_theme(self):
        """تطبيق الثيم الفاتح المحسن"""
        if not self.app:
            return False
            
        try:
            # تطبيق الثيم الأساسي
            self.load_light_theme_file()
            
            # تطبيق تحسينات إضافية
            self.apply_enhanced_styling()
            
            # تحسين الخطوط
            self.setup_enhanced_fonts()
            
            # تطبيق لوحة الألوان المحسنة
            self.setup_enhanced_palette()
            
            print("✅ تم تطبيق الثيم الفاتح المحسن بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تطبيق الثيم الفاتح المحسن: {str(e)}")
            return False
            
    def load_light_theme_file(self):
        """تحميل ملف الثيم الفاتح"""
        from pathlib import Path
        
        theme_file = Path(__file__).parent / "light_theme.qss"
        
        if theme_file.exists():
            with open(theme_file, 'r', encoding='utf-8') as file:
                style_sheet = file.read()
                self.app.setStyleSheet(style_sheet)
        else:
            raise FileNotFoundError("ملف الثيم الفاتح غير موجود")
            
    def apply_enhanced_styling(self):
        """تطبيق تحسينات إضافية للستايل"""
        enhanced_style = """
        /* تحسينات إضافية للثيم الفاتح */
        
        /* تحسين الأزرار الخاصة */
        QPushButton[objectName="primaryButton"] {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0969da, stop:1 #0550ae);
            color: white;
            border: 1px solid #0550ae;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
        }
        
        QPushButton[objectName="primaryButton"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0550ae, stop:1 #033d8b);
            transform: translateY(-1px);
        }
        
        QPushButton[objectName="successButton"] {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1a7f37, stop:1 #116329);
            color: white;
            border: 1px solid #116329;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
        }
        
        QPushButton[objectName="successButton"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #116329, stop:1 #0d4f23);
            transform: translateY(-1px);
        }
        
        QPushButton[objectName="dangerButton"] {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #cf222e, stop:1 #a40e26);
            color: white;
            border: 1px solid #a40e26;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
        }
        
        QPushButton[objectName="dangerButton"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #a40e26, stop:1 #82071e);
            transform: translateY(-1px);
        }
        
        QPushButton[objectName="warningButton"] {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #fb8500, stop:1 #e85d00);
            color: white;
            border: 1px solid #e85d00;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
        }
        
        QPushButton[objectName="warningButton"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e85d00, stop:1 #d54e00);
            transform: translateY(-1px);
        }
        
        /* تحسين البطاقات */
        QFrame[objectName="card"] {
            background: white;
            border: 1px solid #d0d7de;
            border-radius: 12px;
            padding: 16px;
        }
        
        QFrame[objectName="card"]:hover {
            border-color: #0969da;
            background: #f6f8fa;
        }
        
        /* تحسين العناوين */
        QLabel[objectName="title"] {
            font-size: 16px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 8px;
        }
        
        QLabel[objectName="subtitle"] {
            font-size: 14px;
            font-weight: 600;
            color: #656d76;
            margin-bottom: 4px;
        }
        
        QLabel[objectName="caption"] {
            font-size: 12px;
            font-weight: 400;
            color: #8c959f;
        }
        
        /* تحسين الحقول المهمة */
        QLineEdit[objectName="importantField"] {
            border: 2px solid #0969da;
            background: #f0f8ff;
            font-weight: 500;
        }
        
        QLineEdit[objectName="errorField"] {
            border: 2px solid #cf222e;
            background: #fff5f5;
        }
        
        QLineEdit[objectName="successField"] {
            border: 2px solid #1a7f37;
            background: #f0fff4;
        }
        
        /* تحسين الجداول المهمة */
        QTableWidget[objectName="dataTable"] {
            border: 1px solid #d0d7de;
            border-radius: 8px;
            background: white;
            gridline-color: #f6f8fa;
        }
        
        QTableWidget[objectName="dataTable"]::item:selected {
            background: #e6f3ff;
            color: #0969da;
            font-weight: 500;
        }
        
        /* تحسين شريط التقدم */
        QProgressBar[objectName="mainProgress"] {
            background: #f6f8fa;
            border: 1px solid #d0d7de;
            border-radius: 8px;
            height: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        QProgressBar[objectName="mainProgress"]::chunk {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #0969da, stop:0.5 #0550ae, stop:1 #033d8b);
            border-radius: 8px;
        }
        
        /* تحسين التبويبات المهمة */
        QTabWidget[objectName="mainTabs"]::pane {
            border: 1px solid #d0d7de;
            border-radius: 8px;
            background: white;
            margin-top: 4px;
        }
        
        QTabBar[objectName="mainTabs"]::tab {
            background: #f6f8fa;
            color: #1a1a1a;
            border: 1px solid #d0d7de;
            padding: 12px 20px;
            margin-right: 4px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            font-weight: 500;
        }
        
        QTabBar[objectName="mainTabs"]::tab:selected {
            background: white;
            border-bottom: 3px solid #0969da;
            color: #0969da;
            font-weight: 600;
        }
        
        /* تحسين مجموعات العناصر المهمة */
        QGroupBox[objectName="importantGroup"] {
            font-weight: 700;
            font-size: 12px;
            color: #1a1a1a;
            border: 2px solid #0969da;
            border-radius: 8px;
            margin-top: 16px;
            padding-top: 16px;
            background: #f0f8ff;
        }
        
        QGroupBox[objectName="importantGroup"]::title {
            subcontrol-origin: margin;
            left: 16px;
            padding: 0 12px 0 12px;
            color: #0969da;
            font-weight: 700;
            background: white;
            border: 1px solid #0969da;
            border-radius: 4px;
        }
        """
        
        # إضافة التحسينات للستايل الحالي
        current_style = self.app.styleSheet()
        enhanced_style_complete = current_style + enhanced_style
        self.app.setStyleSheet(enhanced_style_complete)
        
    def setup_enhanced_fonts(self):
        """إعداد الخطوط المحسنة"""
        # خط أساسي محسن
        base_font = QFont("Segoe UI", 11)
        base_font.setWeight(QFont.Normal)
        base_font.setHintingPreference(QFont.PreferFullHinting)
        base_font.setStyleStrategy(QFont.PreferAntialias | QFont.PreferQuality)
        
        self.app.setFont(base_font)
        
    def setup_enhanced_palette(self):
        """إعداد لوحة الألوان المحسنة"""
        palette = QPalette()
        
        # الألوان الأساسية
        palette.setColor(QPalette.Window, QColor("#ffffff"))
        palette.setColor(QPalette.WindowText, QColor("#1a1a1a"))
        palette.setColor(QPalette.Base, QColor("#ffffff"))
        palette.setColor(QPalette.AlternateBase, QColor("#f6f8fa"))
        palette.setColor(QPalette.Text, QColor("#1a1a1a"))
        palette.setColor(QPalette.Button, QColor("#f6f8fa"))
        palette.setColor(QPalette.ButtonText, QColor("#1a1a1a"))
        
        # ألوان التمييز
        palette.setColor(QPalette.Highlight, QColor("#e6f3ff"))
        palette.setColor(QPalette.HighlightedText, QColor("#0969da"))
        
        # ألوان الروابط
        palette.setColor(QPalette.Link, QColor("#0969da"))
        palette.setColor(QPalette.LinkVisited, QColor("#8250df"))
        
        # تطبيق اللوحة
        self.app.setPalette(palette)
        
    def apply_widget_enhancements(self, widget):
        """تطبيق تحسينات على ويدجت محدد"""
        if not widget:
            return
            
        # تطبيق خط محسن
        font = QFont("Segoe UI", 11)
        font.setWeight(QFont.Normal)
        font.setHintingPreference(QFont.PreferFullHinting)
        font.setStyleStrategy(QFont.PreferAntialias | QFont.PreferQuality)
        widget.setFont(font)
        
        # تحسين الرسم
        widget.setAttribute(Qt.WA_OpaquePaintEvent, False)
        widget.setAttribute(Qt.WA_NoSystemBackground, False)
        
    def get_enhanced_button_style(self, button_type="default"):
        """الحصول على ستايل زر محسن"""
        styles = {
            "primary": "QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0969da, stop:1 #0550ae); color: white; border: 1px solid #0550ae; font-weight: 600; padding: 12px 24px; border-radius: 8px; }",
            "success": "QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1a7f37, stop:1 #116329); color: white; border: 1px solid #116329; font-weight: 600; padding: 12px 24px; border-radius: 8px; }",
            "danger": "QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #cf222e, stop:1 #a40e26); color: white; border: 1px solid #a40e26; font-weight: 600; padding: 12px 24px; border-radius: 8px; }",
            "warning": "QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fb8500, stop:1 #e85d00); color: white; border: 1px solid #e85d00; font-weight: 600; padding: 12px 24px; border-radius: 8px; }",
            "default": "QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8f9fa); color: #1a1a1a; border: 1px solid #d0d7de; font-weight: 500; padding: 10px 20px; border-radius: 6px; }"
        }
        
        return styles.get(button_type, styles["default"])

# إنشاء مثيل عام
light_theme_enhancer = LightThemeEnhancer()

def apply_enhanced_light_theme():
    """دالة سريعة لتطبيق الثيم الفاتح المحسن"""
    return light_theme_enhancer.apply_enhanced_light_theme()

def enhance_widget(widget):
    """دالة سريعة لتحسين ويدجت"""
    light_theme_enhancer.apply_widget_enhancements(widget)

def get_button_style(button_type="default"):
    """دالة سريعة للحصول على ستايل زر"""
    return light_theme_enhancer.get_enhanced_button_style(button_type)
