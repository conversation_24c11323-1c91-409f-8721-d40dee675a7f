# -*- coding: utf-8 -*-
"""
نافذة إدارة حسابات الموردين الشاملة والمتقدمة
Comprehensive Supplier Accounts Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTabWidget, QGroupBox, QGridLayout, QLabel,
                               QPushButton, QLineEdit, QComboBox, QTableWidget,
                               QTableWidgetItem, QHeaderView, QAbstractItemView,
                               QMessageBox, QFrame, QTextEdit, QCheckBox,
                               QDoubleSpinBox, QSpinBox, QDateEdit, QTimeEdit,
                               QProgressBar, QToolBar, QStatusBar, QSplitter,
                               QTreeWidget, QTreeWidgetItem, QScrollArea,
                               QDialog, QCalendarWidget, QSlider, QDial)
from PySide6.QtCore import Qt, QTimer, Signal, QDate, QTime, QDateTime
from PySide6.QtGui import QFont, QIcon, QPixmap, QColor, QPalette, QAction, QPainter

import sqlite3
import json
from pathlib import Path
from datetime import datetime, timedelta
import csv
import xlsxwriter
from decimal import Decimal

from ...utils.arabic_support import reshape_arabic_text

class SupplierAccountsManagementWindow(QMainWindow):
    """نافذة إدارة حسابات الموردين الشاملة والمتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة حسابات الموردين - ProShipment")
        self.setMinimumSize(1600, 1000)
        self.resize(1800, 1100)
        
        # متغيرات النافذة
        self.accounts_data = []
        self.suppliers_data = []
        self.currencies_data = []
        self.transactions_data = []
        self.selected_account_id = None
        self.refresh_timer = QTimer()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        
        # تحميل البيانات
        self.load_initial_data()
        self.start_auto_refresh()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # عنوان النافذة مع الإحصائيات
        self.create_header_section(layout)
        
        # التبويبات الرئيسية
        self.tabs = QTabWidget()
        
        # تبويب نظرة عامة
        overview_tab = self.create_overview_tab()
        self.tabs.addTab(overview_tab, "📊 نظرة عامة")
        
        # تبويب الحسابات
        accounts_tab = self.create_accounts_tab()
        self.tabs.addTab(accounts_tab, "💳 الحسابات")
        
        # تبويب المعاملات
        transactions_tab = self.create_transactions_tab()
        self.tabs.addTab(transactions_tab, "💰 المعاملات")
        
        # تبويب الأرصدة والحدود
        balances_tab = self.create_balances_tab()
        self.tabs.addTab(balances_tab, "⚖️ الأرصدة والحدود")
        
        # تبويب التقارير المالية
        reports_tab = self.create_reports_tab()
        self.tabs.addTab(reports_tab, "📈 التقارير المالية")
        
        # تبويب التنبيهات والمراقبة
        alerts_tab = self.create_alerts_tab()
        self.tabs.addTab(alerts_tab, "🔔 التنبيهات والمراقبة")
        
        # تبويب الإعدادات المتقدمة
        settings_tab = self.create_settings_tab()
        self.tabs.addTab(settings_tab, "⚙️ الإعدادات المتقدمة")
        
        layout.addWidget(self.tabs)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان مع الإحصائيات"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #059669, stop:1 #10b981);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة ومعلومات العنوان
        title_section = QHBoxLayout()
        
        icon_label = QLabel("💳")
        icon_label.setStyleSheet("font-size: 40px; color: white;")
        title_section.addWidget(icon_label)
        
        title_info = QVBoxLayout()
        title_label = QLabel("إدارة حسابات الموردين")
        title_label.setFont(QFont("Arial", 22, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        
        subtitle_label = QLabel("نظام شامل لإدارة الحسابات المالية والمعاملات والأرصدة")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setStyleSheet("color: #d1fae5;")
        
        title_info.addWidget(title_label)
        title_info.addWidget(subtitle_label)
        title_section.addLayout(title_info)
        
        header_layout.addLayout(title_section)
        header_layout.addStretch()
        
        # إحصائيات سريعة
        stats_container = QFrame()
        stats_container.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.15);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        stats_layout = QGridLayout(stats_container)
        
        # إحصائيات مختلفة
        self.total_accounts_label = QLabel("إجمالي الحسابات\n0")
        self.active_accounts_label = QLabel("الحسابات النشطة\n0")
        self.total_balance_label = QLabel("إجمالي الأرصدة\n0.00 ريال")
        self.pending_transactions_label = QLabel("المعاملات المعلقة\n0")
        self.overdue_accounts_label = QLabel("الحسابات المتأخرة\n0")
        self.credit_utilization_label = QLabel("استخدام الائتمان\n0%")
        
        stats_labels = [
            self.total_accounts_label, self.active_accounts_label,
            self.total_balance_label, self.pending_transactions_label,
            self.overdue_accounts_label, self.credit_utilization_label
        ]
        
        for i, label in enumerate(stats_labels):
            label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    text-align: center;
                    font-size: 11px;
                    padding: 5px;
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            row = i // 3
            col = i % 3
            stats_layout.addWidget(label, row, col)
        
        header_layout.addWidget(stats_container)
        layout.addWidget(header_frame)
        
    def create_overview_tab(self):
        """إنشاء تبويب النظرة العامة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # الصف الأول - الرسوم البيانية
        charts_row = QHBoxLayout()
        
        # رسم بياني دائري للحسابات حسب الحالة
        accounts_chart_frame = QGroupBox("توزيع الحسابات حسب الحالة")
        accounts_chart_layout = QVBoxLayout(accounts_chart_frame)
        
        self.accounts_pie_chart = self.create_accounts_pie_chart()
        accounts_chart_layout.addWidget(self.accounts_pie_chart)
        
        charts_row.addWidget(accounts_chart_frame)
        
        # رسم بياني للأرصدة حسب العملة
        balances_chart_frame = QGroupBox("توزيع الأرصدة حسب العملة")
        balances_chart_layout = QVBoxLayout(balances_chart_frame)
        
        self.balances_bar_chart = self.create_balances_bar_chart()
        balances_chart_layout.addWidget(self.balances_bar_chart)
        
        charts_row.addWidget(balances_chart_frame)
        
        layout.addLayout(charts_row)
        
        # الصف الثاني - الجداول السريعة
        tables_row = QHBoxLayout()
        
        # جدول أكبر الحسابات
        top_accounts_frame = QGroupBox("أكبر الحسابات (حسب الرصيد)")
        top_accounts_layout = QVBoxLayout(top_accounts_frame)
        
        self.top_accounts_table = QTableWidget()
        self.setup_top_accounts_table()
        top_accounts_layout.addWidget(self.top_accounts_table)
        
        tables_row.addWidget(top_accounts_frame)
        
        # جدول آخر المعاملات
        recent_transactions_frame = QGroupBox("آخر المعاملات")
        recent_transactions_layout = QVBoxLayout(recent_transactions_frame)
        
        self.recent_transactions_table = QTableWidget()
        self.setup_recent_transactions_table()
        recent_transactions_layout.addWidget(self.recent_transactions_table)
        
        tables_row.addWidget(recent_transactions_frame)
        
        layout.addLayout(tables_row)
        
        # الصف الثالث - التنبيهات والإشعارات
        alerts_frame = QGroupBox("التنبيهات والإشعارات")
        alerts_layout = QVBoxLayout(alerts_frame)
        
        self.alerts_list = QTextEdit()
        self.alerts_list.setMaximumHeight(150)
        self.alerts_list.setReadOnly(True)
        alerts_layout.addWidget(self.alerts_list)
        
        layout.addWidget(alerts_frame)
        
        return tab
        
    def create_accounts_tab(self):
        """إنشاء تبويب الحسابات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أدوات البحث والتصفية
        search_frame = QFrame()
        search_layout = QGridLayout(search_frame)
        
        # البحث
        search_layout.addWidget(QLabel("البحث:"), 0, 0)
        self.accounts_search_input = QLineEdit()
        self.accounts_search_input.setPlaceholderText("ابحث في أرقام الحسابات، أسماء الموردين...")
        search_layout.addWidget(self.accounts_search_input, 0, 1)
        
        # تصفية حسب المورد
        search_layout.addWidget(QLabel("المورد:"), 0, 2)
        self.supplier_filter_combo = QComboBox()
        self.supplier_filter_combo.addItem("جميع الموردين")
        search_layout.addWidget(self.supplier_filter_combo, 0, 3)
        
        # تصفية حسب العملة
        search_layout.addWidget(QLabel("العملة:"), 1, 0)
        self.currency_filter_combo = QComboBox()
        self.currency_filter_combo.addItem("جميع العملات")
        search_layout.addWidget(self.currency_filter_combo, 1, 1)
        
        # تصفية حسب الحالة
        search_layout.addWidget(QLabel("الحالة:"), 1, 2)
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItems(["جميع الحالات", "نشط", "غير نشط", "مجمد", "مغلق"])
        search_layout.addWidget(self.status_filter_combo, 1, 3)
        
        layout.addWidget(search_frame)
        
        # أزرار الإجراءات
        actions_frame = QFrame()
        actions_layout = QHBoxLayout(actions_frame)
        
        new_account_btn = QPushButton("➕ حساب جديد")
        new_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #059669; }
        """)
        new_account_btn.clicked.connect(self.create_new_account)
        actions_layout.addWidget(new_account_btn)
        
        edit_account_btn = QPushButton("✏️ تعديل")
        edit_account_btn.clicked.connect(self.edit_selected_account)
        actions_layout.addWidget(edit_account_btn)
        
        freeze_account_btn = QPushButton("❄️ تجميد")
        freeze_account_btn.clicked.connect(self.freeze_selected_account)
        actions_layout.addWidget(freeze_account_btn)
        
        close_account_btn = QPushButton("🔒 إغلاق")
        close_account_btn.clicked.connect(self.close_selected_account)
        actions_layout.addWidget(close_account_btn)
        
        actions_layout.addStretch()
        
        # أزرار التصدير
        export_excel_btn = QPushButton("📊 تصدير Excel")
        export_excel_btn.clicked.connect(self.export_accounts_to_excel)
        actions_layout.addWidget(export_excel_btn)
        
        export_csv_btn = QPushButton("📄 تصدير CSV")
        export_csv_btn.clicked.connect(self.export_accounts_to_csv)
        actions_layout.addWidget(export_csv_btn)
        
        layout.addWidget(actions_frame)
        
        # جدول الحسابات
        self.accounts_table = QTableWidget()
        self.setup_accounts_table()
        layout.addWidget(self.accounts_table)
        
        return tab

    def create_transactions_tab(self):
        """إنشاء تبويب المعاملات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # أدوات التحكم في المعاملات
        controls_frame = QFrame()
        controls_layout = QGridLayout(controls_frame)

        # تصفية حسب الحساب
        controls_layout.addWidget(QLabel("الحساب:"), 0, 0)
        self.transaction_account_filter = QComboBox()
        self.transaction_account_filter.addItem("جميع الحسابات")
        controls_layout.addWidget(self.transaction_account_filter, 0, 1)

        # تصفية حسب نوع المعاملة
        controls_layout.addWidget(QLabel("نوع المعاملة:"), 0, 2)
        self.transaction_type_filter = QComboBox()
        self.transaction_type_filter.addItems(["جميع الأنواع", "إيداع", "سحب", "تحويل", "رسوم", "فوائد"])
        controls_layout.addWidget(self.transaction_type_filter, 0, 3)

        # فترة زمنية
        controls_layout.addWidget(QLabel("من تاريخ:"), 1, 0)
        self.transaction_from_date = QDateEdit()
        self.transaction_from_date.setDate(QDate.currentDate().addDays(-30))
        self.transaction_from_date.setCalendarPopup(True)
        controls_layout.addWidget(self.transaction_from_date, 1, 1)

        controls_layout.addWidget(QLabel("إلى تاريخ:"), 1, 2)
        self.transaction_to_date = QDateEdit()
        self.transaction_to_date.setDate(QDate.currentDate())
        self.transaction_to_date.setCalendarPopup(True)
        controls_layout.addWidget(self.transaction_to_date, 1, 3)

        layout.addWidget(controls_frame)

        # أزرار المعاملات
        transaction_actions_frame = QFrame()
        transaction_actions_layout = QHBoxLayout(transaction_actions_frame)

        new_transaction_btn = QPushButton("➕ معاملة جديدة")
        new_transaction_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #2563eb; }
        """)
        new_transaction_btn.clicked.connect(self.create_new_transaction)
        transaction_actions_layout.addWidget(new_transaction_btn)

        bulk_transfer_btn = QPushButton("🔄 تحويل جماعي")
        bulk_transfer_btn.clicked.connect(self.bulk_transfer)
        transaction_actions_layout.addWidget(bulk_transfer_btn)

        reconcile_btn = QPushButton("⚖️ تسوية")
        reconcile_btn.clicked.connect(self.reconcile_accounts)
        transaction_actions_layout.addWidget(reconcile_btn)

        transaction_actions_layout.addStretch()

        # إحصائيات المعاملات
        stats_frame = QFrame()
        stats_frame.setStyleSheet("background-color: #f8fafc; border-radius: 8px; padding: 10px;")
        stats_layout = QHBoxLayout(stats_frame)

        self.total_transactions_label = QLabel("إجمالي المعاملات: 0")
        self.total_amount_label = QLabel("إجمالي المبلغ: 0.00")
        self.avg_transaction_label = QLabel("متوسط المعاملة: 0.00")

        for label in [self.total_transactions_label, self.total_amount_label, self.avg_transaction_label]:
            label.setStyleSheet("font-weight: bold; color: #374151;")
            stats_layout.addWidget(label)

        stats_layout.addStretch()
        transaction_actions_layout.addWidget(stats_frame)

        layout.addWidget(transaction_actions_frame)

        # جدول المعاملات
        self.transactions_table = QTableWidget()
        self.setup_transactions_table()
        layout.addWidget(self.transactions_table)

        return tab

    def create_balances_tab(self):
        """إنشاء تبويب الأرصدة والحدود"""
        tab = QWidget()
        layout = QHBoxLayout(tab)

        # الجانب الأيسر - قائمة الحسابات
        left_panel = QFrame()
        left_layout = QVBoxLayout(left_panel)

        left_layout.addWidget(QLabel("الحسابات:"))

        self.balances_accounts_list = QTableWidget()
        self.setup_balances_accounts_list()
        left_layout.addWidget(self.balances_accounts_list)

        # الجانب الأيمن - تفاصيل الحساب المحدد
        right_panel = QScrollArea()
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # معلومات الحساب
        account_info_group = QGroupBox("معلومات الحساب")
        account_info_layout = QGridLayout(account_info_group)

        account_info_layout.addWidget(QLabel("رقم الحساب:"), 0, 0)
        self.selected_account_number_label = QLabel("-")
        account_info_layout.addWidget(self.selected_account_number_label, 0, 1)

        account_info_layout.addWidget(QLabel("المورد:"), 1, 0)
        self.selected_supplier_label = QLabel("-")
        account_info_layout.addWidget(self.selected_supplier_label, 1, 1)

        account_info_layout.addWidget(QLabel("العملة:"), 2, 0)
        self.selected_currency_label = QLabel("-")
        account_info_layout.addWidget(self.selected_currency_label, 2, 1)

        right_layout.addWidget(account_info_group)

        # الأرصدة
        balances_group = QGroupBox("الأرصدة")
        balances_layout = QGridLayout(balances_group)

        balances_layout.addWidget(QLabel("الرصيد الحالي:"), 0, 0)
        self.current_balance_label = QLabel("0.00")
        self.current_balance_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #059669;")
        balances_layout.addWidget(self.current_balance_label, 0, 1)

        balances_layout.addWidget(QLabel("الرصيد المتاح:"), 1, 0)
        self.available_balance_label = QLabel("0.00")
        balances_layout.addWidget(self.available_balance_label, 1, 1)

        balances_layout.addWidget(QLabel("الرصيد المحجوز:"), 2, 0)
        self.blocked_balance_label = QLabel("0.00")
        self.blocked_balance_label.setStyleSheet("color: #ef4444;")
        balances_layout.addWidget(self.blocked_balance_label, 2, 1)

        right_layout.addWidget(balances_group)

        # حدود الائتمان
        credit_group = QGroupBox("حدود الائتمان")
        credit_layout = QGridLayout(credit_group)

        credit_layout.addWidget(QLabel("حد الائتمان:"), 0, 0)
        self.credit_limit_label = QLabel("0.00")
        credit_layout.addWidget(self.credit_limit_label, 0, 1)

        credit_layout.addWidget(QLabel("الائتمان المستخدم:"), 1, 0)
        self.used_credit_label = QLabel("0.00")
        credit_layout.addWidget(self.used_credit_label, 1, 1)

        credit_layout.addWidget(QLabel("الائتمان المتاح:"), 2, 0)
        self.available_credit_label = QLabel("0.00")
        credit_layout.addWidget(self.available_credit_label, 2, 1)

        # شريط تقدم استخدام الائتمان
        credit_layout.addWidget(QLabel("نسبة الاستخدام:"), 3, 0)
        self.credit_usage_progress = QProgressBar()
        self.credit_usage_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #e5e7eb;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #3b82f6;
                border-radius: 3px;
            }
        """)
        credit_layout.addWidget(self.credit_usage_progress, 3, 1)

        right_layout.addWidget(credit_group)

        # أزرار الإجراءات
        actions_group = QGroupBox("إجراءات الحساب")
        actions_layout = QVBoxLayout(actions_group)

        adjust_balance_btn = QPushButton("💰 تعديل الرصيد")
        adjust_balance_btn.clicked.connect(self.adjust_balance)
        actions_layout.addWidget(adjust_balance_btn)

        adjust_limits_btn = QPushButton("📊 تعديل الحدود")
        adjust_limits_btn.clicked.connect(self.adjust_limits)
        actions_layout.addWidget(adjust_limits_btn)

        block_amount_btn = QPushButton("🔒 حجز مبلغ")
        block_amount_btn.clicked.connect(self.block_amount)
        actions_layout.addWidget(block_amount_btn)

        unblock_amount_btn = QPushButton("🔓 إلغاء حجز")
        unblock_amount_btn.clicked.connect(self.unblock_amount)
        actions_layout.addWidget(unblock_amount_btn)

        right_layout.addWidget(actions_group)
        right_layout.addStretch()

        right_panel.setWidget(right_widget)

        # تقسيم النافذة
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([500, 700])

        layout.addWidget(splitter)

        return tab

    def create_reports_tab(self):
        """إنشاء تبويب التقارير المالية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # أدوات التقارير
        reports_controls_frame = QFrame()
        reports_controls_layout = QGridLayout(reports_controls_frame)

        # نوع التقرير
        reports_controls_layout.addWidget(QLabel("نوع التقرير:"), 0, 0)
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "كشف حساب مورد",
            "تقرير الأرصدة الشامل",
            "تقرير المعاملات",
            "تقرير استخدام الائتمان",
            "تقرير الحسابات المتأخرة",
            "تقرير الربحية",
            "تقرير التدفق النقدي",
            "تقرير مقارن بالفترات"
        ])
        reports_controls_layout.addWidget(self.report_type_combo, 0, 1)

        # فترة التقرير
        reports_controls_layout.addWidget(QLabel("من تاريخ:"), 1, 0)
        self.report_from_date = QDateEdit()
        self.report_from_date.setDate(QDate.currentDate().addDays(-30))
        self.report_from_date.setCalendarPopup(True)
        reports_controls_layout.addWidget(self.report_from_date, 1, 1)

        reports_controls_layout.addWidget(QLabel("إلى تاريخ:"), 1, 2)
        self.report_to_date = QDateEdit()
        self.report_to_date.setDate(QDate.currentDate())
        self.report_to_date.setCalendarPopup(True)
        reports_controls_layout.addWidget(self.report_to_date, 1, 3)

        # تصفية حسب المورد
        reports_controls_layout.addWidget(QLabel("المورد:"), 2, 0)
        self.report_supplier_filter = QComboBox()
        self.report_supplier_filter.addItem("جميع الموردين")
        reports_controls_layout.addWidget(self.report_supplier_filter, 2, 1)

        # تصفية حسب العملة
        reports_controls_layout.addWidget(QLabel("العملة:"), 2, 2)
        self.report_currency_filter = QComboBox()
        self.report_currency_filter.addItem("جميع العملات")
        reports_controls_layout.addWidget(self.report_currency_filter, 2, 3)

        layout.addWidget(reports_controls_frame)

        # أزرار التقارير
        reports_actions_frame = QFrame()
        reports_actions_layout = QHBoxLayout(reports_actions_frame)

        generate_report_btn = QPushButton("📊 إنشاء التقرير")
        generate_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #8b5cf6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #7c3aed; }
        """)
        generate_report_btn.clicked.connect(self.generate_report)
        reports_actions_layout.addWidget(generate_report_btn)

        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.clicked.connect(self.export_report_pdf)
        reports_actions_layout.addWidget(export_pdf_btn)

        export_excel_btn = QPushButton("📊 تصدير Excel")
        export_excel_btn.clicked.connect(self.export_report_excel)
        reports_actions_layout.addWidget(export_excel_btn)

        print_report_btn = QPushButton("🖨️ طباعة")
        print_report_btn.clicked.connect(self.print_report)
        reports_actions_layout.addWidget(print_report_btn)

        reports_actions_layout.addStretch()

        # معاينة سريعة
        preview_btn = QPushButton("👁️ معاينة سريعة")
        preview_btn.clicked.connect(self.quick_preview)
        reports_actions_layout.addWidget(preview_btn)

        layout.addWidget(reports_actions_frame)

        # منطقة عرض التقرير
        self.report_display = QTextEdit()
        self.report_display.setReadOnly(True)
        self.report_display.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 1px solid #d1d5db;
                border-radius: 8px;
                padding: 15px;
                font-family: 'Courier New', monospace;
            }
        """)
        layout.addWidget(self.report_display)

        return tab

    def create_alerts_tab(self):
        """إنشاء تبويب التنبيهات والمراقبة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات التنبيهات
        alerts_settings_frame = QGroupBox("إعدادات التنبيهات")
        alerts_settings_layout = QGridLayout(alerts_settings_frame)

        # تنبيهات الأرصدة
        self.low_balance_alerts_checkbox = QCheckBox("تنبيهات الرصيد المنخفض")
        self.low_balance_alerts_checkbox.setChecked(True)
        alerts_settings_layout.addWidget(self.low_balance_alerts_checkbox, 0, 0, 1, 2)

        self.high_balance_alerts_checkbox = QCheckBox("تنبيهات الرصيد المرتفع")
        alerts_settings_layout.addWidget(self.high_balance_alerts_checkbox, 1, 0, 1, 2)

        # تنبيهات الائتمان
        self.credit_limit_alerts_checkbox = QCheckBox("تنبيهات حد الائتمان")
        self.credit_limit_alerts_checkbox.setChecked(True)
        alerts_settings_layout.addWidget(self.credit_limit_alerts_checkbox, 2, 0, 1, 2)

        # تنبيهات المعاملات المشبوهة
        self.suspicious_transactions_checkbox = QCheckBox("تنبيهات المعاملات المشبوهة")
        alerts_settings_layout.addWidget(self.suspicious_transactions_checkbox, 3, 0, 1, 2)

        layout.addWidget(alerts_settings_frame)

        # مراقبة في الوقت الفعلي
        monitoring_frame = QGroupBox("المراقبة في الوقت الفعلي")
        monitoring_layout = QVBoxLayout(monitoring_frame)

        # مؤشرات الأداء
        kpi_frame = QFrame()
        kpi_layout = QGridLayout(kpi_frame)

        self.active_transactions_label = QLabel("المعاملات النشطة: 0")
        self.pending_approvals_label = QLabel("في انتظار الموافقة: 0")
        self.failed_transactions_label = QLabel("المعاملات الفاشلة: 0")
        self.system_load_label = QLabel("حمولة النظام: 0%")

        kpi_labels = [self.active_transactions_label, self.pending_approvals_label,
                     self.failed_transactions_label, self.system_load_label]

        for i, label in enumerate(kpi_labels):
            label.setStyleSheet("font-weight: bold; padding: 10px; background-color: #f3f4f6; border-radius: 5px;")
            row = i // 2
            col = i % 2
            kpi_layout.addWidget(label, row, col)

        monitoring_layout.addWidget(kpi_frame)

        layout.addWidget(monitoring_frame)

        # قائمة التنبيهات النشطة
        active_alerts_frame = QGroupBox("التنبيهات النشطة")
        active_alerts_layout = QVBoxLayout(active_alerts_frame)

        self.alerts_table = QTableWidget()
        self.setup_alerts_table()
        active_alerts_layout.addWidget(self.alerts_table)

        # أزرار إدارة التنبيهات
        alerts_actions_frame = QFrame()
        alerts_actions_layout = QHBoxLayout(alerts_actions_frame)

        mark_read_btn = QPushButton("✅ تحديد كمقروء")
        mark_read_btn.clicked.connect(self.mark_alerts_read)
        alerts_actions_layout.addWidget(mark_read_btn)

        dismiss_btn = QPushButton("❌ تجاهل")
        dismiss_btn.clicked.connect(self.dismiss_alerts)
        alerts_actions_layout.addWidget(dismiss_btn)

        export_alerts_btn = QPushButton("📤 تصدير التنبيهات")
        export_alerts_btn.clicked.connect(self.export_alerts)
        alerts_actions_layout.addWidget(export_alerts_btn)

        alerts_actions_layout.addStretch()
        active_alerts_layout.addWidget(alerts_actions_frame)

        layout.addWidget(active_alerts_frame)

        return tab

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات المتقدمة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات النظام
        system_settings_group = QGroupBox("إعدادات النظام")
        system_settings_layout = QGridLayout(system_settings_group)

        # تحديث تلقائي
        self.auto_refresh_checkbox = QCheckBox("تحديث تلقائي للبيانات")
        self.auto_refresh_checkbox.setChecked(True)
        system_settings_layout.addWidget(self.auto_refresh_checkbox, 0, 0, 1, 2)

        system_settings_layout.addWidget(QLabel("فترة التحديث (ثواني):"), 1, 0)
        self.refresh_interval_spinbox = QSpinBox()
        self.refresh_interval_spinbox.setRange(10, 300)
        self.refresh_interval_spinbox.setValue(30)
        system_settings_layout.addWidget(self.refresh_interval_spinbox, 1, 1)

        # إعدادات العرض
        system_settings_layout.addWidget(QLabel("عدد السجلات المعروضة:"), 2, 0)
        self.records_per_page_spinbox = QSpinBox()
        self.records_per_page_spinbox.setRange(10, 1000)
        self.records_per_page_spinbox.setValue(100)
        system_settings_layout.addWidget(self.records_per_page_spinbox, 2, 1)

        layout.addWidget(system_settings_group)

        # إعدادات الأمان
        security_settings_group = QGroupBox("إعدادات الأمان")
        security_settings_layout = QGridLayout(security_settings_group)

        self.require_approval_checkbox = QCheckBox("تتطلب موافقة للمعاملات الكبيرة")
        security_settings_layout.addWidget(self.require_approval_checkbox, 0, 0, 1, 2)

        security_settings_layout.addWidget(QLabel("حد الموافقة:"), 1, 0)
        self.approval_threshold_spinbox = QDoubleSpinBox()
        self.approval_threshold_spinbox.setRange(0, 1000000)
        self.approval_threshold_spinbox.setValue(10000)
        security_settings_layout.addWidget(self.approval_threshold_spinbox, 1, 1)

        self.audit_log_checkbox = QCheckBox("تفعيل سجل المراجعة")
        self.audit_log_checkbox.setChecked(True)
        security_settings_layout.addWidget(self.audit_log_checkbox, 2, 0, 1, 2)

        layout.addWidget(security_settings_group)

        # أزرار الحفظ
        settings_actions_frame = QFrame()
        settings_actions_layout = QHBoxLayout(settings_actions_frame)

        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #059669;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #047857; }
        """)
        save_settings_btn.clicked.connect(self.save_settings)
        settings_actions_layout.addWidget(save_settings_btn)

        reset_settings_btn = QPushButton("🔄 إعادة تعيين")
        reset_settings_btn.clicked.connect(self.reset_settings)
        settings_actions_layout.addWidget(reset_settings_btn)

        backup_settings_btn = QPushButton("💾 نسخ احتياطي للإعدادات")
        backup_settings_btn.clicked.connect(self.backup_settings)
        settings_actions_layout.addWidget(backup_settings_btn)

        restore_settings_btn = QPushButton("📥 استعادة الإعدادات")
        restore_settings_btn.clicked.connect(self.restore_settings)
        settings_actions_layout.addWidget(restore_settings_btn)

        settings_actions_layout.addStretch()
        layout.addWidget(settings_actions_frame)

        layout.addStretch()
        return tab

    # دوال إعداد الجداول والرسوم البيانية
    def setup_accounts_table(self):
        """إعداد جدول الحسابات"""
        headers = ["الرقم", "رقم الحساب", "المورد", "العملة", "الرصيد الحالي",
                  "الرصيد المتاح", "حد الائتمان", "الحالة", "تاريخ الإنشاء"]
        self.accounts_table.setColumnCount(len(headers))
        self.accounts_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.accounts_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.accounts_table.setAlternatingRowColors(True)
        self.accounts_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.accounts_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_transactions_table(self):
        """إعداد جدول المعاملات"""
        headers = ["الرقم", "رقم الحساب", "نوع المعاملة", "المبلغ", "العملة",
                  "الرصيد بعد المعاملة", "التاريخ", "الوصف", "الحالة"]
        self.transactions_table.setColumnCount(len(headers))
        self.transactions_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.transactions_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.transactions_table.setAlternatingRowColors(True)
        self.transactions_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.transactions_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_balances_accounts_list(self):
        """إعداد قائمة الحسابات في تبويب الأرصدة"""
        headers = ["رقم الحساب", "المورد", "الرصيد", "العملة"]
        self.balances_accounts_list.setColumnCount(len(headers))
        self.balances_accounts_list.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.balances_accounts_list.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.balances_accounts_list.setAlternatingRowColors(True)
        self.balances_accounts_list.itemClicked.connect(self.show_account_details)

        # تعديل عرض الأعمدة
        header = self.balances_accounts_list.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_top_accounts_table(self):
        """إعداد جدول أكبر الحسابات"""
        headers = ["المورد", "رقم الحساب", "الرصيد", "العملة"]
        self.top_accounts_table.setColumnCount(len(headers))
        self.top_accounts_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.top_accounts_table.setMaximumHeight(200)
        self.top_accounts_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.top_accounts_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_recent_transactions_table(self):
        """إعداد جدول آخر المعاملات"""
        headers = ["الحساب", "النوع", "المبلغ", "التاريخ"]
        self.recent_transactions_table.setColumnCount(len(headers))
        self.recent_transactions_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.recent_transactions_table.setMaximumHeight(200)
        self.recent_transactions_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.recent_transactions_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_alerts_table(self):
        """إعداد جدول التنبيهات"""
        headers = ["النوع", "الرسالة", "الحساب", "التاريخ", "الحالة"]
        self.alerts_table.setColumnCount(len(headers))
        self.alerts_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.alerts_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.alerts_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.alerts_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def create_accounts_pie_chart(self):
        """إنشاء رسم بياني دائري للحسابات"""
        try:
            from PySide6.QtCharts import QChart, QChartView, QPieSeries

            series = QPieSeries()
            series.append("نشط", 75)
            series.append("غير نشط", 15)
            series.append("مجمد", 8)
            series.append("مغلق", 2)

            chart = QChart()
            chart.addSeries(series)
            chart.setTitle("توزيع الحسابات حسب الحالة")
            chart.legend().setAlignment(Qt.AlignBottom)

            chart_view = QChartView(chart)
            chart_view.setRenderHint(QPainter.Antialiasing)
            chart_view.setMaximumHeight(300)

            return chart_view

        except ImportError:
            # في حالة عدم توفر QtCharts
            placeholder = QLabel("الرسم البياني غير متاح\n(QtCharts غير مثبت)")
            placeholder.setAlignment(Qt.AlignCenter)
            placeholder.setStyleSheet("color: #6b7280; font-style: italic;")
            placeholder.setMaximumHeight(300)
            return placeholder

    def create_balances_bar_chart(self):
        """إنشاء رسم بياني للأرصدة"""
        try:
            from PySide6.QtCharts import QChart, QChartView, QBarSeries, QBarSet

            set0 = QBarSet("الأرصدة")
            set0.append([1000000, 750000, 500000, 250000])

            series = QBarSeries()
            series.append(set0)

            chart = QChart()
            chart.addSeries(series)
            chart.setTitle("توزيع الأرصدة حسب العملة")
            chart.legend().setAlignment(Qt.AlignBottom)

            chart_view = QChartView(chart)
            chart_view.setRenderHint(QPainter.Antialiasing)
            chart_view.setMaximumHeight(300)

            return chart_view

        except ImportError:
            # في حالة عدم توفر QtCharts
            placeholder = QLabel("الرسم البياني غير متاح\n(QtCharts غير مثبت)")
            placeholder.setAlignment(Qt.AlignCenter)
            placeholder.setStyleSheet("color: #6b7280; font-style: italic;")
            placeholder.setMaximumHeight(300)
            return placeholder

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # تحديث البيانات
        refresh_action = toolbar.addAction("🔄 تحديث")
        refresh_action.setToolTip("تحديث جميع البيانات")
        refresh_action.triggered.connect(self.refresh_all_data)

        toolbar.addSeparator()

        # إنشاء حساب جديد
        new_account_action = toolbar.addAction("➕ حساب جديد")
        new_account_action.setToolTip("إنشاء حساب مورد جديد")
        new_account_action.triggered.connect(self.create_new_account)

        # معاملة جديدة
        new_transaction_action = toolbar.addAction("💰 معاملة جديدة")
        new_transaction_action.setToolTip("إنشاء معاملة جديدة")
        new_transaction_action.triggered.connect(self.create_new_transaction)

        toolbar.addSeparator()

        # تصدير البيانات
        export_action = toolbar.addAction("📤 تصدير")
        export_action.setToolTip("تصدير البيانات")
        export_action.triggered.connect(self.export_data)

        # طباعة التقارير
        print_action = toolbar.addAction("🖨️ طباعة")
        print_action.setToolTip("طباعة التقارير")
        print_action.triggered.connect(self.print_reports)

        toolbar.addSeparator()

        # الإعدادات
        settings_action = toolbar.addAction("⚙️ الإعدادات")
        settings_action.setToolTip("إعدادات النظام")
        settings_action.triggered.connect(lambda: self.tabs.setCurrentIndex(6))

        toolbar.addSeparator()

        # زر الخروج
        exit_action = toolbar.addAction("❌ خروج")
        exit_action.setToolTip("إغلاق نافذة إدارة حسابات الموردين")
        exit_action.triggered.connect(self.close)

    def setup_statusbar(self):
        """إعداد شريط الحالة"""
        self.statusbar = self.statusBar()

        # حالة الاتصال
        self.connection_status = QLabel("متصل")
        self.connection_status.setStyleSheet("color: green; font-weight: bold;")
        self.statusbar.addWidget(self.connection_status)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # آخر تحديث
        self.last_update_label = QLabel("آخر تحديث: غير محدد")
        self.statusbar.addPermanentWidget(self.last_update_label)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # عدد السجلات
        self.records_count_label = QLabel("السجلات: 0")
        self.statusbar.addPermanentWidget(self.records_count_label)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.statusbar.addPermanentWidget(self.progress_bar)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # البحث والتصفية في الحسابات
        self.accounts_search_input.textChanged.connect(self.filter_accounts)
        self.supplier_filter_combo.currentTextChanged.connect(self.filter_accounts)
        self.currency_filter_combo.currentTextChanged.connect(self.filter_accounts)
        self.status_filter_combo.currentTextChanged.connect(self.filter_accounts)

        # تصفية المعاملات
        self.transaction_account_filter.currentTextChanged.connect(self.filter_transactions)
        self.transaction_type_filter.currentTextChanged.connect(self.filter_transactions)
        self.transaction_from_date.dateChanged.connect(self.filter_transactions)
        self.transaction_to_date.dateChanged.connect(self.filter_transactions)

        # تحديد حساب في جدول الأرصدة
        self.balances_accounts_list.itemClicked.connect(self.show_account_details)

        # تحديد حساب في الجدول الرئيسي
        self.accounts_table.itemClicked.connect(self.on_account_selected)

        # إعدادات التحديث التلقائي
        self.auto_refresh_checkbox.toggled.connect(self.toggle_auto_refresh)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            self.load_accounts_data()
            self.load_suppliers_data()
            self.load_currencies_data()
            self.load_transactions_data()
            self.update_statistics()
            self.update_filters()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        if self.auto_refresh_checkbox.isChecked():
            interval = self.refresh_interval_spinbox.value() * 1000  # تحويل إلى ميلي ثانية
            self.refresh_timer.timeout.connect(self.refresh_all_data)
            self.refresh_timer.start(interval)

    def load_accounts_data(self):
        """تحميل بيانات الحسابات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # استعلام شامل للحسابات مع معلومات الموردين والعملات
            query = """
                SELECT sa.*, s.name as supplier_name, c.code as currency_code, c.name as currency_name
                FROM supplier_accounts sa
                LEFT JOIN suppliers s ON sa.supplier_id = s.id
                LEFT JOIN currencies c ON sa.currency_id = c.id
                ORDER BY sa.created_at DESC
            """

            cursor.execute(query)
            self.accounts_data = cursor.fetchall()

            # تحديث جدول الحسابات
            self.update_accounts_table()

            # تحديث قائمة الحسابات في تبويب الأرصدة
            self.update_balances_accounts_list()

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الحسابات: {e}")
            self.accounts_data = []

    def load_suppliers_data(self):
        """تحميل بيانات الموردين"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name")
            self.suppliers_data = cursor.fetchall()

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الموردين: {e}")
            self.suppliers_data = []

    def load_currencies_data(self):
        """تحميل بيانات العملات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT id, code, name FROM currencies WHERE is_active = 1 ORDER BY name")
            self.currencies_data = cursor.fetchall()

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات العملات: {e}")
            self.currencies_data = []

    def load_transactions_data(self):
        """تحميل بيانات المعاملات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء جدول المعاملات إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS account_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    account_id INTEGER NOT NULL,
                    transaction_type TEXT NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    balance_after DECIMAL(15,2) NOT NULL,
                    description TEXT,
                    reference_number TEXT,
                    status TEXT DEFAULT 'completed',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (account_id) REFERENCES supplier_accounts(id)
                )
            """)

            # تحميل المعاملات مع معلومات الحسابات
            query = """
                SELECT at.*, sa.account_number, s.name as supplier_name
                FROM account_transactions at
                LEFT JOIN supplier_accounts sa ON at.account_id = sa.id
                LEFT JOIN suppliers s ON sa.supplier_id = s.id
                ORDER BY at.created_at DESC
                LIMIT 1000
            """

            cursor.execute(query)
            self.transactions_data = cursor.fetchall()

            # تحديث جدول المعاملات
            self.update_transactions_table()

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات المعاملات: {e}")
            self.transactions_data = []

    def update_accounts_table(self):
        """تحديث جدول الحسابات"""
        self.accounts_table.setRowCount(len(self.accounts_data))

        for row, account in enumerate(self.accounts_data):
            # الرقم
            self.accounts_table.setItem(row, 0, QTableWidgetItem(str(account[0])))

            # رقم الحساب
            self.accounts_table.setItem(row, 1, QTableWidgetItem(account[3] or ""))

            # المورد
            supplier_name = account[34] if len(account) > 34 else ""
            self.accounts_table.setItem(row, 2, QTableWidgetItem(supplier_name or ""))

            # العملة
            currency_code = account[35] if len(account) > 35 else ""
            self.accounts_table.setItem(row, 3, QTableWidgetItem(currency_code or ""))

            # الرصيد الحالي
            current_balance = account[5] if len(account) > 5 else 0
            balance_item = QTableWidgetItem(f"{current_balance:,.2f}")
            if current_balance < 0:
                balance_item.setBackground(QColor("#fef2f2"))
            elif current_balance > 100000:
                balance_item.setBackground(QColor("#f0fdf4"))
            self.accounts_table.setItem(row, 4, balance_item)

            # الرصيد المتاح
            available_balance = account[6] if len(account) > 6 else 0
            self.accounts_table.setItem(row, 5, QTableWidgetItem(f"{available_balance:,.2f}"))

            # حد الائتمان
            credit_limit = account[9] if len(account) > 9 else 0
            self.accounts_table.setItem(row, 6, QTableWidgetItem(f"{credit_limit:,.2f}"))

            # الحالة
            is_active = account[17] if len(account) > 17 else True
            is_frozen = account[19] if len(account) > 19 else False

            if is_frozen:
                status = "مجمد"
                status_color = "#fbbf24"
            elif is_active:
                status = "نشط"
                status_color = "#10b981"
            else:
                status = "غير نشط"
                status_color = "#ef4444"

            status_item = QTableWidgetItem(status)
            status_item.setBackground(QColor(status_color))
            status_item.setForeground(QColor("white"))
            self.accounts_table.setItem(row, 7, status_item)

            # تاريخ الإنشاء
            created_at = account[33] if len(account) > 33 else ""
            if created_at:
                created_date = created_at[:10]  # أخذ التاريخ فقط
                self.accounts_table.setItem(row, 8, QTableWidgetItem(created_date))
            else:
                self.accounts_table.setItem(row, 8, QTableWidgetItem(""))

    def update_transactions_table(self):
        """تحديث جدول المعاملات"""
        self.transactions_table.setRowCount(len(self.transactions_data))

        for row, transaction in enumerate(self.transactions_data):
            # الرقم
            self.transactions_table.setItem(row, 0, QTableWidgetItem(str(transaction[0])))

            # رقم الحساب
            account_number = transaction[10] if len(transaction) > 10 else ""
            self.transactions_table.setItem(row, 1, QTableWidgetItem(account_number or ""))

            # نوع المعاملة
            transaction_type = transaction[2] or ""
            type_item = QTableWidgetItem(transaction_type)

            # تلوين حسب نوع المعاملة
            if transaction_type == "إيداع":
                type_item.setBackground(QColor("#dcfce7"))
            elif transaction_type == "سحب":
                type_item.setBackground(QColor("#fef2f2"))
            elif transaction_type == "تحويل":
                type_item.setBackground(QColor("#dbeafe"))

            self.transactions_table.setItem(row, 2, type_item)

            # المبلغ
            amount = transaction[3] or 0
            amount_item = QTableWidgetItem(f"{amount:,.2f}")
            if amount < 0:
                amount_item.setForeground(QColor("#ef4444"))
            else:
                amount_item.setForeground(QColor("#059669"))
            self.transactions_table.setItem(row, 3, amount_item)

            # العملة (يمكن إضافتها لاحقاً)
            self.transactions_table.setItem(row, 4, QTableWidgetItem("ريال"))

            # الرصيد بعد المعاملة
            balance_after = transaction[4] or 0
            self.transactions_table.setItem(row, 5, QTableWidgetItem(f"{balance_after:,.2f}"))

            # التاريخ
            created_at = transaction[8] if len(transaction) > 8 else ""
            if created_at:
                date_time = created_at[:16]  # التاريخ والوقت
                self.transactions_table.setItem(row, 6, QTableWidgetItem(date_time))
            else:
                self.transactions_table.setItem(row, 6, QTableWidgetItem(""))

            # الوصف
            description = transaction[5] or ""
            self.transactions_table.setItem(row, 7, QTableWidgetItem(description))

            # الحالة
            status = transaction[7] or "مكتمل"
            status_item = QTableWidgetItem(status)
            if status == "مكتمل":
                status_item.setBackground(QColor("#dcfce7"))
            elif status == "معلق":
                status_item.setBackground(QColor("#fef3c7"))
            elif status == "فاشل":
                status_item.setBackground(QColor("#fef2f2"))
            self.transactions_table.setItem(row, 8, status_item)

    def update_balances_accounts_list(self):
        """تحديث قائمة الحسابات في تبويب الأرصدة"""
        self.balances_accounts_list.setRowCount(len(self.accounts_data))

        for row, account in enumerate(self.accounts_data):
            # رقم الحساب
            self.balances_accounts_list.setItem(row, 0, QTableWidgetItem(account[3] or ""))

            # المورد
            supplier_name = account[34] if len(account) > 34 else ""
            self.balances_accounts_list.setItem(row, 1, QTableWidgetItem(supplier_name or ""))

            # الرصيد
            current_balance = account[5] if len(account) > 5 else 0
            balance_item = QTableWidgetItem(f"{current_balance:,.2f}")
            if current_balance < 0:
                balance_item.setForeground(QColor("#ef4444"))
            else:
                balance_item.setForeground(QColor("#059669"))
            self.balances_accounts_list.setItem(row, 2, balance_item)

            # العملة
            currency_code = account[35] if len(account) > 35 else ""
            self.balances_accounts_list.setItem(row, 3, QTableWidgetItem(currency_code or ""))

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            total_accounts = len(self.accounts_data)
            active_accounts = len([acc for acc in self.accounts_data if acc[17]])  # is_active

            # حساب إجمالي الأرصدة
            total_balance = sum([acc[5] for acc in self.accounts_data if acc[5]])

            # حساب المعاملات المعلقة
            pending_transactions = len([trans for trans in self.transactions_data if trans[7] == "معلق"])

            # حساب الحسابات المتأخرة (مثال: الحسابات التي لم تستخدم لأكثر من 30 يوم)
            overdue_accounts = 0  # يمكن تطوير هذا لاحقاً

            # حساب استخدام الائتمان
            total_credit_limit = sum([acc[9] for acc in self.accounts_data if acc[9]])
            used_credit = sum([acc[10] for acc in self.accounts_data if acc[10]])
            credit_utilization = (used_credit / total_credit_limit * 100) if total_credit_limit > 0 else 0

            # تحديث التسميات
            self.total_accounts_label.setText(f"إجمالي الحسابات\n{total_accounts}")
            self.active_accounts_label.setText(f"الحسابات النشطة\n{active_accounts}")
            self.total_balance_label.setText(f"إجمالي الأرصدة\n{total_balance:,.2f} ريال")
            self.pending_transactions_label.setText(f"المعاملات المعلقة\n{pending_transactions}")
            self.overdue_accounts_label.setText(f"الحسابات المتأخرة\n{overdue_accounts}")
            self.credit_utilization_label.setText(f"استخدام الائتمان\n{credit_utilization:.1f}%")

            # تحديث شريط الحالة
            self.records_count_label.setText(f"السجلات: {total_accounts}")
            self.last_update_label.setText(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def update_filters(self):
        """تحديث قوائم التصفية"""
        # تحديث فلتر الموردين
        self.supplier_filter_combo.clear()
        self.supplier_filter_combo.addItem("جميع الموردين")
        for supplier in self.suppliers_data:
            self.supplier_filter_combo.addItem(supplier[1], supplier[0])

        # تحديث فلتر العملات
        self.currency_filter_combo.clear()
        self.currency_filter_combo.addItem("جميع العملات")
        for currency in self.currencies_data:
            self.currency_filter_combo.addItem(f"{currency[1]} - {currency[2]}", currency[0])

        # تحديث فلتر الحسابات في المعاملات
        self.transaction_account_filter.clear()
        self.transaction_account_filter.addItem("جميع الحسابات")
        for account in self.accounts_data:
            account_display = f"{account[3]} - {account[34] if len(account) > 34 else 'غير محدد'}"
            self.transaction_account_filter.addItem(account_display, account[0])

        # تحديث فلاتر التقارير
        self.report_supplier_filter.clear()
        self.report_supplier_filter.addItem("جميع الموردين")
        for supplier in self.suppliers_data:
            self.report_supplier_filter.addItem(supplier[1], supplier[0])

        self.report_currency_filter.clear()
        self.report_currency_filter.addItem("جميع العملات")
        for currency in self.currencies_data:
            self.report_currency_filter.addItem(f"{currency[1]} - {currency[2]}", currency[0])

    # دوال التصفية والبحث
    def filter_accounts(self):
        """تصفية الحسابات"""
        search_text = self.accounts_search_input.text().lower()
        supplier_id = self.supplier_filter_combo.currentData()
        currency_id = self.currency_filter_combo.currentData()
        status_filter = self.status_filter_combo.currentText()

        for row in range(self.accounts_table.rowCount()):
            show_row = True

            # تصفية النص
            if search_text:
                account_number = self.accounts_table.item(row, 1).text().lower()
                supplier_name = self.accounts_table.item(row, 2).text().lower()
                if search_text not in account_number and search_text not in supplier_name:
                    show_row = False

            # تصفية المورد
            if supplier_id and show_row:
                # البحث عن المورد في البيانات
                account_id = int(self.accounts_table.item(row, 0).text())
                account_data = next((acc for acc in self.accounts_data if acc[0] == account_id), None)
                if account_data and account_data[1] != supplier_id:
                    show_row = False

            # تصفية العملة
            if currency_id and show_row:
                # البحث عن العملة في البيانات
                account_id = int(self.accounts_table.item(row, 0).text())
                account_data = next((acc for acc in self.accounts_data if acc[0] == account_id), None)
                if account_data and account_data[2] != currency_id:
                    show_row = False

            # تصفية الحالة
            if status_filter != "جميع الحالات" and show_row:
                account_status = self.accounts_table.item(row, 7).text()
                if account_status != status_filter:
                    show_row = False

            self.accounts_table.setRowHidden(row, not show_row)

    def filter_transactions(self):
        """تصفية المعاملات"""
        account_id = self.transaction_account_filter.currentData()
        transaction_type = self.transaction_type_filter.currentText()
        from_date = self.transaction_from_date.date()
        to_date = self.transaction_to_date.date()

        for row in range(self.transactions_table.rowCount()):
            show_row = True

            # تصفية الحساب
            if account_id and show_row:
                transaction_id = int(self.transactions_table.item(row, 0).text())
                transaction_data = next((trans for trans in self.transactions_data if trans[0] == transaction_id), None)
                if transaction_data and transaction_data[1] != account_id:
                    show_row = False

            # تصفية نوع المعاملة
            if transaction_type != "جميع الأنواع" and show_row:
                trans_type = self.transactions_table.item(row, 2).text()
                if trans_type != transaction_type:
                    show_row = False

            # تصفية التاريخ
            if show_row:
                date_text = self.transactions_table.item(row, 6).text()
                if date_text:
                    trans_date = QDate.fromString(date_text[:10], "yyyy-MM-dd")
                    if trans_date < from_date or trans_date > to_date:
                        show_row = False

            self.transactions_table.setRowHidden(row, not show_row)

    def show_account_details(self, item):
        """عرض تفاصيل الحساب المحدد"""
        row = item.row()
        account_number = self.balances_accounts_list.item(row, 0).text()

        # البحث عن الحساب في البيانات
        account_data = None
        for account in self.accounts_data:
            if account[3] == account_number:
                account_data = account
                break

        if not account_data:
            return

        # تحديث تفاصيل الحساب
        self.selected_account_number_label.setText(account_data[3] or "-")
        self.selected_supplier_label.setText(account_data[34] if len(account_data) > 34 else "-")
        self.selected_currency_label.setText(account_data[35] if len(account_data) > 35 else "-")

        # تحديث الأرصدة
        current_balance = account_data[5] or 0
        available_balance = account_data[6] or 0
        blocked_balance = account_data[7] or 0

        self.current_balance_label.setText(f"{current_balance:,.2f}")
        self.available_balance_label.setText(f"{available_balance:,.2f}")
        self.blocked_balance_label.setText(f"{blocked_balance:,.2f}")

        # تحديث حدود الائتمان
        credit_limit = account_data[9] or 0
        used_credit = account_data[10] or 0
        available_credit = account_data[11] or 0

        self.credit_limit_label.setText(f"{credit_limit:,.2f}")
        self.used_credit_label.setText(f"{used_credit:,.2f}")
        self.available_credit_label.setText(f"{available_credit:,.2f}")

        # تحديث شريط تقدم الائتمان
        if credit_limit > 0:
            usage_percentage = (used_credit / credit_limit) * 100
            self.credit_usage_progress.setValue(int(usage_percentage))

            # تغيير لون الشريط حسب النسبة
            if usage_percentage > 90:
                color = "#ef4444"  # أحمر
            elif usage_percentage > 75:
                color = "#f59e0b"  # برتقالي
            else:
                color = "#10b981"  # أخضر

            self.credit_usage_progress.setStyleSheet(f"""
                QProgressBar {{
                    border: 2px solid #e5e7eb;
                    border-radius: 5px;
                    text-align: center;
                }}
                QProgressBar::chunk {{
                    background-color: {color};
                    border-radius: 3px;
                }}
            """)
        else:
            self.credit_usage_progress.setValue(0)

        # تحديث حدود المعاملات
        max_transaction = account_data[14] or 0
        daily_limit = account_data[15] or 0
        monthly_limit = account_data[16] or 0

        self.max_transaction_label.setText(f"{max_transaction:,.2f}")
        self.daily_limit_label.setText(f"{daily_limit:,.2f}")
        self.monthly_limit_label.setText(f"{monthly_limit:,.2f}")

        # حفظ معرف الحساب المحدد
        self.selected_account_id = account_data[0]

    def on_account_selected(self, item):
        """معالج تحديد حساب في الجدول الرئيسي"""
        row = item.row()
        account_id = int(self.accounts_table.item(row, 0).text())
        self.selected_account_id = account_id

    # دوال الإجراءات
    def create_new_account(self):
        """إنشاء حساب جديد"""
        try:
            from .new_supplier_account_dialog import NewSupplierAccountDialog

            dialog = NewSupplierAccountDialog(self)
            dialog.account_created.connect(self.on_account_created)

            if dialog.exec() == QDialog.Accepted:
                self.refresh_all_data()
                QMessageBox.information(self, "نجح", "تم إنشاء الحساب بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء الحساب: {str(e)}")

    def on_account_created(self, account_id):
        """معالج إنشاء حساب جديد"""
        self.refresh_all_data()

    def edit_selected_account(self):
        """تعديل الحساب المحدد"""
        if not self.selected_account_id:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد حساب للتعديل")
            return

        QMessageBox.information(self, "قريباً", "نافذة تعديل الحساب قيد التطوير")

    def freeze_selected_account(self):
        """تجميد الحساب المحدد"""
        if not self.selected_account_id:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد حساب للتجميد")
            return

        reply = QMessageBox.question(self, "تأكيد", "هل أنت متأكد من تجميد هذا الحساب؟")
        if reply == QMessageBox.Yes:
            try:
                db_path = Path("data/proshipment.db")
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()

                cursor.execute("UPDATE supplier_accounts SET is_frozen = 1 WHERE id = ?",
                             (self.selected_account_id,))
                conn.commit()
                conn.close()

                self.refresh_all_data()
                QMessageBox.information(self, "نجح", "تم تجميد الحساب بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في تجميد الحساب: {str(e)}")

    def close_selected_account(self):
        """إغلاق الحساب المحدد"""
        if not self.selected_account_id:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد حساب للإغلاق")
            return

        reply = QMessageBox.question(self, "تأكيد", "هل أنت متأكد من إغلاق هذا الحساب؟\nلا يمكن التراجع عن هذا الإجراء.")
        if reply == QMessageBox.Yes:
            try:
                db_path = Path("data/proshipment.db")
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()

                cursor.execute("UPDATE supplier_accounts SET is_active = 0 WHERE id = ?",
                             (self.selected_account_id,))
                conn.commit()
                conn.close()

                self.refresh_all_data()
                QMessageBox.information(self, "نجح", "تم إغلاق الحساب بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في إغلاق الحساب: {str(e)}")

    def create_new_transaction(self):
        """إنشاء معاملة جديدة"""
        try:
            from .new_transaction_dialog import NewTransactionDialog

            dialog = NewTransactionDialog(self, self.selected_account_id)
            dialog.transaction_created.connect(self.on_transaction_created)

            if dialog.exec() == QDialog.Accepted:
                self.refresh_all_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة المعاملة الجديدة: {str(e)}")

    def bulk_transfer(self):
        """تحويل جماعي"""
        try:
            from .bulk_transfer_dialog import BulkTransferDialog

            dialog = BulkTransferDialog(self)
            dialog.transfers_completed.connect(self.on_transfers_completed)

            if dialog.exec() == QDialog.Accepted:
                self.refresh_all_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة التحويل الجماعي: {str(e)}")

    def reconcile_accounts(self):
        """تسوية الحسابات"""
        try:
            from .account_reconciliation_dialog import AccountReconciliationDialog

            dialog = AccountReconciliationDialog(self, self.selected_account_id)
            dialog.reconciliation_completed.connect(self.on_reconciliation_completed)

            if dialog.exec() == QDialog.Accepted:
                self.refresh_all_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة تسوية الحسابات: {str(e)}")

    def adjust_balance(self):
        """تعديل الرصيد"""
        if not self.selected_account_id:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد حساب أولاً")
            return

        QMessageBox.information(self, "قريباً", "نافذة تعديل الرصيد قيد التطوير")

    def adjust_limits(self):
        """تعديل الحدود"""
        if not self.selected_account_id:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد حساب أولاً")
            return

        QMessageBox.information(self, "قريباً", "نافذة تعديل الحدود قيد التطوير")

    def block_amount(self):
        """حجز مبلغ"""
        if not self.selected_account_id:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد حساب أولاً")
            return

        QMessageBox.information(self, "قريباً", "نافذة حجز المبلغ قيد التطوير")

    def unblock_amount(self):
        """إلغاء حجز مبلغ"""
        if not self.selected_account_id:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد حساب أولاً")
            return

        QMessageBox.information(self, "قريباً", "نافذة إلغاء حجز المبلغ قيد التطوير")

    # دوال التصدير
    def export_accounts_to_excel(self):
        """تصدير الحسابات إلى Excel"""
        try:
            from PySide6.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير الحسابات",
                f"supplier_accounts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )

            if file_path:
                # إنشاء ملف Excel
                workbook = xlsxwriter.Workbook(file_path)
                worksheet = workbook.add_worksheet('الحسابات')

                # إعداد التنسيق
                header_format = workbook.add_format({
                    'bold': True,
                    'bg_color': '#4472C4',
                    'font_color': 'white',
                    'border': 1
                })

                # كتابة العناوين
                headers = ["الرقم", "رقم الحساب", "المورد", "العملة", "الرصيد الحالي",
                          "الرصيد المتاح", "حد الائتمان", "الحالة", "تاريخ الإنشاء"]

                for col, header in enumerate(headers):
                    worksheet.write(0, col, header, header_format)

                # كتابة البيانات
                for row in range(self.accounts_table.rowCount()):
                    if not self.accounts_table.isRowHidden(row):
                        for col in range(len(headers)):
                            item = self.accounts_table.item(row, col)
                            if item:
                                worksheet.write(row + 1, col, item.text())

                workbook.close()
                QMessageBox.information(self, "نجح", f"تم تصدير البيانات إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تصدير البيانات: {str(e)}")

    def export_accounts_to_csv(self):
        """تصدير الحسابات إلى CSV"""
        try:
            from PySide6.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير الحسابات",
                f"supplier_accounts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    headers = ["الرقم", "رقم الحساب", "المورد", "العملة", "الرصيد الحالي",
                              "الرصيد المتاح", "حد الائتمان", "الحالة", "تاريخ الإنشاء"]
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(self.accounts_table.rowCount()):
                        if not self.accounts_table.isRowHidden(row):
                            row_data = []
                            for col in range(len(headers)):
                                item = self.accounts_table.item(row, col)
                                row_data.append(item.text() if item else "")
                            writer.writerow(row_data)

                QMessageBox.information(self, "نجح", f"تم تصدير البيانات إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تصدير البيانات: {str(e)}")

    # دوال التقارير
    def generate_report(self):
        """إنشاء التقرير"""
        report_type = self.report_type_combo.currentText()
        from_date = self.report_from_date.date().toString("yyyy-MM-dd")
        to_date = self.report_to_date.date().toString("yyyy-MM-dd")

        report_content = f"""
========================================
{report_type}
========================================

الفترة: من {from_date} إلى {to_date}
تاريخ الإنشاء: {datetime.now().strftime("%Y-%m-%d %H:%M")}

========================================
الإحصائيات العامة:
========================================

إجمالي الحسابات: {len(self.accounts_data)}
الحسابات النشطة: {len([acc for acc in self.accounts_data if acc[17]])}
إجمالي الأرصدة: {sum([acc[5] for acc in self.accounts_data if acc[5]]):,.2f} ريال
إجمالي المعاملات: {len(self.transactions_data)}

========================================
تفاصيل الحسابات:
========================================
"""

        # إضافة تفاصيل الحسابات
        for account in self.accounts_data[:10]:  # أول 10 حسابات
            supplier_name = account[34] if len(account) > 34 else "غير محدد"
            current_balance = account[5] if len(account) > 5 else 0
            currency_code = account[35] if len(account) > 35 else "ريال"

            report_content += f"""
رقم الحساب: {account[3]}
المورد: {supplier_name}
الرصيد: {current_balance:,.2f} {currency_code}
الحالة: {"نشط" if account[17] else "غير نشط"}
---
"""

        report_content += f"""
========================================
ملاحظة: هذا تقرير تجريبي. التقارير التفصيلية قيد التطوير.
========================================
        """

        self.report_display.setPlainText(report_content)

    def export_report_pdf(self):
        """تصدير التقرير إلى PDF"""
        QMessageBox.information(self, "قريباً", "تصدير PDF قيد التطوير")

    def export_report_excel(self):
        """تصدير التقرير إلى Excel"""
        QMessageBox.information(self, "قريباً", "تصدير Excel للتقارير قيد التطوير")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قريباً", "طباعة التقارير قيد التطوير")

    def quick_preview(self):
        """معاينة سريعة"""
        self.generate_report()

    # دوال التنبيهات
    def mark_alerts_read(self):
        """تحديد التنبيهات كمقروءة"""
        QMessageBox.information(self, "تم", "تم تحديد التنبيهات كمقروءة")

    def dismiss_alerts(self):
        """تجاهل التنبيهات"""
        QMessageBox.information(self, "تم", "تم تجاهل التنبيهات المحددة")

    def export_alerts(self):
        """تصدير التنبيهات"""
        QMessageBox.information(self, "قريباً", "تصدير التنبيهات قيد التطوير")

    # دوال الإعدادات
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings = {
                'auto_refresh': self.auto_refresh_checkbox.isChecked(),
                'refresh_interval': self.refresh_interval_spinbox.value(),
                'records_per_page': self.records_per_page_spinbox.value(),
                'require_approval': self.require_approval_checkbox.isChecked(),
                'approval_threshold': self.approval_threshold_spinbox.value(),
                'audit_log': self.audit_log_checkbox.isChecked(),
                'low_balance_alerts': self.low_balance_alerts_checkbox.isChecked(),
                'high_balance_alerts': self.high_balance_alerts_checkbox.isChecked(),
                'credit_limit_alerts': self.credit_limit_alerts_checkbox.isChecked(),
                'suspicious_transactions': self.suspicious_transactions_checkbox.isChecked()
            }

            # حفظ الإعدادات في ملف
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)

            with open(config_dir / "supplier_accounts_settings.json", 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الإعدادات: {str(e)}")

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        self.auto_refresh_checkbox.setChecked(True)
        self.refresh_interval_spinbox.setValue(30)
        self.records_per_page_spinbox.setValue(100)
        self.require_approval_checkbox.setChecked(False)
        self.approval_threshold_spinbox.setValue(10000)
        self.audit_log_checkbox.setChecked(True)
        self.low_balance_alerts_checkbox.setChecked(True)
        self.high_balance_alerts_checkbox.setChecked(False)
        self.credit_limit_alerts_checkbox.setChecked(True)
        self.suspicious_transactions_checkbox.setChecked(False)

    def backup_settings(self):
        """نسخ احتياطي للإعدادات"""
        QMessageBox.information(self, "قريباً", "نسخ احتياطي للإعدادات قيد التطوير")

    def restore_settings(self):
        """استعادة الإعدادات"""
        QMessageBox.information(self, "قريباً", "استعادة الإعدادات قيد التطوير")

    # دوال مساعدة
    def toggle_auto_refresh(self, enabled):
        """تفعيل/تعطيل التحديث التلقائي"""
        if enabled:
            self.start_auto_refresh()
        else:
            self.refresh_timer.stop()

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)

        try:
            self.load_initial_data()
            QTimer.singleShot(1000, self.hide_progress_bar)

        except Exception as e:
            self.hide_progress_bar()
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث البيانات: {str(e)}")

    def hide_progress_bar(self):
        """إخفاء شريط التقدم"""
        self.progress_bar.setVisible(False)

    def export_data(self):
        """تصدير البيانات"""
        self.export_accounts_to_excel()

    def print_reports(self):
        """طباعة التقارير"""
        self.print_report()

    def on_transaction_created(self, transaction_id):
        """معالج إنشاء معاملة جديدة"""
        self.refresh_all_data()
        QMessageBox.information(self, "نجح", f"تم إنشاء المعاملة رقم {transaction_id} بنجاح")

    def on_transfers_completed(self, transfer_ids):
        """معالج اكتمال التحويلات الجماعية"""
        self.refresh_all_data()
        QMessageBox.information(self, "نجح", f"تم إكمال {len(transfer_ids)} تحويل بنجاح")

    def on_reconciliation_completed(self, reconciliation_id):
        """معالج اكتمال تسوية الحسابات"""
        self.refresh_all_data()
        QMessageBox.information(self, "نجح", f"تم إكمال التسوية رقم {reconciliation_id} بنجاح")
