# -*- coding: utf-8 -*-
"""
سكريبت تحديث قاعدة البيانات لإضافة جداول نظام إدارة الحوالات
Database Update Script for Remittances Management System
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.database.database_manager import DatabaseManager
from src.database.models import (Base, Bank, BankAccount, SupplierAccount, Remittance,
                                RemittanceDetail, RemittanceStatusHistory, SupplierTransaction,
                                RemittanceDocument, RemittanceReport, Currency, Supplier,
                                RemittanceStatus, TransactionType)
from sqlalchemy import text
from datetime import datetime


def update_database():
    """تحديث قاعدة البيانات لإضافة جداول الحوالات"""
    try:
        print("بدء تحديث قاعدة البيانات...")
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # إنشاء الجداول الجديدة
        print("إنشاء الجداول الجديدة...")
        Base.metadata.create_all(db_manager.engine)
        print("تم إنشاء الجداول بنجاح")
        
        # إضافة البيانات الافتراضية
        add_default_data(db_manager)
        
        print("تم تحديث قاعدة البيانات بنجاح!")
        
    except Exception as e:
        print(f"خطأ في تحديث قاعدة البيانات: {e}")
        return False
        
    return True


def add_default_data(db_manager):
    """إضافة البيانات الافتراضية"""
    try:
        session = db_manager.get_session()
        
        # إضافة بنوك افتراضية
        add_default_banks(session)
        
        # إضافة حسابات بنكية افتراضية
        add_default_bank_accounts(session)
        
        # إضافة حسابات موردين افتراضية
        add_default_supplier_accounts(session)
        
        session.commit()
        print("تم إضافة البيانات الافتراضية بنجاح")
        
    except Exception as e:
        session.rollback()
        print(f"خطأ في إضافة البيانات الافتراضية: {e}")
    finally:
        session.close()


def add_default_banks(session):
    """إضافة بنوك افتراضية"""
    try:
        # التحقق من وجود بنوك
        existing_banks = session.query(Bank).count()
        if existing_banks > 0:
            print("البنوك موجودة بالفعل، تخطي إضافة البنوك الافتراضية")
            return
            
        default_banks = [
            {
                'code': 'SNB',
                'name': 'البنك الأهلي السعودي',
                'name_en': 'Saudi National Bank',
                'swift_code': 'NCBKSAJE',
                'country': 'المملكة العربية السعودية',
                'city': 'الرياض'
            },
            {
                'code': 'RJHI',
                'name': 'مصرف الراجحي',
                'name_en': 'Al Rajhi Bank',
                'swift_code': 'RJHISARI',
                'country': 'المملكة العربية السعودية',
                'city': 'الرياض'
            },
            {
                'code': 'SABB',
                'name': 'البنك السعودي البريطاني',
                'name_en': 'Saudi British Bank',
                'swift_code': 'SABBSARI',
                'country': 'المملكة العربية السعودية',
                'city': 'الرياض'
            },
            {
                'code': 'RIBL',
                'name': 'بنك الرياض',
                'name_en': 'Riyad Bank',
                'swift_code': 'RIBLSARI',
                'country': 'المملكة العربية السعودية',
                'city': 'الرياض'
            },
            {
                'code': 'SAMBA',
                'name': 'بنك سامبا المالي',
                'name_en': 'Samba Financial Group',
                'swift_code': 'SAMBSARI',
                'country': 'المملكة العربية السعودية',
                'city': 'الرياض'
            }
        ]
        
        for bank_data in default_banks:
            bank = Bank(
                code=bank_data['code'],
                name=bank_data['name'],
                name_en=bank_data['name_en'],
                swift_code=bank_data['swift_code'],
                country=bank_data['country'],
                city=bank_data['city'],
                is_active=True,
                created_by=1
            )
            session.add(bank)
            
        print(f"تم إضافة {len(default_banks)} بنك افتراضي")
        
    except Exception as e:
        print(f"خطأ في إضافة البنوك الافتراضية: {e}")


def add_default_bank_accounts(session):
    """إضافة حسابات بنكية افتراضية"""
    try:
        # التحقق من وجود حسابات بنكية
        existing_accounts = session.query(BankAccount).count()
        if existing_accounts > 0:
            print("الحسابات البنكية موجودة بالفعل، تخطي إضافة الحسابات الافتراضية")
            return
            
        # الحصول على البنوك والعملات
        banks = session.query(Bank).all()
        currencies = session.query(Currency).all()
        
        if not banks or not currencies:
            print("لا توجد بنوك أو عملات، تخطي إضافة الحسابات البنكية")
            return
            
        # إضافة حساب لكل بنك مع العملة الأساسية (ريال سعودي)
        sar_currency = session.query(Currency).filter(Currency.code == 'SAR').first()
        usd_currency = session.query(Currency).filter(Currency.code == 'USD').first()
        
        account_counter = 1
        
        for bank in banks:
            # حساب بالريال السعودي
            if sar_currency:
                account = BankAccount(
                    bank_id=bank.id,
                    account_number=f"{bank.code}-SAR-{account_counter:06d}",
                    account_name=f"حساب {bank.name} - ريال سعودي",
                    iban=f"SA{account_counter:02d}{bank.code}000000{account_counter:010d}",
                    currency_id=sar_currency.id,
                    balance=1000000.0,  # رصيد افتراضي
                    is_active=True,
                    created_by=1
                )
                session.add(account)
                account_counter += 1
                
            # حساب بالدولار الأمريكي
            if usd_currency:
                account = BankAccount(
                    bank_id=bank.id,
                    account_number=f"{bank.code}-USD-{account_counter:06d}",
                    account_name=f"حساب {bank.name} - دولار أمريكي",
                    iban=f"SA{account_counter:02d}{bank.code}000000{account_counter:010d}",
                    currency_id=usd_currency.id,
                    balance=100000.0,  # رصيد افتراضي
                    is_active=True,
                    created_by=1
                )
                session.add(account)
                account_counter += 1
                
        print(f"تم إضافة {account_counter - 1} حساب بنكي افتراضي")
        
    except Exception as e:
        print(f"خطأ في إضافة الحسابات البنكية الافتراضية: {e}")


def add_default_supplier_accounts(session):
    """إضافة حسابات موردين افتراضية"""
    try:
        # التحقق من وجود حسابات موردين
        existing_accounts = session.query(SupplierAccount).count()
        if existing_accounts > 0:
            print("حسابات الموردين موجودة بالفعل، تخطي إضافة الحسابات الافتراضية")
            return
            
        # الحصول على الموردين والعملات
        suppliers = session.query(Supplier).all()
        currencies = session.query(Currency).all()
        
        if not suppliers or not currencies:
            print("لا توجد موردين أو عملات، تخطي إضافة حسابات الموردين")
            return
            
        # إضافة حساب لكل مورد مع العملة الأساسية
        sar_currency = session.query(Currency).filter(Currency.code == 'SAR').first()
        
        account_counter = 1
        
        for supplier in suppliers:
            if sar_currency:
                account = SupplierAccount(
                    supplier_id=supplier.id,
                    currency_id=sar_currency.id,
                    account_number=f"SUP-{supplier.code}-{account_counter:06d}",
                    balance=0.0,
                    credit_limit=100000.0,  # حد ائتمان افتراضي
                    available_credit=100000.0,
                    total_remittances=0.0,
                    total_payments=0.0,
                    is_active=True,
                    created_by=1
                )
                session.add(account)
                account_counter += 1
                
        print(f"تم إضافة {account_counter - 1} حساب مورد افتراضي")
        
    except Exception as e:
        print(f"خطأ في إضافة حسابات الموردين الافتراضية: {e}")


def create_sample_remittances(session):
    """إنشاء حوالات تجريبية"""
    try:
        # التحقق من وجود حوالات
        existing_remittances = session.query(Remittance).count()
        if existing_remittances > 0:
            print("الحوالات موجودة بالفعل، تخطي إنشاء الحوالات التجريبية")
            return
            
        # الحصول على البيانات المطلوبة
        suppliers = session.query(Supplier).limit(3).all()
        currencies = session.query(Currency).all()
        banks = session.query(Bank).limit(2).all()
        
        if not suppliers or not currencies or not banks:
            print("البيانات الأساسية غير متوفرة، تخطي إنشاء الحوالات التجريبية")
            return
            
        sar_currency = session.query(Currency).filter(Currency.code == 'SAR').first()
        
        # إنشاء حوالات تجريبية
        sample_remittances = [
            {
                'supplier_id': suppliers[0].id,
                'currency_id': sar_currency.id if sar_currency else currencies[0].id,
                'amount': 50000.0,
                'purpose': 'دفعة مقدمة لطلبية البضائع',
                'status': RemittanceStatus.COMPLETED
            },
            {
                'supplier_id': suppliers[1].id if len(suppliers) > 1 else suppliers[0].id,
                'currency_id': sar_currency.id if sar_currency else currencies[0].id,
                'amount': 75000.0,
                'purpose': 'تسوية مستحقات سابقة',
                'status': RemittanceStatus.PENDING
            },
            {
                'supplier_id': suppliers[2].id if len(suppliers) > 2 else suppliers[0].id,
                'currency_id': sar_currency.id if sar_currency else currencies[0].id,
                'amount': 25000.0,
                'purpose': 'دفعة نهائية للطلبية',
                'status': RemittanceStatus.DRAFT
            }
        ]
        
        for i, remittance_data in enumerate(sample_remittances):
            remittance = Remittance(
                remittance_number=f"REM-2024-{i+1:06d}",
                supplier_id=remittance_data['supplier_id'],
                currency_id=remittance_data['currency_id'],
                amount=remittance_data['amount'],
                exchange_rate=1.0,
                amount_in_base_currency=remittance_data['amount'],
                sender_bank_id=banks[0].id,
                receiver_bank_id=banks[1].id if len(banks) > 1 else banks[0].id,
                status=remittance_data['status'],
                remittance_date=datetime.now().date(),
                purpose=remittance_data['purpose'],
                created_by=1
            )
            session.add(remittance)
            
        print(f"تم إنشاء {len(sample_remittances)} حوالة تجريبية")
        
    except Exception as e:
        print(f"خطأ في إنشاء الحوالات التجريبية: {e}")


if __name__ == "__main__":
    print("=== تحديث قاعدة البيانات لنظام إدارة الحوالات ===")
    
    success = update_database()
    
    if success:
        print("\n✅ تم تحديث قاعدة البيانات بنجاح!")
        print("يمكنك الآن استخدام نظام إدارة الحوالات")
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات")
        print("يرجى مراجعة الأخطاء أعلاه")
