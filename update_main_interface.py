#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث الواجهة الرئيسية - Update Main Interface
تحديث الواجهة التجريبية لتصبح الواجهة الرئيسية
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

def update_main_interface():
    """تحديث الواجهة الرئيسية"""
    print("🔄 تحديث الواجهة الرئيسية للتطبيق")
    print("=" * 50)
    
    # التحقق من وجود الملفات المطلوبة
    required_files = [
        "main_application.py",
        "template_library.py", 
        "form_template.py",
        "example_usage.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   • {file}")
        return False
    
    print("✅ جميع الملفات المطلوبة متوفرة")
    
    # اختبار استيراد الواجهة الرئيسية
    try:
        from main_application import MainApplication
        print("✅ تم استيراد الواجهة الرئيسية بنجاح")
    except ImportError as e:
        print(f"❌ خطأ في استيراد الواجهة الرئيسية: {e}")
        return False
    
    # اختبار مكتبة القوالب
    try:
        from template_library import Templates, نموذج_ادخال_اساسي
        print("✅ تم استيراد مكتبة القوالب بنجاح")
    except ImportError as e:
        print(f"❌ خطأ في استيراد مكتبة القوالب: {e}")
        return False
    
    print("\n🎯 الواجهة الرئيسية الجديدة تحتوي على:")
    print("   📋 واجهة رئيسية محدثة مع أزرار كبيرة")
    print("   🏛️ تكامل مع مكتبة القوالب")
    print("   ⚙️ قوائم وأشرطة أدوات متقدمة")
    print("   📊 شريط حالة تفاعلي")
    print("   🎨 تصميم احترافي وجذاب")
    print("   💡 رسائل ترحيب ومساعدة")
    print("   🔄 إدارة النماذج النشطة")
    print("   ⏰ تحديث الوقت في الوقت الفعلي")
    
    return True

def test_main_interface():
    """اختبار الواجهة الرئيسية"""
    print("\n🧪 اختبار الواجهة الرئيسية...")
    
    try:
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # استيراد واختبار الواجهة
        from main_application import MainApplication
        from template_library import Templates
        
        # إعداد مكتبة القوالب
        Templates.setup_app(app)
        
        # إنشاء الواجهة الرئيسية
        main_app = MainApplication()
        
        print("✅ تم إنشاء الواجهة الرئيسية بنجاح")
        print(f"   العنوان: {main_app.windowTitle()}")
        print(f"   الحجم: {main_app.size().width()}x{main_app.size().height()}")
        print(f"   المستخدم: {main_app.current_user}")
        print(f"   حالة النظام: {main_app.system_status}")
        
        # اختبار إنشاء نموذج
        print("\n🔧 اختبار إنشاء نموذج...")
        form = main_app.open_basic_form()
        if form:
            print("✅ تم إنشاء النموذج الأساسي بنجاح")
        
        # إغلاق الاختبار
        main_app.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        return False

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n📖 تعليمات الاستخدام:")
    print("=" * 30)
    
    print("\n🚀 تشغيل الواجهة الرئيسية:")
    print("   python run_main_app.py")
    print("   أو")
    print("   python main_application.py")
    
    print("\n🔄 البدائل المتاحة:")
    print("   python run_template_library.py  # مكتبة القوالب")
    print("   python template_examples.py     # الأمثلة")
    print("   python quick_template_demo.py   # العرض السريع")
    
    print("\n⚙️ الميزات المتاحة في الواجهة الرئيسية:")
    print("   • نموذج إدخال أساسي مع بيانات تجريبية")
    print("   • مكتبة قوالب شاملة")
    print("   • إدارة النماذج النشطة")
    print("   • قوائم وأشرطة أدوات متقدمة")
    print("   • رسائل مساعدة وترحيب")
    print("   • تحديث الوقت والحالة")
    
    print("\n🎯 الاختصارات:")
    print("   Ctrl+N: نموذج جديد")
    print("   Ctrl+O: فتح")
    print("   Ctrl+Q: خروج")
    print("   F11: ملء الشاشة (في النماذج)")

def main():
    """الدالة الرئيسية"""
    print("🔄 تحديث الواجهة الرئيسية - SHIPMENT ERP")
    print("=" * 60)
    
    # تحديث الواجهة
    if not update_main_interface():
        print("\n❌ فشل في تحديث الواجهة الرئيسية!")
        return 1
    
    # اختبار الواجهة
    if not test_main_interface():
        print("\n❌ فشل في اختبار الواجهة الرئيسية!")
        return 1
    
    # عرض تعليمات الاستخدام
    show_usage_instructions()
    
    print("\n" + "=" * 60)
    print("✅ تم تحديث الواجهة الرئيسية بنجاح!")
    print("🎯 الواجهة الجديدة جاهزة للاستخدام")
    print("🚀 شغل: python run_main_app.py")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
