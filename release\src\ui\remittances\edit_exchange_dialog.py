# -*- coding: utf-8 -*-
"""
نافذة تعديل الصراف
Edit Exchange Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QTextEdit, QPushButton,
                               QGroupBox, QCheckBox, QMessageBox, QFrame, QProgressBar,
                               QDoubleSpinBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

import sqlite3
from pathlib import Path
from datetime import datetime

class EditExchangeDialog(QDialog):
    """نافذة تعديل الصراف"""
    
    exchange_updated = Signal(int)  # إشارة عند تحديث الصراف
    
    def __init__(self, exchange_id, parent=None):
        super().__init__(parent)
        self.exchange_id = exchange_id
        self.setWindowTitle("تعديل الصراف - ProShipment")
        self.setMinimumSize(700, 800)
        self.resize(800, 800)
        self.setModal(True)

        # توسيط النافذة
        self.center_window()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.load_exchange_data()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel("تعديل بيانات الصراف")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # المعلومات الأساسية
        basic_group = QGroupBox("المعلومات الأساسية")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        basic_layout = QGridLayout(basic_group)
        basic_layout.setSpacing(10)
        
        # اسم الصراف
        basic_layout.addWidget(QLabel("اسم الصراف: *"), 0, 0)
        self.exchange_name_input = QLineEdit()
        self.exchange_name_input.setPlaceholderText("أدخل اسم الصراف...")
        self.exchange_name_input.setMinimumHeight(35)
        basic_layout.addWidget(self.exchange_name_input, 0, 1, 1, 3)

        # اسم الصراف بالإنجليزية
        basic_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 1, 0)
        self.exchange_name_en_input = QLineEdit()
        self.exchange_name_en_input.setPlaceholderText("Exchange Name in English...")
        self.exchange_name_en_input.setMinimumHeight(35)
        basic_layout.addWidget(self.exchange_name_en_input, 1, 1, 1, 3)

        # رمز الصراف
        basic_layout.addWidget(QLabel("رمز الصراف:"), 2, 0)
        self.exchange_code_input = QLineEdit()
        self.exchange_code_input.setPlaceholderText("مثال: EX001")
        self.exchange_code_input.setMaxLength(20)
        self.exchange_code_input.setMinimumHeight(35)
        basic_layout.addWidget(self.exchange_code_input, 2, 1)

        # فئة الصراف
        basic_layout.addWidget(QLabel("فئة الصراف:"), 2, 2)
        self.category_combo = QComboBox()
        self.category_combo.addItems([
            "صرافة",
            "مكتب تحويل",
            "وكيل مالي",
            "شركة تحويل",
            "أخرى"
        ])
        self.category_combo.setMinimumHeight(35)
        basic_layout.addWidget(self.category_combo, 2, 3)

        # رقم الترخيص
        basic_layout.addWidget(QLabel("رقم الترخيص:"), 3, 0)
        self.license_number_input = QLineEdit()
        self.license_number_input.setPlaceholderText("رقم ترخيص الصراف...")
        self.license_number_input.setMinimumHeight(35)
        basic_layout.addWidget(self.license_number_input, 3, 1, 1, 3)
        
        layout.addWidget(basic_group)
        
        # معلومات الاتصال
        contact_group = QGroupBox("معلومات الاتصال")
        contact_group.setStyleSheet(basic_group.styleSheet())
        contact_layout = QGridLayout(contact_group)
        contact_layout.setSpacing(10)
        
        # الهاتف
        contact_layout.addWidget(QLabel("الهاتف:"), 0, 0)
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("+966 11 123 4567")
        self.phone_input.setMinimumHeight(35)
        contact_layout.addWidget(self.phone_input, 0, 1)

        # الجوال
        contact_layout.addWidget(QLabel("الجوال:"), 0, 2)
        self.mobile_input = QLineEdit()
        self.mobile_input.setPlaceholderText("+966 50 123 4567")
        self.mobile_input.setMinimumHeight(35)
        contact_layout.addWidget(self.mobile_input, 0, 3)

        # البريد الإلكتروني
        contact_layout.addWidget(QLabel("البريد الإلكتروني:"), 1, 0)
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        self.email_input.setMinimumHeight(35)
        contact_layout.addWidget(self.email_input, 1, 1)

        # الموقع الإلكتروني
        contact_layout.addWidget(QLabel("الموقع الإلكتروني:"), 1, 2)
        self.website_input = QLineEdit()
        self.website_input.setPlaceholderText("www.exchange.com")
        self.website_input.setMinimumHeight(35)
        contact_layout.addWidget(self.website_input, 1, 3)

        # العنوان
        contact_layout.addWidget(QLabel("العنوان:"), 2, 0)
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        self.address_input.setMinimumHeight(80)
        self.address_input.setPlaceholderText("أدخل العنوان التفصيلي...")
        contact_layout.addWidget(self.address_input, 2, 1, 1, 3)
        
        layout.addWidget(contact_group)
        
        # الرسوم والعمولات
        fees_group = QGroupBox("الرسوم والعمولات")
        fees_group.setStyleSheet(basic_group.styleSheet())
        fees_layout = QGridLayout(fees_group)
        fees_layout.setSpacing(10)
        
        # رسوم التحويل
        fees_layout.addWidget(QLabel("رسوم التحويل (ريال):"), 0, 0)
        self.transfer_fee_input = QDoubleSpinBox()
        self.transfer_fee_input.setRange(0, 9999.99)
        self.transfer_fee_input.setDecimals(2)
        self.transfer_fee_input.setSuffix(" ريال")
        self.transfer_fee_input.setMinimumHeight(35)
        fees_layout.addWidget(self.transfer_fee_input, 0, 1)

        # نسبة العمولة
        fees_layout.addWidget(QLabel("نسبة العمولة (%):"), 0, 2)
        self.commission_rate_input = QDoubleSpinBox()
        self.commission_rate_input.setRange(0, 100)
        self.commission_rate_input.setDecimals(2)
        self.commission_rate_input.setSuffix(" %")
        self.commission_rate_input.setMinimumHeight(35)
        fees_layout.addWidget(self.commission_rate_input, 0, 3)
        
        layout.addWidget(fees_group)
        
        # معلومات إضافية
        extra_group = QGroupBox("معلومات إضافية")
        extra_group.setStyleSheet(basic_group.styleSheet())
        extra_layout = QGridLayout(extra_group)
        extra_layout.setSpacing(10)
        
        # الملاحظات
        extra_layout.addWidget(QLabel("ملاحظات:"), 0, 0)
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setMinimumHeight(80)
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        extra_layout.addWidget(self.notes_input, 0, 1, 1, 3)

        # حالة الصراف
        self.is_active_checkbox = QCheckBox("الصراف نشط")
        self.is_active_checkbox.setChecked(True)
        self.is_active_checkbox.setMinimumHeight(30)
        extra_layout.addWidget(self.is_active_checkbox, 1, 0, 1, 2)
        
        layout.addWidget(extra_group)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # أزرار الإجراءات
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.addStretch()
        
        self.save_btn = QPushButton("💾 حفظ التعديلات")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_exchange)
        self.cancel_btn.clicked.connect(self.reject)
        
        # تحويل رمز الصراف للأحرف الكبيرة
        self.exchange_code_input.textChanged.connect(
            lambda: self.exchange_code_input.setText(self.exchange_code_input.text().upper())
        )

    def load_exchange_data(self):
        """تحميل بيانات الصراف للتعديل"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("""
                SELECT name, name_en, code, category, license_number, phone, mobile,
                       email, address, website, transfer_fee, commission_rate, notes, is_active
                FROM exchanges WHERE id = ?
            """, (self.exchange_id,))

            exchange_data = cursor.fetchone()
            conn.close()

            if exchange_data:
                self.exchange_name_input.setText(exchange_data[0] or "")
                self.exchange_name_en_input.setText(exchange_data[1] or "")
                self.exchange_code_input.setText(exchange_data[2] or "")

                # تعيين فئة الصراف
                if exchange_data[3]:
                    index = self.category_combo.findText(exchange_data[3])
                    if index >= 0:
                        self.category_combo.setCurrentIndex(index)

                self.license_number_input.setText(exchange_data[4] or "")
                self.phone_input.setText(exchange_data[5] or "")
                self.mobile_input.setText(exchange_data[6] or "")
                self.email_input.setText(exchange_data[7] or "")
                self.address_input.setPlainText(exchange_data[8] or "")
                self.website_input.setText(exchange_data[9] or "")

                # تعيين الرسوم والعمولات
                if exchange_data[10] is not None:
                    self.transfer_fee_input.setValue(float(exchange_data[10]))
                if exchange_data[11] is not None:
                    self.commission_rate_input.setValue(float(exchange_data[11]))

                self.notes_input.setPlainText(exchange_data[12] or "")
                self.is_active_checkbox.setChecked(bool(exchange_data[13]))
            else:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على بيانات الصراف")
                self.reject()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الصراف:\n{str(e)}")
            self.reject()

    def validate_form(self):
        """التحقق من صحة النموذج"""
        if not self.exchange_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم الصراف")
            self.exchange_name_input.setFocus()
            return False

        return True

    def save_exchange(self):
        """حفظ تعديلات الصراف"""
        if not self.validate_form():
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.save_btn.setEnabled(False)

            # جمع البيانات
            exchange_data = {
                'name': self.exchange_name_input.text().strip(),
                'name_en': self.exchange_name_en_input.text().strip() or None,
                'code': self.exchange_code_input.text().strip() or None,
                'category': self.category_combo.currentText(),
                'license_number': self.license_number_input.text().strip() or None,
                'phone': self.phone_input.text().strip() or None,
                'mobile': self.mobile_input.text().strip() or None,
                'email': self.email_input.text().strip() or None,
                'address': self.address_input.toPlainText().strip() or None,
                'website': self.website_input.text().strip() or None,
                'transfer_fee': self.transfer_fee_input.value(),
                'commission_rate': self.commission_rate_input.value(),
                'notes': self.notes_input.toPlainText().strip() or None,
                'is_active': self.is_active_checkbox.isChecked(),
                'updated_at': datetime.now().isoformat()
            }

            # حفظ في قاعدة البيانات
            success = self.update_exchange_in_database(exchange_data)

            if success:
                QMessageBox.information(self, "نجح الحفظ",
                                      f"تم تحديث الصراف '{exchange_data['name']}' بنجاح")

                # إرسال إشارة
                self.exchange_updated.emit(self.exchange_id)
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في تحديث الصراف")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ التعديلات:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.save_btn.setEnabled(True)

    def update_exchange_in_database(self, data):
        """تحديث الصراف في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحديث الصراف
            cursor.execute("""
                UPDATE exchanges SET
                    name = ?, name_en = ?, code = ?, category = ?, license_number = ?,
                    phone = ?, mobile = ?, email = ?, address = ?, website = ?,
                    transfer_fee = ?, commission_rate = ?, notes = ?, is_active = ?, updated_at = ?
                WHERE id = ?
            """, (
                data['name'], data['name_en'], data['code'], data['category'],
                data['license_number'], data['phone'], data['mobile'], data['email'],
                data['address'], data['website'], data['transfer_fee'], data['commission_rate'],
                data['notes'], data['is_active'], data['updated_at'], self.exchange_id
            ))

            conn.commit()
            conn.close()

            return True

        except sqlite3.IntegrityError as e:
            if "UNIQUE constraint failed: exchanges.code" in str(e):
                QMessageBox.warning(self, "خطأ", "رمز الصراف موجود بالفعل. يرجى اختيار رمز آخر.")
            else:
                QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {e}")
            return False
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات: {e}")
            return False
