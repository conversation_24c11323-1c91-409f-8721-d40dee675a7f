#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تتبع الشحنات المتقدمة
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QMessageBox, QHeaderView, QAbstractItemView, QComboBox,
                               QGroupBox, QFormLayout, QDateEdit, QToolBar, QStatusBar,
                               QTabWidget, QTextEdit, QSplitter, QFrame, QGridLayout,
                               QProgressBar, QCheckBox, QSpinBox, QDoubleSpinBox,
                               QScrollArea, QDialog, QDialogButtonBox, QFileDialog, QInputDialog)
from PySide6.QtCore import Qt, QDate, Signal, QTimer
from PySide6.QtGui import QAction, QIcon, QFont, QPalette, QColor

try:
    from ...database.database_manager import DatabaseManager
    from ...database.models import Shipment, Supplier, Container, ShipmentItem
except ImportError:
    # للاختبار المباشر
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from database.database_manager import DatabaseManager
    from database.models import Shipment, Supplier, Container, ShipmentItem
try:
    from ...utils.formatters import format_date, format_datetime, format_number, format_currency
except ImportError:
    # للاختبار المباشر
    from utils.formatters import format_date, format_datetime, format_number, format_currency

class AdvancedShipmentTrackingWindow(QMainWindow):
    """نافذة تتبع الشحنات المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)

        try:
            self.db_manager = DatabaseManager()

            # إعداد timer للتحديث التلقائي
            self.auto_refresh_timer = QTimer()
            self.auto_refresh_timer.timeout.connect(self.auto_refresh_data)

            # إعداد timer للبحث الفوري
            self.search_timer = QTimer()
            self.search_timer.setSingleShot(True)
            self.search_timer.timeout.connect(self.perform_instant_search)

            # إعداد timer للبحث السريع
            self.quick_search_timer = QTimer()
            self.quick_search_timer.setSingleShot(True)
            self.quick_search_timer.timeout.connect(self.perform_quick_search)

            self.setup_ui()
            self.setup_toolbar()
            self.setup_connections()

            # تحميل البيانات في النهاية
            QTimer.singleShot(100, self.load_tracking_data)  # تأخير بسيط لضمان اكتمال الواجهة

            # بدء التحديث التلقائي كل 5 دقائق
            self.auto_refresh_timer.start(300000)  # 5 دقائق

        except Exception as e:
            print(f"خطأ في إنشاء نافذة التتبع المتقدمة: {e}")
            QMessageBox.critical(None, "خطأ", f"فشل في إنشاء نافذة التتبع المتقدمة:\n{str(e)}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تتبع الشحنات المتقدم - ProShipment")
        
        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # لوحة الإحصائيات السريعة
        stats_frame = self.create_stats_dashboard()
        main_layout.addWidget(stats_frame, 0)  # مساحة ثابتة
        
        # منطقة البحث والفلترة المتقدمة
        search_frame = self.create_advanced_search_panel()
        main_layout.addWidget(search_frame, 0)  # مساحة ثابتة
        
        # التبويبات الرئيسية
        self.tabs_widget = QTabWidget()
        
        # تبويب التتبع الرئيسي
        main_tracking_tab = self.create_main_tracking_tab()
        self.tabs_widget.addTab(main_tracking_tab, "🚢 التتبع الرئيسي")
        
        # تبويب التحليلات والإحصائيات
        analytics_tab = self.create_analytics_tab()
        self.tabs_widget.addTab(analytics_tab, "📊 التحليلات والإحصائيات")
        
        # تبويب الخريطة والمواقع
        map_tab = self.create_map_tab()
        self.tabs_widget.addTab(map_tab, "🗺️ الخريطة والمواقع")
        
        # تبويب التقارير المتقدمة
        reports_tab = self.create_reports_tab()
        self.tabs_widget.addTab(reports_tab, "📋 التقارير المتقدمة")
        
        # تبويب التنبيهات والإشعارات
        alerts_tab = self.create_alerts_tab()
        self.tabs_widget.addTab(alerts_tab, "🔔 التنبيهات والإشعارات")
        
        main_layout.addWidget(self.tabs_widget, 1)  # قابل للتمدد
        
        # شريط الحالة المتقدم
        self.setup_advanced_status_bar()
        
    def create_stats_dashboard(self):
        """إنشاء لوحة الإحصائيات السريعة"""
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.StyledPanel)
        stats_frame.setMaximumHeight(120)
        
        stats_layout = QGridLayout(stats_frame)
        
        # إحصائيات سريعة
        self.total_shipments_label = QLabel("0")
        self.active_shipments_label = QLabel("0")
        self.in_transit_label = QLabel("0")
        self.delivered_label = QLabel("0")
        self.delayed_label = QLabel("0")
        self.customs_pending_label = QLabel("0")
        
        # تنسيق الأرقام
        font = QFont()
        font.setPointSize(16)
        font.setBold(True)
        
        for label in [self.total_shipments_label, self.active_shipments_label, 
                     self.in_transit_label, self.delivered_label, 
                     self.delayed_label, self.customs_pending_label]:
            label.setFont(font)
            label.setAlignment(Qt.AlignCenter)
        
        # إضافة الإحصائيات إلى الشبكة
        stats_layout.addWidget(QLabel("إجمالي الشحنات"), 0, 0)
        stats_layout.addWidget(self.total_shipments_label, 1, 0)
        
        stats_layout.addWidget(QLabel("الشحنات النشطة"), 0, 1)
        stats_layout.addWidget(self.active_shipments_label, 1, 1)
        
        stats_layout.addWidget(QLabel("في الطريق"), 0, 2)
        stats_layout.addWidget(self.in_transit_label, 1, 2)
        
        stats_layout.addWidget(QLabel("تم التسليم"), 0, 3)
        stats_layout.addWidget(self.delivered_label, 1, 3)
        
        stats_layout.addWidget(QLabel("متأخرة"), 0, 4)
        stats_layout.addWidget(self.delayed_label, 1, 4)
        
        stats_layout.addWidget(QLabel("في انتظار الجمارك"), 0, 5)
        stats_layout.addWidget(self.customs_pending_label, 1, 5)
        
        return stats_frame
    
    def create_advanced_search_panel(self):
        """إنشاء لوحة البحث والفلترة المتقدمة"""
        search_frame = QGroupBox("البحث والفلترة")
        main_layout = QVBoxLayout(search_frame)

        # البحث السريع والفوري
        quick_search_layout = QHBoxLayout()
        quick_search_label = QLabel("البحث السريع:")
        quick_search_label.setMinimumWidth(100)
        quick_search_label.setStyleSheet("font-weight: bold; color: #007bff;")

        self.quick_search_input = QLineEdit()
        self.quick_search_input.setPlaceholderText("البحث السريع في جميع البيانات (رقم الشحنة، المورد، الحالة...)...")
        self.quick_search_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #007bff;
                border-radius: 8px;
                font-size: 13px;
                background-color: #f8f9fa;
            }
            QLineEdit:focus {
                border-color: #0056b3;
                background-color: white;
            }
        """)

        quick_search_layout.addWidget(quick_search_label)
        quick_search_layout.addWidget(self.quick_search_input)
        main_layout.addLayout(quick_search_layout)

        # خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator)

        # الفلاتر المتقدمة في صف واحد
        filters_label = QLabel("الفلاتر المتقدمة:")
        filters_label.setStyleSheet("font-weight: bold; color: #6c757d; margin-top: 10px;")
        main_layout.addWidget(filters_label)

        filters_layout = QHBoxLayout()

        # حقل البحث بالرقم
        self.search_shipment_number = QLineEdit()
        self.search_shipment_number.setPlaceholderText("رقم الشحنة...")
        filters_layout.addWidget(QLabel("رقم الشحنة:"))
        filters_layout.addWidget(self.search_shipment_number)

        # البحث بالمورد
        self.search_supplier = QComboBox()
        self.search_supplier.setEditable(True)
        self.search_supplier.setPlaceholderText("المورد...")
        filters_layout.addWidget(QLabel("المورد:"))
        filters_layout.addWidget(self.search_supplier)

        # البحث بالحالة
        self.search_status = QComboBox()
        self.search_status.addItems(["الكل", "جديد", "في الطريق", "وصل", "تم التسليم", "متأخر"])
        filters_layout.addWidget(QLabel("الحالة:"))
        filters_layout.addWidget(self.search_status)

        # فلترة بحالة الإفراج
        self.search_clearance_status = QComboBox()
        self.search_clearance_status.addItems(["الكل", "في انتظار الإفراج", "تم الإفراج", "مرفوض"])
        filters_layout.addWidget(QLabel("حالة الإفراج:"))
        filters_layout.addWidget(self.search_clearance_status)

        main_layout.addLayout(filters_layout)

        # تواريخ البحث في صف منفصل
        dates_layout = QHBoxLayout()

        self.search_date_from = QDateEdit()
        self.search_date_from.setDate(QDate.currentDate().addDays(-30))
        self.search_date_from.setCalendarPopup(True)
        dates_layout.addWidget(QLabel("من تاريخ:"))
        dates_layout.addWidget(self.search_date_from)

        self.search_date_to = QDateEdit()
        self.search_date_to.setDate(QDate.currentDate())
        self.search_date_to.setCalendarPopup(True)
        dates_layout.addWidget(QLabel("إلى تاريخ:"))
        dates_layout.addWidget(self.search_date_to)

        dates_layout.addStretch()  # مساحة فارغة
        main_layout.addLayout(dates_layout)

        # أزرار العمليات
        buttons_layout = QHBoxLayout()

        self.search_button = QPushButton("🔍 بحث متقدم")
        self.search_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        self.clear_search_button = QPushButton("🗑️ مسح الفلاتر")
        self.clear_search_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)

        self.export_button = QPushButton("📤 تصدير CSV")
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        buttons_layout.addWidget(self.search_button)
        buttons_layout.addWidget(self.clear_search_button)
        buttons_layout.addWidget(self.export_button)
        buttons_layout.addStretch()  # مساحة فارغة

        main_layout.addLayout(buttons_layout)

        return search_frame

    def create_main_tracking_tab(self):
        """إنشاء تبويب التتبع الرئيسي"""
        tab_widget = QWidget()
        tab_layout = QVBoxLayout(tab_widget)

        # جدول التتبع الرئيسي
        self.tracking_table = QTableWidget()
        self.tracking_table.setColumnCount(25)  # عدد أعمدة موسع
        self.tracking_table.setHorizontalHeaderLabels([
            "رقم الشحنة", "التاريخ", "المورد", "تفاصيل الأصناف", "الكمية",
            "عدد الحاويات", "رقم الحاوية", "حالة الشحنة", "حالة الإفراج",
            "بوليصة الشحن", "رقم التتبع", "رقم DHL", "شركة الشحن", "طريقة الشحن",
            "ميناء التحميل", "ميناء التفريغ", "ميناء الوصول", "الوجهة النهائية",
            "اسم السفينة", "رقم الرحلة", "تاريخ المغادرة", "تاريخ الوصول المتوقع",
            "الموقع الحالي", "نسبة الإنجاز", "الملاحظات"
        ])

        # إعداد الجدول
        header = self.tracking_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الشحنة
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # المورد
        header.resizeSection(2, 150)
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # تفاصيل الأصناف

        # باقي الأعمدة
        for i in range(4, 25):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

        self.tracking_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.tracking_table.setAlternatingRowColors(True)
        self.tracking_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.tracking_table.setSortingEnabled(True)

        tab_layout.addWidget(self.tracking_table)

        # لوحة تفاصيل الشحنة المحددة
        details_frame = self.create_shipment_details_panel()
        tab_layout.addWidget(details_frame)

        return tab_widget

    def create_shipment_details_panel(self):
        """إنشاء لوحة تفاصيل الشحنة"""
        details_frame = QGroupBox("تفاصيل الشحنة المحددة")
        details_frame.setMaximumHeight(200)
        details_layout = QHBoxLayout(details_frame)

        # معلومات أساسية
        basic_info_layout = QFormLayout()
        self.selected_shipment_number = QLabel("-")
        self.selected_supplier = QLabel("-")
        self.selected_status = QLabel("-")
        self.selected_clearance_status = QLabel("-")

        basic_info_layout.addRow("رقم الشحنة:", self.selected_shipment_number)
        basic_info_layout.addRow("المورد:", self.selected_supplier)
        basic_info_layout.addRow("حالة الشحنة:", self.selected_status)
        basic_info_layout.addRow("حالة الإفراج:", self.selected_clearance_status)

        # معلومات الشحن
        shipping_info_layout = QFormLayout()
        self.selected_tracking_number = QLabel("-")
        self.selected_shipping_company = QLabel("-")
        self.selected_departure_date = QLabel("-")
        self.selected_expected_arrival = QLabel("-")

        shipping_info_layout.addRow("رقم التتبع:", self.selected_tracking_number)
        shipping_info_layout.addRow("شركة الشحن:", self.selected_shipping_company)
        shipping_info_layout.addRow("تاريخ المغادرة:", self.selected_departure_date)
        shipping_info_layout.addRow("الوصول المتوقع:", self.selected_expected_arrival)

        # شريط التقدم
        progress_layout = QVBoxLayout()
        self.shipment_progress = QProgressBar()
        self.shipment_progress.setMaximum(100)
        self.shipment_progress_label = QLabel("0%")

        progress_layout.addWidget(QLabel("نسبة الإنجاز:"))
        progress_layout.addWidget(self.shipment_progress)
        progress_layout.addWidget(self.shipment_progress_label)
        progress_layout.addStretch()

        details_layout.addLayout(basic_info_layout)
        details_layout.addLayout(shipping_info_layout)
        details_layout.addLayout(progress_layout)

        return details_frame

    def create_analytics_tab(self):
        """إنشاء تبويب التحليلات والإحصائيات"""
        tab_widget = QWidget()
        main_layout = QVBoxLayout(tab_widget)

        # شريط أدوات التحليلات
        toolbar_layout = QHBoxLayout()

        # فترة التحليل
        period_label = QLabel("فترة التحليل:")
        self.analytics_period = QComboBox()
        self.analytics_period.addItems(["آخر 7 أيام", "آخر 30 يوم", "آخر 3 أشهر", "آخر 6 أشهر", "آخر سنة", "الكل"])
        self.analytics_period.setCurrentText("آخر 30 يوم")
        self.analytics_period.currentTextChanged.connect(self.update_analytics)

        # زر تحديث
        refresh_analytics_btn = QPushButton("🔄 تحديث التحليلات")
        refresh_analytics_btn.clicked.connect(self.update_analytics)

        # زر تصدير التحليلات
        export_analytics_btn = QPushButton("📊 تصدير التحليلات")
        export_analytics_btn.clicked.connect(self.export_analytics)

        toolbar_layout.addWidget(period_label)
        toolbar_layout.addWidget(self.analytics_period)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(refresh_analytics_btn)
        toolbar_layout.addWidget(export_analytics_btn)

        main_layout.addLayout(toolbar_layout)

        # المحتوى الرئيسي
        content_scroll = QScrollArea()
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # الإحصائيات العامة
        general_stats_frame = self.create_general_statistics_section()
        content_layout.addWidget(general_stats_frame)

        # إحصائيات الحالات
        status_stats_frame = self.create_status_statistics_section()
        content_layout.addWidget(status_stats_frame)

        # إحصائيات الموردين
        supplier_stats_frame = self.create_supplier_statistics_section()
        content_layout.addWidget(supplier_stats_frame)

        # إحصائيات مالية
        financial_stats_frame = self.create_financial_statistics_section()
        content_layout.addWidget(financial_stats_frame)

        # إحصائيات الأداء
        performance_stats_frame = self.create_performance_statistics_section()
        content_layout.addWidget(performance_stats_frame)

        content_scroll.setWidget(content_widget)
        content_scroll.setWidgetResizable(True)
        main_layout.addWidget(content_scroll)

        # تحديث البيانات عند الإنشاء
        QTimer.singleShot(100, self.update_analytics)

        return tab_widget

    def create_general_statistics_section(self):
        """إنشاء قسم الإحصائيات العامة"""
        frame = QGroupBox("📊 الإحصائيات العامة")
        layout = QGridLayout(frame)

        # بطاقات الإحصائيات
        self.total_shipments_analytics = self.create_stat_card("إجمالي الشحنات", "0", "#3498db")
        self.active_shipments_analytics = self.create_stat_card("الشحنات النشطة", "0", "#2ecc71")
        self.completed_shipments_analytics = self.create_stat_card("الشحنات المكتملة", "0", "#27ae60")
        self.delayed_shipments_analytics = self.create_stat_card("الشحنات المتأخرة", "0", "#e74c3c")

        layout.addWidget(self.total_shipments_analytics, 0, 0)
        layout.addWidget(self.active_shipments_analytics, 0, 1)
        layout.addWidget(self.completed_shipments_analytics, 0, 2)
        layout.addWidget(self.delayed_shipments_analytics, 0, 3)

        return frame

    def create_status_statistics_section(self):
        """إنشاء قسم إحصائيات الحالات"""
        frame = QGroupBox("📈 توزيع حالات الشحنات")
        layout = QVBoxLayout(frame)

        # جدول الحالات
        self.status_table = QTableWidget()
        self.status_table.setColumnCount(3)
        self.status_table.setHorizontalHeaderLabels(["الحالة", "العدد", "النسبة المئوية"])
        self.status_table.horizontalHeader().setStretchLastSection(True)
        self.status_table.setAlternatingRowColors(True)
        self.status_table.setMaximumHeight(200)

        layout.addWidget(self.status_table)

        return frame

    def create_supplier_statistics_section(self):
        """إنشاء قسم إحصائيات الموردين"""
        frame = QGroupBox("🏢 إحصائيات الموردين")
        layout = QVBoxLayout(frame)

        # جدول الموردين
        self.supplier_table = QTableWidget()
        self.supplier_table.setColumnCount(4)
        self.supplier_table.setHorizontalHeaderLabels(["المورد", "عدد الشحنات", "إجمالي القيمة", "متوسط القيمة"])
        self.supplier_table.horizontalHeader().setStretchLastSection(True)
        self.supplier_table.setAlternatingRowColors(True)
        self.supplier_table.setMaximumHeight(250)

        layout.addWidget(self.supplier_table)

        return frame

    def create_financial_statistics_section(self):
        """إنشاء قسم الإحصائيات المالية"""
        frame = QGroupBox("💰 الإحصائيات المالية")
        layout = QGridLayout(frame)

        # بطاقات مالية
        self.total_value_card = self.create_stat_card("إجمالي القيمة", "0", "#f39c12")
        self.avg_value_card = self.create_stat_card("متوسط قيمة الشحنة", "0", "#e67e22")
        self.insurance_total_card = self.create_stat_card("إجمالي التأمين", "0", "#9b59b6")
        self.freight_total_card = self.create_stat_card("إجمالي الشحن", "0", "#8e44ad")

        layout.addWidget(self.total_value_card, 0, 0)
        layout.addWidget(self.avg_value_card, 0, 1)
        layout.addWidget(self.insurance_total_card, 0, 2)
        layout.addWidget(self.freight_total_card, 0, 3)

        return frame

    def create_performance_statistics_section(self):
        """إنشاء قسم إحصائيات الأداء"""
        frame = QGroupBox("⚡ إحصائيات الأداء")
        layout = QVBoxLayout(frame)

        # مؤشرات الأداء
        performance_layout = QGridLayout()

        self.on_time_delivery_card = self.create_stat_card("التسليم في الوقت", "0%", "#2ecc71")
        self.avg_delivery_time_card = self.create_stat_card("متوسط وقت التسليم", "0 يوم", "#3498db")
        self.customs_clearance_rate_card = self.create_stat_card("معدل الإفراج الجمركي", "0%", "#f39c12")
        self.shipment_success_rate_card = self.create_stat_card("معدل نجاح الشحنات", "0%", "#27ae60")

        performance_layout.addWidget(self.on_time_delivery_card, 0, 0)
        performance_layout.addWidget(self.avg_delivery_time_card, 0, 1)
        performance_layout.addWidget(self.customs_clearance_rate_card, 0, 2)
        performance_layout.addWidget(self.shipment_success_rate_card, 0, 3)

        layout.addLayout(performance_layout)

        return frame

    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setStyleSheet(f"""
            QFrame {{
                border: 2px solid {color};
                border-radius: 10px;
                background-color: white;
                padding: 10px;
            }}
        """)

        layout = QVBoxLayout(card)

        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 12px; color: #666; font-weight: bold;")

        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"font-size: 24px; color: {color}; font-weight: bold;")

        layout.addWidget(title_label)
        layout.addWidget(value_label)

        # حفظ مرجع للقيمة للتحديث لاحقاً
        card.value_label = value_label

        return card

    def create_map_tab(self):
        """إنشاء تبويب الخريطة والمواقع"""
        tab_widget = QWidget()
        main_layout = QVBoxLayout(tab_widget)

        # شريط أدوات الخريطة
        toolbar_layout = QHBoxLayout()

        # فلتر الشحنات
        filter_label = QLabel("عرض الشحنات:")
        self.map_filter = QComboBox()
        self.map_filter.addItems(["جميع الشحنات", "الشحنات النشطة", "في الطريق", "وصلت الميناء", "تم التسليم"])
        self.map_filter.currentTextChanged.connect(self.update_map_data)

        # زر تحديث الخريطة
        refresh_map_btn = QPushButton("🔄 تحديث الخريطة")
        refresh_map_btn.clicked.connect(self.update_map_data)

        # زر عرض المسارات
        show_routes_btn = QPushButton("🗺️ عرض المسارات")
        show_routes_btn.clicked.connect(self.show_shipping_routes)

        toolbar_layout.addWidget(filter_label)
        toolbar_layout.addWidget(self.map_filter)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(refresh_map_btn)
        toolbar_layout.addWidget(show_routes_btn)

        main_layout.addLayout(toolbar_layout)

        # المحتوى الرئيسي - تقسيم أفقي
        content_splitter = QSplitter(Qt.Horizontal)

        # قائمة الشحنات على الخريطة
        shipments_list_frame = QGroupBox("📍 الشحنات على الخريطة")
        shipments_list_layout = QVBoxLayout(shipments_list_frame)

        # جدول الشحنات المصغر
        self.map_shipments_table = QTableWidget()
        self.map_shipments_table.setColumnCount(4)
        self.map_shipments_table.setHorizontalHeaderLabels(["رقم الشحنة", "المورد", "الموقع الحالي", "الحالة"])
        self.map_shipments_table.horizontalHeader().setStretchLastSection(True)
        self.map_shipments_table.setAlternatingRowColors(True)
        self.map_shipments_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.map_shipments_table.itemSelectionChanged.connect(self.on_map_shipment_selected)

        shipments_list_layout.addWidget(self.map_shipments_table)

        # منطقة الخريطة
        map_frame = QGroupBox("🌍 خريطة تتبع الشحنات")
        map_layout = QVBoxLayout(map_frame)

        # منطقة عرض الخريطة (محاكاة)
        self.map_display = QTextEdit()
        self.map_display.setReadOnly(True)
        self.map_display.setStyleSheet("""
            QTextEdit {
                background-color: #f0f8ff;
                border: 2px solid #4682b4;
                border-radius: 10px;
                font-family: 'Segoe UI', Arial;
                font-size: 12px;
            }
        """)

        # معلومات الشحنة المحددة
        selected_shipment_info = QGroupBox("📋 معلومات الشحنة المحددة")
        selected_info_layout = QVBoxLayout(selected_shipment_info)

        self.selected_map_shipment_info = QLabel("اختر شحنة من القائمة لعرض تفاصيلها")
        self.selected_map_shipment_info.setWordWrap(True)
        self.selected_map_shipment_info.setStyleSheet("padding: 10px; background-color: #f9f9f9; border-radius: 5px;")

        selected_info_layout.addWidget(self.selected_map_shipment_info)

        map_layout.addWidget(self.map_display)
        map_layout.addWidget(selected_shipment_info)

        # إضافة الأجزاء للمقسم
        content_splitter.addWidget(shipments_list_frame)
        content_splitter.addWidget(map_frame)
        content_splitter.setSizes([300, 700])  # تحديد النسب

        main_layout.addWidget(content_splitter)

        # تحديث البيانات عند الإنشاء
        QTimer.singleShot(100, self.update_map_data)

        return tab_widget

    def create_reports_tab(self):
        """إنشاء تبويب التقارير المتقدمة"""
        tab_widget = QWidget()
        main_layout = QVBoxLayout(tab_widget)

        # شريط أدوات التقارير
        toolbar_layout = QHBoxLayout()

        # نوع التقرير
        report_type_label = QLabel("نوع التقرير:")
        self.report_type = QComboBox()
        self.report_type.addItems([
            "تقرير شامل للشحنات",
            "تقرير الشحنات حسب المورد",
            "تقرير الشحنات حسب الحالة",
            "تقرير مالي مفصل",
            "تقرير الأداء والإحصائيات",
            "تقرير الشحنات المتأخرة",
            "تقرير الإفراج الجمركي"
        ])
        self.report_type.currentTextChanged.connect(self.update_report_preview)

        # فترة التقرير
        period_label = QLabel("الفترة:")
        self.report_period = QComboBox()
        self.report_period.addItems(["آخر 7 أيام", "آخر 30 يوم", "آخر 3 أشهر", "آخر 6 أشهر", "آخر سنة", "الكل"])
        self.report_period.setCurrentText("آخر 30 يوم")
        self.report_period.currentTextChanged.connect(self.update_report_preview)

        toolbar_layout.addWidget(report_type_label)
        toolbar_layout.addWidget(self.report_type)
        toolbar_layout.addWidget(period_label)
        toolbar_layout.addWidget(self.report_period)
        toolbar_layout.addStretch()

        main_layout.addLayout(toolbar_layout)

        # أزرار العمليات
        buttons_layout = QHBoxLayout()

        generate_report_btn = QPushButton("📊 إنشاء التقرير")
        generate_report_btn.clicked.connect(self.generate_report)

        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.clicked.connect(self.export_report_pdf)

        export_excel_btn = QPushButton("📈 تصدير Excel")
        export_excel_btn.clicked.connect(self.export_report_excel)

        print_report_btn = QPushButton("🖨️ طباعة")
        print_report_btn.clicked.connect(self.print_report)

        buttons_layout.addWidget(generate_report_btn)
        buttons_layout.addWidget(export_pdf_btn)
        buttons_layout.addWidget(export_excel_btn)
        buttons_layout.addWidget(print_report_btn)
        buttons_layout.addStretch()

        main_layout.addLayout(buttons_layout)

        # منطقة معاينة التقرير
        preview_frame = QGroupBox("📋 معاينة التقرير")
        preview_layout = QVBoxLayout(preview_frame)

        self.report_preview = QTextEdit()
        self.report_preview.setReadOnly(True)
        self.report_preview.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-family: 'Segoe UI', Arial;
                font-size: 11px;
                line-height: 1.4;
            }
        """)

        preview_layout.addWidget(self.report_preview)
        main_layout.addWidget(preview_frame)

        # إعدادات التقرير
        settings_frame = QGroupBox("⚙️ إعدادات التقرير")
        settings_layout = QGridLayout(settings_frame)

        # خيارات التضمين
        self.include_financial = QCheckBox("تضمين البيانات المالية")
        self.include_financial.setChecked(True)

        self.include_containers = QCheckBox("تضمين معلومات الحاويات")
        self.include_containers.setChecked(True)

        self.include_items = QCheckBox("تضمين تفاصيل الأصناف")
        self.include_items.setChecked(False)

        self.include_charts = QCheckBox("تضمين الرسوم البيانية")
        self.include_charts.setChecked(True)

        settings_layout.addWidget(self.include_financial, 0, 0)
        settings_layout.addWidget(self.include_containers, 0, 1)
        settings_layout.addWidget(self.include_items, 1, 0)
        settings_layout.addWidget(self.include_charts, 1, 1)

        main_layout.addWidget(settings_frame)

        # تحديث المعاينة عند الإنشاء
        QTimer.singleShot(100, self.update_report_preview)

        return tab_widget

    def create_alerts_tab(self):
        """إنشاء تبويب التنبيهات والإشعارات"""
        tab_widget = QWidget()
        main_layout = QVBoxLayout(tab_widget)

        # شريط أدوات التنبيهات
        toolbar_layout = QHBoxLayout()

        # فلتر التنبيهات
        filter_label = QLabel("عرض التنبيهات:")
        self.alerts_filter = QComboBox()
        self.alerts_filter.addItems(["جميع التنبيهات", "التنبيهات النشطة", "تنبيهات التأخير", "تنبيهات الوصول", "تنبيهات مالية"])
        self.alerts_filter.currentTextChanged.connect(self.update_alerts_list)

        # زر إضافة تنبيه
        add_alert_btn = QPushButton("➕ إضافة تنبيه")
        add_alert_btn.clicked.connect(self.add_new_alert)

        # زر إعدادات التنبيهات
        settings_btn = QPushButton("⚙️ إعدادات التنبيهات")
        settings_btn.clicked.connect(self.show_alert_settings)

        toolbar_layout.addWidget(filter_label)
        toolbar_layout.addWidget(self.alerts_filter)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(add_alert_btn)
        toolbar_layout.addWidget(settings_btn)

        main_layout.addLayout(toolbar_layout)

        # المحتوى الرئيسي - تقسيم أفقي
        content_splitter = QSplitter(Qt.Horizontal)

        # قائمة التنبيهات
        alerts_list_frame = QGroupBox("🔔 قائمة التنبيهات")
        alerts_list_layout = QVBoxLayout(alerts_list_frame)

        # جدول التنبيهات
        self.alerts_table = QTableWidget()
        self.alerts_table.setColumnCount(5)
        self.alerts_table.setHorizontalHeaderLabels(["النوع", "العنوان", "الحالة", "التاريخ", "الأولوية"])
        self.alerts_table.horizontalHeader().setStretchLastSection(True)
        self.alerts_table.setAlternatingRowColors(True)
        self.alerts_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.alerts_table.itemSelectionChanged.connect(self.on_alert_selected)

        # إضافة قائمة سياق للتنبيهات
        self.alerts_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.alerts_table.customContextMenuRequested.connect(self.show_alert_context_menu)

        alerts_list_layout.addWidget(self.alerts_table)

        # تفاصيل التنبيه المحدد
        alert_details_frame = QGroupBox("📋 تفاصيل التنبيه")
        alert_details_layout = QVBoxLayout(alert_details_frame)

        self.alert_details_text = QTextEdit()
        self.alert_details_text.setReadOnly(True)
        self.alert_details_text.setMaximumHeight(200)
        self.alert_details_text.setStyleSheet("""
            QTextEdit {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-family: 'Segoe UI', Arial;
                font-size: 11px;
            }
        """)

        # أزرار العمليات
        alert_actions_layout = QHBoxLayout()

        mark_read_btn = QPushButton("✅ تحديد كمقروء")
        mark_read_btn.clicked.connect(self.mark_alert_as_read)

        delete_alert_btn = QPushButton("🗑️ حذف التنبيه")
        delete_alert_btn.clicked.connect(self.delete_selected_alert)

        snooze_alert_btn = QPushButton("⏰ تأجيل التنبيه")
        snooze_alert_btn.clicked.connect(self.snooze_alert)

        alert_actions_layout.addWidget(mark_read_btn)
        alert_actions_layout.addWidget(delete_alert_btn)
        alert_actions_layout.addWidget(snooze_alert_btn)
        alert_actions_layout.addStretch()

        alert_details_layout.addWidget(self.alert_details_text)
        alert_details_layout.addLayout(alert_actions_layout)

        # إحصائيات التنبيهات
        stats_frame = QGroupBox("📊 إحصائيات التنبيهات")
        stats_layout = QGridLayout(stats_frame)

        # بطاقات الإحصائيات
        self.total_alerts_card = self.create_stat_card("إجمالي التنبيهات", "0", "#3498db")
        self.active_alerts_card = self.create_stat_card("التنبيهات النشطة", "0", "#e74c3c")
        self.read_alerts_card = self.create_stat_card("التنبيهات المقروءة", "0", "#27ae60")
        self.high_priority_card = self.create_stat_card("عالية الأولوية", "0", "#f39c12")

        stats_layout.addWidget(self.total_alerts_card, 0, 0)
        stats_layout.addWidget(self.active_alerts_card, 0, 1)
        stats_layout.addWidget(self.read_alerts_card, 1, 0)
        stats_layout.addWidget(self.high_priority_card, 1, 1)

        alert_details_layout.addWidget(stats_frame)

        # إضافة الأجزاء للمقسم
        content_splitter.addWidget(alerts_list_frame)
        content_splitter.addWidget(alert_details_frame)
        content_splitter.setSizes([600, 400])

        main_layout.addWidget(content_splitter)

        # تحديث البيانات عند الإنشاء
        QTimer.singleShot(100, self.load_sample_alerts)

        return tab_widget

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = QToolBar("أدوات التتبع المتقدم")
        self.addToolBar(toolbar)

        # تحديث
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setStatusTip("تحديث بيانات التتبع")
        refresh_action.triggered.connect(self.load_tracking_data)
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        # تحديث تلقائي
        auto_refresh_action = QAction("⏰ تحديث تلقائي", self)
        auto_refresh_action.setCheckable(True)
        auto_refresh_action.setChecked(True)
        auto_refresh_action.setStatusTip("تفعيل/إلغاء التحديث التلقائي")
        auto_refresh_action.triggered.connect(self.toggle_auto_refresh)
        toolbar.addAction(auto_refresh_action)

        toolbar.addSeparator()

        # تصدير
        export_action = QAction("📤 تصدير", self)
        export_action.setStatusTip("تصدير بيانات التتبع")
        export_action.triggered.connect(self.export_tracking_data)
        toolbar.addAction(export_action)

        toolbar.addSeparator()

        # إعدادات التتبع
        settings_action = QAction("⚙️ إعدادات", self)
        settings_action.setStatusTip("إعدادات التتبع")
        settings_action.triggered.connect(self.show_tracking_settings)
        toolbar.addAction(settings_action)

    def setup_connections(self):
        """إعداد الاتصالات"""
        # البحث السريع والفوري
        self.quick_search_input.textChanged.connect(self.start_quick_search_timer)

        # البحث المتقدم
        self.search_shipment_number.textChanged.connect(self.start_search_timer)
        self.search_supplier.currentTextChanged.connect(self.start_search_timer)
        self.search_status.currentTextChanged.connect(self.start_search_timer)
        self.search_clearance_status.currentTextChanged.connect(self.start_search_timer)

        # أزرار البحث
        self.search_button.clicked.connect(self.perform_search)
        self.clear_search_button.clicked.connect(self.clear_search)
        self.export_button.clicked.connect(self.export_tracking_data)

        # تحديد الشحنة في الجدول
        self.tracking_table.itemSelectionChanged.connect(self.update_shipment_details)

    def setup_advanced_status_bar(self):
        """إعداد شريط الحالة المتقدم"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # معلومات الحالة
        self.status_label = QLabel("جاهز")
        self.last_update_label = QLabel("آخر تحديث: -")
        self.connection_status_label = QLabel("🟢 متصل")

        self.status_bar.addWidget(self.status_label)
        self.status_bar.addPermanentWidget(self.last_update_label)
        self.status_bar.addPermanentWidget(self.connection_status_label)

    def load_tracking_data(self):
        """تحميل بيانات التتبع"""
        self.status_label.setText("جاري تحميل البيانات...")

        session = self.db_manager.get_session()
        try:
            # تحميل الشحنات مع البيانات المرتبطة
            shipments = session.query(Shipment).outerjoin(Supplier).filter(
                Shipment.is_active == True
            ).order_by(Shipment.created_at.desc()).all()



            # تحميل بيانات الحاويات والأصناف لكل شحنة
            for shipment in shipments:
                try:
                    shipment.containers = session.query(Container).filter(
                        Container.shipment_id == shipment.id
                    ).all()
                    shipment.items = session.query(ShipmentItem).filter(
                        ShipmentItem.shipment_id == shipment.id
                    ).all()
                except Exception:
                    # في حالة عدم وجود بيانات، تعيين قوائم فارغة
                    shipment.containers = []
                    shipment.items = []

            # ملء الجدول
            self.populate_tracking_table(shipments)

            self.update_statistics(shipments)
            self.load_suppliers_for_search()

            # تحديث شريط الحالة
            current_time = QDate.currentDate().toString('yyyy-MM-dd')
            self.last_update_label.setText(f"آخر تحديث: {current_time}")
            self.status_label.setText(f"تم تحميل {len(shipments)} شحنة")

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")  # للتشخيص
            import traceback
            traceback.print_exc()  # طباعة التفاصيل الكاملة للخطأ
            self.status_label.setText("خطأ في التحميل")
            # إنشاء جدول فارغ
            self.tracking_table.setRowCount(0)
            # إعادة تعيين الإحصائيات
            for label in [self.total_shipments_label, self.active_shipments_label,
                         self.in_transit_label, self.delivered_label,
                         self.delayed_label, self.customs_pending_label]:
                label.setText("0")
        finally:
            session.close()

    def populate_tracking_table(self, shipments):
        """ملء جدول التتبع"""
        if not shipments:
            self.tracking_table.setRowCount(0)
            return

        self.tracking_table.setRowCount(len(shipments))

        for row, shipment in enumerate(shipments):
            try:

                # رقم الشحنة
                shipment_item = QTableWidgetItem(shipment.shipment_number or "")
                shipment_item.setData(Qt.UserRole, shipment.id)
                self.tracking_table.setItem(row, 0, shipment_item)

                # التاريخ
                date_str = format_date(shipment.shipment_date) if shipment.shipment_date else ""
                self.tracking_table.setItem(row, 1, QTableWidgetItem(date_str))

                # المورد
                supplier_name = shipment.supplier.name if shipment.supplier else "غير محدد"
                self.tracking_table.setItem(row, 2, QTableWidgetItem(supplier_name))

                # تفاصيل الأصناف
                items_details = self.get_items_summary(getattr(shipment, 'items', []))
                self.tracking_table.setItem(row, 3, QTableWidgetItem(items_details))

                # الكمية الإجمالية
                items = getattr(shipment, 'items', [])
                total_quantity = sum(getattr(item, 'quantity', 0) for item in items)
                self.tracking_table.setItem(row, 4, QTableWidgetItem(str(total_quantity)))

                # عدد الحاويات
                containers = getattr(shipment, 'containers', [])
                containers_count = len(containers)
                self.tracking_table.setItem(row, 5, QTableWidgetItem(str(containers_count)))

                # أرقام الحاويات
                container_numbers = ", ".join([c.container_number for c in containers if getattr(c, 'container_number', None)])
                self.tracking_table.setItem(row, 6, QTableWidgetItem(container_numbers))

                # حالة الشحنة
                self.tracking_table.setItem(row, 7, QTableWidgetItem(shipment.shipment_status or ""))

                # حالة الإفراج
                clearance_status = shipment.clearance_status or "بدون الافراج"
                self.tracking_table.setItem(row, 8, QTableWidgetItem(clearance_status))

                # باقي البيانات
                self.tracking_table.setItem(row, 9, QTableWidgetItem(shipment.bill_of_lading or ""))
                self.tracking_table.setItem(row, 10, QTableWidgetItem(shipment.tracking_number or ""))
                self.tracking_table.setItem(row, 11, QTableWidgetItem(shipment.dhl_number or ""))
                self.tracking_table.setItem(row, 12, QTableWidgetItem(shipment.shipping_company or ""))
                self.tracking_table.setItem(row, 13, QTableWidgetItem(shipment.shipping_method or ""))
                self.tracking_table.setItem(row, 14, QTableWidgetItem(shipment.port_of_loading or ""))
                self.tracking_table.setItem(row, 15, QTableWidgetItem(shipment.port_of_discharge or ""))
                self.tracking_table.setItem(row, 16, QTableWidgetItem(shipment.port_of_arrival or ""))
                self.tracking_table.setItem(row, 17, QTableWidgetItem(shipment.final_destination or ""))
                self.tracking_table.setItem(row, 18, QTableWidgetItem(shipment.vessel_name or ""))
                self.tracking_table.setItem(row, 19, QTableWidgetItem(shipment.voyage_number or ""))

                # التواريخ
                departure_date = format_date(shipment.actual_departure_date) if shipment.actual_departure_date else ""
                self.tracking_table.setItem(row, 20, QTableWidgetItem(departure_date))

                expected_arrival = format_date(shipment.estimated_arrival_date) if shipment.estimated_arrival_date else ""
                self.tracking_table.setItem(row, 21, QTableWidgetItem(expected_arrival))

                # الموقع الحالي (مؤقت)
                current_location = self.get_current_location(shipment)
                self.tracking_table.setItem(row, 22, QTableWidgetItem(current_location))

                # نسبة الإنجاز (مؤقت)
                progress_percentage = self.calculate_progress(shipment)
                self.tracking_table.setItem(row, 23, QTableWidgetItem(f"{progress_percentage}%"))

                # الملاحظات
                self.tracking_table.setItem(row, 24, QTableWidgetItem(shipment.notes or ""))

            except Exception:
                # في حالة حدوث خطأ، تخطي هذا الصف
                continue

    def get_items_summary(self, items):
        """الحصول على ملخص الأصناف"""
        if not items:
            return "لا توجد أصناف"

        try:
            if len(items) == 1:
                return getattr(items[0], 'item_name', 'صنف غير محدد') or "صنف غير محدد"
            else:
                first_item = getattr(items[0], 'item_name', 'صنف غير محدد') or "صنف غير محدد"
                return f"{first_item} + {len(items)-1} أصناف أخرى"
        except:
            return "خطأ في البيانات"

    def get_current_location(self, shipment):
        """الحصول على الموقع الحالي للشحنة (مؤقت)"""
        if shipment.shipment_status == "تم التسليم":
            return shipment.final_destination or "تم التسليم"
        elif shipment.shipment_status == "في الطريق":
            return "في البحر"
        elif shipment.shipment_status == "وصل":
            return shipment.port_of_arrival or "الميناء"
        else:
            return shipment.port_of_loading or "نقطة البداية"

    def calculate_progress(self, shipment):
        """حساب نسبة الإنجاز (مؤقت)"""
        if shipment.shipment_status == "تم التسليم":
            return 100
        elif shipment.shipment_status == "وصلت الميناء":
            return 90
        elif shipment.shipment_status == "في الطريق":
            return 50
        elif shipment.shipment_status == "تحت الطلب":
            return 10
        else:
            return 25

    def update_statistics(self, shipments):
        """تحديث الإحصائيات"""
        total = len(shipments)
        active = len([s for s in shipments if s.shipment_status != "تم التسليم"])
        in_transit = len([s for s in shipments if s.shipment_status == "في الطريق"])
        delivered = len([s for s in shipments if s.shipment_status == "تم التسليم"])
        delayed = len([s for s in shipments if s.shipment_status == "متاخرة"])
        customs_pending = len([s for s in shipments if s.clearance_status == "بدون الافراج"])

        self.total_shipments_label.setText(str(total))
        self.active_shipments_label.setText(str(active))
        self.in_transit_label.setText(str(in_transit))
        self.delivered_label.setText(str(delivered))
        self.delayed_label.setText(str(delayed))
        self.customs_pending_label.setText(str(customs_pending))

    def load_suppliers_for_search(self):
        """تحميل الموردين لقائمة البحث"""
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()
            self.search_supplier.clear()
            self.search_supplier.addItem("الكل")
            for supplier in suppliers:
                self.search_supplier.addItem(supplier.name)
        except Exception as e:
            print(f"خطأ في تحميل الموردين: {e}")
        finally:
            session.close()

    def start_search_timer(self):
        """بدء مؤقت البحث الفوري"""
        self.search_timer.start(500)  # نصف ثانية

    def start_quick_search_timer(self):
        """بدء مؤقت البحث السريع"""
        self.quick_search_timer.start(300)  # 300 مللي ثانية للبحث السريع

    def perform_instant_search(self):
        """تنفيذ البحث الفوري"""
        self.perform_search()

    def perform_quick_search(self):
        """تنفيذ البحث السريع في جميع البيانات"""
        search_text = self.quick_search_input.text().strip().lower()

        if not search_text:
            # إذا كان البحث فارغاً، عرض جميع البيانات
            self.load_tracking_data()
            return

        session = self.db_manager.get_session()
        try:
            # البحث في جميع الحقول المهمة
            query = session.query(Shipment).outerjoin(Supplier).filter(
                Shipment.is_active == True
            ).filter(
                # البحث في رقم الشحنة
                Shipment.shipment_number.ilike(f'%{search_text}%') |
                # البحث في اسم المورد
                Supplier.name.ilike(f'%{search_text}%') |
                # البحث في الحالة
                Shipment.shipment_status.ilike(f'%{search_text}%') |
                # البحث في رقم التتبع
                Shipment.tracking_number.ilike(f'%{search_text}%') |
                # البحث في شركة الشحن
                Shipment.shipping_company.ilike(f'%{search_text}%') |
                # البحث في الملاحظات
                Shipment.notes.ilike(f'%{search_text}%')
            ).order_by(Shipment.created_at.desc())

            shipments = query.all()

            # تحميل البيانات المرتبطة
            for shipment in shipments:
                try:
                    shipment.containers = session.query(Container).filter(
                        Container.shipment_id == shipment.id
                    ).all()
                    shipment.items = session.query(ShipmentItem).filter(
                        ShipmentItem.shipment_id == shipment.id
                    ).all()
                except:
                    shipment.containers = []
                    shipment.items = []

            self.populate_tracking_table(shipments)
            self.update_statistics(shipments)

            # تحديث شريط الحالة
            self.status_label.setText(f"تم العثور على {len(shipments)} شحنة")

        except Exception as e:
            print(f"خطأ في البحث السريع: {e}")
            self.status_label.setText("خطأ في البحث")
        finally:
            session.close()

    def perform_search(self):
        """تنفيذ البحث"""
        # جمع معايير البحث
        shipment_number = self.search_shipment_number.text().strip()
        supplier = self.search_supplier.currentText()
        status = self.search_status.currentText()
        clearance_status = self.search_clearance_status.currentText()
        date_from = self.search_date_from.date().toPython()
        date_to = self.search_date_to.date().toPython()

        session = self.db_manager.get_session()
        try:
            # بناء الاستعلام
            query = session.query(Shipment).outerjoin(Supplier).filter(Shipment.is_active == True)

            # تطبيق الفلاتر
            if shipment_number:
                query = query.filter(Shipment.shipment_number.contains(shipment_number))

            if supplier and supplier != "الكل":
                query = query.filter(Supplier.name == supplier)

            if status and status != "الكل":
                query = query.filter(Shipment.shipment_status == status)

            if clearance_status and clearance_status != "الكل":
                if clearance_status == "تم الإفراج":
                    query = query.filter(Shipment.clearance_status == "مع الافراج")
                elif clearance_status == "في انتظار الإفراج":
                    query = query.filter(Shipment.clearance_status == "بدون الافراج")

            # فلتر التاريخ
            query = query.filter(Shipment.shipment_date >= date_from)
            query = query.filter(Shipment.shipment_date <= date_to)

            shipments = query.order_by(Shipment.created_at.desc()).all()

            # تحميل البيانات المرتبطة
            for shipment in shipments:
                shipment.containers = session.query(Container).filter(
                    Container.shipment_id == shipment.id
                ).all()
                shipment.items = session.query(ShipmentItem).filter(
                    ShipmentItem.shipment_id == shipment.id
                ).all()

            self.populate_tracking_table(shipments)
            self.update_statistics(shipments)

            self.status_label.setText(f"تم العثور على {len(shipments)} شحنة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث:\n{str(e)}")
        finally:
            session.close()

    def clear_search(self):
        """مسح البحث"""
        # مسح البحث السريع
        self.quick_search_input.clear()

        # مسح الفلاتر المتقدمة
        self.search_shipment_number.clear()
        self.search_supplier.setCurrentIndex(0)
        self.search_status.setCurrentIndex(0)
        self.search_clearance_status.setCurrentIndex(0)
        self.search_date_from.setDate(QDate.currentDate().addDays(-30))
        self.search_date_to.setDate(QDate.currentDate())

        # إعادة تحميل جميع البيانات
        self.load_tracking_data()

    def update_shipment_details(self):
        """تحديث تفاصيل الشحنة المحددة"""
        current_row = self.tracking_table.currentRow()
        if current_row >= 0:
            # الحصول على معرف الشحنة
            shipment_id_item = self.tracking_table.item(current_row, 0)
            if shipment_id_item:
                shipment_id = shipment_id_item.data(Qt.UserRole)

                # تحميل تفاصيل الشحنة
                session = self.db_manager.get_session()
                try:
                    shipment = session.query(Shipment).outerjoin(Supplier).filter(
                        Shipment.id == shipment_id
                    ).first()

                    if shipment:
                        # تحديث المعلومات الأساسية
                        self.selected_shipment_number.setText(shipment.shipment_number or "-")
                        self.selected_supplier.setText(shipment.supplier.name if shipment.supplier else "-")
                        self.selected_status.setText(shipment.shipment_status or "-")

                        self.selected_clearance_status.setText(shipment.clearance_status or "بدون الافراج")

                        # معلومات الشحن
                        self.selected_tracking_number.setText(shipment.tracking_number or "-")
                        self.selected_shipping_company.setText(shipment.shipping_company or "-")

                        departure_date = format_date(shipment.departure_date) if shipment.departure_date else "-"
                        self.selected_departure_date.setText(departure_date)

                        expected_arrival = format_date(shipment.expected_arrival_date) if shipment.expected_arrival_date else "-"
                        self.selected_expected_arrival.setText(expected_arrival)

                        # شريط التقدم
                        progress = self.calculate_progress(shipment)
                        self.shipment_progress.setValue(progress)
                        self.shipment_progress_label.setText(f"{progress}%")

                except Exception as e:
                    print(f"خطأ في تحميل تفاصيل الشحنة: {e}")
                finally:
                    session.close()
        else:
            # مسح التفاصيل
            self.clear_shipment_details()

    def clear_shipment_details(self):
        """مسح تفاصيل الشحنة"""
        self.selected_shipment_number.setText("-")
        self.selected_supplier.setText("-")
        self.selected_status.setText("-")
        self.selected_clearance_status.setText("-")
        self.selected_tracking_number.setText("-")
        self.selected_shipping_company.setText("-")
        self.selected_departure_date.setText("-")
        self.selected_expected_arrival.setText("-")
        self.shipment_progress.setValue(0)
        self.shipment_progress_label.setText("0%")

    def auto_refresh_data(self):
        """التحديث التلقائي للبيانات"""
        self.load_tracking_data()

    def toggle_auto_refresh(self, enabled):
        """تفعيل/إلغاء التحديث التلقائي"""
        if enabled:
            self.auto_refresh_timer.start(300000)  # 5 دقائق
            self.status_label.setText("التحديث التلقائي مفعل")
        else:
            self.auto_refresh_timer.stop()
            self.status_label.setText("التحديث التلقائي معطل")

    def export_tracking_data(self):
        """تصدير بيانات التتبع"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "تصدير بيانات التتبع",
                f"tracking_data_{QDate.currentDate().toString('yyyy-MM-dd')}.csv",
                "CSV Files (*.csv);;Excel Files (*.xlsx)"
            )

            if file_path:
                # جمع البيانات من الجدول
                rows = self.tracking_table.rowCount()
                cols = self.tracking_table.columnCount()

                data = []
                # إضافة العناوين
                headers = []
                for col in range(cols):
                    header_item = self.tracking_table.horizontalHeaderItem(col)
                    headers.append(header_item.text() if header_item else f"Column {col}")
                data.append(headers)

                # إضافة البيانات
                for row in range(rows):
                    row_data = []
                    for col in range(cols):
                        item = self.tracking_table.item(row, col)
                        row_data.append(item.text() if item else "")
                    data.append(row_data)

                # كتابة الملف
                if file_path.endswith('.csv'):
                    import csv
                    with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                        writer = csv.writer(csvfile)
                        writer.writerows(data)
                else:
                    # Excel export would require additional library
                    QMessageBox.information(self, "تنبيه", "تصدير Excel غير متاح حالياً. يرجى استخدام CSV.")
                    return

                QMessageBox.information(self, "نجح التصدير", f"تم تصدير البيانات إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في التصدير", f"فشل في تصدير البيانات:\n{str(e)}")

    def show_tracking_settings(self):
        """إظهار إعدادات التتبع"""
        QMessageBox.information(self, "إعدادات التتبع", "🚧 إعدادات التتبع قيد التطوير")

    def update_analytics(self):
        """تحديث بيانات التحليلات"""
        try:
            session = self.db_manager.get_session()

            # تحديد الفترة الزمنية
            period = self.analytics_period.currentText()
            date_filter = self.get_date_filter_for_period(period)

            # استعلام الشحنات حسب الفترة
            query = session.query(Shipment).outerjoin(Supplier).filter(Shipment.is_active == True)
            if date_filter:
                query = query.filter(Shipment.created_at >= date_filter)

            shipments = query.all()

            # تحديث الإحصائيات العامة
            self.update_general_analytics(shipments)

            # تحديث إحصائيات الحالات
            self.update_status_analytics(shipments, session)

            # تحديث إحصائيات الموردين
            self.update_supplier_analytics(shipments, session)

            # تحديث الإحصائيات المالية
            self.update_financial_analytics(shipments)

            # تحديث إحصائيات الأداء
            self.update_performance_analytics(shipments)

            session.close()

        except Exception as e:
            print(f"خطأ في تحديث التحليلات: {e}")

    def get_date_filter_for_period(self, period):
        """الحصول على فلتر التاريخ حسب الفترة"""
        from datetime import datetime, timedelta

        if period == "آخر 7 أيام":
            return datetime.now() - timedelta(days=7)
        elif period == "آخر 30 يوم":
            return datetime.now() - timedelta(days=30)
        elif period == "آخر 3 أشهر":
            return datetime.now() - timedelta(days=90)
        elif period == "آخر 6 أشهر":
            return datetime.now() - timedelta(days=180)
        elif period == "آخر سنة":
            return datetime.now() - timedelta(days=365)
        else:  # الكل
            return None

    def update_general_analytics(self, shipments):
        """تحديث الإحصائيات العامة"""
        total = len(shipments)
        active = len([s for s in shipments if s.shipment_status != "تم التسليم"])
        completed = len([s for s in shipments if s.shipment_status == "تم التسليم"])
        delayed = len([s for s in shipments if s.shipment_status == "متاخرة"])

        self.total_shipments_analytics.value_label.setText(str(total))
        self.active_shipments_analytics.value_label.setText(str(active))
        self.completed_shipments_analytics.value_label.setText(str(completed))
        self.delayed_shipments_analytics.value_label.setText(str(delayed))

    def update_status_analytics(self, shipments, session):
        """تحديث إحصائيات الحالات"""
        # حساب توزيع الحالات
        status_counts = {}
        for shipment in shipments:
            status = shipment.shipment_status or "غير محدد"
            status_counts[status] = status_counts.get(status, 0) + 1

        # تحديث الجدول
        self.status_table.setRowCount(len(status_counts))
        total_shipments = len(shipments)

        for row, (status, count) in enumerate(status_counts.items()):
            percentage = (count / total_shipments * 100) if total_shipments > 0 else 0

            self.status_table.setItem(row, 0, QTableWidgetItem(status))
            self.status_table.setItem(row, 1, QTableWidgetItem(str(count)))
            self.status_table.setItem(row, 2, QTableWidgetItem(f"{percentage:.1f}%"))

    def update_supplier_analytics(self, shipments, session):
        """تحديث إحصائيات الموردين"""
        # حساب إحصائيات الموردين
        supplier_stats = {}
        for shipment in shipments:
            supplier_name = shipment.supplier.name if shipment.supplier else "غير محدد"
            if supplier_name not in supplier_stats:
                supplier_stats[supplier_name] = {
                    'count': 0,
                    'total_value': 0.0,
                    'values': []
                }

            supplier_stats[supplier_name]['count'] += 1
            value = shipment.total_amount or 0.0
            supplier_stats[supplier_name]['total_value'] += value
            supplier_stats[supplier_name]['values'].append(value)

        # تحديث الجدول
        self.supplier_table.setRowCount(len(supplier_stats))

        for row, (supplier, stats) in enumerate(supplier_stats.items()):
            avg_value = stats['total_value'] / stats['count'] if stats['count'] > 0 else 0

            self.supplier_table.setItem(row, 0, QTableWidgetItem(supplier))
            self.supplier_table.setItem(row, 1, QTableWidgetItem(str(stats['count'])))
            self.supplier_table.setItem(row, 2, QTableWidgetItem(f"{stats['total_value']:,.2f}"))
            self.supplier_table.setItem(row, 3, QTableWidgetItem(f"{avg_value:,.2f}"))

    def update_financial_analytics(self, shipments):
        """تحديث الإحصائيات المالية"""
        total_value = sum(s.total_amount or 0 for s in shipments)
        avg_value = total_value / len(shipments) if shipments else 0
        total_insurance = sum(s.insurance_amount or 0 for s in shipments)
        total_freight = sum(s.freight_amount or 0 for s in shipments)

        self.total_value_card.value_label.setText(f"{total_value:,.0f}")
        self.avg_value_card.value_label.setText(f"{avg_value:,.0f}")
        self.insurance_total_card.value_label.setText(f"{total_insurance:,.0f}")
        self.freight_total_card.value_label.setText(f"{total_freight:,.0f}")

    def update_performance_analytics(self, shipments):
        """تحديث إحصائيات الأداء"""
        from datetime import datetime

        if not shipments:
            self.on_time_delivery_card.value_label.setText("0%")
            self.avg_delivery_time_card.value_label.setText("0 يوم")
            self.customs_clearance_rate_card.value_label.setText("0%")
            self.shipment_success_rate_card.value_label.setText("0%")
            return

        # معدل التسليم في الوقت (تقدير)
        delivered_shipments = [s for s in shipments if s.shipment_status == "تم التسليم"]
        on_time_rate = len(delivered_shipments) / len(shipments) * 100 if shipments else 0

        # متوسط وقت التسليم (تقدير)
        delivery_times = []
        for shipment in delivered_shipments:
            if shipment.created_at and shipment.actual_arrival_date:
                delta = shipment.actual_arrival_date - shipment.created_at
                delivery_times.append(delta.days)

        avg_delivery_time = sum(delivery_times) / len(delivery_times) if delivery_times else 0

        # معدل الإفراج الجمركي
        cleared_shipments = [s for s in shipments if s.clearance_status == "مع الافراج"]
        clearance_rate = len(cleared_shipments) / len(shipments) * 100 if shipments else 0

        # معدل نجاح الشحنات
        successful_shipments = [s for s in shipments if s.shipment_status in ["تم التسليم", "وصلت الميناء"]]
        success_rate = len(successful_shipments) / len(shipments) * 100 if shipments else 0

        # تحديث البطاقات
        self.on_time_delivery_card.value_label.setText(f"{on_time_rate:.1f}%")
        self.avg_delivery_time_card.value_label.setText(f"{avg_delivery_time:.0f} يوم")
        self.customs_clearance_rate_card.value_label.setText(f"{clearance_rate:.1f}%")
        self.shipment_success_rate_card.value_label.setText(f"{success_rate:.1f}%")

    def export_analytics(self):
        """تصدير التحليلات"""
        try:
            from PySide6.QtWidgets import QFileDialog
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير التحليلات",
                f"تحليلات_الشحنات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رأس التقرير
                    writer.writerow(['تقرير تحليلات الشحنات'])
                    writer.writerow(['تاريخ التقرير:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
                    writer.writerow(['فترة التحليل:', self.analytics_period.currentText()])
                    writer.writerow([])  # سطر فارغ

                    # الإحصائيات العامة
                    writer.writerow(['الإحصائيات العامة'])
                    writer.writerow(['المؤشر', 'القيمة'])
                    writer.writerow(['إجمالي الشحنات', self.total_shipments_analytics.value_label.text()])
                    writer.writerow(['الشحنات النشطة', self.active_shipments_analytics.value_label.text()])
                    writer.writerow(['الشحنات المكتملة', self.completed_shipments_analytics.value_label.text()])
                    writer.writerow(['الشحنات المتأخرة', self.delayed_shipments_analytics.value_label.text()])
                    writer.writerow([])

                    # إحصائيات الحالات
                    writer.writerow(['توزيع حالات الشحنات'])
                    writer.writerow(['الحالة', 'العدد', 'النسبة المئوية'])
                    for row in range(self.status_table.rowCount()):
                        status = self.status_table.item(row, 0).text() if self.status_table.item(row, 0) else ""
                        count = self.status_table.item(row, 1).text() if self.status_table.item(row, 1) else ""
                        percentage = self.status_table.item(row, 2).text() if self.status_table.item(row, 2) else ""
                        writer.writerow([status, count, percentage])

                QMessageBox.information(self, "تصدير التحليلات", f"تم تصدير التحليلات بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التحليلات:\n{str(e)}")

    def update_map_data(self):
        """تحديث بيانات الخريطة"""
        try:
            session = self.db_manager.get_session()

            # فلتر الشحنات حسب الاختيار
            filter_type = self.map_filter.currentText()
            query = session.query(Shipment).outerjoin(Supplier).filter(Shipment.is_active == True)

            if filter_type == "الشحنات النشطة":
                query = query.filter(Shipment.shipment_status != "تم التسليم")
            elif filter_type == "في الطريق":
                query = query.filter(Shipment.shipment_status == "في الطريق")
            elif filter_type == "وصلت الميناء":
                query = query.filter(Shipment.shipment_status == "وصلت الميناء")
            elif filter_type == "تم التسليم":
                query = query.filter(Shipment.shipment_status == "تم التسليم")

            shipments = query.order_by(Shipment.created_at.desc()).all()

            # تحديث جدول الشحنات
            self.populate_map_shipments_table(shipments)

            # تحديث عرض الخريطة
            self.update_map_display(shipments)

            session.close()

        except Exception as e:
            print(f"خطأ في تحديث بيانات الخريطة: {e}")

    def populate_map_shipments_table(self, shipments):
        """ملء جدول الشحنات للخريطة"""
        self.map_shipments_table.setRowCount(len(shipments))

        for row, shipment in enumerate(shipments):
            # رقم الشحنة
            self.map_shipments_table.setItem(row, 0, QTableWidgetItem(shipment.shipment_number or ""))

            # المورد
            supplier_name = shipment.supplier.name if shipment.supplier else "غير محدد"
            self.map_shipments_table.setItem(row, 1, QTableWidgetItem(supplier_name))

            # الموقع الحالي
            current_location = self.get_current_location_for_map(shipment)
            self.map_shipments_table.setItem(row, 2, QTableWidgetItem(current_location))

            # الحالة
            self.map_shipments_table.setItem(row, 3, QTableWidgetItem(shipment.shipment_status or ""))

    def get_current_location_for_map(self, shipment):
        """تحديد الموقع الحالي للشحنة على الخريطة"""
        status = shipment.shipment_status

        if status == "تم التسليم":
            return shipment.final_destination or "تم التسليم"
        elif status == "وصلت الميناء":
            return shipment.port_of_arrival or "الميناء"
        elif status == "في الطريق":
            return "في البحر"
        elif status == "تم الشحن":
            return shipment.port_of_loading or "ميناء التحميل"
        else:
            return "نقطة البداية"

    def update_map_display(self, shipments):
        """تحديث عرض الخريطة"""
        map_content = "🌍 خريطة تتبع الشحنات\n"
        map_content += "=" * 50 + "\n\n"

        if not shipments:
            map_content += "📍 لا توجد شحنات لعرضها على الخريطة\n"
        else:
            # تجميع الشحنات حسب الموقع
            location_groups = {}
            for shipment in shipments:
                location = self.get_current_location_for_map(shipment)
                if location not in location_groups:
                    location_groups[location] = []
                location_groups[location].append(shipment)

            # عرض المواقع والشحنات
            for location, location_shipments in location_groups.items():
                map_content += f"📍 {location} ({len(location_shipments)} شحنة)\n"
                for shipment in location_shipments[:3]:  # عرض أول 3 شحنات فقط
                    supplier = shipment.supplier.name if shipment.supplier else "غير محدد"
                    map_content += f"   • {shipment.shipment_number} - {supplier}\n"

                if len(location_shipments) > 3:
                    map_content += f"   ... و {len(location_shipments) - 3} شحنة أخرى\n"
                map_content += "\n"

            # إحصائيات سريعة
            map_content += "\n" + "=" * 30 + "\n"
            map_content += "📊 إحصائيات سريعة:\n"
            map_content += f"• إجمالي الشحنات: {len(shipments)}\n"
            map_content += f"• عدد المواقع: {len(location_groups)}\n"

            # توزيع الحالات
            status_counts = {}
            for shipment in shipments:
                status = shipment.shipment_status or "غير محدد"
                status_counts[status] = status_counts.get(status, 0) + 1

            map_content += "\n🔄 توزيع الحالات:\n"
            for status, count in status_counts.items():
                map_content += f"• {status}: {count}\n"

        self.map_display.setText(map_content)

    def on_map_shipment_selected(self):
        """معالجة اختيار شحنة من جدول الخريطة"""
        try:
            current_row = self.map_shipments_table.currentRow()
            if current_row >= 0:
                shipment_number = self.map_shipments_table.item(current_row, 0).text()

                # البحث عن الشحنة
                session = self.db_manager.get_session()
                shipment = session.query(Shipment).outerjoin(Supplier).filter(
                    Shipment.shipment_number == shipment_number
                ).first()

                if shipment:
                    # تحديث معلومات الشحنة المحددة
                    info_text = f"📦 رقم الشحنة: {shipment.shipment_number}\n"
                    info_text += f"🏢 المورد: {shipment.supplier.name if shipment.supplier else 'غير محدد'}\n"
                    info_text += f"📍 الموقع الحالي: {self.get_current_location_for_map(shipment)}\n"
                    info_text += f"🔄 الحالة: {shipment.shipment_status or 'غير محدد'}\n"
                    info_text += f"🚢 شركة الشحن: {shipment.shipping_company or 'غير محدد'}\n"
                    info_text += f"📋 رقم التتبع: {shipment.tracking_number or 'غير محدد'}\n"

                    if shipment.port_of_loading:
                        info_text += f"🚢 ميناء التحميل: {shipment.port_of_loading}\n"
                    if shipment.port_of_arrival:
                        info_text += f"🏁 ميناء الوصول: {shipment.port_of_arrival}\n"
                    if shipment.final_destination:
                        info_text += f"🎯 الوجهة النهائية: {shipment.final_destination}\n"

                    self.selected_map_shipment_info.setText(info_text)

                session.close()

        except Exception as e:
            print(f"خطأ في اختيار الشحنة: {e}")

    def show_shipping_routes(self):
        """عرض مسارات الشحن"""
        try:
            from .shipping_routes_window import ShippingRoutesWindow

            # الحصول على الشحنات المفلترة
            session = self.db_manager.get_session()
            try:
                filter_type = self.map_filter.currentText()
                query = session.query(Shipment).outerjoin(Supplier).filter(Shipment.is_active == True)

                if filter_type == "الشحنات النشطة":
                    query = query.filter(Shipment.shipment_status != "تم التسليم")
                elif filter_type == "في الطريق":
                    query = query.filter(Shipment.shipment_status == "في الطريق")
                elif filter_type == "وصلت الميناء":
                    query = query.filter(Shipment.shipment_status == "وصلت الميناء")
                elif filter_type == "تم التسليم":
                    query = query.filter(Shipment.shipment_status == "تم التسليم")

                shipments = query.order_by(Shipment.created_at.desc()).all()

                # فتح نافذة المسارات
                routes_window = ShippingRoutesWindow(shipments, self)
                routes_window.exec()

            finally:
                session.close()

        except ImportError:
            # إذا لم تكن النافذة موجودة، أنشئها
            self.create_shipping_routes_window()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة المسارات: {str(e)}")

    def create_shipping_routes_window(self):
        """إنشاء نافذة مسارات الشحن المؤقتة"""
        dialog = QDialog(self)
        dialog.setWindowTitle("🗺️ مسارات الشحن التفاعلية")
        dialog.setModal(True)
        dialog.resize(1200, 800)

        layout = QVBoxLayout(dialog)

        # شريط العنوان
        title_label = QLabel("🗺️ مسارات الشحن التفاعلية")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; padding: 15px; background-color: #ecf0f1; border-radius: 8px; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # محتوى مؤقت
        content_text = QTextEdit()
        content_text.setReadOnly(True)
        content_text.setText(
            "🚧 نافذة مسارات الشحن قيد التطوير...\n\n"
            "سيتم إضافة الميزات التالية قريباً:\n\n"
            "🗺️ خريطة تفاعلية للمسارات\n"
            "📍 تتبع مواقع الشحنات في الوقت الفعلي\n"
            "🚢 عرض مسارات السفن والطائرات\n"
            "⏰ تقديرات أوقات الوصول\n"
            "🚨 تنبيهات التأخير والتحديثات\n"
            "📊 إحصائيات المسارات والأداء\n"
            "🔍 بحث وفلترة متقدمة للمسارات\n"
            "📱 دعم الخرائط التفاعلية\n"
            "🌍 عرض المسارات العالمية\n"
            "📈 تحليل كفاءة المسارات"
        )
        content_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                font-size: 14px;
                line-height: 1.6;
            }
        """)
        layout.addWidget(content_text)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.close)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_button)

        layout.addLayout(buttons_layout)

        dialog.exec()

    def update_report_preview(self):
        """تحديث معاينة التقرير"""
        try:
            report_type = self.report_type.currentText()
            period = self.report_period.currentText()

            # إنشاء معاينة التقرير
            preview_content = self.generate_report_content(report_type, period, preview_mode=True)
            self.report_preview.setText(preview_content)

        except Exception as e:
            print(f"خطأ في تحديث معاينة التقرير: {e}")

    def generate_report(self):
        """إنشاء التقرير الكامل"""
        try:
            report_type = self.report_type.currentText()
            period = self.report_period.currentText()

            # إنشاء التقرير الكامل
            report_content = self.generate_report_content(report_type, period, preview_mode=False)
            self.report_preview.setText(report_content)

            QMessageBox.information(self, "إنشاء التقرير", "تم إنشاء التقرير بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير:\n{str(e)}")

    def generate_report_content(self, report_type, period, preview_mode=False):
        """إنشاء محتوى التقرير"""
        from datetime import datetime

        session = self.db_manager.get_session()

        try:
            # تحديد الفترة الزمنية
            date_filter = self.get_date_filter_for_period(period)

            # استعلام الشحنات
            query = session.query(Shipment).outerjoin(Supplier).filter(Shipment.is_active == True)
            if date_filter:
                query = query.filter(Shipment.created_at >= date_filter)

            shipments = query.all()

            # رأس التقرير
            content = f"📊 {report_type}\n"
            content += "=" * 60 + "\n"
            content += f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            content += f"⏰ فترة التقرير: {period}\n"
            content += f"📦 عدد الشحنات: {len(shipments)}\n"
            content += "=" * 60 + "\n\n"

            # محتوى التقرير حسب النوع
            if report_type == "تقرير شامل للشحنات":
                content += self.generate_comprehensive_report(shipments, preview_mode)
            elif report_type == "تقرير الشحنات حسب المورد":
                content += self.generate_supplier_report(shipments, preview_mode)
            elif report_type == "تقرير الشحنات حسب الحالة":
                content += self.generate_status_report(shipments, preview_mode)
            elif report_type == "تقرير مالي مفصل":
                content += self.generate_financial_report(shipments, preview_mode)
            elif report_type == "تقرير الأداء والإحصائيات":
                content += self.generate_performance_report(shipments, preview_mode)
            elif report_type == "تقرير الشحنات المتأخرة":
                content += self.generate_delayed_report(shipments, preview_mode)
            elif report_type == "تقرير الإفراج الجمركي":
                content += self.generate_customs_report(shipments, preview_mode)

            # ذيل التقرير
            content += "\n" + "=" * 60 + "\n"
            content += "📋 تم إنشاء هذا التقرير بواسطة نظام إدارة الشحنات\n"
            content += f"🕒 وقت الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            return content

        finally:
            session.close()

    def generate_comprehensive_report(self, shipments, preview_mode):
        """إنشاء التقرير الشامل"""
        content = "📋 ملخص الشحنات:\n"
        content += "-" * 30 + "\n"

        limit = 5 if preview_mode else len(shipments)
        for i, shipment in enumerate(shipments[:limit]):
            supplier_name = shipment.supplier.name if shipment.supplier else "غير محدد"
            content += f"{i+1}. {shipment.shipment_number} - {supplier_name}\n"
            content += f"   الحالة: {shipment.shipment_status or 'غير محدد'}\n"
            if self.include_financial.isChecked():
                content += f"   القيمة: {shipment.total_amount or 0:,.0f}\n"
            content += "\n"

        if preview_mode and len(shipments) > 5:
            content += f"... و {len(shipments) - 5} شحنة أخرى\n"

        return content

    def generate_supplier_report(self, shipments, preview_mode):
        """إنشاء تقرير الموردين"""
        supplier_stats = {}
        for shipment in shipments:
            supplier_name = shipment.supplier.name if shipment.supplier else "غير محدد"
            if supplier_name not in supplier_stats:
                supplier_stats[supplier_name] = {'count': 0, 'total_value': 0}
            supplier_stats[supplier_name]['count'] += 1
            supplier_stats[supplier_name]['total_value'] += shipment.total_amount or 0

        content = "🏢 تقرير الشحنات حسب المورد:\n"
        content += "-" * 40 + "\n"

        for supplier, stats in supplier_stats.items():
            content += f"• {supplier}:\n"
            content += f"  عدد الشحنات: {stats['count']}\n"
            if self.include_financial.isChecked():
                content += f"  إجمالي القيمة: {stats['total_value']:,.0f}\n"
            content += "\n"

        return content

    def generate_status_report(self, shipments, preview_mode):
        """إنشاء تقرير الحالات"""
        status_counts = {}
        for shipment in shipments:
            status = shipment.shipment_status or "غير محدد"
            status_counts[status] = status_counts.get(status, 0) + 1

        content = "🔄 تقرير الشحنات حسب الحالة:\n"
        content += "-" * 35 + "\n"

        total = len(shipments)
        for status, count in status_counts.items():
            percentage = (count / total * 100) if total > 0 else 0
            content += f"• {status}: {count} ({percentage:.1f}%)\n"

        return content

    def generate_financial_report(self, shipments, preview_mode):
        """إنشاء التقرير المالي"""
        total_value = sum(s.total_amount or 0 for s in shipments)
        total_insurance = sum(s.insurance_amount or 0 for s in shipments)
        total_freight = sum(s.freight_amount or 0 for s in shipments)
        avg_value = total_value / len(shipments) if shipments else 0

        content = "💰 التقرير المالي المفصل:\n"
        content += "-" * 30 + "\n"
        content += f"• إجمالي قيمة الشحنات: {total_value:,.0f}\n"
        content += f"• متوسط قيمة الشحنة: {avg_value:,.0f}\n"
        content += f"• إجمالي التأمين: {total_insurance:,.0f}\n"
        content += f"• إجمالي الشحن: {total_freight:,.0f}\n"
        content += f"• إجمالي التكاليف: {total_value + total_insurance + total_freight:,.0f}\n"

        return content

    def generate_performance_report(self, shipments, preview_mode):
        """إنشاء تقرير الأداء"""
        delivered = len([s for s in shipments if s.shipment_status == "تم التسليم"])
        in_transit = len([s for s in shipments if s.shipment_status == "في الطريق"])
        delayed = len([s for s in shipments if s.shipment_status == "متاخرة"])

        content = "📈 تقرير الأداء والإحصائيات:\n"
        content += "-" * 35 + "\n"
        content += f"• الشحنات المسلمة: {delivered}\n"
        content += f"• الشحنات في الطريق: {in_transit}\n"
        content += f"• الشحنات المتأخرة: {delayed}\n"

        if shipments:
            delivery_rate = (delivered / len(shipments)) * 100
            content += f"• معدل التسليم: {delivery_rate:.1f}%\n"

        return content

    def generate_delayed_report(self, shipments, preview_mode):
        """إنشاء تقرير الشحنات المتأخرة"""
        delayed_shipments = [s for s in shipments if s.shipment_status == "متاخرة"]

        content = "⚠️ تقرير الشحنات المتأخرة:\n"
        content += "-" * 30 + "\n"
        content += f"عدد الشحنات المتأخرة: {len(delayed_shipments)}\n\n"

        limit = 3 if preview_mode else len(delayed_shipments)
        for i, shipment in enumerate(delayed_shipments[:limit]):
            supplier_name = shipment.supplier.name if shipment.supplier else "غير محدد"
            content += f"{i+1}. {shipment.shipment_number} - {supplier_name}\n"

        if preview_mode and len(delayed_shipments) > 3:
            content += f"... و {len(delayed_shipments) - 3} شحنة متأخرة أخرى\n"

        return content

    def generate_customs_report(self, shipments, preview_mode):
        """إنشاء تقرير الإفراج الجمركي"""
        cleared = len([s for s in shipments if s.clearance_status == "مع الافراج"])
        not_cleared = len([s for s in shipments if s.clearance_status == "بدون الافراج"])

        content = "🛃 تقرير الإفراج الجمركي:\n"
        content += "-" * 30 + "\n"
        content += f"• شحنات مع الإفراج: {cleared}\n"
        content += f"• شحنات بدون الإفراج: {not_cleared}\n"

        if shipments:
            clearance_rate = (cleared / len(shipments)) * 100
            content += f"• معدل الإفراج: {clearance_rate:.1f}%\n"

        return content

    def export_report_pdf(self):
        """تصدير التقرير كـ PDF"""
        QMessageBox.information(
            self, "تصدير PDF",
            "📄 تصدير التقرير كملف PDF\n\n"
            "هذه الميزة ستتضمن:\n"
            "• تنسيق احترافي للتقرير\n"
            "• الرسوم البيانية والجداول\n"
            "• إمكانية الطباعة المباشرة\n\n"
            "🚧 قيد التطوير..."
        )

    def export_report_excel(self):
        """تصدير التقرير كـ Excel"""
        try:
            from PySide6.QtWidgets import QFileDialog
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير التقرير",
                f"تقرير_{self.report_type.currentText()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                # إنشاء التقرير الكامل
                report_content = self.generate_report_content(
                    self.report_type.currentText(),
                    self.report_period.currentText(),
                    preview_mode=False
                )

                # حفظ التقرير كملف نصي
                with open(file_path, 'w', encoding='utf-8-sig') as f:
                    f.write(report_content)

                QMessageBox.information(self, "تصدير التقرير", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(
            self, "طباعة التقرير",
            "🖨️ طباعة التقرير\n\n"
            "هذه الميزة ستتضمن:\n"
            "• معاينة الطباعة\n"
            "• إعدادات الطباعة المتقدمة\n"
            "• تنسيق مناسب للطباعة\n\n"
            "🚧 قيد التطوير..."
        )

    def load_sample_alerts(self):
        """تحميل التنبيهات الذكية من قاعدة البيانات"""
        try:
            session = self.db_manager.get_session()
            from datetime import datetime, timedelta

            # الحصول على جميع الشحنات
            shipments = session.query(Shipment).all()

            # إنشاء التنبيهات الذكية
            alerts = []
            current_time = datetime.now()

            for shipment in shipments:
                # تنبيهات التأخير
                if shipment.arrival_date and shipment.arrival_date < current_time.date():
                    if shipment.shipment_status not in ['تم التسليم', 'مكتمل']:
                        days_late = (current_time.date() - shipment.arrival_date).days
                        alerts.append({
                            'type': '⚠️ تأخير',
                            'title': f'شحنة متأخرة - {shipment.shipment_number}',
                            'status': 'نشط',
                            'date': current_time - timedelta(days=days_late),
                            'priority': 'عالية' if days_late > 7 else 'متوسطة',
                            'details': f'الشحنة {shipment.shipment_number} متأخرة عن الموعد المحدد بـ {days_late} يوم.',
                            'shipment_id': shipment.id
                        })

                # تنبيهات الوصول المتوقع
                if shipment.arrival_date:
                    days_until_arrival = (shipment.arrival_date - current_time.date()).days
                    if 0 <= days_until_arrival <= 3 and shipment.shipment_status in ['في الطريق', 'تم الشحن']:
                        alerts.append({
                            'type': '📦 وصول متوقع',
                            'title': f'وصول متوقع - {shipment.shipment_number}',
                            'status': 'نشط',
                            'date': current_time,
                            'priority': 'متوسطة',
                            'details': f'من المتوقع وصول الشحنة {shipment.shipment_number} خلال {days_until_arrival} يوم.',
                            'shipment_id': shipment.id
                        })

                # تنبيهات الحالة
                if shipment.shipment_status == 'وصل الميناء':
                    alerts.append({
                        'type': '🚢 وصول',
                        'title': f'وصلت إلى الميناء - {shipment.shipment_number}',
                        'status': 'نشط',
                        'date': current_time,
                        'priority': 'متوسطة',
                        'details': f'وصلت الشحنة {shipment.shipment_number} إلى الميناء وتحتاج للإفراج الجمركي.',
                        'shipment_id': shipment.id
                    })

                # تنبيهات الإفراج الجمركي
                if shipment.clearance_status == 'قيد المراجعة':
                    alerts.append({
                        'type': '🛃 جمارك',
                        'title': f'مراجعة جمركية - {shipment.shipment_number}',
                        'status': 'نشط',
                        'date': current_time,
                        'priority': 'عالية',
                        'details': f'الشحنة {shipment.shipment_number} قيد المراجعة الجمركية.',
                        'shipment_id': shipment.id
                    })

                # تنبيهات مالية (للشحنات عالية القيمة)
                if shipment.total_amount and shipment.total_amount > 100000:
                    if shipment.shipment_status not in ['تم التسليم', 'مكتمل']:
                        alerts.append({
                            'type': '💰 مالي',
                            'title': f'شحنة عالية القيمة - {shipment.shipment_number}',
                            'status': 'نشط',
                            'date': current_time,
                            'priority': 'عالية',
                            'details': f'الشحنة {shipment.shipment_number} عالية القيمة ({shipment.total_amount:,.2f}) وتحتاج متابعة خاصة.',
                            'shipment_id': shipment.id
                        })

            # ترتيب التنبيهات حسب الأولوية والتاريخ
            priority_order = {'عالية': 0, 'متوسطة': 1, 'منخفضة': 2}
            alerts.sort(key=lambda x: (priority_order.get(x['priority'], 3), x['date']), reverse=True)

            # حفظ التنبيهات للاستخدام في الفلترة
            self.current_alerts = alerts

            self.populate_alerts_table(alerts)
            self.update_alerts_statistics(alerts)

        except Exception as e:
            print(f"خطأ في تحميل التنبيهات: {e}")
            # عرض بيانات تجريبية في حالة الخطأ
            sample_alerts = [
                {
                    'type': '⚠️ تأخير',
                    'title': 'شحنة متأخرة - SH001',
                    'status': 'نشط',
                    'date': datetime.now() - timedelta(hours=2),
                    'priority': 'عالية',
                    'details': 'الشحنة SH001 متأخرة عن الموعد المتوقع بـ 3 أيام. يرجى المتابعة مع شركة الشحن.',
                    'shipment_id': None
                }
            ]
            self.current_alerts = sample_alerts
            self.populate_alerts_table(sample_alerts)
            self.update_alerts_statistics(sample_alerts)
        finally:
            if 'session' in locals():
                session.close()

    def populate_alerts_table(self, alerts):
        """ملء جدول التنبيهات"""
        self.alerts_table.setRowCount(len(alerts))

        for row, alert in enumerate(alerts):
            self.alerts_table.setItem(row, 0, QTableWidgetItem(alert['type']))
            self.alerts_table.setItem(row, 1, QTableWidgetItem(alert['title']))

            # تلوين الحالة
            status_item = QTableWidgetItem(alert['status'])
            if alert['status'] == 'نشط':
                status_item.setBackground(QColor('#ffebee'))
                status_item.setForeground(QColor('#c62828'))
            else:
                status_item.setBackground(QColor('#e8f5e8'))
                status_item.setForeground(QColor('#2e7d32'))

            self.alerts_table.setItem(row, 2, status_item)
            self.alerts_table.setItem(row, 3, QTableWidgetItem(alert['date'].strftime('%Y-%m-%d %H:%M')))

            # تلوين الأولوية
            priority_item = QTableWidgetItem(alert['priority'])
            if alert['priority'] == 'عالية':
                priority_item.setBackground(QColor('#ffebee'))
                priority_item.setForeground(QColor('#c62828'))
            elif alert['priority'] == 'متوسطة':
                priority_item.setBackground(QColor('#fff3e0'))
                priority_item.setForeground(QColor('#ef6c00'))
            else:
                priority_item.setBackground(QColor('#e8f5e8'))
                priority_item.setForeground(QColor('#2e7d32'))

            self.alerts_table.setItem(row, 4, priority_item)

        # حفظ البيانات للاستخدام لاحقاً
        self.current_alerts = alerts

    def update_alerts_statistics(self, alerts):
        """تحديث إحصائيات التنبيهات"""
        total = len(alerts)
        active = len([a for a in alerts if a['status'] == 'نشط'])
        read = len([a for a in alerts if a['status'] == 'مقروء'])
        high_priority = len([a for a in alerts if a['priority'] == 'عالية'])

        self.total_alerts_card.value_label.setText(str(total))
        self.active_alerts_card.value_label.setText(str(active))
        self.read_alerts_card.value_label.setText(str(read))
        self.high_priority_card.value_label.setText(str(high_priority))

    def on_alert_selected(self):
        """معالجة اختيار تنبيه"""
        try:
            current_row = self.alerts_table.currentRow()
            if current_row >= 0 and hasattr(self, 'current_alerts'):
                alert = self.current_alerts[current_row]

                # عرض تفاصيل التنبيه
                details_text = f"📋 تفاصيل التنبيه\n"
                details_text += "=" * 30 + "\n\n"
                details_text += f"🏷️ النوع: {alert['type']}\n"
                details_text += f"📝 العنوان: {alert['title']}\n"
                details_text += f"🔄 الحالة: {alert['status']}\n"
                details_text += f"📅 التاريخ: {alert['date'].strftime('%Y-%m-%d %H:%M:%S')}\n"
                details_text += f"⚡ الأولوية: {alert['priority']}\n\n"
                details_text += f"📄 التفاصيل:\n{alert['details']}"

                self.alert_details_text.setText(details_text)

        except Exception as e:
            print(f"خطأ في اختيار التنبيه: {e}")

    def update_alerts_list(self):
        """تحديث قائمة التنبيهات حسب الفلتر"""
        if hasattr(self, 'current_alerts'):
            filter_type = self.alerts_filter.currentText()

            if filter_type == "جميع التنبيهات":
                filtered_alerts = self.current_alerts
            elif filter_type == "التنبيهات النشطة":
                filtered_alerts = [a for a in self.current_alerts if a['status'] == 'نشط']
            elif filter_type == "تنبيهات التأخير":
                filtered_alerts = [a for a in self.current_alerts if 'تأخير' in a['type']]
            elif filter_type == "تنبيهات الوصول":
                filtered_alerts = [a for a in self.current_alerts if 'وصول' in a['type']]
            elif filter_type == "تنبيهات مالية":
                filtered_alerts = [a for a in self.current_alerts if 'مالي' in a['type']]
            else:
                filtered_alerts = self.current_alerts

            self.populate_alerts_table(filtered_alerts)

    def show_alert_context_menu(self, position):
        """عرض قائمة السياق للتنبيهات"""
        if self.alerts_table.itemAt(position):
            context_menu = QMenu(self)

            mark_read_action = context_menu.addAction("✅ تحديد كمقروء")
            mark_read_action.triggered.connect(self.mark_alert_as_read)

            context_menu.addSeparator()

            delete_action = context_menu.addAction("🗑️ حذف التنبيه")
            delete_action.triggered.connect(self.delete_selected_alert)

            snooze_action = context_menu.addAction("⏰ تأجيل التنبيه")
            snooze_action.triggered.connect(self.snooze_alert)

            context_menu.exec_(self.alerts_table.mapToGlobal(position))

    def mark_alert_as_read(self):
        """تحديد التنبيه كمقروء"""
        try:
            current_row = self.alerts_table.currentRow()
            if current_row >= 0 and hasattr(self, 'current_alerts'):
                alert = self.current_alerts[current_row]

                # تحديث حالة التنبيه
                alert['status'] = 'مقروء'

                # تحديث الجدول
                status_item = self.alerts_table.item(current_row, 2)
                if status_item:
                    status_item.setText('مقروء')
                    status_item.setBackground(QColor("#d4edda"))  # لون أخضر فاتح

                # تحديث الإحصائيات
                self.update_alerts_statistics(self.current_alerts)

                QMessageBox.information(
                    self, "تحديد كمقروء",
                    "✅ تم تحديد التنبيه كمقروء بنجاح"
                )
            else:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار تنبيه أولاً")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث التنبيه: {str(e)}")

    def delete_selected_alert(self):
        """حذف التنبيه المحدد"""
        try:
            current_row = self.alerts_table.currentRow()
            if current_row >= 0 and hasattr(self, 'current_alerts'):
                alert = self.current_alerts[current_row]

                # تأكيد الحذف
                reply = QMessageBox.question(
                    self, "تأكيد الحذف",
                    f"هل أنت متأكد من حذف التنبيه:\n{alert['title']}؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    # حذف التنبيه من القائمة
                    del self.current_alerts[current_row]

                    # تحديث الجدول
                    self.populate_alerts_table(self.current_alerts)
                    self.update_alerts_statistics(self.current_alerts)

                    QMessageBox.information(
                        self, "تم الحذف",
                        "🗑️ تم حذف التنبيه بنجاح"
                    )
            else:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار تنبيه أولاً")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف التنبيه: {str(e)}")

    def snooze_alert(self):
        """تأجيل التنبيه"""
        try:
            current_row = self.alerts_table.currentRow()
            if current_row >= 0 and hasattr(self, 'current_alerts'):
                alert = self.current_alerts[current_row]

                # خيارات التأجيل
                snooze_options = [
                    "15 دقيقة",
                    "30 دقيقة",
                    "ساعة واحدة",
                    "ساعتان",
                    "4 ساعات",
                    "يوم واحد"
                ]

                snooze_time, ok = QInputDialog.getItem(
                    self, "تأجيل التنبيه",
                    "اختر فترة التأجيل:",
                    snooze_options, 0, False
                )

                if ok and snooze_time:
                    from datetime import datetime, timedelta

                    # حساب وقت التأجيل
                    snooze_minutes = {
                        "15 دقيقة": 15,
                        "30 دقيقة": 30,
                        "ساعة واحدة": 60,
                        "ساعتان": 120,
                        "4 ساعات": 240,
                        "يوم واحد": 1440
                    }

                    minutes = snooze_minutes.get(snooze_time, 60)
                    new_date = datetime.now() + timedelta(minutes=minutes)

                    # تحديث تاريخ التنبيه
                    alert['date'] = new_date
                    alert['status'] = 'مؤجل'

                    # تحديث الجدول
                    self.populate_alerts_table(self.current_alerts)
                    self.update_alerts_statistics(self.current_alerts)

                    QMessageBox.information(
                        self, "تم التأجيل",
                        f"⏰ تم تأجيل التنبيه لمدة {snooze_time}"
                    )
            else:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار تنبيه أولاً")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تأجيل التنبيه: {str(e)}")

    def add_new_alert(self):
        """إضافة تنبيه جديد"""
        QMessageBox.information(
            self, "إضافة تنبيه",
            "➕ إضافة تنبيه جديد\n\n"
            "هذه الميزة ستتضمن:\n"
            "• إنشاء تنبيهات مخصصة\n"
            "• تحديد مواعيد التذكير\n"
            "• ربط التنبيهات بالشحنات\n"
            "• إعدادات الأولوية\n\n"
            "🚧 قيد التطوير..."
        )

    def show_alert_settings(self):
        """عرض إعدادات التنبيهات"""
        QMessageBox.information(
            self, "إعدادات التنبيهات",
            "⚙️ إعدادات التنبيهات\n\n"
            "هذه الميزة ستتضمن:\n"
            "• إعدادات الإشعارات\n"
            "• تخصيص أنواع التنبيهات\n"
            "• إعدادات البريد الإلكتروني\n"
            "• تكرار التنبيهات\n\n"
            "🚧 قيد التطوير..."
        )

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        # إيقاف المؤقتات
        self.auto_refresh_timer.stop()
        self.search_timer.stop()

        # إغلاق النافذة
        event.accept()
