# -*- coding: utf-8 -*-
"""
نافذة إنشاء معاملة جديدة شاملة ومتقدمة
Comprehensive New Transaction Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QDoubleSpinBox,
                               QPushButton, QGroupBox, QTextEdit, QCheckBox,
                               QMessageBox, QFrame, QTabWidget, QWidget,
                               QDateTimeEdit, QSpinBox, QProgressBar, QTableWidget,
                               QTableWidgetItem, QHeaderView, QAbstractItemView,
                               QSplitter, QScrollArea, QCalendarWidget)
from PySide6.QtCore import Qt, QDateTime, Signal, QTimer
from PySide6.QtGui import QFont, QDoubleValidator, QRegularExpressionValidator, QColor

import sqlite3
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
import uuid

from ...utils.arabic_support import reshape_arabic_text

class NewTransactionDialog(QDialog):
    """نافذة إنشاء معاملة جديدة شاملة ومتقدمة"""
    
    transaction_created = Signal(int)  # إشارة إنشاء المعاملة
    
    def __init__(self, parent=None, account_id=None):
        super().__init__(parent)
        self.setWindowTitle("إنشاء معاملة جديدة - ProShipment")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        self.setModal(True)
        
        # متغيرات النافذة
        self.account_id = account_id
        self.accounts_data = []
        self.currencies_data = []
        self.selected_account_data = None
        self.transaction_fee = 0.0
        self.exchange_rate = 1.0
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        
        # تحميل البيانات
        self.load_initial_data()
        
        # تحديد الحساب إذا تم تمريره
        if self.account_id:
            self.select_account_by_id(self.account_id)
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        self.create_header_section(layout)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب المعاملة الأساسية
        basic_tab = self.create_basic_transaction_tab()
        self.tabs.addTab(basic_tab, "المعاملة الأساسية")
        
        # تبويب التفاصيل المتقدمة
        advanced_tab = self.create_advanced_details_tab()
        self.tabs.addTab(advanced_tab, "التفاصيل المتقدمة")
        
        # تبويب المراجعة والتأكيد
        review_tab = self.create_review_tab()
        self.tabs.addTab(review_tab, "المراجعة والتأكيد")
        
        layout.addWidget(self.tabs)
        
        # أزرار الإجراءات
        self.create_action_buttons(layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3b82f6, stop:1 #1d4ed8);
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة المعاملة
        icon_label = QLabel("💰")
        icon_label.setStyleSheet("font-size: 36px; color: white;")
        header_layout.addWidget(icon_label)
        
        # معلومات العنوان
        info_layout = QVBoxLayout()
        
        title_label = QLabel("إنشاء معاملة جديدة")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        
        subtitle_label = QLabel("إدارة المعاملات المالية مع التحقق التلقائي والأمان المتقدم")
        subtitle_label.setFont(QFont("Arial", 11))
        subtitle_label.setStyleSheet("color: #dbeafe;")
        
        info_layout.addWidget(title_label)
        info_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        # معلومات سريعة
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        stats_layout = QVBoxLayout(stats_frame)
        
        self.transaction_number_label = QLabel(f"رقم المعاملة\n{self.generate_transaction_number()}")
        self.transaction_date_label = QLabel(f"التاريخ\n{datetime.now().strftime('%Y-%m-%d %H:%M')}")
        
        for label in [self.transaction_number_label, self.transaction_date_label]:
            label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    text-align: center;
                    font-size: 11px;
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(label)
        
        header_layout.addWidget(stats_frame)
        layout.addWidget(header_frame)
        
    def create_basic_transaction_tab(self):
        """إنشاء تبويب المعاملة الأساسية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات الحساب
        account_group = QGroupBox("معلومات الحساب")
        account_layout = QGridLayout(account_group)
        
        # اختيار الحساب
        account_layout.addWidget(QLabel("الحساب: *"), 0, 0)
        self.account_combo = QComboBox()
        self.account_combo.setMinimumWidth(300)
        account_layout.addWidget(self.account_combo, 0, 1)
        
        # معلومات الحساب المحدد
        account_layout.addWidget(QLabel("المورد:"), 1, 0)
        self.supplier_label = QLabel("-")
        self.supplier_label.setStyleSheet("background-color: #f3f4f6; padding: 5px; border-radius: 3px;")
        account_layout.addWidget(self.supplier_label, 1, 1)
        
        account_layout.addWidget(QLabel("العملة:"), 2, 0)
        self.currency_label = QLabel("-")
        self.currency_label.setStyleSheet("background-color: #f3f4f6; padding: 5px; border-radius: 3px;")
        account_layout.addWidget(self.currency_label, 2, 1)
        
        account_layout.addWidget(QLabel("الرصيد الحالي:"), 3, 0)
        self.current_balance_label = QLabel("0.00")
        self.current_balance_label.setStyleSheet("background-color: #f3f4f6; padding: 5px; border-radius: 3px; font-weight: bold; color: #059669;")
        account_layout.addWidget(self.current_balance_label, 3, 1)
        
        layout.addWidget(account_group)
        
        # تفاصيل المعاملة
        transaction_group = QGroupBox("تفاصيل المعاملة")
        transaction_layout = QGridLayout(transaction_group)
        
        # نوع المعاملة
        transaction_layout.addWidget(QLabel("نوع المعاملة: *"), 0, 0)
        self.transaction_type_combo = QComboBox()
        self.transaction_type_combo.addItems(["إيداع", "سحب", "تحويل داخلي", "تحويل خارجي", "رسوم", "فوائد", "تعديل"])
        transaction_layout.addWidget(self.transaction_type_combo, 0, 1)
        
        # المبلغ
        transaction_layout.addWidget(QLabel("المبلغ: *"), 1, 0)
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0.01, *********.99)
        self.amount_input.setDecimals(2)
        self.amount_input.setMinimumWidth(200)
        transaction_layout.addWidget(self.amount_input, 1, 1)
        
        # العملة
        transaction_layout.addWidget(QLabel("العملة:"), 2, 0)
        self.transaction_currency_combo = QComboBox()
        transaction_layout.addWidget(self.transaction_currency_combo, 2, 1)
        
        # سعر الصرف (للعملات المختلفة)
        transaction_layout.addWidget(QLabel("سعر الصرف:"), 3, 0)
        self.exchange_rate_input = QDoubleSpinBox()
        self.exchange_rate_input.setRange(0.0001, 1000.0)
        self.exchange_rate_input.setDecimals(4)
        self.exchange_rate_input.setValue(1.0)
        self.exchange_rate_input.setEnabled(False)
        transaction_layout.addWidget(self.exchange_rate_input, 3, 1)
        
        layout.addWidget(transaction_group)
        
        # الوصف والملاحظات
        description_group = QGroupBox("الوصف والملاحظات")
        description_layout = QVBoxLayout(description_group)
        
        description_layout.addWidget(QLabel("وصف المعاملة: *"))
        self.description_input = QLineEdit()
        self.description_input.setPlaceholderText("أدخل وصف المعاملة...")
        description_layout.addWidget(self.description_input)
        
        description_layout.addWidget(QLabel("ملاحظات إضافية:"))
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("ملاحظات اختيارية...")
        description_layout.addWidget(self.notes_input)
        
        layout.addWidget(description_group)
        
        # معاينة المعاملة
        preview_group = QGroupBox("معاينة المعاملة")
        preview_layout = QGridLayout(preview_group)
        
        preview_layout.addWidget(QLabel("المبلغ النهائي:"), 0, 0)
        self.final_amount_label = QLabel("0.00")
        self.final_amount_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #3b82f6;")
        preview_layout.addWidget(self.final_amount_label, 0, 1)
        
        preview_layout.addWidget(QLabel("الرسوم:"), 1, 0)
        self.fees_label = QLabel("0.00")
        preview_layout.addWidget(self.fees_label, 1, 1)
        
        preview_layout.addWidget(QLabel("الرصيد بعد المعاملة:"), 2, 0)
        self.balance_after_label = QLabel("0.00")
        self.balance_after_label.setStyleSheet("font-weight: bold;")
        preview_layout.addWidget(self.balance_after_label, 2, 1)
        
        layout.addWidget(preview_group)
        layout.addStretch()
        
        return tab
        
    def create_advanced_details_tab(self):
        """إنشاء تبويب التفاصيل المتقدمة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # إعدادات المعاملة
        settings_group = QGroupBox("إعدادات المعاملة")
        settings_layout = QGridLayout(settings_group)
        
        # تاريخ ووقت المعاملة
        settings_layout.addWidget(QLabel("تاريخ ووقت المعاملة:"), 0, 0)
        self.transaction_datetime = QDateTimeEdit()
        self.transaction_datetime.setDateTime(QDateTime.currentDateTime())
        self.transaction_datetime.setCalendarPopup(True)
        settings_layout.addWidget(self.transaction_datetime, 0, 1)
        
        # أولوية المعاملة
        settings_layout.addWidget(QLabel("أولوية المعاملة:"), 1, 0)
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["عادية", "عالية", "عاجلة"])
        settings_layout.addWidget(self.priority_combo, 1, 1)
        
        # رقم المرجع
        settings_layout.addWidget(QLabel("رقم المرجع:"), 2, 0)
        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("رقم مرجع اختياري...")
        settings_layout.addWidget(self.reference_input, 2, 1)
        
        # فئة المعاملة
        settings_layout.addWidget(QLabel("فئة المعاملة:"), 3, 0)
        self.category_combo = QComboBox()
        self.category_combo.addItems(["عامة", "تشغيلية", "استثمارية", "طارئة", "صيانة"])
        settings_layout.addWidget(self.category_combo, 3, 1)
        
        layout.addWidget(settings_group)
        
        # إعدادات الأمان
        security_group = QGroupBox("إعدادات الأمان")
        security_layout = QGridLayout(security_group)
        
        # تتطلب موافقة
        self.require_approval_checkbox = QCheckBox("تتطلب موافقة إضافية")
        security_layout.addWidget(self.require_approval_checkbox, 0, 0, 1, 2)
        
        # تشفير البيانات
        self.encrypt_data_checkbox = QCheckBox("تشفير بيانات المعاملة")
        self.encrypt_data_checkbox.setChecked(True)
        security_layout.addWidget(self.encrypt_data_checkbox, 1, 0, 1, 2)
        
        # إشعار SMS
        self.sms_notification_checkbox = QCheckBox("إرسال إشعار SMS")
        security_layout.addWidget(self.sms_notification_checkbox, 2, 0, 1, 2)
        
        # إشعار بريد إلكتروني
        self.email_notification_checkbox = QCheckBox("إرسال إشعار بريد إلكتروني")
        security_layout.addWidget(self.email_notification_checkbox, 3, 0, 1, 2)
        
        layout.addWidget(security_group)
        
        # معلومات إضافية
        additional_group = QGroupBox("معلومات إضافية")
        additional_layout = QGridLayout(additional_group)
        
        # الغرض من المعاملة
        additional_layout.addWidget(QLabel("الغرض من المعاملة:"), 0, 0)
        self.purpose_combo = QComboBox()
        self.purpose_combo.addItems([
            "دفع مستحقات", "استلام دفعة", "تسوية حساب", 
            "رد مبلغ", "تحويل رصيد", "رسوم خدمة", "أخرى"
        ])
        additional_layout.addWidget(self.purpose_combo, 0, 1)
        
        # المستفيد (للتحويلات)
        additional_layout.addWidget(QLabel("المستفيد:"), 1, 0)
        self.beneficiary_input = QLineEdit()
        self.beneficiary_input.setPlaceholderText("اسم المستفيد (للتحويلات)")
        additional_layout.addWidget(self.beneficiary_input, 1, 1)
        
        # رقم حساب المستفيد
        additional_layout.addWidget(QLabel("رقم حساب المستفيد:"), 2, 0)
        self.beneficiary_account_input = QLineEdit()
        self.beneficiary_account_input.setPlaceholderText("رقم حساب المستفيد")
        additional_layout.addWidget(self.beneficiary_account_input, 2, 1)
        
        layout.addWidget(additional_group)
        layout.addStretch()
        
        return tab

    def create_review_tab(self):
        """إنشاء تبويب المراجعة والتأكيد"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # ملخص المعاملة
        summary_group = QGroupBox("ملخص المعاملة")
        summary_layout = QVBoxLayout(summary_group)

        self.summary_text = QTextEdit()
        self.summary_text.setReadOnly(True)
        self.summary_text.setMaximumHeight(200)
        self.summary_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 10px;
                font-family: 'Courier New', monospace;
            }
        """)
        summary_layout.addWidget(self.summary_text)

        layout.addWidget(summary_group)

        # التحقق من الصحة
        validation_group = QGroupBox("التحقق من الصحة")
        validation_layout = QVBoxLayout(validation_group)

        self.validation_results = QTextEdit()
        self.validation_results.setReadOnly(True)
        self.validation_results.setMaximumHeight(150)
        validation_layout.addWidget(self.validation_results)

        # زر التحقق
        validate_btn = QPushButton("🔍 تحقق من صحة البيانات")
        validate_btn.clicked.connect(self.validate_transaction)
        validation_layout.addWidget(validate_btn)

        layout.addWidget(validation_group)

        # تأكيد المعاملة
        confirmation_group = QGroupBox("تأكيد المعاملة")
        confirmation_layout = QVBoxLayout(confirmation_group)

        self.confirm_checkbox = QCheckBox("أؤكد صحة جميع البيانات المدخلة")
        confirmation_layout.addWidget(self.confirm_checkbox)

        self.terms_checkbox = QCheckBox("أوافق على الشروط والأحكام")
        confirmation_layout.addWidget(self.terms_checkbox)

        layout.addWidget(confirmation_group)
        layout.addStretch()

        return tab

    def create_action_buttons(self, layout):
        """إنشاء أزرار الإجراءات"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        buttons_layout.addWidget(self.progress_bar)

        buttons_layout.addStretch()

        # زر المعاينة
        preview_btn = QPushButton("👁️ معاينة")
        preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #4b5563; }
        """)
        preview_btn.clicked.connect(self.preview_transaction)
        buttons_layout.addWidget(preview_btn)

        # زر الحفظ كمسودة
        draft_btn = QPushButton("📝 حفظ كمسودة")
        draft_btn.setStyleSheet("""
            QPushButton {
                background-color: #f59e0b;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #d97706; }
        """)
        draft_btn.clicked.connect(self.save_as_draft)
        buttons_layout.addWidget(draft_btn)

        # زر تنفيذ المعاملة
        execute_btn = QPushButton("✅ تنفيذ المعاملة")
        execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #059669; }
        """)
        execute_btn.clicked.connect(self.execute_transaction)
        buttons_layout.addWidget(execute_btn)

        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #dc2626; }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addWidget(buttons_frame)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # تحديث معلومات الحساب عند التغيير
        self.account_combo.currentTextChanged.connect(self.update_account_info)

        # تحديث المعاينة عند تغيير البيانات
        self.amount_input.valueChanged.connect(self.update_preview)
        self.transaction_type_combo.currentTextChanged.connect(self.update_preview)
        self.transaction_currency_combo.currentTextChanged.connect(self.update_currency_settings)

        # التحقق من صحة البيانات
        self.amount_input.valueChanged.connect(self.validate_form)
        self.account_combo.currentTextChanged.connect(self.validate_form)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل الحسابات
            cursor.execute("""
                SELECT sa.id, sa.account_number, s.name as supplier_name,
                       c.code as currency_code, sa.current_balance, sa.available_balance
                FROM supplier_accounts sa
                LEFT JOIN suppliers s ON sa.supplier_id = s.id
                LEFT JOIN currencies c ON sa.currency_id = c.id
                WHERE sa.is_active = 1
                ORDER BY s.name
            """)
            self.accounts_data = cursor.fetchall()

            self.account_combo.addItem("اختر الحساب...", None)
            for account in self.accounts_data:
                display_text = f"{account[1]} - {account[2]} ({account[4]:,.2f} {account[3]})"
                self.account_combo.addItem(display_text, account[0])

            # تحميل العملات
            cursor.execute("SELECT id, code, name FROM currencies WHERE is_active = 1 ORDER BY name")
            self.currencies_data = cursor.fetchall()

            for currency in self.currencies_data:
                self.transaction_currency_combo.addItem(f"{currency[1]} - {currency[2]}", currency[0])

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def select_account_by_id(self, account_id):
        """تحديد حساب بواسطة المعرف"""
        for i in range(self.account_combo.count()):
            if self.account_combo.itemData(i) == account_id:
                self.account_combo.setCurrentIndex(i)
                break

    def update_account_info(self):
        """تحديث معلومات الحساب المحددة"""
        account_id = self.account_combo.currentData()
        if account_id:
            # البحث عن بيانات الحساب
            for account in self.accounts_data:
                if account[0] == account_id:
                    self.selected_account_data = account
                    self.supplier_label.setText(account[2] or "-")
                    self.currency_label.setText(account[3] or "-")
                    self.current_balance_label.setText(f"{account[4]:,.2f}")

                    # تعيين العملة الافتراضية
                    currency_code = account[3]
                    for i in range(self.transaction_currency_combo.count()):
                        if currency_code in self.transaction_currency_combo.itemText(i):
                            self.transaction_currency_combo.setCurrentIndex(i)
                            break
                    break
        else:
            self.selected_account_data = None
            self.supplier_label.setText("-")
            self.currency_label.setText("-")
            self.current_balance_label.setText("0.00")

        self.update_preview()

    def update_currency_settings(self):
        """تحديث إعدادات العملة"""
        if not self.selected_account_data:
            return

        account_currency = self.selected_account_data[3]
        selected_currency_text = self.transaction_currency_combo.currentText()
        selected_currency_code = selected_currency_text.split(" - ")[0] if " - " in selected_currency_text else ""

        # تفعيل سعر الصرف إذا كانت العملة مختلفة
        if account_currency != selected_currency_code:
            self.exchange_rate_input.setEnabled(True)
            # يمكن هنا جلب سعر الصرف من API أو قاعدة البيانات
            self.exchange_rate_input.setValue(1.0)  # قيمة افتراضية
        else:
            self.exchange_rate_input.setEnabled(False)
            self.exchange_rate_input.setValue(1.0)

        self.update_preview()

    def update_preview(self):
        """تحديث معاينة المعاملة"""
        if not self.selected_account_data:
            return

        amount = self.amount_input.value()
        transaction_type = self.transaction_type_combo.currentText()
        exchange_rate = self.exchange_rate_input.value()

        # حساب المبلغ النهائي
        final_amount = amount * exchange_rate

        # حساب الرسوم (مثال: 0.5% للتحويلات الخارجية)
        if transaction_type == "تحويل خارجي":
            self.transaction_fee = final_amount * 0.005
        elif transaction_type == "تحويل داخلي":
            self.transaction_fee = 5.0  # رسم ثابت
        else:
            self.transaction_fee = 0.0

        # حساب الرصيد بعد المعاملة
        current_balance = self.selected_account_data[4]
        if transaction_type in ["سحب", "تحويل داخلي", "تحويل خارجي", "رسوم"]:
            balance_after = current_balance - (final_amount + self.transaction_fee)
        else:
            balance_after = current_balance + final_amount

        # تحديث التسميات
        self.final_amount_label.setText(f"{final_amount:,.2f}")
        self.fees_label.setText(f"{self.transaction_fee:,.2f}")
        self.balance_after_label.setText(f"{balance_after:,.2f}")

        # تلوين الرصيد بعد المعاملة
        if balance_after < 0:
            self.balance_after_label.setStyleSheet("font-weight: bold; color: #ef4444;")
        else:
            self.balance_after_label.setStyleSheet("font-weight: bold; color: #059669;")

    def validate_form(self):
        """التحقق من صحة النموذج"""
        account_id = self.account_combo.currentData()
        amount = self.amount_input.value()

        is_valid = (account_id is not None and amount > 0)

        # تفعيل/تعطيل أزرار التنفيذ
        for button in self.findChildren(QPushButton):
            if "تنفيذ المعاملة" in button.text():
                button.setEnabled(is_valid)

    def validate_transaction(self):
        """التحقق من صحة المعاملة"""
        validation_results = []

        # التحقق من الحساب
        if not self.account_combo.currentData():
            validation_results.append("❌ يجب اختيار حساب")
        else:
            validation_results.append("✅ تم اختيار حساب صحيح")

        # التحقق من المبلغ
        amount = self.amount_input.value()
        if amount <= 0:
            validation_results.append("❌ يجب إدخال مبلغ أكبر من صفر")
        else:
            validation_results.append("✅ المبلغ صحيح")

        # التحقق من الرصيد
        if self.selected_account_data:
            transaction_type = self.transaction_type_combo.currentText()
            current_balance = self.selected_account_data[4]
            final_amount = amount * self.exchange_rate_input.value()

            if transaction_type in ["سحب", "تحويل داخلي", "تحويل خارجي", "رسوم"]:
                if current_balance < (final_amount + self.transaction_fee):
                    validation_results.append("⚠️ الرصيد غير كافي")
                else:
                    validation_results.append("✅ الرصيد كافي")

        # التحقق من الوصف
        if not self.description_input.text().strip():
            validation_results.append("❌ يجب إدخال وصف للمعاملة")
        else:
            validation_results.append("✅ تم إدخال وصف المعاملة")

        self.validation_results.setPlainText("\n".join(validation_results))

    def preview_transaction(self):
        """معاينة المعاملة"""
        self.update_summary()
        self.tabs.setCurrentIndex(2)  # الانتقال إلى تبويب المراجعة

    def update_summary(self):
        """تحديث ملخص المعاملة"""
        if not self.selected_account_data:
            return

        summary = f"""
========================================
ملخص المعاملة
========================================

رقم المعاملة: {self.transaction_number_label.text().split('\\n')[1]}
التاريخ: {self.transaction_datetime.dateTime().toString('yyyy-MM-dd hh:mm')}

معلومات الحساب:
- رقم الحساب: {self.selected_account_data[1]}
- المورد: {self.selected_account_data[2]}
- العملة: {self.selected_account_data[3]}
- الرصيد الحالي: {self.selected_account_data[4]:,.2f}

تفاصيل المعاملة:
- النوع: {self.transaction_type_combo.currentText()}
- المبلغ: {self.amount_input.value():,.2f}
- العملة: {self.transaction_currency_combo.currentText()}
- سعر الصرف: {self.exchange_rate_input.value():.4f}
- المبلغ النهائي: {float(self.final_amount_label.text().replace(',', '')):,.2f}
- الرسوم: {self.transaction_fee:,.2f}
- الرصيد بعد المعاملة: {self.balance_after_label.text()}

الوصف: {self.description_input.text()}
الملاحظات: {self.notes_input.toPlainText()}

الأولوية: {self.priority_combo.currentText()}
الفئة: {self.category_combo.currentText()}
الغرض: {self.purpose_combo.currentText()}

========================================
        """

        self.summary_text.setPlainText(summary.strip())

    def generate_transaction_number(self):
        """توليد رقم معاملة فريد"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"TXN{timestamp}"

    def save_as_draft(self):
        """حفظ المعاملة كمسودة"""
        QMessageBox.information(self, "تم", "تم حفظ المعاملة كمسودة")

    def execute_transaction(self):
        """تنفيذ المعاملة"""
        if not self.validate_transaction_data():
            return

        if not self.confirm_checkbox.isChecked():
            QMessageBox.warning(self, "تحذير", "يجب تأكيد صحة البيانات")
            return

        if not self.terms_checkbox.isChecked():
            QMessageBox.warning(self, "تحذير", "يجب الموافقة على الشروط والأحكام")
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)

            transaction_id = self.save_transaction_to_database()

            if transaction_id:
                self.transaction_created.emit(transaction_id)
                QMessageBox.information(self, "نجح", "تم تنفيذ المعاملة بنجاح")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في تنفيذ المعاملة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تنفيذ المعاملة: {str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def validate_transaction_data(self):
        """التحقق من صحة بيانات المعاملة"""
        if not self.account_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار الحساب")
            return False

        if self.amount_input.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
            return False

        if not self.description_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال وصف المعاملة")
            return False

        return True

    def save_transaction_to_database(self):
        """حفظ المعاملة في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء جدول المعاملات إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS account_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    account_id INTEGER NOT NULL,
                    transaction_type TEXT NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    balance_after DECIMAL(15,2) NOT NULL,
                    description TEXT,
                    reference_number TEXT,
                    status TEXT DEFAULT 'completed',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    exchange_rate DECIMAL(10,4) DEFAULT 1.0,
                    fees DECIMAL(15,2) DEFAULT 0.0,
                    priority TEXT DEFAULT 'عادية',
                    category TEXT DEFAULT 'عامة',
                    purpose TEXT DEFAULT 'أخرى',
                    beneficiary_name TEXT,
                    beneficiary_account TEXT,
                    notes TEXT,
                    FOREIGN KEY (account_id) REFERENCES supplier_accounts(id)
                )
            """)

            # حساب المبلغ النهائي والرصيد الجديد
            amount = self.amount_input.value()
            exchange_rate = self.exchange_rate_input.value()
            final_amount = amount * exchange_rate
            transaction_type = self.transaction_type_combo.currentText()

            current_balance = self.selected_account_data[4]
            if transaction_type in ["سحب", "تحويل داخلي", "تحويل خارجي", "رسوم"]:
                new_balance = current_balance - (final_amount + self.transaction_fee)
            else:
                new_balance = current_balance + final_amount

            # إدراج المعاملة
            insert_query = """
                INSERT INTO account_transactions (
                    account_id, transaction_type, amount, balance_after,
                    description, reference_number, status, created_at,
                    exchange_rate, fees, priority, category, purpose,
                    beneficiary_name, beneficiary_account, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            cursor.execute(insert_query, (
                self.account_combo.currentData(),
                transaction_type,
                final_amount,
                new_balance,
                self.description_input.text(),
                self.reference_input.text() or self.generate_transaction_number(),
                "completed",
                self.transaction_datetime.dateTime().toString("yyyy-MM-dd hh:mm:ss"),
                exchange_rate,
                self.transaction_fee,
                self.priority_combo.currentText(),
                self.category_combo.currentText(),
                self.purpose_combo.currentText(),
                self.beneficiary_input.text(),
                self.beneficiary_account_input.text(),
                self.notes_input.toPlainText()
            ))

            transaction_id = cursor.lastrowid

            # تحديث رصيد الحساب
            cursor.execute("""
                UPDATE supplier_accounts
                SET current_balance = ?, available_balance = ?, last_transaction_date = ?
                WHERE id = ?
            """, (new_balance, new_balance, datetime.now(), self.account_combo.currentData()))

            conn.commit()
            conn.close()

            return transaction_id

        except Exception as e:
            if conn:
                conn.rollback()
                conn.close()
            raise e
