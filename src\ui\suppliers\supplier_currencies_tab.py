# -*- coding: utf-8 -*-
"""
تبويب ربط الموردين بالعملات
Supplier Currencies Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QComboBox, QCheckBox,
                               QTextEdit, QDoubleSpinBox, QAbstractItemView, QSplitter)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QKeySequence, QShortcut

from ...database.database_manager import DatabaseManager
from ...database.models import Supplier, Currency, SupplierCurrency
from .advanced_supplier_search_dialog import AdvancedSupplierSearchDialog
from .multi_currency_selection_dialog import MultiCurrencySelectionDialog


class SupplierCurrenciesTab(QWidget):
    """تبويب ربط الموردين بالعملات"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_supplier_currency_id = None
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        self.selected_currencies_data = []  # لحفظ العملات المحددة
        self.setup_ui()
        self.setup_shortcuts()
        self.load_data()

    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # اختصار F9 لفتح البحث المتقدم
        self.f9_shortcut = QShortcut(QKeySequence("F9"), self)
        self.f9_shortcut.activated.connect(self.open_advanced_search)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # إنشاء splitter للتحكم في الأحجام
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - نموذج الإدخال
        form_widget = self.create_form_section()
        form_widget.setMaximumWidth(450)
        form_widget.setMinimumWidth(400)
        
        # الجانب الأيمن - الجدول
        table_widget = self.create_table_section()
        
        splitter.addWidget(form_widget)
        splitter.addWidget(table_widget)
        splitter.setStretchFactor(0, 0)  # النموذج لا يتمدد
        splitter.setStretchFactor(1, 1)  # الجدول يتمدد
        
        main_layout.addWidget(splitter)
    
    def create_form_section(self):
        """إنشاء قسم النموذج"""
        form_group = QGroupBox("ربط مورد بعملة")
        form_layout = QVBoxLayout(form_group)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(15, 15, 15, 15)

        # نموذج الإدخال
        input_form = QFormLayout()
        input_form.setSpacing(10)

        # البحث عن المورد مع زر البحث المتقدم
        supplier_search_layout = QHBoxLayout()

        self.supplier_search = QLineEdit()
        self.supplier_search.setPlaceholderText("ابحث عن المورد... (F9 للبحث المتقدم)")
        self.supplier_search.textChanged.connect(self.on_supplier_search_changed)
        self.supplier_search.setMinimumHeight(35)
        self.supplier_search.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)

        # زر البحث المتقدم
        self.advanced_search_button = QPushButton("🔍")
        self.advanced_search_button.setToolTip("البحث المتقدم (F9)")
        self.advanced_search_button.clicked.connect(self.open_advanced_search)
        self.advanced_search_button.setMaximumWidth(40)
        self.advanced_search_button.setMinimumHeight(35)
        self.advanced_search_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)

        supplier_search_layout.addWidget(self.supplier_search)
        supplier_search_layout.addWidget(self.advanced_search_button)

        input_form.addRow("البحث عن المورد:", supplier_search_layout)

        # قائمة الموردين
        self.supplier_combo = QComboBox()
        self.supplier_combo.setMinimumHeight(35)
        self.supplier_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        input_form.addRow("المورد:", self.supplier_combo)

        # العملات المتعددة
        currencies_layout = QHBoxLayout()

        self.currencies_display = QLineEdit()
        self.currencies_display.setPlaceholderText("لم يتم اختيار عملات...")
        self.currencies_display.setReadOnly(True)
        self.currencies_display.setMinimumHeight(35)
        self.currencies_display.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
                background-color: #f8f9fa;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)

        # زر اختيار العملات
        self.select_currencies_button = QPushButton("اختيار العملات")
        self.select_currencies_button.clicked.connect(self.open_currency_selection)
        self.select_currencies_button.setMinimumHeight(35)
        self.select_currencies_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)

        currencies_layout.addWidget(self.currencies_display)
        currencies_layout.addWidget(self.select_currencies_button)

        input_form.addRow("العملات:", currencies_layout)

        # الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات عامة حول ربط المورد بالعملات...")
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        input_form.addRow("الملاحظات:", self.notes_edit)

        form_layout.addLayout(input_form)

        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.add_button = QPushButton("إضافة ربط العملات")
        self.add_button.clicked.connect(self.add_supplier_currencies)
        self.add_button.setMinimumHeight(40)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)

        self.update_button = QPushButton("تحديث")
        self.update_button.clicked.connect(self.update_supplier_currency)
        self.update_button.setEnabled(False)
        self.update_button.setMinimumHeight(40)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)

        self.clear_button = QPushButton("مسح")
        self.clear_button.clicked.connect(self.clear_form)
        self.clear_button.setMinimumHeight(40)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)

        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.clear_button)

        form_layout.addLayout(buttons_layout)
        
        return form_group
    
    def create_table_section(self):
        """إنشاء قسم الجدول"""
        table_group = QGroupBox("ربط الموردين بالعملات")
        table_layout = QVBoxLayout(table_group)
        table_layout.setSpacing(10)
        table_layout.setContentsMargins(15, 15, 15, 15)

        # الجدول
        self.currencies_table = QTableWidget()
        self.currencies_table.setColumnCount(7)
        self.currencies_table.setHorizontalHeaderLabels([
            "المورد", "العملة", "الرمز", "مفضلة", "سعر مخصص", "الملاحظات", "الحالة"
        ])
        
        # إعدادات الجدول
        self.currencies_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.currencies_table.setAlternatingRowColors(True)
        self.currencies_table.horizontalHeader().setStretchLastSection(True)
        self.currencies_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # ضبط عرض الأعمدة
        header = self.currencies_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # المورد
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # العملة
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الرمز
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # مفضلة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # سعر مخصص
        header.setSectionResizeMode(5, QHeaderView.Stretch)           # الملاحظات
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الحالة

        # ربط إشارة التحديد
        self.currencies_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        table_layout.addWidget(self.currencies_table)

        # أزرار الجدول
        table_buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_data)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        delete_button = QPushButton("حذف")
        delete_button.clicked.connect(self.delete_supplier_currency)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addStretch()
        
        table_layout.addLayout(table_buttons_layout)
        
        return table_group

    def open_advanced_search(self):
        """فتح نافذة البحث المتقدم للموردين"""
        try:
            dialog = AdvancedSupplierSearchDialog(self)
            dialog.supplier_selected.connect(self.on_supplier_selected_from_search)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة البحث: {str(e)}")

    def on_supplier_selected_from_search(self, supplier):
        """عند اختيار مورد من نافذة البحث المتقدم"""
        try:
            # تحديث حقل البحث
            self.supplier_search.setText(supplier.name)

            # تحديث القائمة المنسدلة
            self.load_suppliers()

            # اختيار المورد في القائمة
            for i in range(self.supplier_combo.count()):
                if self.supplier_combo.itemData(i) == supplier.id:
                    self.supplier_combo.setCurrentIndex(i)
                    break

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في اختيار المورد: {str(e)}")

    def open_currency_selection(self):
        """فتح نافذة اختيار العملات المتعددة"""
        try:
            dialog = MultiCurrencySelectionDialog(self, self.selected_currencies_data)
            dialog.currencies_selected.connect(self.on_currencies_selected)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة اختيار العملات: {str(e)}")

    def on_currencies_selected(self, currencies_data):
        """عند اختيار العملات من النافذة"""
        try:
            self.selected_currencies_data = currencies_data

            # تحديث النص المعروض
            if currencies_data:
                currency_names = []
                for currency in currencies_data:
                    name = currency['currency_name']
                    if currency['is_preferred']:
                        name += " (مفضلة)"
                    currency_names.append(name)

                display_text = ", ".join(currency_names)
                if len(display_text) > 50:
                    display_text = display_text[:47] + "..."

                self.currencies_display.setText(display_text)
                self.currencies_display.setToolTip(", ".join([c['currency_name'] for c in currencies_data]))
            else:
                self.currencies_display.setText("لم يتم اختيار عملات...")
                self.currencies_display.setToolTip("")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في معالجة العملات المحددة: {str(e)}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter_by(is_active=True).order_by(Supplier.name).all()

            self.supplier_combo.clear()
            self.supplier_combo.addItem("-- اختر مورد --", None)

            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل الموردين: {str(e)}")
        finally:
            session.close()

    def load_data(self):
        """تحميل بيانات ربط الموردين بالعملات"""
        # تحميل قائمة الموردين
        self.load_suppliers()

        # تحميل بيانات الجدول
        session = self.db_manager.get_session()
        try:
            supplier_currencies = session.query(SupplierCurrency).join(
                Supplier
            ).join(
                Currency
            ).filter(
                SupplierCurrency.is_active == True
            ).order_by(
                Supplier.name, Currency.name
            ).all()

            self.currencies_table.setRowCount(len(supplier_currencies))

            for row, sc in enumerate(supplier_currencies):
                # المورد
                supplier_item = QTableWidgetItem(sc.supplier.name)
                supplier_item.setData(Qt.UserRole, sc.id)
                self.currencies_table.setItem(row, 0, supplier_item)

                # العملة
                currency_item = QTableWidgetItem(sc.currency.name)
                self.currencies_table.setItem(row, 1, currency_item)

                # الرمز
                symbol_item = QTableWidgetItem(sc.currency.symbol or "")
                self.currencies_table.setItem(row, 2, symbol_item)

                # مفضلة
                preferred_item = QTableWidgetItem("✓" if sc.is_preferred else "")
                preferred_item.setTextAlignment(Qt.AlignCenter)
                self.currencies_table.setItem(row, 3, preferred_item)

                # سعر مخصص
                rate_text = ""
                if sc.exchange_rate_override and sc.exchange_rate_override > 0:
                    rate_text = f"{sc.exchange_rate_override:.4f}"
                else:
                    rate_text = f"{sc.currency.exchange_rate:.4f} (افتراضي)"
                rate_item = QTableWidgetItem(rate_text)
                self.currencies_table.setItem(row, 4, rate_item)

                # الملاحظات
                notes_item = QTableWidgetItem(sc.notes or "")
                self.currencies_table.setItem(row, 5, notes_item)

                # الحالة
                status_item = QTableWidgetItem("نشط" if sc.is_active else "غير نشط")
                self.currencies_table.setItem(row, 6, status_item)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
        finally:
            session.close()

    def on_supplier_search_changed(self):
        """عند تغيير نص البحث"""
        self.search_timer.stop()
        self.search_timer.start(300)  # انتظار 300ms قبل البحث

    def perform_search(self):
        """تنفيذ البحث الفوري"""
        search_text = self.supplier_search.text().strip().lower()

        if not search_text:
            # إذا كان البحث فارغ، أظهر جميع الموردين
            self.load_suppliers()
            return

        session = self.db_manager.get_session()
        try:
            # البحث في اسم المورد أو الكود
            suppliers = session.query(Supplier).filter(
                Supplier.is_active == True,
                (Supplier.name.ilike(f'%{search_text}%') |
                 Supplier.code.ilike(f'%{search_text}%'))
            ).order_by(Supplier.name).all()

            self.supplier_combo.clear()
            self.supplier_combo.addItem("-- اختر مورد --", None)

            for supplier in suppliers:
                display_text = supplier.name
                if supplier.code:
                    display_text += f" ({supplier.code})"
                self.supplier_combo.addItem(display_text, supplier.id)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في البحث: {str(e)}")
        finally:
            session.close()

    def add_supplier_currencies(self):
        """إضافة ربط جديد بين مورد وعملات متعددة"""
        if not self.validate_form():
            return

        supplier_id = self.supplier_combo.currentData()

        if not self.selected_currencies_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة واحدة على الأقل")
            return

        session = self.db_manager.get_session()
        try:
            # التحقق من الروابط الموجودة
            existing_currencies = []
            for currency_data in self.selected_currencies_data:
                existing = session.query(SupplierCurrency).filter_by(
                    supplier_id=supplier_id,
                    currency_id=currency_data['currency_id']
                ).first()

                if existing:
                    existing_currencies.append(currency_data['currency_name'])

            if existing_currencies:
                reply = QMessageBox.question(
                    self, "تحذير",
                    f"العملات التالية مرتبطة بالفعل بهذا المورد:\n{', '.join(existing_currencies)}\n\nهل تريد المتابعة وتحديث البيانات الموجودة؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply != QMessageBox.Yes:
                    return

            # إلغاء تفضيل العملات الأخرى إذا كان هناك عملة مفضلة جديدة
            preferred_currencies = [c for c in self.selected_currencies_data if c['is_preferred']]
            if preferred_currencies:
                session.query(SupplierCurrency).filter_by(
                    supplier_id=supplier_id,
                    is_preferred=True
                ).update({SupplierCurrency.is_preferred: False})

            # إضافة أو تحديث الروابط
            added_count = 0
            updated_count = 0

            for currency_data in self.selected_currencies_data:
                existing = session.query(SupplierCurrency).filter_by(
                    supplier_id=supplier_id,
                    currency_id=currency_data['currency_id']
                ).first()

                if existing:
                    # تحديث الربط الموجود
                    existing.is_preferred = currency_data['is_preferred']
                    existing.exchange_rate_override = currency_data['exchange_rate_override']
                    existing.notes = self.notes_edit.toPlainText().strip() or None
                    existing.is_active = True
                    updated_count += 1
                else:
                    # إنشاء ربط جديد
                    supplier_currency = SupplierCurrency(
                        supplier_id=supplier_id,
                        currency_id=currency_data['currency_id'],
                        is_preferred=currency_data['is_preferred'],
                        exchange_rate_override=currency_data['exchange_rate_override'],
                        notes=self.notes_edit.toPlainText().strip() or None
                    )
                    session.add(supplier_currency)
                    added_count += 1

            session.commit()

            message = f"تم بنجاح!\n"
            if added_count > 0:
                message += f"- إضافة {added_count} ربط جديد\n"
            if updated_count > 0:
                message += f"- تحديث {updated_count} ربط موجود"

            QMessageBox.information(self, "نجح", message)
            self.clear_form()
            self.load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الروابط: {str(e)}")
        finally:
            session.close()

    def update_supplier_currency(self):
        """تحديث ربط موجود - هذه الدالة لم تعد مستخدمة مع النظام الجديد"""
        QMessageBox.information(
            self, "معلومات",
            "لتحديث الروابط، يرجى حذف الربط الحالي وإضافة ربط جديد بالعملات المطلوبة"
        )

    def delete_supplier_currency(self):
        """حذف ربط محدد"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ربط للحذف")
            return

        # الحصول على معرف الربط
        supplier_currency_id = self.currencies_table.item(current_row, 0).data(Qt.UserRole)
        if not supplier_currency_id:
            return

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا الربط؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        session = self.db_manager.get_session()
        try:
            supplier_currency = session.query(SupplierCurrency).get(supplier_currency_id)
            if supplier_currency:
                # حذف ناعم - تعطيل الربط بدلاً من الحذف
                supplier_currency.is_active = False
                session.commit()

                QMessageBox.information(self, "نجح", "تم حذف الربط بنجاح!")
                self.load_data()
            else:
                QMessageBox.warning(self, "خطأ", "الربط غير موجود!")

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف الربط: {str(e)}")
        finally:
            session.close()

    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.currencies_table.currentRow()
        if current_row >= 0:
            self.load_selected_supplier_currency()

    def load_selected_supplier_currency(self):
        """تحميل بيانات الربط المحدد - للعرض فقط"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            return

        supplier_currency_id = self.currencies_table.item(current_row, 0).data(Qt.UserRole)
        if not supplier_currency_id:
            return

        session = self.db_manager.get_session()
        try:
            supplier_currency = session.query(SupplierCurrency).get(supplier_currency_id)
            if not supplier_currency:
                return

            # عرض معلومات الربط المحدد
            info_text = f"""
معلومات الربط المحدد:
المورد: {supplier_currency.supplier.name}
العملة: {supplier_currency.currency.name} ({supplier_currency.currency.code})
مفضلة: {'نعم' if supplier_currency.is_preferred else 'لا'}
سعر مخصص: {supplier_currency.exchange_rate_override or 'افتراضي'}
الملاحظات: {supplier_currency.notes or 'لا توجد'}
            """

            QMessageBox.information(self, "معلومات الربط", info_text.strip())

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
        finally:
            session.close()

    def clear_form(self):
        """مسح النموذج"""
        self.current_supplier_currency_id = None
        self.supplier_search.clear()
        self.supplier_combo.setCurrentIndex(0)
        self.selected_currencies_data = []
        self.currencies_display.setText("لم يتم اختيار عملات...")
        self.currencies_display.setToolTip("")
        self.notes_edit.clear()

        # إعادة تفعيل زر الإضافة
        self.add_button.setEnabled(True)
        self.update_button.setEnabled(False)

    def validate_form(self):
        """التحقق من صحة البيانات"""
        if self.supplier_combo.currentData() is None:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد")
            self.supplier_combo.setFocus()
            return False

        if not self.selected_currencies_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة واحدة على الأقل")
            self.select_currencies_button.setFocus()
            return False

        return True
