"""
نافذة مسارات الشحن التفاعلية
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                               QWidget, QLabel, QTableWidget, QTableWidgetItem,
                               QPushButton, QTextEdit, QProgressBar, QGroupBox,
                               QFormLayout, QSpinBox, QDoubleSpinBox, QCheckBox,
                               QComboBox, QLineEdit, QMessageBox, QHeaderView,
                               QSplitter, QFrame, QScrollArea, QGridLayout,
                               QListWidget, QListWidgetItem, QSlider, QDateEdit)
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QDate, QDateTime
from PySide6.QtGui import QFont, QIcon, QPixmap, QPainter, QPen, QBrush, QColor

from database.database_manager import DatabaseManager
from database.models import Shipment, Supplier
import json
import math
from datetime import datetime, timedelta

class RouteMapWidget(QWidget):
    """ويدجت خريطة المسارات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.shipments = []
        self.selected_shipment = None
        self.zoom_level = 1.0
        self.pan_x = 0
        self.pan_y = 0
        self.setMinimumSize(800, 600)

        # إعداد الألوان والأنماط
        self.colors = {
            'ocean': QColor(173, 216, 230),
            'land': QColor(245, 245, 220),
            'route_active': QColor(0, 123, 255),
            'route_completed': QColor(40, 167, 69),
            'route_delayed': QColor(220, 53, 69),
            'port': QColor(255, 193, 7),
            'ship': QColor(23, 162, 184)
        }

        # مواقع الموانئ الرئيسية (إحداثيات تقريبية)
        self.major_ports = {
            'جدة': (39.2, 21.5),
            'الدمام': (50.1, 26.4),
            'دبي': (55.3, 25.3),
            'شنغهاي': (121.5, 31.2),
            'سنغافورة': (103.8, 1.3),
            'هونغ كونغ': (114.2, 22.3),
            'روتردام': (4.5, 51.9),
            'هامبورغ': (10.0, 53.6),
            'لوس أنجلوس': (-118.2, 34.1),
            'نيويورك': (-74.0, 40.7),
            'طوكيو': (139.7, 35.7),
            'بوسان': (129.1, 35.2)
        }

    def set_shipments(self, shipments):
        """تعيين الشحنات لعرضها على الخريطة"""
        self.shipments = shipments
        self.update()

    def paintEvent(self, event):
        """رسم الخريطة والمسارات"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # رسم الخلفية (المحيط)
        painter.fillRect(self.rect(), self.colors['ocean'])

        # رسم الخريطة الأساسية
        self.draw_base_map(painter)

        # رسم الموانئ
        self.draw_ports(painter)

        # رسم مسارات الشحن
        self.draw_shipping_routes(painter)

        # رسم الشحنات
        self.draw_shipments(painter)

        # رسم المعلومات
        self.draw_info_overlay(painter)

    def draw_base_map(self, painter):
        """رسم الخريطة الأساسية"""
        # رسم القارات بشكل مبسط
        painter.setBrush(QBrush(self.colors['land']))
        painter.setPen(QPen(QColor(139, 69, 19), 2))

        # آسيا (مبسطة)
        asia_rect = self.coord_to_pixel(100, 10, 140, 50)
        painter.drawEllipse(asia_rect)

        # أوروبا (مبسطة)
        europe_rect = self.coord_to_pixel(-10, 35, 40, 70)
        painter.drawEllipse(europe_rect)

        # أمريكا الشمالية (مبسطة)
        na_rect = self.coord_to_pixel(-130, 25, -60, 70)
        painter.drawEllipse(na_rect)

        # الشرق الأوسط
        me_rect = self.coord_to_pixel(25, 15, 65, 40)
        painter.drawEllipse(me_rect)

    def draw_ports(self, painter):
        """رسم الموانئ الرئيسية"""
        painter.setBrush(QBrush(self.colors['port']))
        painter.setPen(QPen(QColor(255, 140, 0), 2))

        for port_name, (lon, lat) in self.major_ports.items():
            x, y = self.coord_to_pixel_single(lon, lat)

            # رسم الميناء
            painter.drawEllipse(x - 8, y - 8, 16, 16)

            # رسم اسم الميناء
            painter.setPen(QPen(QColor(0, 0, 0), 1))
            painter.drawText(x + 12, y + 5, port_name)
            painter.setPen(QPen(QColor(255, 140, 0), 2))

    def draw_shipping_routes(self, painter):
        """رسم مسارات الشحن"""
        if not self.shipments:
            return

        for shipment in self.shipments:
            self.draw_single_route(painter, shipment)

    def draw_single_route(self, painter, shipment):
        """رسم مسار شحنة واحدة"""
        # تحديد نقاط المسار
        route_points = self.get_route_points(shipment)

        if len(route_points) < 2:
            return

        # تحديد لون المسار حسب الحالة
        status = shipment.shipment_status or ""
        if "تم التسليم" in status:
            color = self.colors['route_completed']
        elif "متأخر" in status or "تأخير" in status:
            color = self.colors['route_delayed']
        else:
            color = self.colors['route_active']

        # رسم المسار
        painter.setPen(QPen(color, 3))

        for i in range(len(route_points) - 1):
            start_point = route_points[i]
            end_point = route_points[i + 1]

            start_x, start_y = self.coord_to_pixel_single(start_point[0], start_point[1])
            end_x, end_y = self.coord_to_pixel_single(end_point[0], end_point[1])

            # رسم خط منحني للمسار
            self.draw_curved_line(painter, start_x, start_y, end_x, end_y)

    def draw_curved_line(self, painter, x1, y1, x2, y2):
        """رسم خط منحني بين نقطتين"""
        # حساب نقطة التحكم للمنحنى
        mid_x = (x1 + x2) / 2
        mid_y = (y1 + y2) / 2 - 50  # منحنى للأعلى

        # رسم خطوط مستقيمة متعددة لمحاكاة المنحنى
        steps = 20
        for i in range(steps):
            t1 = i / steps
            t2 = (i + 1) / steps

            # حساب النقاط على المنحنى التربيعي
            p1_x = (1 - t1)**2 * x1 + 2 * (1 - t1) * t1 * mid_x + t1**2 * x2
            p1_y = (1 - t1)**2 * y1 + 2 * (1 - t1) * t1 * mid_y + t1**2 * y2

            p2_x = (1 - t2)**2 * x1 + 2 * (1 - t2) * t2 * mid_x + t2**2 * x2
            p2_y = (1 - t2)**2 * y1 + 2 * (1 - t2) * t2 * mid_y + t2**2 * y2

            painter.drawLine(int(p1_x), int(p1_y), int(p2_x), int(p2_y))

    def draw_shipments(self, painter):
        """رسم الشحنات على الخريطة"""
        painter.setBrush(QBrush(self.colors['ship']))
        painter.setPen(QPen(QColor(0, 0, 0), 2))

        for shipment in self.shipments:
            # تحديد الموقع الحالي للشحنة
            current_pos = self.get_current_shipment_position(shipment)
            if current_pos:
                x, y = self.coord_to_pixel_single(current_pos[0], current_pos[1])

                # رسم الشحنة
                if shipment == self.selected_shipment:
                    painter.setBrush(QBrush(QColor(255, 0, 0)))  # أحمر للمحدد
                    painter.drawEllipse(x - 10, y - 10, 20, 20)
                else:
                    painter.setBrush(QBrush(self.colors['ship']))
                    painter.drawEllipse(x - 6, y - 6, 12, 12)

                # رسم رقم الشحنة
                painter.setPen(QPen(QColor(0, 0, 0), 1))
                painter.drawText(x + 8, y - 8, shipment.shipment_number or "")

    def draw_info_overlay(self, painter):
        """رسم معلومات إضافية على الخريطة"""
        # رسم مقياس الخريطة
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.drawText(10, self.height() - 20, f"مقياس: {self.zoom_level:.1f}x")

        # رسم عدد الشحنات
        painter.drawText(10, 20, f"عدد الشحنات: {len(self.shipments)}")

    def get_route_points(self, shipment):
        """الحصول على نقاط المسار للشحنة"""
        points = []

        # نقطة البداية (ميناء التحميل)
        if shipment.port_of_loading:
            start_coord = self.get_port_coordinates(shipment.port_of_loading)
            if start_coord:
                points.append(start_coord)

        # نقطة الوسط (ميناء التفريغ)
        if shipment.port_of_discharge and shipment.port_of_discharge != shipment.port_of_loading:
            mid_coord = self.get_port_coordinates(shipment.port_of_discharge)
            if mid_coord:
                points.append(mid_coord)

        # نقطة النهاية (ميناء الوصول أو الوجهة النهائية)
        end_port = shipment.port_of_arrival or shipment.final_destination
        if end_port:
            end_coord = self.get_port_coordinates(end_port)
            if end_coord:
                points.append(end_coord)

        return points

    def get_current_shipment_position(self, shipment):
        """تحديد الموقع الحالي للشحنة"""
        status = shipment.shipment_status or ""

        if "تم التسليم" in status:
            # في الوجهة النهائية
            dest = shipment.final_destination or shipment.port_of_arrival
            return self.get_port_coordinates(dest) if dest else None
        elif "وصلت الميناء" in status:
            # في ميناء الوصول
            return self.get_port_coordinates(shipment.port_of_arrival) if shipment.port_of_arrival else None
        elif "في الطريق" in status:
            # في البحر - موقع تقديري
            route_points = self.get_route_points(shipment)
            if len(route_points) >= 2:
                # موقع في منتصف المسار
                start = route_points[0]
                end = route_points[-1]
                mid_lon = (start[0] + end[0]) / 2
                mid_lat = (start[1] + end[1]) / 2
                return (mid_lon, mid_lat)
        elif "تم الشحن" in status:
            # في ميناء التحميل
            return self.get_port_coordinates(shipment.port_of_loading) if shipment.port_of_loading else None

        # افتراضي - ميناء التحميل
        return self.get_port_coordinates(shipment.port_of_loading) if shipment.port_of_loading else None

    def get_port_coordinates(self, port_name):
        """الحصول على إحداثيات الميناء"""
        # البحث في الموانئ الرئيسية
        for major_port, coords in self.major_ports.items():
            if major_port in port_name or port_name in major_port:
                return coords

        # إحداثيات افتراضية حسب المنطقة
        port_lower = port_name.lower()
        if any(keyword in port_lower for keyword in ['جدة', 'السعودية', 'الرياض']):
            return (39.2, 21.5)  # جدة
        elif any(keyword in port_lower for keyword in ['دبي', 'الإمارات', 'أبوظبي']):
            return (55.3, 25.3)  # دبي
        elif any(keyword in port_lower for keyword in ['الصين', 'شنغهاي', 'بكين']):
            return (121.5, 31.2)  # شنغهاي
        elif any(keyword in port_lower for keyword in ['سنغافورة']):
            return (103.8, 1.3)  # سنغافورة
        elif any(keyword in port_lower for keyword in ['ألمانيا', 'هامبورغ']):
            return (10.0, 53.6)  # هامبورغ
        else:
            return (50.0, 25.0)  # موقع افتراضي في الخليج

    def coord_to_pixel(self, lon1, lat1, lon2, lat2):
        """تحويل الإحداثيات الجغرافية إلى بكسل (مستطيل)"""
        x1, y1 = self.coord_to_pixel_single(lon1, lat1)
        x2, y2 = self.coord_to_pixel_single(lon2, lat2)
        return QRect(min(x1, x2), min(y1, y2), abs(x2 - x1), abs(y2 - y1))

    def coord_to_pixel_single(self, lon, lat):
        """تحويل إحداثية واحدة إلى بكسل"""
        # تحويل بسيط من الإحداثيات الجغرافية إلى البكسل
        # خط الطول: -180 إلى 180 -> 0 إلى عرض الشاشة
        # خط العرض: -90 إلى 90 -> ارتفاع الشاشة إلى 0 (مقلوب)

        x = ((lon + 180) / 360) * self.width() * self.zoom_level + self.pan_x
        y = ((90 - lat) / 180) * self.height() * self.zoom_level + self.pan_y

        return int(x), int(y)

    def mousePressEvent(self, event):
        """التعامل مع النقر على الخريطة"""
        # البحث عن الشحنة المنقور عليها
        for shipment in self.shipments:
            pos = self.get_current_shipment_position(shipment)
            if pos:
                x, y = self.coord_to_pixel_single(pos[0], pos[1])
                if abs(event.x() - x) < 15 and abs(event.y() - y) < 15:
                    self.selected_shipment = shipment
                    self.update()

                    # إشعار النافذة الأب بالاختيار
                    if hasattr(self.parent(), 'update_selected_shipment_status'):
                        self.parent().update_selected_shipment_status(shipment)
                    break

        # حفظ نقطة البداية للسحب
        self.last_pan_point = event.position()

    def mouseMoveEvent(self, event):
        """التعامل مع حركة الماوس للسحب"""
        if hasattr(self, 'last_pan_point') and event.buttons() == Qt.LeftButton:
            # حساب المسافة المحركة
            delta = event.position() - self.last_pan_point
            self.pan_x += delta.x()
            self.pan_y += delta.y()
            self.last_pan_point = event.position()
            self.update()

            # تحديث الإحداثيات في شريط الحالة
            if hasattr(self.parent(), 'update_coordinates_status'):
                self.parent().update_coordinates_status(event.x(), event.y())

    def wheelEvent(self, event):
        """التعامل مع عجلة الماوس للتكبير/التصغير"""
        # تكبير/تصغير حول موقع الماوس
        zoom_factor = 1.2 if event.angleDelta().y() > 0 else 1/1.2
        old_zoom = self.zoom_level
        self.zoom_level = max(0.2, min(5.0, self.zoom_level * zoom_factor))

        # تعديل موقع التحريك للتكبير حول الماوس
        if self.zoom_level != old_zoom:
            mouse_x = event.position().x() - self.width() // 2
            mouse_y = event.position().y() - self.height() // 2

            zoom_change = self.zoom_level / old_zoom
            self.pan_x = mouse_x + (self.pan_x - mouse_x) * zoom_change
            self.pan_y = mouse_y + (self.pan_y - mouse_y) * zoom_change

            self.update()

            # تحديث حالة التكبير
            if hasattr(self.parent(), 'update_zoom_status'):
                self.parent().update_zoom_status()

class ShippingRoutesWindow(QDialog):
    """نافذة مسارات الشحن التفاعلية"""

    def __init__(self, shipments=None, parent=None):
        super().__init__(parent)
        self.shipments = shipments or []
        self.db_manager = DatabaseManager()

        self.setup_ui()
        self.setup_shortcuts()
        self.load_routes_data()

    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار"""
        from PySide6.QtGui import QShortcut, QKeySequence

        # F11 لتبديل وضع ملء الشاشة
        self.fullscreen_shortcut = QShortcut(QKeySequence("F11"), self)
        self.fullscreen_shortcut.activated.connect(self.toggle_window_mode)

        # Ctrl+H لإخفاء/إظهار الشريط الجانبي
        self.sidebar_shortcut = QShortcut(QKeySequence("Ctrl+H"), self)
        self.sidebar_shortcut.activated.connect(self.toggle_sidebar)

        # Ctrl+R لتحديث البيانات
        self.refresh_shortcut = QShortcut(QKeySequence("Ctrl+R"), self)
        self.refresh_shortcut.activated.connect(self.refresh_data)

        # Ctrl+E لتصدير البيانات
        self.export_shortcut = QShortcut(QKeySequence("Ctrl+E"), self)
        self.export_shortcut.activated.connect(self.export_routes)

        # Escape للخروج من ملء الشاشة
        self.escape_shortcut = QShortcut(QKeySequence("Escape"), self)
        self.escape_shortcut.activated.connect(self.handle_escape_key)

        # مفاتيح التكبير والتصغير
        self.zoom_in_shortcut = QShortcut(QKeySequence("Ctrl++"), self)
        self.zoom_in_shortcut.activated.connect(self.zoom_in)

        self.zoom_out_shortcut = QShortcut(QKeySequence("Ctrl+-"), self)
        self.zoom_out_shortcut.activated.connect(self.zoom_out)

        self.reset_zoom_shortcut = QShortcut(QKeySequence("Ctrl+0"), self)
        self.reset_zoom_shortcut.activated.connect(self.reset_map_view)

    def handle_escape_key(self):
        """التعامل مع مفتاح Escape"""
        if self.isFullScreen():
            self.showNormal()
            self.fullscreen_mode = False
            self.view_mode_btn.setText("⛶ ملء الشاشة")
        else:
            self.close()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🗺️ مسارات الشحن التفاعلية - وضع ملء الشاشة")
        self.setModal(True)

        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()
        self.setWindowState(Qt.WindowMaximized)

        # إعداد النافذة لملء الشاشة
        self.setMinimumSize(1200, 800)

        # إضافة دعم مفتاح F11 للتبديل بين ملء الشاشة والوضع العادي
        self.fullscreen_mode = True

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)

        # شريط العنوان
        title_label = QLabel("🗺️ مسارات الشحن التفاعلية")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:0.5 #2980b9, stop:1 #3498db);
                color: white;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # شريط الأدوات
        self.create_toolbar(main_layout)

        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(content_splitter)

        # الخريطة
        self.create_map_section(content_splitter)

        # اللوحة الجانبية
        self.create_side_panel(content_splitter)

        # تعيين النسب للاستفادة من ملء الشاشة
        # 70% للخريطة، 30% للوحة الجانبية
        content_splitter.setSizes([1400, 600])
        content_splitter.setStretchFactor(0, 7)  # الخريطة تأخذ مساحة أكبر
        content_splitter.setStretchFactor(1, 3)  # اللوحة الجانبية مساحة أقل

        # شريط الحالة
        self.create_status_bar(main_layout)

        # أزرار التحكم
        self.create_control_buttons(main_layout)

    def create_toolbar(self, layout):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 5px;
            }
        """)
        toolbar_layout = QHBoxLayout(toolbar_frame)

        # فلتر الشحنات
        filter_label = QLabel("عرض:")
        self.route_filter = QComboBox()
        self.route_filter.addItems([
            "جميع الشحنات", "الشحنات النشطة", "في الطريق",
            "وصلت الميناء", "تم التسليم", "متأخرة"
        ])
        self.route_filter.currentTextChanged.connect(self.filter_routes)

        # فلتر شركة الشحن
        company_label = QLabel("شركة الشحن:")
        self.company_filter = QComboBox()
        self.company_filter.addItem("جميع الشركات")
        self.company_filter.currentTextChanged.connect(self.filter_routes)

        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_data)

        # زر تصدير
        export_btn = QPushButton("📊 تصدير")
        export_btn.clicked.connect(self.export_routes)

        # زر الإعدادات
        settings_btn = QPushButton("⚙️ إعدادات")
        settings_btn.clicked.connect(self.show_settings)

        # إضافة العناصر
        toolbar_layout.addWidget(filter_label)
        toolbar_layout.addWidget(self.route_filter)
        toolbar_layout.addWidget(company_label)
        toolbar_layout.addWidget(self.company_filter)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(refresh_btn)
        toolbar_layout.addWidget(export_btn)
        toolbar_layout.addWidget(settings_btn)

        layout.addWidget(toolbar_frame)

    def create_map_section(self, splitter):
        """إنشاء قسم الخريطة"""
        map_frame = QGroupBox("🌍 خريطة المسارات التفاعلية")
        map_layout = QVBoxLayout(map_frame)

        # ويدجت الخريطة
        self.map_widget = RouteMapWidget()
        map_layout.addWidget(self.map_widget)

        # أدوات التحكم في الخريطة
        map_controls_layout = QHBoxLayout()

        # أزرار التكبير
        zoom_in_btn = QPushButton("🔍+")
        zoom_in_btn.clicked.connect(self.zoom_in)
        zoom_out_btn = QPushButton("🔍-")
        zoom_out_btn.clicked.connect(self.zoom_out)

        # زر إعادة تعيين العرض
        reset_view_btn = QPushButton("🎯 إعادة تعيين")
        reset_view_btn.clicked.connect(self.reset_map_view)

        # زر تبديل وضع العرض
        self.view_mode_btn = QPushButton("🗗 وضع النافذة")
        self.view_mode_btn.clicked.connect(self.toggle_window_mode)
        self.view_mode_btn.setToolTip("تبديل بين ملء الشاشة والوضع العادي (F11)")

        # زر إخفاء/إظهار اللوحة الجانبية
        self.toggle_sidebar_btn = QPushButton("◀ إخفاء الشريط الجانبي")
        self.toggle_sidebar_btn.clicked.connect(self.toggle_sidebar)
        self.toggle_sidebar_btn.setToolTip("إخفاء/إظهار اللوحة الجانبية لمزيد من المساحة للخريطة")

        map_controls_layout.addWidget(zoom_in_btn)
        map_controls_layout.addWidget(zoom_out_btn)
        map_controls_layout.addWidget(reset_view_btn)
        map_controls_layout.addStretch()
        map_controls_layout.addWidget(self.view_mode_btn)
        map_controls_layout.addWidget(self.toggle_sidebar_btn)

        map_layout.addLayout(map_controls_layout)

        splitter.addWidget(map_frame)

    def create_side_panel(self, splitter):
        """إنشاء اللوحة الجانبية"""
        side_panel = QTabWidget()

        # تبويب قائمة الشحنات
        self.create_shipments_list_tab(side_panel)

        # تبويب تفاصيل الشحنة
        self.create_shipment_details_tab(side_panel)

        # تبويب الإحصائيات
        self.create_statistics_tab(side_panel)

        # تبويب التنبيهات
        self.create_alerts_tab(side_panel)

        splitter.addWidget(side_panel)

    def create_shipments_list_tab(self, tab_widget):
        """إنشاء تبويب قائمة الشحنات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # جدول الشحنات
        self.shipments_table = QTableWidget()
        self.shipments_table.setColumnCount(5)
        self.shipments_table.setHorizontalHeaderLabels([
            "رقم الشحنة", "المورد", "الحالة", "الموقع الحالي", "التقدم"
        ])
        self.shipments_table.horizontalHeader().setStretchLastSection(True)
        self.shipments_table.setAlternatingRowColors(True)
        self.shipments_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.shipments_table.itemSelectionChanged.connect(self.on_shipment_selected)

        layout.addWidget(self.shipments_table)

        tab_widget.addTab(tab, "📋 قائمة الشحنات")

    def create_shipment_details_tab(self, tab_widget):
        """إنشاء تبويب تفاصيل الشحنة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # معلومات الشحنة المحددة
        self.shipment_details = QTextEdit()
        self.shipment_details.setReadOnly(True)
        self.shipment_details.setMaximumHeight(200)
        layout.addWidget(self.shipment_details)

        # مسار الشحنة
        route_group = QGroupBox("🗺️ مسار الشحنة")
        route_layout = QVBoxLayout(route_group)

        self.route_timeline = QTextEdit()
        self.route_timeline.setReadOnly(True)
        route_layout.addWidget(self.route_timeline)

        layout.addWidget(route_group)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        track_btn = QPushButton("📍 تتبع")
        track_btn.clicked.connect(self.track_selected_shipment)

        details_btn = QPushButton("📄 تفاصيل كاملة")
        details_btn.clicked.connect(self.show_full_details)

        actions_layout.addWidget(track_btn)
        actions_layout.addWidget(details_btn)
        actions_layout.addStretch()

        layout.addLayout(actions_layout)

        tab_widget.addTab(tab, "📄 تفاصيل الشحنة")

    def create_statistics_tab(self, tab_widget):
        """إنشاء تبويب الإحصائيات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إحصائيات عامة
        stats_group = QGroupBox("📊 إحصائيات المسارات")
        stats_layout = QFormLayout(stats_group)

        self.total_shipments_label = QLabel("0")
        self.active_routes_label = QLabel("0")
        self.completed_routes_label = QLabel("0")
        self.delayed_routes_label = QLabel("0")
        self.avg_transit_time_label = QLabel("0 يوم")

        stats_layout.addRow("إجمالي الشحنات:", self.total_shipments_label)
        stats_layout.addRow("المسارات النشطة:", self.active_routes_label)
        stats_layout.addRow("المسارات المكتملة:", self.completed_routes_label)
        stats_layout.addRow("المسارات المتأخرة:", self.delayed_routes_label)
        stats_layout.addRow("متوسط وقت النقل:", self.avg_transit_time_label)

        layout.addWidget(stats_group)

        # إحصائيات الموانئ
        ports_group = QGroupBox("🚢 إحصائيات الموانئ")
        ports_layout = QVBoxLayout(ports_group)

        self.ports_stats = QTextEdit()
        self.ports_stats.setReadOnly(True)
        self.ports_stats.setMaximumHeight(150)
        ports_layout.addWidget(self.ports_stats)

        layout.addWidget(ports_group)

        layout.addStretch()

        tab_widget.addTab(tab, "📊 إحصائيات")

    def create_alerts_tab(self, tab_widget):
        """إنشاء تبويب التنبيهات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # قائمة التنبيهات
        alerts_group = QGroupBox("🚨 التنبيهات والتحديثات")
        alerts_layout = QVBoxLayout(alerts_group)

        self.alerts_list = QListWidget()
        alerts_layout.addWidget(self.alerts_list)

        layout.addWidget(alerts_group)

        # إعدادات التنبيهات
        settings_group = QGroupBox("⚙️ إعدادات التنبيهات")
        settings_layout = QFormLayout(settings_group)

        self.delay_threshold = QSpinBox()
        self.delay_threshold.setRange(1, 30)
        self.delay_threshold.setValue(3)
        self.delay_threshold.setSuffix(" أيام")

        self.auto_refresh = QCheckBox("تحديث تلقائي")
        self.auto_refresh.setChecked(True)

        settings_layout.addRow("حد التأخير:", self.delay_threshold)
        settings_layout.addRow(self.auto_refresh)

        layout.addWidget(settings_group)

        tab_widget.addTab(tab, "🚨 التنبيهات")

    def create_status_bar(self, layout):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #e9ecef;
                border-top: 1px solid #dee2e6;
                padding: 5px;
            }
        """)
        status_layout = QHBoxLayout(status_frame)

        self.status_label = QLabel("جاهز")
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        # معلومات إضافية في شريط الحالة لوضع ملء الشاشة
        self.zoom_level_label = QLabel("تكبير: 100%")
        self.coordinates_label = QLabel("الإحداثيات: --")
        self.selected_shipment_label = QLabel("لا توجد شحنة محددة")

        # معلومات مفاتيح الاختصار
        shortcuts_label = QLabel("F11: ملء الشاشة | Ctrl+H: إخفاء الشريط | Ctrl+R: تحديث | Ctrl+E: تصدير")
        shortcuts_label.setStyleSheet("color: #6c757d; font-size: 10px;")

        status_layout.addWidget(self.status_label)
        status_layout.addWidget(QLabel("|"))
        status_layout.addWidget(self.zoom_level_label)
        status_layout.addWidget(QLabel("|"))
        status_layout.addWidget(self.coordinates_label)
        status_layout.addWidget(QLabel("|"))
        status_layout.addWidget(self.selected_shipment_label)
        status_layout.addStretch()
        status_layout.addWidget(shortcuts_label)
        status_layout.addWidget(self.progress_bar)

        layout.addWidget(status_frame)

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم"""
        buttons_layout = QHBoxLayout()

        # زر المساعدة
        help_btn = QPushButton("❓ مساعدة")
        help_btn.clicked.connect(self.show_help)

        buttons_layout.addWidget(help_btn)
        buttons_layout.addStretch()

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)

        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

    def load_routes_data(self):
        """تحميل بيانات المسارات"""
        try:
            self.status_label.setText("جاري تحميل البيانات...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد

            # تحديث الخريطة
            self.map_widget.set_shipments(self.shipments)

            # تحديث جدول الشحنات
            self.populate_shipments_table()

            # تحديث الإحصائيات
            self.update_statistics()

            # تحديث فلتر شركات الشحن
            self.update_company_filter()

            # تحديث التنبيهات
            self.update_alerts()

            self.status_label.setText(f"تم تحميل {len(self.shipments)} شحنة")
            self.progress_bar.setVisible(False)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
            self.status_label.setText("خطأ في التحميل")
            self.progress_bar.setVisible(False)

    def populate_shipments_table(self):
        """ملء جدول الشحنات"""
        self.shipments_table.setRowCount(len(self.shipments))

        for row, shipment in enumerate(self.shipments):
            # رقم الشحنة
            self.shipments_table.setItem(row, 0, QTableWidgetItem(shipment.shipment_number or ""))

            # المورد
            supplier_name = shipment.supplier.name if shipment.supplier else "غير محدد"
            self.shipments_table.setItem(row, 1, QTableWidgetItem(supplier_name))

            # الحالة
            status = shipment.shipment_status or "غير محدد"
            status_item = QTableWidgetItem(status)

            # تلوين الحالة
            if "تم التسليم" in status:
                status_item.setBackground(QColor(212, 237, 218))  # أخضر فاتح
            elif "في الطريق" in status:
                status_item.setBackground(QColor(255, 243, 205))  # أصفر فاتح
            elif "متأخر" in status:
                status_item.setBackground(QColor(248, 215, 218))  # أحمر فاتح

            self.shipments_table.setItem(row, 2, status_item)

            # الموقع الحالي
            current_location = self.get_current_location_text(shipment)
            self.shipments_table.setItem(row, 3, QTableWidgetItem(current_location))

            # التقدم
            progress = self.calculate_shipment_progress(shipment)
            progress_item = QTableWidgetItem(f"{progress}%")
            self.shipments_table.setItem(row, 4, progress_item)

    def get_current_location_text(self, shipment):
        """الحصول على نص الموقع الحالي"""
        status = shipment.shipment_status or ""

        if "تم التسليم" in status:
            return shipment.final_destination or "تم التسليم"
        elif "وصلت الميناء" in status:
            return shipment.port_of_arrival or "الميناء"
        elif "في الطريق" in status:
            return "في البحر"
        elif "تم الشحن" in status:
            return shipment.port_of_loading or "ميناء التحميل"
        else:
            return "نقطة البداية"

    def calculate_shipment_progress(self, shipment):
        """حساب تقدم الشحنة"""
        status = shipment.shipment_status or ""

        if "تم التسليم" in status:
            return 100
        elif "وصلت الميناء" in status:
            return 80
        elif "في الطريق" in status:
            return 50
        elif "تم الشحن" in status:
            return 25
        else:
            return 0

    def update_statistics(self):
        """تحديث الإحصائيات"""
        total = len(self.shipments)
        self.total_shipments_label.setText(str(total))

        # حساب الإحصائيات
        active_count = 0
        completed_count = 0
        delayed_count = 0
        transit_times = []

        for shipment in self.shipments:
            status = shipment.shipment_status or ""

            if "تم التسليم" in status:
                completed_count += 1
                # حساب وقت النقل
                if shipment.shipment_date and shipment.actual_arrival_date:
                    transit_time = (shipment.actual_arrival_date - shipment.shipment_date).days
                    transit_times.append(transit_time)
            elif status in ["في الطريق", "وصلت الميناء", "تم الشحن"]:
                active_count += 1

            # فحص التأخير
            if shipment.estimated_arrival_date and shipment.estimated_arrival_date < datetime.now():
                if "تم التسليم" not in status:
                    delayed_count += 1

        self.active_routes_label.setText(str(active_count))
        self.completed_routes_label.setText(str(completed_count))
        self.delayed_routes_label.setText(str(delayed_count))

        # متوسط وقت النقل
        if transit_times:
            avg_transit = sum(transit_times) / len(transit_times)
            self.avg_transit_time_label.setText(f"{avg_transit:.1f} يوم")
        else:
            self.avg_transit_time_label.setText("غير متاح")

        # إحصائيات الموانئ
        self.update_ports_statistics()

    def update_ports_statistics(self):
        """تحديث إحصائيات الموانئ"""
        port_counts = {}

        for shipment in self.shipments:
            ports = [
                shipment.port_of_loading,
                shipment.port_of_discharge,
                shipment.port_of_arrival
            ]

            for port in ports:
                if port:
                    port_counts[port] = port_counts.get(port, 0) + 1

        # ترتيب الموانئ حسب الاستخدام
        sorted_ports = sorted(port_counts.items(), key=lambda x: x[1], reverse=True)

        ports_text = "أكثر الموانئ استخداماً:\n\n"
        for port, count in sorted_ports[:10]:  # أول 10 موانئ
            ports_text += f"• {port}: {count} شحنة\n"

        self.ports_stats.setText(ports_text)

    def update_company_filter(self):
        """تحديث فلتر شركات الشحن"""
        companies = set()
        for shipment in self.shipments:
            if shipment.shipping_company:
                companies.add(shipment.shipping_company)

        self.company_filter.clear()
        self.company_filter.addItem("جميع الشركات")
        for company in sorted(companies):
            self.company_filter.addItem(company)

    def update_alerts(self):
        """تحديث التنبيهات"""
        self.alerts_list.clear()

        current_date = datetime.now()
        delay_threshold = self.delay_threshold.value()

        for shipment in self.shipments:
            # فحص التأخير
            if shipment.estimated_arrival_date:
                days_late = (current_date - shipment.estimated_arrival_date).days
                if days_late > delay_threshold and "تم التسليم" not in (shipment.shipment_status or ""):
                    alert_item = QListWidgetItem(
                        f"🚨 تأخير: {shipment.shipment_number} - متأخر {days_late} يوم"
                    )
                    alert_item.setBackground(QColor(248, 215, 218))
                    self.alerts_list.addItem(alert_item)

            # فحص الشحنات بدون تتبع
            if not shipment.tracking_number and shipment.shipment_status == "في الطريق":
                alert_item = QListWidgetItem(
                    f"⚠️ تتبع مفقود: {shipment.shipment_number} - لا يوجد رقم تتبع"
                )
                alert_item.setBackground(QColor(255, 243, 205))
                self.alerts_list.addItem(alert_item)

        # إضافة تنبيه إذا لم توجد تنبيهات
        if self.alerts_list.count() == 0:
            alert_item = QListWidgetItem("✅ لا توجد تنبيهات حالياً")
            alert_item.setBackground(QColor(212, 237, 218))
            self.alerts_list.addItem(alert_item)

    def filter_routes(self):
        """فلترة المسارات"""
        try:
            session = self.db_manager.get_session()

            # الحصول على الفلاتر
            status_filter = self.route_filter.currentText()
            company_filter = self.company_filter.currentText()

            # بناء الاستعلام
            query = session.query(Shipment).outerjoin(Supplier).filter(Shipment.is_active == True)

            # فلتر الحالة
            if status_filter == "الشحنات النشطة":
                query = query.filter(Shipment.shipment_status != "تم التسليم")
            elif status_filter == "في الطريق":
                query = query.filter(Shipment.shipment_status == "في الطريق")
            elif status_filter == "وصلت الميناء":
                query = query.filter(Shipment.shipment_status == "وصلت الميناء")
            elif status_filter == "تم التسليم":
                query = query.filter(Shipment.shipment_status == "تم التسليم")
            elif status_filter == "متأخرة":
                query = query.filter(
                    Shipment.estimated_arrival_date < datetime.now(),
                    Shipment.shipment_status != "تم التسليم"
                )

            # فلتر شركة الشحن
            if company_filter != "جميع الشركات":
                query = query.filter(Shipment.shipping_company == company_filter)

            # تنفيذ الاستعلام
            filtered_shipments = query.order_by(Shipment.created_at.desc()).all()

            # تحديث العرض
            self.shipments = filtered_shipments
            self.map_widget.set_shipments(self.shipments)
            self.populate_shipments_table()
            self.update_statistics()
            self.update_alerts()

            self.status_label.setText(f"تم عرض {len(self.shipments)} شحنة")

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في الفلترة: {str(e)}")

    def on_shipment_selected(self):
        """عند اختيار شحنة من الجدول"""
        current_row = self.shipments_table.currentRow()
        if current_row >= 0 and current_row < len(self.shipments):
            selected_shipment = self.shipments[current_row]

            # تحديد الشحنة في الخريطة
            self.map_widget.selected_shipment = selected_shipment
            self.map_widget.update()

            # تحديث تفاصيل الشحنة
            self.update_shipment_details(selected_shipment)

            # تحديث شريط الحالة
            self.selected_shipment_label.setText(f"محدد: {selected_shipment.shipment_number}")
        else:
            self.selected_shipment_label.setText("لا توجد شحنة محددة")

    def update_shipment_details(self, shipment):
        """تحديث تفاصيل الشحنة"""
        details_text = f"""
📦 تفاصيل الشحنة: {shipment.shipment_number}

🏢 المورد: {shipment.supplier.name if shipment.supplier else 'غير محدد'}
📅 تاريخ الشحنة: {shipment.shipment_date.strftime('%Y-%m-%d') if shipment.shipment_date else 'غير محدد'}
🚢 شركة الشحن: {shipment.shipping_company or 'غير محدد'}
🛳️ اسم السفينة: {shipment.vessel_name or 'غير محدد'}
🔢 رقم الرحلة: {shipment.voyage_number or 'غير محدد'}
📋 رقم التتبع: {shipment.tracking_number or 'غير محدد'}
📄 بوليصة الشحن: {shipment.bill_of_lading or 'غير محدد'}
📦 رقم الحاوية: {shipment.container_number or 'غير محدد'}

🚢 ميناء التحميل: {shipment.port_of_loading or 'غير محدد'}
🏁 ميناء التفريغ: {shipment.port_of_discharge or 'غير محدد'}
🎯 ميناء الوصول: {shipment.port_of_arrival or 'غير محدد'}
🏠 الوجهة النهائية: {shipment.final_destination or 'غير محدد'}

⏰ تاريخ المغادرة المتوقع: {shipment.estimated_departure_date.strftime('%Y-%m-%d') if shipment.estimated_departure_date else 'غير محدد'}
⏰ تاريخ الوصول المتوقع: {shipment.estimated_arrival_date.strftime('%Y-%m-%d') if shipment.estimated_arrival_date else 'غير محدد'}

🔄 الحالة الحالية: {shipment.shipment_status or 'غير محدد'}
        """

        self.shipment_details.setText(details_text.strip())

        # تحديث مسار الشحنة
        self.update_route_timeline(shipment)

    def update_route_timeline(self, shipment):
        """تحديث الجدول الزمني للمسار"""
        timeline_text = f"🗺️ مسار الشحنة {shipment.shipment_number}\n"
        timeline_text += "=" * 40 + "\n\n"

        # نقاط المسار
        route_points = []

        if shipment.port_of_loading:
            route_points.append(("🚢 ميناء التحميل", shipment.port_of_loading, shipment.estimated_departure_date))

        if shipment.port_of_discharge and shipment.port_of_discharge != shipment.port_of_loading:
            route_points.append(("🔄 ميناء التفريغ", shipment.port_of_discharge, None))

        if shipment.port_of_arrival:
            route_points.append(("🏁 ميناء الوصول", shipment.port_of_arrival, shipment.estimated_arrival_date))

        if shipment.final_destination:
            route_points.append(("🎯 الوجهة النهائية", shipment.final_destination, None))

        # عرض نقاط المسار
        current_status = shipment.shipment_status or ""

        for i, (stage, location, date) in enumerate(route_points):
            # تحديد حالة المرحلة
            if i == 0 and "تم الشحن" in current_status:
                status_icon = "✅"
            elif i == len(route_points) - 1 and "تم التسليم" in current_status:
                status_icon = "✅"
            elif "في الطريق" in current_status and i > 0:
                status_icon = "🔄"
            elif "وصلت الميناء" in current_status and "ميناء الوصول" in stage:
                status_icon = "✅"
            else:
                status_icon = "⏳"

            timeline_text += f"{status_icon} {stage}: {location}\n"
            if date:
                timeline_text += f"   📅 {date.strftime('%Y-%m-%d')}\n"
            timeline_text += "\n"

        # إضافة معلومات إضافية
        progress = self.calculate_shipment_progress(shipment)
        timeline_text += f"📊 التقدم الإجمالي: {progress}%\n"

        if shipment.estimated_arrival_date:
            days_remaining = (shipment.estimated_arrival_date - datetime.now()).days
            if days_remaining > 0:
                timeline_text += f"⏰ الأيام المتبقية: {days_remaining} يوم\n"
            elif days_remaining < 0:
                timeline_text += f"🚨 متأخر بـ: {abs(days_remaining)} يوم\n"

        self.route_timeline.setText(timeline_text)

    # وظائف التحكم في الخريطة
    def zoom_in(self):
        """تكبير الخريطة"""
        self.map_widget.zoom_level = min(self.map_widget.zoom_level * 1.2, 5.0)
        self.map_widget.update()
        self.update_zoom_status()

    def zoom_out(self):
        """تصغير الخريطة"""
        self.map_widget.zoom_level = max(self.map_widget.zoom_level / 1.2, 0.2)
        self.map_widget.update()
        self.update_zoom_status()

    def reset_map_view(self):
        """إعادة تعيين عرض الخريطة"""
        self.map_widget.zoom_level = 1.0
        self.map_widget.pan_x = 0
        self.map_widget.pan_y = 0
        self.map_widget.update()
        self.update_zoom_status()

    def update_zoom_status(self):
        """تحديث حالة التكبير في شريط الحالة"""
        zoom_percentage = int(self.map_widget.zoom_level * 100)
        self.zoom_level_label.setText(f"تكبير: {zoom_percentage}%")

    def toggle_window_mode(self):
        """تبديل وضع النافذة بين ملء الشاشة والوضع العادي"""
        if self.isFullScreen():
            self.showNormal()
            self.fullscreen_mode = False
            self.view_mode_btn.setText("⛶ ملء الشاشة")
            self.view_mode_btn.setToolTip("تبديل إلى وضع ملء الشاشة (F11)")
        else:
            self.showFullScreen()
            self.fullscreen_mode = True
            self.view_mode_btn.setText("🗗 وضع النافذة")
            self.view_mode_btn.setToolTip("تبديل إلى الوضع العادي (F11)")

    def toggle_sidebar(self):
        """إخفاء/إظهار اللوحة الجانبية"""
        # البحث عن اللوحة الجانبية في التخطيط
        for i in range(self.layout().count()):
            item = self.layout().itemAt(i)
            if item and hasattr(item, 'widget'):
                widget = item.widget()
                if hasattr(widget, 'sizes'):  # هذا هو الـ splitter
                    splitter = widget
                    side_panel = splitter.widget(1)  # اللوحة الجانبية هي الثانية

                    if side_panel.isVisible():
                        side_panel.hide()
                        self.toggle_sidebar_btn.setText("▶ إظهار الشريط الجانبي")
                        self.toggle_sidebar_btn.setToolTip("إظهار اللوحة الجانبية (Ctrl+H)")
                        # إعطاء كامل المساحة للخريطة
                        splitter.setSizes([2000, 0])
                    else:
                        side_panel.show()
                        self.toggle_sidebar_btn.setText("◀ إخفاء الشريط الجانبي")
                        self.toggle_sidebar_btn.setToolTip("إخفاء اللوحة الجانبية (Ctrl+H)")
                        # إعادة توزيع المساحة
                        splitter.setSizes([1400, 600])
                    break

    # وظائف الإجراءات
    def track_selected_shipment(self):
        """تتبع الشحنة المحددة"""
        current_row = self.shipments_table.currentRow()
        if current_row >= 0 and current_row < len(self.shipments):
            shipment = self.shipments[current_row]

            # تركيز الخريطة على الشحنة
            self.map_widget.selected_shipment = shipment
            pos = self.map_widget.get_current_shipment_position(shipment)
            if pos:
                # تحريك الخريطة لتركز على الشحنة
                self.map_widget.pan_x = -pos[0] * 2
                self.map_widget.pan_y = -pos[1] * 2
                self.map_widget.update()
        else:
            QMessageBox.information(self, "تنبيه", "يرجى اختيار شحنة من القائمة أولاً")

    def show_full_details(self):
        """عرض التفاصيل الكاملة للشحنة"""
        current_row = self.shipments_table.currentRow()
        if current_row >= 0 and current_row < len(self.shipments):
            shipment = self.shipments[current_row]

            # إنشاء نافذة تفاصيل
            details_dialog = QDialog(self)
            details_dialog.setWindowTitle(f"تفاصيل الشحنة {shipment.shipment_number}")
            details_dialog.setModal(True)
            details_dialog.resize(600, 500)

            layout = QVBoxLayout(details_dialog)

            # محتوى التفاصيل
            details_text = QTextEdit()
            details_text.setReadOnly(True)

            # تفاصيل شاملة
            full_details = f"""
📦 معلومات الشحنة الكاملة
{'=' * 50}

🔢 رقم الشحنة: {shipment.shipment_number or 'غير محدد'}
📅 تاريخ الشحنة: {shipment.shipment_date.strftime('%Y-%m-%d %H:%M') if shipment.shipment_date else 'غير محدد'}
🏢 المورد: {shipment.supplier.name if shipment.supplier else 'غير محدد'}
📄 رقم فاتورة المورد: {shipment.supplier_invoice_number or 'غير محدد'}

🚢 معلومات الشحن
{'=' * 30}
🚢 شركة الشحن: {shipment.shipping_company or 'غير محدد'}
🛳️ اسم السفينة: {shipment.vessel_name or 'غير محدد'}
🔢 رقم الرحلة: {shipment.voyage_number or 'غير محدد'}
📋 رقم التتبع: {shipment.tracking_number or 'غير محدد'}
📄 بوليصة الشحن: {shipment.bill_of_lading or 'غير محدد'}
📦 رقم الحاوية: {shipment.container_number or 'غير محدد'}
🚚 رقم DHL: {shipment.dhl_number or 'غير محدد'}
🚛 طريقة الشحن: {shipment.shipping_method or 'غير محدد'}

🗺️ معلومات المسار
{'=' * 30}
🚢 ميناء التحميل: {shipment.port_of_loading or 'غير محدد'}
🔄 ميناء التفريغ: {shipment.port_of_discharge or 'غير محدد'}
🏁 ميناء الوصول: {shipment.port_of_arrival or 'غير محدد'}
🎯 الوجهة النهائية: {shipment.final_destination or 'غير محدد'}

⏰ التواريخ المهمة
{'=' * 30}
📅 تاريخ المغادرة المتوقع: {shipment.estimated_departure_date.strftime('%Y-%m-%d') if shipment.estimated_departure_date else 'غير محدد'}
✅ تاريخ المغادرة الفعلي: {shipment.actual_departure_date.strftime('%Y-%m-%d') if shipment.actual_departure_date else 'غير محدد'}
📅 تاريخ الوصول المتوقع: {shipment.estimated_arrival_date.strftime('%Y-%m-%d') if shipment.estimated_arrival_date else 'غير محدد'}
✅ تاريخ الوصول الفعلي: {shipment.actual_arrival_date.strftime('%Y-%m-%d') if shipment.actual_arrival_date else 'غير محدد'}

🔄 معلومات الحالة
{'=' * 30}
📊 الحالة الحالية: {shipment.shipment_status or 'غير محدد'}
📝 ملاحظات: {shipment.notes or 'لا توجد ملاحظات'}

📈 معلومات النظام
{'=' * 30}
📅 تاريخ الإنشاء: {shipment.created_at.strftime('%Y-%m-%d %H:%M') if shipment.created_at else 'غير محدد'}
🔄 آخر تحديث: {shipment.updated_at.strftime('%Y-%m-%d %H:%M') if shipment.updated_at else 'غير محدد'}
            """

            details_text.setText(full_details.strip())
            layout.addWidget(details_text)

            # زر إغلاق
            close_btn = QPushButton("إغلاق")
            close_btn.clicked.connect(details_dialog.close)
            layout.addWidget(close_btn)

            details_dialog.exec()
        else:
            QMessageBox.information(self, "تنبيه", "يرجى اختيار شحنة من القائمة أولاً")

    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # إعادة تحميل الشحنات من قاعدة البيانات
            session = self.db_manager.get_session()

            shipments = session.query(Shipment).outerjoin(Supplier).filter(
                Shipment.is_active == True
            ).order_by(Shipment.created_at.desc()).all()

            self.shipments = shipments
            session.close()

            # تحديث العرض
            self.load_routes_data()

            QMessageBox.information(self, "تحديث", "تم تحديث البيانات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث البيانات: {str(e)}")

    def export_routes(self):
        """تصدير بيانات المسارات"""
        try:
            from PySide6.QtWidgets import QFileDialog
            import csv

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير بيانات المسارات",
                f"shipping_routes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    headers = [
                        'رقم الشحنة', 'المورد', 'شركة الشحن', 'اسم السفينة',
                        'ميناء التحميل', 'ميناء التفريغ', 'ميناء الوصول', 'الوجهة النهائية',
                        'الحالة', 'تاريخ المغادرة المتوقع', 'تاريخ الوصول المتوقع',
                        'رقم التتبع', 'بوليصة الشحن', 'رقم الحاوية'
                    ]
                    writer.writerow(headers)

                    # كتابة البيانات
                    for shipment in self.shipments:
                        row = [
                            shipment.shipment_number or '',
                            shipment.supplier.name if shipment.supplier else '',
                            shipment.shipping_company or '',
                            shipment.vessel_name or '',
                            shipment.port_of_loading or '',
                            shipment.port_of_discharge or '',
                            shipment.port_of_arrival or '',
                            shipment.final_destination or '',
                            shipment.shipment_status or '',
                            shipment.estimated_departure_date.strftime('%Y-%m-%d') if shipment.estimated_departure_date else '',
                            shipment.estimated_arrival_date.strftime('%Y-%m-%d') if shipment.estimated_arrival_date else '',
                            shipment.tracking_number or '',
                            shipment.bill_of_lading or '',
                            shipment.container_number or ''
                        ]
                        writer.writerow(row)

                QMessageBox.information(self, "تصدير", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تصدير البيانات: {str(e)}")

    def show_settings(self):
        """عرض إعدادات المسارات"""
        settings_dialog = QDialog(self)
        settings_dialog.setWindowTitle("⚙️ إعدادات المسارات")
        settings_dialog.setModal(True)
        settings_dialog.resize(400, 300)

        layout = QVBoxLayout(settings_dialog)

        # إعدادات العرض
        display_group = QGroupBox("إعدادات العرض")
        display_layout = QFormLayout(display_group)

        # حد التأخير
        delay_spin = QSpinBox()
        delay_spin.setRange(1, 30)
        delay_spin.setValue(self.delay_threshold.value())
        delay_spin.setSuffix(" أيام")

        # التحديث التلقائي
        auto_refresh_check = QCheckBox()
        auto_refresh_check.setChecked(self.auto_refresh.isChecked())

        display_layout.addRow("حد التأخير:", delay_spin)
        display_layout.addRow("التحديث التلقائي:", auto_refresh_check)

        layout.addWidget(display_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ")
        cancel_btn = QPushButton("إلغاء")

        def save_settings():
            self.delay_threshold.setValue(delay_spin.value())
            self.auto_refresh.setChecked(auto_refresh_check.isChecked())
            self.update_alerts()
            settings_dialog.accept()

        save_btn.clicked.connect(save_settings)
        cancel_btn.clicked.connect(settings_dialog.reject)

        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        settings_dialog.exec()

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
🗺️ مساعدة نافذة مسارات الشحن التفاعلية

📋 الوظائف الأساسية:
• عرض الشحنات على خريطة تفاعلية
• تتبع مسارات الشحن في الوقت الفعلي
• فلترة الشحنات حسب الحالة وشركة الشحن
• عرض إحصائيات مفصلة للمسارات
• تنبيهات للشحنات المتأخرة

🖱️ التحكم في الخريطة:
• النقر على الشحنة لتحديدها
• استخدام عجلة الماوس للتكبير/التصغير
• أزرار التكبير والتصغير في الأسفل
• زر إعادة تعيين العرض

📊 التبويبات:
• قائمة الشحنات: جدول بجميع الشحنات
• تفاصيل الشحنة: معلومات مفصلة للشحنة المحددة
• الإحصائيات: إحصائيات شاملة للمسارات
• التنبيهات: تنبيهات التأخير والمشاكل

🔧 الأدوات:
• فلترة متقدمة للشحنات
• تصدير البيانات إلى CSV
• إعدادات قابلة للتخصيص
• تحديث تلقائي للبيانات

💡 نصائح:
• انقر على شحنة في الجدول لرؤيتها على الخريطة
• استخدم الفلاتر لتركيز العرض
• راجع التنبيهات بانتظام للشحنات المتأخرة
        """

        QMessageBox.information(self, "مساعدة", help_text.strip())

    # وظائف مساعدة لتحديث شريط الحالة
    def update_selected_shipment_status(self, shipment):
        """تحديث حالة الشحنة المحددة في شريط الحالة"""
        if shipment:
            self.selected_shipment_label.setText(f"محدد: {shipment.shipment_number}")
        else:
            self.selected_shipment_label.setText("لا توجد شحنة محددة")

    def update_coordinates_status(self, x, y):
        """تحديث الإحداثيات في شريط الحالة"""
        self.coordinates_label.setText(f"الإحداثيات: {int(x)}, {int(y)}")

    def update_zoom_status(self):
        """تحديث حالة التكبير في شريط الحالة"""
        zoom_percentage = int(self.map_widget.zoom_level * 100)
        self.zoom_level_label.setText(f"تكبير: {zoom_percentage}%")

    def keyPressEvent(self, event):
        """التعامل مع مفاتيح الاختصار الإضافية"""
        # مفاتيح الأسهم لتحريك الخريطة
        if event.key() == Qt.Key_Left:
            self.map_widget.pan_x += 20
            self.map_widget.update()
        elif event.key() == Qt.Key_Right:
            self.map_widget.pan_x -= 20
            self.map_widget.update()
        elif event.key() == Qt.Key_Up:
            self.map_widget.pan_y += 20
            self.map_widget.update()
        elif event.key() == Qt.Key_Down:
            self.map_widget.pan_y -= 20
            self.map_widget.update()
        else:
            super().keyPressEvent(event)

    def resizeEvent(self, event):
        """التعامل مع تغيير حجم النافذة"""
        super().resizeEvent(event)
        # تحديث الخريطة عند تغيير الحجم
        if hasattr(self, 'map_widget'):
            self.map_widget.update()

    def closeEvent(self, event):
        """التعامل مع إغلاق النافذة"""
        # حفظ إعدادات النافذة إذا لزم الأمر
        event.accept()