#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الأنظمة المدمجة - Test Integrated Systems
اختبار دمج الأنظمة مع الواجهة الجديدة
"""

import sys
import os

def test_imports():
    """اختبار استيراد الأنظمة"""
    print("🔍 اختبار استيراد الأنظمة...")
    print("=" * 50)
    
    # اختبار مكتبة القوالب
    try:
        from template_library import Templates, نموذج_ادخال_اساسي
        print("✅ مكتبة القوالب متاحة")
        templates_available = True
    except ImportError as e:
        print(f"❌ مكتبة القوالب غير متاحة: {e}")
        templates_available = False
    
    # اختبار أنظمة التطبيق
    systems_available = False
    
    # محاولة استيراد من src
    try:
        from src.ui.settings.settings_window import SettingsWindow
        from src.ui.items.items_window import ItemsWindow
        from src.ui.suppliers.suppliers_window import SuppliersWindow
        from src.ui.shipments.shipments_window import ShipmentsWindow
        from src.ui.shipments.live_tracking_window import LiveTrackingWindow
        from src.ui.shipments.shipment_maps_window import ShipmentMapsWindow
        print("✅ أنظمة التطبيق متاحة من src")
        systems_available = True
    except ImportError:
        # محاولة استيراد من release
        try:
            from release.src.ui.settings.settings_window import SettingsWindow
            from release.src.ui.items.items_window import ItemsWindow
            from release.src.ui.suppliers.suppliers_window import SuppliersWindow
            from release.src.ui.shipments.shipments_window import ShipmentsWindow
            from release.src.ui.shipments.live_tracking_window import LiveTrackingWindow
            from release.src.ui.shipments.shipment_maps_window import ShipmentMapsWindow
            print("✅ أنظمة التطبيق متاحة من release")
            systems_available = True
        except ImportError as e:
            print(f"❌ أنظمة التطبيق غير متاحة: {e}")
            systems_available = False
    
    # اختبار الواجهة المطورة
    try:
        from main_interface_enhanced import EnhancedMainWindow
        print("✅ الواجهة المطورة متاحة")
        interface_available = True
    except ImportError as e:
        print(f"❌ الواجهة المطورة غير متاحة: {e}")
        interface_available = False
    
    return templates_available, systems_available, interface_available

def test_files():
    """اختبار وجود الملفات"""
    print("\n📁 اختبار وجود الملفات...")
    print("=" * 50)
    
    required_files = [
        "main_interface_enhanced.py",
        "run_enhanced_interface.py",
        "template_library.py",
        "main_window_prototype.py"
    ]
    
    optional_files = [
        "src/ui/settings/settings_window.py",
        "src/ui/items/items_window.py",
        "src/ui/suppliers/suppliers_window.py",
        "src/ui/shipments/shipments_window.py",
        "src/ui/shipments/live_tracking_window.py",
        "src/ui/shipments/shipment_maps_window.py"
    ]
    
    print("الملفات المطلوبة:")
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
    
    print("\nالملفات الاختيارية:")
    for file in optional_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"⚠️ {file} (غير موجود)")

def test_integration():
    """اختبار التكامل"""
    print("\n🔗 اختبار التكامل...")
    print("=" * 50)
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # اختبار إنشاء الواجهة المطورة
        from main_interface_enhanced import EnhancedMainWindow
        
        print("🔧 إنشاء الواجهة المطورة...")
        window = EnhancedMainWindow()
        
        print(f"✅ تم إنشاء الواجهة بنجاح")
        print(f"   العنوان: {window.windowTitle()}")
        print(f"   الحجم: {window.size().width()}x{window.size().height()}")
        print(f"   المستخدم: {window.current_user}")
        print(f"   حالة النظام: {window.system_status}")
        
        # اختبار الوظائف
        print("\n🧪 اختبار الوظائف...")
        
        # اختبار فتح النماذج
        if hasattr(window, 'open_new_shipment_form'):
            print("✅ وظيفة فتح النموذج الأساسي متاحة")
        
        # اختبار فتح الأنظمة
        system_functions = [
            'open_shipments_window',
            'open_suppliers_window',
            'open_items_window',
            'open_settings_window',
            'open_live_tracking_window',
            'open_shipment_maps_window'
        ]
        
        for func_name in system_functions:
            if hasattr(window, func_name):
                print(f"✅ وظيفة {func_name} متاحة")
            else:
                print(f"❌ وظيفة {func_name} غير متاحة")
        
        # إغلاق النافذة
        window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الأنظمة المدمجة - SHIPMENT ERP")
    print("=" * 60)
    
    # اختبار الاستيرادات
    templates_ok, systems_ok, interface_ok = test_imports()
    
    # اختبار الملفات
    test_files()
    
    # اختبار التكامل
    integration_ok = test_integration()
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print("=" * 60)
    
    print(f"🏛️ مكتبة القوالب: {'✅ متاحة' if templates_ok else '❌ غير متاحة'}")
    print(f"🏢 أنظمة التطبيق: {'✅ متاحة' if systems_ok else '❌ غير متاحة'}")
    print(f"🖥️ الواجهة المطورة: {'✅ متاحة' if interface_ok else '❌ غير متاحة'}")
    print(f"🔗 التكامل: {'✅ يعمل بنجاح' if integration_ok else '❌ يحتاج إصلاح'}")
    
    # التوصيات
    print("\n💡 التوصيات:")
    if not templates_ok:
        print("   • تأكد من وجود ملف template_library.py")
    if not systems_ok:
        print("   • تأكد من وجود مجلد src مع أنظمة التطبيق")
    if not interface_ok:
        print("   • تأكد من وجود ملف main_interface_enhanced.py")
    if not integration_ok:
        print("   • تأكد من تثبيت PySide6")
        print("   • تحقق من سلامة الملفات")
    
    if all([templates_ok, interface_ok, integration_ok]):
        print("🎉 جميع الأنظمة تعمل بنجاح!")
        print("🚀 يمكنك تشغيل: python run_enhanced_interface.py")
    else:
        print("⚠️ بعض الأنظمة تحتاج إصلاح")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
