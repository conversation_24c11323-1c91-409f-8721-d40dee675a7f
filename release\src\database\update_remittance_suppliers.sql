-- إضافة جدول تفاصيل الموردين المتعددين في الحوالة
CREATE TABLE IF NOT EXISTS remittance_suppliers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    remittance_id INTEGER NOT NULL,
    supplier_id INTEGER NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency_id INTEGER NOT NULL,
    exchange_rate DECIMAL(10,4) NOT NULL DEFAULT 1.0000,
    amount_in_base_currency DECIMAL(15,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (remittance_id) REFERENCES remittances(id) ON DELETE CASCADE,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (currency_id) REFERENCES currencies(id)
);

-- إنشاء فهرس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_remittance_suppliers_remittance_id ON remittance_suppliers(remittance_id);
CREATE INDEX IF NOT EXISTS idx_remittance_suppliers_supplier_id ON remittance_suppliers(supplier_id);
