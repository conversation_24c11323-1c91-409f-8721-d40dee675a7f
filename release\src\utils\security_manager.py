# -*- coding: utf-8 -*-
"""
مدير الأمان والتحكم في الوصول
Security and Access Control Manager
"""

import sqlite3
import hashlib
import secrets
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, List, Any
import logging

class SecurityManager:
    """مدير الأمان والتحكم في الوصول"""
    
    def __init__(self):
        self.current_user_id = None
        self.current_user_data = None
        self.session_token = None
        self.session_expiry = None
        self.failed_attempts = {}
        self.max_failed_attempts = 5
        self.lockout_duration = 30  # دقيقة
        
        # إعداد نظام السجلات
        self.setup_logging()
        
    def setup_logging(self):
        """إعداد نظام السجلات"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "security.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("SecurityManager")
        
    def hash_password(self, password: str, salt: str = None) -> tuple:
        """تشفير كلمة المرور"""
        if salt is None:
            salt = secrets.token_hex(32)
        
        # استخدام PBKDF2 مع SHA-256
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 100,000 iterations
        )
        
        return password_hash.hex(), salt
        
    def verify_password(self, password: str, stored_hash: str, salt: str) -> bool:
        """التحقق من كلمة المرور"""
        password_hash, _ = self.hash_password(password, salt)
        return password_hash == stored_hash
        
    def generate_session_token(self) -> str:
        """توليد رمز الجلسة"""
        return secrets.token_urlsafe(32)
        
    def authenticate_user(self, username: str, password: str) -> Dict[str, Any]:
        """مصادقة المستخدم"""
        try:
            # التحقق من محاولات الدخول الفاشلة
            if self.is_account_locked(username):
                self.logger.warning(f"محاولة دخول لحساب محجوز: {username}")
                return {
                    'success': False,
                    'error': 'الحساب محجوز مؤقتاً بسبب محاولات دخول فاشلة متعددة',
                    'locked_until': self.get_lockout_expiry(username)
                }
                
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # البحث عن المستخدم
            cursor.execute("""
                SELECT id, username, password_hash, salt, full_name, email, 
                       role, is_active, last_login, failed_login_attempts,
                       account_locked_until
                FROM users 
                WHERE username = ? AND is_active = 1
            """, (username,))
            
            user = cursor.fetchone()
            
            if not user:
                self.record_failed_attempt(username)
                self.logger.warning(f"محاولة دخول بمستخدم غير موجود: {username}")
                return {'success': False, 'error': 'اسم المستخدم أو كلمة المرور غير صحيحة'}
                
            user_id, db_username, password_hash, salt, full_name, email, role, is_active, last_login, failed_attempts, locked_until = user
            
            # التحقق من كلمة المرور
            if not self.verify_password(password, password_hash, salt):
                self.record_failed_attempt(username)
                self.update_failed_attempts(user_id, failed_attempts + 1)
                self.logger.warning(f"محاولة دخول بكلمة مرور خاطئة: {username}")
                return {'success': False, 'error': 'اسم المستخدم أو كلمة المرور غير صحيحة'}
                
            # نجحت المصادقة
            self.current_user_id = user_id
            self.current_user_data = {
                'id': user_id,
                'username': db_username,
                'full_name': full_name,
                'email': email,
                'role': role
            }
            
            # إنشاء جلسة جديدة
            self.session_token = self.generate_session_token()
            self.session_expiry = datetime.now() + timedelta(hours=8)  # 8 ساعات
            
            # تحديث آخر دخول ومسح محاولات الفشل
            cursor.execute("""
                UPDATE users 
                SET last_login = CURRENT_TIMESTAMP, 
                    failed_login_attempts = 0,
                    account_locked_until = NULL
                WHERE id = ?
            """, (user_id,))
            
            # تسجيل الجلسة
            cursor.execute("""
                INSERT INTO user_sessions (
                    user_id, session_token, created_at, expires_at, ip_address
                ) VALUES (?, ?, CURRENT_TIMESTAMP, ?, ?)
            """, (user_id, self.session_token, self.session_expiry.isoformat(), "127.0.0.1"))
            
            conn.commit()
            conn.close()
            
            # مسح محاولات الفشل من الذاكرة
            if username in self.failed_attempts:
                del self.failed_attempts[username]
                
            self.logger.info(f"دخول ناجح للمستخدم: {username}")
            
            return {
                'success': True,
                'user': self.current_user_data,
                'session_token': self.session_token,
                'expires_at': self.session_expiry.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في مصادقة المستخدم {username}: {str(e)}")
            return {'success': False, 'error': 'خطأ في النظام'}
            
    def is_account_locked(self, username: str) -> bool:
        """التحقق من حجز الحساب"""
        if username in self.failed_attempts:
            attempts, last_attempt = self.failed_attempts[username]
            if attempts >= self.max_failed_attempts:
                if datetime.now() - last_attempt < timedelta(minutes=self.lockout_duration):
                    return True
                else:
                    # انتهت فترة الحجز
                    del self.failed_attempts[username]
                    
        return False
        
    def get_lockout_expiry(self, username: str) -> Optional[str]:
        """الحصول على وقت انتهاء الحجز"""
        if username in self.failed_attempts:
            attempts, last_attempt = self.failed_attempts[username]
            if attempts >= self.max_failed_attempts:
                expiry = last_attempt + timedelta(minutes=self.lockout_duration)
                return expiry.isoformat()
        return None
        
    def record_failed_attempt(self, username: str):
        """تسجيل محاولة دخول فاشلة"""
        if username in self.failed_attempts:
            attempts, _ = self.failed_attempts[username]
            self.failed_attempts[username] = (attempts + 1, datetime.now())
        else:
            self.failed_attempts[username] = (1, datetime.now())
            
    def update_failed_attempts(self, user_id: int, attempts: int):
        """تحديث محاولات الفشل في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            locked_until = None
            if attempts >= self.max_failed_attempts:
                locked_until = (datetime.now() + timedelta(minutes=self.lockout_duration)).isoformat()
                
            cursor.execute("""
                UPDATE users 
                SET failed_login_attempts = ?, account_locked_until = ?
                WHERE id = ?
            """, (attempts, locked_until, user_id))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث محاولات الفشل: {str(e)}")
            
    def validate_session(self, session_token: str) -> bool:
        """التحقق من صحة الجلسة"""
        try:
            if not session_token:
                return False
                
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT us.user_id, us.expires_at, u.username, u.full_name, u.role
                FROM user_sessions us
                JOIN users u ON us.user_id = u.id
                WHERE us.session_token = ? AND us.is_active = 1
            """, (session_token,))
            
            session = cursor.fetchone()
            
            if not session:
                conn.close()
                return False
                
            user_id, expires_at, username, full_name, role = session
            
            # التحقق من انتهاء الجلسة
            expiry_time = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
            if datetime.now() > expiry_time:
                # إنهاء الجلسة المنتهية الصلاحية
                cursor.execute("""
                    UPDATE user_sessions 
                    SET is_active = 0, ended_at = CURRENT_TIMESTAMP
                    WHERE session_token = ?
                """, (session_token,))
                conn.commit()
                conn.close()
                return False
                
            # تحديث آخر نشاط
            cursor.execute("""
                UPDATE user_sessions 
                SET last_activity = CURRENT_TIMESTAMP
                WHERE session_token = ?
            """, (session_token,))
            
            conn.commit()
            conn.close()
            
            # تحديث بيانات المستخدم الحالي
            self.current_user_id = user_id
            self.current_user_data = {
                'id': user_id,
                'username': username,
                'full_name': full_name,
                'role': role
            }
            self.session_token = session_token
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من الجلسة: {str(e)}")
            return False
            
    def logout(self, session_token: str = None):
        """تسجيل الخروج"""
        try:
            token = session_token or self.session_token
            if not token:
                return
                
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE user_sessions 
                SET is_active = 0, ended_at = CURRENT_TIMESTAMP
                WHERE session_token = ?
            """, (token,))
            
            conn.commit()
            conn.close()
            
            if self.current_user_data:
                self.logger.info(f"تسجيل خروج للمستخدم: {self.current_user_data['username']}")
                
            # مسح بيانات الجلسة
            self.current_user_id = None
            self.current_user_data = None
            self.session_token = None
            self.session_expiry = None
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الخروج: {str(e)}")
            
    def check_permission(self, permission: str) -> bool:
        """التحقق من صلاحية المستخدم"""
        if not self.current_user_data:
            return False
            
        user_role = self.current_user_data.get('role', '')
        
        # صلاحيات المدير العام
        if user_role == 'admin':
            return True
            
        # صلاحيات المدير
        if user_role == 'manager':
            restricted_permissions = ['delete_user', 'modify_system_settings']
            return permission not in restricted_permissions
            
        # صلاحيات المستخدم العادي
        if user_role == 'user':
            allowed_permissions = [
                'view_remittances', 'create_remittance', 'edit_remittance',
                'view_suppliers', 'view_reports'
            ]
            return permission in allowed_permissions
            
        return False
        
    def log_action(self, action: str, details: Dict[str, Any] = None):
        """تسجيل إجراء المستخدم"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO audit_log (
                    user_id, action, details, ip_address, user_agent, created_at
                ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                self.current_user_id,
                action,
                json.dumps(details, ensure_ascii=False) if details else None,
                "127.0.0.1",  # سيتم تحديثه لاحقاً
                "ProShipment Desktop App"
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"إجراء مسجل: {action} - المستخدم: {self.current_user_id}")
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الإجراء: {str(e)}")
            
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """الحصول على بيانات المستخدم الحالي"""
        return self.current_user_data
        
    def is_authenticated(self) -> bool:
        """التحقق من حالة المصادقة"""
        return self.current_user_id is not None and self.session_token is not None
