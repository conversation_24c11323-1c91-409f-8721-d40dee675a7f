#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق مع تتبع الأخطاء
Run Application with Debug
"""

import sys
import traceback
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_with_debug():
    """تشغيل التطبيق مع تتبع الأخطاء"""
    
    print("🚀 تشغيل التطبيق مع تتبع الأخطاء")
    print("=" * 40)
    
    try:
        print("📋 فحص الاستيرادات...")
        
        # فحص الاستيرادات الأساسية
        from PySide6.QtWidgets import QApplication
        print("✅ PySide6 متاح")
        
        from src.database.universal_database_manager import UniversalDatabaseManager
        from src.database.oracle_config import DatabaseConfigManager
        print("✅ وحدات قاعدة البيانات متاحة")
        
        from src.ui.main_window import MainWindow
        print("✅ واجهة التطبيق متاحة")
        
        from src.utils.arabic_support import setup_arabic_support
        print("✅ دعم اللغة العربية متاح")
        
        print("\n🔧 إعداد قاعدة البيانات...")
        
        # إعداد قاعدة البيانات
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        print(f"✅ تم تحميل التكوين: {config.type.value}")
        
        db_manager = UniversalDatabaseManager(config)
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # اختبار الاتصال
        if db_manager.test_connection():
            print("✅ تم اختبار الاتصال بنجاح")
        else:
            print("⚠️ فشل في اختبار الاتصال، لكن سنحاول المتابعة")
        
        # تهيئة قاعدة البيانات
        if db_manager.initialize_database():
            print("✅ تم تهيئة قاعدة البيانات")
        else:
            print("⚠️ مشكلة في تهيئة قاعدة البيانات")
        
        print("\n🖥️ تشغيل واجهة التطبيق...")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        print("✅ تم إنشاء QApplication")
        
        # إعداد دعم اللغة العربية
        setup_arabic_support(app)
        print("✅ تم إعداد دعم اللغة العربية")
        
        # إنشاء النافذة الرئيسية
        window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # عرض النافذة
        window.show()
        print("✅ تم عرض النافذة")
        
        print("\n🎉 تم تشغيل التطبيق بنجاح!")
        print("💡 يمكنك الآن استخدام التطبيق")
        print("💡 للوصول لإدارة وحدات القياس: إدارة الأصناف → وحدات القياس")
        
        # تشغيل حلقة الأحداث
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت جميع المكتبات المطلوبة")
        traceback.print_exc()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print("💡 تفاصيل الخطأ:")
        traceback.print_exc()
        
        # محاولة تشخيص المشكلة
        print("\n🔍 تشخيص المشكلة...")
        
        try:
            # فحص ملف التكوين
            config_file = Path("src/database/config/database.json")
            if config_file.exists():
                print("✅ ملف التكوين موجود")
            else:
                print("❌ ملف التكوين غير موجود")
            
            # فحص مجلد البيانات
            data_dir = Path("data")
            if not data_dir.exists():
                print("📁 إنشاء مجلد البيانات...")
                data_dir.mkdir(exist_ok=True)
                print("✅ تم إنشاء مجلد البيانات")
            else:
                print("✅ مجلد البيانات موجود")
                
        except Exception as diag_error:
            print(f"❌ خطأ في التشخيص: {diag_error}")

def main():
    """الدالة الرئيسية"""
    run_with_debug()

if __name__ == "__main__":
    main()
