#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام SHIPMENT ERP المطور
Enhanced SHIPMENT ERP System Launcher
"""

import sys
import os
from datetime import datetime

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║               🚢 SHIPMENT ERP SYSTEM 🚢                      ║
    ║                                                              ║
    ║              نظام إدارة الشحنات المتقدم                     ║
    ║                                                              ║
    ║                    الإصدار 1.0.0                            ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_system():
    """فحص النظام والمتطلبات"""
    print("🔍 فحص النظام...")
    
    # فحص Python
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python {python_version.major}.{python_version.minor}.{python_version.micro} - يتطلب 3.8+")
        return False
    
    # فحص PySide6
    try:
        import PySide6
        print(f"✅ PySide6 {PySide6.__version__}")
    except ImportError:
        print("❌ PySide6 غير مثبت - قم بتثبيته: pip install PySide6")
        return False
    
    # فحص الملفات المطلوبة
    required_files = [
        "main_window_prototype.py",
        "enhanced_ui_components.py", 
        "advanced_tree_menu.py",
        "run_prototype.py"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    return True

def show_features():
    """عرض ميزات النظام"""
    print("\n🎯 ميزات النظام الجديدة:")
    print("   📦 تم تغيير الاسم من CnX ERP إلى SHIPMENT ERP")
    print("   🌳 شجرة أنظمة متقدمة مع 6 أنظمة رئيسية")
    print("   🔍 بحث متقدم في الوظائف")
    print("   🖱️ قوائم منبثقة تفاعلية")
    print("   📊 48 وظيفة موزعة على الأنظمة")
    print("   🎨 تصميم محسن مع ألوان متدرجة")
    print("   ⚡ استجابة سريعة وتفاعل محسن")

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 65)
    
    # فحص النظام
    if not check_system():
        print("\n❌ فشل في فحص النظام!")
        print("🔧 يرجى إصلاح المشاكل المذكورة أعلاه")
        input("\nاضغط Enter للخروج...")
        return
    
    # عرض الميزات
    show_features()
    
    print("\n" + "=" * 65)
    print("🚀 بدء تشغيل SHIPMENT ERP...")
    print("=" * 65)
    
    # تشغيل النظام
    try:
        print("📱 تحميل الواجهة الرئيسية...")
        os.system("python run_prototype.py")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في التشغيل: {e}")
        print("🔧 تحقق من سلامة الملفات وأعد المحاولة")
    
    print("\n👋 شكراً لاستخدام SHIPMENT ERP!")

if __name__ == "__main__":
    main()
