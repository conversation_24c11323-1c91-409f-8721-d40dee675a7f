#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكتبة القوالب - SHIPMENT ERP Template Library
مكتبة شاملة لجميع قوالب النماذج في النظام
"""

import sys
import os
from typing import Dict, Any, Optional, Type
from PySide6.QtWidgets import QMainWindow, QApplication, QMessageBox
from PySide6.QtCore import Qt, QObject, Signal
from PySide6.QtGui import QFont

# استيراد القوالب
from form_template import FormTemplate

class TemplateRegistry:
    """سجل القوالب - يحتوي على جميع القوالب المتاحة"""
    
    def __init__(self):
        self._templates = {}
        self._register_default_templates()
    
    def _register_default_templates(self):
        """تسجيل القوالب الافتراضية"""
        self.register_template(
            name="نموذج_ادخال_اساسي",
            display_name="نموذج إدخال أساسي",
            description="نموذج أساسي لإدخال بيانات الشحنات والعملاء",
            template_class=FormTemplate,
            category="نماذج_البيانات",
            version="1.0.0",
            author="فريق SHIPMENT Solutions",
            tags=["أساسي", "إدخال_بيانات", "شحنات", "عملاء"]
        )
    
    def register_template(self, name: str, display_name: str, description: str, 
                         template_class: Type[QMainWindow], category: str = "عام",
                         version: str = "1.0.0", author: str = "غير محدد",
                         tags: list = None, **kwargs):
        """تسجيل قالب جديد في المكتبة"""
        
        if tags is None:
            tags = []
        
        template_info = {
            'name': name,
            'display_name': display_name,
            'description': description,
            'template_class': template_class,
            'category': category,
            'version': version,
            'author': author,
            'tags': tags,
            'registered_date': self._get_current_date(),
            'kwargs': kwargs
        }
        
        self._templates[name] = template_info
        print(f"✅ تم تسجيل القالب: {display_name}")
    
    def get_template(self, name: str) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات قالب"""
        return self._templates.get(name)
    
    def get_all_templates(self) -> Dict[str, Dict[str, Any]]:
        """الحصول على جميع القوالب"""
        return self._templates.copy()
    
    def get_templates_by_category(self, category: str) -> Dict[str, Dict[str, Any]]:
        """الحصول على القوالب حسب الفئة"""
        return {
            name: info for name, info in self._templates.items()
            if info['category'] == category
        }
    
    def search_templates(self, query: str) -> Dict[str, Dict[str, Any]]:
        """البحث في القوالب"""
        query = query.lower()
        results = {}
        
        for name, info in self._templates.items():
            # البحث في الاسم والوصف والعلامات
            if (query in info['display_name'].lower() or
                query in info['description'].lower() or
                any(query in tag.lower() for tag in info['tags'])):
                results[name] = info
        
        return results
    
    def list_templates(self) -> list:
        """قائمة بأسماء جميع القوالب"""
        return list(self._templates.keys())
    
    def _get_current_date(self) -> str:
        """الحصول على التاريخ الحالي"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

class TemplateManager(QObject):
    """مدير القوالب - يدير إنشاء وتشغيل القوالب"""
    
    # إشارات مخصصة
    template_created = Signal(str, object)  # اسم القالب، كائن القالب
    template_closed = Signal(str)           # اسم القالب
    
    def __init__(self):
        super().__init__()
        self.registry = TemplateRegistry()
        self.active_templates = {}  # القوالب النشطة
        self.app = None
    
    def set_application(self, app: QApplication):
        """تعيين تطبيق Qt"""
        self.app = app
    
    def create_template(self, template_name: str, fullscreen: bool = True, 
                       show_immediately: bool = True, **kwargs) -> Optional[QMainWindow]:
        """إنشاء قالب جديد"""
        
        template_info = self.registry.get_template(template_name)
        if not template_info:
            print(f"❌ القالب '{template_name}' غير موجود")
            return None
        
        try:
            # إنشاء كائن القالب
            template_class = template_info['template_class']
            
            # دمج المعاملات
            all_kwargs = template_info['kwargs'].copy()
            all_kwargs.update(kwargs)
            all_kwargs['fullscreen'] = fullscreen
            
            # إنشاء القالب
            template_instance = template_class(**all_kwargs)
            
            # تخصيص العنوان
            template_instance.setWindowTitle(
                f"{template_info['display_name']} - SHIPMENT ERP v{template_info['version']}"
            )
            
            # حفظ في القوالب النشطة
            instance_id = f"{template_name}_{len(self.active_templates)}"
            self.active_templates[instance_id] = {
                'template': template_instance,
                'info': template_info,
                'created_date': self.registry._get_current_date()
            }
            
            # ربط إشارة الإغلاق
            template_instance.destroyed.connect(
                lambda: self._on_template_closed(instance_id)
            )
            
            # إظهار القالب
            if show_immediately:
                template_instance.show()
            
            # إرسال إشارة الإنشاء
            self.template_created.emit(template_name, template_instance)
            
            print(f"✅ تم إنشاء القالب: {template_info['display_name']}")
            return template_instance
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء القالب '{template_name}': {e}")
            return None
    
    def _on_template_closed(self, instance_id: str):
        """معالج إغلاق القالب"""
        if instance_id in self.active_templates:
            template_name = self.active_templates[instance_id]['info']['name']
            del self.active_templates[instance_id]
            self.template_closed.emit(template_name)
            print(f"🔒 تم إغلاق القالب: {template_name}")
    
    def get_active_templates(self) -> Dict[str, Dict[str, Any]]:
        """الحصول على القوالب النشطة"""
        return self.active_templates.copy()
    
    def close_all_templates(self):
        """إغلاق جميع القوالب النشطة"""
        for instance_id, template_data in list(self.active_templates.items()):
            template_data['template'].close()
    
    def show_template_info(self, template_name: str):
        """إظهار معلومات القالب"""
        template_info = self.registry.get_template(template_name)
        if not template_info:
            print(f"❌ القالب '{template_name}' غير موجود")
            return
        
        info_text = f"""
📋 معلومات القالب:

🏷️ الاسم: {template_info['display_name']}
🔖 الاسم المرجعي: {template_info['name']}
📝 الوصف: {template_info['description']}
📂 الفئة: {template_info['category']}
🔢 الإصدار: {template_info['version']}
👨‍💻 المطور: {template_info['author']}
📅 تاريخ التسجيل: {template_info['registered_date']}
🏷️ العلامات: {', '.join(template_info['tags'])}
        """
        
        print(info_text)
        return template_info

class TemplateLibrary:
    """المكتبة الرئيسية للقوالب - واجهة سهلة الاستخدام"""
    
    _instance = None
    _manager = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._manager = TemplateManager()
        return cls._instance
    
    @classmethod
    def get_manager(cls) -> TemplateManager:
        """الحصول على مدير القوالب"""
        if cls._manager is None:
            cls._manager = TemplateManager()
        return cls._manager
    
    @classmethod
    def create(cls, template_name: str, fullscreen: bool = True, **kwargs) -> Optional[QMainWindow]:
        """إنشاء قالب - الطريقة السريعة"""
        return cls.get_manager().create_template(template_name, fullscreen, **kwargs)
    
    @classmethod
    def نموذج_ادخال_اساسي(cls, fullscreen: bool = True, **kwargs) -> Optional[QMainWindow]:
        """إنشاء نموذج إدخال أساسي - اختصار مباشر"""
        return cls.create("نموذج_ادخال_اساسي", fullscreen, **kwargs)
    
    @classmethod
    def list_all(cls) -> list:
        """قائمة جميع القوالب المتاحة"""
        return cls.get_manager().registry.list_templates()
    
    @classmethod
    def search(cls, query: str) -> Dict[str, Dict[str, Any]]:
        """البحث في القوالب"""
        return cls.get_manager().registry.search_templates(query)
    
    @classmethod
    def info(cls, template_name: str):
        """إظهار معلومات القالب"""
        return cls.get_manager().show_template_info(template_name)
    
    @classmethod
    def register_new(cls, name: str, display_name: str, description: str,
                    template_class: Type[QMainWindow], **kwargs):
        """تسجيل قالب جديد"""
        cls.get_manager().registry.register_template(
            name, display_name, description, template_class, **kwargs
        )
    
    @classmethod
    def setup_app(cls, app: QApplication):
        """إعداد التطبيق"""
        cls.get_manager().set_application(app)
        
        # إعداد الخط الافتراضي
        font = QFont("Arial", 10)
        app.setFont(font)
        
        # إعداد اتجاه النص
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم إعداد مكتبة القوالب")

# اختصارات سريعة للاستخدام المباشر
Templates = TemplateLibrary

def create_template(template_name: str, fullscreen: bool = True, **kwargs):
    """دالة سريعة لإنشاء قالب"""
    return TemplateLibrary.create(template_name, fullscreen, **kwargs)

def نموذج_ادخال_اساسي(fullscreen: bool = True, **kwargs):
    """دالة سريعة لإنشاء نموذج إدخال أساسي"""
    return TemplateLibrary.نموذج_ادخال_اساسي(fullscreen, **kwargs)
