# -*- coding: utf-8 -*-
"""
مدير الستايلات والثيمات
Style and Theme Manager
"""

import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QFile, QTextStream

class StyleManager:
    """مدير الستايلات والثيمات"""
    
    def __init__(self):
        self.styles_dir = Path(__file__).parent
        self.current_theme = "advanced"
        self.advanced_themes = {
            "advanced": "الثيم المتقدم",
            "glassmorphism": "ثيم الزجاج",
            "neon": "ثيم النيون",
            "gradient": "ثيم التدرجات",
            "modern": "ثيم حديث"
        }
    
    def load_theme(self, theme_name="advanced"):
        """تحميل ثيم محدد"""
        try:
            # محاولة تحميل الثيم المتقدم أولاً
            if theme_name in self.advanced_themes:
                style_sheet = self.get_advanced_theme_style(theme_name)
                if style_sheet:
                    app = QApplication.instance()
                    if app:
                        app.setStyleSheet(style_sheet)
                        self.current_theme = theme_name
                        print(f"تم تحميل الثيم المتقدم: {self.advanced_themes[theme_name]}")
                        return True

            # محاولة تحميل من ملف QSS
            theme_file = self.styles_dir / f"{theme_name}_theme.qss"

            if not theme_file.exists():
                print(f"ملف الثيم غير موجود: {theme_file}")
                return False

            with open(theme_file, 'r', encoding='utf-8') as file:
                style_sheet = file.read()

            # تطبيق الستايل على التطبيق
            app = QApplication.instance()
            if app:
                app.setStyleSheet(style_sheet)
                self.current_theme = theme_name
                print(f"تم تحميل ثيم: {theme_name}")
                return True

        except Exception as e:
            print(f"خطأ في تحميل الثيم: {e}")
            return False

        return False

    def get_advanced_theme_style(self, theme_name):
        """إنشاء أنماط الثيمات المتقدمة"""
        if theme_name == "advanced":
            return self.get_advanced_gradient_theme()
        elif theme_name == "glassmorphism":
            return self.get_glassmorphism_theme()
        elif theme_name == "neon":
            return self.get_neon_theme()
        elif theme_name == "gradient":
            return self.get_gradient_theme()
        return None

    def get_advanced_gradient_theme(self):
        """ثيم التدرجات المتقدم"""
        return """
        /* الثيم المتقدم مع التدرجات والشفافية */
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #667eea, stop:0.3 #764ba2, stop:0.7 #f093fb, stop:1 #f5576c);
        }

        QFrame {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #667eea, stop:1 #764ba2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            color: white;
            font-weight: bold;
            padding: 15px 25px;
            font-size: 14px;
        }

        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #764ba2, stop:1 #667eea);
            border-color: rgba(255, 255, 255, 0.4);
        }

        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #5a67d8, stop:1 #6b46c1);
        }

        QLabel {
            color: #2d3748;
            font-weight: 500;
        }

        QScrollBar:vertical {
            background: rgba(255, 255, 255, 0.1);
            width: 14px;
            border-radius: 7px;
        }

        QScrollBar::handle:vertical {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(102, 126, 234, 0.8), stop:1 rgba(118, 75, 162, 0.8));
            border-radius: 7px;
            min-height: 30px;
            margin: 2px;
        }

        QScrollBar::handle:vertical:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
        }
        """

    def get_glassmorphism_theme(self):
        """ثيم الزجاج الشفاف"""
        return """
        /* ثيم الزجاج الشفاف */
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #a8edea, stop:0.5 #fed6e3, stop:1 #d299c2);
        }

        QFrame {
            background: rgba(255, 255, 255, 0.25);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        QPushButton {
            background: rgba(255, 255, 255, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.18);
            border-radius: 16px;
            color: #2d3748;
            font-weight: bold;
            padding: 15px 25px;
        }

        QPushButton:hover {
            background: rgba(255, 255, 255, 0.35);
            border-color: rgba(255, 255, 255, 0.3);
        }
        """

    def get_neon_theme(self):
        """ثيم النيون المتوهج"""
        return """
        /* ثيم النيون المتوهج */
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #0f0f23, stop:0.5 #1a1a2e, stop:1 #16213e);
        }

        QFrame {
            background: rgba(26, 26, 46, 0.8);
            border-radius: 16px;
            border: 2px solid #00ffff;
        }

        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #ff006e, stop:1 #8338ec);
            border: 2px solid #00ffff;
            border-radius: 16px;
            color: white;
            font-weight: bold;
            padding: 15px 25px;
        }

        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #8338ec, stop:1 #ff006e);
        }

        QLabel {
            color: #00ffff;
            font-weight: bold;
        }
        """

    def get_gradient_theme(self):
        """ثيم التدرجات الملونة"""
        return """
        /* ثيم التدرجات الملونة */
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #ff9a9e, stop:0.25 #fecfef, stop:0.5 #fecfef, stop:0.75 #fad0c4, stop:1 #ffd1ff);
        }

        QFrame {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.5);
        }

        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #ff9a9e, stop:1 #fad0c4);
            border: none;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            padding: 15px 25px;
        }

        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #fad0c4, stop:1 #ff9a9e);
        }
        """

    def get_available_themes(self):
        """الحصول على قائمة الثيمات المتاحة"""
        themes = []

        # إضافة الثيمات المتقدمة
        for theme_key, theme_name in self.advanced_themes.items():
            themes.append({
                'key': theme_key,
                'name': theme_name,
                'type': 'advanced'
            })

        # إضافة ثيمات الملفات
        for file in self.styles_dir.glob("*_theme.qss"):
            theme_name = file.stem.replace("_theme", "")
            if theme_name not in self.advanced_themes:
                themes.append({
                    'key': theme_name,
                    'name': theme_name.title(),
                    'type': 'file'
                })

        return themes
    
    def get_current_theme(self):
        """الحصول على الثيم الحالي"""
        return self.current_theme
    
    def create_gradient_style(self, start_color, end_color, direction="vertical"):
        """إنشاء ستايل متدرج"""
        if direction == "vertical":
            gradient = f"qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {start_color}, stop:1 {end_color})"
        else:  # horizontal
            gradient = f"qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {start_color}, stop:1 {end_color})"
        
        return gradient
    
    def create_button_style(self, bg_color, hover_color, text_color="white", border_radius=8):
        """إنشاء ستايل زر مخصص"""
        return f"""
            QPushButton {{
                background: {bg_color};
                color: {text_color};
                border: none;
                border-radius: {border_radius}px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background: {hover_color};
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background: {hover_color};
                transform: translateY(0px);
            }}
        """
    
    def create_card_style(self, bg_color="white", border_color="#e0e0e0", shadow=True):
        """إنشاء ستايل كارت"""
        shadow_style = ""
        if shadow:
            shadow_style = "box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);"
        
        return f"""
            QFrame {{
                background: {bg_color};
                border: 1px solid {border_color};
                border-radius: 12px;
                padding: 15px;
                {shadow_style}
            }}
        """
    
    def apply_dark_theme(self):
        """تطبيق الثيم المظلم"""
        dark_style = """
            QMainWindow {
                background: #2b2b2b;
                color: #ffffff;
            }
            
            QWidget {
                background: #2b2b2b;
                color: #ffffff;
            }
            
            QPushButton {
                background: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background: #505050;
                border-color: #777777;
            }
            
            QPushButton:pressed {
                background: #353535;
            }
            
            QLineEdit, QTextEdit, QPlainTextEdit {
                background: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 8px;
            }
            
            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
                border-color: #3498db;
            }
            
            QComboBox {
                background: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 6px;
            }
            
            QTableWidget, QTableView {
                background: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                gridline-color: #555555;
            }
            
            QHeaderView::section {
                background: #353535;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 8px;
                font-weight: bold;
            }
            
            QMenuBar {
                background: #353535;
                color: #ffffff;
                border-bottom: 1px solid #555555;
            }
            
            QMenuBar::item:selected {
                background: #505050;
            }
            
            QMenu {
                background: #404040;
                color: #ffffff;
                border: 1px solid #555555;
            }
            
            QMenu::item:selected {
                background: #3498db;
            }
            
            QStatusBar {
                background: #353535;
                color: #ffffff;
                border-top: 1px solid #555555;
            }
            
            QTabWidget::pane {
                background: #404040;
                border: 1px solid #555555;
            }
            
            QTabBar::tab {
                background: #353535;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 8px 16px;
                margin: 2px;
            }
            
            QTabBar::tab:selected {
                background: #3498db;
            }
            
            QScrollBar:vertical {
                background: #353535;
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #777777;
            }
        """
        
        app = QApplication.instance()
        if app:
            app.setStyleSheet(dark_style)
            self.current_theme = "dark"
    
    def apply_light_theme(self):
        """تطبيق الثيم الفاتح"""
        return self.load_theme("modern")
    
    def reset_style(self):
        """إعادة تعيين الستايل للافتراضي"""
        app = QApplication.instance()
        if app:
            app.setStyleSheet("")
            self.current_theme = "default"

# إنشاء مثيل عام من مدير الستايلات
style_manager = StyleManager()
