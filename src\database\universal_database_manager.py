#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات الشامل - ProShipment V2.0.0
Universal Database Manager supporting SQLite and Oracle
"""

import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any, Union
from datetime import datetime
from contextlib import contextmanager

from sqlalchemy import create_engine, text, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError, OperationalError
from sqlalchemy.pool import QueuePool, StaticPool

from .oracle_config import (
    DatabaseConfig, DatabaseType, OracleConfig, SQLiteConfig,
    DatabaseConfigManager, load_oracle_config_from_env
)

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseConnectionError(Exception):
    """خطأ في الاتصال بقاعدة البيانات"""
    pass

class DatabaseMigrationError(Exception):
    """خطأ في ترحيل قاعدة البيانات"""
    pass

class UniversalDatabaseManager:
    """مدير قاعدة البيانات الشامل"""
    
    def __init__(self, config: Optional[DatabaseConfig] = None):
        """تهيئة مدير قاعدة البيانات"""
        self.config = config or self._load_default_config()
        self.engine = None
        self.SessionLocal = None
        self._connection_pool = None
        self._is_connected = False
        
        # إعداد السجلات
        self.logger = logging.getLogger(f"{__name__}.{self.config.type.value}")
        
        # تهيئة الاتصال
        self._setup_database_connection()
    
    def _load_default_config(self) -> DatabaseConfig:
        """تحميل الإعدادات الافتراضية"""
        config_manager = DatabaseConfigManager()
        return config_manager.load_config()
    
    def _setup_database_connection(self):
        """إعداد اتصال قاعدة البيانات"""
        try:
            if self.config.type == DatabaseType.SQLITE:
                self._setup_sqlite()
            elif self.config.type == DatabaseType.ORACLE:
                self._setup_oracle()
            else:
                raise ValueError(f"نوع قاعدة البيانات غير مدعوم: {self.config.type}")
            
            # إنشاء session factory
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            self.logger.info(f"تم إعداد اتصال {self.config.type.value} بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد قاعدة البيانات: {e}")
            raise DatabaseConnectionError(f"فشل في إعداد قاعدة البيانات: {e}")
    
    def _setup_sqlite(self):
        """إعداد SQLite"""
        sqlite_config = self.config.sqlite_config
        
        # إنشاء مجلد البيانات
        db_path = Path(sqlite_config.path)
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # إنشاء connection string
        connection_string = f"sqlite:///{sqlite_config.path}"
        
        # إعدادات SQLite
        connect_args = {
            'check_same_thread': sqlite_config.check_same_thread,
            'timeout': sqlite_config.timeout
        }
        
        self.engine = create_engine(
            connection_string,
            echo=False,
            poolclass=StaticPool,
            connect_args=connect_args,
            pool_pre_ping=True
        )
        
        # تفعيل Foreign Keys في SQLite
        @event.listens_for(self.engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            cursor = dbapi_connection.cursor()
            cursor.execute("PRAGMA foreign_keys=ON")
            cursor.close()
        
        self.logger.info(f"تم إعداد SQLite: {sqlite_config.path}")
    
    def _setup_oracle(self):
        """إعداد Oracle"""
        oracle_config = self.config.oracle_config
        
        # بناء connection string
        connection_string = self._build_oracle_connection_string(oracle_config)
        
        # إعدادات الاتصال
        connect_args = {
            'encoding': oracle_config.encoding,
            'nencoding': oracle_config.nencoding,
            'threaded': oracle_config.threaded
        }
        
        # إضافة إعدادات SSL إذا كانت مفعلة
        if oracle_config.use_ssl:
            if oracle_config.wallet_location:
                connect_args['wallet_location'] = oracle_config.wallet_location
            if oracle_config.ssl_cert_path:
                connect_args['ssl_cert_path'] = oracle_config.ssl_cert_path
        
        self.engine = create_engine(
            connection_string,
            echo=False,
            poolclass=QueuePool,
            pool_size=oracle_config.pool_size,
            max_overflow=oracle_config.max_overflow,
            pool_timeout=oracle_config.pool_timeout,
            pool_recycle=oracle_config.pool_recycle,
            pool_pre_ping=oracle_config.pool_pre_ping,
            connect_args=connect_args
        )
        
        self.logger.info(f"تم إعداد Oracle: {oracle_config.host}:{oracle_config.port}")
    
    def _build_oracle_connection_string(self, config: OracleConfig) -> str:
        """بناء connection string لـ Oracle"""
        from .oracle_config import OracleConnectionType
        
        base_url = f"oracle+cx_oracle://{config.username}:{config.password}@{config.host}:{config.port}"
        
        if config.connection_type == OracleConnectionType.SERVICE_NAME:
            return f"{base_url}/?service_name={config.service_name}"
        elif config.connection_type == OracleConnectionType.SID:
            return f"{base_url}/{config.sid}"
        elif config.connection_type == OracleConnectionType.TNS:
            return f"oracle+cx_oracle://{config.username}:{config.password}@{config.service_name}"
        else:
            raise ValueError(f"نوع اتصال Oracle غير مدعوم: {config.connection_type}")
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            with self.engine.connect() as connection:
                if self.config.type == DatabaseType.SQLITE:
                    result = connection.execute(text("SELECT 1"))
                elif self.config.type == DatabaseType.ORACLE:
                    result = connection.execute(text("SELECT 1 FROM DUAL"))
                
                row = result.fetchone()
                if row and row[0] == 1:
                    self._is_connected = True
                    self.logger.info(f"اختبار الاتصال بـ {self.config.type.value} نجح")
                    return True
                else:
                    self._is_connected = False
                    self.logger.error(f"فشل اختبار الاتصال بـ {self.config.type.value}")
                    return False
                    
        except Exception as e:
            self._is_connected = False
            self.logger.error(f"خطأ في اختبار الاتصال: {e}")
            return False
    
    def initialize_database(self) -> bool:
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            # اختبار الاتصال أولاً
            if not self.test_connection():
                raise DatabaseConnectionError("فشل في اختبار الاتصال")
            
            # استيراد النماذج
            from .models import Base
            
            # إنشاء جميع الجداول
            Base.metadata.create_all(bind=self.engine)
            
            # إدراج البيانات الأساسية
            self._insert_default_data()
            
            self.logger.info(f"تم تهيئة قاعدة بيانات {self.config.type.value} بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False
    
    @contextmanager
    def get_session(self):
        """الحصول على جلسة قاعدة البيانات مع إدارة تلقائية"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self.logger.error(f"خطأ في جلسة قاعدة البيانات: {e}")
            raise
        finally:
            session.close()
    
    def get_raw_session(self) -> Session:
        """الحصول على جلسة خام (يجب إدارتها يدوياً)"""
        return self.SessionLocal()
    
    def close_all_sessions(self):
        """إغلاق جميع الجلسات النشطة"""
        try:
            if self.engine:
                self.engine.dispose()
                self._is_connected = False
                self.logger.info("تم إغلاق جميع الجلسات")
        except Exception as e:
            self.logger.error(f"خطأ في إغلاق الجلسات: {e}")
    
    def _insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        with self.get_session() as session:
            from .models import SystemSettings, Currency, FiscalYear, Company, UnitOfMeasure, ItemGroup

            # إدراج البيانات بشكل منفصل
            self._insert_system_settings(session)
            self._insert_currencies(session)
            self._insert_fiscal_year(session)
            self._insert_company(session)
            self._insert_units_of_measure(session)
            self._insert_item_groups(session)

            self.logger.info(f"تم إدراج البيانات الافتراضية في {self.config.type.value}")

    def _insert_system_settings(self, session):
        """إدراج إعدادات النظام"""
        # التحقق من وجود إعدادات النظام
        if session.query(SystemSettings).count() > 0:
            return
            
        # إعدادات النظام
        default_settings = [
            SystemSettings(
                key="app_name",
                value="نظام إدارة الشحنات",
                description="اسم التطبيق",
                category="general"
            ),
            SystemSettings(
                key="app_version",
                value="2.0.0",
                description="إصدار التطبيق",
                category="general"
            ),
            SystemSettings(
                key="database_type",
                value=self.config.type.value.upper(),
                description="نوع قاعدة البيانات",
                category="system"
            ),
            SystemSettings(
                key="default_currency",
                value="SAR",
                description="العملة الافتراضية",
                category="financial"
            )
        ]

        session.add_all(default_settings)

    def _insert_currencies(self, session):
        """إدراج العملات الافتراضية"""
        from .models import Currency

        # التحقق من وجود عملات
        if session.query(Currency).count() > 0:
            return
            
        # العملات الافتراضية
        default_currencies = [
            Currency(
                code="SAR",
                name="ريال سعودي",
                name_en="Saudi Riyal",
                symbol="ر.س",
                exchange_rate=1.0,
                is_base=True
            ),
            Currency(
                code="USD",
                name="دولار أمريكي",
                name_en="US Dollar",
                symbol="$",
                exchange_rate=3.75
            ),
            Currency(
                code="EUR",
                name="يورو",
                name_en="Euro",
                symbol="€",
                exchange_rate=4.10
            )
        ]

        session.add_all(default_currencies)

    def _insert_fiscal_year(self, session):
        """إدراج السنة المالية"""
        from .models import FiscalYear

        # التحقق من وجود سنة مالية
        if session.query(FiscalYear).count() > 0:
            return
            
        # السنة المالية
        current_year = datetime.now().year
        fiscal_year = FiscalYear(
            year=current_year,
            start_date=datetime(current_year, 1, 1),
            end_date=datetime(current_year, 12, 31),
            is_current=True
        )

        session.add(fiscal_year)

    def _insert_company(self, session):
        """إدراج بيانات الشركة"""
        from .models import Company

        # التحقق من وجود شركة
        if session.query(Company).count() > 0:
            return
            
        # بيانات الشركة
        default_company = Company(
            name="شركة الشحنات المتقدمة",
            name_en="Advanced Shipping Company",
            address="الرياض، المملكة العربية السعودية",
            phone="+966-11-1234567",
            email="<EMAIL>"
        )

        session.add(default_company)

    def _insert_units_of_measure(self, session):
        """إدراج وحدات القياس الافتراضية"""
        from .models import UnitOfMeasure

        # التحقق من وجود وحدات قياس
        if session.query(UnitOfMeasure).count() > 0:
            return

        # وحدات القياس الافتراضية
        default_units = [
                # وحدات الوزن
                UnitOfMeasure(
                    name="كيلوجرام",
                    name_en="Kilogram",
                    symbol="كجم",
                    symbol_en="kg",
                    description="وحدة قياس الوزن الأساسية"
                ),
                UnitOfMeasure(
                    name="جرام",
                    name_en="Gram",
                    symbol="جم",
                    symbol_en="g",
                    description="وحدة قياس الوزن الصغيرة"
                ),
                UnitOfMeasure(
                    name="طن",
                    name_en="Ton",
                    symbol="طن",
                    symbol_en="t",
                    description="وحدة قياس الوزن الكبيرة"
                ),
                # وحدات الطول
                UnitOfMeasure(
                    name="متر",
                    name_en="Meter",
                    symbol="م",
                    symbol_en="m",
                    description="وحدة قياس الطول الأساسية"
                ),
                UnitOfMeasure(
                    name="سنتيمتر",
                    name_en="Centimeter",
                    symbol="سم",
                    symbol_en="cm",
                    description="وحدة قياس الطول الصغيرة"
                ),
                UnitOfMeasure(
                    name="مليمتر",
                    name_en="Millimeter",
                    symbol="مم",
                    symbol_en="mm",
                    description="وحدة قياس الطول الدقيقة"
                ),
                # وحدات المساحة
                UnitOfMeasure(
                    name="متر مربع",
                    name_en="Square Meter",
                    symbol="م²",
                    symbol_en="m²",
                    description="وحدة قياس المساحة"
                ),
                # وحدات الحجم
                UnitOfMeasure(
                    name="متر مكعب",
                    name_en="Cubic Meter",
                    symbol="م³",
                    symbol_en="m³",
                    description="وحدة قياس الحجم"
                ),
                UnitOfMeasure(
                    name="لتر",
                    name_en="Liter",
                    symbol="لتر",
                    symbol_en="L",
                    description="وحدة قياس السوائل"
                ),
                # وحدات العد
                UnitOfMeasure(
                    name="قطعة",
                    name_en="Piece",
                    symbol="قطعة",
                    symbol_en="pcs",
                    description="وحدة العد الأساسية"
                ),
                UnitOfMeasure(
                    name="عبوة",
                    name_en="Package",
                    symbol="عبوة",
                    symbol_en="pkg",
                    description="وحدة التعبئة"
                ),
                UnitOfMeasure(
                    name="صندوق",
                    name_en="Box",
                    symbol="صندوق",
                    symbol_en="box",
                    description="وحدة التعبئة الكبيرة"
                ),
                UnitOfMeasure(
                    name="كرتون",
                    name_en="Carton",
                    symbol="كرتون",
                    symbol_en="ctn",
                    description="وحدة التعبئة المتوسطة"
                ),
                # وحدات خاصة
                UnitOfMeasure(
                    name="زوج",
                    name_en="Pair",
                    symbol="زوج",
                    symbol_en="pair",
                    description="وحدة للأشياء المزدوجة"
                ),
                UnitOfMeasure(
                    name="دستة",
                    name_en="Dozen",
                    symbol="دستة",
                    symbol_en="dz",
                    description="12 قطعة"
                )
        ]

        session.add_all(default_units)

    def _insert_item_groups(self, session):
        """إدراج مجموعات الأصناف الافتراضية"""
        from .models import ItemGroup

        # التحقق من وجود مجموعات أصناف
        if session.query(ItemGroup).count() > 0:
            return

        # مجموعات الأصناف الافتراضية
        default_item_groups = [
            ItemGroup(
                name="إلكترونيات",
                name_en="Electronics",
                description="الأجهزة والمعدات الإلكترونية"
            ),
            ItemGroup(
                name="ملابس",
                name_en="Clothing",
                description="الملابس والأزياء"
            ),
            ItemGroup(
                name="أغذية",
                name_en="Food",
                description="المواد الغذائية والمشروبات"
            ),
            ItemGroup(
                name="أدوات منزلية",
                name_en="Home Appliances",
                description="الأدوات والأجهزة المنزلية"
            ),
            ItemGroup(
                name="مواد بناء",
                name_en="Construction Materials",
                description="مواد ومعدات البناء"
            ),
            ItemGroup(
                name="قطع غيار",
                name_en="Spare Parts",
                description="قطع الغيار والمكونات"
            ),
            ItemGroup(
                name="مستحضرات تجميل",
                name_en="Cosmetics",
                description="مستحضرات التجميل والعناية"
            ),
            ItemGroup(
                name="كتب ومطبوعات",
                name_en="Books & Publications",
                description="الكتب والمطبوعات والقرطاسية"
            )
        ]

        session.add_all(default_item_groups)
    
    def backup_database(self, backup_path: Optional[str] = None) -> bool:
        """إنشاء نسخة احتياطية"""
        try:
            if backup_path is None:
                backup_dir = Path("backups")
                backup_dir.mkdir(exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                
                if self.config.type == DatabaseType.SQLITE:
                    backup_path = backup_dir / f"sqlite_backup_{timestamp}.db"
                elif self.config.type == DatabaseType.ORACLE:
                    backup_path = backup_dir / f"oracle_backup_{timestamp}.sql"
            
            if self.config.type == DatabaseType.SQLITE:
                # نسخ ملف SQLite
                import shutil
                shutil.copy2(self.config.sqlite_config.path, backup_path)
                self.logger.info(f"تم إنشاء نسخة احتياطية SQLite: {backup_path}")
                
            elif self.config.type == DatabaseType.ORACLE:
                # لـ Oracle، نحتاج أدوات خاصة
                oracle_config = self.config.oracle_config
                self.logger.info("لإنشاء نسخة احتياطية من Oracle، استخدم:")
                self.logger.info(f"expdp {oracle_config.username}/*****@{oracle_config.host}:{oracle_config.port}/{oracle_config.service_name}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def get_database_info(self) -> Dict[str, Any]:
        """الحصول على معلومات قاعدة البيانات"""
        info = {
            'type': self.config.type.value,
            'connected': self._is_connected,
            'engine_info': str(self.engine.url) if self.engine else None
        }
        
        if self.config.type == DatabaseType.SQLITE:
            info.update({
                'path': self.config.sqlite_config.path,
                'size': self._get_sqlite_size()
            })
        elif self.config.type == DatabaseType.ORACLE:
            oracle_config = self.config.oracle_config
            info.update({
                'host': oracle_config.host,
                'port': oracle_config.port,
                'service_name': oracle_config.service_name,
                'pool_size': oracle_config.pool_size
            })
        
        return info
    
    def _get_sqlite_size(self) -> Optional[int]:
        """الحصول على حجم ملف SQLite"""
        try:
            db_path = Path(self.config.sqlite_config.path)
            if db_path.exists():
                return db_path.stat().st_size
        except Exception:
            pass
        return None
    
    @property
    def is_connected(self) -> bool:
        """حالة الاتصال"""
        return self._is_connected
    
    @property
    def database_type(self) -> DatabaseType:
        """نوع قاعدة البيانات"""
        return self.config.type

# دوال مساعدة لإنشاء مديري قواعد البيانات
def create_sqlite_manager(db_path: str = "data/proshipment.db") -> UniversalDatabaseManager:
    """إنشاء مدير SQLite"""
    config = DatabaseConfig(
        type=DatabaseType.SQLITE,
        sqlite_config=SQLiteConfig(path=db_path)
    )
    return UniversalDatabaseManager(config)

def create_oracle_manager(
    host: str = "localhost",
    port: int = 1521,
    service_name: str = "XE",
    username: str = "proshipment",
    password: str = ""
) -> UniversalDatabaseManager:
    """إنشاء مدير Oracle"""
    config = DatabaseConfig(
        type=DatabaseType.ORACLE,
        oracle_config=OracleConfig(
            host=host,
            port=port,
            service_name=service_name,
            username=username,
            password=password
        )
    )
    return UniversalDatabaseManager(config)

def create_manager_from_env() -> UniversalDatabaseManager:
    """إنشاء مدير من متغيرات البيئة"""
    db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()

    if db_type == 'oracle':
        oracle_config = load_oracle_config_from_env()
        config = DatabaseConfig(
            type=DatabaseType.ORACLE,
            oracle_config=oracle_config
        )
    else:
        sqlite_path = os.getenv('SQLITE_PATH', 'data/proshipment.db')
        config = DatabaseConfig(
            type=DatabaseType.SQLITE,
            sqlite_config=SQLiteConfig(path=sqlite_path)
        )

    return UniversalDatabaseManager(config)

# مثال على الاستخدام
if __name__ == "__main__":
    # اختبار SQLite
    print("🔄 اختبار SQLite...")
    sqlite_manager = create_sqlite_manager("data/test_v2.db")

    if sqlite_manager.test_connection():
        print("✅ اتصال SQLite نجح")
        if sqlite_manager.initialize_database():
            print("✅ تهيئة SQLite نجحت")
        else:
            print("❌ فشل في تهيئة SQLite")
    else:
        print("❌ فشل اتصال SQLite")

    # اختبار Oracle (إذا كان متاحاً)
    print("\n🔄 اختبار Oracle...")
    try:
        oracle_manager = create_oracle_manager(
            host="localhost",
            port=1521,
            service_name="XE",
            username="proshipment",
            password="test123"
        )

        if oracle_manager.test_connection():
            print("✅ اتصال Oracle نجح")
            if oracle_manager.initialize_database():
                print("✅ تهيئة Oracle نجحت")
            else:
                print("❌ فشل في تهيئة Oracle")
        else:
            print("⚠️ Oracle غير متاح أو إعدادات خاطئة")

    except Exception as e:
        print(f"⚠️ Oracle غير متاح: {e}")

    print("\n✅ انتهى الاختبار")
