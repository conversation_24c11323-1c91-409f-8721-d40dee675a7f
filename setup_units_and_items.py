#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد شامل لوحدات القياس والأصناف
Complete Setup for Units of Measurement and Items
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.universal_database_manager import UniversalDatabaseManager
from src.database.oracle_config import DatabaseConfigManager
from src.database.models import UnitOfMeasure, ItemGroup, Item

def setup_complete_system():
    """إعداد النظام الكامل"""
    
    print("🚀 إعداد نظام إدارة الأصناف ووحدات القياس")
    print("=" * 60)
    
    try:
        # تحميل التكوين
        print("📋 تحميل إعدادات قاعدة البيانات...")
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        print(f"   نوع قاعدة البيانات: {config.type.value}")
        
        # اختبار الاتصال
        print("🔗 اختبار الاتصال بقاعدة البيانات...")
        if not db_manager.test_connection():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
        print("   ✅ تم الاتصال بنجاح")
        
        # تهيئة قاعدة البيانات
        print("🏗️ تهيئة قاعدة البيانات...")
        if not db_manager.initialize_database():
            print("❌ فشل في تهيئة قاعدة البيانات")
            return False
        print("   ✅ تم تهيئة قاعدة البيانات بنجاح")
        
        # عرض الإحصائيات
        print("\n📊 إحصائيات النظام:")
        show_statistics(db_manager)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد النظام: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_statistics(db_manager):
    """عرض إحصائيات النظام"""
    
    try:
        with db_manager.get_session() as session:
            # إحصائيات وحدات القياس
            total_units = session.query(UnitOfMeasure).count()
            active_units = session.query(UnitOfMeasure).filter_by(is_active=True).count()
            
            # إحصائيات مجموعات الأصناف
            total_groups = session.query(ItemGroup).count()
            active_groups = session.query(ItemGroup).filter_by(is_active=True).count()
            
            # إحصائيات الأصناف
            total_items = session.query(Item).count()
            active_items = session.query(Item).filter_by(is_active=True).count()
            
            print(f"   📏 وحدات القياس: {total_units} (نشطة: {active_units})")
            print(f"   📂 مجموعات الأصناف: {total_groups} (نشطة: {active_groups})")
            print(f"   📦 الأصناف: {total_items} (نشطة: {active_items})")
            
            # عرض أمثلة من وحدات القياس
            print(f"\n📏 أمثلة من وحدات القياس:")
            units = session.query(UnitOfMeasure).filter_by(is_active=True).limit(10).all()
            for unit in units:
                print(f"   • {unit.name} ({unit.symbol}) - {unit.name_en}")
            
            # عرض أمثلة من مجموعات الأصناف
            print(f"\n📂 مجموعات الأصناف:")
            groups = session.query(ItemGroup).filter_by(is_active=True).all()
            for group in groups:
                items_count = session.query(Item).filter_by(group_id=group.id, is_active=True).count()
                print(f"   • {group.name} ({items_count} صنف)")
            
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {e}")

def show_sample_units():
    """عرض نماذج من وحدات القياس"""
    
    print("\n📏 نماذج من وحدات القياس المتاحة:")
    print("-" * 50)
    
    try:
        config_manager = DatabaseConfigManager("src/database/config/database.json")
        config = config_manager.load_config()
        db_manager = UniversalDatabaseManager(config)
        
        with db_manager.get_session() as session:
            units = session.query(UnitOfMeasure).filter_by(is_active=True).order_by(UnitOfMeasure.name).all()
            
            if not units:
                print("   لا توجد وحدات قياس مسجلة")
                return
            
            # تجميع الوحدات حسب النوع
            weight_units = []
            length_units = []
            volume_units = []
            count_units = []
            other_units = []
            
            for unit in units:
                name_lower = unit.name.lower()
                if any(word in name_lower for word in ['كيلو', 'جرام', 'طن', 'وزن']):
                    weight_units.append(unit)
                elif any(word in name_lower for word in ['متر', 'سنتي', 'مليمتر', 'طول']):
                    length_units.append(unit)
                elif any(word in name_lower for word in ['لتر', 'مكعب', 'حجم']):
                    volume_units.append(unit)
                elif any(word in name_lower for word in ['قطعة', 'صندوق', 'عبوة', 'زوج', 'دستة']):
                    count_units.append(unit)
                else:
                    other_units.append(unit)
            
            if weight_units:
                print("🏋️ وحدات الوزن:")
                for unit in weight_units:
                    print(f"   • {unit.name} ({unit.symbol}) - {unit.name_en}")
            
            if length_units:
                print("\n📐 وحدات الطول:")
                for unit in length_units:
                    print(f"   • {unit.name} ({unit.symbol}) - {unit.name_en}")
            
            if volume_units:
                print("\n🧊 وحدات الحجم:")
                for unit in volume_units:
                    print(f"   • {unit.name} ({unit.symbol}) - {unit.name_en}")
            
            if count_units:
                print("\n🔢 وحدات العد:")
                for unit in count_units:
                    print(f"   • {unit.name} ({unit.symbol}) - {unit.name_en}")
            
            if other_units:
                print("\n🔧 وحدات أخرى:")
                for unit in other_units:
                    print(f"   • {unit.name} ({unit.symbol}) - {unit.name_en}")
            
    except Exception as e:
        print(f"❌ خطأ في عرض وحدات القياس: {e}")

def show_usage_guide():
    """عرض دليل الاستخدام"""
    
    print("\n📖 دليل الاستخدام:")
    print("=" * 40)
    print("1. 🏃 تشغيل التطبيق:")
    print("   python main.py")
    print()
    print("2. 📏 إدارة وحدات القياس:")
    print("   - افتح التطبيق")
    print("   - اذهب إلى قائمة 'إدارة الأصناف'")
    print("   - اختر 'وحدات القياس'")
    print()
    print("3. 📦 إدارة الأصناف:")
    print("   - افتح التطبيق")
    print("   - اذهب إلى قائمة 'إدارة الأصناف'")
    print("   - اختر 'الأصناف'")
    print()
    print("4. 🔧 أدوات إضافية:")
    print("   - إضافة وحدات قياس إضافية: python add_sample_units.py")
    print("   - إضافة أصناف تجريبية: python add_sample_items.py")
    print("   - اختبار قاعدة البيانات: python test_oracle_connection.py")
    print("   - تبديل قاعدة البيانات: python switch_database.py")
    print()
    print("5. 📊 ميزات متقدمة:")
    print("   - البحث والتصفية في وحدات القياس")
    print("   - تصدير البيانات إلى Excel")
    print("   - إدارة حالة الوحدات (نشطة/غير نشطة)")
    print("   - ربط الأصناف بوحدات القياس المناسبة")

def main():
    """الدالة الرئيسية"""
    
    # إعداد النظام
    success = setup_complete_system()
    
    if success:
        # عرض نماذج من وحدات القياس
        show_sample_units()
        
        # عرض دليل الاستخدام
        show_usage_guide()
        
        print("\n🎉 تم إعداد النظام بنجاح!")
        print("💡 يمكنك الآن تشغيل التطبيق باستخدام: python main.py")
        
    else:
        print("\n❌ فشل في إعداد النظام!")
        print("💡 تحقق من إعدادات قاعدة البيانات وحاول مرة أخرى")
        sys.exit(1)

if __name__ == "__main__":
    main()
