# -*- coding: utf-8 -*-
"""
مدير الأيقونات والرموز
Icons and Symbols Manager
"""

class IconManager:
    """مدير الأيقونات والرموز"""
    
    # أيقونات الأنظمة الرئيسية
    SETTINGS = "⚙️"
    ITEMS = "📦"
    SUPPLIERS = "🏭"
    SHIPMENTS = "🚢"
    CUSTOMS = "🏛️"
    COSTS = "💰"
    
    # أيقونات الحالة
    SUCCESS = "✅"
    WARNING = "⚠️"
    ERROR = "❌"
    INFO = "ℹ️"
    LOADING = "⏳"
    
    # أيقونات الإجراءات
    ADD = "➕"
    EDIT = "✏️"
    DELETE = "🗑️"
    SAVE = "💾"
    CANCEL = "❌"
    SEARCH = "🔍"
    REFRESH = "🔄"
    PRINT = "🖨️"
    EXPORT = "📤"
    IMPORT = "📥"
    
    # أيقونات التنقل
    HOME = "🏠"
    BACK = "⬅️"
    FORWARD = "➡️"
    UP = "⬆️"
    DOWN = "⬇️"
    
    # أيقونات الملفات والمستندات
    FILE = "📄"
    FOLDER = "📁"
    IMAGE = "🖼️"
    PDF = "📋"
    EXCEL = "📊"
    WORD = "📝"
    
    # أيقونات الشبكة والاتصال
    ONLINE = "🟢"
    OFFLINE = "🔴"
    SYNC = "🔄"
    CLOUD = "☁️"
    DOWNLOAD = "⬇️"
    UPLOAD = "⬆️"
    
    # أيقونات المستخدمين والأمان
    USER = "👤"
    USERS = "👥"
    ADMIN = "👑"
    LOCK = "🔒"
    UNLOCK = "🔓"
    KEY = "🔑"
    
    # أيقونات الوقت والتاريخ
    CLOCK = "🕐"
    CALENDAR = "📅"
    TIMER = "⏱️"
    STOPWATCH = "⏱️"
    
    # أيقونات الإحصائيات والتقارير
    CHART = "📊"
    GRAPH = "📈"
    REPORT = "📋"
    ANALYTICS = "📊"
    
    # أيقونات التواصل
    EMAIL = "📧"
    PHONE = "📞"
    MESSAGE = "💬"
    NOTIFICATION = "🔔"
    
    # أيقونات النقل والشحن
    TRUCK = "🚛"
    SHIP = "🚢"
    PLANE = "✈️"
    TRAIN = "🚂"
    CONTAINER = "📦"
    
    # أيقونات المال والمحاسبة
    MONEY = "💰"
    DOLLAR = "💵"
    EURO = "💶"
    POUND = "💷"
    CREDIT_CARD = "💳"
    BANK = "🏦"
    
    # أيقونات الطقس والبيئة
    SUN = "☀️"
    CLOUD_WEATHER = "☁️"
    RAIN = "🌧️"
    SNOW = "❄️"
    WIND = "💨"
    
    # أيقونات الأدوات
    TOOL = "🔧"
    HAMMER = "🔨"
    WRENCH = "🔧"
    GEAR = "⚙️"
    SETTINGS_GEAR = "⚙️"
    
    @classmethod
    def get_system_icon(cls, system_name):
        """الحصول على أيقونة نظام معين"""
        icons_map = {
            "settings": cls.SETTINGS,
            "items": cls.ITEMS,
            "suppliers": cls.SUPPLIERS,
            "shipments": cls.SHIPMENTS,
            "customs": cls.CUSTOMS,
            "costs": cls.COSTS
        }
        return icons_map.get(system_name.lower(), "📋")
    
    @classmethod
    def get_status_icon(cls, status):
        """الحصول على أيقونة حالة معينة"""
        status_map = {
            "success": cls.SUCCESS,
            "warning": cls.WARNING,
            "error": cls.ERROR,
            "info": cls.INFO,
            "loading": cls.LOADING,
            "online": cls.ONLINE,
            "offline": cls.OFFLINE
        }
        return status_map.get(status.lower(), cls.INFO)
    
    @classmethod
    def get_action_icon(cls, action):
        """الحصول على أيقونة إجراء معين"""
        action_map = {
            "add": cls.ADD,
            "edit": cls.EDIT,
            "delete": cls.DELETE,
            "save": cls.SAVE,
            "cancel": cls.CANCEL,
            "search": cls.SEARCH,
            "refresh": cls.REFRESH,
            "print": cls.PRINT,
            "export": cls.EXPORT,
            "import": cls.IMPORT
        }
        return action_map.get(action.lower(), "🔧")
    
    @classmethod
    def get_file_icon(cls, file_type):
        """الحصول على أيقونة نوع ملف معين"""
        file_map = {
            "file": cls.FILE,
            "folder": cls.FOLDER,
            "image": cls.IMAGE,
            "pdf": cls.PDF,
            "excel": cls.EXCEL,
            "word": cls.WORD
        }
        return file_map.get(file_type.lower(), cls.FILE)
    
    @classmethod
    def create_icon_text(cls, icon, text, separator=" "):
        """دمج أيقونة مع نص"""
        return f"{icon}{separator}{text}"
    
    @classmethod
    def get_colored_circle(cls, color="green"):
        """الحصول على دائرة ملونة"""
        color_map = {
            "green": "🟢",
            "red": "🔴",
            "blue": "🔵",
            "yellow": "🟡",
            "orange": "🟠",
            "purple": "🟣",
            "brown": "🟤",
            "black": "⚫",
            "white": "⚪"
        }
        return color_map.get(color.lower(), "🟢")
    
    @classmethod
    def get_arrow(cls, direction="right"):
        """الحصول على سهم بالاتجاه المحدد"""
        arrow_map = {
            "right": "➡️",
            "left": "⬅️",
            "up": "⬆️",
            "down": "⬇️",
            "up-right": "↗️",
            "up-left": "↖️",
            "down-right": "↘️",
            "down-left": "↙️"
        }
        return arrow_map.get(direction.lower(), "➡️")

# إنشاء مثيل عام من مدير الأيقونات
icon_manager = IconManager()
