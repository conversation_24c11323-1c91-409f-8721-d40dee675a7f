# -*- coding: utf-8 -*-
"""
نافذة البحث عن الأصناف
Item Search Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLineEdit, 
                               QPushButton, QTableWidget, QTableWidgetItem, 
                               QLabel, QMessageBox, QHeaderView, QComboBox,
                               QDialogButtonBox, QGroupBox, QFormLayout)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from ...database.database_manager import DatabaseManager
from ...database.models import Item, ItemGroup, UnitOfMeasure

class ItemSearchDialog(QDialog):
    """نافذة البحث عن الأصناف"""
    
    item_selected = Signal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.selected_item = None
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("البحث عن الأصناف")
        self.setModal(True)
        self.resize(900, 600)
        
        layout = QVBoxLayout(self)
        
        # مجموعة البحث والتصفية
        search_group = QGroupBox("البحث والتصفية")
        search_layout = QFormLayout(search_group)
        
        # حقل البحث
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالكود أو الاسم...")
        self.search_edit.textChanged.connect(self.search_items)
        search_layout.addRow("البحث:", self.search_edit)
        
        # تصفية حسب المجموعة
        self.group_combo = QComboBox()
        self.group_combo.addItem("جميع المجموعات", None)
        self.group_combo.currentTextChanged.connect(self.filter_by_group)
        search_layout.addRow("المجموعة:", self.group_combo)
        
        # تصفية حسب الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["جميع الأصناف", "نشط فقط", "غير نشط فقط"])
        self.status_combo.currentTextChanged.connect(self.filter_by_status)
        search_layout.addRow("الحالة:", self.status_combo)
        
        layout.addWidget(search_group)
        
        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(8)
        self.items_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "المجموعة", "وحدة القياس", 
            "سعر التكلفة", "سعر البيع", "الحالة", "الوزن"
        ])
        
        # تنسيق الجدول
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.items_table.setAlternatingRowColors(True)
        
        # ربط النقر المزدوج
        self.items_table.itemDoubleClicked.connect(self.accept_selection)
        
        layout.addWidget(self.items_table)
        
        # معلومات الصنف المختار
        info_group = QGroupBox("معلومات الصنف المختار")
        info_layout = QFormLayout(info_group)
        
        self.selected_code_label = QLabel("-")
        info_layout.addRow("الكود:", self.selected_code_label)
        
        self.selected_name_label = QLabel("-")
        info_layout.addRow("الاسم:", self.selected_name_label)
        
        self.selected_group_label = QLabel("-")
        info_layout.addRow("المجموعة:", self.selected_group_label)
        
        self.selected_price_label = QLabel("-")
        info_layout.addRow("سعر التكلفة:", self.selected_price_label)
        
        layout.addWidget(info_group)
        
        # أزرار الحوار
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        buttons.accepted.connect(self.accept_selection)
        buttons.rejected.connect(self.reject)
        
        # تخصيص النصوص
        buttons.button(QDialogButtonBox.Ok).setText("اختيار")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")
        
        layout.addWidget(buttons)
        
        # ربط تغيير الاختيار
        self.items_table.itemSelectionChanged.connect(self.on_selection_changed)
        
    def load_data(self):
        """تحميل البيانات"""
        self.load_groups()
        self.load_items()
        
    def load_groups(self):
        """تحميل مجموعات الأصناف"""
        try:
            session = self.db_manager.get_session()
            groups = session.query(ItemGroup).filter(ItemGroup.is_active == True).all()
            
            for group in groups:
                self.group_combo.addItem(group.name, group.id)
                
            session.close()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المجموعات: {str(e)}")
            
    def load_items(self, search_text="", group_id=None, status_filter="جميع الأصناف"):
        """تحميل الأصناف"""
        try:
            session = self.db_manager.get_session()
            
            # بناء الاستعلام
            query = session.query(Item)
            
            # تصفية حسب النص
            if search_text:
                query = query.filter(
                    (Item.code.contains(search_text)) |
                    (Item.name.contains(search_text))
                )
                
            # تصفية حسب المجموعة
            if group_id:
                query = query.filter(Item.group_id == group_id)
                
            # تصفية حسب الحالة
            if status_filter == "نشط فقط":
                query = query.filter(Item.is_active == True)
            elif status_filter == "غير نشط فقط":
                query = query.filter(Item.is_active == False)
                
            items = query.order_by(Item.code).all()
            
            # تعبئة الجدول
            self.items_table.setRowCount(len(items))
            
            for row, item in enumerate(items):
                # الكود
                self.items_table.setItem(row, 0, QTableWidgetItem(item.code))
                
                # الاسم
                self.items_table.setItem(row, 1, QTableWidgetItem(item.name))
                
                # المجموعة
                group_name = item.group.name if item.group else ""
                self.items_table.setItem(row, 2, QTableWidgetItem(group_name))
                
                # وحدة القياس
                unit_name = item.unit.name if item.unit else ""
                self.items_table.setItem(row, 3, QTableWidgetItem(unit_name))
                
                # سعر التكلفة
                cost_price = f"{item.cost_price:.2f}" if item.cost_price else "0.00"
                self.items_table.setItem(row, 4, QTableWidgetItem(cost_price))
                
                # سعر البيع
                selling_price = f"{item.selling_price:.2f}" if item.selling_price else "0.00"
                self.items_table.setItem(row, 5, QTableWidgetItem(selling_price))
                
                # الحالة
                status = "نشط" if item.is_active else "غير نشط"
                self.items_table.setItem(row, 6, QTableWidgetItem(status))
                
                # الوزن
                weight = f"{item.weight:.2f}" if item.weight else "0.00"
                self.items_table.setItem(row, 7, QTableWidgetItem(weight))
                
                # حفظ معرف الصنف
                self.items_table.item(row, 0).setData(Qt.UserRole, item.id)
                
                # حفظ بيانات الصنف كاملة
                item_data = {
                    'id': item.id,
                    'code': item.code,
                    'name': item.name,
                    'group_id': item.group_id,
                    'group_name': group_name,
                    'unit_id': item.unit_id,
                    'unit_name': unit_name,
                    'cost_price': item.cost_price or 0.0,
                    'selling_price': item.selling_price or 0.0,
                    'weight': item.weight or 0.0,
                    'is_active': item.is_active,
                    'description': item.description or ""
                }
                self.items_table.item(row, 0).setData(Qt.UserRole + 1, item_data)
                
            session.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الأصناف: {str(e)}")
            
    def search_items(self):
        """البحث في الأصناف"""
        search_text = self.search_edit.text().strip()
        group_id = self.group_combo.currentData()
        status_filter = self.status_combo.currentText()
        
        self.load_items(search_text, group_id, status_filter)
        
    def filter_by_group(self):
        """تصفية حسب المجموعة"""
        self.search_items()
        
    def filter_by_status(self):
        """تصفية حسب الحالة"""
        self.search_items()
        
    def on_selection_changed(self):
        """عند تغيير الاختيار"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            item_data = self.items_table.item(current_row, 0).data(Qt.UserRole + 1)
            if item_data:
                self.selected_code_label.setText(item_data['code'])
                self.selected_name_label.setText(item_data['name'])
                self.selected_group_label.setText(item_data['group_name'])
                self.selected_price_label.setText(f"{item_data['cost_price']:.2f}")
                self.selected_item = item_data
        else:
            self.selected_code_label.setText("-")
            self.selected_name_label.setText("-")
            self.selected_group_label.setText("-")
            self.selected_price_label.setText("-")
            self.selected_item = None
            
    def accept_selection(self):
        """قبول الاختيار"""
        if not self.selected_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صنف")
            return
            
        self.item_selected.emit(self.selected_item)
        self.accept()
        
    def get_selected_item(self):
        """الحصول على الصنف المختار"""
        return self.selected_item
