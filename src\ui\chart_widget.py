"""
وحدة الرسوم البيانية المتقدمة
تحتوي على رسوم بيانية تفاعلية للنظام
"""

import sys
import random
from datetime import datetime, timedelta
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QFrame, QSizePolicy)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QPainter, QColor, QPen, QBrush, QLinearGradient

class ChartWidget(QWidget):
    """ويدجت الرسم البياني المتقدم"""
    
    def __init__(self, chart_type="bar", title="رسم بياني", parent=None):
        super().__init__(parent)
        self.chart_type = chart_type
        self.title = title
        self.data = []
        self.labels = []
        self.colors = []
        
        self.setup_ui()
        self.generate_sample_data()
        
        # تحديث البيانات كل 30 ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_chart)
        self.timer.start(30000)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # إطار الرسم البياني
        self.chart_frame = QFrame()
        self.chart_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
            }
        """)
        self.chart_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        chart_layout = QVBoxLayout(self.chart_frame)
        chart_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الرسم البياني
        self.title_label = QLabel(self.title)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        chart_layout.addWidget(self.title_label)
        
        # منطقة الرسم
        self.chart_area = ChartArea(self.chart_type)
        chart_layout.addWidget(self.chart_area)
        
        layout.addWidget(self.chart_frame)
    
    def generate_sample_data(self):
        """توليد بيانات تجريبية"""
        if self.chart_type == "bar":
            self.labels = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو"]
            self.data = [random.randint(10000, 50000) for _ in range(6)]
            self.colors = ["#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6", "#1abc9c"]
        
        elif self.chart_type == "pie":
            self.labels = ["مكتملة", "قيد التنفيذ", "متأخرة", "ملغاة"]
            self.data = [45, 30, 15, 10]
            self.colors = ["#2ecc71", "#f39c12", "#e74c3c", "#95a5a6"]
        
        elif self.chart_type == "area":
            self.labels = ["الأسبوع 1", "الأسبوع 2", "الأسبوع 3", "الأسبوع 4"]
            self.data = [random.randint(5000, 25000) for _ in range(4)]
            self.colors = ["#3498db", "#2ecc71", "#e74c3c", "#f39c12"]
        
        self.chart_area.set_data(self.data, self.labels, self.colors)
    
    def update_chart(self):
        """تحديث بيانات الرسم البياني"""
        if self.chart_type == "bar":
            # تحديث بيانات الأعمدة
            self.data = [random.randint(10000, 50000) for _ in range(6)]
        
        elif self.chart_type == "pie":
            # تحديث بيانات الدائري
            total = 100
            self.data = []
            remaining = total
            for i in range(3):
                value = random.randint(5, remaining - (3-i)*5)
                self.data.append(value)
                remaining -= value
            self.data.append(remaining)
        
        elif self.chart_type == "area":
            # تحديث بيانات المساحي
            self.data = [random.randint(5000, 25000) for _ in range(4)]
        
        self.chart_area.set_data(self.data, self.labels, self.colors)
        self.chart_area.update()

class ChartArea(QWidget):
    """منطقة رسم الرسم البياني"""
    
    def __init__(self, chart_type="bar", parent=None):
        super().__init__(parent)
        self.chart_type = chart_type
        self.data = []
        self.labels = []
        self.colors = []
        self.setMinimumSize(300, 200)
    
    def set_data(self, data, labels, colors):
        """تعيين بيانات الرسم البياني"""
        self.data = data
        self.labels = labels
        self.colors = colors
        self.update()
    
    def paintEvent(self, event):
        """رسم الرسم البياني"""
        painter = QPainter(self)

        # التحقق من صحة الرسام
        if not painter.isActive():
            return

        painter.setRenderHint(QPainter.Antialiasing)

        if not self.data:
            painter.end()
            return

        rect = self.rect()
        margin = 40
        chart_rect = rect.adjusted(margin, margin, -margin, -margin)

        try:
            if self.chart_type == "bar":
                self.draw_bar_chart(painter, chart_rect)
            elif self.chart_type == "pie":
                self.draw_pie_chart(painter, chart_rect)
            elif self.chart_type == "area":
                self.draw_area_chart(painter, chart_rect)
        except Exception as e:
            print(f"خطأ في رسم الرسم البياني: {e}")
        finally:
            painter.end()
    
    def draw_bar_chart(self, painter, rect):
        """رسم الرسم البياني الأعمدة"""
        if not self.data:
            return
        
        max_value = max(self.data)
        bar_width = rect.width() / len(self.data) * 0.8
        spacing = rect.width() / len(self.data) * 0.2
        
        for i, (value, color) in enumerate(zip(self.data, self.colors)):
            # حساب ارتفاع العمود
            bar_height = (value / max_value) * rect.height()
            
            # موقع العمود
            x = rect.left() + i * (bar_width + spacing) + spacing / 2
            y = rect.bottom() - bar_height
            
            # رسم العمود مع تدرج
            gradient = QLinearGradient(0, y, 0, rect.bottom())
            gradient.setColorAt(0, QColor(color))
            gradient.setColorAt(1, QColor(color).darker(120))
            
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(color).darker(140), 2))
            painter.drawRect(int(x), int(y), int(bar_width), int(bar_height))
            
            # رسم القيمة
            painter.setPen(QPen(QColor("#2c3e50"), 1))
            painter.drawText(int(x), int(y - 5), int(bar_width), 20, 
                           Qt.AlignCenter, f"{value:,}")
    
    def draw_pie_chart(self, painter, rect):
        """رسم الرسم البياني الدائري"""
        if not self.data:
            return
        
        total = sum(self.data)
        start_angle = 0
        
        # رسم الدائرة
        center = rect.center()
        radius = min(rect.width(), rect.height()) / 2 - 20
        
        for i, (value, color) in enumerate(zip(self.data, self.colors)):
            # حساب الزاوية
            angle = (value / total) * 360 * 16  # Qt uses 1/16th degrees
            
            # رسم القطاع
            painter.setBrush(QBrush(QColor(color)))
            painter.setPen(QPen(QColor(color).darker(120), 2))
            painter.drawPie(int(center.x() - radius), int(center.y() - radius),
                          int(radius * 2), int(radius * 2),
                          int(start_angle), int(angle))
            
            start_angle += angle
    
    def draw_area_chart(self, painter, rect):
        """رسم الرسم البياني المساحي"""
        if not self.data or len(self.data) < 2:
            return
        
        max_value = max(self.data)
        points = []
        
        # حساب النقاط
        for i, value in enumerate(self.data):
            x = rect.left() + (i / (len(self.data) - 1)) * rect.width()
            y = rect.bottom() - (value / max_value) * rect.height()
            points.append((x, y))
        
        # رسم المنطقة المملوءة
        gradient = QLinearGradient(0, rect.top(), 0, rect.bottom())
        gradient.setColorAt(0, QColor("#3498db").lighter(150))
        gradient.setColorAt(1, QColor("#3498db").lighter(200))
        
        painter.setBrush(QBrush(gradient))
        painter.setPen(QPen(QColor("#3498db"), 3))
        
        # رسم الخط والمنطقة
        from PySide6.QtGui import QPolygonF
        from PySide6.QtCore import QPointF
        
        polygon = QPolygonF()
        polygon.append(QPointF(rect.left(), rect.bottom()))
        
        for x, y in points:
            polygon.append(QPointF(x, y))
        
        polygon.append(QPointF(rect.right(), rect.bottom()))
        painter.drawPolygon(polygon)
        
        # رسم النقاط
        painter.setBrush(QBrush(QColor("#2c3e50")))
        for x, y in points:
            painter.drawEllipse(int(x - 4), int(y - 4), 8, 8)
