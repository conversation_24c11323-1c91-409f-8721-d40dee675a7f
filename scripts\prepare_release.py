#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تحضير الإصدار - ProShipment V2.0.0
Release Preparation Script
"""

import sys
import os
import shutil
import zipfile
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class ReleasePreparation:
    """فئة تحضير الإصدار"""
    
    def __init__(self):
        self.version = "2.0.0"
        self.release_date = datetime.now().strftime("%Y-%m-%d")
        self.project_root = Path(__file__).parent.parent
        self.release_dir = self.project_root / "release"
        self.dist_dir = self.project_root / "dist"
        
    def prepare_release(self):
        """تحضير الإصدار الكامل"""
        print(f"🚀 تحضير إصدار ProShipment V{self.version}")
        print("="*60)
        
        # إنشاء مجلدات الإصدار
        self.create_release_directories()
        
        # فحص الملفات المطلوبة
        self.check_required_files()
        
        # نسخ الملفات الأساسية
        self.copy_core_files()
        
        # إنشاء ملف معلومات الإصدار
        self.create_release_info()
        
        # تحديث requirements.txt
        self.update_requirements()
        
        # إنشاء ملفات التوزيع
        self.create_distribution_packages()
        
        # إنشاء checksums
        self.create_checksums()
        
        # تشغيل اختبار نهائي
        self.run_final_tests()
        
        print(f"\n✅ تم تحضير الإصدار V{self.version} بنجاح!")
        print(f"📁 مجلد الإصدار: {self.release_dir}")
        print(f"📦 ملفات التوزيع: {self.dist_dir}")
    
    def create_release_directories(self):
        """إنشاء مجلدات الإصدار"""
        print("📁 إنشاء مجلدات الإصدار...")
        
        # حذف المجلدات القديمة
        if self.release_dir.exists():
            shutil.rmtree(self.release_dir)
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        
        # إنشاء مجلدات جديدة
        self.release_dir.mkdir(parents=True)
        self.dist_dir.mkdir(parents=True)
        
        print(f"   ✅ تم إنشاء: {self.release_dir}")
        print(f"   ✅ تم إنشاء: {self.dist_dir}")
    
    def check_required_files(self):
        """فحص الملفات المطلوبة"""
        print("🔍 فحص الملفات المطلوبة...")
        
        required_files = [
            "main.py",
            "README.md",
            "CHANGELOG.md",
            "requirements.txt",
            "src/database/universal_database_manager.py",
            "src/database/oracle_config.py",
            "src/ui/main_window.py",
            "tools/environment_checker.py",
            "tools/oracle_setup_wizard.py",
            "tools/data_migration_tool.py",
            "docs/oracle_integration_guide.md",
            "docs/quick_start_oracle.md"
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
            else:
                print(f"   ✅ {file_path}")
        
        if missing_files:
            print(f"\n❌ ملفات مفقودة:")
            for file_path in missing_files:
                print(f"   ❌ {file_path}")
            raise FileNotFoundError("ملفات مطلوبة مفقودة")
        
        print("   ✅ جميع الملفات المطلوبة موجودة")
    
    def copy_core_files(self):
        """نسخ الملفات الأساسية"""
        print("📋 نسخ الملفات الأساسية...")
        
        # قائمة المجلدات والملفات للنسخ
        items_to_copy = [
            "src/",
            "tools/",
            "config/",
            "docs/",
            "scripts/",
            "main.py",
            "README.md",
            "CHANGELOG.md",
            "requirements.txt"
        ]
        
        for item in items_to_copy:
            source = self.project_root / item
            destination = self.release_dir / item
            
            if source.is_dir():
                shutil.copytree(source, destination, ignore=self.ignore_patterns)
                print(f"   📁 {item}")
            elif source.is_file():
                destination.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source, destination)
                print(f"   📄 {item}")
        
        # إنشاء مجلدات فارغة مطلوبة
        empty_dirs = ["data", "logs", "backups", "reports"]
        for dir_name in empty_dirs:
            dir_path = self.release_dir / dir_name
            dir_path.mkdir(exist_ok=True)
            
            # إنشاء ملف .gitkeep
            gitkeep = dir_path / ".gitkeep"
            gitkeep.touch()
            print(f"   📁 {dir_name}/ (فارغ)")
    
    def ignore_patterns(self, dir_path, names):
        """أنماط الملفات المتجاهلة"""
        ignore_list = []
        
        for name in names:
            # تجاهل ملفات Python المترجمة
            if name.endswith('.pyc') or name == '__pycache__':
                ignore_list.append(name)
            
            # تجاهل ملفات النسخ الاحتياطي
            elif name.endswith('.bak') or name.endswith('.tmp'):
                ignore_list.append(name)
            
            # تجاهل ملفات IDE
            elif name in ['.vscode', '.idea', '.vs']:
                ignore_list.append(name)
            
            # تجاهل ملفات Git
            elif name in ['.git', '.gitignore']:
                ignore_list.append(name)
        
        return ignore_list
    
    def create_release_info(self):
        """إنشاء ملف معلومات الإصدار"""
        print("📝 إنشاء ملف معلومات الإصدار...")
        
        release_info = {
            "name": "ProShipment",
            "version": self.version,
            "release_date": self.release_date,
            "description": "نظام إدارة الشحنات المتقدم مع دعم Oracle",
            "features": [
                "دعم Oracle Database",
                "نقل البيانات التلقائي",
                "نظام مراقبة متقدم",
                "نسخ احتياطي ذكي",
                "واجهة مستخدم محدثة"
            ],
            "requirements": {
                "python": ">=3.8",
                "operating_systems": ["Windows", "Linux", "macOS"],
                "databases": ["SQLite", "Oracle Database 11g+"]
            },
            "installation": {
                "quick_start": "python main.py",
                "oracle_setup": "python tools/oracle_setup_wizard.py",
                "environment_check": "python tools/environment_checker.py"
            },
            "documentation": {
                "main_guide": "docs/oracle_integration_guide.md",
                "quick_start": "docs/quick_start_oracle.md",
                "changelog": "CHANGELOG.md"
            }
        }
        
        release_info_file = self.release_dir / "release_info.json"
        with open(release_info_file, 'w', encoding='utf-8') as f:
            json.dump(release_info, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ تم إنشاء: {release_info_file}")
    
    def update_requirements(self):
        """تحديث requirements.txt"""
        print("📦 تحديث requirements.txt...")
        
        requirements = [
            "PySide6>=6.0.0",
            "SQLAlchemy>=1.4.0",
            "cx_Oracle>=8.3.0",
            "reportlab>=3.6.0",
            "requests>=2.25.0",
            "openpyxl>=3.0.0",
            "Pillow>=8.0.0",
            "psutil>=5.8.0",
            "schedule>=1.1.0",
            "cryptography>=3.4.0"
        ]
        
        requirements_file = self.release_dir / "requirements.txt"
        with open(requirements_file, 'w', encoding='utf-8') as f:
            for req in requirements:
                f.write(f"{req}\n")
        
        print(f"   ✅ تم تحديث: {requirements_file}")
        print(f"   📦 عدد المكتبات: {len(requirements)}")
    
    def create_distribution_packages(self):
        """إنشاء ملفات التوزيع"""
        print("📦 إنشاء ملفات التوزيع...")
        
        # إنشاء ملف ZIP للتوزيع
        zip_filename = f"ProShipment-V{self.version}-{self.release_date}.zip"
        zip_path = self.dist_dir / zip_filename
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(self.release_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_name = file_path.relative_to(self.release_dir)
                    zipf.write(file_path, arc_name)
        
        print(f"   ✅ تم إنشاء: {zip_filename}")
        
        # إنشاء ملف TAR.GZ للينكس
        import tarfile
        tar_filename = f"ProShipment-V{self.version}-{self.release_date}.tar.gz"
        tar_path = self.dist_dir / tar_filename
        
        with tarfile.open(tar_path, 'w:gz') as tarf:
            tarf.add(self.release_dir, arcname=f"ProShipment-V{self.version}")
        
        print(f"   ✅ تم إنشاء: {tar_filename}")
        
        # عرض أحجام الملفات
        zip_size = zip_path.stat().st_size / 1024 / 1024
        tar_size = tar_path.stat().st_size / 1024 / 1024
        
        print(f"   📊 حجم ZIP: {zip_size:.2f} MB")
        print(f"   📊 حجم TAR.GZ: {tar_size:.2f} MB")
    
    def create_checksums(self):
        """إنشاء checksums للملفات"""
        print("🔐 إنشاء checksums...")
        
        import hashlib
        
        checksums = {}
        
        for dist_file in self.dist_dir.glob("ProShipment-V*"):
            if dist_file.is_file():
                # حساب SHA256
                sha256_hash = hashlib.sha256()
                with open(dist_file, 'rb') as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        sha256_hash.update(chunk)
                
                checksums[dist_file.name] = {
                    "sha256": sha256_hash.hexdigest(),
                    "size": dist_file.stat().st_size
                }
                
                print(f"   ✅ {dist_file.name}")
        
        # حفظ checksums
        checksums_file = self.dist_dir / "checksums.json"
        with open(checksums_file, 'w', encoding='utf-8') as f:
            json.dump(checksums, f, indent=2)
        
        print(f"   ✅ تم إنشاء: checksums.json")
    
    def run_final_tests(self):
        """تشغيل اختبار نهائي"""
        print("🧪 تشغيل اختبار نهائي...")
        
        try:
            # تشغيل فحص البيئة
            import subprocess
            
            env_check = subprocess.run([
                sys.executable, 
                str(self.release_dir / "tools" / "environment_checker.py")
            ], capture_output=True, text=True, cwd=self.release_dir)
            
            if env_check.returncode == 0:
                print("   ✅ فحص البيئة نجح")
            else:
                print("   ⚠️ فحص البيئة أظهر تحذيرات")
            
            # اختبار استيراد الوحدات الأساسية
            sys.path.insert(0, str(self.release_dir))
            
            try:
                from src.database.universal_database_manager import UniversalDatabaseManager
                print("   ✅ استيراد مدير قاعدة البيانات نجح")
            except ImportError as e:
                print(f"   ❌ فشل استيراد مدير قاعدة البيانات: {e}")
            
            try:
                from src.database.oracle_config import DatabaseConfigManager
                print("   ✅ استيراد إعدادات Oracle نجح")
            except ImportError as e:
                print(f"   ❌ فشل استيراد إعدادات Oracle: {e}")
            
        except Exception as e:
            print(f"   ❌ خطأ في الاختبار النهائي: {e}")
    
    def generate_release_notes(self):
        """إنشاء ملاحظات الإصدار"""
        print("📋 إنشاء ملاحظات الإصدار...")
        
        release_notes = f"""# ProShipment V{self.version} - ملاحظات الإصدار

## تاريخ الإصدار: {self.release_date}

### الميزات الجديدة الرئيسية:
- 🗄️ دعم Oracle Database الكامل
- 🔄 أداة نقل البيانات الشاملة
- 🔧 معالج إعداد Oracle التفاعلي
- 📊 نظام مراقبة وتشخيص متقدم
- 💾 نظام النسخ الاحتياطي المتطور

### التحسينات:
- ✅ إصلاح مشكلة تحديث طلب الحوالة
- ✅ إضافة حقل Bank Country
- ✅ تحسين دفتر العناوين
- ✅ واجهة مستخدم محدثة

### التثبيت:
1. فك ضغط الملف
2. تثبيت المتطلبات: `pip install -r requirements.txt`
3. فحص البيئة: `python tools/environment_checker.py`
4. تشغيل التطبيق: `python main.py`

### للترقية من V1.x:
1. إنشاء نسخة احتياطية من البيانات
2. إعداد Oracle (اختياري): `python tools/oracle_setup_wizard.py`
3. نقل البيانات: `python tools/data_migration_tool.py`

### الدعم:
- 📧 البريد الإلكتروني: <EMAIL>
- 📚 الوثائق: docs/oracle_integration_guide.md
- 🚀 البدء السريع: docs/quick_start_oracle.md

---
ProShipment V{self.version} - نظام إدارة الشحنات المتقدم
"""
        
        release_notes_file = self.dist_dir / f"RELEASE_NOTES_V{self.version}.md"
        with open(release_notes_file, 'w', encoding='utf-8') as f:
            f.write(release_notes)
        
        print(f"   ✅ تم إنشاء: {release_notes_file}")

def main():
    """الدالة الرئيسية"""
    try:
        release_prep = ReleasePreparation()
        release_prep.prepare_release()
        release_prep.generate_release_notes()
        
        print(f"\n🎉 تم تحضير إصدار ProShipment V{release_prep.version} بنجاح!")
        print("\n📋 الملفات المُنشأة:")
        
        # عرض محتويات مجلد التوزيع
        for file in release_prep.dist_dir.glob("*"):
            size_mb = file.stat().st_size / 1024 / 1024
            print(f"   📦 {file.name} ({size_mb:.2f} MB)")
        
        print(f"\n📁 مجلد الإصدار: {release_prep.release_dir}")
        print(f"📦 ملفات التوزيع: {release_prep.dist_dir}")
        
        print("\n✅ الإصدار جاهز للنشر!")
        
    except Exception as e:
        print(f"\n❌ خطأ في تحضير الإصدار: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
