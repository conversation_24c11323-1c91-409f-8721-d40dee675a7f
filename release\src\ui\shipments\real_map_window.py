"""
نافذة الخرائط الحقيقية المتصلة بالإنترنت
Real Internet-Connected Maps Window
"""

import os
import sys
import tempfile
import webbrowser
from datetime import datetime
import folium
import requests
from geopy.geocoders import Nominatim
from geopy.distance import geodesic

from PySide6.QtWidgets import (
    QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton,
    QLabel, QComboBox, QLineEdit, QTextEdit, QSplitter, QGroupBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QProgressBar, QCheckBox, QSpinBox, QFrame
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal, QUrl
from PySide6.QtGui import QFont, QIcon, QPixmap, QDesktopServices

import sys
import os

# إضافة مسار المشروع إلى sys.path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, project_root)

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, Supplier


class MapDataLoader(QThread):
    """خيط تحميل بيانات الخريطة"""
    data_loaded = Signal(dict)
    progress_updated = Signal(int)
    error_occurred = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.geolocator = Nominatim(user_agent="ProShipment_Maps")
        
    def run(self):
        """تحميل بيانات الشحنات والمواقع"""
        try:
            self.progress_updated.emit(10)
            
            # تحميل الشحنات من قاعدة البيانات
            db_manager = DatabaseManager()
            session = db_manager.get_session()
            
            self.progress_updated.emit(30)
            
            shipments = session.query(Shipment).outerjoin(Supplier).all()
            
            self.progress_updated.emit(50)
            
            # تحويل أسماء الموانئ إلى إحداثيات
            locations_data = {}
            processed_ports = set()
            
            for i, shipment in enumerate(shipments):
                # تحديث شريط التقدم
                progress = 50 + int((i / len(shipments)) * 40)
                self.progress_updated.emit(progress)
                
                # معالجة ميناء المغادرة
                departure_port = getattr(shipment, 'port_of_loading', '')
                if departure_port and departure_port not in processed_ports:
                    coords = self.get_port_coordinates(departure_port)
                    if coords:
                        locations_data[departure_port] = coords
                    processed_ports.add(departure_port)
                
                # معالجة ميناء الوصول
                arrival_port = getattr(shipment, 'port_of_discharge', '')
                if arrival_port and arrival_port not in processed_ports:
                    coords = self.get_port_coordinates(arrival_port)
                    if coords:
                        locations_data[arrival_port] = coords
                    processed_ports.add(arrival_port)
            
            self.progress_updated.emit(90)
            
            # إعداد البيانات النهائية
            map_data = {
                'shipments': shipments,
                'locations': locations_data,
                'total_shipments': len(shipments),
                'total_ports': len(locations_data)
            }
            
            self.progress_updated.emit(100)
            self.data_loaded.emit(map_data)
            
            session.close()
            
        except Exception as e:
            self.error_occurred.emit(f"خطأ في تحميل البيانات: {str(e)}")
    
    def get_port_coordinates(self, port_name):
        """الحصول على إحداثيات الميناء"""
        try:
            # قاموس الموانئ الرئيسية مع إحداثياتها
            major_ports = {
                'جدة': (21.4858, 39.1925),
                'الدمام': (26.4207, 50.0888),
                'الكويت': (29.3759, 47.9774),
                'دبي': (25.2048, 55.2708),
                'أبوظبي': (24.4539, 54.3773),
                'الدوحة': (25.2854, 51.5310),
                'المنامة': (26.2285, 50.5860),
                'مسقط': (23.5859, 58.4059),
                'شنغهاي': (31.2304, 121.4737),
                'سنغافورة': (1.3521, 103.8198),
                'هونغ كونغ': (22.3193, 114.1694),
                'روتردام': (51.9244, 4.4777),
                'هامبورغ': (53.5511, 9.9937),
                'لوس أنجلوس': (33.7701, -118.1937),
                'نيويورك': (40.6892, -74.0445),
                'الإسكندرية': (31.2001, 29.9187),
                'بورسعيد': (31.2653, 32.3019)
            }
            
            # البحث في القاموس أولاً
            if port_name in major_ports:
                return major_ports[port_name]
            
            # البحث باستخدام Nominatim
            location = self.geolocator.geocode(f"{port_name} port")
            if location:
                return (location.latitude, location.longitude)
            
            return None
            
        except Exception as e:
            print(f"خطأ في الحصول على إحداثيات {port_name}: {str(e)}")
            return None


class RealMapWindow(QMainWindow):
    """نافذة الخرائط الحقيقية"""
    
    def __init__(self):
        super().__init__()
        try:
            print("بدء تهيئة نافذة الخرائط الحقيقية...")

            self.setWindowTitle("🗺️ خرائط الشحنات الحقيقية - متصلة بالإنترنت")
            self.setGeometry(100, 100, 1400, 900)

            # إعدادات النافذة للظهور في المقدمة
            self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
            self.setAttribute(Qt.WA_ShowWithoutActivating, False)

            # المتغيرات
            self.map_data = {}
            self.current_map_file = None
            self.selected_shipments = []

            print("إعداد الواجهة...")
            # إعداد الواجهة
            self.setup_ui()
            print("تم إعداد الواجهة بنجاح")

            print("بدء تحميل البيانات...")
            # بدء تحميل البيانات
            self.load_map_data()
            print("تم بدء تحميل البيانات")

            # إزالة خاصية البقاء في المقدمة بعد 3 ثوان
            QTimer.singleShot(3000, self.remove_stay_on_top)

        except Exception as e:
            print(f"خطأ في تهيئة النافذة: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

    def remove_stay_on_top(self):
        """إزالة خاصية البقاء في المقدمة"""
        try:
            self.setWindowFlags(Qt.Window)
            self.show()  # إعادة عرض النافذة بالإعدادات الجديدة
            print("تم إزالة خاصية البقاء في المقدمة")
        except Exception as e:
            print(f"خطأ في إزالة خاصية البقاء في المقدمة: {e}")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # شريط الأدوات العلوي
        self.create_toolbar(main_layout)
        
        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(content_splitter)
        
        # اللوحة اليسرى - التحكم والمعلومات
        self.create_control_panel(content_splitter)
        
        # اللوحة اليمنى - عرض الخريطة
        self.create_map_display(content_splitter)
        
        # شريط الحالة
        self.create_status_bar(main_layout)
        
        # تعيين النسب
        content_splitter.setSizes([400, 1000])
    
    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # عنوان النافذة
        title_label = QLabel("🗺️ خرائط الشحنات الحقيقية")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2E86AB; padding: 10px;")
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # أزرار التحكم
        self.refresh_btn = QPushButton("🔄 تحديث البيانات")
        self.refresh_btn.clicked.connect(self.load_map_data)
        toolbar_layout.addWidget(self.refresh_btn)
        
        self.export_btn = QPushButton("📤 تصدير الخريطة")
        self.export_btn.clicked.connect(self.export_map)
        toolbar_layout.addWidget(self.export_btn)
        
        parent_layout.addWidget(toolbar_frame)
    
    def create_control_panel(self, parent_splitter):
        """إنشاء لوحة التحكم"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        
        # مجموعة الفلاتر
        filters_group = QGroupBox("🔍 فلاتر البحث")
        filters_layout = QVBoxLayout(filters_group)
        
        # فلتر حالة الشحنة
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("حالة الشحنة:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "الكل", "جديدة", "قيد التحضير", "تم الشحن", 
            "في الطريق", "وصلت", "تم التسليم", "ملغية"
        ])
        self.status_combo.currentTextChanged.connect(self.filter_shipments)
        status_layout.addWidget(self.status_combo)
        filters_layout.addLayout(status_layout)
        
        # فلتر الميناء
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("الميناء:"))
        self.port_combo = QComboBox()
        self.port_combo.addItem("جميع الموانئ")
        self.port_combo.currentTextChanged.connect(self.filter_shipments)
        port_layout.addWidget(self.port_combo)
        filters_layout.addLayout(port_layout)
        
        control_layout.addWidget(filters_group)
        
        # مجموعة الإحصائيات
        stats_group = QGroupBox("📊 الإحصائيات")
        stats_layout = QVBoxLayout(stats_group)
        
        self.total_shipments_label = QLabel("إجمالي الشحنات: 0")
        self.total_ports_label = QLabel("إجمالي الموانئ: 0")
        self.active_routes_label = QLabel("المسارات النشطة: 0")
        
        stats_layout.addWidget(self.total_shipments_label)
        stats_layout.addWidget(self.total_ports_label)
        stats_layout.addWidget(self.active_routes_label)
        
        control_layout.addWidget(stats_group)
        
        # مجموعة خيارات العرض
        display_group = QGroupBox("🎨 خيارات العرض")
        display_layout = QVBoxLayout(display_group)
        
        self.show_routes_cb = QCheckBox("عرض المسارات")
        self.show_routes_cb.setChecked(True)
        self.show_routes_cb.toggled.connect(self.update_map_display)
        display_layout.addWidget(self.show_routes_cb)
        
        self.show_ports_cb = QCheckBox("عرض الموانئ")
        self.show_ports_cb.setChecked(True)
        self.show_ports_cb.toggled.connect(self.update_map_display)
        display_layout.addWidget(self.show_ports_cb)
        
        self.show_shipments_cb = QCheckBox("عرض الشحنات")
        self.show_shipments_cb.setChecked(True)
        self.show_shipments_cb.toggled.connect(self.update_map_display)
        display_layout.addWidget(self.show_shipments_cb)
        
        control_layout.addWidget(display_group)
        
        control_layout.addStretch()
        parent_splitter.addWidget(control_widget)
    
    def create_map_display(self, parent_splitter):
        """إنشاء منطقة عرض الخريطة"""
        map_widget = QWidget()
        map_layout = QVBoxLayout(map_widget)
        
        # معلومات الخريطة
        map_info_label = QLabel("🌍 خريطة العالم الحقيقية - مدعومة بـ OpenStreetMap")
        map_info_label.setFont(QFont("Arial", 12, QFont.Bold))
        map_info_label.setAlignment(Qt.AlignCenter)
        map_info_label.setStyleSheet("background-color: #E8F4FD; padding: 10px; border-radius: 5px;")
        map_layout.addWidget(map_info_label)
        
        # منطقة عرض الخريطة
        self.map_display_label = QLabel("جاري تحميل الخريطة...")
        self.map_display_label.setAlignment(Qt.AlignCenter)
        self.map_display_label.setStyleSheet("""
            QLabel {
                background-color: #F0F8FF;
                border: 2px dashed #2E86AB;
                border-radius: 10px;
                font-size: 16px;
                color: #2E86AB;
                min-height: 400px;
            }
        """)
        map_layout.addWidget(self.map_display_label)
        
        # أزرار التحكم في الخريطة
        map_controls_layout = QHBoxLayout()
        
        self.open_browser_btn = QPushButton("🌐 فتح في المتصفح")
        self.open_browser_btn.clicked.connect(self.open_in_browser)
        self.open_browser_btn.setEnabled(False)
        map_controls_layout.addWidget(self.open_browser_btn)
        
        self.save_map_btn = QPushButton("💾 حفظ الخريطة")
        self.save_map_btn.clicked.connect(self.save_map)
        self.save_map_btn.setEnabled(False)
        map_controls_layout.addWidget(self.save_map_btn)
        
        map_controls_layout.addStretch()
        map_layout.addLayout(map_controls_layout)
        
        parent_splitter.addWidget(map_widget)
    
    def create_status_bar(self, parent_layout):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        parent_layout.addWidget(status_frame)

    def load_map_data(self):
        """تحميل بيانات الخريطة"""
        try:
            print("بدء تحميل بيانات الخريطة...")
            self.status_label.setText("جاري تحميل البيانات...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(50)

            # تعطيل الأزرار أثناء التحميل
            self.refresh_btn.setEnabled(False)
            self.export_btn.setEnabled(False)

            # تحميل بيانات تجريبية بسيطة
            self.load_simple_data()

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")
            import traceback
            traceback.print_exc()
            self.on_error_occurred(f"فشل في تحميل البيانات: {str(e)}")

    def load_simple_data(self):
        """تحميل بيانات بسيطة للاختبار"""
        try:
            print("تحميل بيانات تجريبية...")

            # بيانات تجريبية
            data = {
                'shipments': [
                    {
                        'id': 1,
                        'container_number': 'TEST001',
                        'origin_port': 'جدة',
                        'destination_port': 'دبي',
                        'status': 'في الطريق',
                        'supplier_name': 'شركة تجريبية'
                    }
                ],
                'ports': [
                    {'name': 'جدة', 'lat': 21.4858, 'lon': 39.1925, 'activity': 1},
                    {'name': 'دبي', 'lat': 25.2048, 'lon': 55.2708, 'activity': 1}
                ],
                'total_shipments': 1,
                'total_ports': 2,
                'active_routes': 1
            }

            print("تم تحميل البيانات التجريبية")
            self.on_data_loaded(data)

        except Exception as e:
            print(f"خطأ في تحميل البيانات البسيطة: {str(e)}")
            raise

    def on_data_loaded(self, data):
        """معالجة البيانات المحملة"""
        self.map_data = data

        # تحديث الإحصائيات
        self.update_statistics()

        # تحديث قائمة الموانئ
        self.update_ports_combo()

        # إنشاء الخريطة
        self.create_folium_map()

        # إعادة تفعيل الأزرار
        self.refresh_btn.setEnabled(True)
        self.export_btn.setEnabled(True)
        self.open_browser_btn.setEnabled(True)
        self.save_map_btn.setEnabled(True)

        self.progress_bar.setVisible(False)
        self.status_label.setText(f"تم تحميل {data['total_shipments']} شحنة من {data['total_ports']} ميناء")

    def on_error_occurred(self, error_message):
        """معالجة الأخطاء"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("خطأ في التحميل")
        self.refresh_btn.setEnabled(True)

        QMessageBox.critical(self, "خطأ", error_message)

    def update_statistics(self):
        """تحديث الإحصائيات"""
        if not self.map_data:
            return

        total_shipments = self.map_data.get('total_shipments', 0)
        total_ports = self.map_data.get('total_ports', 0)

        # حساب المسارات النشطة
        active_routes = 0
        shipments = self.map_data.get('shipments', [])
        for shipment in shipments:
            status = getattr(shipment, 'shipment_status', '')
            if status in ['قيد التحضير', 'تم الشحن', 'في الطريق']:
                active_routes += 1

        self.total_shipments_label.setText(f"إجمالي الشحنات: {total_shipments}")
        self.total_ports_label.setText(f"إجمالي الموانئ: {total_ports}")
        self.active_routes_label.setText(f"المسارات النشطة: {active_routes}")

    def update_ports_combo(self):
        """تحديث قائمة الموانئ"""
        self.port_combo.clear()
        self.port_combo.addItem("جميع الموانئ")

        if self.map_data and 'locations' in self.map_data:
            ports = sorted(self.map_data['locations'].keys())
            self.port_combo.addItems(ports)

    def create_folium_map(self):
        """إنشاء خريطة Folium"""
        try:
            print("بدء إنشاء خريطة Folium...")
            self.status_label.setText("جاري إنشاء الخريطة...")

            # إنشاء خريطة بسيطة
            m = folium.Map(
                location=[25.0, 45.0],  # وسط الشرق الأوسط
                zoom_start=5,
                tiles='OpenStreetMap'
            )

            print("تم إنشاء الخريطة الأساسية")

            # إضافة الموانئ التجريبية
            if self.map_data and 'ports' in self.map_data:
                print("إضافة الموانئ للخريطة...")
                for port in self.map_data['ports']:
                    try:
                        folium.Marker(
                            location=[port['lat'], port['lon']],
                            popup=f"ميناء {port['name']}",
                            tooltip=port['name'],
                            icon=folium.Icon(color='blue', icon='ship')
                        ).add_to(m)
                        print(f"تم إضافة ميناء {port['name']}")
                    except Exception as e:
                        print(f"خطأ في إضافة ميناء {port['name']}: {e}")
                        # إضافة علامة بسيطة كبديل
                        folium.CircleMarker(
                            location=[port['lat'], port['lon']],
                            radius=8,
                            popup=f"ميناء {port['name']}",
                            color='blue',
                            fillColor='blue',
                            fillOpacity=0.7
                        ).add_to(m)

            # حفظ الخريطة
            print("حفظ الخريطة...")
            try:
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.html', mode='w', encoding='utf-8')
                temp_file.close()
                self.current_map_file = temp_file.name

                # حفظ الخريطة
                m.save(self.current_map_file)
                print(f"تم حفظ الخريطة في: {self.current_map_file}")

                # تحديث الحالة
                self.status_label.setText("تم إنشاء الخريطة بنجاح!")
                self.open_browser_btn.setEnabled(True)
                self.save_map_btn.setEnabled(True)

            except Exception as save_error:
                raise Exception(f"فشل في حفظ الخريطة: {str(save_error)}")

        except Exception as e:
            error_msg = str(e)
            print(f"خطأ في إنشاء الخريطة: {error_msg}")
            import traceback
            traceback.print_exc()
            self.status_label.setText("فشل في إنشاء الخريطة")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء الخريطة:\n{error_msg}")

    def add_ports_to_map(self, map_obj):
        """إضافة الموانئ إلى الخريطة"""
        locations = self.map_data.get('locations', {})

        for port_name, (lat, lon) in locations.items():
            # تحديد لون الميناء حسب النشاط
            port_activity = self.get_port_activity(port_name)

            if port_activity > 10:
                color = 'red'
                icon = 'anchor'
            elif port_activity > 5:
                color = 'orange'
                icon = 'ship'
            else:
                color = 'blue'
                icon = 'circle'

            # إضافة علامة الميناء
            try:
                folium.Marker(
                    location=[lat, lon],
                    popup=folium.Popup(
                        f"""
                        <div style='width: 200px; font-family: Arial, sans-serif;'>
                            <h4 style='color: #2E86AB; margin-bottom: 10px;'>{port_name}</h4>
                            <p><strong>الإحداثيات:</strong> {lat:.4f}, {lon:.4f}</p>
                            <p><strong>عدد الشحنات:</strong> {port_activity}</p>
                        </div>
                        """,
                        max_width=250
                    ),
                    tooltip=f"ميناء {port_name}",
                    icon=folium.Icon(color=color, icon='ship' if icon == 'ship' else 'circle')
                ).add_to(map_obj)
            except Exception as e:
                print(f"تحذير: فشل في إضافة ميناء {port_name}: {e}")
                # إضافة علامة بسيطة كبديل
                folium.CircleMarker(
                    location=[lat, lon],
                    radius=10,
                    popup=f"ميناء {port_name}",
                    color=color,
                    fillColor=color,
                    fillOpacity=0.7
                ).add_to(map_obj)

    def add_shipments_to_map(self, map_obj):
        """إضافة الشحنات إلى الخريطة"""
        shipments = self.map_data.get('shipments', [])
        locations = self.map_data.get('locations', {})

        # ألوان حسب حالة الشحنة
        status_colors = {
            'جديدة': 'gray',
            'قيد التحضير': 'blue',
            'تم الشحن': 'green',
            'في الطريق': 'orange',
            'وصلت': 'purple',
            'تم التسليم': 'darkgreen',
            'ملغية': 'red'
        }

        for shipment in shipments:
            # الحصول على موقع الشحنة
            departure_port = getattr(shipment, 'port_of_loading', '')
            arrival_port = getattr(shipment, 'port_of_discharge', '')

            # تحديد الموقع الحالي للشحنة
            current_location = None
            status = getattr(shipment, 'shipment_status', 'جديدة')

            if status in ['جديدة', 'قيد التحضير'] and departure_port in locations:
                current_location = locations[departure_port]
            elif status in ['وصلت', 'تم التسليم'] and arrival_port in locations:
                current_location = locations[arrival_port]
            elif status == 'في الطريق' and departure_port in locations and arrival_port in locations:
                # حساب موقع تقريبي في منتصف المسار
                dep_coords = locations[departure_port]
                arr_coords = locations[arrival_port]
                current_location = (
                    (dep_coords[0] + arr_coords[0]) / 2,
                    (dep_coords[1] + arr_coords[1]) / 2
                )

            if current_location:
                color = status_colors.get(status, 'gray')

                # إضافة علامة الشحنة
                folium.CircleMarker(
                    location=current_location,
                    radius=8,
                    popup=folium.Popup(
                        f"""
                        <div style='width: 250px;'>
                            <h4>شحنة رقم {shipment.id}</h4>
                            <p><strong>الحالة:</strong> {status}</p>
                            <p><strong>ميناء المغادرة:</strong> {departure_port}</p>
                            <p><strong>ميناء الوصول:</strong> {arrival_port}</p>
                            <p><strong>شركة الشحن:</strong> {getattr(shipment, 'shipping_company', 'غير محدد')}</p>
                        </div>
                        """,
                        max_width=300
                    ),
                    tooltip=f"شحنة {shipment.id} - {status}",
                    color='white',
                    fillColor=color,
                    fillOpacity=0.8,
                    weight=2
                ).add_to(map_obj)

    def add_routes_to_map(self, map_obj):
        """إضافة المسارات إلى الخريطة"""
        shipments = self.map_data.get('shipments', [])
        locations = self.map_data.get('locations', {})

        # تجميع المسارات لتجنب التكرار
        routes = {}

        for shipment in shipments:
            departure_port = getattr(shipment, 'port_of_loading', '')
            arrival_port = getattr(shipment, 'port_of_discharge', '')

            if departure_port in locations and arrival_port in locations:
                route_key = f"{departure_port}-{arrival_port}"
                if route_key not in routes:
                    routes[route_key] = {
                        'departure': locations[departure_port],
                        'arrival': locations[arrival_port],
                        'shipments': []
                    }
                routes[route_key]['shipments'].append(shipment)

        # رسم المسارات
        for route_key, route_data in routes.items():
            departure_coords = route_data['departure']
            arrival_coords = route_data['arrival']
            shipment_count = len(route_data['shipments'])

            # تحديد لون ووزن الخط حسب عدد الشحنات
            if shipment_count > 5:
                color = 'red'
                weight = 5
            elif shipment_count > 2:
                color = 'orange'
                weight = 3
            else:
                color = 'blue'
                weight = 2

            # رسم الخط
            folium.PolyLine(
                locations=[departure_coords, arrival_coords],
                color=color,
                weight=weight,
                opacity=0.7,
                popup=folium.Popup(
                    f"""
                    <div style='width: 200px;'>
                        <h4>مسار الشحن</h4>
                        <p><strong>من:</strong> {route_key.split('-')[0]}</p>
                        <p><strong>إلى:</strong> {route_key.split('-')[1]}</p>
                        <p><strong>عدد الشحنات:</strong> {shipment_count}</p>
                        <p><strong>المسافة:</strong> {self.calculate_distance(departure_coords, arrival_coords):.0f} كم</p>
                    </div>
                    """,
                    max_width=250
                )
            ).add_to(map_obj)

    def get_port_activity(self, port_name):
        """حساب نشاط الميناء"""
        if not self.map_data or 'shipments' not in self.map_data:
            return 0

        activity = 0
        shipments = self.map_data['shipments']

        for shipment in shipments:
            departure_port = getattr(shipment, 'port_of_loading', '')
            arrival_port = getattr(shipment, 'port_of_discharge', '')

            if port_name == departure_port or port_name == arrival_port:
                activity += 1

        return activity

    def calculate_distance(self, coord1, coord2):
        """حساب المسافة بين نقطتين"""
        try:
            return geodesic(coord1, coord2).kilometers
        except:
            return 0

    def update_map_display_info(self):
        """تحديث معلومات عرض الخريطة"""
        if self.current_map_file:
            self.map_display_label.setText(
                "✅ تم إنشاء الخريطة بنجاح!\n\n"
                "🌐 اضغط على 'فتح في المتصفح' لعرض الخريطة التفاعلية\n"
                "💾 اضغط على 'حفظ الخريطة' لحفظها على جهازك\n\n"
                "🗺️ الخريطة تحتوي على:\n"
                f"• {len(self.map_data.get('locations', {}))} ميناء\n"
                f"• {len(self.map_data.get('shipments', []))} شحنة\n"
                "• مسارات الشحن التفاعلية\n"
                "• معلومات مفصلة لكل عنصر"
            )
            self.map_display_label.setStyleSheet("""
                QLabel {
                    background-color: #E8F5E8;
                    border: 2px solid #4CAF50;
                    border-radius: 10px;
                    font-size: 14px;
                    color: #2E7D32;
                    padding: 20px;
                    min-height: 400px;
                }
            """)

    def filter_shipments(self):
        """فلترة الشحنات"""
        # إعادة إنشاء الخريطة مع الفلاتر الجديدة
        if self.map_data:
            self.create_folium_map()

    def update_map_display(self):
        """تحديث عرض الخريطة"""
        if self.map_data:
            self.create_folium_map()

    def open_in_browser(self):
        """فتح الخريطة في المتصفح"""
        if self.current_map_file and os.path.exists(self.current_map_file):
            try:
                # فتح الملف في المتصفح الافتراضي
                QDesktopServices.openUrl(QUrl.fromLocalFile(self.current_map_file))
                self.status_label.setText("تم فتح الخريطة في المتصفح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في فتح المتصفح:\n{str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "لا توجد خريطة لعرضها")

    def save_map(self):
        """حفظ الخريطة"""
        if not self.current_map_file or not os.path.exists(self.current_map_file):
            QMessageBox.warning(self, "تحذير", "لا توجد خريطة لحفظها")
            return

        try:
            # إنشاء اسم ملف مع التاريخ والوقت
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"shipment_map_{timestamp}.html"

            # نسخ الملف إلى مجلد المشروع
            import shutil
            destination = os.path.join(os.getcwd(), "maps", filename)
            os.makedirs(os.path.dirname(destination), exist_ok=True)
            shutil.copy2(self.current_map_file, destination)

            QMessageBox.information(
                self, "نجح الحفظ",
                f"تم حفظ الخريطة بنجاح:\n{destination}"
            )
            self.status_label.setText(f"تم حفظ الخريطة: {filename}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الخريطة:\n{str(e)}")

    def export_map(self):
        """تصدير الخريطة"""
        if not self.current_map_file:
            QMessageBox.warning(self, "تحذير", "لا توجد خريطة للتصدير")
            return

        try:
            # فتح الخريطة في المتصفح للتصدير
            self.open_in_browser()

            QMessageBox.information(
                self, "تصدير الخريطة",
                "تم فتح الخريطة في المتصفح.\n\n"
                "يمكنك الآن:\n"
                "• أخذ لقطة شاشة\n"
                "• طباعة الخريطة\n"
                "• حفظ الصفحة كـ PDF\n"
                "• مشاركة الرابط"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير الخريطة:\n{str(e)}")

    def closeEvent(self, event):
        """تنظيف الملفات المؤقتة عند إغلاق النافذة"""
        if self.current_map_file and os.path.exists(self.current_map_file):
            try:
                os.unlink(self.current_map_file)
            except:
                pass
        event.accept()
