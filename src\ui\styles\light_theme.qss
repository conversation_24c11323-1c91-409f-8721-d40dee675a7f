/* ثيم فاتح محسن ومتطور لنظام إدارة الشحنات */

/* النافذة الرئيسية */
QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #ffffff, stop:0.3 #fafbfc, stop:0.7 #f5f6fa, stop:1 #f1f2f6);
    font-family: "Segoe UI", "<PERSON>ibri", "Tahoma", sans-serif;
    font-size: 11px;
    color: #1a1a1a;
    font-weight: 400;
}

/* شريط القوائم */
QMenuBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    color: #1a1a1a;
    border: none;
    border-bottom: 1px solid #e9ecef;
    padding: 8px 12px;
    font-size: 11px;
    font-weight: 500;
    font-family: "Segoe UI", "<PERSON><PERSON><PERSON>", sans-serif;
}

QMenuBar::item {
    background: transparent;
    padding: 8px 16px;
    border-radius: 6px;
    margin: 2px;
    color: #1a1a1a;
}

QMenuBar::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e3f2fd, stop:1 #bbdefb);
    color: #0d47a1;
    border: 1px solid #90caf9;
}

QMenuBar::item:pressed {
    background: #bbdefb;
    color: #0d47a1;
}

/* القوائم المنسدلة */
QMenu {
    background: white;
    border: 1px solid #d0d7de;
    border-radius: 8px;
    padding: 8px;
    font-size: 11px;
    font-family: "Segoe UI", "Calibri", sans-serif;
    color: #1a1a1a;
    font-weight: 400;
}

QMenu::item {
    padding: 8px 20px;
    border-radius: 6px;
    margin: 1px;
    color: #1a1a1a;
}

QMenu::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e3f2fd, stop:1 #bbdefb);
    color: #0d47a1;
}

QMenu::separator {
    height: 1px;
    background: #e9ecef;
    margin: 6px 12px;
}

/* شريط الأدوات */
QToolBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: none;
    border-bottom: 1px solid #e9ecef;
    spacing: 6px;
    padding: 8px;
}

QToolBar QToolButton {
    background: transparent;
    color: #1a1a1a;
    border: 1px solid transparent;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
}

QToolBar QToolButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f0f8ff, stop:1 #e6f3ff);
    border-color: #b3d9ff;
    color: #0d47a1;
}

QToolBar QToolButton:pressed {
    background: #e6f3ff;
    border-color: #90caf9;
}

/* شريط الحالة */
QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border-top: 1px solid #e9ecef;
    color: #495057;
    font-size: 10px;
    font-weight: 500;
    padding: 4px 8px;
}

QStatusBar QLabel {
    padding: 4px 8px;
    border-radius: 4px;
    background: transparent;
    color: #495057;
}

/* الأزرار العامة */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    color: #1a1a1a;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    padding: 10px 20px;
    font-size: 11px;
    font-weight: 500;
    font-family: "Segoe UI", "Calibri", sans-serif;
    min-height: 14px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f0f8ff, stop:1 #e6f3ff);
    border-color: #0969da;
    color: #0969da;
}

QPushButton:pressed {
    background: #e6f3ff;
    border-color: #0550ae;
    color: #0550ae;
}

QPushButton:disabled {
    background: #f6f8fa;
    color: #8c959f;
    border-color: #d1d9e0;
}

/* أزرار أساسية */
QPushButton[class="primary"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #0969da, stop:1 #0550ae);
    color: white;
    border: 1px solid #0550ae;
    font-weight: 600;
}

QPushButton[class="primary"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #0550ae, stop:1 #033d8b);
}

/* أزرار النجاح */
QPushButton[class="success"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1a7f37, stop:1 #116329);
    color: white;
    border: 1px solid #116329;
    font-weight: 600;
}

QPushButton[class="success"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #116329, stop:1 #0d4f23);
}

/* أزرار الخطر */
QPushButton[class="danger"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #cf222e, stop:1 #a40e26);
    color: white;
    border: 1px solid #a40e26;
    font-weight: 600;
}

QPushButton[class="danger"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #a40e26, stop:1 #82071e);
}

/* حقول الإدخال */
QLineEdit, QTextEdit, QPlainTextEdit {
    background: white;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 11px;
    font-family: "Segoe UI", "Calibri", sans-serif;
    color: #1a1a1a;
    selection-background-color: #0969da;
    selection-color: white;
    font-weight: 400;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #0969da;
    outline: 2px solid #0969da40;
    background: #ffffff;
}

QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
    background: #f6f8fa;
    color: #8c959f;
    border-color: #d1d9e0;
}

/* القوائم المنسدلة */
QComboBox {
    background: white;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 11px;
    font-family: "Segoe UI", "Calibri", sans-serif;
    color: #1a1a1a;
    min-width: 120px;
    font-weight: 400;
}

QComboBox:focus {
    border-color: #0969da;
    outline: 2px solid #0969da40;
}

QComboBox:disabled {
    background: #f6f8fa;
    color: #8c959f;
    border-color: #d1d9e0;
}

QComboBox::drop-down {
    border: none;
    width: 24px;
    background: transparent;
}

QComboBox::down-arrow {
    width: 12px;
    height: 12px;
    background: #656d76;
}

QComboBox QAbstractItemView {
    background: white;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    selection-background-color: #e6f3ff;
    selection-color: #0969da;
    font-size: 11px;
    padding: 4px;
}

/* مربعات الاختيار */
QCheckBox {
    color: #1a1a1a;
    font-size: 11px;
    font-family: "Segoe UI", "Calibri", sans-serif;
    font-weight: 400;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #d0d7de;
    border-radius: 3px;
    background: white;
}

QCheckBox::indicator:hover {
    border-color: #0969da;
}

QCheckBox::indicator:checked {
    background: #0969da;
    border-color: #0969da;
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjIgNy44TDEuNCA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
}

/* أزرار الراديو */
QRadioButton {
    color: #1a1a1a;
    font-size: 11px;
    font-family: "Segoe UI", "Calibri", sans-serif;
    font-weight: 400;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #d0d7de;
    border-radius: 8px;
    background: white;
}

QRadioButton::indicator:hover {
    border-color: #0969da;
}

QRadioButton::indicator:checked {
    background: white;
    border: 4px solid #0969da;
}

/* شريط التقدم */
QProgressBar {
    background: #f6f8fa;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    height: 16px;
    text-align: center;
    font-size: 10px;
    font-weight: 500;
    color: #1a1a1a;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #0969da, stop:1 #0550ae);
    border-radius: 6px;
}

/* الجداول */
QTableWidget {
    background: white;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    font-size: 11px;
    font-family: "Segoe UI", "Calibri", sans-serif;
    gridline-color: #f6f8fa;
    selection-background-color: #e6f3ff;
    selection-color: #0969da;
    alternate-background-color: #f6f8fa;
    color: #1a1a1a;
}

QTableWidget::item {
    padding: 8px 12px;
    border-bottom: 1px solid #f6f8fa;
    color: #1a1a1a;
    font-weight: 400;
}

QTableWidget::item:selected {
    background: #e6f3ff;
    color: #0969da;
}

QTableWidget::item:hover {
    background: #f0f8ff;
}

QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f6f8fa, stop:1 #eaeef2);
    color: #1a1a1a;
    padding: 8px 12px;
    border: none;
    border-right: 1px solid #d0d7de;
    border-bottom: 1px solid #d0d7de;
    font-weight: 600;
    font-size: 10px;
    font-family: "Segoe UI", "Calibri", sans-serif;
}

QHeaderView::section:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #eaeef2, stop:1 #d0d7de);
}

/* مجموعات العناصر */
QGroupBox {
    font-weight: 600;
    font-size: 11px;
    font-family: "Segoe UI", "Calibri", sans-serif;
    color: #1a1a1a;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    margin-top: 12px;
    padding-top: 12px;
    background: white;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 12px;
    padding: 0 8px 0 8px;
    color: #1a1a1a;
    font-weight: 600;
    background: white;
}

/* التبويبات */
QTabWidget::pane {
    border: 1px solid #d0d7de;
    border-radius: 6px;
    background: white;
    top: -1px;
}

QTabBar::tab {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f6f8fa, stop:1 #eaeef2);
    color: #1a1a1a;
    border: 1px solid #d0d7de;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    font-size: 11px;
    font-weight: 500;
    font-family: "Segoe UI", "Calibri", sans-serif;
}

QTabBar::tab:selected {
    background: white;
    border-bottom: 2px solid #0969da;
    color: #0969da;
    font-weight: 600;
}

QTabBar::tab:hover:!selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #eaeef2, stop:1 #d0d7de);
}

/* أشرطة التمرير */
QScrollBar:vertical {
    background: #f6f8fa;
    width: 12px;
    border-radius: 6px;
    border: 1px solid #d0d7de;
}

QScrollBar::handle:vertical {
    background: #d0d7de;
    border-radius: 5px;
    min-height: 20px;
    margin: 1px;
}

QScrollBar::handle:vertical:hover {
    background: #8c959f;
}

QScrollBar::handle:vertical:pressed {
    background: #656d76;
}

QScrollBar:horizontal {
    background: #f6f8fa;
    height: 12px;
    border-radius: 6px;
    border: 1px solid #d0d7de;
}

QScrollBar::handle:horizontal {
    background: #d0d7de;
    border-radius: 5px;
    min-width: 20px;
    margin: 1px;
}

QScrollBar::handle:horizontal:hover {
    background: #8c959f;
}

QScrollBar::handle:horizontal:pressed {
    background: #656d76;
}

/* الإطارات */
QFrame {
    border-radius: 6px;
    background: white;
}

QFrame[frameShape="1"] { /* StyledPanel */
    border: 1px solid #d0d7de;
    background: white;
}

QFrame[frameShape="4"] { /* HLine */
    border: none;
    background: #d0d7de;
    max-height: 1px;
}

QFrame[frameShape="5"] { /* VLine */
    border: none;
    background: #d0d7de;
    max-width: 1px;
}

/* التسميات */
QLabel {
    color: #1a1a1a;
    font-size: 11px;
    font-family: "Segoe UI", "Calibri", sans-serif;
    font-weight: 400;
    background: transparent;
}

/* نصائح الأدوات */
QToolTip {
    background: #1a1a1a;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 10px;
    font-family: "Segoe UI", "Calibri", sans-serif;
    font-weight: 400;
}

/* مربعات الحوار */
QDialog {
    background: white;
    border: 1px solid #d0d7de;
    border-radius: 8px;
}

/* مربعات النص الدوارة */
QSpinBox, QDoubleSpinBox {
    background: white;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    padding: 6px 8px;
    font-size: 11px;
    font-family: "Segoe UI", "Calibri", sans-serif;
    color: #1a1a1a;
    font-weight: 400;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #0969da;
    outline: 2px solid #0969da40;
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    background: #f6f8fa;
    border: 1px solid #d0d7de;
    border-radius: 3px;
    width: 16px;
}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {
    background: #eaeef2;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    background: #f6f8fa;
    border: 1px solid #d0d7de;
    border-radius: 3px;
    width: 16px;
}

QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background: #eaeef2;
}

/* منزلقات القيم */
QSlider::groove:horizontal {
    background: #f6f8fa;
    height: 6px;
    border-radius: 3px;
    border: 1px solid #d0d7de;
}

QSlider::handle:horizontal {
    background: #0969da;
    border: 1px solid #0550ae;
    width: 16px;
    height: 16px;
    border-radius: 8px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background: #0550ae;
}

/* تحسينات خاصة للنوافذ */
QMainWindow::separator {
    background: #d0d7de;
    width: 1px;
    height: 1px;
}

QMainWindow::separator:hover {
    background: #8c959f;
}

/* تحسينات للقوائم */
QListWidget {
    background: white;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    font-size: 11px;
    font-family: "Segoe UI", "Calibri", sans-serif;
    color: #1a1a1a;
    selection-background-color: #e6f3ff;
    selection-color: #0969da;
    alternate-background-color: #f6f8fa;
}

QListWidget::item {
    padding: 8px 12px;
    border-bottom: 1px solid #f6f8fa;
    color: #1a1a1a;
}

QListWidget::item:selected {
    background: #e6f3ff;
    color: #0969da;
}

QListWidget::item:hover {
    background: #f0f8ff;
}

/* تحسينات للشجرة */
QTreeWidget {
    background: white;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    font-size: 11px;
    font-family: "Segoe UI", "Calibri", sans-serif;
    color: #1a1a1a;
    selection-background-color: #e6f3ff;
    selection-color: #0969da;
    alternate-background-color: #f6f8fa;
}

QTreeWidget::item {
    padding: 6px 8px;
    color: #1a1a1a;
}

QTreeWidget::item:selected {
    background: #e6f3ff;
    color: #0969da;
}

QTreeWidget::item:hover {
    background: #f0f8ff;
}
