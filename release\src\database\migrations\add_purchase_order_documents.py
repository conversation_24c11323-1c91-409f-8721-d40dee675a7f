#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة حقول المستندات إلى جدول طلبات الشراء
Migration: add_purchase_order_documents
Created: 2025-07-05
"""

import sqlite3
import os
from pathlib import Path

def get_database_path():
    """الحصول على مسار قاعدة البيانات"""
    # البحث عن ملف قاعدة البيانات في المجلد الجذر للمشروع
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent.parent
    
    # البحث عن ملفات قاعدة البيانات المحتملة
    possible_paths = [
        project_root / "proshipment.db",
        project_root / "database.db",
        project_root / "data" / "proshipment.db",
        project_root / "src" / "database" / "proshipment.db"
    ]
    
    for path in possible_paths:
        if path.exists():
            return str(path)
    
    # إذا لم توجد، استخدم المسار الافتراضي
    return str(project_root / "proshipment.db")

def add_documents_columns():
    """إضافة أعمدة المستندات إلى جدول طلبات الشراء"""
    db_path = get_database_path()
    
    print(f"🔍 البحث عن قاعدة البيانات في: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ لم يتم العثور على قاعدة البيانات في: {db_path}")
        print("💡 تأكد من وجود ملف قاعدة البيانات أو قم بتشغيل التطبيق أولاً لإنشائها")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 فحص هيكل جدول purchase_orders...")
        
        # فحص الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(purchase_orders)")
        existing_columns = [column[1] for column in cursor.fetchall()]
        
        print(f"📋 الأعمدة الموجودة: {existing_columns}")
        
        # قائمة الأعمدة الجديدة المطلوب إضافتها
        new_columns = [
            ("contract_url", "TEXT", "رابط العقد مع المورد"),
            ("initial_designs_url", "TEXT", "رابط التصاميم الأولية"),
            ("final_design_url", "TEXT", "رابط التصميم النهائي المعتمد"),
            ("other_attachments_url", "TEXT", "رابط المرفقات الأخرى")
        ]
        
        # إضافة الأعمدة الجديدة
        columns_added = 0
        for column_name, column_type, comment in new_columns:
            if column_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE purchase_orders ADD COLUMN {column_name} {column_type}"
                    cursor.execute(sql)
                    print(f"✅ تم إضافة العمود: {column_name} ({comment})")
                    columns_added += 1
                except sqlite3.Error as e:
                    print(f"❌ خطأ في إضافة العمود {column_name}: {e}")
            else:
                print(f"⚠️  العمود {column_name} موجود مسبقاً")
        
        # حفظ التغييرات
        conn.commit()
        
        if columns_added > 0:
            print(f"🎉 تم إضافة {columns_added} عمود جديد بنجاح!")
        else:
            print("ℹ️  جميع الأعمدة موجودة مسبقاً")
        
        # التحقق من النتيجة النهائية
        cursor.execute("PRAGMA table_info(purchase_orders)")
        final_columns = [column[1] for column in cursor.fetchall()]
        
        print(f"📊 الأعمدة النهائية ({len(final_columns)}): {final_columns}")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء migration: إضافة حقول المستندات إلى طلبات الشراء")
    print("=" * 60)
    
    success = add_documents_columns()
    
    print("=" * 60)
    if success:
        print("✅ تم تنفيذ Migration بنجاح!")
        print("💡 يمكنك الآن استخدام حقول المستندات في شاشة طلبات الشراء")
    else:
        print("❌ فشل في تنفيذ Migration")
        print("💡 تحقق من رسائل الخطأ أعلاه وحاول مرة أخرى")

if __name__ == "__main__":
    main()
