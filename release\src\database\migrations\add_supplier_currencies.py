# -*- coding: utf-8 -*-
"""
إضافة جدول ربط الموردين بالعملات
Add Supplier Currencies Table Migration
"""

import sqlite3
import os
from datetime import datetime

def add_supplier_currencies_table():
    """إضافة جدول ربط الموردين بالعملات"""
    
    # مسار قاعدة البيانات
    db_path = 'data/proshipment.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 فحص وجود جدول supplier_currencies...")
        
        # فحص وجود الجدول
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='supplier_currencies'
        """)
        
        if cursor.fetchone():
            print("ℹ️ جدول supplier_currencies موجود بالفعل")
            return True
        
        print("🔨 إنشاء جدول supplier_currencies...")
        
        # إنشاء الجدول
        cursor.execute("""
            CREATE TABLE supplier_currencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_id INTEGER NOT NULL,
                currency_id INTEGER NOT NULL,
                is_preferred BOOLEAN DEFAULT 0,
                exchange_rate_override REAL,
                notes TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (currency_id) REFERENCES currencies (id)
            )
        """)
        
        # إنشاء فهرس فريد
        cursor.execute("""
            CREATE UNIQUE INDEX idx_supplier_currency_unique 
            ON supplier_currencies (supplier_id, currency_id)
        """)
        
        # إنشاء فهارس إضافية للأداء
        cursor.execute("""
            CREATE INDEX idx_supplier_currencies_supplier_id 
            ON supplier_currencies (supplier_id)
        """)
        
        cursor.execute("""
            CREATE INDEX idx_supplier_currencies_currency_id 
            ON supplier_currencies (currency_id)
        """)
        
        cursor.execute("""
            CREATE INDEX idx_supplier_currencies_is_preferred 
            ON supplier_currencies (is_preferred)
        """)
        
        # حفظ التغييرات
        conn.commit()
        
        print("✅ تم إنشاء جدول supplier_currencies بنجاح!")
        print("✅ تم إنشاء الفهارس بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجدول: {str(e)}")
        return False
        
    finally:
        if conn:
            conn.close()

def test_supplier_currencies_table():
    """اختبار جدول ربط الموردين بالعملات"""
    try:
        from ...database.models import SupplierCurrency, Supplier, Currency
        from ...database.database_manager import DatabaseManager
        
        print("\n🧪 اختبار جدول supplier_currencies...")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # البحث عن مورد وعملة للاختبار
        supplier = session.query(Supplier).first()
        currency = session.query(Currency).first()
        
        if supplier and currency:
            print(f"📦 اختبار المورد: {supplier.name}")
            print(f"💰 اختبار العملة: {currency.name}")
            
            # فحص وجود ربط مسبق
            existing = session.query(SupplierCurrency).filter_by(
                supplier_id=supplier.id,
                currency_id=currency.id
            ).first()
            
            if not existing:
                # إنشاء ربط تجريبي
                supplier_currency = SupplierCurrency(
                    supplier_id=supplier.id,
                    currency_id=currency.id,
                    is_preferred=True,
                    notes="ربط تجريبي"
                )
                
                session.add(supplier_currency)
                session.commit()
                
                print("✅ تم إنشاء ربط تجريبي بنجاح!")
            else:
                print("ℹ️ الربط موجود بالفعل")
            
            # اختبار العلاقات
            supplier_currencies = session.query(SupplierCurrency).filter_by(
                supplier_id=supplier.id
            ).all()
            
            print(f"📊 عدد العملات المرتبطة بالمورد: {len(supplier_currencies)}")
            
            for sc in supplier_currencies:
                print(f"   - {sc.currency.name} ({'مفضلة' if sc.is_preferred else 'عادية'})")
            
        else:
            print("ℹ️ لا توجد بيانات موردين أو عملات للاختبار")
            
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إضافة جدول ربط الموردين بالعملات")
    print("=" * 50)
    
    if add_supplier_currencies_table():
        print("\n" + "=" * 50)
        test_supplier_currencies_table()
        print("\n🎉 تم إكمال العملية بنجاح!")
    else:
        print("\n❌ فشل في إضافة الجدول")
