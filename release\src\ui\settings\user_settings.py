# -*- coding: utf-8 -*-
"""
تبويب إعدادات المستخدمين
User Settings Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QComboBox, QCheckBox)
from PySide6.QtCore import Qt
import hashlib

from ...database.database_manager import DatabaseManager
from ...database.models import User

class UserSettingsWidget(QWidget):
    """ويدجت إعدادات المستخدمين"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # مجموعة إضافة مستخدم جديد
        add_group = QGroupBox("إضافة مستخدم جديد")
        add_layout = QFormLayout(add_group)
        
        self.username_edit = QLineEdit()
        add_layout.addRow("اسم المستخدم:", self.username_edit)
        
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        add_layout.addRow("كلمة المرور:", self.password_edit)
        
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        add_layout.addRow("تأكيد كلمة المرور:", self.confirm_password_edit)
        
        self.full_name_edit = QLineEdit()
        add_layout.addRow("الاسم الكامل:", self.full_name_edit)
        
        self.email_edit = QLineEdit()
        add_layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        self.phone_edit = QLineEdit()
        add_layout.addRow("الهاتف:", self.phone_edit)
        
        self.role_combo = QComboBox()
        self.role_combo.addItems(["user", "admin", "manager", "operator"])
        add_layout.addRow("الدور:", self.role_combo)
        
        self.is_active_check = QCheckBox("نشط")
        self.is_active_check.setChecked(True)
        add_layout.addRow("", self.is_active_check)
        
        # أزرار الإدارة
        buttons_layout = QHBoxLayout()
        
        add_button = QPushButton("إضافة مستخدم")
        add_button.clicked.connect(self.add_user)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        clear_button = QPushButton("مسح الحقول")
        clear_button.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(add_button)
        buttons_layout.addWidget(clear_button)
        buttons_layout.addStretch()
        
        add_layout.addRow("", buttons_layout)
        main_layout.addWidget(add_group)
        
        # جدول المستخدمين
        table_group = QGroupBox("المستخدمون المسجلون")
        table_layout = QVBoxLayout(table_group)
        
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(6)
        self.users_table.setHorizontalHeaderLabels([
            "اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني", "الهاتف", "الدور", "الحالة"
        ])
        
        # تنسيق الجدول
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        table_layout.addWidget(self.users_table)
        
        # أزرار إدارة الجدول
        table_buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_data)
        
        edit_button = QPushButton("تعديل المحدد")
        edit_button.clicked.connect(self.edit_selected)
        
        reset_password_button = QPushButton("إعادة تعيين كلمة المرور")
        reset_password_button.clicked.connect(self.reset_password)
        
        delete_button = QPushButton("حذف المحدد")
        delete_button.clicked.connect(self.delete_selected)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(edit_button)
        table_buttons_layout.addWidget(reset_password_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addStretch()
        
        table_layout.addLayout(table_buttons_layout)
        main_layout.addWidget(table_group)
        
        main_layout.addStretch()
    
    def load_data(self):
        """تحميل بيانات المستخدمين"""
        session = self.db_manager.get_session()
        try:
            users = session.query(User).all()
            
            self.users_table.setRowCount(len(users))
            
            for row, user in enumerate(users):
                # اسم المستخدم
                self.users_table.setItem(row, 0, QTableWidgetItem(user.username))
                
                # الاسم الكامل
                self.users_table.setItem(row, 1, QTableWidgetItem(user.full_name or ""))
                
                # البريد الإلكتروني
                self.users_table.setItem(row, 2, QTableWidgetItem(user.email or ""))
                
                # الهاتف
                self.users_table.setItem(row, 3, QTableWidgetItem(user.phone or ""))
                
                # الدور
                role_text = {
                    "admin": "مدير",
                    "manager": "مدير قسم",
                    "operator": "مشغل",
                    "user": "مستخدم"
                }.get(user.role, user.role)
                self.users_table.setItem(row, 4, QTableWidgetItem(role_text))
                
                # الحالة
                status_text = "نشط" if user.is_active else "غير نشط"
                status_item = QTableWidgetItem(status_text)
                if user.is_active:
                    status_item.setBackground(Qt.green)
                else:
                    status_item.setBackground(Qt.red)
                self.users_table.setItem(row, 5, status_item)
                
                # حفظ ID في البيانات
                self.users_table.item(row, 0).setData(Qt.UserRole, user.id)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل بيانات المستخدمين:\n{str(e)}"
            )
        finally:
            session.close()
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        # التحقق من صحة البيانات
        if not self.username_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم")
            return
        
        if not self.password_edit.text():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور")
            return
        
        if self.password_edit.text() != self.confirm_password_edit.text():
            QMessageBox.warning(self, "خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
            return
        
        if not self.full_name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال الاسم الكامل")
            return
        
        session = self.db_manager.get_session()
        try:
            # التحقق من عدم وجود المستخدم مسبقاً
            existing = session.query(User).filter_by(username=self.username_edit.text()).first()
            if existing:
                QMessageBox.warning(self, "مستخدم موجود", "اسم المستخدم موجود بالفعل")
                return
            
            # تشفير كلمة المرور
            password_hash = hashlib.sha256(self.password_edit.text().encode()).hexdigest()
            
            # إنشاء المستخدم الجديد
            new_user = User(
                username=self.username_edit.text(),
                password_hash=password_hash,
                full_name=self.full_name_edit.text(),
                email=self.email_edit.text(),
                phone=self.phone_edit.text(),
                role=self.role_combo.currentText(),
                is_active=self.is_active_check.isChecked()
            )
            
            session.add(new_user)
            session.commit()
            
            QMessageBox.information(self, "تم الإضافة", "تم إضافة المستخدم بنجاح")
            
            self.clear_form()
            self.load_data()
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في الإضافة", f"حدث خطأ أثناء إضافة المستخدم:\n{str(e)}")
        finally:
            session.close()
    
    def clear_form(self):
        """مسح حقول النموذج"""
        self.username_edit.clear()
        self.password_edit.clear()
        self.confirm_password_edit.clear()
        self.full_name_edit.clear()
        self.email_edit.clear()
        self.phone_edit.clear()
        self.role_combo.setCurrentIndex(0)
        self.is_active_check.setChecked(True)
    
    def edit_selected(self):
        """تعديل المستخدم المحدد"""
        QMessageBox.information(self, "قيد التطوير", "ميزة تعديل المستخدم قيد التطوير")
    
    def reset_password(self):
        """إعادة تعيين كلمة مرور المستخدم المحدد"""
        QMessageBox.information(self, "قيد التطوير", "ميزة إعادة تعيين كلمة المرور قيد التطوير")
    
    def delete_selected(self):
        """حذف المستخدم المحدد"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "لا يوجد تحديد", "يرجى تحديد مستخدم للحذف")
            return
        
        user_id = self.users_table.item(current_row, 0).data(Qt.UserRole)
        username = self.users_table.item(current_row, 0).text()
        
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف المستخدم {username}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                user = session.query(User).get(user_id)
                if user:
                    session.delete(user)
                    session.commit()
                    
                    QMessageBox.information(self, "تم الحذف", f"تم حذف المستخدم {username} بنجاح")
                    self.load_data()
                    
            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف المستخدم:\n{str(e)}")
            finally:
                session.close()
    
    def save_settings(self):
        """حفظ الإعدادات"""
        pass
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        self.clear_form()
