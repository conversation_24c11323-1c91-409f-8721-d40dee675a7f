# -*- coding: utf-8 -*-
"""
ويدجت إدارة البنوك
Banks Management Widget
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QLineEdit, QComboBox, QTableWidget,
                               QTableWidgetItem, QHeaderView, QAbstractItemView, QGroupBox,
                               QFormLayout, QMessageBox, QDialog, QDialogButtonBox,
                               QSplitter, QFrame, QTextEdit, QCheckBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QIcon, QPalette, QColor

from ...database.database_manager import DatabaseManager
from ...database.models import Bank, BankAccount, Currency


class BanksManagementWidget(QWidget):
    """ويدجت إدارة البنوك"""
    
    # الإشارات
    bank_created = Signal(dict)
    bank_updated = Signal(dict)
    account_created = Signal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.current_bank = None
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        self.create_toolbar()
        layout.addWidget(self.toolbar_frame)
        
        # المحتوى الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # الجانب الأيسر - قائمة البنوك
        self.create_banks_list()
        splitter.addWidget(self.banks_group)
        
        # الجانب الأيمن - تفاصيل البنك
        self.create_bank_details()
        splitter.addWidget(self.details_group)
        
        # تعيين النسب
        splitter.setSizes([400, 600])
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar_frame = QFrame()
        self.toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_layout = QHBoxLayout(self.toolbar_frame)
        
        # أزرار العمليات
        self.new_bank_btn = QPushButton("🏛️ بنك جديد")
        self.new_bank_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e7e34, stop:1 #155724);
            }
        """)
        
        self.edit_bank_btn = QPushButton("✏️ تعديل")
        self.edit_bank_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffc107, stop:1 #e0a800);
                border: none;
                border-radius: 6px;
                color: #212529;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e0a800, stop:1 #d39e00);
            }
        """)
        
        self.new_account_btn = QPushButton("💳 حساب جديد")
        self.new_account_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0056b3, stop:1 #004085);
            }
        """)
        
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #17a2b8, stop:1 #138496);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #138496, stop:1 #117a8b);
            }
        """)
        
        # إضافة الأزرار
        toolbar_layout.addWidget(self.new_bank_btn)
        toolbar_layout.addWidget(self.edit_bank_btn)
        toolbar_layout.addWidget(self.new_account_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addStretch()
        
        # شريط البحث
        search_label = QLabel("🔍 بحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث باسم البنك أو الكود...")
        self.search_edit.setMaximumWidth(300)
        
        toolbar_layout.addWidget(search_label)
        toolbar_layout.addWidget(self.search_edit)
        
    def create_banks_list(self):
        """إنشاء قائمة البنوك"""
        self.banks_group = QGroupBox("🏛️ قائمة البنوك")
        banks_layout = QVBoxLayout(self.banks_group)
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_layout = QHBoxLayout(stats_frame)
        
        self.total_banks_label = QLabel("إجمالي البنوك: 0")
        self.active_banks_label = QLabel("البنوك النشطة: 0")
        self.total_accounts_label = QLabel("إجمالي الحسابات: 0")
        
        stats_layout.addWidget(self.total_banks_label)
        stats_layout.addWidget(self.active_banks_label)
        stats_layout.addWidget(self.total_accounts_label)
        stats_layout.addStretch()
        
        banks_layout.addWidget(stats_frame)
        
        # الجدول
        self.banks_table = QTableWidget()
        self.banks_table.setColumnCount(6)
        self.banks_table.setHorizontalHeaderLabels([
            "الكود", "اسم البنك", "رمز SWIFT", "الدولة", "عدد الحسابات", "الحالة"
        ])
        
        # إعداد الجدول
        header = self.banks_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.banks_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.banks_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.banks_table.setAlternatingRowColors(True)
        self.banks_table.setSortingEnabled(True)
        
        banks_layout.addWidget(self.banks_table)
        
    def create_bank_details(self):
        """إنشاء تفاصيل البنك"""
        self.details_group = QGroupBox("📋 تفاصيل البنك")
        details_layout = QVBoxLayout(self.details_group)
        
        # معلومات البنك
        info_group = QGroupBox("📄 معلومات البنك")
        info_layout = QFormLayout(info_group)
        
        self.info_code_label = QLabel("-")
        self.info_name_label = QLabel("-")
        self.info_name_en_label = QLabel("-")
        self.info_swift_label = QLabel("-")
        self.info_country_label = QLabel("-")
        self.info_city_label = QLabel("-")
        self.info_address_label = QLabel("-")
        self.info_phone_label = QLabel("-")
        self.info_email_label = QLabel("-")
        self.info_website_label = QLabel("-")
        self.info_status_label = QLabel("-")
        
        info_layout.addRow("الكود:", self.info_code_label)
        info_layout.addRow("اسم البنك:", self.info_name_label)
        info_layout.addRow("الاسم الإنجليزي:", self.info_name_en_label)
        info_layout.addRow("رمز SWIFT:", self.info_swift_label)
        info_layout.addRow("الدولة:", self.info_country_label)
        info_layout.addRow("المدينة:", self.info_city_label)
        info_layout.addRow("العنوان:", self.info_address_label)
        info_layout.addRow("الهاتف:", self.info_phone_label)
        info_layout.addRow("البريد الإلكتروني:", self.info_email_label)
        info_layout.addRow("الموقع الإلكتروني:", self.info_website_label)
        info_layout.addRow("الحالة:", self.info_status_label)
        
        details_layout.addWidget(info_group)
        
        # حسابات البنك
        accounts_group = QGroupBox("💳 حسابات البنك")
        accounts_layout = QVBoxLayout(accounts_group)
        
        self.accounts_table = QTableWidget()
        self.accounts_table.setColumnCount(5)
        self.accounts_table.setHorizontalHeaderLabels([
            "رقم الحساب", "اسم الحساب", "IBAN", "العملة", "الرصيد"
        ])
        
        # إعداد جدول الحسابات
        header = self.accounts_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.accounts_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.accounts_table.setAlternatingRowColors(True)
        self.accounts_table.setMaximumHeight(200)
        
        accounts_layout.addWidget(self.accounts_table)
        details_layout.addWidget(accounts_group)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        # أزرار العمليات
        self.new_bank_btn.clicked.connect(self.new_bank)
        self.edit_bank_btn.clicked.connect(self.edit_bank)
        self.new_account_btn.clicked.connect(self.new_account)
        self.refresh_btn.clicked.connect(self.refresh_data)
        
        # البحث
        self.search_edit.textChanged.connect(self.filter_banks)
        
        # الجدول
        self.banks_table.itemSelectionChanged.connect(self.on_bank_selection_changed)
        
    def load_data(self):
        """تحميل البيانات"""
        try:
            self.load_banks()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}")
            
    def load_banks(self):
        """تحميل قائمة البنوك"""
        try:
            session = self.db_manager.get_session()
            banks = session.query(Bank).all()
            
            self.banks_table.setRowCount(len(banks))
            
            active_count = 0
            total_accounts = 0
            
            for row, bank in enumerate(banks):
                # الكود
                self.banks_table.setItem(row, 0, QTableWidgetItem(bank.code or ""))
                
                # اسم البنك
                self.banks_table.setItem(row, 1, QTableWidgetItem(bank.name or ""))
                
                # رمز SWIFT
                self.banks_table.setItem(row, 2, QTableWidgetItem(bank.swift_code or ""))
                
                # الدولة
                self.banks_table.setItem(row, 3, QTableWidgetItem(bank.country or ""))
                
                # عدد الحسابات
                accounts_count = len(bank.accounts) if hasattr(bank, 'accounts') else 0
                self.banks_table.setItem(row, 4, QTableWidgetItem(str(accounts_count)))
                total_accounts += accounts_count
                
                # الحالة
                status_item = QTableWidgetItem("نشط" if bank.is_active else "غير نشط")
                if bank.is_active:
                    status_item.setBackground(QColor("#d4edda"))
                    active_count += 1
                else:
                    status_item.setBackground(QColor("#f8d7da"))
                self.banks_table.setItem(row, 5, status_item)
                
                # حفظ معرف البنك
                self.banks_table.item(row, 0).setData(Qt.UserRole, bank.id)
                
            # تحديث الإحصائيات
            self.total_banks_label.setText(f"إجمالي البنوك: {len(banks)}")
            self.active_banks_label.setText(f"البنوك النشطة: {active_count}")
            self.total_accounts_label.setText(f"إجمالي الحسابات: {total_accounts}")
            
            session.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البنوك:\n{str(e)}")
            
    def filter_banks(self):
        """تصفية البنوك حسب النص المدخل"""
        search_text = self.search_edit.text().lower()
        
        for row in range(self.banks_table.rowCount()):
            show_row = False
            
            # البحث في الكود واسم البنك
            for col in [0, 1]:
                item = self.banks_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
                    
            self.banks_table.setRowHidden(row, not show_row)
            
    def on_bank_selection_changed(self):
        """عند تغيير التحديد في جدول البنوك"""
        current_row = self.banks_table.currentRow()
        
        if current_row >= 0:
            # الحصول على معرف البنك
            bank_id = self.banks_table.item(current_row, 0).data(Qt.UserRole)
            if bank_id:
                self.load_bank_details(bank_id)
                self.edit_bank_btn.setEnabled(True)
                self.new_account_btn.setEnabled(True)
        else:
            self.clear_bank_details()
            self.edit_bank_btn.setEnabled(False)
            self.new_account_btn.setEnabled(False)
            
    def load_bank_details(self, bank_id):
        """تحميل تفاصيل البنك"""
        try:
            session = self.db_manager.get_session()
            bank = session.query(Bank).get(bank_id)
            
            if not bank:
                session.close()
                return
                
            self.current_bank = bank_id
            
            # تحديث معلومات البنك
            self.info_code_label.setText(bank.code or "-")
            self.info_name_label.setText(bank.name or "-")
            self.info_name_en_label.setText(bank.name_en or "-")
            self.info_swift_label.setText(bank.swift_code or "-")
            self.info_country_label.setText(bank.country or "-")
            self.info_city_label.setText(bank.city or "-")
            self.info_address_label.setText(bank.address or "-")
            self.info_phone_label.setText(bank.phone or "-")
            self.info_email_label.setText(bank.email or "-")
            self.info_website_label.setText(bank.website or "-")
            self.info_status_label.setText("نشط" if bank.is_active else "غير نشط")
            
            # تحميل حسابات البنك
            self.load_bank_accounts(bank_id)
            
            session.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل تفاصيل البنك:\n{str(e)}")
            
    def load_bank_accounts(self, bank_id):
        """تحميل حسابات البنك"""
        try:
            session = self.db_manager.get_session()
            accounts = session.query(BankAccount).filter(BankAccount.bank_id == bank_id).all()
            
            self.accounts_table.setRowCount(len(accounts))
            
            for row, account in enumerate(accounts):
                # رقم الحساب
                self.accounts_table.setItem(row, 0, QTableWidgetItem(account.account_number or ""))
                
                # اسم الحساب
                self.accounts_table.setItem(row, 1, QTableWidgetItem(account.account_name or ""))
                
                # IBAN
                self.accounts_table.setItem(row, 2, QTableWidgetItem(account.iban or ""))
                
                # العملة
                currency_code = account.currency.code if account.currency else ""
                self.accounts_table.setItem(row, 3, QTableWidgetItem(currency_code))
                
                # الرصيد
                balance_item = QTableWidgetItem(f"{account.balance:.3f}" if account.balance else "0.000")
                balance_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.accounts_table.setItem(row, 4, balance_item)
                
            session.close()
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل حسابات البنك:\n{str(e)}")
            
    def clear_bank_details(self):
        """مسح تفاصيل البنك"""
        self.current_bank = None
        
        # مسح معلومات البنك
        labels = [
            self.info_code_label, self.info_name_label, self.info_name_en_label,
            self.info_swift_label, self.info_country_label, self.info_city_label,
            self.info_address_label, self.info_phone_label, self.info_email_label,
            self.info_website_label, self.info_status_label
        ]
        
        for label in labels:
            label.setText("-")
            
        # مسح حسابات البنك
        self.accounts_table.setRowCount(0)
        
    def new_bank(self):
        """إنشاء بنك جديد"""
        dialog = BankDialog(self)
        if dialog.exec() == QDialog.Accepted:
            bank_data = dialog.get_bank_data()
            self.create_bank(bank_data)
            
    def edit_bank(self):
        """تعديل البنك المحدد"""
        if not self.current_bank:
            QMessageBox.warning(self, "تحذير", "يجب اختيار بنك للتعديل")
            return
            
        try:
            session = self.db_manager.get_session()
            bank = session.query(Bank).get(self.current_bank)
            
            if bank:
                dialog = BankDialog(self, bank)
                if dialog.exec() == QDialog.Accepted:
                    bank_data = dialog.get_bank_data()
                    self.update_bank(bank_data)
                    
            session.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات البنك:\n{str(e)}")
            
    def new_account(self):
        """إنشاء حساب بنكي جديد"""
        if not self.current_bank:
            QMessageBox.warning(self, "تحذير", "يجب اختيار بنك أولاً")
            return
            
        dialog = BankAccountDialog(self, self.current_bank)
        if dialog.exec() == QDialog.Accepted:
            account_data = dialog.get_account_data()
            self.create_bank_account(account_data)
            
    def create_bank(self, bank_data):
        """إنشاء بنك جديد في قاعدة البيانات"""
        try:
            session = self.db_manager.get_session()
            
            # التحقق من عدم وجود بنك بنفس الكود
            existing = session.query(Bank).filter(Bank.code == bank_data['code']).first()
            if existing:
                QMessageBox.warning(self, "تحذير", "يوجد بنك بالفعل بنفس الكود")
                session.close()
                return
                
            # إنشاء البنك الجديد
            bank = Bank(
                code=bank_data['code'],
                name=bank_data['name'],
                name_en=bank_data['name_en'],
                swift_code=bank_data['swift_code'],
                country=bank_data['country'],
                city=bank_data['city'],
                address=bank_data['address'],
                phone=bank_data['phone'],
                email=bank_data['email'],
                website=bank_data['website'],
                is_active=bank_data['is_active'],
                created_by=1  # يجب الحصول على معرف المستخدم الحالي
            )
            
            session.add(bank)
            session.commit()
            
            # إرسال إشارة
            self.bank_created.emit({
                'id': bank.id,
                'code': bank.code,
                'name': bank.name
            })
            
            QMessageBox.information(self, "نجح", "تم إنشاء البنك بنجاح")
            
            # تحديث البيانات
            self.load_banks()
            
            session.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء البنك:\n{str(e)}")
            
    def update_bank(self, bank_data):
        """تحديث البنك في قاعدة البيانات"""
        try:
            session = self.db_manager.get_session()
            bank = session.query(Bank).get(self.current_bank)
            
            if bank:
                bank.name = bank_data['name']
                bank.name_en = bank_data['name_en']
                bank.swift_code = bank_data['swift_code']
                bank.country = bank_data['country']
                bank.city = bank_data['city']
                bank.address = bank_data['address']
                bank.phone = bank_data['phone']
                bank.email = bank_data['email']
                bank.website = bank_data['website']
                bank.is_active = bank_data['is_active']
                
                session.commit()
                
                # إرسال إشارة
                self.bank_updated.emit({
                    'id': bank.id,
                    'code': bank.code,
                    'name': bank.name
                })
                
                QMessageBox.information(self, "نجح", "تم تحديث البنك بنجاح")
                
                # تحديث البيانات
                self.load_banks()
                self.load_bank_details(self.current_bank)
                
            session.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث البنك:\n{str(e)}")
            
    def create_bank_account(self, account_data):
        """إنشاء حساب بنكي جديد"""
        try:
            session = self.db_manager.get_session()
            
            # إنشاء الحساب الجديد
            account = BankAccount(
                bank_id=self.current_bank,
                account_number=account_data['account_number'],
                account_name=account_data['account_name'],
                iban=account_data['iban'],
                currency_id=account_data['currency_id'],
                balance=account_data['balance'],
                created_by=1  # يجب الحصول على معرف المستخدم الحالي
            )
            
            session.add(account)
            session.commit()
            
            # إرسال إشارة
            self.account_created.emit({
                'id': account.id,
                'bank_id': self.current_bank,
                'account_number': account.account_number,
                'account_name': account.account_name
            })
            
            QMessageBox.information(self, "نجح", "تم إنشاء الحساب البنكي بنجاح")
            
            # تحديث البيانات
            self.load_banks()
            self.load_bank_details(self.current_bank)
            
            session.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء الحساب البنكي:\n{str(e)}")
            
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
        if self.current_bank:
            self.load_bank_details(self.current_bank)


# نوافذ الحوار المساعدة
class BankDialog(QDialog):
    """نافذة حوار إنشاء/تعديل البنك"""

    def __init__(self, parent=None, bank=None):
        super().__init__(parent)
        self.bank = bank
        self.setup_ui()

        if bank:
            self.load_bank_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إنشاء بنك جديد" if not self.bank else "تعديل البنك")
        self.setModal(True)
        self.resize(500, 400)

        layout = QVBoxLayout(self)

        # النموذج
        form_layout = QFormLayout()

        # الكود
        self.code_edit = QLineEdit()
        self.code_edit.setEnabled(not self.bank)  # تعطيل التعديل للبنوك الموجودة
        form_layout.addRow("كود البنك:", self.code_edit)

        # اسم البنك
        self.name_edit = QLineEdit()
        form_layout.addRow("اسم البنك:", self.name_edit)

        # الاسم الإنجليزي
        self.name_en_edit = QLineEdit()
        form_layout.addRow("الاسم الإنجليزي:", self.name_en_edit)

        # رمز SWIFT
        self.swift_code_edit = QLineEdit()
        form_layout.addRow("رمز SWIFT:", self.swift_code_edit)

        # الدولة
        self.country_edit = QLineEdit()
        form_layout.addRow("الدولة:", self.country_edit)

        # المدينة
        self.city_edit = QLineEdit()
        form_layout.addRow("المدينة:", self.city_edit)

        # العنوان
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        form_layout.addRow("العنوان:", self.address_edit)

        # الهاتف
        self.phone_edit = QLineEdit()
        form_layout.addRow("الهاتف:", self.phone_edit)

        # البريد الإلكتروني
        self.email_edit = QLineEdit()
        form_layout.addRow("البريد الإلكتروني:", self.email_edit)

        # الموقع الإلكتروني
        self.website_edit = QLineEdit()
        form_layout.addRow("الموقع الإلكتروني:", self.website_edit)

        # الحالة
        self.is_active_check = QCheckBox("نشط")
        self.is_active_check.setChecked(True)
        form_layout.addRow("الحالة:", self.is_active_check)

        layout.addLayout(form_layout)

        # الأزرار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def load_bank_data(self):
        """تحميل بيانات البنك للتعديل"""
        if self.bank:
            self.code_edit.setText(self.bank.code or "")
            self.name_edit.setText(self.bank.name or "")
            self.name_en_edit.setText(self.bank.name_en or "")
            self.swift_code_edit.setText(self.bank.swift_code or "")
            self.country_edit.setText(self.bank.country or "")
            self.city_edit.setText(self.bank.city or "")
            self.address_edit.setPlainText(self.bank.address or "")
            self.phone_edit.setText(self.bank.phone or "")
            self.email_edit.setText(self.bank.email or "")
            self.website_edit.setText(self.bank.website or "")
            self.is_active_check.setChecked(self.bank.is_active)

    def get_bank_data(self):
        """الحصول على بيانات البنك"""
        return {
            'code': self.code_edit.text().strip(),
            'name': self.name_edit.text().strip(),
            'name_en': self.name_en_edit.text().strip(),
            'swift_code': self.swift_code_edit.text().strip(),
            'country': self.country_edit.text().strip(),
            'city': self.city_edit.text().strip(),
            'address': self.address_edit.toPlainText().strip(),
            'phone': self.phone_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'website': self.website_edit.text().strip(),
            'is_active': self.is_active_check.isChecked()
        }


class BankAccountDialog(QDialog):
    """نافذة حوار إنشاء حساب بنكي جديد"""

    def __init__(self, parent=None, bank_id=None):
        super().__init__(parent)
        self.bank_id = bank_id
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.load_currencies()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إنشاء حساب بنكي جديد")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout(self)

        # النموذج
        form_layout = QFormLayout()

        # رقم الحساب
        self.account_number_edit = QLineEdit()
        form_layout.addRow("رقم الحساب:", self.account_number_edit)

        # اسم الحساب
        self.account_name_edit = QLineEdit()
        form_layout.addRow("اسم الحساب:", self.account_name_edit)

        # IBAN
        self.iban_edit = QLineEdit()
        form_layout.addRow("رقم IBAN:", self.iban_edit)

        # العملة
        self.currency_combo = QComboBox()
        form_layout.addRow("العملة:", self.currency_combo)

        # الرصيد الأولي
        self.balance_spin = QDoubleSpinBox()
        self.balance_spin.setRange(-*********.999, *********.999)
        self.balance_spin.setDecimals(3)
        form_layout.addRow("الرصيد الأولي:", self.balance_spin)

        layout.addLayout(form_layout)

        # الأزرار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def load_currencies(self):
        """تحميل قائمة العملات"""
        try:
            session = self.db_manager.get_session()
            currencies = session.query(Currency).filter(Currency.is_active == True).all()

            self.currency_combo.addItem("", None)
            for currency in currencies:
                self.currency_combo.addItem(f"{currency.code} - {currency.name}", currency.id)

            session.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل العملات:\n{str(e)}")

    def get_account_data(self):
        """الحصول على بيانات الحساب"""
        return {
            'account_number': self.account_number_edit.text().strip(),
            'account_name': self.account_name_edit.text().strip(),
            'iban': self.iban_edit.text().strip(),
            'currency_id': self.currency_combo.currentData(),
            'balance': self.balance_spin.value()
        }
